package main

import (
	"bytes"
	"database/sql" // added for SP500 update
	"encoding/csv" // added for CSV parsing
	"fmt"
	"log"
	"net/http" // added for HTTP GET
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"

	_ "github.com/lib/pq" // PostgreSQL driver for database/sql
	"github.com/supabase-community/supabase-go"
)

// TableDefinition represents a SQLite table structure
type TableDefinition struct {
	Name    string   `json:"name"`
	Columns []Column `json:"columns"`
}

// Column represents a table column definition
type Column struct {
	Name       string `json:"name"`
	DataType   string `json:"data_type"`
	IsNullable bool   `json:"is_nullable"`
}

// DataRow represents a generic data row with dynamic fields
type DataRow map[string]interface{}

// DBExporter handles the export process
type DBExporter struct {
	OutputPath    string
	Tables        []string
	SupabaseURL   string
	SupabaseKey   string
	DBConnection  string // Added database connection string
	supaClient    *supabase.Client
	schemaVersion int
	schemaDesc    string
}

// NewDBExporter creates a new DBExporter instance
func NewDBExporter(outputPath string) (*DBExporter, error) {
	supabaseURL := os.Getenv("PROMZ_SUPABASE_URL")
	supabaseKey := os.Getenv("PROMZ_SUPABASE_KEY")
	dbConnection := os.Getenv("PROMZ_DB_CONNECTION")

	if supabaseURL == "" || supabaseKey == "" {
		return nil, fmt.Errorf("PROMZ_SUPABASE_URL and PROMZ_SUPABASE_KEY are required")
	}

	if dbConnection == "" {
		return nil, fmt.Errorf("PROMZ_DB_CONNECTION is required")
	}

	client, err := supabase.NewClient(supabaseURL, supabaseKey, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create Supabase client: %w", err)
	}

	return &DBExporter{
		OutputPath:    outputPath,
		SupabaseURL:   supabaseURL,
		SupabaseKey:   supabaseKey,
		DBConnection:  dbConnection, // Store the database connection string
		supaClient:    client,
		schemaVersion: 8,
		schemaDesc:    "Added SP500 tickers",
		Tables: []string{
			"schema_version",
			"categories",
			"prompts",
			"keyword_mappings",
			"sp500_tickers",
			"local_prompt_usage",
			"topics",
		},
	}, nil
}

// Export performs the full export process
func (e *DBExporter) Export() error {
	// Create output directory if it doesn't exist
	if err := os.MkdirAll(filepath.Dir(e.OutputPath), 0o755); err != nil {
		return fmt.Errorf("failed to create output directory: %w", err)
	}

	// Create SQLite database
	dbPath := filepath.Join(filepath.Dir(e.OutputPath), "promz.db")
	if err := e.createDatabase(dbPath); err != nil {
		return fmt.Errorf("failed to create database: %w", err)
	}

	return nil
}

// getDatabaseSchema uses pg_dump to get the schema and data from Supabase
func (e *DBExporter) getDatabaseSchema() (string, error) {
	log.Printf("Using database connection string for schema and data extraction...")

	// Build pg_dump command with connection string
	// Note: Removed the --schema-only flag to include data
	args := []string{
		"--no-owner",
		"--no-acl",
		"--inserts", // Use INSERT statements for better compatibility
	}

	// Add table filtering options
	for _, table := range e.Tables {
		args = append(args, "-t", fmt.Sprintf("public.%s", table))
	}

	// Add connection string
	args = append(args, e.DBConnection)

	// Create the command
	cmd := exec.Command("pg_dump", args...)

	var stdout bytes.Buffer
	var stderr bytes.Buffer
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr

	log.Printf("Running pg_dump with database connection string to extract schema and data...")
	if err := cmd.Run(); err != nil {
		return "", fmt.Errorf("pg_dump failed: %w, stderr: %s", err, stderr.String())
	}

	// Get schema as string
	dump := stdout.String()
	if dump == "" {
		return "", fmt.Errorf("pg_dump returned empty output")
	}

	log.Printf("Schema and data extracted successfully, length: %d bytes", len(dump))
	return dump, nil
}

// Update createDatabase to keep temporary files for inspection
func (e *DBExporter) createDatabase(dbPath string) error {
	log.Printf("Creating SQLite database directly at: %s", dbPath)

	// Create temporary directory for processing - use a consistent location so it's easier to find
	tmpDir := filepath.Join(os.TempDir(), "promz_export")
	if err := os.MkdirAll(tmpDir, 0o755); err != nil {
		return fmt.Errorf("failed to create temp directory: %w", err)
	}

	// Note: Not using defer os.RemoveAll(tmpDir) to keep the files for inspection
	log.Printf("Using temporary directory: %s", tmpDir)

	// Get PostgreSQL dump with schema and data
	pgDump, err := e.getDatabaseSchema()
	if err != nil {
		return fmt.Errorf("failed to extract schema and data from PostgreSQL: %w", err)
	}

	// Save the original dump for inspection
	pgDumpFile := filepath.Join(tmpDir, "pg_dump_original.sql")
	if err := os.WriteFile(pgDumpFile, []byte(pgDump), 0o644); err != nil {
		log.Printf("Warning: Failed to write original pg_dump file: %v", err)
	} else {
		log.Printf("Original pg_dump saved to: %s", pgDumpFile)
	}

	// Convert PostgreSQL SQL to SQLite-compatible SQL
	sqliteDump, err := e.convertPgToSqlite(pgDump)
	if err != nil {
		return fmt.Errorf("failed to convert PostgreSQL dump to SQLite: %w", err)
	}

	// Write the converted SQL to a temporary file
	sqliteFile := filepath.Join(tmpDir, "sqlite_dump.sql")
	if err := os.WriteFile(sqliteFile, []byte(sqliteDump), 0o644); err != nil {
		return fmt.Errorf("failed to write SQLite dump to file: %w", err)
	}
	log.Printf("Converted SQLite SQL saved to: %s", sqliteFile)

	// Create output directory if it doesn't exist
	if err := os.MkdirAll(filepath.Dir(dbPath), 0o755); err != nil {
		return fmt.Errorf("failed to create output directory: %w", err)
	}

	// Remove existing database
	if err := os.Remove(dbPath); err != nil && !os.IsNotExist(err) {
		return fmt.Errorf("failed to remove existing database: %w", err)
	}

	// Create and import directly into SQLite
	log.Printf("Importing converted SQL into SQLite...")
	err = e.runSQLiteCommand(dbPath, []string{".read " + sqliteFile})
	if err != nil {
		log.Printf("Import failed. Saving additional debug information...")
		if debugErr := e.debugSQLiteDump(sqliteDump, tmpDir); debugErr != nil {
			log.Printf("Failed to save debug information: %v", debugErr)
		}
		return fmt.Errorf("failed to import into SQLite: %w", err)
	}

	// Verify the database by checking schema_version
	if err := e.verifySQLiteDatabase(dbPath); err != nil {
		return fmt.Errorf("database verification failed: %w", err)
	}

	log.Printf("Database created and verified successfully")
	return nil
}

// Update convertPgToSqlite to fix the PRIMARY KEY issue
func (e *DBExporter) convertPgToSqlite(pgSQL string) (string, error) {
	log.Printf("Converting PostgreSQL SQL to SQLite format with simplified approach...")

	// Pre-process to remove PostgreSQL type casts
	pgSQL = strings.ReplaceAll(pgSQL, "::jsonb", "")
	pgSQL = strings.ReplaceAll(pgSQL, "::text", "")
	pgSQL = strings.ReplaceAll(pgSQL, "::boolean", "")

	// Split the SQL into lines for processing
	lines := strings.Split(pgSQL, "\n")
	var sqliteSQL strings.Builder

	// Write SQLite header
	sqliteSQL.WriteString("-- SQLite database dump converted from PostgreSQL\n")
	sqliteSQL.WriteString("PRAGMA foreign_keys = OFF;\n")
	sqliteSQL.WriteString("BEGIN TRANSACTION;\n\n")

	inCreateTable := false
	tableName := ""
	columnDefs := []string{}
	columnNames := []string{} // Track column names to know if 'id' exists
	hasPrimaryKey := false

	// Process each line
	for _, line := range lines {
		trimmedLine := strings.TrimSpace(line)

		// Skip PostgreSQL-specific statements
		if trimmedLine == "" ||
			strings.HasPrefix(trimmedLine, "SET ") ||
			strings.HasPrefix(trimmedLine, "SELECT ") ||
			strings.HasPrefix(trimmedLine, "ALTER ") ||
			strings.HasPrefix(trimmedLine, "CREATE SEQUENCE") ||
			strings.HasPrefix(trimmedLine, "CREATE FUNCTION") ||
			strings.HasPrefix(trimmedLine, "CREATE EXTENSION") ||
			strings.HasPrefix(trimmedLine, "CREATE INDEX") {
			continue
		}

		// Keep informational comments
		if strings.HasPrefix(trimmedLine, "--") {
			sqliteSQL.WriteString(line)
			sqliteSQL.WriteString("\n")
			continue
		}

		// Start of CREATE TABLE
		if strings.HasPrefix(trimmedLine, "CREATE TABLE") {
			inCreateTable = true
			columnDefs = []string{}
			columnNames = []string{} // Reset column names for this table
			hasPrimaryKey = false

			// Extract table name without schema prefix
			parts := strings.Fields(trimmedLine)
			if len(parts) >= 3 {
				tableFull := parts[2]
				tableName = tableFull
				if strings.Contains(tableFull, ".") {
					tableName = strings.Split(tableFull, ".")[1]
				}

				// Start the CREATE TABLE statement
				sqliteSQL.WriteString("CREATE TABLE IF NOT EXISTS ")
				sqliteSQL.WriteString(tableName)
				sqliteSQL.WriteString(" (\n")
			}
			continue
		}

		// End of CREATE TABLE
		if inCreateTable && trimmedLine == ");" {
			inCreateTable = false

			// If no primary key defined and an 'id' column exists, add a primary key constraint
			if !hasPrimaryKey && contains(columnNames, "id") && tableName != "prompt_tags" {
				columnDefs = append(columnDefs, "PRIMARY KEY(id)")
			}

			log.Printf("Table %s columns: %v, has primary key: %v",
				tableName, columnNames, hasPrimaryKey)

			// Join all column definitions with commas
			sqliteSQL.WriteString(strings.Join(columnDefs, ",\n"))
			sqliteSQL.WriteString("\n);\n\n")
			continue
		}

		// Inside CREATE TABLE, process column definitions
		if inCreateTable {
			// Skip constraints that SQLite doesn't support well
			if strings.HasPrefix(trimmedLine, "CONSTRAINT") {
				continue
			}

			// Extract column name and track it
			colParts := strings.Fields(trimmedLine)
			if len(colParts) >= 1 {
				colName := colParts[0]
				columnNames = append(columnNames, colName)
			}

			// Check for primary key
			if strings.Contains(trimmedLine, "PRIMARY KEY") {
				hasPrimaryKey = true
			}

			// Process column definition
			colDef := trimmedLine

			// Remove trailing comma
			colDef = strings.TrimSuffix(colDef, ",")

			// Convert types to SQLite types
			colDef = strings.ReplaceAll(colDef, "timestamp with time zone", "INTEGER")
			colDef = strings.ReplaceAll(colDef, "boolean", "INTEGER")
			colDef = strings.ReplaceAll(colDef, "jsonb", "TEXT")
			colDef = strings.ReplaceAll(colDef, "uuid", "TEXT")
			colDef = strings.ReplaceAll(colDef, "real", "REAL")
			colDef = strings.ReplaceAll(colDef, "integer", "INTEGER")
			colDef = strings.ReplaceAll(colDef, "text", "TEXT")

			// Handle DEFAULT CURRENT_TIMESTAMP
			colDef = strings.ReplaceAll(colDef, "DEFAULT CURRENT_TIMESTAMP",
				"DEFAULT (strftime('%s','now') * 1000)")

			// Remove all DEFAULT function calls
			colDef = regexp.MustCompile(`DEFAULT\s+[a-zA-Z0-9_.]+\(\)`).ReplaceAllString(colDef, "")

			// Remove schema qualifiers
			colDef = strings.ReplaceAll(colDef, "public.", "")

			// Add to column definitions if not empty
			if colDef != "" {
				columnDefs = append(columnDefs, colDef)
			}
			continue
		}

		// Handle INSERT statements
		if strings.HasPrefix(trimmedLine, "INSERT INTO") {
			// Remove schema prefix
			line = strings.ReplaceAll(line, "public.", "")

			// Convert boolean values
			line = strings.ReplaceAll(line, "true", "1")
			line = strings.ReplaceAll(line, "false", "0")

			// Convert timestamp literals to milliseconds
			if strings.Contains(line, "'20") &&
				(strings.Contains(line, "T") || strings.Contains(line, "-")) {
				re := regexp.MustCompile(`'(\d{4}-\d{2}-\d{2}[T ]\d{2}:\d{2}:\d{2}[.0-9]*[Z+-][0-9:]*)'`)
				line = re.ReplaceAllStringFunc(line, func(match string) string {
					dateStr := match[1 : len(match)-1]
					if t, err := time.Parse(time.RFC3339, dateStr); err == nil {
						return fmt.Sprintf("%d", t.UnixMilli())
					}
					if t, err := time.Parse("2006-01-02 15:04:05-07", dateStr); err == nil {
						return fmt.Sprintf("%d", t.UnixMilli())
					}
					return match
				})
			}

			// Verify schema_version
			if strings.Contains(line, "INSERT INTO schema_version") {
				versionRegex := regexp.MustCompile(`VALUES\s*\((\d+),`)
				matches := versionRegex.FindStringSubmatch(line)
				if len(matches) >= 2 {
					version, err := strconv.Atoi(matches[1])
					if err != nil {
						return "", fmt.Errorf("invalid schema version format: %s", matches[1])
					}
					if version != e.schemaVersion {
						return "", fmt.Errorf("schema version mismatch: found %d, expected %d",
							version, e.schemaVersion)
					}
					log.Printf("Verified schema version: %d", version)
				}
			}

			sqliteSQL.WriteString(line)
			sqliteSQL.WriteString("\n")
			continue
		}
	}

	// Write SQLite footer
	sqliteSQL.WriteString("\nCOMMIT;\n")
	sqliteSQL.WriteString("PRAGMA foreign_keys = ON;\n")

	return sqliteSQL.String(), nil
}

// Add method to verify SQLite database after creation
func (e *DBExporter) verifySQLiteDatabase(dbPath string) error {
	log.Printf("Verifying database structure...")

	// Check schema_version table
	var schemaVersion, description string
	verifyCmd := exec.Command("sqlite3", dbPath,
		"SELECT version, description, applied_at FROM schema_version ORDER BY version DESC LIMIT 1;")
	var stdout, stderr bytes.Buffer
	verifyCmd.Stdout = &stdout
	verifyCmd.Stderr = &stderr

	if err := verifyCmd.Run(); err != nil {
		return fmt.Errorf("failed to verify schema_version: %w, stderr: %s",
			err, stderr.String())
	}

	result := strings.TrimSpace(stdout.String())
	if result == "" {
		return fmt.Errorf("schema_version table is empty")
	}

	parts := strings.Split(result, "|")
	if len(parts) != 3 {
		return fmt.Errorf("unexpected schema_version format: %s", result)
	}

	schemaVersion = parts[0]
	description = parts[1]

	log.Printf("Database schema_version: %s - %s", schemaVersion, description)

	// Check table counts
	for _, table := range e.Tables {
		countCmd := exec.Command("sqlite3", dbPath, fmt.Sprintf("SELECT COUNT(*) FROM %s;", table))
		stdout.Reset()
		stderr.Reset()
		countCmd.Stdout = &stdout
		countCmd.Stderr = &stderr

		if err := countCmd.Run(); err != nil {
			return fmt.Errorf("failed to get count for table %s: %w, stderr: %s",
				table, err, stderr.String())
		}

		count := strings.TrimSpace(stdout.String())
		log.Printf("Table %s: %s records", table, count)
	}

	return nil
}

// runSQLiteCommand executes commands on the SQLite database
func (e *DBExporter) runSQLiteCommand(dbPath string, commands []string) error {
	args := []string{dbPath}
	args = append(args, commands...)

	command := exec.Command("sqlite3", args...)
	var stdout bytes.Buffer
	var stderr bytes.Buffer
	command.Stdout = &stdout
	command.Stderr = &stderr

	if err := command.Run(); err != nil {
		return fmt.Errorf("sqlite3 command failed: %w\nstdout: %s\nstderr: %s",
			err, stdout.String(), stderr.String())
	}
	return nil
}

// Enhance debug function to show more details
func (e *DBExporter) debugSQLiteDump(dump, tmpDir string) error {
	// Write original dump to file for inspection (already done in createDatabase)

	// Try importing just the schema without data
	schemaLines := []string{"PRAGMA foreign_keys = OFF;", "BEGIN TRANSACTION;"}
	dataLines := []string{}

	for _, line := range strings.Split(dump, "\n") {
		trimmedLine := strings.TrimSpace(line)
		if strings.HasPrefix(trimmedLine, "INSERT INTO") {
			dataLines = append(dataLines, line)
		} else if trimmedLine != "" && !strings.HasPrefix(trimmedLine, "--") {
			schemaLines = append(schemaLines, line)
		}
	}

	// Add transaction end to schema
	schemaLines = append(schemaLines, "COMMIT;", "PRAGMA foreign_keys = ON;")

	// Write schema only SQL
	schemaOnly := strings.Join(schemaLines, "\n")
	schemaFile := filepath.Join(tmpDir, "schema_only.sql")
	if err := os.WriteFile(schemaFile, []byte(schemaOnly), 0o644); err != nil {
		return fmt.Errorf("failed to write schema file: %w", err)
	}
	log.Printf("Schema-only SQL saved to: %s", schemaFile)

	// Write data only SQL
	if len(dataLines) > 0 {
		dataOnly := strings.Join(
			append(
				append([]string{"PRAGMA foreign_keys = OFF;", "BEGIN TRANSACTION;"}, dataLines...),
				"COMMIT;", "PRAGMA foreign_keys = ON;",
			),
			"\n",
		)
		dataFile := filepath.Join(tmpDir, "data_only.sql")
		if err := os.WriteFile(dataFile, []byte(dataOnly), 0o644); err != nil {
			return fmt.Errorf("failed to write data file: %w", err)
		}
		log.Printf("Data-only SQL saved to: %s", dataFile)
	}

	// Try importing just the schema to debug issues
	testDbPath := filepath.Join(tmpDir, "test.db")
	log.Printf("Attempting schema-only import to: %s", testDbPath)
	if err := e.runSQLiteCommand(testDbPath, []string{".read " + schemaFile}); err != nil {
		log.Printf("Schema-only import failed: %v", err)

		// Try importing line by line to identify the exact issue
		log.Printf("Attempting line-by-line import to identify problem area...")
		lineByLineFile := filepath.Join(tmpDir, "line_by_line.sql")
		var lineByLineBuilder strings.Builder
		lineByLineBuilder.WriteString("PRAGMA foreign_keys = OFF;\nBEGIN TRANSACTION;\n\n")

		testDb := filepath.Join(tmpDir, "line_test.db")
		success := true
		for i, line := range schemaLines {
			if i <= 1 || i >= len(schemaLines)-2 { // Skip pragma and transaction lines
				continue
			}

			// Add this line to our cumulative script
			lineByLineBuilder.WriteString(line + "\n")

			// Try executing everything up to this line
			tempScript := lineByLineBuilder.String() + "\nCOMMIT;\nPRAGMA foreign_keys = ON;\n"
			tempFile := filepath.Join(tmpDir, "temp_test.sql")
			if err := os.WriteFile(tempFile, []byte(tempScript), 0o644); err != nil {
				log.Printf("Warning: Failed to write temporary test file")
				continue
			}

			// Remove previous test db
			os.Remove(testDb)

			// Try to execute
			err := e.runSQLiteCommand(testDb, []string{".read " + tempFile})
			if err != nil {
				success = false
				log.Printf("Failed at line %d: %s", i, line)
				log.Printf("Error: %v", err)
				break
			}
		}

		if success {
			log.Printf("Line-by-line import completed successfully, but full import still fails")
		}

		// Save our cumulative script
		os.WriteFile(lineByLineFile, []byte(lineByLineBuilder.String()), 0o644)
		log.Printf("Line-by-line SQL saved to: %s", lineByLineFile)
	} else {
		log.Printf("Schema-only import succeeded")
	}

	return nil
}

// UpdateSP500 downloads the S&P 500 CSV data and updates the sp500_tickers table.
func (e *DBExporter) UpdateSP500() error {
	const csvURL = "https://raw.githubusercontent.com/datasets/s-and-p-500-companies/main/data/constituents.csv"
	log.Printf("Downloading S&P 500 data from %s", csvURL)
	resp, err := http.Get(csvURL)
	if err != nil {
		return fmt.Errorf("failed to download CSV: %w", err)
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("failed to download CSV: status %d", resp.StatusCode)
	}
	reader := csv.NewReader(resp.Body)
	records, err := reader.ReadAll()
	if err != nil {
		return fmt.Errorf("failed to parse CSV: %w", err)
	}
	if len(records) < 2 {
		return fmt.Errorf("no data in CSV")
	}
	// Open database connection using the PROMZ_DB_CONNECTION value.
	db, err := sql.Open("postgres", e.DBConnection)
	if err != nil {
		return fmt.Errorf("failed to open DB connection: %w", err)
	}
	defer db.Close()
	// Iterate over records skipping header row.
	for i, row := range records {
		if i == 0 {
			continue // skip header
		}
		if len(row) < 4 {
			continue
		}
		sqlStmt := `
			INSERT INTO sp500_tickers (symbol, company_name, sector, industry)
			VALUES ($1, $2, $3, $4)
			ON CONFLICT (symbol) DO UPDATE SET
				company_name = EXCLUDED.company_name,
				sector = EXCLUDED.sector,
				industry = EXCLUDED.industry,
				last_updated = CURRENT_TIMESTAMP;
		`
		if _, err := db.Exec(sqlStmt, row[0], row[1], row[2], row[3]); err != nil {
			return fmt.Errorf("failed to update record for symbol %s: %w", row[0], err)
		}
	}
	log.Printf("sp500_tickers table updated successfully.")
	return nil
}

// SimplifySP500TableForClient modifies the SQLite database to have a simplified SP500 tickers table
// that only includes symbol and company_name columns, removing sector and industry.
func (e *DBExporter) SimplifySP500TableForClient(dbPath string) error {
	log.Printf("Simplifying SP500 tickers table for client database...")

	// Commands to execute on the SQLite database
	commands := []string{
		// Drop the temporary table if it exists
		`DROP TABLE IF EXISTS sp500_tickers_temp;`,

		// Create a temporary table with only the columns we want
		`CREATE TABLE sp500_tickers_temp (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			symbol TEXT NOT NULL UNIQUE,
			company_name TEXT NOT NULL,
			last_updated TEXT NOT NULL
		);`,

		// Copy data from the original table to the temp table
		`INSERT INTO sp500_tickers_temp (id, symbol, company_name, last_updated)
		SELECT id, symbol, company_name, last_updated FROM sp500_tickers;`,

		// Drop the original table
		`DROP TABLE sp500_tickers;`,

		// Rename the temp table to the original name
		`ALTER TABLE sp500_tickers_temp RENAME TO sp500_tickers;`,

		// Create an index on symbol for faster lookups
		`CREATE INDEX idx_sp500_tickers_symbol ON sp500_tickers(symbol);`,
	}

	// Execute the commands
	if err := e.runSQLiteCommand(dbPath, commands); err != nil {
		return fmt.Errorf("failed to simplify SP500 tickers table: %w", err)
	}

	log.Printf("SP500 tickers table simplified successfully for client database")
	return nil
}

func main() {
	// New behavior: if argument "sp500" is provided, only update SP500 data.
	if len(os.Args) > 1 && os.Args[1] == "sp500" {
		exporter, err := NewDBExporter("unused") // OutputPath not used here
		if err != nil {
			log.Fatalf("Failed to create exporter: %v", err)
		}
		if err := exporter.UpdateSP500(); err != nil {
			log.Fatalf("Error updating sp500: %v", err)
		} else {
			log.Printf("sp500_tickers table updated successfully.")
		}
		return
	}

	// Default path: perform full export.
	outputDir := filepath.Join("..", "..", "..", "client", "assets")
	if err := os.MkdirAll(outputDir, 0o755); err != nil {
		log.Fatalf("Failed to create output directory: %v", err)
	}
	outputPath := filepath.Join(outputDir, "promz.db")
	exporter, err := NewDBExporter(outputPath)
	if err != nil {
		log.Fatalf("Failed to create exporter: %v", err)
	}
	if err := exporter.Export(); err != nil {
		log.Fatalf("Export failed: %v", err)
	}

	// Simplify the SP500 tickers table for the client database
	if err := exporter.SimplifySP500TableForClient(outputPath); err != nil {
		log.Fatalf("Failed to simplify SP500 tickers table: %v", err)
	}

	log.Printf("Export completed successfully to %s", outputPath)
}

// Helper function to check if a string is in a slice
func contains(slice []string, str string) bool {
	for _, s := range slice {
		if s == str {
			return true
		}
	}
	return false
}
