# PowerShell script for exporting schema to SQLite format

# Function to export schema to SQLite format
function Export-SchemaToSqlite {
    Write-Host "Exporting schema to SQLite format..." -ForegroundColor Yellow

    # Run the Go script to export the schema and data
    Write-Host "Running db_export.go..." -ForegroundColor Gray
    
    # Get the script's directory - using a more reliable method
    $SCRIPT_DIR = $PSScriptRoot
    if (-not $SCRIPT_DIR) {
        $SCRIPT_DIR = Split-Path -Parent $PSCommandPath
        if (-not $SCRIPT_DIR) {
            $SCRIPT_DIR = Get-Location
        }
    }
    
    Write-Host "Script directory: $SCRIPT_DIR" -ForegroundColor Gray
    
    # Change to the script's directory
    Set-Location $SCRIPT_DIR

    # Source the environment variables
    $envVarsPath = Join-Path $SCRIPT_DIR "..\..\..\scripts\script_utils.ps1"
    Write-Host "Looking for script_utils.ps1 at: $envVarsPath" -ForegroundColor Gray
    
    if (Test-Path $envVarsPath) {
        Write-Host "Found script_utils.ps1, loading environment variables..." -ForegroundColor Gray
        . $envVarsPath
    } else {
        Write-Host "Error: script_utils.ps1 not found at $envVarsPath" -ForegroundColor Red
        return $false
    }

    # Verify environment variables are set
    if (-not $env:PROMZ_SUPABASE_URL -or -not $env:PROMZ_SUPABASE_KEY -or -not $env:PROMZ_DB_CONNECTION) {
        Write-Host "Error: Required environment variables are not set. Make sure script_utils.ps1 sets PROMZ_SUPABASE_URL, PROMZ_SUPABASE_KEY, and PROMZ_DB_CONNECTION." -ForegroundColor Red
        return $false
    }

    # Display environment variables for debugging
    Write-Host "Environment variables loaded:" -ForegroundColor Gray
    Write-Host "  PROMZ_SUPABASE_URL: $($env:PROMZ_SUPABASE_URL.Substring(0, 10))..." -ForegroundColor Gray
    Write-Host "  PROMZ_SUPABASE_KEY: $($env:PROMZ_SUPABASE_KEY.Substring(0, 10))..." -ForegroundColor Gray
    Write-Host "  PROMZ_DB_CONNECTION: $($env:PROMZ_DB_CONNECTION.Substring(0, 10))..." -ForegroundColor Gray

    # Execute the Go program with the sourced environment variables
    try {
        Write-Host "Running: go run db_export.go" -ForegroundColor Gray
        go run db_export.go
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Error: Failed to export schema and data (Exit code: $LASTEXITCODE)" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "Error: $_" -ForegroundColor Red
        return $false
    }

    Write-Host "Schema and data exported successfully to promz.db" -ForegroundColor Green
    return $true
}

# Main execution
if (Export-SchemaToSqlite) {
    Write-Host "Schema export completed successfully." -ForegroundColor Green
} else {
    Write-Host "Schema export failed." -ForegroundColor Red
    exit 1
}
