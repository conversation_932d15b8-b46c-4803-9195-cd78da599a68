[{"provider_id": "google", "model_id": "gemini-2.0-flash", "display_name": "gemini-2.0-flash", "cost_input_per_million_tokens": 0.1, "cost_output_per_million_tokens": 0.4, "cost_tier": 1, "intended_user_tier": "any", "priority_within_tier": 99, "capabilities": [], "is_globally_enabled": true, "is_free": true}, {"provider_id": "google", "model_id": "claude-3-5-haiku", "display_name": "claude-3-5-haiku", "cost_input_per_million_tokens": 0.8, "cost_output_per_million_tokens": 4, "cost_tier": 2, "intended_user_tier": "any", "priority_within_tier": 99, "capabilities": [], "is_globally_enabled": true, "is_free": false}, {"provider_id": "google", "model_id": "gemini-2.5-pro-exp-03-25", "display_name": "gemini-2.5-pro-exp-03-25", "cost_input_per_million_tokens": 0, "cost_output_per_million_tokens": 0, "cost_tier": 0, "intended_user_tier": "any", "priority_within_tier": 99, "capabilities": [], "is_globally_enabled": true, "is_free": true}, {"provider_id": "google", "model_id": "gemini-2.0-flash-lite", "display_name": "gemini-2.0-flash-lite", "cost_input_per_million_tokens": 0.075, "cost_output_per_million_tokens": 0.3, "cost_tier": 1, "intended_user_tier": "any", "priority_within_tier": 99, "capabilities": [], "is_globally_enabled": true, "is_free": true}, {"provider_id": "google", "model_id": "gemini-2.5-flash-preview", "display_name": "gemini-2.5-flash-preview", "cost_input_per_million_tokens": 0.15, "cost_output_per_million_tokens": 0.6, "cost_tier": 1, "intended_user_tier": "any", "priority_within_tier": 99, "capabilities": [], "is_globally_enabled": true, "is_free": false}, {"provider_id": "openai", "model_id": "gpt-4o-mini", "display_name": "gpt-4o-mini", "cost_input_per_million_tokens": 0.15, "cost_output_per_million_tokens": 0.6, "cost_tier": 1, "intended_user_tier": "any", "priority_within_tier": 99, "capabilities": [], "is_globally_enabled": true, "is_free": false}, {"provider_id": "openai", "model_id": "gpt-4o-mini-2024-07-18", "display_name": "gpt-4o-mini-2024-07-18", "cost_input_per_million_tokens": 0.15, "cost_output_per_million_tokens": 0.6, "cost_tier": 1, "intended_user_tier": "any", "priority_within_tier": 99, "capabilities": [], "is_globally_enabled": true, "is_free": false}, {"provider_id": "openai", "model_id": "o3-mini", "display_name": "o3-mini", "cost_input_per_million_tokens": 1.1, "cost_output_per_million_tokens": 4.4, "cost_tier": 3, "intended_user_tier": "any", "priority_within_tier": 99, "capabilities": [], "is_globally_enabled": true, "is_free": false}, {"provider_id": "openai", "model_id": "o3-mini-2025-01-31", "display_name": "o3-mini-2025-01-31", "cost_input_per_million_tokens": 1.1, "cost_output_per_million_tokens": 4.4, "cost_tier": 3, "intended_user_tier": "any", "priority_within_tier": 99, "capabilities": [], "is_globally_enabled": true, "is_free": false}, {"provider_id": "openai", "model_id": "gpt-4.1-mini", "display_name": "gpt-4.1-mini", "cost_input_per_million_tokens": 0.4, "cost_output_per_million_tokens": 1.6, "cost_tier": 2, "intended_user_tier": "any", "priority_within_tier": 99, "capabilities": [], "is_globally_enabled": true, "is_free": false}, {"provider_id": "openai", "model_id": "gpt-4.1-mini-2025-04-14", "display_name": "gpt-4.1-mini-2025-04-14", "cost_input_per_million_tokens": 0.4, "cost_output_per_million_tokens": 1.6, "cost_tier": 2, "intended_user_tier": "any", "priority_within_tier": 99, "capabilities": [], "is_globally_enabled": true, "is_free": false}, {"provider_id": "openai", "model_id": "gpt-4.1-nano", "display_name": "gpt-4.1-nano", "cost_input_per_million_tokens": 0.1, "cost_output_per_million_tokens": 0.4, "cost_tier": 1, "intended_user_tier": "any", "priority_within_tier": 99, "capabilities": [], "is_globally_enabled": true, "is_free": true}, {"provider_id": "openai", "model_id": "gpt-4.1-nano-2025-04-14", "display_name": "gpt-4.1-nano-2025-04-14", "cost_input_per_million_tokens": 0.1, "cost_output_per_million_tokens": 0.4, "cost_tier": 1, "intended_user_tier": "any", "priority_within_tier": 99, "capabilities": [], "is_globally_enabled": true, "is_free": false}, {"provider_id": "openai", "model_id": "gpt-4o-mini-realtime", "display_name": "gpt-4o-mini-realtime", "cost_input_per_million_tokens": 0.15, "cost_output_per_million_tokens": 0.6, "cost_tier": 1, "intended_user_tier": "any", "priority_within_tier": 99, "capabilities": [], "is_globally_enabled": true, "is_free": false}, {"provider_id": "openai", "model_id": "o4-mini", "display_name": "o4-mini", "cost_input_per_million_tokens": 1.1, "cost_output_per_million_tokens": 4.4, "cost_tier": 3, "intended_user_tier": "any", "priority_within_tier": 99, "capabilities": [], "is_globally_enabled": true, "is_free": false}, {"provider_id": "openai", "model_id": "o4-mini-2025-04-16", "display_name": "o4-mini-2025-04-16", "cost_input_per_million_tokens": 1.1, "cost_output_per_million_tokens": 4.4, "cost_tier": 3, "intended_user_tier": "any", "priority_within_tier": 99, "capabilities": [], "is_globally_enabled": true, "is_free": false}, {"provider_id": "anthropic", "model_id": "claude-3-5-haiku-20241022", "display_name": "claude-3-5-haiku-20241022", "cost_input_per_million_tokens": 0.8, "cost_output_per_million_tokens": 4, "cost_tier": 2, "intended_user_tier": "any", "priority_within_tier": 99, "capabilities": [], "is_globally_enabled": true, "is_free": false}]