{"name": "llm-pricing-seeder", "version": "1.0.0", "description": "Script to fetch LLM pricing data from Helicone source and seed Supabase DB.", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "ts-node src/index.ts", "seed": "npm run build && node dist/index.js"}, "keywords": ["llm", "pricing", "seeder", "supabase"], "author": "", "license": "ISC", "devDependencies": {"@types/node": "^20.0.0", "@types/node-fetch": "^2.6.12", "ts-node": "^10.9.2", "typescript": "^5.0.0"}, "dependencies": {"@supabase/supabase-js": "^2.0.0", "node-fetch": "^2.7.0", "ts-morph": "^22.0.0"}}