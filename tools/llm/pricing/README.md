# LLM Pricing Seeder

This script fetches LLM pricing information from Helicone's public GitHub repository 
source files, processes it, and uploads it to a specified Supabase table. 
It also saves the processed data locally to `output/current_llm_pricing.json` 
which should be committed to Git for traceability.

## Setup

1.  **Install Dependencies:**
    ```bash
    npm install
    ```
2.  **Configure Environment Variables:**
    Create a `.env` file in this directory (`tools/llm/pricing/`) or set environment variables directly:
    ```dotenv
    SUPABASE_URL=your_supabase_project_url
    SUPABASE_SERVICE_KEY=your_supabase_service_role_key 
    ```
    *   `SUPABASE_URL`: Found in your Supabase project settings (API -> Project URL).
    *   `SUPABASE_SERVICE_KEY`: Found in your Supabase project settings (API -> Project API Keys -> service_role secret). **Keep this key secure!**

3.  **Configure Supabase Table:**
    *   Ensure you have a table in Supabase to store this data. The default name used in `src/supabase.ts` is `llm_models`.
    *   The table should have columns corresponding to the fields in `src/types.ts` (e.g., `provider_id` TEXT, `model_id` TEXT, `cost_input_per_million_tokens` FLOAT8, etc.).
    *   Set up a unique constraint on the combination of `provider_id` and `model_id` to allow upserts.

## Usage

1.  **Build TypeScript (Optional but recommended for production):**
    ```bash
    npm run build 
    ```
2.  **Run the Seeder:**
    *   Using ts-node (for development):
      ```bash
      npm start
      ```
    *   Using compiled JavaScript:
      ```bash
      npm run seed 
      # or node dist/index.js
      ```

## Important Notes

*   **Parsing Logic:** The core parsing logic in `src/parser.ts` (specifically `parsePricingDataFromAST`) is a **placeholder**. You **MUST** inspect the actual Helicone `.ts` files specified in `HELICONE_FILE_URLS` and adapt the AST traversal logic to match their current structure.
*   **Supabase Mapping:** Ensure the data mapping in `src/supabase.ts` correctly aligns the fields from `ModelPricingInfo` to your exact Supabase table schema.
*   **Security:** The `SUPABASE_SERVICE_KEY` has full access to your database. Do not commit it to Git. Use environment variables or a secure secret management solution.
*   **Output File:** Remember to commit the `output/current_llm_pricing.json` file after running the script to track pricing data changes over time.
