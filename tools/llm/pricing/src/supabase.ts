import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { ModelPricingInfo } from './types';

// Ensure these environment variables are set when running the script
const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_KEY;
const TARGET_TABLE = 'llm_models'; // Replace with your actual table name

let supabase: SupabaseClient | null = null;

function getSupabaseClient(): SupabaseClient {
  if (!supabase) {
    if (!SUPABASE_URL || !SUPABASE_SERVICE_KEY) {
      throw new Error('Supabase URL and Service Key must be provided via environment variables.');
    }
    supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);
  }
  return supabase;
}

export async function uploadToSupabase(pricingData: ModelPricingInfo[]): Promise<void> {
  const client = getSupabaseClient();

  if (pricingData.length === 0) {
    console.warn('No pricing data provided to upload.');
    return;
  }

  // Map ModelPricingInfo to the structure expected by your Supabase table
  // This assumes your table columns match the keys in ModelPricingInfo
  // You might need to transform the data here.
  const recordsToUpsert = pricingData.map(data => ({
    provider_id: data.provider_id,
    model_id: data.model_id,
    display_name: data.display_name || data.model_id, // Use model_id as fallback display name
    cost_input_per_million_tokens: data.cost_input_per_million_tokens,
    cost_output_per_million_tokens: data.cost_output_per_million_tokens,
    cost_tier: data.cost_tier,
    intended_user_tier: data.intended_user_tier || 'any', // Default tier
    priority_within_tier: data.priority_within_tier || 99, // Default priority (lower is higher)
    capabilities: data.capabilities || [], // Default capabilities
    is_globally_enabled: data.is_globally_enabled !== undefined ? data.is_globally_enabled : true, // Default enabled
    // Add/remove fields to match your actual table schema exactly
    // Example: api_endpoint_override: data.api_endpoint_override,
    // Example: api_key_ref: data.api_key_ref,
  }));

  console.log(`Attempting to upsert ${recordsToUpsert.length} records into ${TARGET_TABLE}...`);

  // Upsert the data. Adjust 'provider_id' and 'model_id' if your unique constraint is different.
  const { data, error } = await client
    .from(TARGET_TABLE)
    .upsert(recordsToUpsert, {
      onConflict: 'provider_id, model_id', // Specify conflict columns
      ignoreDuplicates: false, // Ensure existing rows are updated
    });

  if (error) {
    console.error('Supabase upsert error:', error);
    throw new Error(`Failed to upload data to Supabase: ${error.message}`);
  }

  console.log('Supabase upsert successful.', data); // `data` might be null on success depending on Supabase version/settings
}
