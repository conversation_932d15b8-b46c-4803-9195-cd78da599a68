import * as fs from 'fs';
import * as path from 'path';
import { fetchAndParseHeliconeData, uploadAllToSupabase } from './parser';
import { uploadToSupabase } from './supabase';
import { ModelPricingInfo } from './types';

const OUTPUT_DIR = path.join(__dirname, '..', 'output');
const OUTPUT_FILE = path.join(OUTPUT_DIR, 'current_llm_pricing.json');

async function main() {
  console.log('Starting LLM pricing seeder...');

  try {
    // 1. Fetch and parse data from Helicone source files
    console.log('Fetching and parsing pricing data from Helicone source...');
    // Parse pricing and permissions
    const { models: pricingData, userModelPermissions } = await fetchAndParseHeliconeData();
    console.log(`Successfully parsed data for ${pricingData.length} models.`);

    // 2. Ensure output directory exists
    if (!fs.existsSync(OUTPUT_DIR)) {
      fs.mkdirSync(OUTPUT_DIR, { recursive: true });
    }

    // 3. Write data to output JSON file
    console.log(`Writing pricing data to ${OUTPUT_FILE}...`);
    fs.writeFileSync(OUTPUT_FILE, JSON.stringify(pricingData, null, 2));
    console.log('Successfully wrote output file.');

    // 4. Print summary for manual review
    printSummary(pricingData);
    console.log('\nReview the output/current_llm_pricing.json file for details.');

    // 5. Prompt for approval before destructive upload
    const readline = require('readline');
    const rl = readline.createInterface({ input: process.stdin, output: process.stdout });
    rl.question('\nProceed with uploading to Supabase? This will DELETE ALL existing llm_models and user_model_permissions. (y/N): ', async (answer: string) => {
      if (answer.trim().toLowerCase() === 'y') {
        try {
          await uploadAllToSupabase(pricingData, userModelPermissions);
          console.log('Successfully uploaded data to Supabase.');
        } catch (err) {
          console.error('Upload failed:', err);
        }
      } else {
        console.log('Upload cancelled. Please review and rerun when ready.');
      }
      rl.close();
      console.log('LLM pricing seeder finished.');
    });
  } catch (error) {
    console.error('Error running LLM pricing seeder:', error);
    process.exit(1); // Exit with error code
  }
}

function printSummary(pricingData: ModelPricingInfo[]) {
  const byProvider: Record<string, ModelPricingInfo[]> = {};
  for (const model of pricingData) {
    if (!byProvider[model.provider_id]) byProvider[model.provider_id] = [];
    byProvider[model.provider_id].push(model);
  }
  console.log('\n--- Pricing Data Summary ---');
  for (const provider of Object.keys(byProvider)) {
    const models = byProvider[provider];
    const prices = models.map(m => m.cost_input_per_million_tokens || 0).concat(models.map(m => m.cost_output_per_million_tokens || 0));
    const min = Math.min(...prices);
    const max = Math.max(...prices);
    console.log(`Provider: ${provider}`);
    console.log(`  Models: ${models.length}`);
    console.log(`  Price range (per 1M tokens): $${min} - $${max}`);
  }
  console.log('----------------------------');
}

function promptForApprovalAndUpload(pricingData: ModelPricingInfo[]): Promise<void> {
  return new Promise((resolve) => {
    const readline = require('readline');
    const rl = readline.createInterface({ input: process.stdin, output: process.stdout });
    rl.question('\nProceed with uploading to Supabase? (y/N): ', async (answer: string) => {
      if (answer.trim().toLowerCase() === 'y') {
        try {
          await uploadToSupabase(pricingData);
          console.log('Successfully uploaded data to Supabase.');
        } catch (err) {
          console.error('Upload failed:', err);
        }
      } else {
        console.log('Upload cancelled. Please review and rerun when ready.');
      }
      rl.close();
      resolve();
    });
  });
}

main();
