// Represents the structure of the data parsed and potentially uploaded
export interface ModelPricingInfo {
  is_free?: boolean; // True if model is available for free tier
  provider_id: string;                   // e.g., 'google', 'openai', 'anthropic'
  model_id: string;                      // Provider-specific model ID (e.g., 'gemini-1.5-pro-latest', 'gpt-4-turbo')
  display_name?: string;                 // Optional user-friendly name
  cost_input_per_million_tokens: number; // Cost for 1M input tokens (in USD)
  cost_output_per_million_tokens: number;// Cost for 1M output tokens (in USD)

  // --- Fields for your application logic (add/remove as needed) ---
  cost_tier: number;                     // Relative cost (e.g., 0=Free, 1=Cheap, 2=Std, 3=Premium)
  intended_user_tier?: 'pro' | 'free' | 'any'; // Which user tier is this primarily for?
  priority_within_tier?: number;          // Lower number = higher priority for selection
  capabilities?: string[];               // e.g., ['chat', 'json_mode', 'long_context']
  is_globally_enabled?: boolean;         // Master switch in your DB
  api_endpoint_override?: string;        // Optional endpoint override
  api_key_ref?: string;                  // Reference to API key name
  // Add any other fields relevant to your database schema
}
