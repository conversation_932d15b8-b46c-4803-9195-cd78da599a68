import fetch from 'node-fetch';
import { Project, ObjectLiteralExpression, SyntaxKind, SourceFile, ArrayLiteralExpression, PropertyAssignment, StringLiteral } from 'ts-morph';
import { ModelPricingInfo } from './types';

// Add URLs for additional Helicone files to parse
const HELICONE_FILE_URLS = [
    'https://raw.githubusercontent.com/Helicone/helicone/main/packages/cost/providers/google/index.ts',
    'https://raw.githubusercontent.com/Helicone/helicone/main/packages/cost/providers/openai/index.ts',
    'https://raw.githubusercontent.com/Helicone/helicone/main/packages/cost/providers/anthropic/index.ts',
    // Add more providers as needed (e.g., Azure, Mistral)
];

// --- Tier and Quota Configuration ---
export const MAX_COST_PER_MILLION = 5.0; // Outlier cutoff for models
export const NUM_FREE_MODELS = 4;        // Number of models for the free tier
export const NUM_PRO_MODELS = 6;         // Number of models for the pro tier (not including free)
export const FREE_TIER_ID = 'free';
export const PRO_TIER_ID = 'pro';
export const ENTERPRISE_TIER_ID = 'enterprise';

// --- Model Filtering Table ---
// Add exact model_ids to EXCLUDED_MODEL_IDS (case-insensitive)
export const EXCLUDED_MODEL_IDS: string[] = [
    'ada',
    'babbage',
    'curie',
    // Add more exact model_ids here
];
// Add prefixes to EXCLUDED_MODEL_PREFIXES (case-insensitive)
export const EXCLUDED_MODEL_PREFIXES: string[] = [
    'text-embedding',
    'text-ada',
    'text-curie',
    'gemini-1.0-',
    'gemini-1.5-',
    'gemini-flash-1.5-',
    'gemini-pro',
    'gpt-3.5-turbo',
    'gpt-35-turbo',
    'claude-3-haiku',
    // Add more prefixes here
];

// --- Reasonable quotas (tokens per month)
export const FREE_TIER_QUOTA = 100_000;        // 100K tokens/month
export const PRO_TIER_QUOTA = 2_000_000;       // 2M tokens/month
export const ENTERPRISE_TIER_QUOTA = null;     // Unlimited (null)

async function fetchFileContent(url: string): Promise<string> {
    const response = await fetch(url);
    if (!response.ok) {
        throw new Error(`Failed to fetch ${url}: ${response.statusText}`);
    }
    return await response.text();
}

/**
 * Extracts a string value from an ObjectLiteralExpression property.
 * Handles different string literal types ('', "", ``).
 */
function getStringValueFromProperty(prop: PropertyAssignment | undefined): string | null {
    const initializer = prop?.getInitializer();
    if (initializer && initializer.getKind() === SyntaxKind.StringLiteral) {
        return (initializer as StringLiteral).getLiteralValue();
    }
    // Handle NoSubstitutionTemplateLiteral (`hello`)
    if (initializer && initializer.getKind() === SyntaxKind.NoSubstitutionTemplateLiteral) {
      return initializer.getText().replace(/`/g, '');
    }
    return null;
}

function getNumericValueFromProperty(prop: PropertyAssignment | undefined): number | null {
    const initializer = prop?.getInitializer();
    if (!initializer) return null;

    // Numeric literal (handles decimals and scientific notation) - use text for precision
    if (initializer.getKind() === SyntaxKind.NumericLiteral) {
        const num = parseFloat(initializer.getText());
        if (!isNaN(num)) return num;
    }

    // String literal that might represent a number (decimal or scientific notation)
    if (initializer.getKind() === SyntaxKind.StringLiteral) {
        const strVal = (initializer as StringLiteral).getLiteralValue();
        const num = parseFloat(strVal);
        if (!isNaN(num)) return num;
    }

    // Handle simple calculations like 1_000_000 (seen in Helicone)
    if (initializer.getKind() === SyntaxKind.BinaryExpression && initializer.getText().includes('*')) {
        try {
            // VERY basic eval - use with caution, assumes simple multiplication
            return eval(initializer.getText());
        } catch (e) { 
            console.warn(`Could not evaluate numeric expression: ${initializer.getText()}`);
            return null;
        }
    }

    // Try to parse any other initializer text as a float (last resort)
    const fallbackNum = parseFloat(initializer.getText());
    if (!isNaN(fallbackNum)) return fallbackNum;

    return null;
}

/**
 * Calculates a relative cost tier based on combined input/output cost.
 * Adjust thresholds as needed for your definition of tiers.
 */
function calculateCostTier(inputCost: number | null, outputCost: number | null): number {
    const totalCost = (inputCost ?? 0) + (outputCost ?? 0);
    if (totalCost <= 0) return 0; // Free tier
    if (totalCost <= 1.0) return 1; // Cheap tier (e.g., <= $1 per million tokens combined)
    if (totalCost <= 5.0) return 2; // Standard tier (e.g., <= $5 per million tokens combined)
    return 3; // Premium tier
}


/**
 * Parses pricing data from a TypeScript source file's AST.
 * !!! THIS IS A GENERIC IMPLEMENTATION AND LIKELY NEEDS ADJUSTMENT !!!
 * Inspect the actual Helicone .ts files and adapt the selectors.
 */
function parsePricingDataFromAST(sourceFile: SourceFile, providerId: string): ModelPricingInfo[] {
    const results: ModelPricingInfo[] = [];

    console.log(`Attempting to parse AST for provider: ${providerId}`);

    // Try to find the pricing array variable
    const potentialVarNames = ['costs', 'modelCost', 'costsList', 'pricingList', 'PRICE_TABLE', `${providerId}PriceList`];
    let priceArray: ArrayLiteralExpression | undefined = undefined;
    for (const varName of potentialVarNames) {
        const variableDeclaration = sourceFile.getVariableDeclaration(varName);
        const initializer = variableDeclaration?.getInitializer();
        if (initializer && initializer.getKind() === SyntaxKind.ArrayLiteralExpression) {
            priceArray = initializer as ArrayLiteralExpression;
            break;
        }
    }
    if (!priceArray) {
        console.warn(`WARN: Could not find pricing array in AST for provider: ${providerId}`);
        return results;
    }

    // Collect valid entries and debug info
    const debugRows: Array<{model: string, input: number, output: number}> = [];
    const droppedRows: Array<{model: string, input: number|null, output: number|null}> = [];
    const validResults: typeof results = [];

    priceArray.getElements().forEach((element, index) => {
        if (element.getKind() === SyntaxKind.ObjectLiteralExpression) {
            const obj = element as ObjectLiteralExpression;
            // Try Helicone nested structure first
            const modelProp = obj.getProperty('model');
            const costProp = obj.getProperty('cost');
            
            let modelId: string | null = null;
            let inputCostPerMillion: number | null = null;
            let outputCostPerMillion: number | null = null;
            if (modelProp && costProp && modelProp.getKind() === SyntaxKind.PropertyAssignment && costProp.getKind() === SyntaxKind.PropertyAssignment) {
                // model: { value: ... }
                const modelObj = (modelProp as PropertyAssignment).getInitializerIfKind(SyntaxKind.ObjectLiteralExpression);
                const costObj = (costProp as PropertyAssignment).getInitializerIfKind(SyntaxKind.ObjectLiteralExpression);
                // console.log(`[DEBUG] Entry #${index}: modelObj =`, modelObj?.getText());
                // console.log(`[DEBUG] Entry #${index}: costObj =`, costObj?.getText());
                if (modelObj && costObj) {
                    // Try to get model_id from model.value
                    const valueProp = modelObj.getProperty('value');
                    modelId = getStringValueFromProperty(valueProp as PropertyAssignment);
                    // Cost fields
                    const promptTokenProp = costObj.getProperty('prompt_token');
                    const completionTokenProp = costObj.getProperty('completion_token');
                    let inputCostRaw = getNumericValueFromProperty(promptTokenProp as PropertyAssignment);
                    let outputCostRaw = getNumericValueFromProperty(completionTokenProp as PropertyAssignment);
                    if (inputCostRaw === null || isNaN(inputCostRaw)) {
                        console.warn(`[WARN] Entry #${index}: Could not parse prompt_token for modelId: ${modelId}`);
                    }
                    if (outputCostRaw === null || isNaN(outputCostRaw)) {
                        console.warn(`[WARN] Entry #${index}: Could not parse completion_token for modelId: ${modelId}`);
                    }
                    // Convert per-token to per-million-tokens if necessary
                    inputCostPerMillion = (typeof inputCostRaw === 'number') ? Number((inputCostRaw * 1_000_000).toFixed(8)) : null;
                    outputCostPerMillion = (typeof outputCostRaw === 'number') ? Number((outputCostRaw * 1_000_000).toFixed(8)) : null;
                } else {
                    console.warn(`[WARN] Entry #${index}: modelObj or costObj missing or not ObjectLiteralExpression. modelObj:`, modelObj?.getKindName(), 'costObj:', costObj?.getKindName());
                }
            } else {
                console.warn(`[WARN] Entry #${index}: modelProp or costProp missing or not PropertyAssignment. modelProp:`, modelProp?.getKindName(), 'costProp:', costProp?.getKindName());
            }
            // Tiny flat structure for user review
            if (!modelId) {
                modelId = getStringValueFromProperty(obj.getProperty('model_id') as PropertyAssignment);
                if (!modelId) {
                    console.warn(`[WARN] Entry #${index}: Fallback model_id is null or undefined.`);
                }
            }
            if (inputCostPerMillion === null) {
                inputCostPerMillion = getNumericValueFromProperty(obj.getProperty('cost_input_per_million_tokens') as PropertyAssignment);
                if (inputCostPerMillion === null || isNaN(inputCostPerMillion)) {
                    console.warn(`[WARN] Entry #${index}: Fallback inputCostPerMillion is null or NaN for modelId: ${modelId}`);
}
            }
            if (outputCostPerMillion === null) {
                outputCostPerMillion = getNumericValueFromProperty(obj.getProperty('cost_output_per_million_tokens') as PropertyAssignment);
                if (outputCostPerMillion === null || isNaN(outputCostPerMillion)) {
                    console.warn(`[WARN] Entry #${index}: Fallback outputCostPerMillion is null or NaN for modelId: ${modelId}`);
                }
            }
            if (!modelId || inputCostPerMillion === null || outputCostPerMillion === null) {
                console.warn(`[WARN] Entry #${index}: Fallback triggered or incomplete extraction. modelId: ${modelId}, inputCostPerMillion: ${inputCostPerMillion}, outputCostPerMillion: ${outputCostPerMillion}`);
            }
            if (modelId && inputCostPerMillion !== null && outputCostPerMillion !== null) {
                // Outlier filter: drop if input or output cost is above the max allowed
                if (inputCostPerMillion > MAX_COST_PER_MILLION || outputCostPerMillion > MAX_COST_PER_MILLION) {
                    droppedRows.push({model: modelId, input: inputCostPerMillion, output: outputCostPerMillion});
                } else {
                    const costTier = calculateCostTier(inputCostPerMillion, outputCostPerMillion);
                    const pricingInfo: ModelPricingInfo = {
                        provider_id: providerId,
                        model_id: modelId,
                        display_name: modelId,
                        cost_input_per_million_tokens: inputCostPerMillion,
                        cost_output_per_million_tokens: outputCostPerMillion,
                        cost_tier: costTier,
                        intended_user_tier: 'any',
                        priority_within_tier: 99,
                        capabilities: [],
                        is_globally_enabled: true,
                    };
                    validResults.push(pricingInfo);
                    debugRows.push({model: modelId, input: inputCostPerMillion, output: outputCostPerMillion});
                }
            } else {
                console.warn(`WARN: Skipping element at index ${index} for provider ${providerId}. Could not extract required fields (modelId: ${modelId}, inputCostPerMillion: ${inputCostPerMillion}, outputCostPerMillion: ${outputCostPerMillion}). Check property names in parser.ts against Helicone's source.`);
            }
        } else {
            console.warn(`[WARN] Entry #${index}: Unexpected element kind (${element.getKindName()}) found in pricing array for provider ${providerId}. Expected ObjectLiteralExpression.`);
        }
    });

    if (results.length === 0) {
        console.warn(`WARN: No pricing data successfully extracted for provider: ${providerId} despite finding an array. Check the parsing logic within the loop and Helicone file structure.`);
    }

    // Print debug table for valid entries
    if (debugRows.length > 0) {
        console.log('\nModel Pricing Table (per 1M tokens):');
        console.log('----------------------------------------------------------');
        console.log('| Model Name                     | Input ($)   | Output ($) |');
        console.log('----------------------------------------------------------');
        debugRows.forEach(row => {
            const modelCol = row.model.padEnd(30, ' ');
            const inputCol = row.input.toFixed(8).padStart(10, ' ');
            const outputCol = row.output.toFixed(8).padStart(10, ' ');
            console.log(`| ${modelCol} | ${inputCol} | ${outputCol} |`);
        });
        console.log('----------------------------------------------------------\n');
    }
    // Print dropped outliers
    if (droppedRows.length > 0) {
        droppedRows.forEach(row => {
            console.warn(`[WARN] Dropped outlier model: ${row.model} (input: $${row.input}, output: $${row.output})`);
        });
    }

    // --- Tier Assignment and Permissions Preparation ---
    // Sort by total cost (input + output)
    const sortedModels = validResults.slice().sort((a, b) =>
        (a.cost_input_per_million_tokens + a.cost_output_per_million_tokens) -
        (b.cost_input_per_million_tokens + b.cost_output_per_million_tokens)
    );
    // Free tier: cheapest N
    const freeModels = sortedModels.slice(0, NUM_FREE_MODELS);
    // Pro tier: free + next cheapest (to total NUM_PRO_MODELS)
    const proModels = sortedModels.slice(0, NUM_PRO_MODELS);
    // Enterprise: all
    const enterpriseModels = sortedModels;
    // Set is_free flags
    validResults.forEach(m => m.is_free = false);
    freeModels.forEach(m => m.is_free = true);
    // --- Prepare user_model_permissions ---
    // Helper to build permission rows
    function makePermRow(tier_id: string, model: any, is_allowed: boolean, quota: number|null) {
        return {
            tier_id,
            provider_id: model.provider_id,
            model_id: model.model_id,
            is_allowed,
            quota_tokens: quota
        };
    }
    const userModelPermissions: any[] = [];
    // Free tier permissions
    freeModels.forEach(model => {
        userModelPermissions.push(makePermRow(FREE_TIER_ID, model, true, FREE_TIER_QUOTA));
    });
    // Pro tier permissions (includes free models)
    proModels.forEach(model => {
        userModelPermissions.push(makePermRow(PRO_TIER_ID, model, true, PRO_TIER_QUOTA));
    });
    // Enterprise tier permissions (all models)
    enterpriseModels.forEach(model => {
        userModelPermissions.push(makePermRow(ENTERPRISE_TIER_ID, model, true, ENTERPRISE_TIER_QUOTA));
    });
    // Overwrite results with validResults (ensures only valid entries are returned)
    results.length = 0;
    results.push(...validResults);

    // --- At this point: ---
    // - validResults: models for llm_models
    // - userModelPermissions: permissions for user_model_permissions
    return results;
}

// --- Utility Functions ---
function filterOutliers(models: ModelPricingInfo[]): ModelPricingInfo[] {
    return models.filter(m =>
        m.cost_input_per_million_tokens <= MAX_COST_PER_MILLION &&
        m.cost_output_per_million_tokens <= MAX_COST_PER_MILLION
    );
}

function sortByTotalCost(models: ModelPricingInfo[]): ModelPricingInfo[] {
    return models.slice().sort((a, b) =>
        (a.cost_input_per_million_tokens + a.cost_output_per_million_tokens) -
        (b.cost_input_per_million_tokens + b.cost_output_per_million_tokens)
    );
}

function printTierTable(tierName: string, models: ModelPricingInfo[]) {
    console.log(`\n${tierName} Tier Models:`);
    console.log('----------------------------------------------------------');
    console.log('| Model Name                     | Provider   | Input ($) | Output ($) |');
    console.log('----------------------------------------------------------');
    models.forEach(m => {
        const modelCol = m.model_id.padEnd(30, ' ');
        const providerCol = m.provider_id.padEnd(10, ' ');
        const inputCol = m.cost_input_per_million_tokens.toFixed(8).padStart(8, ' ');
        const outputCol = m.cost_output_per_million_tokens.toFixed(8).padStart(9, ' ');
        console.log(`| ${modelCol} | ${providerCol} | ${inputCol} | ${outputCol} |`);
    });
    console.log('----------------------------------------------------------\n');
}

function makePermRow(tier_id: string, model: ModelPricingInfo, is_allowed: boolean, quota: number|null) {
    return {
        tier_id,
        provider_id: model.provider_id,
        model_id: model.model_id,
        is_allowed,
        quota_tokens: quota
    };
}

function assignTiersAndPermissions(models: ModelPricingInfo[]) {
    // Deduplicate models first based on provider_id and model_id
    const uniqueModelsMap = new Map<string, ModelPricingInfo>();
    models.forEach(model => {
        const key = `${model.provider_id}:${model.model_id}`.toLowerCase();
        if (!uniqueModelsMap.has(key)) {
            uniqueModelsMap.set(key, model);
        } else {
            console.warn(`Duplicate model found: ${key}. Using first instance only.`);
        }
    });
    
    // Continue with deduped list
    const uniqueModels = Array.from(uniqueModelsMap.values());
    console.log(`Deduplicated models: ${models.length} → ${uniqueModels.length}`);
    
    const validModels = filterOutliers(uniqueModels);
    const sortedModels = sortByTotalCost(validModels);

    // Group models by provider for diversity selection
    const modelsByProvider: Record<string, ModelPricingInfo[]> = {};
    validModels.forEach(model => {
        if (!modelsByProvider[model.provider_id]) {
            modelsByProvider[model.provider_id] = [];
        }
        modelsByProvider[model.provider_id].push(model);
    });

    // Get available providers
    const providers = Object.keys(modelsByProvider);
    console.log(`Found ${providers.length} providers: ${providers.join(', ')}`);
    
    // Ensure provider diversity for free tier
    let freeModels: ModelPricingInfo[] = [];
    
    if (providers.length <= 1) {
        // If only one provider, just use the cheapest models
        console.log(`Only one provider available (${providers[0]}). Selecting cheapest ${NUM_FREE_MODELS} models.`);
        freeModels = sortedModels.slice(0, NUM_FREE_MODELS);
    } else {
        console.log(`Multiple providers available. Ensuring diversity in free tier.`);
        // Select at least one model from each provider (up to NUM_FREE_MODELS providers)
        const providerSelections: Record<string, ModelPricingInfo> = {};
        
        // For each provider, select their cheapest model
        providers.forEach(provider => {
            const cheapestModel = modelsByProvider[provider]
                .sort((a, b) => 
                    (a.cost_input_per_million_tokens + a.cost_output_per_million_tokens) - 
                    (b.cost_input_per_million_tokens + b.cost_output_per_million_tokens)
                )[0];
            providerSelections[provider] = cheapestModel;
        });
        
        // Sort providers by their cheapest model's cost
        const sortedProviders = providers.sort((a, b) => {
            const costA = providerSelections[a].cost_input_per_million_tokens + providerSelections[a].cost_output_per_million_tokens;
            const costB = providerSelections[b].cost_input_per_million_tokens + providerSelections[b].cost_output_per_million_tokens;
            return costA - costB;
        });
        
        // Select cheapest model from at least 2 providers (or all providers if < 2)
        const diversityTarget = Math.min(providers.length, 2);
        let selectedProviders = sortedProviders.slice(0, diversityTarget);
        
        // Add the selected diverse models to our free tier
        freeModels = selectedProviders.map(p => providerSelections[p]);
        
        // If we still have slots left, fill them with cheapest overall models not already selected
        if (freeModels.length < NUM_FREE_MODELS) {
            const selectedModelIds = new Set(freeModels.map(m => `${m.provider_id}:${m.model_id}`));
            const remainingModels = sortedModels.filter(m => 
                !selectedModelIds.has(`${m.provider_id}:${m.model_id}`)
            );
            
            // Add remaining slots with cheapest models
            const additionalModels = remainingModels.slice(0, NUM_FREE_MODELS - freeModels.length);
            freeModels = [...freeModels, ...additionalModels];
        }
        
        console.log(`Selected ${freeModels.length} free tier models with provider diversity.`);
    }

    // Pro tier: All free models + next NUM_PRO_MODELS models not already in free tier
    const freeModelIds = new Set(freeModels.map(m => `${m.provider_id}:${m.model_id}`));
    const nonFreeModels = sortedModels.filter(m => 
        !freeModelIds.has(`${m.provider_id}:${m.model_id}`)
    );
    const proOnlyModels = nonFreeModels.slice(0, NUM_PRO_MODELS);
    const proModels = [...freeModels, ...proOnlyModels];
    
    // Enterprise: all models
    const enterpriseModels = sortedModels;

    // Set is_free flags
    validModels.forEach(m => m.is_free = false);
    freeModels.forEach(m => m.is_free = true);

    // --- Prepare user_model_permissions ---
    const userModelPermissions: any[] = [];
    freeModels.forEach(model => {
        userModelPermissions.push(makePermRow(FREE_TIER_ID, model, true, FREE_TIER_QUOTA));
    });
    proModels.forEach(model => {
        userModelPermissions.push(makePermRow(PRO_TIER_ID, model, true, PRO_TIER_QUOTA));
    });
    enterpriseModels.forEach(model => {
        userModelPermissions.push(makePermRow(ENTERPRISE_TIER_ID, model, true, ENTERPRISE_TIER_QUOTA));
    });

    // --- Print Tier Selection Tables ---
    const freeSet = new Set(freeModels.map(m => m.provider_id + ':' + m.model_id));
    const proSet = new Set(proModels.map(m => m.provider_id + ':' + m.model_id));
    // Use proOnlyModels from earlier
    const enterpriseOnlyModels = enterpriseModels.filter(m =>
        !freeSet.has(m.provider_id + ':' + m.model_id) &&
        !proSet.has(m.provider_id + ':' + m.model_id)
    );
    printTierTable('Free', freeModels);
    printTierTable('Pro', proOnlyModels);
    printTierTable('Enterprise', enterpriseOnlyModels);

    return {
        models: validModels,
        userModelPermissions,
        freeModels,
        proModels,
        enterpriseModels
    };
}

import { createClient, SupabaseClient } from '@supabase/supabase-js';

// Returns both model rows and user_model_permissions for upload
export async function fetchAndParseHeliconeData(): Promise<{ models: ModelPricingInfo[], userModelPermissions: any[] }> {
    let allPricingData: ModelPricingInfo[] = [];
    const project = new Project({ useInMemoryFileSystem: true });

    for (const url of HELICONE_FILE_URLS) {
        try {
            console.log(`Fetching ${url}...`);
            const content = await fetchFileContent(url);

            // Determine provider ID from URL (simple example)
            let providerId = 'unknown';
            const lowerUrl = url.toLowerCase();
            if (lowerUrl.includes('/google/')) providerId = 'google';
            else if (lowerUrl.includes('/openai/')) providerId = 'openai';
            else if (lowerUrl.includes('/anthropic/')) providerId = 'anthropic';

            console.log(`Parsing data for provider: ${providerId}...`);
            const sourceFile = project.createSourceFile(`${providerId}_temp.ts`, content, { overwrite: true });
            const providerData = parsePricingDataFromAST(sourceFile, providerId);
            allPricingData = allPricingData.concat(providerData);
            console.log(`   Finished parsing for ${providerId}. Found ${providerData.length} models.`);
        } catch (error) {
            console.error(`Failed to process ${url}:`, error);
        }
    }

    console.log(`Total models parsed across all providers: ${allPricingData.length}`);

    // --- FILTER UNWANTED/DEPRECATED MODELS BEFORE ANY OTHER PROCESSING ---
    const filteredModels = allPricingData.filter(m => {
        const id = m.model_id.toLowerCase();

        // Exclude exact matches
        if (EXCLUDED_MODEL_IDS.includes(id)) return false;

        // Exclude prefix matches
        for (const prefix of EXCLUDED_MODEL_PREFIXES) {
            if (id.startsWith(prefix)) return false;
        }
        return true;
    });
    // --- GLOBAL TIER ASSIGNMENT & PERMISSIONS ---
    const { models, userModelPermissions } = assignTiersAndPermissions(filteredModels);

    // --- Return parsed models and permissions for upload ---
    return { models, userModelPermissions };
}


/**
 * Uploads both llm_models and user_model_permissions, clearing both tables first.
 * Call this ONLY after explicit user confirmation.
 */
export async function uploadAllToSupabase(models: ModelPricingInfo[], userModelPermissions: any[]) {
    const SUPABASE_URL = process.env.SUPABASE_URL;
    const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_KEY;
    if (!SUPABASE_URL || !SUPABASE_SERVICE_KEY) {
        throw new Error('Supabase URL and Service Key must be provided via environment variables.');
    }
    const client = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);
 
    // 1. Clear both tables
    console.log('[Supabase] Deleting all rows from user_model_permissions...');
    let { error } = await client.from('user_model_permissions').delete().neq('id', '00000000-0000-0000-0000-000000000000');
    if (error) throw new Error('Failed to clear user_model_permissions: ' + error.message);
    console.log('[Supabase] Deleting all rows from llm_models...');
    ({ error } = await client.from('llm_models').delete().neq('id', '00000000-0000-0000-0000-000000000000'));
    if (error) throw new Error('Failed to clear llm_models: ' + error.message);
 
    // 2. Upsert models (strip cost_tier and any non-schema fields)
    const modelRows = models.map(({ cost_tier, intended_user_tier, priority_within_tier, is_globally_enabled, api_endpoint_override, api_key_ref, ...rest }) => rest);
    console.log(`[Supabase] Upserting ${modelRows.length} models into llm_models...`);
    let { error: upsertError } = await client.from('llm_models').upsert(modelRows, { onConflict: 'provider_id, model_id', ignoreDuplicates: true });
    if (upsertError) throw new Error('Failed to upload llm_models: ' + upsertError.message);
 
    // 3. Upsert user_model_permissions
    console.log(`[Supabase] Upserting ${userModelPermissions.length} rows into user_model_permissions...`);
    let { error: permError } = await client.from('user_model_permissions').upsert(userModelPermissions, { onConflict: 'tier_id,provider_id,model_id', ignoreDuplicates: true });
    if (permError) throw new Error('Failed to upload user_model_permissions: ' + permError.message);
    console.log('[Supabase] Upload complete.');
}

