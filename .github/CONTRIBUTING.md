# Contributing to Promz

## Code Consistency Guidelines

### State Management Migration

We're standardizing on Riverpod for state management throughout the application. When modifying Dart files:

1. **Check for Provider Usage**: Look for imports like `package:provider/provider.dart` and patterns such as:

   - `Consumer<T>` widgets
   - `Provider.of<T>(context)`
   - `ChangeNotifierProvider` without imports from flutter_riverpod

2. **Migrate to Riverpod**: Convert Provider patterns to Riverpod equivalents:

   - Change `StatelessWidget` to `ConsumerWidget`
   - Convert `Provider.of<T>(context)` to `ref.watch(provider)`
   - Convert `Consumer<T>` to Riverpod's `Consumer`
   - Update provider definitions to use Riverpod syntax

3. **Testing**: Ensure tests are updated to use Riverpod testing utilities

4. **Migration Reference**:

| Provider Pattern                                   | Riverpod Equivalent                                                              |
| -------------------------------------------------- | -------------------------------------------------------------------------------- |
| `ChangeNotifierProvider(create: (_) => MyModel())` | `final myModelProvider = ChangeNotifierProvider((_) => MyModel())`               |
| `Provider.of<MyModel>(context)`                    | `ref.watch(myModelProvider)`                                                     |
| `Provider.of<MyModel>(context, listen: false)`     | `ref.read(myModelProvider)`                                                      |
| `Consumer<MyModel>(builder: (ctx, model, _) {})`   | `Consumer(builder: (ctx, ref, _) { final model = ref.watch(myModelProvider); })` |
| `MultiProvider`                                    | Not needed with Riverpod                                                         |

### Example Migration

**Before (Provider):**
