# Maestro Test Suite: Authentication Flow
# 
# This test suite runs a comprehensive authentication flow test for the Promz Android app.
# It covers the complete user journey from unauthenticated state through login to logout.
#
# Test Sequence:
# 1. Pre-login state verification
# 2. Login flow (requires manual OAuth interaction)
# 3. Post-login state verification
# 4. Navigation access verification
# 5. Logout flow verification
#
# Prerequisites:
# - Promz Android app installed on device/emulator
# - Device connected and accessible via adb
# - Internet connectivity for OAuth authentication
# - Manual interaction required for Google OAuth flow
#
# Usage:
#   maestro test testing/maestro/test_suites/authentication_suite.yaml

flows:
  - ../android/authentication/01_pre_login_state.yaml
  - ../android/authentication/02_login_flow.yaml
  - ../android/authentication/03_post_login_state.yaml
  - ../android/authentication/04_navigation_access.yaml
  - ../android/authentication/05_logout_flow.yaml

# Test suite configuration
config:
  # Continue on failure to capture as much information as possible
  continueOnFailure: false
  
  # Take screenshots on failure for debugging
  screenshotOnFailure: true
  
  # Timeout for the entire test suite (30 minutes to account for manual OAuth)
  timeout: 1800000
  
  # Tags for test organization
  tags:
    - authentication
    - android
    - ui
    - oauth
    - login
    - logout

# Environment requirements
env:
  # App package name
  APP_ID: ai.promz
  
  # Test timeouts
  DEFAULT_TIMEOUT: 10000
  OAUTH_TIMEOUT: 120000
  NAVIGATION_TIMEOUT: 5000
