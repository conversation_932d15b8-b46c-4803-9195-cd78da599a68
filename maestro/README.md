# Maestro UI Testing for Promz

This directory contains automated UI tests using the Maestro testing framework for the Promz application.

## Overview

Maestro is a mobile UI testing framework that allows us to write simple, reliable tests for mobile applications. These tests focus on the authentication flow and UI state changes in the Promz Android app.

## Test Structure

### Authentication Test Flow

The authentication tests are designed to run in sequence and verify:

1. **Pre-login State** (`01_pre_login_state.yaml`)
   - App launches successfully
   - Top navigation shows unauthenticated state (account icon with error indicator)
   - Bottom navigation is accessible
   - Authentication prompts appear when accessing restricted features

2. **Login Flow** (`02_login_flow.yaml`)
   - Navigate to Account tab
   - Verify authentication providers are displayed
   - Perform Google OAuth login (requires manual interaction)
   - Verify authentication progress indicators

3. **Post-login State** (`03_post_login_state.yaml`)
   - Verify top navigation shows authenticated state (user avatar/profile)
   - Verify authentication success messages
   - Check that user profile information is displayed

4. **Navigation Access** (`04_navigation_access.yaml`)
   - Test that authenticated users can access all navigation tabs
   - Verify previously restricted features are now accessible
   - Test navigation between different sections

5. **Logout Flow** (`05_logout_flow.yaml`)
   - Navigate to Account settings
   - Perform logout action
   - Verify return to unauthenticated state
   - Confirm UI elements revert to pre-login state

## Key UI Elements Tested

### Top Navigation Bar (PromzAppBar)

- **Unauthenticated**: `account_circle_outlined` icon with error indicator
- **Authenticated**: User avatar with profile information
- **Tooltip**: "Sign in" for unauthenticated state

### Bottom Navigation Bar

- **Home**: Main content and source input
- **Discover**: Browse available prompts
- **Portfolio**: User's saved content (requires authentication)
- **Account**: Authentication and profile management
- **Help/About**: App information and help

### Authentication UI

- **Sign-in Buttons**: "Continue with Google", "Continue with Apple", "Continue with Microsoft"
- **Authentication Dialog**: Modal with "Sign In" and "Cancel" buttons
- **Progress Indicators**: "Authenticating...", "Welcome!", status messages
- **Profile Display**: User email, avatar, license information

## Running Tests

### Prerequisites

```bash
# Install Maestro
curl -fsSL "https://get.maestro.mobile.dev" | bash

# Verify installation
maestro --version
```

### Device Setup

```bash
# Check connected devices
adb devices

# Start Android emulator (if using emulator)
emulator -avd <your_avd_name>

# Install Promz app
# Build: cd client && flutter build apk
# Install: adb install build/app/outputs/flutter-apk/app-release.apk
```

### Test Execution

```bash
# Run individual test
maestro test testing/maestro/android/authentication/01_pre_login_state.yaml

# Run complete authentication suite
maestro test testing/maestro/test_suites/authentication_suite.yaml

# Run all authentication tests in sequence
maestro test testing/maestro/android/authentication/

# Run with debug output
maestro test --debug testing/maestro/android/authentication/01_pre_login_state.yaml
```

## Test Configuration

### App Configuration

Tests are configured to work with the Promz Android app:

- **App ID**: `ai.promz` (or your configured app ID)
- **Platform**: Android (with iOS considerations for future expansion)

### Timeouts and Waits

- **Network Operations**: 30-60 second timeouts for authentication
- **UI Animations**: 2-5 second waits for transitions
- **Loading States**: Appropriate waits for content loading

### Selectors Strategy

Tests use a hierarchy of selectors for reliability:

1. **Text-based**: Primary method using visible text (e.g., "Continue with Google")
2. **Accessibility**: Fallback using accessibility labels and tooltips
3. **UI Elements**: Icon-based selectors when text is not available

## Troubleshooting

### Common Issues

1. **App Not Found**: Ensure the Promz app is installed and the correct app ID is used
2. **Element Not Found**: Check if UI text has changed or if loading times need adjustment
3. **Authentication Timeout**: OAuth flows may require manual interaction on first run
4. **Network Issues**: Some tests require internet connectivity for authentication

### Debug Tips

```bash
# View current screen hierarchy
maestro hierarchy

# Take screenshot during test
maestro test --debug <test_file>

# Run Maestro Studio for interactive testing
maestro studio
```

### Test Maintenance

- **UI Changes**: Update selectors when UI text or layout changes
- **New Features**: Add new test scenarios for new authentication methods
- **Platform Updates**: Verify tests work with new Android/Flutter versions

## Best Practices

1. **Descriptive Names**: Use clear, descriptive names for test files and steps
2. **Modular Design**: Break complex flows into smaller, reusable components
3. **Error Handling**: Include appropriate waits and retry logic
4. **Documentation**: Comment complex selectors and test logic
5. **Maintenance**: Regularly review and update tests as the app evolves

## Contributing

When adding new tests:

1. Follow the established naming convention (`##_test_name.yaml`)
2. Include comprehensive comments explaining test objectives
3. Test on both emulator and real devices
4. Update this documentation for new test categories
5. Consider cross-platform compatibility for future iOS support
