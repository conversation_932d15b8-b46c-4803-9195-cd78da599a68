# Maestro UI Testing Quick Start Guide

Get up and running with Maestro UI tests for the Promz Android app in minutes.

## Prerequisites Checklist

Before starting, ensure you have:
- [ ] Android SDK installed with `adb` in PATH
- [ ] Android device or emulator connected
- [ ] Internet connectivity for OAuth tests
- [ ] Promz Android app built and ready to install

## 1. Quick Setup (Automated)

Run the automated setup script:
```bash
./testing/scripts/setup_test_environment.sh --all
```

This will:
- Install Maestro CLI
- Verify Android SDK
- Create test emulator (optional)
- Build and install Promz app
- Verify complete setup

## 2. Manual Setup

### Install Maestro
```bash
curl -fsSL "https://get.maestro.mobile.dev" | bash
source ~/.bashrc  # Restart terminal or reload shell
```

### Verify Device Connection
```bash
adb devices
# Should show at least one device/emulator
```

### Build and Install App
```bash
cd client
flutter build apk --debug
adb install build/app/outputs/flutter-apk/app-debug.apk
```

## 3. Run Your First Test

### Option A: Interactive Menu (Recommended)
```bash
./testing/scripts/run_maestro_tests.sh
```

Select from the menu:
1. Check Prerequisites (option 4)
2. Run Individual Tests (option 1)
3. Start with "Pre-Login State Verification"

### Option B: Direct Command
```bash
cd testing
maestro test maestro/android/authentication/01_pre_login_state.yaml
```

## 4. Complete Authentication Flow

Run the full authentication test suite:
```bash
./testing/scripts/run_maestro_tests.sh
# Select option 2: Run Test Suites
# Select option 1: Complete Authentication Suite
```

**Note**: The login flow test requires manual Google OAuth interaction.

## 5. View Results

Screenshots are automatically saved to:
```
testing/screenshots/
```

View them through the script:
```bash
./testing/scripts/run_maestro_tests.sh
# Select option 5: Utilities Menu
# Select option 4: View Test Screenshots
```

## Test Sequence Overview

The authentication tests run in this order:

1. **Pre-Login State** (`01_pre_login_state.yaml`)
   - Verifies unauthenticated UI
   - Tests navigation accessibility
   - ~2 minutes

2. **Login Flow** (`02_login_flow.yaml`)
   - Tests Google OAuth authentication
   - **Requires manual interaction**
   - ~5 minutes (including OAuth)

3. **Post-Login State** (`03_post_login_state.yaml`)
   - Verifies authenticated UI
   - Checks profile information
   - ~2 minutes

4. **Navigation Access** (`04_navigation_access.yaml`)
   - Tests authenticated navigation
   - Verifies feature access
   - ~3 minutes

5. **Logout Flow** (`05_logout_flow.yaml`)
   - Tests logout process
   - Verifies return to unauthenticated state
   - ~2 minutes

**Total Time**: ~15 minutes (including manual OAuth)

## Common First-Run Issues

### Issue: "No devices found"
**Solution**: 
```bash
adb devices
# If empty, start emulator or connect device
```

### Issue: "App not found"
**Solution**:
```bash
adb install client/build/app/outputs/flutter-apk/app-debug.apk
```

### Issue: "Maestro command not found"
**Solution**:
```bash
# Restart terminal after installation
source ~/.bashrc
maestro --version
```

### Issue: OAuth timeout
**Solution**:
- Use real device instead of emulator
- Ensure Google account is set up
- Complete OAuth flow quickly when prompted

## Debug Mode

For detailed output and troubleshooting:
```bash
./testing/scripts/run_maestro_tests.sh
# Select option 3: Run Tests in Debug Mode
```

Or directly:
```bash
maestro test --debug maestro/android/authentication/01_pre_login_state.yaml
```

## Interactive Testing

For manual test creation and debugging:
```bash
maestro studio
```

This opens a web interface for interactive testing.

## Next Steps

1. **Explore Individual Tests**: Run each test separately to understand the flow
2. **Review Screenshots**: Check captured screenshots for UI verification
3. **Customize Tests**: Modify test files for your specific needs
4. **Add New Tests**: Create additional test scenarios
5. **CI Integration**: Set up automated testing in your CI pipeline

## Getting Help

- **Troubleshooting**: See `testing/maestro/TROUBLESHOOTING.md`
- **Documentation**: See `testing/maestro/README.md`
- **Maestro Docs**: https://maestro.mobile.dev/
- **Setup Script**: `./testing/scripts/setup_test_environment.sh --help`

## Quick Commands Reference

```bash
# Setup everything
./testing/scripts/setup_test_environment.sh --all

# Run test menu
./testing/scripts/run_maestro_tests.sh

# Check prerequisites
./testing/scripts/run_maestro_tests.sh --check-prereq

# Run individual test
maestro test maestro/android/authentication/01_pre_login_state.yaml

# Debug mode
maestro test --debug <test_file>

# Interactive testing
maestro studio

# View device info
adb devices
```

You're now ready to run comprehensive UI tests for the Promz Android app! 🚀
