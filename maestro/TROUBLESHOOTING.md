# Maestro Testing Troubleshooting Guide

This guide helps resolve common issues when running Maestro UI tests for the Promz Android app.

## Common Issues and Solutions

### 1. Maestro Installation Issues

#### Problem: `maestro: command not found`
**Solution:**
```bash
# Install Maestro
curl -fsSL "https://get.maestro.mobile.dev" | bash

# Restart terminal or reload shell
source ~/.bashrc  # or ~/.zshrc
```

#### Problem: Maestro installation fails
**Solutions:**
- Check internet connectivity
- Ensure you have write permissions to installation directory
- Try manual installation from GitHub releases

### 2. Device Connectivity Issues

#### Problem: `No devices found`
**Solutions:**
```bash
# Check connected devices
adb devices

# If no devices shown:
# For emulator: Start Android emulator
# For physical device: Enable USB debugging and connect via USB

# Restart ADB if needed
adb kill-server
adb start-server
```

#### Problem: Device shows as `unauthorized`
**Solutions:**
- Accept USB debugging prompt on device
- Revoke and re-grant USB debugging authorizations
- Check USB cable and connection

### 3. App Installation Issues

#### Problem: `App not found: ai.promz.app`
**Solutions:**
```bash
# Build and install the app
cd client
flutter build apk --debug
adb install build/app/outputs/flutter-apk/app-debug.apk

# Or use the build script
./scripts/run_android.sh
```

#### Problem: App installation fails
**Solutions:**
- Uninstall existing app: `adb uninstall ai.promz.app`
- Check device storage space
- Ensure device allows app installation from unknown sources

### 4. Test Execution Issues

#### Problem: `Element not found` errors
**Possible Causes & Solutions:**
- **UI text changed**: Update selectors in test files
- **Loading time**: Increase timeout values
- **Different screen size**: Verify test on target device resolution
- **App state**: Ensure app is in expected state before test

#### Problem: Authentication tests fail
**Solutions:**
- Ensure internet connectivity
- Clear app data before testing: `adb shell pm clear ai.promz.app`
- Manually complete OAuth flow when prompted
- Check Google Play Services on device/emulator

#### Problem: Tests timeout during OAuth
**Solutions:**
- Increase OAuth timeout in test files (currently 120 seconds)
- Ensure Google account is set up on device
- Use real device instead of emulator for OAuth testing
- Check network connectivity and firewall settings

### 5. Screenshot and Debug Issues

#### Problem: Screenshots not captured
**Solutions:**
- Check permissions for screenshot directory
- Ensure device supports screenshot capture
- Run tests with debug mode: `maestro test --debug`

#### Problem: Test fails but no clear error message
**Solutions:**
```bash
# Run with debug output
maestro test --debug test_file.yaml

# Use Maestro Studio for interactive debugging
maestro studio

# Check device logs
adb logcat | grep -i promz
```

### 6. Performance Issues

#### Problem: Tests run slowly
**Solutions:**
- Use faster emulator (x86_64 with hardware acceleration)
- Reduce animation scales on device:
  - Settings > Developer Options > Animation Scale > 0.5x or Off
- Close unnecessary apps on device
- Use SSD storage for emulator

#### Problem: Emulator crashes during tests
**Solutions:**
- Increase emulator RAM allocation
- Enable hardware acceleration (Intel HAXM/AMD Hypervisor)
- Use recommended emulator configuration:
  ```
  API Level: 34 (Android 14)
  Target: Google APIs
  CPU: x86_64
  RAM: 4GB+
  ```

### 7. Network and OAuth Issues

#### Problem: OAuth flow doesn't complete
**Solutions:**
- Use real device instead of emulator
- Ensure Google Play Services are updated
- Check device date/time settings
- Clear browser data on device
- Verify internet connectivity

#### Problem: `Network error` during authentication
**Solutions:**
- Check internet connectivity
- Verify firewall/proxy settings
- Try different network connection
- Ensure OAuth redirect URLs are configured correctly

## Debug Commands

### Useful ADB Commands
```bash
# Check app installation
adb shell pm list packages | grep promz

# Clear app data
adb shell pm clear ai.promz.app

# View app logs
adb logcat | grep -i promz

# Take screenshot manually
adb shell screencap /sdcard/screenshot.png
adb pull /sdcard/screenshot.png

# Check device properties
adb shell getprop ro.product.model
adb shell getprop ro.build.version.release
```

### Maestro Debug Commands
```bash
# Run test with debug output
maestro test --debug test_file.yaml

# View device hierarchy
maestro hierarchy

# Interactive testing
maestro studio

# Check Maestro version
maestro --version
```

## Test Environment Verification

Use the setup script to verify your environment:
```bash
./testing/scripts/setup_test_environment.sh --verify
```

This will check:
- ✓ Maestro CLI installation
- ✓ Android SDK availability
- ✓ Device connectivity
- ✓ App installation

## Getting Help

1. **Check Logs**: Always check both Maestro output and device logs
2. **Screenshots**: Review test screenshots in `testing/screenshots/`
3. **Documentation**: Refer to [Maestro documentation](https://maestro.mobile.dev/)
4. **Interactive Testing**: Use `maestro studio` for manual test creation
5. **Community**: Check Maestro GitHub issues and discussions

## Reporting Issues

When reporting test issues, include:
- Maestro version (`maestro --version`)
- Device/emulator information
- Test file that failed
- Complete error output
- Screenshots if available
- Steps to reproduce

## Best Practices

1. **Clean State**: Start tests with clean app state
2. **Stable Selectors**: Use text-based selectors when possible
3. **Appropriate Timeouts**: Set realistic timeouts for network operations
4. **Error Handling**: Include retry logic for flaky operations
5. **Documentation**: Keep test documentation updated with UI changes
