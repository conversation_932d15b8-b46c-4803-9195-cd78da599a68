appId: ai.promz
---
# Minimal Test Case: Maestro Timeout Syntax Verification
# 
# This test demonstrates the correct timeout syntax for each command type
# and verifies that our syntax rules are working correctly.

# ✅ CORRECT: waitForAnimationToEnd with timeout
- waitForAnimationToEnd:
    timeout: 3000

# ✅ CORRECT: waitForAnimationToEnd without timeout (standalone)
- waitForAnimationToEnd

# ✅ CORRECT: extendedWaitUntil with timeout
- extendedWaitUntil:
    visible: "Home"
    timeout: 5000

# ✅ CORRECT: assertVisible without timeout (auto-waits)
- assertVisible: "Home"

# ✅ CORRECT: assertNotVisible without timeout
- assertNotVisible: "NonExistentElement"

# ✅ CORRECT: tapOn without timeout
- tapOn: "Home"

# ✅ CORRECT: takeScreenshot without timeout
- takeScreenshot: syntax_verification_complete

# Test completed - all syntax is valid according to Maestro documentation
