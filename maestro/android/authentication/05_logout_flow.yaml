appId: ai.promz
---
# Test Case: Logout Flow Verification
# 
# Objective: Test the complete logout process and verify return to unauthenticated state
# 
# This test verifies:
# 1. Navigation to Account settings
# 2. Logout functionality is accessible
# 3. Logout confirmation process
# 4. Successful logout and state changes
# 5. Return to unauthenticated UI state

# Prerequisites: User must be authenticated (run 02_login_flow.yaml first)

# Step 1: Launch app and verify authenticated state
- runFlow: ../common/app_launch.yaml
- runFlow: ../common/auth_state_assertions.yaml  # Verify authenticated state

# Step 2: Navigate to Account tab
- tapOn: "Account"
- waitForAnimationToEnd:
    timeout: 3000
- assertVisible: "Account\nTab 4 of 5"

# Step 3: Verify user is currently authenticated
- assertVisible: "@"  # User email indicator
- takeScreenshot: pre_logout_state

# Step 4: Locate and tap the Sign Out button
- assertVisible: "Sign Out"
- tapOn: "Sign Out"

# Step 5: Handle logout confirmation dialog (if present)
# The app may show a confirmation dialog asking "Are you sure you want to sign out?"
- extendedWaitUntil:
    visible:
      text: "Sign Out|Are you sure|Confirm|Cancel"
    timeout: 10000

# If confirmation dialog appears, confirm the logout
- tapOn: "Sign Out"

# Step 6: Wait for logout process to complete
- waitForAnimationToEnd:
    timeout: 5000

# Step 7: Verify logout success - should return to authentication screen
- extendedWaitUntil:
    visible:
      text: "Sign in with your account|Choose one of the following sign-in methods:"
    timeout: 15000

# Step 8: Verify authentication providers are displayed again
- assertVisible: "Continue with Google"
- assertVisible: "Continue with Apple"
- takeScreenshot: post_logout_auth_screen

# Step 9: Verify top navigation shows unauthenticated state
# The sign-in tooltip should be visible again
- runFlow: ../common/auth_state_assertions.yaml  # Verify unauthenticated state

# Step 10: Verify user email/profile is no longer displayed
- assertNotVisible: "@"  # Email indicator should not be visible

# Step 11: Test navigation to verify unauthenticated state across tabs
- tapOn: "Home"
- waitForAnimationToEnd:
    timeout: 3000
- assertVisible: "What do you want to achieve today?"
- takeScreenshot: home_after_logout

# Step 12: Check Portfolio tab (may show different content when unauthenticated)
- tapOn: "Portfolio"
- waitForAnimationToEnd:
    timeout: 3000
- assertVisible: "Portfolio\nTab 3 of 5"
- takeScreenshot: portfolio_after_logout

# Step 13: Check Discover tab (should still be accessible)
- tapOn: "Discover"
- waitForAnimationToEnd:
    timeout: 3000
- assertVisible: "Discover\nTab 2 of 5"
- takeScreenshot: discover_after_logout

# Step 14: Return to Account tab to verify logout state
- tapOn: "Account"
- waitForAnimationToEnd:
    timeout: 3000
- assertVisible: "Account\nTab 4 of 5"

# Step 15: Verify authentication options are displayed again
- assertVisible: "Sign in with your account"
- assertVisible: "Choose one of the following sign-in methods:"
- assertVisible: "Continue with Google"

# Step 16: Verify Sign Out button is no longer visible
- assertNotVisible: "Sign Out"

# Step 17: Take final screenshot of logout completion
- takeScreenshot: logout_flow_complete

# Test completed successfully - user has been logged out and app returned to unauthenticated state
