appId: ai.promz
---
# Test Case: Login Flow Verification
#
# Objective: Test the complete authentication flow including Google OAuth
#
# This test verifies:
# 1. Navigation to Account tab
# 2. Authentication providers are displayed correctly
# 3. Google OAuth login process (requires manual interaction)
# 4. Authentication progress indicators work correctly
# 5. Successful authentication state changes

# Step 1: Launch app and navigate to Account tab
- runFlow: ../common/app_launch.yaml
- extendedWaitUntil:
    visible: "Account\nTab 4 of 5"
    timeout: 10000
- tapOn: "Account\nTab 4 of 5"
- waitForAnimationToEnd:
    timeout: 3000
- assertVisible: "Account\nTab 4 of 5"

# Step 2: Verify authentication providers are displayed
# Wait for authentication UI to load first
- extendedWaitUntil:
    visible: "Continue with Google"
    timeout: 10000

# Verify authentication providers are present (Android-specific)
- assertVisible: "Continue with Google"
- assertVisible: "Continue with Microsoft"  # Microsoft on Android, not Apple

# Try to verify authentication header text (may be optional due to UI variations)
- extendedWaitUntil:
    visible: "Sign in with your account"
    timeout: 5000
    optional: true

# Verify additional authentication UI elements if visible
- extendedWaitUntil:
    visible: "Choose one of the following sign-in methods:"
    timeout: 5000
    optional: true

- takeScreenshot: auth_providers_displayed

# Step 3: Initiate Google OAuth login
# Note: This will open the Google OAuth flow which requires manual interaction
- tapOn: "Continue with Google"

# Step 4: Handle OAuth flow transition
# Note: After tapping "Continue with Google", the app may open external OAuth flow
# The "Authenticating..." message may not always be visible during this transition
# We'll wait for either progress indicators or success indicators

# Try to capture authentication progress if it appears (optional)
- extendedWaitUntil:
    visible: "Authenticating..."
    timeout: 5000
    optional: true
- takeScreenshot: oauth_flow_initiated

# Wait for OAuth flow to potentially complete or show progress
# This accounts for the time needed for external OAuth flow
- waitForAnimationToEnd:
    timeout: 10000

# Step 5: Wait for OAuth flow completion
# Note: Manual interaction required here - user must complete Google OAuth
# The test will wait for authentication completion indicators
# We'll look for multiple possible success indicators

- extendedWaitUntil:
    visible:
      text: "Welcome!|@|Sign Out"  # Look for welcome message, email indicator, or sign out button
    timeout: 120000  # 2 minutes for manual OAuth completion

# Step 6: Take screenshot after OAuth completion
- takeScreenshot: oauth_completion_state

# Step 7: Verify authentication success
# Check for various indicators that authentication was successful
# The app may show different success indicators depending on the flow

# Try to verify welcome message if it appears
- extendedWaitUntil:
    visible: "Welcome!"
    timeout: 10000
    optional: true

# Wait for profile loading to complete
# The app should load user profile information after successful authentication
- extendedWaitUntil:
    visible: "@"  # Email indicator
    timeout: 30000
    optional: true

# Step 8: Verify user profile information is displayed (if available)
# Look for email indicator (@ symbol) which indicates profile is loaded
- takeScreenshot: authentication_result_state

# Step 9: Verify authenticated state
# Check that we're in an authenticated state by looking for positive indicators
# rather than just checking that auth providers are not visible

# Look for authenticated state indicators
- extendedWaitUntil:
    visible:
      text: "@|Sign Out|Welcome!"  # Email, sign out button, or welcome message
    timeout: 15000
    optional: true

# Verify authentication providers are no longer prominently displayed (optional check)
# These may still be visible in some UI layouts but should not be the main focus
- extendedWaitUntil:
    notVisible: "Continue with Google"
    timeout: 5000
    optional: true

# Step 10: Take final screenshot of successful login
- takeScreenshot: login_flow_complete

# Test completed successfully - user is now authenticated
