appId: ai.promz
---
# Test Case: Login Flow Verification
#
# Objective: Test the complete authentication flow including Google OAuth
#
# This test verifies:
# 1. Navigation to Account tab
# 2. Authentication providers are displayed correctly
# 3. Google OAuth login process (requires manual interaction)
# 4. Authentication progress indicators work correctly
# 5. Successful authentication state changes

# Step 1: Launch app and navigate to Account tab
- runFlow: ../common/app_launch.yaml
- extendedWaitUntil:
    visible: "Account\nTab 4 of 5"
    timeout: 10000
- tapOn: "Account\nTab 4 of 5"
- waitForAnimationToEnd:
    timeout: 3000
- assertVisible: "Account\nTab 4 of 5"

# Step 2: Verify authentication providers are displayed
- assertVisible: "Sign in with your account"
- assertVisible: "Choose one of the following sign-in methods:"
- assertVisible: "Continue with Google"
- assertVisible: "Continue with Apple"
- takeScreenshot: auth_providers_displayed

# Step 3: Initiate Google OAuth login
# Note: This will open the Google OAuth flow which requires manual interaction
- tapOn: "Continue with Google"

# Step 4: Wait for authentication progress indicators
# The app should show "Authenticating..." or similar progress message
- extendedWaitUntil:
    visible: "Authenticating..."
    timeout: 15000
- assertVisible: "Authenticating..."
- takeScreenshot: authentication_in_progress

# Step 5: Wait for OAuth flow completion
# Note: Manual interaction required here - user must complete Google OAuth
# The test will wait for authentication completion indicators
- extendedWaitUntil:
    visible: "Welcome!"
    timeout: 120000  # 2 minutes for manual OAuth completion

# Step 6: Verify authentication success indicators
- assertVisible: "Welcome!"
- takeScreenshot: authentication_success

# Step 7: Wait for profile loading to complete
# The app should load user profile information after successful authentication
- extendedWaitUntil:
    visible:
      text: "@"
    timeout: 30000

# Step 8: Verify user profile information is displayed
# Look for email indicator (@ symbol) which indicates profile is loaded
- assertVisible: "@"
- takeScreenshot: profile_loaded

# Step 9: Verify top navigation shows authenticated state
# The account icon should no longer show the error indicator
- runFlow: ../common/auth_state_assertions.yaml  # Verify authenticated state

# Step 10: Take final screenshot of successful login
- takeScreenshot: login_flow_complete

# Test completed successfully - user is now authenticated
