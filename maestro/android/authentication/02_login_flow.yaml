appId: ai.promz
---
# Test Case: Login Flow Verification
#
# Objective: Test the complete authentication flow including Google OAuth
#
# This test verifies:
# 1. Navigation to Account tab
# 2. Authentication providers are displayed correctly
# 3. Google OAuth login process (requires manual interaction)
# 4. Authentication progress indicators work correctly
# 5. Successful authentication state changes

# Step 1: Launch app and navigate to Account tab
- runFlow: ../common/app_launch.yaml
- extendedWaitUntil:
    visible: "Account\nTab 4 of 5"
    timeout: 10000
- tapOn: "Account\nTab 4 of 5"
- waitForAnimationToEnd:
    timeout: 3000
- assertVisible: "Account\nTab 4 of 5"

# Step 2: Verify authentication providers are displayed
# Wait for authentication UI to load first
- extendedWaitUntil:
    visible: "Continue with Google"
    timeout: 10000

# Verify authentication providers are present (Android-specific)
- assertVisible: "Continue with Google"
- assertVisible: "Continue with Microsoft"  # Microsoft on Android, not Apple

# Try to verify authentication header text (may be optional due to UI variations)
- extendedWaitUntil:
    visible: "Sign in with your account"
    timeout: 5000
    optional: true

# Verify additional authentication UI elements if visible
- extendedWaitUntil:
    visible: "Choose one of the following sign-in methods:"
    timeout: 5000
    optional: true

- takeScreenshot: auth_providers_displayed

# Step 3: Initiate Google OAuth login
# Note: This will open the Google OAuth flow which requires manual interaction
- tapOn: "Continue with Google"

# Step 4: Wait for OAuth flow completion
# Note: Manual interaction required - user must complete Google OAuth in external browser/app
# After OAuth completion, the app should return to an authenticated state

# Take screenshot after OAuth initiation
- takeScreenshot: oauth_flow_initiated

# Wait for OAuth flow to complete and return to app
# Look for the absence of authentication providers as success indicator
- extendedWaitUntil:
    notVisible: "Continue with Google"
    timeout: 120000  # 2 minutes for manual OAuth completion

# Step 5: Verify authentication success by checking auth providers are gone
# The most reliable indicator is that auth providers are no longer visible
- assertNotVisible: "Continue with Google"
- assertNotVisible: "Continue with Microsoft"

# Step 6: Take screenshot of successful authentication
- takeScreenshot: authentication_success

# Step 7: Take final screenshot of successful login
- takeScreenshot: login_flow_complete

# Test completed successfully - user is now authenticated
