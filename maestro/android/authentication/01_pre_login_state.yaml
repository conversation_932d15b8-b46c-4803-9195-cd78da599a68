appId: ai.promz
---
# Test Case: Pre-Login State Verification (Refactored)
# 
# Objective: Verify that the app displays correct UI elements when user is not authenticated
# 
# This test demonstrates the new modular approach using reusable components
# 
# This test verifies:
# 1. App launches successfully
# 2. Top navigation shows unauthenticated state (account icon with error indicator)  
# 3. Bottom navigation is accessible
# 4. Authentication prompts appear when accessing restricted features
# 5. All navigation tabs are accessible but may show authentication prompts

# Step 1: Launch the app and verify initial state
- runFlow: ../common/app_launch.yaml

# Step 2: Navigate to Account tab using the full accessibility text
- tapOn: "Account\nTab 4 of 5"
- waitForAnimationToEnd:
    timeout: 3000
- assertVisible: "Account\nTab 4 of 5"

# Step 3: Verify authentication UI is present
- runFlow: ../common/auth_state_assertions.yaml

# Step 4: Test navigation to other tabs to verify accessibility
# Navigate back to Home tab
- tapOn: "Home\nTab 1 of 5"
- waitForAnimationToEnd:
    timeout: 3000
- assertVisible: "What do you want to achieve today?"
- takeScreenshot: home_tab_unauthenticated

# Navigate to Discover tab
- tapOn: "Discover\nTab 2 of 5"
- waitForAnimationToEnd:
    timeout: 3000
- assertVisible: "Discover\nTab 2 of 5"
- takeScreenshot: discover_tab_unauthenticated

# Navigate to Portfolio tab
- tapOn: "Portfolio\nTab 3 of 5"
- waitForAnimationToEnd:
    timeout: 3000
- assertVisible: "Portfolio\nTab 3 of 5"
- takeScreenshot: portfolio_tab_unauthenticated

# Navigate to About tab
- tapOn: "About\nTab 5 of 5"
- waitForAnimationToEnd:
    timeout: 3000
- assertVisible: "About\nTab 5 of 5"
- takeScreenshot: about_tab_unauthenticated

# Step 5: Return to Account tab for final verification
- tapOn: "Account\nTab 4 of 5"
- waitForAnimationToEnd:
    timeout: 3000
- assertVisible: "Account\nTab 4 of 5"

# Step 6: Take final screenshot of pre-login state
- takeScreenshot: pre_login_complete

# Test completed successfully - app is in expected unauthenticated state
