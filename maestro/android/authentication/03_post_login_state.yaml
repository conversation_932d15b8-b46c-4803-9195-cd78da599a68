appId: ai.promz
---
# Test Case: Post-Login State Verification
# 
# Objective: Verify that the app displays correct UI elements after successful authentication
# 
# This test verifies:
# 1. Top navigation shows authenticated state (user avatar/profile)
# 2. User profile information is correctly displayed
# 3. Authentication success messages are shown
# 4. License information is displayed if available
# 5. Account settings are accessible

# Prerequisites: User must be authenticated (run 02_login_flow.yaml first)

# Step 1: Launch app (should maintain authenticated state)
- runFlow: ../common/app_launch.yaml

# Step 2: Verify authenticated state in top navigation
# The top bar should show user profile instead of sign-in prompt
- assertNotVisible: "Sign in"

# Step 3: Navigate to Account tab to verify profile information
- tapOn: "Account"
- waitForAnimationToEnd:
    timeout: 3000
- assertVisible: "Account\nTab 4 of 5"

# Step 4: Verify user profile information is displayed
- extendedWaitUntil:
    visible: "@"  # Email indicator
    timeout: 10000
- assertVisible: "@"
- takeScreenshot: authenticated_account_view

# Step 5: Check for license information display
# The app should show license status (Free, Trial, or Pro)
- assertVisible: "License"

# Step 6: Verify sign-out option is available
- assertVisible: "Sign Out"
- takeScreenshot: sign_out_available

# Step 7: Check that authentication providers are no longer prominently displayed
# Since user is authenticated, the sign-in options should not be the main focus
- assertNotVisible: "Choose one of the following sign-in methods:"

# Step 8: Verify profile avatar/badge is shown in top navigation
# Navigate to different tabs to verify consistent authenticated state
- tapOn: "Home"
- waitForAnimationToEnd:
    timeout: 3000
- takeScreenshot: home_authenticated_state

# Step 9: Verify Portfolio access (should be available when authenticated)
- tapOn: "Portfolio"
- waitForAnimationToEnd:
    timeout: 3000
- assertVisible: "Portfolio\nTab 3 of 5"
- takeScreenshot: portfolio_authenticated_access

# Step 10: Verify Discover tab maintains authenticated state
- tapOn: "Discover"
- waitForAnimationToEnd:
    timeout: 3000
- assertVisible: "Discover\nTab 2 of 5"
- takeScreenshot: discover_authenticated_state

# Step 11: Return to Account tab for final verification
- tapOn: "Account"
- waitForAnimationToEnd:
    timeout: 3000

# Step 12: Verify user can access account settings/profile management
# Look for profile management options that should be available when authenticated
- assertVisible: "Account\nTab 4 of 5"

# Step 13: Take final screenshot of post-login state
- takeScreenshot: post_login_complete

# Test completed successfully - app is in expected authenticated state
