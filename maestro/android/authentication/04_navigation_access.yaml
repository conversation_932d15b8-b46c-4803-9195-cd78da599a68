appId: ai.promz
---
# Test Case: Navigation Access Verification
# 
# Objective: Test that authenticated users can access all navigation tabs and features
# 
# This test verifies:
# 1. All navigation tabs are accessible to authenticated users
# 2. Previously restricted features are now available
# 3. Navigation between sections works correctly
# 4. Content loads properly in each section
# 5. No authentication prompts appear for accessible features

# Prerequisites: User must be authenticated (run 02_login_flow.yaml first)

# Step 1: Launch app and verify authenticated state
- runFlow: ../common/app_launch.yaml
- assertNotVisible:
    text: "Sign in"
    timeout: 5000

# Step 2: Test Home tab access and functionality
- tapOn: "Home"
- waitForAnimationToEnd:
    timeout: 3000
    timeout: 3000
- assertVisible: "What do you want to achieve today?"
- assertVisible: "Home\nTab 1 of 5"
- takeScreenshot: home_tab_authenticated

# Test source input functionality (should be available when authenticated)
- assertVisible: "What do you want to achieve today?"

# Step 3: Test Discover tab access
- tapOn: "Discover"
- waitForAnimationToEnd:
    timeout: 3000
    timeout: 3000
- assertVisible: "Discover\nTab 2 of 5"
- takeScreenshot: discover_tab_authenticated

# Verify discover content loads (categories, prompts, etc.)
- extendedWaitUntil:
    visible:
      text: "Category|Prompt|Browse|Search"
    timeout: 15000

# Step 4: Test Portfolio tab access (should be fully accessible when authenticated)
- tapOn: "Portfolio"
- waitForAnimationToEnd:
    timeout: 3000
    timeout: 3000
- assertVisible: "Portfolio\nTab 3 of 5"
- takeScreenshot: portfolio_tab_authenticated

# Verify portfolio functionality is available
- extendedWaitUntil:
    visible:
      text: "Topic|Create|Saved|Collection"
    timeout: 15000

# Step 5: Test Account tab access and settings
- tapOn: "Account"
- waitForAnimationToEnd:
    timeout: 3000
    timeout: 3000
- assertVisible: "Account\nTab 4 of 5"
- assertVisible: "@"  # User email indicator
- takeScreenshot: account_tab_authenticated

# Step 6: Test About/Help tab access
- tapOn: "About"
- waitForAnimationToEnd:
    timeout: 3000
    timeout: 3000
- assertVisible: "About\nTab 5 of 5"
- takeScreenshot: about_tab_authenticated

# Step 7: Test navigation flow between multiple tabs
# Verify smooth navigation without authentication interruptions
- tapOn: "Home"
- waitForAnimationToEnd:
    timeout: 3000
    timeout: 2000
- tapOn: "Portfolio"
- waitForAnimationToEnd:
    timeout: 3000
    timeout: 2000
- tapOn: "Discover"
- waitForAnimationToEnd:
    timeout: 3000
    timeout: 2000
- tapOn: "Account"
- waitForAnimationToEnd:
    timeout: 3000
    timeout: 2000

# Step 8: Verify no authentication dialogs appear during navigation
# Should not see any "Sign In" dialogs or authentication prompts
- assertNotVisible: "Authentication required"
- assertNotVisible: "Please sign in"
- assertNotVisible: "Sign In"  # As a dialog button

# Step 9: Test feature access that might require authentication
# Navigate back to Home and test advanced features
- tapOn: "Home"
- waitForAnimationToEnd:
    timeout: 3000
    timeout: 3000

# Try to access features that might be restricted to authenticated users
# This could include prompt execution, content processing, etc.
- assertVisible: "What do you want to achieve today?"

# Step 10: Verify consistent authenticated state across all tabs
# Check that user profile/avatar is consistently shown in top navigation
- tapOn: "Discover"
- waitForAnimationToEnd:
    timeout: 3000
    timeout: 2000
- assertNotVisible: "Sign in"  # Should not see sign-in tooltip

- tapOn: "Portfolio"
- waitForAnimationToEnd:
    timeout: 3000
    timeout: 2000
- assertNotVisible: "Sign in"

- tapOn: "Account"
- waitForAnimationToEnd:
    timeout: 3000
    timeout: 2000
- assertVisible: "@"  # User email indicator

# Step 11: Take final screenshot showing full navigation access
- takeScreenshot: navigation_access_complete

# Test completed successfully - authenticated user has full navigation access
