appId: ai.promz
---
# Common App Launch Flow
# This flow handles launching the Promz app and waiting for initial load
# Can be used as a nested flow in other tests

- launchApp
- assertVisible: "Promz: AI Made Easy"  # App bar title from strings
- waitForAnimationToEnd:
    timeout: 5000
    
# Wait for the home page to fully load
# The home page should show the source input section
- extendedWaitUntil:
    visible: "What do you want to achieve today?"
    timeout: 10000
- assertVisible: "What do you want to achieve today?"

# Verify bottom navigation is present
# Use modular navigation assertions for consistency
- runFlow: navigation_assertions.yaml

# Take a screenshot for debugging purposes
- takeScreenshot: app_launched
