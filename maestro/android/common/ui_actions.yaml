appId: ai.promz
---
# Common UI Actions Library
# Reusable UI interaction patterns
# 
# Usage: runFlow: ../common/ui_actions.yaml
# Each flow section can be called independently

# Standard animation wait (3 seconds)
# Most common wait time for UI transitions
- waitForAnimationToEnd:
    timeout: 3000

---
# Extended animation wait (5 seconds)
# For slower transitions or complex animations
- waitForAnimationToEnd:
    timeout: 5000

---
# Quick animation wait (1 second)
# For fast transitions
- waitForAnimationToEnd:
    timeout: 1000

---
# Navigate to Account tab (basic)
# Just navigation without verification
- tapOn: "Account"
- waitForAnimationToEnd:
    timeout: 3000

---
# Navigate to Home tab (basic)
# Just navigation without verification
- tapOn: "Home"
- waitForAnimationToEnd:
    timeout: 3000

---
# Navigate to Discover tab (basic)
# Just navigation without verification
- tapOn: "Discover"
- waitForAnimationToEnd:
    timeout: 3000

---
# Navigate to Portfolio tab (basic)
# Just navigation without verification
- tapOn: "Portfolio"
- waitForAnimationToEnd:
    timeout: 3000

---
# Navigate to About tab (basic)
# Just navigation without verification
- tapOn: "About"
- waitForAnimationToEnd:
    timeout: 3000

---
# Take screenshot with standard naming pattern
# Note: Actual screenshot names should be specified in calling test
- takeScreenshot: test_screenshot

---
# Initiate Google OAuth login
# Common pattern for starting authentication
- tapOn: "Continue with Google"

---
# Initiate Apple OAuth login
# Common pattern for starting authentication
- tapOn: "Continue with Apple"

---
# Confirm logout action
# Handle logout confirmation dialog
- tapOn: "Sign Out"

---
# Wait for app to fully load
# Extended wait for initial app loading
- waitForAnimationToEnd:
    timeout: 5000
