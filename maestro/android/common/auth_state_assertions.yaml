appId: ai.promz
---
# Authentication State Assertions Library
# Reusable authentication state verification components
# 
# Usage: runFlow: ../common/auth_state_assertions.yaml
# Each flow section can be called independently

# Verify unauthenticated state by checking authentication providers in Account tab
# Note: Authentication UI text may be combined into accessibility elements
# This flow should be called after navigating to the Account tab

# Wait for authentication UI to load - try both individual and combined text
- extendedWaitUntil:
    visible: "Continue with Google"
    timeout: 10000

# Verify authentication providers are present
- assertVisible: "Continue with Google"
- assertVisible: "Continue with Microsoft"

---
# Verify authenticated state by checking that auth providers are NOT visible
# This flow should be called after navigating to the Account tab
- assertNotVisible: "Sign in with your account"
- assertNotVisible: "Choose one of the following sign-in methods:"

---
# Verify user profile is loaded (authenticated state)
# Look for email indicator which shows profile is loaded
- extendedWaitUntil:
    visible: "@"
    timeout: 10000
- assertVisible: "@"

---
# Verify user profile is NOT loaded (unauthenticated state)
- assertNotVisible: "@"

---
# Verify authentication providers are visible
# This appears on the Account tab when unauthenticated
- assertVisible: "Sign in with your account"
- assertVisible: "Choose one of the following sign-in methods:"
- assertVisible: "Continue with Google"
- assertVisible: "Continue with Microsoft"  # Microsoft on Android, Apple on iOS
- assertVisible: "Your account will be automatically linked to your license."

---
# Verify authentication providers are NOT visible
# This should be the case when user is authenticated
- assertNotVisible: "Sign in with your account"
- assertNotVisible: "Choose one of the following sign-in methods:"

---
# Verify authentication progress indicators
# Used during login flow
- extendedWaitUntil:
    visible: "Authenticating..."
    timeout: 30000
- assertVisible: "Authenticating..."

---
# Verify authentication success indicators
# Used after successful login
- extendedWaitUntil:
    visible: "Welcome!"
    timeout: 10000
- assertVisible: "Welcome!"

---
# Verify authentication failure indicators
# Used when login fails
- extendedWaitUntil:
    visible: "Authentication failed"
    timeout: 10000
- assertVisible: "Authentication failed"

---
# Verify logout button is visible (authenticated state)
- assertVisible: "Sign Out"

---
# Verify logout button is NOT visible (unauthenticated state)
- assertNotVisible: "Sign Out"

---
# Verify account linking message
# Shown during authentication flow
- assertVisible: "Your account will be automatically linked to your license."
