appId: ai.promz
---
# Navigation Bar Assertions Library
# Reusable navigation bar verification components
# 
# Usage: runFlow: ../common/navigation_assertions.yaml
# Each flow section can be called independently

# Verify all navigation tabs are visible
# This is the most commonly used navigation verification
- extendedWaitUntil:
    visible: "Home\nTab 1 of 5"
    timeout: 10000
- assertVisible: "Home\nTab 1 of 5"
- assertVisible: "Discover\nTab 2 of 5"
- assertVisible: "Portfolio\nTab 3 of 5"
- assertVisible: "Account\nTab 4 of 5"
- assertVisible: "About\nTab 5 of 5"

---
# Verify Home tab is selected and visible
- assertVisible: "Home\nTab 1 of 5"
- assertVisible: "What do you want to achieve today?"

---
# Verify Discover tab is selected and visible
- assertVisible: "Discover\nTab 2 of 5"

---
# Verify Portfolio tab is selected and visible
- assertVisible: "Portfolio\nTab 3 of 5"

---
# Verify Account tab is selected and visible
- assertVisible: "Account\nTab 4 of 5"

---
# Verify About tab is selected and visible
- assertVisible: "About\nTab 5 of 5"

---
# Navigate to Home tab with verification
- tapOn: "Home"
- waitForAnimationToEnd:
    timeout: 3000
- assertVisible: "What do you want to achieve today?"

---
# Navigate to Discover tab with verification
- tapOn: "Discover"
- waitForAnimationToEnd:
    timeout: 3000
- assertVisible: "Discover\nTab 2 of 5"

---
# Navigate to Portfolio tab with verification
- tapOn: "Portfolio"
- waitForAnimationToEnd:
    timeout: 3000
- assertVisible: "Portfolio\nTab 3 of 5"

---
# Navigate to Account tab with verification
- tapOn: "Account"
- waitForAnimationToEnd:
    timeout: 3000
- assertVisible: "Account\nTab 4 of 5"

---
# Navigate to About tab with verification
- tapOn: "About"
- waitForAnimationToEnd:
    timeout: 3000
- assertVisible: "About\nTab 5 of 5"
