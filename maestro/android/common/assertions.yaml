appId: ai.promz
---
# Common Assertion Flows
# Reusable assertions for consistent state verification

# Assert unauthenticated state in top navigation
# The top bar should show an account icon with error indicator
- extendedWaitUntil:
    visible:
      id: "account_circle_outlined"
    timeout: 5000
- assertVisible:
    id: "account_circle_outlined"
- assertVisible: "Sign in"  # Tooltip text for unauthenticated state

---
# Assert authenticated state in top navigation
# The top bar should show user profile information
- assertNotVisible:
    id: "account_circle_outlined"
# User avatar or profile information should be visible
# Look for email indicator
- assertVisible: "@"  # Email indicator

---
# Assert authentication providers are visible
- assertVisible: "Sign in with your account"
- assertVisible: "Choose one of the following sign-in methods:"
- assertVisible: "Continue with Google"

# Check for platform-specific auth providers
# Apple Sign-In is available on iOS and macOS
- assertVisible: "Continue with Apple"

---
# Assert authentication progress indicators
- extendedWaitUntil:
    visible: "Authenticating..."
    timeout: 30000
- assertVisible: "Authenticating..."

---
# Assert authentication success
- extendedWaitUntil:
    visible: "Welcome!"
    timeout: 10000
- assertVisible: "Welcome!"

---
# Assert authentication failure
- extendedWaitUntil:
    visible: "Authentication failed"
    timeout: 10000
- assertVisible: "Authentication failed"
