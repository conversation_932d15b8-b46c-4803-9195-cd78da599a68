appId: ai.promz
---
# Debug Navigation Test
# This test helps debug what navigation elements are actually visible

# Step 1: Launch the app
- launchApp
- assertVisible: "Promz: AI Made Easy"
- waitForAnimationToEnd:
    timeout: 5000

# Step 2: Wait for home page to load
- extendedWaitUntil:
    visible: "What do you want to achieve today?"
    timeout: 10000
- assertVisible: "What do you want to achieve today?"

# Step 3: Take screenshot to see current state
- takeScreenshot: debug_home_state

# Step 4: Try to find navigation elements with different approaches
# Try the full tab format first
- extendedWaitUntil:
    visible: "Account\nTab 4 of 5"
    timeout: 5000
    optional: true

# Try just the label
- extendedWaitUntil:
    visible: "Account"
    timeout: 5000
    optional: true

# Try the icon approach (if text is not visible)
- extendedWaitUntil:
    visible:
      id: "person_outline"
    timeout: 5000
    optional: true

# Step 5: Take final screenshot
- takeScreenshot: debug_navigation_elements
