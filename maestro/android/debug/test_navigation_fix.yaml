appId: ai.promz
---
# Test Navigation Fix
# This test verifies that the navigation fix works correctly

# Step 1: Launch the app
- launchApp
- assertVisible: "Promz: AI Made Easy"
- waitForAnimationToEnd:
    timeout: 5000

# Step 2: Wait for home page to load
- extendedWaitUntil:
    visible: "What do you want to achieve today?"
    timeout: 10000
- assertVisible: "What do you want to achieve today?"

# Step 3: Verify all navigation tabs are visible (this should work)
- assertVisible: "Home\nTab 1 of 5"
- assertVisible: "Discover\nTab 2 of 5"
- assertVisible: "Portfolio\nTab 3 of 5"
- assertVisible: "Account\nTab 4 of 5"
- assertVisible: "About\nTab 5 of 5"

# Step 4: Test tapping with full accessibility text (this should work now)
- tapOn: "Account\nTab 4 of 5"
- waitForAnimationToEnd:
    timeout: 3000
- assertVisible: "Account\nTab 4 of 5"
- takeScreenshot: account_tab_success

# Step 5: Navigate back to Home
- tapOn: "Home\nTab 1 of 5"
- waitForAnimationToEnd:
    timeout: 3000
- assertVisible: "What do you want to achieve today?"
- takeScreenshot: home_tab_success

# Test completed successfully
