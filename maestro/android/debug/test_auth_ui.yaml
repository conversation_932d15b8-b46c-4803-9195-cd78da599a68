appId: ai.promz
---
# Test Authentication UI on Android
# This test verifies what authentication elements are actually visible

# Step 1: Launch the app
- launchApp
- assertVisible: "Promz: AI Made Easy"
- waitForAnimationToEnd:
    timeout: 5000

# Step 2: Wait for home page to load
- extendedWaitUntil:
    visible: "What do you want to achieve today?"
    timeout: 10000
- assertVisible: "What do you want to achieve today?"

# Step 3: Navigate to Account tab
- tapOn: "Account\nTab 4 of 5"
- waitForAnimationToEnd:
    timeout: 3000
- assertVisible: "Account\nTab 4 of 5"
- takeScreenshot: account_tab_loaded

# Step 4: Wait for authentication UI to load
- extendedWaitUntil:
    visible: "Continue with Google"
    timeout: 10000

# Step 5: Test individual authentication elements
- assertVisible: "Continue with Google"
- takeScreenshot: google_button_visible

# Step 6: Test Microsoft provider (Android-specific)
- assertVisible: "Continue with Microsoft"
- takeScreenshot: microsoft_button_visible

# Step 7: Try to find the authentication header text
- extendedWaitUntil:
    visible: "Sign in with your account"
    timeout: 5000
    optional: true

# Step 8: Take final screenshot
- takeScreenshot: auth_ui_complete
