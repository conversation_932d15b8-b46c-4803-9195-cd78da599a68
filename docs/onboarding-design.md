# Promz Onboarding Experience Design

## 1. Introduction

This document outlines the design for the Promz app's out-of-the-box experience, focusing on
creating an engaging and informative onboarding flow for new users. The current experience takes
users directly to the homepage without context or guidance, missing an opportunity to showcase
the app's value proposition and unique features.

## 2. Goals and Objectives

### 2.1 Primary Goals

- Create a compelling first impression that clearly communicates Promz's value proposition
- Educate users about key features and benefits before they start using the app
- Guide users toward account creation while providing a "skip" option
- Maintain platform-specific design principles while ensuring a consistent experience
- Increase user retention by setting clear expectations about app functionality

### 2.2 Success Metrics

- Increased user sign-up rate
- Reduced early abandonment rate
- Higher engagement with core features
- Improved user understanding of app capabilities (measured through surveys)

## 3. User Flow

### 3.1 Entry Points

- **Fresh Install**: When a user opens the app for the first time
- **Account View**: A "Begin Tour" button in the Account view allows users to access the onboarding flow at any time
- **Skip & Return**: Users who skip can access the tour later from the Account view

### 3.2 Flow Sequence

1. **App Launch** → **Onboarding Screens** → **Account View** → **Home Screen**
2. Alternative path: **App Launch** → **Onboarding Screens** → **Skip** → **Home Screen**

## 4. Design Specifications

### 4.1 Overall Design Principles

- **Platform-Native Feel**: Follow iOS design guidelines primarily, with adaptations for Android
- **Consistency**: Use existing UI components and styling from the app
- **Accessibility**: Ensure all screens are accessible with proper contrast and text sizing
- **Responsiveness**: Design should adapt to different screen sizes and orientations
- **Future-Proofing**: Consider extensibility for macOS, Windows, and web platforms

### 4.2 Onboarding Screen Structure

Each onboarding screen will follow a consistent layout:

- **Header**: App logo and progress indicator
- **Illustration**: Visual representation of the feature/benefit
- **Title**: Concise, benefit-oriented heading
- **Description**: Brief explanation of the feature/benefit
- **Navigation**: "Next" button and "Skip" option

### 4.3 Screen Transitions

- Smooth horizontal slide transitions between screens
- Fade transition to the Account View or Home Screen

## 5. Screen Content

### 5.1 Screen 1: Smarter AI, Real Results

- **Title**: "Smarter AI, Real Results"
- **Description**: "Promz connects you with expert-crafted prompts tailored for real-world use — no fluff, no guesswork. Get things done faster, better."
- **Illustration**: Image showing a person getting more from AI (get-more-from-ai.png)
- **Action**: "Next" button and "Skip" text link

### 5.2 Screen 2: Discover, Share, Sync

- **Title**: "Discover. Share. Sync."
- **Description**: "Explore community-powered prompts, save your best work into topics, and stay in sync across devices. Your AI portfolio, your way — even offline."
- **Illustration**: Image showing sharing and syncing capabilities (sharing.png)
- **Action**: "Get Started" button and "Skip" text link

## 6. Technical Implementation

### 6.1 Component Structure

- Create a new `OnboardingView` widget that uses a `PageView` for horizontal swiping
- Implement a reusable `OnboardingPage` widget for consistent layout across screens
- Store onboarding completion status in `SharedPreferences`
- Add a "Begin Tour" button in the Account view to allow users to access the onboarding flow at any time

### 6.2 Navigation Logic

```dart
// Pseudocode for navigation logic
void checkAndShowOnboarding() {
  final hasSeenOnboarding = prefs.getBool('has_seen_onboarding') ?? false;

  if (!hasSeenOnboarding) {
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(builder: (context) => OnboardingView()),
    );
  } else {
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(builder: (context) => HomePage()),
    );
  }
}
```

### 6.3 Platform-Specific Considerations

- Use platform-specific widgets (Cupertino vs Material) based on the current platform
- Adapt button styles, transitions, and typography to match platform conventions
- Leverage existing platform-aware widgets like `PromzButton` and `PromzProgressIndicator`

## 7. Implementation Plan

### 7.1 Phase 1: Core Onboarding Flow

1. Create the `OnboardingView` and `OnboardingPage` widgets
2. Implement the two main content screens
3. Add navigation logic and persistence of onboarding status
4. Connect to the Account View for sign-up/sign-in
5. Add a "Begin Tour" button in the Account view

### 7.2 Phase 2: Refinements

1. Add animations and transitions
2. Implement analytics to track onboarding completion rates
3. A/B test different content variations
4. Add localization support

### 7.3 Phase 3: Extended Platforms

1. Adapt layout for tablet and desktop form factors
2. Implement platform-specific adjustments for macOS and Windows
3. Create a web-optimized version of the onboarding flow

## 8. Accessibility Considerations

- Support dynamic text sizes
- Ensure proper contrast ratios
- Add semantic labels for screen readers
- Support keyboard navigation
- Test with accessibility tools on both iOS and Android

## 9. Future Enhancements

- Personalized onboarding based on user interests
- Interactive demos of key features
- Video tutorials embedded in onboarding
- Contextual help throughout the app that references onboarding concepts

## 10. Conclusion

The proposed onboarding experience will significantly improve the first-time user experience for
Promz by clearly communicating the app's value proposition and guiding users toward account
creation. By following platform-specific design guidelines while maintaining a consistent
experience, we can create an engaging introduction that sets users up for success with the app.
