# Deploying the Promz API Server to Google Cloud Run

This document provides instructions for deploying the Promz API server to Google Cloud Run using Docker and Google Cloud Build.

## Prerequisites

Before you begin, make sure you have the following:

1. **Google Cloud Account**: You need a Google Cloud account with billing enabled.

2. **Google Cloud SDK**: Install the [Google Cloud SDK](https://cloud.google.com/sdk/docs/install) which includes:

   - `gcloud` command-line tool
   - `gsutil` for Google Cloud Storage
   - `bq` for BigQuery

3. **Docker**: Install [Docker Desktop](https://www.docker.com/products/docker-desktop/) for building and testing containers locally.

4. **Git**: Make sure you have Git installed to clone and manage your repository.

5. **Enable Required APIs**: In your Google Cloud project, enable the following APIs:
   - Cloud Build API
   - Cloud Run API
   - Container Registry API or Artifact Registry API

## Project Structure

The deployment configuration consists of three key files:

1. **Dockerfile**: Defines how to build the Go application container.
2. **.dockerignore**: Specifies which files to exclude from the Docker build context.
3. **cloudbuild.yaml**: Configures the Google Cloud Build pipeline for CI/CD.

## Local Testing

Before deploying to Google Cloud Run, test your Docker container locally:

```bash
# Navigate to the API directory
cd /path/to/promz/api

# Build the Docker image
docker build -t promz-api:local .

# Run the container locally
docker run -p 8080:8080 promz-api:local
```

Verify that your API is working by accessing it at http://localhost:8080

## Deployment to Google Cloud Run

### Option 1: Manual Deployment

You can manually deploy your application using the Google Cloud SDK:

```bash
# Set your Google Cloud project ID
gcloud config set project YOUR_PROJECT_ID

# Build and push the container image to Google Container Registry
gcloud builds submit --tag gcr.io/YOUR_PROJECT_ID/promz-api

# Deploy to Cloud Run
gcloud run deploy promz-api \
  --image gcr.io/YOUR_PROJECT_ID/promz-api \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated
```

### Option 2: Automated Deployment with Cloud Build

For automated CI/CD, use the provided `cloudbuild.yaml` file:

1. **Connect your repository to Cloud Build**:

   - Go to Cloud Build in the Google Cloud Console
   - Connect your GitHub or Bitbucket repository
   - Configure the build trigger to use the `cloudbuild.yaml` file

2. **Trigger a build**:
   - Builds will automatically trigger on commits to your configured branch
   - You can also manually trigger builds from the Cloud Build console

The `cloudbuild.yaml` file includes steps to:

- Build the Docker image
- Push it to Container Registry
- Deploy it to Cloud Run

## Environment Variables

For security, sensitive configuration should be set as environment variables in Cloud Run:

1. **In the Google Cloud Console**:

   - Go to Cloud Run > Your Service > Edit & Deploy New Revision
   - Under "Container, Variables & Secrets", add your environment variables

2. **Using gcloud**:

   ```bash
   gcloud run services update promz-api \
     --update-env-vars="KEY1=VALUE1,KEY2=VALUE2"
   ```

Common environment variables for the Promz API include:

- Database connection strings
- API keys for external services
- Authentication configuration

## Monitoring and Troubleshooting

After deployment, monitor your application using:

1. **Cloud Run Dashboard**: View request logs, error rates, and latency metrics
2. **Cloud Logging**: Access detailed application logs
3. **Cloud Monitoring**: Set up alerts and dashboards

To view logs:

```bash
gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=promz-api"
```

## Security Considerations

1. **Authentication**: By default, the deployment allows unauthenticated access. For production, consider adding authentication:

   ```bash
   gcloud run services update promz-api --no-allow-unauthenticated
   ```

2. **Secrets**: Use Secret Manager for sensitive information rather than environment variables for production deployments.

3. **VPC Connector**: For accessing private resources, configure a VPC connector for your Cloud Run service.

## Cost Optimization

Cloud Run charges based on:

- Number of requests
- Time your container instances spend processing requests
- Memory allocated to your container instances

To optimize costs:

- Configure appropriate memory limits
- Implement efficient request handling
- Set concurrency appropriately for your workload

## Additional Resources

- [Cloud Run Documentation](https://cloud.google.com/run/docs)
- [Cloud Build Documentation](https://cloud.google.com/build/docs)
- [Docker Documentation](https://docs.docker.com/)
- [Go on Google Cloud](https://cloud.google.com/go)
