# Promz Architecture Diagram

This document provides a visual representation of the Promz platform architecture, showing how the different components interact with each other.

## System Overview

```mermaid
flowchart TD
    %% Main Components
    Client[Flutter Client App]
    Server[Go API Server]
    Admin[Flutter Web Admin]
    
    %% Databases
    ClientDB[(SQLite DB)]
    ServerDB[(Supabase PostgreSQL)]
    
    %% External Services
    OpenAI[OpenAI API]
    GoogleAI[Google AI API]
    
    %% Client Internal Components
    CCS[ClientContextService]
    VariableManager[Variable Manager]
    EntityDetection[Entity Detection]
    PromptSuggestion[Prompt Suggestion]
    
    %% Server Internal Components
    Router[API Router]
    TemplateProcessor[Template Processor]
    PopularityService[Popularity Service]
    LLMExecutor[LLM Executor]
    
    %% Connections
    Client --> |API Calls| Server
    Admin --> |API Calls| Server
    
    %% Database Connections
    Client --> |Local Storage| ClientDB
    Server --> |Cloud Storage| ServerDB
    
    %% External Service Connections
    Server --> |Execute Prompts| OpenAI
    Server --> |Execute Prompts| GoogleAI
    
    %% Client Internal Connections
    Client --> CCS
    CCS --> VariableManager
    CCS --> EntityDetection
    CCS --> PromptSuggestion
    
    %% Server Internal Connections
    Server --> Router
    Router --> TemplateProcessor
    Router --> PopularityService
    Router --> LLMExecutor
    LLMExecutor --> OpenAI
    LLMExecutor --> GoogleAI
    
    %% Sync Process
    Client <--> |Sync| Server
    
    %% Styling
    classDef clientComponents fill:#d4f1f9,stroke:#05a0c8,stroke-width:2px
    classDef serverComponents fill:#ffe6cc,stroke:#f7931e,stroke-width:2px
    classDef databases fill:#e1d5e7,stroke:#9673a6,stroke-width:2px
    classDef externalServices fill:#d5e8d4,stroke:#82b366,stroke-width:2px
    
    class Client,CCS,VariableManager,EntityDetection,PromptSuggestion,Admin clientComponents
    class Server,Router,TemplateProcessor,PopularityService,LLMExecutor serverComponents
    class ClientDB,ServerDB databases
    class OpenAI,GoogleAI externalServices
```

## Detailed Component Diagram

```mermaid
flowchart TD
    %% Client Architecture
    subgraph Client [Flutter Client]
        subgraph CCS [ClientContextService]
            VariableManager[Variable Manager]
            EntityDetection[Entity Detection]
            PromptSuggestion[Prompt Suggestion]
            SyncService[Sync Service]
        end
        
        subgraph ViewModels [View Models]
            HomeVM[Home ViewModel]
            PromptListVM[Prompt List ViewModel]
            VariablesVM[Variables ViewModel]
        end
        
        subgraph Views [Views]
            HomePage[Home Page]
            PromptListView[Prompt List View]
            VariablesDialog[Variables Dialog]
        end
        
        subgraph LocalDB [Local Storage]
            SQLite[(SQLite)]
            SecureStorage[(Secure Storage)]
        end
        
        %% Client Connections
        Views --> ViewModels
        ViewModels --> CCS
        CCS --> LocalDB
    end
    
    %% Server Architecture
    subgraph Server [Go API Server]
        subgraph Handlers [API Handlers]
            PromptHandler[Prompt Handler]
            UsageHandler[Usage Handler]
            AuthHandler[Auth Handler]
            LLMHandler[LLM Handler]
        end
        
        subgraph Services [Services]
            TemplateProcessor[Template Processor]
            PopularityService[Popularity Service]
            InstructionsService[Instructions Service]
        end
        
        subgraph LLMExecutor [LLM Executor]
            ProviderManager[Provider Manager]
            TemplateEngine[Template Engine]
            ResponseProcessor[Response Processor]
        end
        
        subgraph Repository [Data Repository]
            SupabaseClient[Supabase Client]
        end
        
        %% Server Connections
        Handlers --> Services
        Handlers --> LLMExecutor
        Services --> Repository
        LLMExecutor --> Services
    end
    
    %% External Components
    subgraph ExternalServices [External Services]
        Supabase[(Supabase DB)]
        OpenAI[OpenAI API]
        GoogleAI[Google AI API]
    end
    
    %% Admin Interface
    subgraph Admin [Flutter Web Admin]
        AdminUI[Admin UI]
    end
    
    %% Cross-Component Connections
    Client <--> |API Calls| Server
    Admin <--> |API Calls| Server
    Server <--> Repository
    Repository <--> Supabase
    LLMExecutor <--> OpenAI
    LLMExecutor <--> GoogleAI
    
    %% Styling
    classDef clientComponents fill:#d4f1f9,stroke:#05a0c8,stroke-width:1px
    classDef serverComponents fill:#ffe6cc,stroke:#f7931e,stroke-width:1px
    classDef databases fill:#e1d5e7,stroke:#9673a6,stroke-width:1px
    classDef externalServices fill:#d5e8d4,stroke:#82b366,stroke-width:1px
    
    class Client,CCS,ViewModels,Views,LocalDB clientComponents
    class Server,Handlers,Services,LLMExecutor,Repository serverComponents
    class Supabase,SQLite,SecureStorage databases
    class OpenAI,GoogleAI,ExternalServices externalServices
    class Admin clientComponents
```

## Data Flow Diagram

```mermaid
sequenceDiagram
    participant User
    participant Client as Flutter Client
    participant Server as Go API Server
    participant DB as Supabase DB
    participant LLM as LLM Provider (OpenAI/Google)
    
    %% Prompt Execution Flow
    User->>Client: Select prompt
    Client->>Client: Detect variables
    Client->>User: Show variables dialog
    User->>Client: Fill variable values
    Client->>Client: Process template
    Client->>Server: Execute prompt request
    Server->>DB: Fetch prompt instructions
    DB->>Server: Return instructions
    Server->>Server: Process template
    Server->>LLM: Send to LLM provider
    LLM->>Server: Return response
    Server->>Client: Return processed response
    Client->>User: Display response
    
    %% Usage Tracking Flow
    Client->>Client: Record prompt usage locally
    Client->>Server: Sync usage data (periodic)
    Server->>DB: Store usage data
    Server->>Server: Update popularity scores
    
    %% Popular Prompts Flow
    Client->>Server: Request popular prompts
    Server->>DB: Fetch popular prompts
    DB->>Server: Return popular prompts
    Server->>Client: Return popular prompts
    Client->>User: Display popular prompts
```

## Variable Processing Flow

```mermaid
flowchart LR
    %% Input and Processing
    Input[User Input] --> EntityDetection[Entity Detection]
    EntityDetection --> VariableExtraction[Variable Extraction]
    VariableExtraction --> VariableDialog[Variables Dialog]
    VariableDialog --> TemplateProcessing[Template Processing]
    TemplateProcessing --> LLMExecution[LLM Execution]
    
    %% Variable Types
    subgraph VariableTypes [Variable Types]
        EntityVar[Entity Variables<br>{{CATEGORY:ENTITY}}]
        PrefixedVar[Prefixed Variables<br>{{$N}CATEGORY:ENTITY}}]
        CustomVar[Custom Variables]
    end
    
    %% Variable Processing
    subgraph Processing [Processing Steps]
        Extract[1. Extract Variables]
        Normalize[2. Normalize Names]
        Resolve[3. Resolve Values]
        Replace[4. Replace in Template]
    end
    
    VariableExtraction --> VariableTypes
    VariableDialog --> Processing
    
    %% Styling
    classDef process fill:#d4f1f9,stroke:#05a0c8,stroke-width:1px
    classDef varTypes fill:#ffe6cc,stroke:#f7931e,stroke-width:1px
    classDef steps fill:#d5e8d4,stroke:#82b366,stroke-width:1px
    
    class Input,EntityDetection,VariableExtraction,VariableDialog,TemplateProcessing,LLMExecution process
    class EntityVar,PrefixedVar,CustomVar varTypes
    class Extract,Normalize,Resolve,Replace steps
```

## Notes

- The diagrams above represent the high-level architecture of the Promz platform.
- Colors are used to distinguish between different types of components:
  - Blue: Client-side components
  - Orange: Server-side components
  - Purple: Database components
  - Green: External services
- Arrows indicate the direction of data flow or dependencies between components.

These diagrams should be updated as the architecture evolves to ensure they remain an accurate representation of the system.
