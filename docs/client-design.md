# Promz Client Design

## Overview

The Promz client is a Flutter-based mobile application built on MVVM architecture with a strong emphasis on maintainable, testable code. The application leverages Riverpod for state management and follows platform-specific design guidelines for both iOS and Android.

## Architecture

### Core Components

#### ClientContextService (CCS)

The ClientContextService (CCS) is the central nervous system of the Promz client application. It:

- Provides centralized state management and context for the entire application
- Handles all prompt-related operations including variables, entity detection, and suggestions
- Manages synchronization between local and remote data
- Coordinates between different services and view models

Key Features:

- Singleton pattern with dependency injection support
- Thread-safe operations for concurrent access
- Built-in caching and optimization
- Extensible plugin architecture for future features

#### View Models

- Follow MVVM pattern strictly
- Use Riverpod for state management (migrated from Provider)
- Handle business logic and state transformations
- Implement proper error handling and logging
- Manage UI state transitions

#### Services

1. **VariableManager**

   - Handles template format: `{{CATEGORY:ENTITY}}`
   - Supports dynamic variable resolution
   - Maintains user-friendly display text mapping
   - Manages variable dependencies
   - Provides centralized variable value storage
   - Supports normalized variable name lookups
   - Handles variable extraction from templates

2. **EntityDetectionService**

   - Real-time entity detection in text
   - Support for multiple entity types (finance, location, etc.)
   - Customizable detection rules
   - Caching for performance
   - Dictionary-based entity matching
   - Fuzzy matching for approximate matches
   - Template-based entity extraction
   - Special handling for financial entities

3. **PromptSuggestionService**
   - AI-powered prompt suggestions
   - Category-aware matching
   - Score-based relevance ranking
   - Recent and popular prompts tracking
   - Keyword extraction and matching
   - Contextual suggestion prioritization
   - Performance optimization for real-time suggestions

## UI Components

### Variables System

The variables system provides a flexible way to handle dynamic content in prompts:

1. **VariablesDialog**

   - Modal interface for editing template variables
   - Supports both entity and custom variables
   - Provides validation and feedback
   - Maintains variable state during editing
   - Offers "Save" and "Execute" options
   - Properly handles dismissal behaviors
   - Restores original values on cancel

2. **VariablesSection**

   - Compact display of active variables as chips
   - Quick access to variable editing
   - Visual indication of variable types
   - Supports variable removal
   - Horizontal scrolling for many variables
   - Gradient overlay for edit button

3. **AutocompleteField**
   - Intelligent text input with suggestions
   - Real-time entity detection
   - Debounced suggestion requests
   - Platform-specific animations
   - Variable resolution on selection
   - Proper overlay management
   - Handles focus and keyboard events

## State Management

### Riverpod Integration

We have fully migrated from Provider to Riverpod for state management, offering:

- Better static analysis and type safety
- More predictable state updates
- Simplified testing capabilities
- Improved performance through selective rebuilds

### State Flow

1. User actions trigger ViewModel methods
2. ViewModels process business logic through CCS
3. Services handle specific functionalities
4. State updates flow back through Riverpod
5. UI updates automatically based on state changes

## Data Flow

```text
User Input → ViewModel → CCS → Services → Repository
     ↑                                        ↓
     └────────── UI ← State Updates ←─────────┘
```

## Database Integration

- SQLite-based local database with Room-like abstraction
- Supabase integration for cloud synchronization
- Efficient query optimization
- Migration support for schema updates
- Entity relationship modeling
- Transaction support for data integrity

## Performance Considerations

- Lazy loading for large datasets
- Caching strategies for frequent operations
- Debouncing for text input and search
- Background processing for heavy computations
- Efficient database operations
- Memory management for large collections
- Optimized list rendering with proper keys

## Testing Strategy

- Unit tests for ViewModels and Services
- Widget tests for UI components
- Integration tests for full features
- Golden tests for UI consistency
- Performance benchmarks
- Mocked dependencies for isolated testing
- Comprehensive test coverage goals

## Error Handling

- Consistent error reporting through CCS
- User-friendly error messages
- Graceful degradation
- Offline support
- Automatic retry mechanisms
- Detailed logging for troubleshooting
- Crash reporting and analytics

## Future Considerations

- Enhanced real-time collaboration
- Advanced entity detection
- Improved suggestion algorithms
- Cross-platform optimizations
- Extended plugin support
- AI-powered content generation
- Enhanced synchronization capabilities
