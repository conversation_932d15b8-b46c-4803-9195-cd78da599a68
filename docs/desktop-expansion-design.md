# Promz Multi-Platform Expansion Design

**Version:** 1.0
**Date:** 2025-04-22

## Document History

| Version | Date       | Description                                         |
| ------- | ---------- | --------------------------------------------------- |
| 1.0     | 2025-04-22 | Initial design document                             |

## Overview

This document outlines the technical requirements and implementation strategy for expanding the
Promz application to support web, Windows, and macOS platforms in addition to the existing mobile
platforms (Android and iOS).

## Current Architecture

Promz is currently a Flutter-based mobile application with the following architecture:

- **Architecture**: MVVM pattern with Riverpod for state management
- **Backend**: Supabase for authentication, data storage, and synchronization
- **Local Storage**: SQLite via drift and sqlite3_flutter_libs
- **Authentication**: Google Sign-In and Apple Sign-In
- **Platform-Specific Features**: File handling, sharing, device information

## Platform-Specific Challenges

### 1. Mobile-Only Dependencies

Several dependencies in the current implementation are mobile-specific:

| Dependency | Issue | Platforms Affected |
|------------|-------|-------------------|
| `safe_device` | Mobile-only, used for emulator detection | Web, Windows, macOS |
| `receive_sharing_intent` | Mobile-specific sharing API | Web, Windows, macOS |
| `sqlite3_flutter_libs` | Not compatible with web | Web |
| `google_sign_in` | Requires platform-specific implementation | Web, Windows, macOS |
| `sign_in_with_apple` | Requires platform-specific implementation | Web, Windows |
| `flutter_secure_storage` | Requires platform-specific alternatives | Web |

### 2. Platform-Specific Code

Current implementation contains platform-specific code that needs adaptation:

- `Platform.isIOS` and `Platform.isAndroid` checks in `PlatformUtils`
- Direct use of `dart:io` for file system operations
- Mobile-specific UI patterns and interactions
- Platform-specific authentication flows

## Platform-Specific MVP Requirements

### macOS Platform MVP

#### Core Requirements

1. **Authentication**:
   - Apple Sign-In integration only for initial MVP
   - Defer Google Sign-In to phase 2

2. **Storage**:
   - Full SQLite implementation using `sqlite3` package
   - Proper file paths for macOS environment
   - Local-first approach with optional cloud sync

3. **UI Adaptation**:
   - Basic responsive layout with minimum window size constraints
   - macOS-style window controls and menus
   - Support for keyboard shortcuts and trackpad gestures

4. **Feature Omissions for MVP**:
   - No emulator detection (remove `safe_device` dependency)
   - Simplified sharing via clipboard instead of system share sheet
   - No WhatsApp export functionality initially

#### Expedited Release Strategy

1. **Authentication Simplification**:
   - Start with Apple Sign-In only
   - Add a simple "Sign in with browser" option that opens Supabase auth in browser

2. **Simplified Storage**:
   - Use existing SQLite implementation with minimal changes
   - Focus on proper file paths and permissions for macOS

3. **UI Adaptation**:
   - Implement basic window sizing and controls
   - Adapt critical UI components only (prompt list, editor, variable handling)
   - Defer advanced UI patterns to post-MVP

### Windows Platform MVP

#### Core Requirements

1. **Authentication**:
   - Browser-based OAuth flow for Google and Microsoft authentication
   - No Apple Sign-In initially

2. **Storage**:
   - Full SQLite implementation with Windows-specific paths
   - Windows-appropriate secure storage for credentials
   - Proper file system permissions handling

3. **UI Adaptation**:
   - Windows-native window chrome and controls
   - Support for keyboard navigation and shortcuts
   - Proper high-DPI support

4. **Feature Omissions for MVP**:
   - No emulator detection
   - Basic file handling without advanced sharing
   - Simplified clipboard operations instead of system share

#### Expedited Release Strategy

1. **Authentication Simplification**:
   - Implement browser-based OAuth flow for Google and Microsoft
   - Defer native Google Sign-In integration
   - Defer Apple Sign-In integration

2. **Storage Optimization**:
   - Reuse existing SQLite code with minimal Windows-specific adaptations
   - Implement simple credential storage using Windows-specific APIs

3. **UI Focus Areas**:
   - Window chrome and basic responsive layout
   - Core prompt management functionality
   - Essential keyboard shortcuts

### Web Platform MVP

#### Core Requirements

1. **Authentication**:
   - Supabase Auth UI integration
   - Support for Google and Microsoft OAuth via redirects
   - Email/password authentication

2. **Storage**:
   - IndexedDB implementation for local storage
   - Browser-compatible secure storage for tokens
   - Strong focus on cloud synchronization

3. **UI Adaptation**:
   - Fully responsive design for various screen sizes
   - Browser-specific UI patterns and navigation
   - Touch and mouse input support

4. **Feature Limitations**:
   - Limited offline capabilities compared to desktop/mobile
   - Simplified file handling due to browser restrictions
   - No system integration features

#### Expedited Release Strategy

1. **Progressive Web App Approach**:
   - Start with core functionality as a PWA
   - Implement service worker for basic offline support
   - Focus on responsive design from the beginning

2. **Storage Simplification**:
   - Start with cloud-first approach (less reliance on local storage)
   - Implement basic IndexedDB for essential offline functionality
   - Defer complex synchronization to post-MVP

3. **Authentication Focus**:
   - Leverage Supabase Auth UI for quick implementation
   - Implement proper redirect handling for OAuth flows
   - Ensure secure token storage in browser context

## Implementation Strategy

### Phase 1: Platform Abstraction Layer

#### 1.1 Platform Service Interface

Create a platform abstraction layer to handle platform-specific functionality:

```dart
abstract class PlatformService {
  bool get isWeb;
  bool get isDesktop;
  bool get isMobile;
  bool get isAndroid;
  bool get isIOS;
  bool get isWindows;
  bool get isMacOS;
  
  // Platform capabilities
  bool get supportsFileSharing;
  bool get supportsGoogleAuth;
  bool get supportsAppleAuth;
  bool get supportsSecureStorage;
  
  // Platform-specific implementations
  Future<void> shareContent(String content, {String? subject});
  Future<String?> getClipboardText();
  Future<Directory> getTemporaryDirectory();
  Future<Directory> getApplicationDocumentsDirectory();
}
```

#### 1.2 Platform-Specific Implementations

Create platform-specific implementations of the interface:

- `MobilePlatformService`: For Android and iOS
- `WebPlatformService`: For web platform
- `DesktopPlatformService`: For Windows and macOS with platform-specific branches

#### 1.3 Dependency Injection

Update the dependency injection system to provide the correct implementation based on platform:

```dart
final platformServiceProvider = Provider<PlatformService>((ref) {
  if (kIsWeb) {
    return WebPlatformService();
  } else if (Platform.isWindows || Platform.isMacOS) {
    return DesktopPlatformService();
  } else {
    return MobilePlatformService();
  }
});
```

### Phase 2: Storage Adaptation

#### 2.1 Web Storage Solution

For web platform, replace SQLite with alternative storage solutions:

- Use `IndexedDB` via `idb_sqflite` package for structured data storage
- Create a `WebStorageRepository` implementing the same interface as the current database repository
- Implement synchronization between local IndexedDB and Supabase

#### 2.2 Desktop Database Configuration

For desktop platforms:

- Continue using SQLite but with platform-specific configuration
- Replace `sqlite3_flutter_libs` with `sqlite3` package for desktop platforms
- Configure proper file paths for database storage on each platform

#### 2.3 Secure Storage Alternatives

Implement platform-specific secure storage solutions:

- Web: Use `flutter_secure_storage_web` with localStorage/sessionStorage encryption
- Windows: Use Windows Credential Manager via FFI
- macOS: Use Keychain via FFI

### Phase 3: Authentication Flows

#### 3.1 Web Authentication

Implement web-specific authentication flows:

- Use Supabase Auth UI for web platforms
- Implement OAuth redirects for Google and Apple authentication
- Handle token storage and refresh in browser context

#### 3.2 Desktop Authentication

Implement desktop-specific authentication:

- Windows: Implement OAuth using system browser or embedded WebView
- macOS: Implement Apple Sign-In using native APIs
- Create desktop-specific token storage and management

#### 3.3 Authentication UI Adaptation

Create platform-specific authentication UI components:

- Web-optimized login screens with proper redirect handling
- Desktop-native authentication dialogs
- Platform-appropriate error handling and recovery flows

### Phase 4: UI Adaptation

#### 4.1 Responsive Layout System

Enhance the UI to be responsive across all platforms:

- Use `flutter_adaptive_scaffold` more extensively
- Implement desktop-specific layouts with proper window sizing
- Create web-responsive designs that work across different screen sizes

#### 4.2 Platform-Specific UI Components

Extend `PlatformUtils` to support web and desktop platforms:

- Add web-specific UI components
- Implement desktop-native UI elements (menus, dialogs, etc.)
- Create platform-appropriate navigation patterns

#### 4.3 Input Handling

Adapt input handling for different platforms:

- Implement keyboard shortcuts for desktop platforms
- Support mouse interactions properly on desktop and web
- Handle touch vs. pointer events appropriately

### Phase 5: File System and Sharing

#### 5.1 Web File Handling

Implement web-specific file handling:

- Use `file_picker_web` for file selection
- Implement browser download API for saving files
- Create web-compatible file processing utilities

#### 5.2 Desktop File System Integration

Enhance file system integration for desktop platforms:

- Implement proper file dialogs using native APIs
- Support drag-and-drop for desktop platforms
- Create desktop-specific file association handling

#### 5.3 Sharing Mechanisms

Replace mobile-specific sharing with cross-platform alternatives:

- Web: Implement Web Share API where available, fallback to copy-to-clipboard
- Desktop: Implement system-native sharing or clipboard operations
- Create unified sharing interface across platforms

## Technical Implementation Details

### Web Platform

#### Configuration Changes

1. Update `web/index.html` to include necessary web-specific configurations:
   - Add appropriate meta tags for PWA support
   - Configure Supabase authentication redirects
   - Set up service worker for offline capabilities

2. Create web-specific entry points and initialization:
   - Implement web-specific initialization in `main.dart`
   - Handle browser history and deep linking

#### Web-Specific Packages

Add the following packages for web support:

```yaml
dependencies:
  # Web alternatives for mobile packages
  idb_sqflite: ^1.0.0  # IndexedDB implementation of sqflite
  file_picker_web: ^2.0.0
  url_launcher_web: ^2.0.0
  flutter_secure_storage_web: ^1.0.0
  
  # PWA support
  pwa_install: ^0.0.5
```

### Windows and macOS Support

#### Project Configuration

1. Add desktop platform support to the project:
   ```bash
   flutter config --enable-windows-desktop
   flutter config --enable-macos-desktop
   ```

2. Create platform-specific configuration files:
   - `windows/runner/main.cpp` - Configure Windows-specific features
   - `macos/Runner/MainFlutterWindow.swift` - Configure macOS-specific features

#### Desktop-Specific Packages

Add the following packages for desktop support:

```yaml
dependencies:
  # Desktop UI enhancements
  bitsdojo_window: ^0.1.5  # Window customization
  desktop_drop: ^0.4.0  # Drag and drop support
  tray_manager: ^0.2.0  # System tray integration
  
  # Desktop storage and security
  sqlite3: ^1.9.1  # SQLite for desktop
  local_auth_windows: ^1.0.0  # Windows authentication
  local_auth_darwin: ^1.0.0  # macOS authentication
```

## Optimized Implementation Roadmap

### Phase 1: Foundation (3 weeks)

1. Create platform abstraction layer with conditional feature flags
2. Implement conditional imports for platform-specific code
3. Set up CI/CD pipeline for multi-platform builds
4. Create initial responsive UI layouts

### Phase 2: Platform-Specific MVPs (Parallel Development)

#### macOS MVP (4 weeks)

1. **Week 1-2**: Basic window and UI adaptation
   - Implement window controls and basic responsive layout
   - Adapt core UI components for desktop interaction
   - Configure SQLite for macOS environment

2. **Week 3-4**: Authentication and core functionality
   - Implement email/password authentication
   - Adapt file system operations for macOS
   - Ensure core prompt management functionality works

#### Windows MVP (5 weeks)

1. **Week 1-2**: Basic window and UI adaptation
   - Implement Windows-native window chrome
   - Create responsive layouts for desktop screens
   - Configure SQLite for Windows environment

2. **Week 3-5**: Authentication and core functionality
   - Implement browser-based OAuth flow for Google and Microsoft
   - Defer native Google Sign-In integration
   - Defer Apple Sign-In integration
   - Adapt file system operations for Windows
   - Implement essential keyboard shortcuts

#### Web MVP (6 weeks)

1. **Week 1-2**: Authentication and basic UI
   - Integrate Supabase Auth UI
   - Implement responsive web layouts
   - Set up PWA configuration
   - Implement browser-based OAuth flow for Google and Microsoft
   - Defer native Google Sign-In integration
   - Defer Apple Sign-In integration

2. **Week 3-4**: Storage and core functionality
   - Implement IndexedDB storage solution
   - Create web-specific file handling
   - Adapt core prompt management for web

3. **Week 5-6**: Progressive enhancements
   - Add offline capabilities with service worker
   - Implement web-specific sharing
   - Optimize performance for various browsers

### Phase 3: Feature Parity and Refinement

#### macOS Enhancements (3 weeks post-MVP)

1. Add Apple Sign-In integration
2. Implement advanced file handling and sharing
3. Add macOS-specific keyboard shortcuts and menu items

#### Windows Enhancements (3 weeks post-MVP)

1. Add native Google Sign-In integration
2. Implement advanced file handling with drag-and-drop
3. Add Windows-specific keyboard shortcuts and context menus

#### Web Enhancements (4 weeks post-MVP)

1. Enhance offline capabilities
2. Implement advanced synchronization
3. Add Apple Sign-In support where available

### Phase 4: Testing and Optimization (Ongoing)

1. Platform-specific testing and bug fixes
2. Performance optimization for each platform
3. Accessibility improvements
4. Documentation updates

## Feature Prioritization Matrix

| Feature | macOS MVP | Windows MVP | Web MVP | Post-MVP |
|---------|-----------|-------------|---------|----------|
| **Authentication** | Email/Password | Email/Password, Browser OAuth | Email/Password, Google OAuth | Full OAuth support |
| **Storage** | SQLite | SQLite | IndexedDB (basic) | Advanced sync |
| **UI Adaptation** | Basic responsive | Basic responsive | Fully responsive | Platform-optimized |
| **File Handling** | Basic open/save | Basic open/save | Download only | Advanced integration |
| **Sharing** | Clipboard | Clipboard | Web Share API | Native sharing |
| **Offline Support** | Full | Full | Basic | Enhanced |
| **Platform Features** | Basic window controls | Basic window controls | PWA install | Native integration |

## Platform-Specific Development Guidelines

### macOS Development

1. **Development Environment**:
   - Use macOS for development and testing
   - Configure Xcode for macOS builds
   - Set up proper code signing and notarization process

2. **UI Guidelines**:
   - Follow macOS Human Interface Guidelines
   - Implement proper window management
   - Support trackpad gestures and keyboard shortcuts

3. **Testing Focus**:
   - Test on both Intel and Apple Silicon
   - Verify proper file system permissions
   - Ensure smooth authentication flows

### Windows Development

1. **Development Environment**:
   - Use Windows for development and testing
   - Configure Visual Studio for Windows builds
   - Set up proper code signing process

2. **UI Guidelines**:
   - Follow Windows UI design principles
   - Implement proper window chrome and controls
   - Support keyboard navigation and shortcuts

3. **Testing Focus**:
   - Test on different Windows versions (10, 11)
   - Verify proper file system access
   - Test high-DPI support

### Web Development

1. **Development Environment**:
   - Use any platform with proper browser testing
   - Set up browser testing matrix (Chrome, Firefox, Safari, Edge)
   - Configure proper CORS and security settings

2. **UI Guidelines**:
   - Follow responsive web design principles
   - Implement proper mobile and desktop views
   - Support both touch and mouse interactions

3. **Testing Focus**:
   - Test on different browsers and screen sizes
   - Verify authentication redirects
   - Test offline capabilities

## Conclusion

Expanding Promz to support web, Windows, and macOS platforms requires significant architectural
changes to handle platform-specific dependencies and behaviors. By implementing a robust platform
abstraction layer and adapting key components for each platform, we can create a consistent user
experience across all supported platforms while leveraging platform-specific capabilities where
appropriate.

The optimized MVP approach allows us to expedite the release of platform-specific versions by
prioritizing core functionality and deferring non-essential features to post-MVP phases. By focusing
on platform-specific strengths and working around limitations, we can deliver valuable functionality
to users more quickly while maintaining a roadmap for feature parity in future releases.

This strategy balances the need for rapid deployment with the goal of providing a consistent,
high-quality experience across all platforms. The phased implementation approach minimizes risk and
allows for early feedback on platform-specific implementations, ensuring that development efforts
are aligned with user needs and expectations.
