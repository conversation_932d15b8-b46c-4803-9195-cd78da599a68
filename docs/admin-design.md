# Promz Admin Design Document

## 1. Overview

The Promz admin application is a Flutter-based tool for maintainers to manage prompts, categories, and related metadata. It is architected to maximize code sharing with the end-user client app, ensuring maintainability, consistency, and rapid feature delivery. The admin’s main role is to support and enhance the client experience, including internationalization and quality assurance.

---

## 2. Architecture

- **Platform:** Flutter (admin and client)
- **Code Sharing:** Both apps depend on the `promz_common` package for models, widgets, and business logic.
- **Backend:** Supabase (PostgreSQL, authentication, storage)
- **API:** Direct Supabase queries; REST endpoints retained for compatibility and integration.
- **Deployment:** Admin is distributed separately from the client but follows the same modular architecture.

---

## 3. Data Storage & Synchronization

- **Client:** Uses a local SQLite database for offline-first operation, with periodic synchronization to the backend.
- **Admin:** Connects directly to Supabase/PostgreSQL for immediate, real-time data management.
- **No Shared Database Code:** There is no code sharing between client and admin in the database layer. Only models, business logic, and UI components are shared.
- **Synchronization:** Data consistency between client and admin is maintained via explicit sync and migration flows, not shared storage code.

---

## 4. Admin Features

- **Prompt Management:** Create, view, edit, and delete prompts. Assign prompts to categories, manage metadata, and synchronize with the client.
- **Category Management:** Full CRUD for categories, including icons, keywords, and metadata.
- **Prompt Instructions:** View and edit prompt instructions using markdown, with support for formatting and entity template insertion.
- **Bulk Operations:** Multi-select and batch actions (planned).
- **Error Handling:** Consistent feedback for loading, success, and error states.
- **Analytics and Audit Logging:** (Directional) Track admin actions for accountability and debugging.

---

## 5. Markdown Integration

- **Editing:** Prompts’ instructions use markdown, edited via widgets from `promz_common` (e.g., MarkdownEditor, MarkdownEditorWidget).
- **Preview:** Toggle between editing and preview modes.
- **Formatting Toolbar:** Bold, italics, headings, lists, code, and entity template insertion (where supported).
- **Entity Templates:** (Planned) Insert dynamic entity variables into markdown.
- **Widget Audit:** The EnhancedMarkdownEditor widget exists but is not currently integrated; a strategy is in place to either unify markdown editing around it or deprecate/remove unused code.

---

## 6. Authentication & Access

- **Supabase Session:** All admin actions require authentication via Supabase.
- **Role-Based Access Control (RBAC):** (Planned) Future support for RBAC to restrict admin-only features and actions.
- **Audit Trail:** (Directional) Track and log admin actions for security and compliance.

---

## 7. Code Sharing & Modularity

- **Shared Models:** Prompts, categories, instructions, and entities are defined in `promz_common`.
- **Shared Widgets:** UI components for lists, editors, and displays are reused across admin and client.
- **Business Logic:** Where possible, services and providers are shared or abstracted for cross-app use.
- **Redundancy Audit:** Ongoing review to unify redundant components (e.g., markdown editors) and remove unused code.

---

## 8. Testing

- **Integration Tests:** Focus on flows that impact both admin and client (e.g., prompt creation, editing, and sync).
- **Regression Testing:** Ensure that admin changes do not break client features.
- **Widget/Unit Tests:** Shared components in `promz_common` are covered by unit and widget tests.
- **Admin-Specific Testing:** Emphasizes supporting and validating client-facing features rather than unique admin logic.

---

## 9. Internationalization (i18n)

- **Shared i18n Resources:** All user-facing text, prompt titles, and instructions are designed for localization.
- **Admin’s Role:** Enables maintainers to manage localized metadata for prompts and categories, supporting the client’s i18n needs.
- **Minimal Admin UI Localization:** The admin interface itself is not the primary focus for localization, but it supports workflows for managing translations and localized content for the client.

---

## 10. Future Considerations & Directional Notes

- **RBAC:** Implement granular permissions for different admin roles.
- **Advanced Search/Filtering:** Enhanced filtering for prompts and categories.
- **Bulk Operations:** Batch actions for efficient management.
- **Analytics:** Dashboard for admin activity and prompt/category usage.
- **Markdown Editor Unification:** Decide on a single markdown editing widget (e.g., integrate or remove EnhancedMarkdownEditor).
- **Automated Sync and Validation:** Tools for ensuring client/admin data consistency.

---

## 11. Appendix

- **Key Shared Widgets/Services:**  
  - MarkdownEditor, MarkdownDisplay, PromptList, PromptCard, etc.
- **Deprecated/Under Review:**  
  - EnhancedMarkdownEditor (pending audit/integration/removal decision)
- **Testing Utilities:**  
  - Shared test harnesses and mock data for integration tests.
