# YouTube Transcript Service Setup

## Overview

The application now supports fetching YouTube transcripts using a multi-strategy approach:

1. Official YouTube Data API (primary method)
2. HTML scraping (fallback method)

## Configuration

### Setting up the YouTube API Key

1. Go to the [Google Developer Console](https://console.developers.google.com/)
2. Create a new project (or use an existing one)
3. Enable the YouTube Data API v3
4. Create API credentials (API Key)
5. Set the environment variable:

```text
YOUTUBE_API_KEY=your_api_key_here
```

### Without an API Key

The system will automatically fall back to the HTML scraping approach if:

- No API key is provided
- The API quota is exceeded
- The API request fails for any reason

## Scaling Considerations

- The default cache TTL is 24 hours to minimize API requests
- For high-traffic deployments, monitor your YouTube API quota usage
- If needed, scale by:
  - Using a Redis or other distributed cache instead of in-memory
  - Implementing API key rotation for higher quotas
  - Adjusting the fallback strategy order

## Troubleshooting

Common issues:

1. **Transcript unavailable**: Not all YouTube videos have transcripts/captions
2. **API quota exceeded**: Check the Google Cloud Console for quota usage
3. **Scraping failures**: YouTube may change their page structure, requiring updates
4. **Rate limiting**: Too many scraping requests can trigger YouTube rate limits

## Logs

The service logs detailed information for troubleshooting:

- Strategy selection
- Success/failure of each strategy
- Cache hit/miss information
- Transcript extraction details
