# File Upload and Processing UI Design

**Version:** 1.0  
**Date:** 2025-04-23

## 1. Overview

This document outlines the design for the file upload and processing UI in the PROMZ application.
The system will provide users with a non-intrusive way to track the status of file uploads and
processing, particularly for shared content, while ensuring proper authentication handling and
background processing capabilities.

## 2. Goals

- Create a non-intrusive UI for tracking file uploads and processing
- Implement persistent storage of upload/processing tasks
- Support background processing of files
- Handle authentication requirements gracefully
- Allow processing of previously shared files after authentication
- Provide clear visual feedback on processing status
- Support multiple concurrent file processing tasks

## 3. Core Concepts

### 3.1. Task Management

1. **Shared Content Tasks**

   - Represent individual file processing operations
   - Track status, progress, and results
   - Persist across app restarts
   - Support various file types (ZIP, PDF, etc.)

2. **Task States**

   - Pending: Task created but not yet started
   - Processing: Task is actively being processed
   - Completed: Task finished successfully
   - Failed: Task encountered an error
   - Auth Required: Authentication needed to process

3. **Authentication Handling**
   - Tasks requiring authentication are preserved
   - Processing resumes automatically after authentication
   - Clear indication of authentication requirements

### 3.2. UI Components

1. **Status Indicator**

   - Small, non-intrusive icon in app header
   - Shows current processing state
   - Displays badge with number of active tasks
   - Color-coded for different states

2. **Task List**

   - Expandable from status indicator
   - Shows all current and recent tasks
   - Displays progress, status, and actions
   - Allows management of tasks (retry, cancel, etc.)

3. **Progress Visualization**
   - Clear progress indicators for active tasks
   - Estimated completion time for large files
   - File size and type information
   - Error state visualization

## 4. Technical Implementation

### 4.1. Data Model

```dart
/// Represents a file processing task
class SharedContentTask {
  /// Unique identifier for the task
  final String id;

  /// Path to the file being processed
  final String filePath;

  /// Name of the file
  final String fileName;

  /// Size of the file in bytes
  final int fileSize;

  /// Current status of the task
  final SharedContentTaskStatus status;

  /// Progress from 0.0 to 1.0
  final double progress;

  /// Optional error message if status is failed
  final String? errorMessage;

  /// When the task was created
  final DateTime createdAt;

  /// When the task was last updated
  final DateTime updatedAt;

  /// ID of the processed content (if completed)
  final String? resultId;
}

/// Possible states for a shared content task
enum SharedContentTaskStatus {
  pending,
  processing,
  completed,
  failed,
  authRequired
}
```

### 4.2. Storage Schema

The tasks will be stored in SecureStorageService using the following JSON structure:

```json
{
  "shared_content_tasks": [
    {
      "id": "unique_task_id",
      "filePath": "/path/to/file.zip",
      "fileName": "file.zip",
      "fileSize": 144070000,
      "status": "pending",
      "progress": 0.0,
      "errorMessage": null,
      "createdAt": "2025-04-23T11:58:10.562Z",
      "updatedAt": "2025-04-23T11:58:10.562Z",
      "resultId": null
    }
  ]
}
```

### 4.3. Key Components

#### 4.3.1. SharedContentTaskManager

```dart
/// Manages shared content processing tasks
class SharedContentTaskManager {
  /// Add a new task for processing
  Future<SharedContentTask> addTask(String filePath);

  /// Update the status of a task
  Future<void> updateTaskStatus(String taskId, SharedContentTaskStatus status);

  /// Update the progress of a task
  Future<void> updateTaskProgress(String taskId, double progress);

  /// Mark a task as completed with a result
  Future<void> completeTask(String taskId, String resultId);

  /// Mark a task as failed with an error message
  Future<void> failTask(String taskId, String errorMessage);

  /// Mark a task as requiring authentication
  Future<void> requireAuthForTask(String taskId);

  /// Process tasks that were waiting for authentication
  Future<void> processAuthRequiredTasks();

  /// Get all tasks
  Future<List<SharedContentTask>> getAllTasks();

  /// Get tasks by status
  Future<List<SharedContentTask>> getTasksByStatus(SharedContentTaskStatus status);

  /// Stream of task updates
  Stream<List<SharedContentTask>> get tasksStream;
}
```

#### 4.3.2. Modified SharedContentHandler

```dart
/// Handles shared content from external sources
class SharedContentHandler {
  /// Process shared files using the task manager
  Future<void> processSharedFiles(List<SharedFile> files);

  /// Process a single file and update its task status
  Future<void> _processFile(SharedContentTask task);

  /// Check for authentication and handle accordingly
  Future<bool> _checkAuthentication(SharedContentTask task);

  /// Process any pending tasks after authentication
  Future<void> processPendingTasksAfterAuth();
}
```

#### 4.3.3. Background Task Integration

```dart
/// Extension of BackgroundTaskService for file processing
extension FileProcessingTasks on BackgroundTaskService {
  /// Start processing pending shared content tasks
  Future<void> startSharedContentProcessing();

  /// Process a specific shared content task
  Future<void> processSharedContentTask(String taskId);

  /// Check for tasks requiring authentication and process them if authenticated
  Future<void> checkAndProcessAuthRequiredTasks();
}
```

## 5. User Interface Design

### 5.1. Status Indicator

The status indicator will be a small icon in the app header that shows the current state of file processing:

- **Idle**: No active tasks (hidden or subtle)
- **Processing**: Animated icon with progress indication
- **Completed**: Success icon (briefly shown, then fades)
- **Error/Auth Required**: Warning icon with badge

The indicator will include a badge showing the number of active tasks when there are multiple files being processed.

### 5.2. Task List View

When tapped, the status indicator expands to show a task list with:

- File name and type
- Progress bar for each active task
- Status indicator (icon + text)
- Action buttons (retry, cancel, view)
- Timestamp information

The task list supports:

- Sorting by status or time
- Filtering by status
- Grouping by type or date

### 5.3. Authentication Flow

When a file requires authentication:

1. Task is marked as "Auth Required"
2. Status indicator shows authentication warning
3. User can tap to see details and sign in
4. After authentication, tasks resume automatically
5. User is notified of resumed processing

## 6. User Flows

### 6.1. Standard Upload Flow

1. User shares file(s) with the app
2. Tasks are created and shown in status indicator
3. Processing begins automatically
4. Progress is updated in real-time
5. On completion, success is indicated briefly
6. User can tap to view processed content

### 6.2. Authentication Required Flow

1. User shares file(s) with the app
2. System detects authentication requirement
3. Tasks are created and marked as "Auth Required"
4. Status indicator shows authentication warning
5. User taps indicator to see details
6. User signs in when ready
7. Processing resumes automatically
8. Normal flow continues from step 4 above

### 6.3. Background Processing Flow

1. User shares large file(s) with the app
2. Initial processing begins
3. User navigates away or closes app
4. Processing continues in background
5. Notifications indicate completion/failure
6. On app return, status is updated
7. User can view completed results

## 7. Implementation Phases

### 7.1. Phase 1: Core Task Management

- Implement SharedContentTask model
- Create SharedContentTaskManager with storage integration
- Modify SharedContentHandler to use task manager
- Implement basic status tracking

### 7.2. Phase 2: UI Components

- Create status indicator component
- Implement expandable task list
- Design and implement progress visualization
- Add authentication flow UI

### 7.3. Phase 3: Background Processing

- Integrate with BackgroundTaskService
- Implement task persistence across app restarts
- Add notification support for background completion
- Optimize for battery and resource efficiency

## 8. Design Considerations

### 8.1. Mobile-First Design

- Minimal UI footprint for status indication
- Touch-friendly interaction for task management
- Efficient background processing for battery life
- Responsive layout for different screen sizes

### 8.2. Error Handling

- Clear error states and messages
- Retry capabilities for failed tasks
- Graceful handling of network issues
- Proper cleanup of temporary files

### 8.3. Security

- Secure storage of task information
- Proper handling of authentication tokens
- Protection of sensitive file content
- Compliance with data protection regulations

## 9. Conclusion

This design provides a comprehensive approach to file upload and processing UI that balances user
experience with technical requirements. By implementing a non-intrusive status indicator with
expandable details, users can easily track the progress of their uploads without disrupting their
workflow. The persistent storage of tasks ensures that processing can continue across app restarts,
and the integration with authentication allows for seamless handling of files shared before
authentication.

The system's ability to process previously shared files after authentication is particularly
important for new users who may share content before creating an account. This creates a smooth
onboarding experience while maintaining security requirements.

---
