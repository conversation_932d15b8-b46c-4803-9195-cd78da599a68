# Server-Client Communication Design

## Overview

This document outlines a strategic approach to modernizing server-client communication in the PROMZ
application. The current architecture relies on REST API calls, which has limitations for real-time
updates, streaming content, and efficient binary data transfer.

Since the application is not yet in users' hands, we will adopt a clean-slate approach that focuses
on a single, efficient protocol (gRPC) with WebSockets for real-time updates. This approach
eliminates the complexity of maintaining backward compatibility with REST and allows for a more
streamlined implementation.

## Current Architecture

### REST API Communication

The current system uses a standard REST API approach:

- **Client**: Uses `ApiClient` class with HTTP methods (GET, POST, PUT, DELETE)
- **Server**: Gin-based router with standard HTTP handlers
- **Authentication**: Bearer token and API key-based auth
- **Data Format**: JSON for request/response payloads
- **File Processing**: Multi-part form uploads with polling for status updates

### Key Limitations

1. **Polling for Updates**: Status checks require periodic polling (e.g., file processing status)
2. **No Real-time Updates**: Changes to shared resources aren't pushed to clients
3. **Inefficient Serialization**: JSON is verbose compared to binary formats
4. **Connection Overhead**: Each request establishes a new connection
5. **Limited Streaming**: File content handling is inefficient for large files
6. **Duplicate Logic**: Multiple services handling file uploads with overlapping functionality
7. **Schema Validation**: No formal contract between client and server leading to potential field mismatches

## Proposed Architecture

We will implement a unified communication architecture with two complementary protocols:

### 1. gRPC for Core Communication

**Primary Protocol**: All core API functionality will use gRPC for efficient, typed communication.

**Target Areas:**

- File upload/download operations with streaming support
- All data operations (prompts, templates, settings)
- Authentication and license management
- Search and filtering operations

**Key Components:**

1. **Protocol Buffer Definitions**

   - Strongly typed service and message definitions
   - Versioned contracts between client and server
   - Efficient binary serialization

2. **Server Implementation**

   - gRPC service implementations in Go
   - Unified error handling and status codes
   - Streaming support for large file transfers

3. **Client Implementation**
   - Protocol-agnostic service interfaces
   - gRPC client implementations
   - Automatic reconnection and error handling

### 2. WebSockets for Real-time Updates

**Complementary Protocol**: WebSockets will handle real-time updates and notifications.

**Target Areas:**

- File processing status updates
- Popular prompts updates
- Collaborative editing notifications
- License status changes

**Key Components:**

1. **WebSocket Server**

   - Topic-based publish/subscribe system
   - Authentication and authorization per topic
   - Efficient message broadcasting

2. **WebSocket Client**
   - Topic subscription management
   - Automatic reconnection
   - Message type conversion

## Implementation Details

### 1. Protocol Buffer Definitions

```protobuf
syntax = "proto3";

package promz.processing;

service FileProcessingService {
  // Upload a file for processing with streaming support
  rpc UploadFile(stream FileUploadRequest) returns (FileUploadResponse);

  // Get processing status
  rpc GetStatus(StatusRequest) returns (StatusResponse);

  // Get processing results with streaming support for large content
  rpc GetResults(ResultsRequest) returns (stream ResultsResponse);

  // Cancel processing
  rpc CancelProcessing(CancelRequest) returns (CancelResponse);
}

message FileUploadRequest {
  // First message contains metadata
  message Metadata {
    string file_name = 1;
    string mime_type = 2;
    int64 file_size = 3;
    string license_tier = 4;
    map<string, string> custom_metadata = 5;
  }

  // Subsequent messages contain file chunks
  message Chunk {
    bytes data = 1;
    int32 chunk_index = 2;
  }

  oneof request {
    Metadata metadata = 1;
    Chunk chunk = 2;
  }
}

message FileUploadResponse {
  string id = 1;
  string status = 2;
  int32 estimated_time_seconds = 3;
  int32 max_tokens = 4;
  int64 file_size = 5;
  string license_tier = 6;
}

message StatusRequest {
  string id = 1;
}

message StatusResponse {
  string id = 1;
  string status = 2;
  double progress = 3;
  string error = 4;
  string message = 5;
  int32 tokens_processed = 6;
  int32 tokens_limit = 7;
  bool tokens_exceeded = 8;
}

message ResultsRequest {
  string id = 1;
}

message ResultsResponse {
  message Metadata {
    string id = 1;
    string content_type = 2;
    map<string, string> metadata = 3;
    bool has_full_content = 4;
    string content_url = 5;
    int64 expires_at = 6;
    int32 tokens_processed = 7;
    int32 tokens_limit = 8;
    bool tokens_exceeded = 9;
  }

  message ContentChunk {
    bytes data = 1;
    int32 chunk_index = 2;
    bool is_last = 3;
  }

  oneof response {
    Metadata metadata = 1;
    ContentChunk content = 2;
  }
}

message CancelRequest {
  string id = 1;
}

message CancelResponse {
  bool success = 1;
  string message = 2;
}
```

### 2. Server Implementation

```go
// FileProcessingServiceServer implements the gRPC FileProcessingService
type FileProcessingServiceServer struct {
    uploadHandler *processing.UploadHandler
    webSocketHandler *handler.WebSocketHandler
}

// UploadFile handles streaming file uploads
func (s *FileProcessingServiceServer) UploadFile(stream pb.FileProcessingService_UploadFileServer) error {
    // Implementation with chunked file handling
    // Broadcasts status updates via WebSocket
}

// GetStatus returns the current processing status
func (s *FileProcessingServiceServer) GetStatus(ctx context.Context, req *pb.StatusRequest) (*pb.StatusResponse, error) {
    // Implementation
}

// GetResults streams the processing results back to the client
func (s *FileProcessingServiceServer) GetResults(req *pb.ResultsRequest, stream pb.FileProcessingService_GetResultsServer) error {
    // Implementation with chunked response
}

// CancelProcessing cancels an ongoing processing job
func (s *FileProcessingServiceServer) CancelProcessing(ctx context.Context, req *pb.CancelRequest) (*pb.CancelResponse, error) {
    // Implementation
}
```

### 3. Client Implementation

```dart
// Protocol-agnostic interface
abstract class FileProcessingClient {
  // Upload a file with progress updates
  Future<FileUploadResponse> uploadFile(
    File file, {
    Map<String, dynamic>? metadata,
    String? licenseTier,
    Function(double progress)? onProgress,
  });

  // Get processing status
  Future<StatusResponse> getStatus(String id);

  // Get processing results
  Future<ResultsResponse> getResults(String id);

  // Cancel processing
  Future<bool> cancelProcessing(String id);
}

// gRPC implementation
class GrpcFileProcessingClient implements FileProcessingClient {
  final FileProcessingServiceClient _client;
  final WebSocketService _webSocketService;

  GrpcFileProcessingClient({
    required FileProcessingServiceClient client,
    required WebSocketService webSocketService,
    required LicenseManagerService licenseManager,
  }) : _client = client,
       _webSocketService = webSocketService;

  @override
  Future<FileUploadResponse> uploadFile(File file, {
    Map<String, dynamic>? metadata,
    String? licenseTier,
    Function(double progress)? onProgress,
  }) async {
    // Implementation using gRPC streaming
    // Subscribe to WebSocket for real-time progress updates
  }

  // Additional method implementations
}

// WebSocket message types for file processing
class FileProcessingMessage {
  final String type = 'file_processing';
  final String action; // 'status_update', 'completed', 'error'
  final FileProcessingPayload payload;

  FileProcessingMessage({
    required this.action,
    required this.payload,
  });

  factory FileProcessingMessage.fromJson(Map<String, dynamic> json) {
    return FileProcessingMessage(
      action: json['action'],
      payload: FileProcessingPayload.fromJson(json['payload']),
    );
  }
}

class FileProcessingPayload {
  final String id;
  final String status;
  final double progress;
  final String? error;
  final String? message;
  final int? tokensProcessed;
  final int? tokensLimit;
  final bool tokensExceeded;

  FileProcessingPayload({
    required this.id,
    required this.status,
    required this.progress,
    this.error,
    this.message,
    this.tokensProcessed,
    this.tokensLimit,
    this.tokensExceeded = false,
  });

  factory FileProcessingPayload.fromJson(Map<String, dynamic> json) {
    // Implementation
  }
}

// WebSocket service integration with file processing
class FileProcessingWebSocketClient {
  final WebSocketService _webSocketService;

  // Subscribe to status updates for a specific file processing job
  Stream<FileProcessingPayload> subscribeToStatusUpdates(String jobId) {
    return _webSocketService.subscribe<FileProcessingPayload>(
      'file_processing_$jobId',
      (data) => FileProcessingPayload.fromJson(data),
    );
  }
}

// Factory for creating the appropriate client implementation
class FileProcessingClientFactory {
  static FileProcessingClient create({
    required LicenseManagerService licenseManager,
  }) {
    // Create gRPC client
    final channel = ClientChannel(
      ApiConfig.grpcHost,
      port: ApiConfig.grpcPort,
      options: ChannelOptions(
        credentials: licenseManager.apiKey != null
            ? ChannelCredentials.secure()
            : ChannelCredentials.insecure(),
      ),
    );

    final client = FileProcessingServiceClient(channel);

    // Create WebSocket service for real-time updates
    final webSocketService = WebSocketService(
      apiKey: licenseManager.apiKey,
    );

    // Return the gRPC implementation
    return GrpcFileProcessingClient(
      client: client,
      webSocketService: webSocketService,
      licenseManager: licenseManager,
    );
  }
}
```

### 4. WebSocket Integration for Real-time Updates

```dart
// WebSocket message types for file processing
class FileProcessingMessage {
  final String type = 'file_processing';
  final String action; // 'status_update', 'completed', 'error'
  final FileProcessingPayload payload;

  FileProcessingMessage({
    required this.action,
    required this.payload,
  });

  factory FileProcessingMessage.fromJson(Map<String, dynamic> json) {
    return FileProcessingMessage(
      action: json['action'],
      payload: FileProcessingPayload.fromJson(json['payload']),
    );
  }
}

class FileProcessingPayload {
  final String id;
  final String status;
  final double progress;
  final String? error;
  final String? message;
  final int? tokensProcessed;
  final int? tokensLimit;
  final bool tokensExceeded;

  FileProcessingPayload({
    required this.id,
    required this.status,
    required this.progress,
    this.error,
    this.message,
    this.tokensProcessed,
    this.tokensLimit,
    this.tokensExceeded = false,
  });

  factory FileProcessingPayload.fromJson(Map<String, dynamic> json) {
    // Implementation
  }
}

// WebSocket service integration with file processing
class FileProcessingWebSocketClient {
  final WebSocketService _webSocketService;

  // Subscribe to status updates for a specific file processing job
  Stream<FileProcessingPayload> subscribeToStatusUpdates(String jobId) {
    return _webSocketService.subscribe<FileProcessingPayload>(
      'file_processing_$jobId',
      (data) => FileProcessingPayload.fromJson(data),
    );
  }
}

// Factory for creating the appropriate client implementation
class FileProcessingClientFactory {
  static FileProcessingClient create({
    required LicenseManagerService licenseManager,
  }) {
    // Create gRPC client
    final channel = ClientChannel(
      ApiConfig.grpcHost,
      port: ApiConfig.grpcPort,
      options: ChannelOptions(
        credentials: licenseManager.apiKey != null
            ? ChannelCredentials.secure()
            : ChannelCredentials.insecure(),
      ),
    );

    final client = FileProcessingServiceClient(channel);

    // Create WebSocket service for real-time updates
    final webSocketService = WebSocketService(
      apiKey: licenseManager.apiKey,
    );

    // Return the gRPC implementation
    return GrpcFileProcessingClient(
      client: client,
      webSocketService: webSocketService,
      licenseManager: licenseManager,
    );
  }
}
```

### 5. Unified File Processing Client

```dart
// Factory for creating the appropriate client implementation
class FileProcessingClientFactory {
  static FileProcessingClient create({
    required LicenseManagerService licenseManager,
  }) {
    // Create gRPC client
    final channel = ClientChannel(
      ApiConfig.grpcHost,
      port: ApiConfig.grpcPort,
      options: ChannelOptions(
        credentials: licenseManager.apiKey != null
            ? ChannelCredentials.secure()
            : ChannelCredentials.insecure(),
      ),
    );

    final client = FileProcessingServiceClient(channel);

    // Create WebSocket service for real-time updates
    final webSocketService = WebSocketService(
      apiKey: licenseManager.apiKey,
    );

    // Return the gRPC implementation
    return GrpcFileProcessingClient(
      client: client,
      webSocketService: webSocketService,
      licenseManager: licenseManager,
    );
  }
}
```

## Benefits of the New Architecture

### 1. Improved Efficiency

- **Streaming Uploads**: Chunked file transfers reduce memory usage
- **Binary Serialization**: Smaller payloads compared to JSON
- **Real-time Updates**: Push notifications eliminate polling
- **Connection Reuse**: gRPC connections are maintained for multiple requests

### 2. Enhanced Reliability

- **Type Safety**: Protocol Buffers provide strong typing and validation
- **Consistent Field Naming**: Formal contract between client and server
- **Versioned APIs**: Clear evolution path for services
- **Unified Error Handling**: Standardized error codes and messages

### 3. Better User Experience

- **Real-time Progress**: Immediate feedback on file processing
- **Faster Uploads**: Optimized for large files
- **Responsive UI**: Non-blocking operations with progress updates
- **Graceful Error Recovery**: Automatic retries and reconnection

## Implementation Roadmap

Since we're adopting a clean-slate approach without backward compatibility concerns, we can implement the new architecture in a more streamlined manner:

### Phase 1: Foundation (2-3 Weeks)

1. **Week 1**: Define Protocol Buffer schemas for all services

   - File processing service definitions
   - Authentication and license management
   - Prompt management services

2. **Week 2**: Set up gRPC server infrastructure

   - Configure Go gRPC server with TLS
   - Implement authentication middleware
   - Set up WebSocket server for real-time updates

3. **Week 3**: Implement client-side infrastructure
   - Create Dart gRPC client infrastructure
   - Implement WebSocket client with reconnection logic
   - Define protocol-agnostic service interfaces

### Phase 2: Core Implementation (3-4 Weeks)

1. **Week 1-2**: Implement File Processing Services

   - File upload with streaming support
   - Processing status and results retrieval
   - WebSocket integration for real-time updates

2. **Week 3-4**: Implement Remaining Services
   - Authentication and license management
   - Prompt management (CRUD operations)
   - Popular prompts with real-time updates

### Phase 3: Integration and Testing (2-3 Weeks)

1. **Week 1**: Integrate with UI Components

   - Update SharedContentHandler to use new services
   - Integrate with ContentProcessingService
   - Update UI components for real-time updates

2. **Week 2-3**: Testing and Optimization
   - Performance testing for large file uploads
   - Stress testing for concurrent connections
   - Mobile-specific optimizations

## Specific Use Cases

### File Processing with Real-time Updates

**New Implementation:**

```dart
// 1. Upload file with gRPC streaming
Future<void> processFile(File file, BuildContext context) async {
  final client = FileProcessingClientFactory.create(
    licenseManager: LicenseManagerService(),
  );

  // Show progress dialog
  showDialog(
    context: context,
    barrierDismissible: false,
    builder: (context) => ProcessingProgressDialog(),
  );

  try {
    // Start file upload with streaming
    final response = await client.uploadFile(
      file,
      metadata: {
        'source': 'user_upload',
        'app_version': AppConfig.version,
      },
      licenseTier: LicenseManagerService().currentTier,
      onProgress: (progress) {
        // Update upload progress in UI
        ProcessingProgressProvider.of(context).updateUploadProgress(progress);
      },
    );

    // 2. Subscribe to real-time status updates via WebSocket
    final statusStream = client.subscribeToStatusUpdates(response.id);

    statusStream.listen(
      (status) {
        // Update processing progress in UI
        ProcessingProgressProvider.of(context).updateProcessingStatus(status);

        // When complete, get results and close dialog
        if (status.isCompleted) {
          client.getResults(response.id).then((results) {
            Navigator.of(context).pop(); // Close progress dialog
            _showResults(context, results);
          });
        }
      },
      onError: (error) {
        Navigator.of(context).pop(); // Close progress dialog
        _showErrorDialog(context, error);
      },
    );
  } catch (e) {
    Navigator.of(context).pop(); // Close progress dialog
    _showErrorDialog(context, e);
  }
}
```

### Shared Content Handler Integration

```dart
class SharedContentHandler {
  // ... existing code ...

  // Process a task and update its status
  Future<void> _processTask(SharedContentTask task) async {
    try {
      _processingStatusController.add(SharedProcessingStatus.started);

      // Create file processing client
      final client = FileProcessingClientFactory.create(
        licenseManager: _licenseManagerService,
      );

      // Upload file with streaming
      final file = File(task.filePath);
      final response = await client.uploadFile(
        file,
        metadata: {
          'source': 'shared_content',
          'file_name': task.fileName,
          'mime_type': task.mimeType,
        },
        licenseTier: _getCurrentLicenseTier(),
      );

      // Subscribe to status updates
      final statusStream = client.subscribeToStatusUpdates(response.id);

      // Update task status based on WebSocket updates
      statusStream.listen(
        (status) async {
          // Update task status in database
          await _taskManager.updateTask(
            task.id,
            (t) => t.copyWith(
              status: _mapStatusToTaskStatus(status.status),
              progress: status.progress,
              processingId: response.id,
              updatedAt: DateTime.now(),
            ),
          );

          // When complete, get results
          if (status.isCompleted) {
            final result = await client.getResults(response.id);
            final contentInfo = _createContentInfo(result, task);
            _sharedContentController.add(contentInfo);
          }
        },
        onError: (error) {
          _handleTaskError(task, error, null);
        },
      );
    } catch (e, stack) {
      _handleTaskError(task, e, stack);
    }
  }
}
```

### Popular Prompts with Real-time Updates

```dart
class PromptUsageService {
  // ... existing code ...

  // Initialize WebSocket subscription for popular prompts
  void _subscribeToPopularPrompts() {
    try {
      // Create WebSocket service
      final webSocketService = WebSocketService(
        apiKey: _licenseManager.apiKey,
      );

      // Subscribe to popular prompts topic
      _popularPromptsSubscription = webSocketService
          .subscribe<List<dynamic>>('popular_prompts', (data) => data)
          .listen(_handlePopularPromptsUpdate);

      appLog.debug('Subscribed to popular prompts via WebSocket', name: _logName);
    } catch (e, stackTrace) {
      appLog.error('Error subscribing to popular prompts',
          name: _logName, error: e, stackTrace: stackTrace);
    }
  }
}
```

## Technical Considerations

### Authentication and Security

- **API Key Authentication**: Use the existing API key infrastructure for both gRPC and WebSocket
- **TLS Encryption**: All communication will be encrypted using TLS
- **Topic Authorization**: Implement permission checks for WebSocket topic subscriptions
- **Rate Limiting**: Apply rate limits to prevent abuse of WebSocket connections

### Mobile Optimization

- **Connection Management**: Properly handle app lifecycle events (background/foreground)
- **Battery Optimization**: Minimize WebSocket keepalive frequency when app is in background
- **Bandwidth Conservation**: Implement compression for all gRPC and WebSocket payloads
- **Offline Support**: Queue operations when offline and sync when connection is restored

### Error Handling and Recovery

- **Standardized Error Codes**: Define consistent error codes across all services
- **Automatic Reconnection**: Implement exponential backoff for connection retries
- **Graceful Degradation**: Fall back to simpler functionality when on poor networks
- **Detailed Logging**: Comprehensive logging for debugging and troubleshooting

## Conclusion

By adopting a clean-slate approach with gRPC and WebSockets, we can create a more efficient,
reliable, and user-friendly communication architecture for the PROMZ application. This approach
eliminates the complexity of maintaining backward compatibility with REST and allows us to focus
on delivering the best possible experience.

The combination of gRPC for core operations and WebSockets for real-time updates provides an optimal
balance of efficiency and responsiveness. The strongly typed nature of Protocol Buffers ensures
consistent field naming and validation, eliminating a common source of bugs in REST APIs.

For file processing specifically, this architecture offers significant improvements:

1. **Streaming uploads** reduce memory usage and improve reliability for large files
2. **Real-time status updates** provide immediate feedback to users
3. **Centralized implementation** eliminates duplicate logic across services
4. **Strong typing** prevents field mismatches between client and server

This modernized architecture sets a solid foundation for future features while addressing the
current limitations of the REST-based approach.
