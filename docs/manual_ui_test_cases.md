# Manual UI Test Cases for Promz Mobile App

**Version:** 1.1
**Date:** 2025-05-15

## Document History

| Version | Date       | Description                |
| ------- | ---------- | -------------------------- |
| 1.1     | 2025-05-15 | Initial set of test cases. |

## Overview

This document outlines manual UI test cases for the Promz mobile application.
These test cases are designed to verify key UI functionalities and user
experiences. They are based on the functional areas described in the
`mobile-testing-strategy.md` and aim to ensure a high-quality user interface.

## Test Case Format

* **Test Case ID:** Unique identifier (e.g., UI_TC_XXX)
* **Feature:** The application feature or module being tested.
* **Test Title:** A concise description of the test objective.
* **Preconditions:** Any conditions that must be met before executing the test.
* **Steps:** Significant steps to perform the test.
* **Expected Results:** The expected outcome after executing the steps.
* **Priority:** High, Medium, Low.

---

## 1. Authentication and User Management

**Test Case ID:** UI_TC_001
**Feature:** Authentication
**Test Title:** Successful User Registration with Email/Password
**Preconditions:** User is on the registration screen. App has network connectivity.

**Steps:**

1. Enter a valid, unique email address.
2. Enter a strong password in the password field.
3. Confirm the password in the confirmation field.
4. Accept terms and conditions (if applicable).
5. Tap the "Register" or "Sign Up" button.

**Expected Results:**

* User account is created successfully.
* User is navigated to the login screen or directly into the app (e.g., to an onboarding flow or
  main dashboard).
* A confirmation (e.g., email verification request) might be initiated.
**Priority:** High

---

**Test Case ID:** UI_TC_002
**Feature:** Authentication
**Test Title:** Successful User Login with Email/Password
**Preconditions:** User has a registered account. User is on the login screen. App has network
connectivity.

**Steps:**

1. Enter the registered email address.
2. Enter the correct password.
3. Tap the "Login" or "Sign In" button.

**Expected Results:**

* User is successfully authenticated.
* User is navigated to the main part of the application (e.g., dashboard or prompt list).
* User-specific data is loaded and displayed correctly.
**Priority:** High

---

**Test Case ID:** UI_TC_003
**Feature:** Authentication
**Test Title:** Successful OAuth Login (e.g., Google)
**Preconditions:** User is on the login/registration screen. App has network connectivity.
Device has Google services configured (if testing Google OAuth).

**Steps:**

1. Tap the "Sign in with Google" (or equivalent) button.
2. If prompted, select a Google account.
3. Complete the Google authentication flow (e.g., enter password if required by Google).

**Expected Results:**

* User is successfully authenticated via OAuth.
* User is navigated into the application.
* A new Promz account is created (if first time) or linked to an existing one.

**Priority:** High

---

**Test Case ID:** UI_TC_004
**Feature:** User Profile Management
**Test Title:** Update User Profile Information
**Preconditions:** User is logged in.

**Steps:**

1. Navigate to the User Profile or Account Settings screen.
2. Tap an "Edit Profile" button or directly on editable fields (e.g., name, avatar).
3. Modify one or more profile fields with valid data.
4. Tap the "Save" or "Update" button.

**Expected Results:**

* Profile information is updated successfully.
* The updated information is reflected on the profile screen and other relevant parts of the UI.
* A confirmation message might be displayed.

**Priority:** High

---

## 2. Prompt Management

**Test Case ID:** UI_TC_005
**Feature:** Prompt Management
**Test Title:** Create a New Prompt with Basic Content
**Preconditions:** User is logged in.

**Steps:**

1. Navigate to the main prompt list screen or prompt creation interface.
2. Tap the "Create New Prompt" button (or equivalent UI element, e.g., a '+' icon).
3. Enter a unique and descriptive title for the prompt.
4. Enter the main content/body of the prompt in the designated text area.
5. Optionally, assign a category or tags if available.
6. Tap the "Save" or "Create" button.

**Expected Results:**

* The new prompt is successfully created and saved.
* The user is either navigated to view the newly created prompt or back to the prompt list where the
  new prompt is visible.
* The prompt displays the entered title and content correctly.

**Priority:** High

---

**Test Case ID:** UI_TC_006
**Feature:** Prompt Management
**Test Title:** Edit an Existing Prompt
**Preconditions:** User is logged in. At least one prompt exists.

**Steps:**

1. Navigate to the prompt list and select an existing prompt to edit.
2. Open the selected prompt for viewing.
3. Tap an "Edit" button or icon.
4. Modify the prompt's title, content, or other attributes (e.g., category).
5. Tap the "Save" or "Update" button.

**Expected Results:**

* The changes to the prompt are saved successfully.
* The updated prompt details are reflected in the prompt view and in the prompt list.

**Priority:** High

---

**Test Case ID:** UI_TC_007
**Feature:** Prompt Management
**Test Title:** Delete a Prompt
**Preconditions:** User is logged in. At least one prompt exists.

**Steps:**

1. Navigate to the prompt list.
2. Select a prompt to delete (e.g., long-press, swipe, or open and find delete option).
3. Tap the "Delete" button/icon.
4. Confirm the deletion if a confirmation dialog appears.

**Expected Results:**

* The prompt is successfully deleted.
* The prompt is removed from the prompt list.
* Associated data (if any directly tied and meant to be deleted) is handled correctly.

**Priority:** High

---

**Test Case ID:** UI_TC_008
**Feature:** Prompt Management
**Test Title:** Categorize/Organize a Prompt
**Preconditions:** User is logged in. At least one prompt exists. Categories/folders feature is available.

**Steps:**

1. Create or edit a prompt.
2. Locate the UI element for assigning a category/tag or moving to a folder.
3. Select an existing category/tag or create a new one.
4. Save the prompt.
5. Navigate to the prompt list and filter/sort by the assigned category/tag, or view within the folder.

**Expected Results:**

* The prompt is correctly associated with the selected category/tag/folder.
* The prompt can be found when filtering or browsing by that category/tag/folder.
* The UI clearly indicates the prompt's organization.
**Priority:** High

---

## 3. Source Input Functionality

**Test Case ID:** UI_TC_021
**Feature:** Source Input
**Test Title:** Add Text from Clipboard as Source
**Preconditions:** User is logged in. App has clipboard access permission. Text content is copied to the clipboard.

**Steps:**

1. Navigate to the home screen with the source input section.
2. Tap the "Paste from Clipboard" button or equivalent UI element.
3. Observe the clipboard content being added as a source.
4. Verify the source card displays the text content appropriately.
**Expected Results:**

* The clipboard content is successfully added as a source.
* A source card is created with the text content.
* The source is visually distinguishable as a clipboard source.
* The source can be selected/deselected.
**Priority:** High

---

**Test Case ID:** UI_TC_022
**Feature:** Source Input
**Test Title:** Add YouTube Link as Source
**Preconditions:** User is logged in. App has network connectivity.

**Steps:**

1. Navigate to the home screen with the source input section.
2. Enter or paste a valid YouTube URL (e.g., `https://www.youtube.com/watch?v=dQw4w9WgXcQ`) in the input field.
3. Observe the system processing the YouTube link.
4. Verify the YouTube source card is created with appropriate metadata (thumbnail, title, channel).

**Expected Results:**

* The YouTube link is recognized and processed correctly.
* A YouTube-specific source card is displayed with video thumbnail and metadata.
* The source card has appropriate styling for a video source.
* The source can be selected/deselected.
**Priority:** High

---

**Test Case ID:** UI_TC_023
**Feature:** Source Input
**Test Title:** Delete a Source from Input Section
**Preconditions:** User is logged in. At least one source has been added.

**Steps:**

1. Navigate to the home screen with at least one source displayed in the source list.
2. Locate the delete/remove button on a source card.
3. Tap the delete/remove button.
4. If a confirmation dialog appears, confirm the deletion.

**Expected Results:**

* The source is removed from the source list.
* The UI updates smoothly to reflect the removal.
* No errors or UI glitches occur during the removal process.
**Priority:** High

---

**Test Case ID:** UI_TC_024
**Feature:** Source Input
**Test Title:** Process Multiple Sources of Different Types
**Preconditions:** User is logged in. App has network connectivity and clipboard access.

**Steps:**

1. Add a text source via clipboard or manual entry.
2. Add a YouTube link source.
3. Add another text source with different content.
4. Observe how the system handles multiple sources of different types.
5. Verify all sources are displayed correctly in the source list.

**Expected Results:**

* All sources are processed and displayed correctly.
* Each source maintains its appropriate type-specific styling and information.
* The source list accommodates multiple sources with proper spacing and layout.
* All sources can be individually selected/deselected.
**Priority:** High

---

**Test Case ID:** UI_TC_025
**Feature:** Source Input
**Test Title:** Receive Prompt Suggestions Based on Source Content
**Preconditions:** User is logged in. App has network connectivity. Source content
that should trigger suggestions is available.

**Steps:**

1. Add a source with content that should trigger prompt suggestions (e.g., news
   article, specific topic text).
2. Wait for the system to process the source and generate suggestions.
3. Observe the suggested prompts section.
4. Tap on one of the suggested prompts.

**Expected Results:**

* Relevant prompt suggestions appear after source processing.
* Suggestions are displayed in a clear, accessible manner.
* Tapping a suggestion selects it for use.
* The selected prompt is appropriately applied or prepared for execution.
**Priority:** High

---

## 4. LLM Integration

**Test Case ID:** UI_TC_009
**Feature:** LLM Integration
**Test Title:** Execute a Prompt with Default LLM Provider
**Preconditions:** User is logged in. At least one prompt exists. LLM integration
is configured. App has network connectivity.
**Steps:**

1. Open an existing prompt or create a new one.
2. Ensure any required template variables are filled (if applicable).
3. Tap the "Execute," "Run," or "Send to LLM" button.

**Expected Results:**

* The prompt is sent to the default LLM provider.
* A loading indicator or progress feedback is shown in the UI.
* The LLM response is received and displayed in the designated area.
* The UI remains responsive during execution (e.g., ability to cancel if implemented).
**Priority:** High

---

**Test Case ID:** UI_TC_010
**Feature:** LLM Integration
**Test Title:** View and Interact with LLM Response
**Preconditions:** A prompt has been successfully executed and a response received.

**Steps:**

1. Observe the displayed LLM response.
2. Verify that formatting (e.g., paragraphs, lists, code blocks if applicable) is rendered correctly.
3. Attempt to copy the response text using a "Copy" button or standard text selection.
4. If applicable, use any other interaction options for the response (e.g., share, save, regenerate).

**Expected Results:**

* The LLM response is clearly legible and well-formatted.
* Copy functionality works as expected.
* Other interactions (if present) function correctly.
**Priority:** High

---

**Test Case ID:** UI_TC_011
**Feature:** LLM Integration
**Test Title:** Switch LLM Provider for a Prompt (if UI allows)
**Preconditions:** User is logged in. Multiple LLM providers are configured and
accessible to the user.

**Steps:**

1. Open or create a prompt.
2. Locate the UI element for selecting/changing the LLM provider (e.g., a dropdown
   menu).
3. Select a different LLM provider from the available options.
4. Execute the prompt.

**Expected Results:**

* The selected LLM provider is used for the prompt execution.
* The UI clearly indicates which LLM provider is currently active or was used.
* The response (if successful) is from the newly selected provider.

**Priority:** Medium (Depends on feature availability)

---

**Test Case ID:** UI_TC_026
**Feature:** LLM Integration
**Test Title:** View and Interact with Enhanced Provider Details
**Preconditions:** User is logged in. A prompt has been successfully executed with
an LLM provider.

**Steps:**

1. After prompt execution, observe the provider details section in the results view.
2. Verify that detailed provider information is displayed (e.g., "Gemini 1.5 Pro"
   instead of just "Google").
3. If applicable, interact with any provider-specific options or information links.
**Expected Results:**

* Detailed provider information is displayed, including model name/version.
* The information is formatted clearly and consistently.
* Any provider-specific links or options function correctly.
**Priority:** Medium

---

**Test Case ID:** UI_TC_027
**Feature:** LLM Integration
**Test Title:** Use Contextual Action Buttons in Results View
**Preconditions:** User is logged in. A prompt has been successfully executed.

**Steps:**

1. After prompt execution, observe the action buttons in the results view.
2. Test each available action button:
   * Copy button to copy the result text
   * Share button to share the result
   * Save to PDF button to save the result as a PDF
   * Save to Topics button (if available) to save to a topic
3. Verify each action functions as expected.

**Expected Results:**

* All action buttons are clearly visible and appropriately labeled.
* Each action functions correctly when tapped.
* Appropriate feedback is provided for each action (e.g., "Copied to clipboard").
* The UI remains responsive during all actions.
**Priority:** High

---

**Test Case ID:** UI_TC_028
**Feature:** LLM Integration
**Test Title:** Verify Source Card Integration in Results View
**Preconditions:** User is logged in. A prompt has been executed with source
material (e.g., YouTube video, news article).

**Steps:**

1. Add a source (YouTube link, news article, or text) and execute a prompt.
2. After execution, observe the results view.
3. Verify that the source card is displayed at the top of the results.
4. Check that the source card contains appropriate metadata and styling based on the source type.

**Expected Results:**

* The source card is displayed prominently at the top of the results view.
* The source card contains appropriate metadata (title, thumbnail, etc.) based on
  source type.
* The source card styling is consistent with the source type (YouTube, news, text).
* The source card is visually integrated with the results display.
**Priority:** High

---

## 5. Portfolio and Topics

**Test Case ID:** UI_TC_029
**Feature:** Portfolio/Topics
**Test Title:** Create a New Topic
**Preconditions:** User is logged in. User has access to the Portfolio feature.
**Steps:**

1. Navigate to the Portfolio section using the bottom navigation.
2. Tap the "+" or "Create new topic" button.
3. Enter a name for the new topic in the dialog.
4. Tap "Create" or equivalent to confirm.
**Expected Results:**

* A new topic is created successfully.
* The new topic appears in the topics list.
* The UI provides appropriate feedback on successful creation.
* The new topic can be selected and viewed.
**Priority:** High

---

**Test Case ID:** UI_TC_030
**Feature:** Portfolio/Topics
**Test Title:** Save an Execution Result to a Topic
**Preconditions:** User is logged in. At least one topic exists. A prompt has been
successfully executed.

**Steps:**

1. Execute a prompt and view the results.
2. Tap the "Save to Topics" or equivalent button in the results view.
3. Select a topic from the list of available topics.
4. Confirm the save action.

**Expected Results:**

* The execution result is saved to the selected topic.
* Appropriate feedback is provided confirming the save action.
* The saved result can be accessed from the topic view.

**Priority:** High

---

**Test Case ID:** UI_TC_031
**Feature:** Portfolio/Topics
**Test Title:** View and Navigate Topic Contents
**Preconditions:** User is logged in. At least one topic exists with saved content.

**Steps:**

1. Navigate to the Portfolio section.
2. Select a topic that contains saved content.
3. Browse through the saved items in the topic.
4. Tap on a saved item to view its details.
5. Use navigation controls to return to the topic list.
**Expected Results:**

* The topic contents are displayed in a clear, organized manner.
* Saved items show appropriate preview information.
* Tapping an item opens its detailed view correctly.
* Navigation between topic list, topic contents, and item details works smoothly.
**Priority:** High

---

## 6. Data Synchronization

**Test Case ID:** UI_TC_012
**Feature:** Data Synchronization
**Test Title:** Verify UI Update After Cloud Sync (e.g., New Prompt from Another Device)
**Preconditions:** User is logged in on two devices (or web and mobile). Cloud sync
is enabled.
**Steps:**

1. On Device A, create and save a new prompt. Ensure it syncs to the cloud.
2. On Device B (the device under test), ensure the app is running and connected.
3. Trigger a manual sync (if available) or wait for automatic sync.
4. Observe the prompt list on Device B.

**Expected Results:**

* The new prompt created on Device A appears in the prompt list on Device B without
  requiring an app restart.
* The UI updates smoothly to reflect the new data.
* No data corruption or duplication is observed.
**Priority:** High

---

**Test Case ID:** UI_TC_013
**Feature:** Data Synchronization
**Test Title:** Access and View Prompts in Offline Mode
**Preconditions:** User is logged in. Some prompts are cached locally.
**Steps:**

1. Enable airplane mode or disconnect the device from the network.
2. Open the Promz application.
3. Navigate to the prompt list.
4. Attempt to open and view an existing, locally cached prompt.

**Expected Results:**

* The application launches and functions for locally available data.
* The user can view the content of cached prompts.
* UI clearly indicates offline status (if designed to do so).
* Features requiring network connectivity are gracefully disabled or show appropriate messages.
**Priority:** High

**Test Case ID:** UI_TC_032
**Feature:** Data Synchronization
**Test Title:** Verify UI Feedback During Synchronization
**Preconditions:** User is logged in. App has network connectivity. Changes have
been made that require synchronization.

**Steps:**

1. Make a change that requires synchronization (e.g., create a new prompt, edit an
   existing one).
2. Observe the UI for synchronization indicators (e.g., progress indicators, status
   messages).
3. Wait for synchronization to complete.
4. Verify that the UI updates to reflect the synchronized state.

**Expected Results:**

* The UI clearly indicates when synchronization is in progress.
* Appropriate progress indicators or animations are displayed during
  synchronization.
* The UI updates to reflect the completed synchronization state.
* No UI elements are blocked or unresponsive during synchronization.
**Priority:** High

---

**Test Case ID:** UI_TC_033
**Feature:** Data Synchronization
**Test Title:** Verify Conflict Resolution UI
**Preconditions:** User is logged in. App has network connectivity. A potential
conflict situation has been created (e.g., same prompt edited on two devices).

**Steps:**

1. Create a conflict scenario (e.g., edit the same prompt on two devices without
   syncing).
2. Trigger synchronization on the test device.
3. Observe how the UI handles the conflict situation.
4. If prompted, make a conflict resolution choice.

**Expected Results:**

* The UI clearly indicates that a conflict has been detected.
* The conflict resolution interface is intuitive and provides clear options.
* The user's conflict resolution choice is correctly applied.
* After resolution, the system returns to a stable, synchronized state.
**Priority:** High

---

## 7. Discover Feature

**Test Case ID:** UI_TC_034
**Feature:** Discover
**Test Title:** Browse and Navigate Prompt Categories
**Preconditions:** User is logged in. App has network connectivity. The Discover
feature is available.
**Steps:**

1. Navigate to the Discover section using the bottom navigation.
2. Observe the category list or browsing interface.
3. Tap on a category to view its contents.
4. Navigate back to the main Discover view.
5. Select a different category.

**Expected Results:**

* Categories are displayed in a clear, organized manner.
* Tapping a category successfully navigates to its contents.
* Category contents are displayed appropriately.
* Navigation between categories and back to the main view works smoothly.
**Priority:** High

---

**Test Case ID:** UI_TC_035
**Feature:** Discover
**Test Title:** Preview and Select a Prompt from Discover
**Preconditions:** User is logged in. App has network connectivity. The Discover
feature is available with prompts.

**Steps:**

1. Navigate to the Discover section.
2. Browse to find a prompt of interest.
3. Tap on the prompt to view its details or preview.
4. Select the prompt for use (e.g., "Use This Prompt" button).

**Expected Results:**

* Prompt preview displays relevant information (title, description, category).
* Preview interface is clear and well-organized.
* Selecting the prompt successfully adds it to the user's workspace or activates it for use.
* Appropriate feedback is provided when the prompt is selected.
**Priority:** High

---

**Test Case ID:** UI_TC_036
**Feature:** Discover
**Test Title:** Search for Prompts in Discover
**Preconditions:** User is logged in. App has network connectivity. The Discover
feature includes search functionality.

**Steps:**

1. Navigate to the Discover section.
2. Locate and tap the search field or icon.
3. Enter a search term relevant to available prompts.
4. Observe the search results.
5. Tap on a search result to view its details.

**Expected Results:**

* Search interface is easily accessible and usable.
* Search results appear as the user types or after submission.
* Results are relevant to the search term.
* Tapping a result successfully navigates to its details.
* The UI provides appropriate feedback for no results or errors.
**Priority:** High

---

## 8. File System Operations

**Test Case ID:** UI_TC_014
**Feature:** File System Operations
**Test Title:** Import Prompts from a Supported File Format (if feature exists)
**Preconditions:** User is logged in. A valid prompt file (e.g., JSON, TXT) is
available on the device.
**Steps:**

1. Navigate to the import feature within the app (e.g., Settings > Import).
2. Tap the "Import from File" button.
3. Use the device's file picker to select the valid prompt file.
4. Confirm the import.

**Expected Results:**

* Prompts from the file are successfully imported into the app.
* Imported prompts appear in the main prompt list.
* A success message is displayed, along with any errors if parts of the file were invalid.

**Priority:** Medium (Depends on feature availability)

---

**Test Case ID:** UI_TC_015
**Feature:** File System Operations
**Test Title:** Export a Single Prompt or All Prompts (e.g., as PDF or Text)
**Preconditions:** User is logged in. At least one prompt exists.

**Steps:**

1. Select a prompt to export or navigate to a "Export All" option.
2. Choose an export format (e.g., PDF, TXT) if multiple options are available.
3. Initiate the export process.
4. If prompted, choose a location to save the file or a sharing target.

**Expected Results:**

* The prompt(s) are successfully exported in the chosen format.
* The exported file is saved to the device or shared as expected.
* The content and formatting in the exported file are correct.

**Priority:** High

---

**Test Case ID:** UI_TC_016
**Feature:** File System Operations / Sharing
**Test Title:** Share a Prompt using Native Share Sheet
**Preconditions:** User is logged in. At least one prompt exists.

**Steps:**

1. Open a prompt.
2. Tap the "Share" icon/button.
3. Verify the native OS share sheet appears.
4. Select a target app (e.g., email, messaging app) from the share sheet.
5. Complete the sharing process in the target app.

**Expected Results:**

* The native share sheet is displayed with relevant sharing options.
* The prompt content (or a link to it, depending on design) is correctly passed to the selected target app.
* Sharing completes successfully.

**Priority:** High

---

**Test Case ID:** UI_TC_037
**Feature:** File System Operations
**Test Title:** Save Execution Results as PDF
**Preconditions:** User is logged in. A prompt has been successfully executed with
results.

**Steps:**

1. After prompt execution, locate and tap the "Save as PDF" or equivalent button in
   the results view.
2. If prompted, choose a location to save the file or confirm the save action.
3. Navigate to the saved location (if applicable) and attempt to open the PDF file.

**Expected Results:**

* The PDF generation process starts successfully.
* Appropriate feedback is provided during and after the PDF generation.
* The generated PDF contains the execution results in a well-formatted manner.
* If applicable, the PDF includes source information (e.g., YouTube video details,
  news article).
* The PDF can be opened and viewed correctly.

**Priority:** High

---

**Test Case ID:** UI_TC_038
**Feature:** File System Operations
**Test Title:** Handle Large File Imports
**Preconditions:** User is logged in. A large file (e.g., large text document, PDF)
is available for import.

**Steps:**

1. Navigate to the import feature or area where files can be added as sources.
2. Select and attempt to import a large file.
3. Observe the system's handling of the large file during import.
4. Verify the file is processed and displayed correctly after import.

**Expected Results:**

* The system handles the large file import without crashing or freezing.
* Appropriate progress indicators are displayed during the import process.
* The imported file is processed correctly and displayed as a source.
* The UI remains responsive throughout the import process.
**Priority:** Medium

---

## 9. Account Management

**Test Case ID:** UI_TC_039
**Feature:** Account Management
**Test Title:** View and Edit Account Settings
**Preconditions:** User is logged in.
**Steps:**

1. Navigate to the Account section using the bottom navigation.
2. Locate and tap on the Settings or Account Settings option.
3. Review the available settings and options.
4. Change a setting (e.g., notification preferences, display options).
5. Save the changes and navigate away from the settings screen.
6. Return to the settings screen to verify the changes persisted.
**Expected Results:**

* Account settings are displayed in a clear, organized manner.
* Settings can be changed and saved successfully.
* Changes persist when returning to the settings screen.
* The UI provides appropriate feedback when settings are changed.
**Priority:** High

---

**Test Case ID:** UI_TC_040
**Feature:** Account Management
**Test Title:** View Subscription Information
**Preconditions:** User is logged in. User has an active subscription or free tier
account.

**Steps:**

1. Navigate to the Account section.
2. Locate and tap on the Subscription or Plan information option.
3. Review the subscription details displayed.
4. If applicable, tap on options to view more details or manage the subscription.
**Expected Results:**

* Subscription information is clearly displayed with relevant details (plan type,
  renewal date, features).
* The information is accurate and up-to-date.
* If applicable, options to manage the subscription are accessible and functional.
* The UI adapts appropriately based on the user's subscription status (free vs.
  paid tiers).

**Priority:** High

---

## 10. UI/UX and General Responsiveness

**Test Case ID:** UI_TC_017
**Feature:** UI/UX
**Test Title:** Verify Responsive Layout on Different Orientations (Portrait/Landscape)
**Preconditions:** User is logged in. Device supports orientation changes.
**Steps:**

1. Navigate to various key screens of the application (e.g., prompt list, prompt view, settings).
2. Rotate the device from portrait to landscape mode.
3. Rotate the device back to portrait mode.
**Expected Results:**

* The UI layout adapts correctly to both orientations without element overlaps,
  truncation, or usability issues.
* All interactive elements remain accessible and functional.
* *Vibe Check:* Observe for any UI glitches, element jitter, or sluggishness during
  orientation changes. The transition should feel smooth.
**Priority:** High

---

**Test Case ID:** UI_TC_018
**Feature:** UI/UX
**Test Title:** Change Application Theme (e.g., Light/Dark Mode)
**Preconditions:** User is logged in. Application supports multiple themes.

**Steps:**

1. Navigate to the app's settings or display options.
2. Locate the theme selection setting.
3. Switch from the current theme to another available theme (e.g., Light to Dark, or vice-versa).
4. Navigate through several key screens.
**Expected Results:**

* All UI elements (text, backgrounds, icons, controls) correctly adapt to the selected theme.
* Readability and contrast are maintained in the new theme.
* No elements remain stuck in the previous theme or become invisible/unusable.
* *Vibe Check:* The theme change should apply consistently and instantly across
  the app.

**Priority:** High

---

**Test Case ID:** UI_TC_019
**Feature:** UI/UX - Accessibility
**Test Title:** Basic Accessibility Check (Tap Targets, Font Readability)
**Preconditions:** User is logged in.

**Steps:**

1. Navigate through various screens.
2. Identify common interactive elements (buttons, list items, input fields, icons).
3. Assess if tap targets are sufficiently large and spaced to be easily tapped
   without accidental presses.
4. Assess if default font sizes are legible. If font scaling is supported via OS
   settings, check if the app UI responds gracefully.
**Expected Results:**

* Tap targets meet general accessibility guidelines (e.g., at least 44x44dp or
  equivalent).
* Text is clear, readable, and has sufficient contrast with its background.
* The app is generally usable with OS-level accessibility font size adjustments
  (if supported).
* *Vibe Check:* Beyond compliance, assess if the app *feels* easy to interact with
  for users who might have minor visual or motor impairments.

**Priority:** High

---

**Test Case ID:** UI_TC_020
**Feature:** UI/UX - Navigation
**Test Title:** Navigation Consistency and Intuition
**Preconditions:** User is logged in.

**Steps:**

1. Navigate through various sections of the app using primary navigation methods
   (e.g., bottom tabs, hamburger menu, back buttons).
2. Perform common multi-step flows (e.g., open prompt -> edit -> save -> back to
   list).
3. Use the system back button/gesture.

**Expected Results:**

* Navigation elements (menus, tabs, buttons) are consistently placed and behave
  predictably across the app.
* The back button (in-app and system) takes the user to the expected previous
  screen or state.
* There are no dead ends or confusing navigation loops.
* *Vibe Check:* The overall navigation should feel intuitive, fluid, and align with
  platform conventions. Users should not feel lost or surprised by navigation
  behavior.

**Priority:** High

---

**Test Case ID:** UI_TC_041
**Feature:** UI/UX - Animation and Transitions
**Test Title:** Verify Loading States and Animations
**Preconditions:** User is logged in. App has network connectivity.

**Steps:**

1. Perform actions that trigger loading states (e.g., executing a prompt, loading source metadata).
2. Observe loading indicators, shimmer effects, and transitions.
3. Wait for loading to complete and observe the transition to the loaded state.
4. Repeat with different features that have loading states.

**Expected Results:**

* Loading states are clearly indicated with appropriate animations (spinners, progress bars, shimmer effects).
* Transitions between loading and loaded states are smooth and visually pleasing.
* Loading indicators provide appropriate feedback on the progress of operations.
* The UI remains responsive during loading states.
* *Vibe Check:* Loading feels polished and professional, not jarring or disruptive to the user experience.

**Priority:** Medium

---

**Test Case ID:** UI_TC_042
**Feature:** UI/UX - Keyboard Interaction
**Test Title:** Verify Keyboard Behavior and Text Input
**Preconditions:** User is logged in.

**Steps:**

1. Navigate to screens with text input fields (e.g., source input, prompt creation).
2. Tap on text fields to activate the keyboard.
3. Test typing, text selection, copy/paste, and cursor movement.
4. Test keyboard dismissal (tapping outside, using done/return buttons).
5. If applicable, test keyboard shortcuts or special input methods.

**Expected Results:**

* Keyboard appears appropriately when text fields are focused.
* Text input, selection, and editing work as expected.
* Keyboard can be dismissed using standard methods.
* The UI adjusts appropriately when the keyboard appears (no important elements hidden).
* *Vibe Check:* Text input feels natural and efficient, without frustrating behaviors or limitations.

**Priority:** High

---

## 11. Error Handling and Edge Cases

**Test Case ID:** UI_TC_043
**Feature:** Error Handling
**Test Title:** Verify Network Error Handling
**Preconditions:** User is logged in. Network connectivity can be controlled.
**Steps:**

1. Disable network connectivity (airplane mode or similar).
2. Attempt operations that require network access (e.g., execute prompt, sync data).
3. Observe error messages and UI feedback.
4. Re-enable network connectivity and retry the operation.

**Expected Results:**

* Clear, user-friendly error messages are displayed for network failures.
* The UI remains responsive and usable despite network errors.
* No crashes or unexpected behaviors occur when network operations fail.
* Operations can be successfully retried when connectivity is restored.
* *Vibe Check:* Error handling feels informative and helpful rather than frustrating.

**Priority:** High

---

**Test Case ID:** UI_TC_044
**Feature:** Error Handling
**Test Title:** Verify LLM Execution Error Handling
**Preconditions:** User is logged in. App has network connectivity.

**Steps:**

1. Create a scenario likely to cause an LLM execution error (e.g., very large input, invalid prompt format).
2. Attempt to execute the prompt.
3. Observe how the error is presented in the UI.
4. Attempt to recover from the error (e.g., modify input, retry).
**Expected Results:**

* LLM execution errors are clearly communicated with specific, actionable information.
* The error UI is visually distinct but not alarming or confusing.
* Recovery options (retry, modify, cancel) are clearly presented.
* The system returns to a stable state after the error.
* *Vibe Check:* Errors feel like helpful guidance rather than dead ends or system failures.

**Priority:** High

---

**Test Case ID:** UI_TC_045
**Feature:** Error Handling
**Test Title:** Handle Input Validation and Feedback
**Preconditions:** User is logged in.

**Steps:**

1. Attempt to submit forms or inputs with invalid data (e.g., empty required fields, invalid formats).
2. Observe validation error messages and UI feedback.
3. Correct the errors and resubmit.
4. Test boundary cases (e.g., maximum character limits, special characters).

**Expected Results:**

* Input validation errors are clearly indicated with specific, helpful messages.
* Error messages appear in proximity to the problematic fields.
* The system prevents submission of invalid data.
* Correcting errors and resubmitting works as expected.
* *Vibe Check:* Validation feels helpful and guiding rather than punitive or confusing.

**Priority:** High

---

## Conclusion

This document provides a comprehensive set of manual UI test cases for the Promz mobile application, covering key functional areas and user interactions. These test cases are designed to verify both the technical functionality and the overall user experience of the application.

### Test Coverage Summary

The test cases cover the following key areas:

1. Authentication and User Management (4 test cases)
2. Prompt Management (4 test cases)
3. Source Input Functionality (5 test cases)
4. LLM Integration (7 test cases)
5. Portfolio and Topics (3 test cases)
6. Data Synchronization (4 test cases)
7. Discover Feature (3 test cases)
8. File System Operations (5 test cases)
9. Account Management (2 test cases)
10. UI/UX and General Responsiveness (5 test cases)
11. Error Handling and Edge Cases (3 test cases)

### Test Execution Guidelines

When executing these test cases, testers should:

1. Follow the preconditions and steps precisely
2. Document any deviations from expected results
3. Pay special attention to "Vibe Check" aspects that assess the overall feel and usability
4. Test across multiple device types and screen sizes when possible
5. Consider accessibility implications during testing

### Maintenance

This test case document should be updated when:

1. New features are added to the application
2. Existing features are significantly modified
3. New edge cases or user scenarios are identified
4. User feedback indicates areas needing additional testing focus

Regular reviews of this document should be conducted to ensure it remains aligned with the current state of the application and continues to provide valuable testing guidance.
