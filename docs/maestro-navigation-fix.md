# Maestro Navigation Bar Test Fix

## Problem Summary

Maestro UI tests were failing when trying to assert visibility of bottom navigation bar elements (<PERSON>, Discover, Portfolio, Account, About) in the Promz Flutter app.

**Error**: `Assertion is false: "Home" is visible`

## Root Cause Analysis

### Investigation Process

1. **Initial Hypothesis**: Suspected conditional label behavior based on screen width
2. **UI Hierarchy Analysis**: Used `maestro hierarchy` to examine actual accessibility tree
3. **Discovery**: Flutter's `NavigationBar` widget automatically adds semantic information

### Root Cause

Flutter's `NavigationBar` widget automatically appends "Tab X of Y" to each navigation destination's accessibility text for improved accessibility.

**Critical Discovery**: While `assertVisible` can find elements using the full accessibility text, `tapOn` commands **ALSO require the full accessibility text** - partial matches do not work for tapping navigation elements.

The actual accessibility text was:

- `"Home\nTab 1 of 5"` instead of just `"Home"`
- `"Discover\nTab 2 of 5"` instead of just `"Discover"`
- `"Portfolio\nTab 3 of 5"` instead of just `"Portfolio"`
- `"Account\nTab 4 of 5"` instead of just `"Account"`
- `"About\nTab 5 of 5"` instead of just `"About"`

## Solution

### Updated Test Assertions

Changed all `assertVisible` commands for navigation elements to use the full accessibility text:

```yaml
# Before (failing)
- assertVisible: "Home"

# After (working)
- assertVisible: "Home\nTab 1 of 5"
```

### Files Updated

1. **testing/maestro/android/common/app_launch.yaml**
   - Updated bottom navigation verification section
   - Added explanatory comments about Flutter NavigationBar behavior

2. **testing/maestro/android/authentication/01_pre_login_state.yaml**
   - Updated navigation assertions in Step 3

3. **testing/maestro/android/common/navigation_helpers.yaml**
   - Updated all navigation helper assertions

4. **testing/maestro/android/authentication/04_navigation_access.yaml**
   - Updated navigation assertions throughout

5. **testing/maestro/android/authentication/05_logout_flow.yaml**
   - Updated navigation assertions

6. **testing/maestro/android/authentication/03_post_login_state.yaml**
   - Updated navigation assertions

### Key Implementation Details

- **tapOn commands**: Still work with just the label (e.g., `"Home"`) as they support partial matches
- **assertVisible commands**: Require the full accessibility text including "Tab X of Y"
- **Timing**: Added `extendedWaitUntil` with 10-second timeout to ensure navigation is fully loaded

## Testing Results

✅ **app_launch.yaml**: All navigation assertions now pass  
✅ **Navigation verification**: All 5 tabs correctly detected  
✅ **Timing issues**: Resolved with proper wait conditions  

## Best Practices for Future Tests

### 1. Use Maestro Studio for UI Inspection

```bash
maestro studio
```

### 2. Check UI Hierarchy When Assertions Fail

```bash
maestro hierarchy
```

### 3. Understand Flutter Widget Accessibility Behavior

- `NavigationBar` adds "Tab X of Y" semantics
- `TextField` uses `hintText` for accessibility
- Custom widgets may need explicit `Semantics` wrappers

### 4. Test Assertion Patterns

```yaml
# For navigation tabs
- assertVisible: "Home\nTab 1 of 5"

# For tapping (MUST use full accessibility text)
- tapOn: "Home\nTab 1 of 5"

# For text content
- assertVisible: "What do you want to achieve today?"
```

## Alternative Solutions Considered

### 1. Custom Accessibility Identifiers

Could add explicit `identifier` properties to navigation destinations:

```dart
NavigationDestination(
  icon: Icon(Icons.home_outlined),
  selectedIcon: Icon(Icons.home),
  label: Strings.homeNavLabel,
  // Could add this for testing
  // But would require Flutter code changes
)
```

### 2. Semantic Wrappers

Could wrap navigation bar with custom semantics:

```dart
Semantics(
  identifier: 'bottom_navigation',
  child: NavigationBar(...)
)
```

### 3. Regex Patterns in Maestro

Maestro supports regex patterns, but exact text matching is more reliable for this use case.

## Conclusion

The fix successfully resolves the navigation bar visibility assertion failures by using the correct accessibility text format that Flutter's NavigationBar widget generates. This approach:

- ✅ Maintains Flutter's built-in accessibility features
- ✅ Requires no changes to application code
- ✅ Follows Maestro best practices for Flutter testing
- ✅ Provides reliable, deterministic test results

The solution demonstrates the importance of understanding how UI frameworks generate accessibility information and using proper debugging tools to investigate test failures.
