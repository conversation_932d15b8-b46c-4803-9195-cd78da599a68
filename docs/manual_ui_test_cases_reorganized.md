# Manual UI Test Cases for Promz Mobile App

**Version:** 1.2
**Date:** 2025-05-20
**Original Version:** 1.1 (2025-05-15)

## Table of Contents
- [Overview](#overview)
- [Test Execution Tracking](#test-execution-tracking)
- [Test Case Summary](#test-case-summary)
- [Test Cases by Feature Area](#test-cases-by-feature-area)
- [Test Execution Guidelines](#test-execution-guidelines)

## Overview

This document outlines manual UI test cases for the Promz mobile application.
These test cases are designed to verify key UI functionalities and user
experiences. They are based on the functional areas described in the
`mobile-testing-strategy.md` and aim to ensure a high-quality user interface.

## Test Execution Tracking

### Test Run Summary

| Run ID | Date       | Tester | Environment | Total Tests | Passed | Failed | Blocked | Pass Rate |
|--------|------------|--------|-------------|-------------|--------|--------|---------|-----------|
| R001   | YYYY-MM-DD |        |             | 45          | 0      | 0      | 0       | 0%        |
| R002   | YYYY-MM-DD |        |             | 45          | 0      | 0      | 0       | 0%        |
|        |            |        |             |             |        |        |         |           |

### Test Case Format

* **Test Case ID:** Unique identifier (e.g., UI_TC_XXX)
* **Feature:** The application feature or module being tested
* **Test Title:** A concise description of the test objective
* **Preconditions:** Any conditions that must be met before executing the test
* **Steps:** Significant steps to perform the test
* **Expected Results:** The expected outcome after executing the steps
* **Priority:** High, Medium, Low
* **Status:** For each test run, one of:
  * ✅ PASS - Test executed successfully with expected results
  * ❌ FAIL - Test executed but did not meet expected results
  * ⚠️ BLOCKED - Test could not be executed due to dependencies
  * ➖ NOT RUN - Test was not executed in this run
* **Notes:** Any observations, issues, or comments during test execution

## Test Case Summary

| ID | Feature | Test Title | Priority | R001 | R002 |
|----|---------|------------|----------|------|------|
| [UI_TC_001](#ui_tc_001) | Authentication | Successful User Registration with Email/Password | High | ➖ | ➖ |
| [UI_TC_002](#ui_tc_002) | Authentication | Successful User Login with Email/Password | High | ➖ | ➖ |
| [UI_TC_003](#ui_tc_003) | Authentication | Successful OAuth Login (e.g., Google) | High | ➖ | ➖ |
| [UI_TC_004](#ui_tc_004) | User Profile Management | Update User Profile Information | High | ➖ | ➖ |
| [UI_TC_005](#ui_tc_005) | Prompt Management | Create a New Prompt with Basic Content | High | ➖ | ➖ |
| [UI_TC_006](#ui_tc_006) | Prompt Management | Edit an Existing Prompt | High | ➖ | ➖ |
| [UI_TC_007](#ui_tc_007) | Prompt Management | Delete a Prompt | High | ➖ | ➖ |
| [UI_TC_008](#ui_tc_008) | Prompt Management | Categorize/Organize a Prompt | High | ➖ | ➖ |
| [UI_TC_009](#ui_tc_009) | LLM Integration | Execute a Prompt with Default LLM Provider | High | ➖ | ➖ |
| [UI_TC_010](#ui_tc_010) | LLM Integration | View and Interact with LLM Response | High | ➖ | ➖ |
| [UI_TC_011](#ui_tc_011) | LLM Integration | Switch LLM Provider for a Prompt | Medium | ➖ | ➖ |
| [UI_TC_012](#ui_tc_012) | Data Synchronization | Verify UI Update After Cloud Sync | High | ➖ | ➖ |
| [UI_TC_013](#ui_tc_013) | Data Synchronization | Access and View Prompts in Offline Mode | High | ➖ | ➖ |
| [UI_TC_014](#ui_tc_014) | File System Operations | Import Prompts from a Supported File Format | Medium | ➖ | ➖ |
| [UI_TC_015](#ui_tc_015) | File System Operations | Export a Single Prompt or All Prompts | High | ➖ | ➖ |
| [UI_TC_016](#ui_tc_016) | File System Operations | Share a Prompt using Native Share Sheet | High | ➖ | ➖ |
| [UI_TC_017](#ui_tc_017) | UI/UX | Verify Responsive Layout on Different Orientations | High | ➖ | ➖ |
| [UI_TC_018](#ui_tc_018) | UI/UX | Change Application Theme | High | ➖ | ➖ |
| [UI_TC_019](#ui_tc_019) | UI/UX - Accessibility | Basic Accessibility Check | High | ➖ | ➖ |
| [UI_TC_020](#ui_tc_020) | UI/UX - Navigation | Navigation Consistency and Intuition | High | ➖ | ➖ |
| [UI_TC_021](#ui_tc_021) | Source Input | Add Text from Clipboard as Source | High | ➖ | ➖ |
| [UI_TC_022](#ui_tc_022) | Source Input | Add YouTube Link as Source | High | ➖ | ➖ |
| [UI_TC_023](#ui_tc_023) | Source Input | Delete a Source from Input Section | High | ➖ | ➖ |
| [UI_TC_024](#ui_tc_024) | Source Input | Process Multiple Sources of Different Types | High | ➖ | ➖ |
| [UI_TC_025](#ui_tc_025) | Source Input | Receive Prompt Suggestions Based on Source Content | High | ➖ | ➖ |
| [UI_TC_026](#ui_tc_026) | LLM Integration | View and Interact with Enhanced Provider Details | Medium | ➖ | ➖ |
| [UI_TC_027](#ui_tc_027) | LLM Integration | Use Contextual Action Buttons in Results View | High | ➖ | ➖ |
| [UI_TC_028](#ui_tc_028) | LLM Integration | Verify Source Card Integration in Results View | High | ➖ | ➖ |
| [UI_TC_029](#ui_tc_029) | Portfolio/Topics | Create a New Topic | High | ➖ | ➖ |
| [UI_TC_030](#ui_tc_030) | Portfolio/Topics | Save an Execution Result to a Topic | High | ➖ | ➖ |
| [UI_TC_031](#ui_tc_031) | Portfolio/Topics | View and Navigate Topic Contents | High | ➖ | ➖ |
| [UI_TC_032](#ui_tc_032) | Data Synchronization | Verify UI Feedback During Synchronization | High | ➖ | ➖ |
| [UI_TC_033](#ui_tc_033) | Data Synchronization | Verify Conflict Resolution UI | High | ➖ | ➖ |
| [UI_TC_034](#ui_tc_034) | Discover | Browse and Navigate Prompt Categories | High | ➖ | ➖ |
| [UI_TC_035](#ui_tc_035) | Discover | Preview and Select a Prompt from Discover | High | ➖ | ➖ |
| [UI_TC_036](#ui_tc_036) | Discover | Search for Prompts in Discover | High | ➖ | ➖ |
| [UI_TC_037](#ui_tc_037) | File System Operations | Save Execution Results as PDF | High | ➖ | ➖ |
| [UI_TC_038](#ui_tc_038) | File System Operations | Handle Large File Imports | Medium | ➖ | ➖ |
| [UI_TC_039](#ui_tc_039) | Account Management | View and Edit Account Settings | High | ➖ | ➖ |
| [UI_TC_040](#ui_tc_040) | Account Management | View Subscription Information | High | ➖ | ➖ |
| [UI_TC_041](#ui_tc_041) | UI/UX - Animation | Verify Loading States and Animations | Medium | ➖ | ➖ |
| [UI_TC_042](#ui_tc_042) | UI/UX - Keyboard | Verify Keyboard Behavior and Text Input | High | ➖ | ➖ |
| [UI_TC_043](#ui_tc_043) | Error Handling | Verify Network Error Handling | High | ➖ | ➖ |
| [UI_TC_044](#ui_tc_044) | Error Handling | Verify LLM Execution Error Handling | High | ➖ | ➖ |
| [UI_TC_045](#ui_tc_045) | Error Handling | Handle Input Validation and Feedback | High | ➖ | ➖ |

## Test Cases by Feature Area

### 1. Authentication and User Management

<a id="ui_tc_001"></a>
#### UI_TC_001: Successful User Registration with Email/Password

**Feature:** Authentication
**Priority:** High
**Preconditions:** User is on the registration screen. App has network connectivity.

**Steps:**
1. Enter a valid, unique email address
2. Enter a strong password in the password field
3. Confirm the password in the confirmation field
4. Accept terms and conditions (if applicable)
5. Tap the "Register" or "Sign Up" button

**Expected Results:**
* User account is created successfully
* User is navigated to the login screen or directly into the app
* A confirmation (e.g., email verification request) might be initiated

**Status:**
| Run ID | Status | Notes |
|--------|--------|-------|
| R001   | ➖     |       |
| R002   | ➖     |       |

---

<a id="ui_tc_002"></a>
#### UI_TC_002: Successful User Login with Email/Password

**Feature:** Authentication
**Priority:** High
**Preconditions:** User has a registered account. User is on the login screen. App has network connectivity.

**Steps:**
1. Enter the registered email address
2. Enter the correct password
3. Tap the "Login" or "Sign In" button

**Expected Results:**
* User is successfully authenticated
* User is navigated to the main part of the application
* User-specific data is loaded and displayed correctly

**Status:**
| Run ID | Status | Notes |
|--------|--------|-------|
| R001   | ➖     |       |
| R002   | ➖     |       |

---

<a id="ui_tc_003"></a>
#### UI_TC_003: Successful OAuth Login (e.g., Google)

**Feature:** Authentication
**Priority:** High
**Preconditions:** User is on the login/registration screen. App has network connectivity. Device has Google services configured.

**Steps:**
1. Tap the "Sign in with Google" (or equivalent) button
2. If prompted, select a Google account
3. Complete the Google authentication flow

**Expected Results:**
* User is successfully authenticated via OAuth
* User is navigated into the application
* A new Promz account is created (if first time) or linked to an existing one

**Status:**
| Run ID | Status | Notes |
|--------|--------|-------|
| R001   | ➖     |       |
| R002   | ➖     |       |

---

<a id="ui_tc_004"></a>
#### UI_TC_004: Update User Profile Information

**Feature:** User Profile Management
**Priority:** High
**Preconditions:** User is logged in.

**Steps:**
1. Navigate to the User Profile or Account Settings screen
2. Tap an "Edit Profile" button or directly on editable fields
3. Modify one or more profile fields with valid data
4. Tap the "Save" or "Update" button

**Expected Results:**
* Profile information is updated successfully
* The updated information is reflected on the profile screen and other relevant parts of the UI
* A confirmation message might be displayed

**Status:**
| Run ID | Status | Notes |
|--------|--------|-------|
| R001   | ➖     |       |
| R002   | ➖     |       |

### 2. Prompt Management

<a id="ui_tc_005"></a>
#### UI_TC_005: Create a New Prompt with Basic Content

**Feature:** Prompt Management
**Priority:** High
**Preconditions:** User is logged in.

**Steps:**
1. Navigate to the main prompt list screen or prompt creation interface
2. Tap the "Create New Prompt" button
3. Enter a unique and descriptive title for the prompt
4. Enter the main content/body of the prompt in the designated text area
5. Optionally, assign a category or tags if available
6. Tap the "Save" or "Create" button

**Expected Results:**
* The new prompt is successfully created and saved
* The user is either navigated to view the newly created prompt or back to the prompt list
* The prompt displays the entered title and content correctly

**Status:**
| Run ID | Status | Notes |
|--------|--------|-------|
| R001   | ➖     |       |
| R002   | ➖     |       |

---

<a id="ui_tc_006"></a>
#### UI_TC_006: Edit an Existing Prompt

**Feature:** Prompt Management
**Priority:** High
**Preconditions:** User is logged in. At least one prompt exists.

**Steps:**
1. Navigate to the prompt list and select an existing prompt to edit
2. Open the selected prompt for viewing
3. Tap an "Edit" button or icon
4. Modify the prompt's title, content, or other attributes
5. Tap the "Save" or "Update" button

**Expected Results:**
* The changes to the prompt are saved successfully
* The updated prompt details are reflected in the prompt view and in the prompt list

**Status:**
| Run ID | Status | Notes |
|--------|--------|-------|
| R001   | ➖     |       |
| R002   | ➖     |       |

---

<a id="ui_tc_007"></a>
#### UI_TC_007: Delete a Prompt

**Feature:** Prompt Management
**Priority:** High
**Preconditions:** User is logged in. At least one prompt exists.

**Steps:**
1. Navigate to the prompt list
2. Select a prompt to delete (e.g., long-press, swipe, or open and find delete option)
3. Tap the "Delete" button/icon
4. Confirm the deletion if a confirmation dialog appears

**Expected Results:**
* The prompt is successfully deleted
* The prompt is removed from the prompt list
* Associated data is handled correctly

**Status:**
| Run ID | Status | Notes |
|--------|--------|-------|
| R001   | ➖     |       |
| R002   | ➖     |       |

### 3. Source Input Functionality

<a id="ui_tc_021"></a>
#### UI_TC_021: Add Text from Clipboard as Source

**Feature:** Source Input
**Priority:** High
**Preconditions:** User is logged in. App has clipboard access permission. Text content is copied to the clipboard.

**Steps:**
1. Navigate to the home screen with the source input section
2. Tap the "Paste from Clipboard" button or equivalent UI element
3. Observe the clipboard content being added as a source
4. Verify the source card displays the text content appropriately

**Expected Results:**
* The clipboard content is successfully added as a source
* A source card is created with the text content
* The source is visually distinguishable as a clipboard source
* The source can be selected/deselected

**Status:**
| Run ID | Status | Notes |
|--------|--------|-------|
| R001   | ➖     |       |
| R002   | ➖     |       |

---

<a id="ui_tc_022"></a>
#### UI_TC_022: Add YouTube Link as Source

**Feature:** Source Input
**Priority:** High
**Preconditions:** User is logged in. App has network connectivity.

**Steps:**
1. Navigate to the home screen with the source input section
2. Enter or paste a valid YouTube URL in the input field
3. Observe the system processing the YouTube link
4. Verify the YouTube source card is created with appropriate metadata

**Expected Results:**
* The YouTube link is recognized and processed correctly
* A YouTube-specific source card is displayed with video thumbnail and metadata
* The source card has appropriate styling for a video source
* The source can be selected/deselected

**Status:**
| Run ID | Status | Notes |
|--------|--------|-------|
| R001   | ➖     |       |
| R002   | ➖     |       |

## Test Execution Guidelines

### How to Use This Document

1. **Planning Test Runs:**
   - Assign a unique Run ID (e.g., R001, R002) for each test execution cycle
   - Record the date, tester name, and environment details in the Test Run Summary table
   - Determine which test cases to execute based on priority and recent changes

2. **Executing Tests:**
   - Follow the preconditions and steps precisely for each test case
   - Compare actual results with expected results
   - Update the status for each test case using the following symbols:
     * ✅ PASS - Test executed successfully with expected results
     * ❌ FAIL - Test executed but did not meet expected results
     * ⚠️ BLOCKED - Test could not be executed due to dependencies
     * ➖ NOT RUN - Test was not executed in this run
   - Add detailed notes for any failures, issues, or observations

3. **Calculating Success Rate:**
   - Count the number of tests with each status
   - Calculate pass rate: (Passed Tests ÷ Total Executed Tests) × 100%
   - Update the Test Run Summary table with these metrics

4. **Reporting Issues:**
   - For failed tests, create detailed bug reports with:
     * Test case ID and title
     * Steps to reproduce
     * Expected vs. actual results
     * Screenshots or recordings
     * Environment details

5. **Tracking Progress:**
   - Compare results across multiple test runs to identify trends
   - Focus on persistent failures or blocked tests
   - Use the pass rate to measure quality improvement over time

### Best Practices

1. **Test Environment:**
   - Test on multiple device types and screen sizes when possible
   - Document the specific device models and OS versions used
   - Ensure a clean test environment before each run

2. **Test Execution:**
   - Execute tests in a consistent order
   - Pay special attention to "Vibe Check" aspects that assess overall feel and usability
   - Consider accessibility implications during testing
   - Take screenshots of key screens for documentation

3. **Documentation:**
   - Keep detailed notes during testing
   - Update test cases if steps or expected results need clarification
   - Document any workarounds used during testing

4. **Maintenance:**
   - Review and update test cases when:
     * New features are added to the application
     * Existing features are significantly modified
     * New edge cases or user scenarios are identified
     * User feedback indicates areas needing additional testing focus