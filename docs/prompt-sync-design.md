# Prompt Sync Feature Design

**Version:** 1.0
**Date:** 2025-04-17

## 1. Overview

This document outlines the design for the "Prompt Sync" feature in the Promz application. The goal is to synchronize prompts from the server to the client, ensuring that users have access to the latest prompts available in the system.

## 2. Goals

- Implement a mechanism to fetch prompts from the server and store them in the local SQLite database
- Ensure efficient synchronization with pagination support
- Avoid duplicates and handle conflicts (server wins)
- Provide a robust background task for periodic synchronization
- Support manual triggering of synchronization when needed

## 3. Implementation

### 3.1. Client-Side Implementation (Flutter)

#### 3.1.1. Database Structure

We will leverage the existing `Prompts` table in the local SQLite database, which already has the necessary fields:

- `id`: UUID of the prompt
- `title`: Title of the prompt
- `subtitle`: Content/subtitle of the prompt
- `source`: Source of the prompt ('local' or 'cloud')
- `isSynced`: <PERSON><PERSON><PERSON> indicating if the prompt is synced
- `serverSyncAt`: DateTime when the prompt was last synced with the server

These fields will be used to track synchronization status and handle conflicts.

#### 3.1.2. Background Task Service

We will add a new background task to the existing `BackgroundTaskService`:

```dart
// Timer for prompt sync
Timer? _promptSyncTimer;

// Prompt sync interval (6 hours)
static const Duration _promptSyncInterval = Duration(hours: 6);

// Shorter interval for debug mode (10 minutes)
static const Duration _debugPromptSyncInterval = Duration(minutes: 10);

// Get the appropriate prompt sync interval based on debug mode
Duration get _currentPromptSyncInterval => 
    kDebugMode ? _debugPromptSyncInterval : _promptSyncInterval;

// Start the prompt sync task
void _startPromptSync() {
  appLog.debug('Starting prompt sync task', name: _logName);

  // Run once after a short delay
  Timer(const Duration(seconds: 5), () {
    _syncPrompts();
  });

  // Then schedule periodic execution
  _promptSyncTimer = Timer.periodic(_currentPromptSyncInterval, (timer) {
    _syncPrompts();
  });
}

// Force a sync of prompts
// This can be called from other parts of the application when needed
Future<void> forceSyncPrompts() async {
  appLog.debug('Force syncing prompts', name: _logName);
  await _syncPrompts();
}
```

#### 3.1.3. Prompt Sync Logic

The core synchronization logic will be implemented in a new method:

```dart
// Flag to prevent multiple simultaneous sync operations
bool _isSyncingPrompts = false;

// Sync prompts with the server
Future<void> _syncPrompts() async {
  // Prevent multiple simultaneous sync operations
  if (_isSyncingPrompts) {
    appLog.debug('Prompt sync already in progress, skipping', name: _logName);
    return;
  }

  _isSyncingPrompts = true;
  appLog.debug('Starting prompt sync', name: _logName);

  try {
    // Get client context service
    ClientContextService? clientContextService;
    try {
      clientContextService = ClientContextService();
    } catch (e) {
      appLog.warning('ClientContextService not initialized, skipping sync', name: _logName);
      _isSyncingPrompts = false;
      return;
    }

    // Get the database instance
    final db = await AppDatabase.getInstance();
    
    // Initialize pagination variables
    int page = 1;
    final int pageSize = 50;
    bool hasMoreData = true;
    
    // Track the last sync time
    final syncTime = DateTime.now();
    
    // Fetch prompts with pagination
    while (hasMoreData) {
      // Construct the API endpoint with pagination parameters
      final endpoint = '${ApiConfig.promptsEndpoint}?page=$page&limit=$pageSize';
      
      // Make the HTTP request to fetch prompts
      final response = await http.get(
        Uri.parse(endpoint),
        headers: {'Content-Type': 'application/json'},
      ).timeout(const Duration(seconds: ApiConfig.timeoutSeconds));
      
      if (response.statusCode == 200) {
        // Parse the response JSON
        final data = json.decode(response.body);
        
        if (data.containsKey('prompts') && data['prompts'] is List) {
          final prompts = List<Map<String, dynamic>>.from(data['prompts']);
          
          if (prompts.isEmpty) {
            // No more prompts to fetch
            hasMoreData = false;
          } else {
            // Process and store the fetched prompts
            await db.syncPrompts(prompts, syncTime);
            appLog.debug('Synced ${prompts.length} prompts from page $page', name: _logName);
            
            // Move to the next page
            page++;
          }
        } else {
          appLog.warning('Invalid response format from prompts endpoint', name: _logName);
          hasMoreData = false;
        }
      } else {
        appLog.warning('Failed to fetch prompts: HTTP ${response.statusCode}', name: _logName);
        hasMoreData = false;
      }
    }
    
    appLog.debug('Prompt sync completed', name: _logName);
  } catch (e, stackTrace) {
    appLog.error('Error in prompt sync task',
        name: _logName, error: e, stackTrace: stackTrace);
  } finally {
    _isSyncingPrompts = false;
  }
}
```

#### 3.1.4. Database Methods

We will add new methods to the `AppDatabase` class to handle prompt synchronization:

```dart
// Sync prompts from the server
Future<void> syncPrompts(List<Map<String, dynamic>> serverData, DateTime syncTime) async {
  try {
    await ensureOpen();
    if (_isClosing) {
      appLog.warning('Database is closing, cannot execute query', name: _logName);
      return;
    }

    // Begin transaction
    await transaction(() async {
      for (final promptData in serverData) {
        final promptId = promptData['id'] as String;
        
        // Check if prompt already exists
        final existingPrompt = await (select(prompts)..where((p) => p.id.equals(promptId))).getSingleOrNull();
        
        // Create a companion object for the prompt
        final companion = PromptsCompanion(
          id: Value(promptId),
          title: Value(promptData['title'] as String? ?? ''),
          subtitle: Value(promptData['subtitle'] as String? ?? ''),
          source: Value('cloud'), // Mark as cloud source
          categoryId: Value(promptData['category_id'] as String? ?? ''),
          keywords: Value(promptData['keywords'] != null ? jsonEncode(promptData['keywords']) : '[]'),
          variables: Value(promptData['variables'] != null ? jsonEncode(promptData['variables']) : '[]'),
          createdAt: Value(DateTime.parse(
              promptData['created_at'] as String? ?? DateTime.now().toIso8601String())),
          isSynced: Value(true),
          serverSyncAt: Value(syncTime),
        );
        
        if (existingPrompt != null) {
          // Update existing prompt (server wins)
          await (update(prompts)..where((p) => p.id.equals(promptId))).write(companion);
          appLog.debug('Updated existing prompt from server: $promptId', name: _logName);
        } else {
          // Insert new prompt
          await into(prompts).insert(companion);
          appLog.debug('Inserted new prompt from server: $promptId', name: _logName);
        }
      }
    });
    
    appLog.debug('Synced ${serverData.length} prompts from server', name: _logName);
  } catch (e, stack) {
    appLog.error('Error syncing prompts from server', name: _logName, error: e, stackTrace: stack);
  }
}
```

### 3.2. Server-Side Implementation (Go)

#### 3.2.1. API Endpoints

We need to enhance the existing prompt handler to support pagination and provide a standardized response format:

```go
// List lists all prompts with pagination support
func (h *PromptHandler) List(c *gin.Context) {
    // Parse pagination parameters
    page := 1
    limit := 50
    
    // Parse page parameter if provided
    pageParam := c.DefaultQuery("page", "1")
    if pageInt, err := strconv.Atoi(pageParam); err == nil && pageInt > 0 {
        page = pageInt
    }
    
    // Parse limit parameter if provided
    limitParam := c.DefaultQuery("limit", "50")
    if limitInt, err := strconv.Atoi(limitParam); err == nil && limitInt > 0 && limitInt <= 100 {
        limit = limitInt
    }
    
    // Calculate offset
    offset := (page - 1) * limit
    
    // Get prompts from repository with pagination
    prompts, err := h.database.GetPrompts(limit, offset)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve prompts"})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{
        "prompts": prompts,
    })
}
```

The endpoint will be:

- `GET /prompts?page=1&limit=50`: Returns a paginated list of prompts
  - Response format: `{ "prompts": [{ "id": "...", "title": "...", "subtitle": "...", "category_id": "...", "keywords": [...], "variables": [...], "created_at": "..." }, ...] }`
  - Pagination parameters:
    - `page`: Page number (starting from 1, default: 1)
    - `limit`: Number of prompts per page (default: 50, max: 100)

#### 3.2.2. Repository Interface Enhancement

The `PromptRepository` interface needs to be updated to support pagination:

```go
type PromptRepository interface {
    GetPrompts(limit, offset int) ([]prompt.Prompt, error)
    GetPromptByID(id string) (*prompt.Prompt, error)
    CreatePrompt(prompt *prompt.Prompt) error
    UpdatePrompt(prompt *prompt.Prompt) error
    DeletePrompt(id string) error
}
```

#### 3.2.3. PostgreSQL Implementation

The PostgreSQL implementation of the repository needs to be updated to support pagination:

```go
// GetPrompts retrieves a paginated list of prompts
func (r *PostgresPromptRepository) GetPrompts(limit, offset int) ([]prompt.Prompt, error) {
    query := `
        SELECT id, title, category_id, subtitle, source, keywords, version, is_synced, 
               created_at, updated_at, last_used_at, server_sync_at
        FROM prompts
        ORDER BY created_at DESC
        LIMIT $1 OFFSET $2
    `
    
    rows, err := r.DB.Query(query, limit, offset)
    if err != nil {
        return nil, err
    }
    defer rows.Close()
    
    var prompts []prompt.Prompt
    for rows.Next() {
        var p prompt.Prompt
        var keywordsJSON string
        var categoryID sql.NullString
        var lastUsedAt, serverSyncAt sql.NullTime
        
        err := rows.Scan(
            &p.ID, &p.Title, &categoryID, &p.Subtitle, &p.Source, &keywordsJSON,
            &p.Version, &p.IsSynced, &p.CreatedAt, &p.UpdatedAt, &lastUsedAt, &serverSyncAt,
        )
        if err != nil {
            return nil, err
        }
        
        // Handle nullable fields
        if categoryID.Valid {
            catID := categoryID.String
            p.CategoryID = &catID
        }
        
        if lastUsedAt.Valid {
            p.LastUsedAt = &lastUsedAt.Time
        }
        
        if serverSyncAt.Valid {
            p.ServerSyncAt = &serverSyncAt.Time
        }
        
        // Parse keywords JSON
        var keywords []string
        if err := json.Unmarshal([]byte(keywordsJSON), &keywords); err == nil {
            p.Keywords = keywords
        }
        
        prompts = append(prompts, p)
    }
    
    if err = rows.Err(); err != nil {
        return nil, err
    }
    
    return prompts, nil
}
```

#### 3.2.4. Supabase Client Implementation

Since the application uses Supabase, we also need to implement the pagination in the Supabase client:

```go
// GetPrompts retrieves a paginated list of prompts from Supabase
func (c *SupabaseClient) GetPrompts(limit, offset int) ([]prompt.Prompt, error) {
    var prompts []prompt.Prompt
    
    // Construct the query with pagination
    query := c.client.From("prompts").Select("*").Order("created_at", &postgrest.OrderOpts{Ascending: false}).Limit(limit, "").Offset(offset, "")
    
    // Execute the query
    res, err := query.Execute()
    if err != nil {
        return nil, err
    }
    
    // Parse the response
    if err := res.Unmarshal(&prompts); err != nil {
        return nil, err
    }
    
    return prompts, nil
}
```

#### 3.2.5. Database Schema

The existing `prompts` table in the database already has the necessary fields to support this feature:

```sql
CREATE TABLE prompts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v7(),
    title TEXT NOT NULL,
    category_id UUID REFERENCES categories(id),
    subtitle TEXT,
    source TEXT NOT NULL CHECK (source IN ('local', 'cloud')),
    keywords JSONB DEFAULT '[]'::jsonb,
    metadata JSONB DEFAULT '{}'::jsonb,
    version INTEGER DEFAULT 1,
    is_synced BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_used_at TIMESTAMP WITH TIME ZONE,
    server_sync_at TIMESTAMP WITH TIME ZONE
);

-- Indexes for better query performance
CREATE INDEX prompts_category_id_idx ON prompts(category_id);
CREATE INDEX prompts_created_at_idx ON prompts(created_at DESC);
```

#### 3.2.6. Performance Considerations

- **Indexing**: The `created_at` field should be indexed to optimize the ORDER BY clause in the query.
- **Caching**: For frequently accessed prompts, consider implementing a caching layer.
- **Connection Pooling**: Ensure proper connection pooling is configured to handle multiple concurrent requests.
- **Rate Limiting**: Implement rate limiting to prevent abuse of the API endpoint.

### 3.3. Data Flow

1. **Client (Periodically):** `BackgroundTaskService._syncPrompts()` is triggered by a timer or manually.
2. **Client:** Makes HTTP request to `GET /prompts?page=1&limit=50`.
3. **Server:** Returns paginated list of prompts.
4. **Client:** Processes each prompt and stores it in the local database.
5. **Client:** Repeats steps 2-4 for each page until all prompts are fetched.

## 4. Testing Strategy

- **Unit Tests:**
  - Test the prompt sync logic in isolation
  - Test database operations for storing and updating prompts
  - Test pagination handling
  - Test conflict resolution (server wins)
- **Integration Tests:**
  - Test the end-to-end flow from API request to database storage
  - Test handling of various response formats and error conditions
- **Performance Tests:**
  - Test with large datasets to ensure efficient pagination
  - Test memory usage during synchronization

## 5. Future Enhancements

- Implement filtering capabilities (by category, creation date, etc.)
- Add support for tracking and handling server-side deletions
- Implement bidirectional synchronization (upload local prompts to server)
- Add more sophisticated conflict resolution strategies
- Implement differential sync to reduce data transfer

## 6. Open Questions

- What is the expected volume of prompts that will need to be synced?
- Are there any specific performance requirements or constraints?
- Should we implement any caching mechanisms to reduce server load?
