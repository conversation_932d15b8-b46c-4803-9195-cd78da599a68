# YouTube Link Variable Feature Design

**Version:** 1.2
**Date:** 2025-04-21 (Updated)

## Document History

| Version | Date       | Description                                         |
| ------- | ---------- | --------------------------------------------------- |
| 1.0     | 2025-04-15 | Initial design document                             |
| 1.1     | 2025-04-21 | Updated to improve coding examples                  |
| 1.2     | 2025-04-21 | Added YouTube transcript functionality on Go server |

## 1. Overview

The YouTube Link Variable feature enhances the variable system by adding support for YouTube video
links. This allows users to reference YouTube videos in their prompts, with the system automatically
detecting YouTube URLs, extracting video metadata, and displaying a standardized preview. The
feature builds upon the existing entity variable system while providing specialized handling for
YouTube content. Additionally, it supports YouTube videos as input sources with a dedicated source
card implementation.

## 2. Goals

- Detect and process YouTube links from various formats (youtube.com and youtu.be)
- Provide an intuitive video preview interface with thumbnail and metadata
- Support YouTube link input via manual entry, clipboard, or app sharing
- Integrate with the existing entity variable system
- Maintain consistency with the news article variable implementation
- Format YouTube variables in a standardized way (MEDIA:YOUTUBE_URL)
- Support YouTube videos as input sources with a dedicated source card

## 3. Core Components

### 3.1. YouTube Link Detection

A specialized detection mechanism that:

- Identifies both standard YouTube URLs (youtube.com/watch?v=VIDEO_ID) and short URLs (youtu.be/VIDEO_ID)
- Extracts video ID from various URL formats
- Prioritizes YouTube link detection before news article detection
- Supports detection from clipboard, shared content, and manual entry

### 3.2. YouTube Preview Component

A reusable component that displays video metadata in a standardized format:

- Shows video thumbnail, title, channel name, and publication date
- Supports both standard and compact layouts
- Follows familiar video preview patterns from social media
- Provides clear visual hierarchy with video-specific styling

### 3.3. YouTube Source Card

A specialized card for displaying YouTube videos in the input sources list:

- Displays video thumbnail with play button overlay
- Shows video title and channel name
- Supports fallback display when metadata is unavailable
- Integrates with the source card factory for automatic detection
- Handles both direct video IDs and MEDIA:VIDEO_ID format from variables

### 3.4. YouTube Video Input

A dedicated input component for selecting YouTube videos as variables:

- Provides URL input field with clipboard integration
- Validates YouTube URLs in real-time
- Displays video preview with metadata
- Supports selecting and changing videos
- Integrates with the variable system using MEDIA:YOUTUBE_URL format

## 4. Technical Implementation

### 4.1. URL Utility Functions

To ensure proper separation of concerns and code reuse, we implement YouTube URL detection logic in a dedicated utility class:

```dart
/// Utility class for YouTube URL operations
class YouTubeUrlUtils {
  // Detects if a URL is a YouTube URL (supports youtube.com and youtu.be formats)
  // Handles standard watch URLs, short URLs, and embedded URLs
  static bool isYouTubeUrl(String url) { /* Implementation */ }

  // Extracts video ID from various YouTube URL formats
  // Handles youtu.be/VIDEO_ID, youtube.com/watch?v=VIDEO_ID, youtube.com/v/VIDEO_ID, etc.
  static String? extractVideoId(String url) { /* Implementation */ }

  // Generates thumbnail URL for a video ID
  static String getHighQualityThumbnailUrl(String videoId) {
    return 'https://img.youtube.com/vi/$videoId/hqdefault.jpg';
  }

  // Generates standard watch URL for a video ID
  static String getWatchUrl(String videoId) {
    return 'https://www.youtube.com/watch?v=$videoId';
  }

  // Generates embed URL for a video ID
  static String getEmbedUrl(String videoId) {
    return 'https://www.youtube.com/embed/$videoId';
  }
}

/// ContentProcessingService extension for YouTube link detection
extension ContentProcessingServiceYouTube on ContentProcessingService {
  // Checks if content appears to be a YouTube link by extracting URLs and validating
  bool _looksLikeYouTubeLink(String content) { /* Implementation */ }
}
```

### 4.2. YouTubeService Implementation

```dart
/// Service for fetching and managing YouTube video metadata
class YouTubeService {
  // HTTP client for network requests
  final http.Client _httpClient;

  // Cache for video metadata to reduce network requests
  final Map<String, Map<String, dynamic>> _metadataCache = {};

  YouTubeService({http.Client? httpClient})
      : _httpClient = httpClient ?? http.Client();

  /// Fetch metadata for a YouTube video with caching
  ///
  /// 1. Extracts video ID from URL
  /// 2. Checks cache for existing metadata
  /// 3. If not cached, fetches HTML and extracts metadata from Open Graph tags
  /// 4. Caches and returns the metadata
  Future<Map<String, dynamic>> fetchVideoMetadata(String url) async {
    // Implementation fetches video metadata with error handling and fallbacks
    // Returns a map with videoId, title, description, thumbnailUrl, channelName, etc.
  }

  /// Register a YouTube video with the entity system and return its ID
  Future<String> registerYouTubeVideo(String url, Map<String, dynamic> metadata) async {
    // Extracts videoId from metadata
    // Registers the video with the entity system
    // Returns a formatted ID: 'MEDIA:YOUTUBE_URL:videoId'
  }

  /// Clear cache for testing or memory management
  void clearCache() {
    _metadataCache.clear();
  }

  /// Extract metadata from HTML meta tags
  String? _extractMetadata(html_parser.Document document, List<String> propertyNames) {
    // Searches for meta tags with specified property names
    // Returns the content attribute if found
  }
}
```

### 4.3. UI Components

#### 4.3.1. YouTubeLinkPreview Widget

```dart
/// A reusable widget for displaying YouTube video link previews
class YouTubeLinkPreview extends StatelessWidget {
  final Map<String, dynamic> metadata;
  final VoidCallback? onTap;
  final bool compact;

  const YouTubeLinkPreview({
    Key? key,
    required this.metadata,
    this.onTap,
    this.compact = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Extract video metadata with fallbacks
    final title = metadata['title'] ?? 'YouTube Video';
    final channelName = metadata['channelName'] ?? 'YouTube';
    final description = metadata['description'] ?? '';
    final thumbnailUrl = metadata['thumbnailUrl'];

    // Renders either a compact (horizontal) or standard (vertical) card layout
    // Compact layout: Used in lists, shows thumbnail and basic info in a row
    // Standard layout: Shows larger thumbnail with more details in a column

    // Both layouts include:
    // - Thumbnail with play button overlay
    // - Video title with proper styling and ellipsis for overflow
    // - Channel name
    // - Description (in standard layout only)

    // Implementation handles missing thumbnails with fallback icons
  }
}
```

#### 4.3.2. YouTubeVideoInput Widget

```dart
/// Widget for inputting and selecting YouTube videos as variables
class YouTubeVideoInput extends StatefulWidget {
  final String initialValue;  // Initial URL or video ID
  final void Function(Variable) onSelected;  // Callback when video is selected
  final YouTubeService youtubeService;  // Service for fetching video metadata
  final Variable variable;  // The variable to update

  const YouTubeVideoInput({
    Key? key,
    required this.initialValue,
    required this.onSelected,
    required this.youtubeService,
    required this.variable,
  }) : super(key: key);

  @override
  State<YouTubeVideoInput> createState() => _YouTubeVideoInputState();
}

class _YouTubeVideoInputState extends State<YouTubeVideoInput> {
  // State variables for tracking URL input, validation, and selection
  late TextEditingController _urlController;
  bool _isLoading = false;
  bool _isValidUrl = false;
  Map<String, dynamic>? _previewMetadata;
  String? _selectedVideoId;

  // Lifecycle methods for initialization and cleanup
  // - Initializes with provided value
  // - Validates URL or handles video ID format
  // - Cleans up resources on dispose

  // Helper methods:
  // - _pasteFromClipboard: Pastes URL from clipboard
  // - _validateUrl: Validates URL format
  // - _fetchPreview: Fetches video metadata for preview
  // - _selectVideo: Registers selected video and updates variable
  // - _clearSelection: Clears current selection
}

  // UI implementation with two main states:
  // 1. Selected video state: Shows preview with change option
  // 2. Input state: Shows URL input field with validation and preview

  @override
  Widget build(BuildContext context) {
    // If a video is already selected, show preview with change option
    if (_selectedVideoId != null && _previewMetadata != null) {
      // Display selected video with preview and change button
    } else {
      // Display URL input field with:
      // - Text field with YouTube URL input
      // - Clipboard paste button
      // - Loading indicator during fetch
      // - Preview with metadata when available
      // - 'Use This Video' button to confirm selection
    }
  }
}
```

### 4.4. Entity System Integration

#### 4.4.1. Entity Model Extensions

```dart
// Add to EntityType enum in entity_model.dart
enum EntityType {
  // Existing types
  // ...
  // New type for YouTube videos
  youtubeVideo,
}
```

#### 4.4.2. InputSource Extensions

```dart
// Add to InputSourceType enum in input_source.dart
enum InputSourceType {
  // Existing types
  // ...
  // New type for YouTube videos
  youtubeVideo,
}

// Add to InputSource class
class InputSource {
  // Factory method for YouTube videos
  factory InputSource.youtubeVideo({
    required String videoId,
    required String title,
    required String channelName,
    String? thumbnailUrl,
    String? description,
  }) {
    // Creates an InputSource with youtubeVideo type and appropriate metadata
  }
}
```

#### 4.4.3. SourceCardFactory Extensions

```dart
// Add to SourceCardFactory.createCardForSource method
static Widget createCardForSource(InputSource source, /* other parameters */) {
  // Check if this is a YouTube video source
  final isYouTubeType = source.type == InputSourceType.youtubeVideo;
  final hasYouTubeMetadata = source.metadata != null &&
      (source.metadata!.containsKey(MetadataKeys.videoId) ||
       source.metadata!.containsKey(MetadataKeys.thumbnailUrl));

  // If it's a YouTube video with valid metadata, use the specialized card
  if (isYouTubeType && hasYouTubeMetadata) {
    return YouTubeSourceCard(
      source: source,
      onTap: onTap,
      onDelete: onDelete,
    );
  }

  // Otherwise, continue with other source type checks...
}
```

### 4.5. EntityVariableField Integration

```dart
// Inside EntityVariableField.build method
if (entityType == EntityType.finance) {
  // Existing stock symbol implementation
} else if (entityType == EntityType.news) {
  // Existing news article implementation
} else if (entityType == EntityType.youtubeVideo) {
  // Get youtubeService from clientContextService
  final youtubeService = clientContextService.youtube;

  // Return a specialized input field for YouTube videos
  // - Shows video icon and variable name
  // - Uses YouTubeVideoInput component for URL input and preview
  // - Handles variable updates through onSelected callback
  return YouTubeVideoInput(
    initialValue: initialValue ?? '',
    youtubeService: youtubeService,
    variable: variable,
    onSelected: onChanged,
  );
} else {
  // Default field for other entity types
}
```

## 5. User Flows

### 5.1. YouTube Video Selection as Variable

1. User encounters a variable of type "MEDIA:YOUTUBE_URL" in the VariablesDialog
2. The system displays a specialized input field with URL input box
3. User enters or pastes a YouTube video URL (either youtube.com or youtu.be format)
4. System validates URL format immediately
5. If valid, system fetches video metadata (title, channel, thumbnail)
6. Preview is displayed in a card format with video thumbnail and play button styling
7. User clicks "Use This Video" to confirm selection
8. System registers the video with the entity system
9. Variable is updated with video ID for resolution in the prompt (MEDIA:YOUTUBE_URL format)
10. User sees confirmation that the video is selected

### 5.2. YouTube Video as Source Input

1. User pastes or enters a YouTube URL in the source input field
2. ContentProcessingService detects the URL as a YouTube video
3. System fetches video metadata (title, channel, thumbnail)
4. Video is added to the source list with a YouTubeSourceCard
5. User can click on the card to view details or delete it from sources
6. The video is available as a source for the prompt

### 5.3. Changing Selected Video

1. User has previously selected a video
2. System displays the selected video preview with a "Change" button
3. User clicks "Change" to select a different video
4. System clears the current selection and shows the URL input field
5. User enters a new URL and the process continues as in 5.1

### 5.4. Error Handling

1. User enters an invalid URL
2. System immediately shows validation error
3. User enters a valid URL but video metadata cannot be fetched
4. System shows appropriate error message with fallback information
5. User can retry or enter a different URL

## 6. Implementation Phases

### 6.1. Phase 1

- YouTube URL detection in ContentProcessingService
- Basic YouTubeService implementation with HTML parsing for metadata
- In-memory caching for video metadata
- YouTubeLinkPreview component for displaying video previews
- YouTubeSourceCard component for displaying YouTube videos in source list
- YouTubeVideoInput component for variable selection
- Integration with EntityVariableField
- Entity system extensions for YouTube video type
- Source card factory integration for YouTube videos

### 6.2. Future Enhancements

- Support for playlists and channels
- Persistent caching with time-based expiration
- Improved error handling and fallback mechanisms
- Support for video timestamps and specific segments
- Enhanced metadata extraction techniques

## 7. Design Considerations

### 7.1. Privacy

- Only process URLs explicitly entered by users
- Clear indication when external content is being fetched
- Minimal data storage (only what's necessary for functionality)
- Follow same authentication patterns as news article feature

### 7.2. Performance

- In-memory caching of video metadata to reduce network requests
- Lightweight metadata fetching with reasonable timeouts
- Proper loading states during network operations
- Graceful error handling for invalid URLs or failed fetches
- High-quality thumbnails with optimized loading and error fallbacks

### 7.3. Code Quality

- Separation of concerns with dedicated utility classes
- Reuse of existing patterns and components from news article implementation
- Clear interfaces between components
- Testable service implementations with dependency injection
- Consistent error handling and logging patterns

### 7.3. User Experience

- Familiar video preview styling with play button overlay
- Clear visual hierarchy in preview cards
- Intuitive clipboard integration for URL pasting
- Responsive design for different screen sizes

### 7.4. Accessibility

- Proper contrast ratios for text content
- Meaningful alternative text for images
- Keyboard navigation support
- Screen reader compatibility

## 8. Integration Points

### 8.1. Entity System

- YouTube videos are registered as entities with the EntityDetectionService
- Video metadata is made available for entity detection in text
- Entity variables can reference YouTube videos by ID

### 8.2. Variable Resolution

- When a prompt is executed, YouTube video variables are resolved to their standardized format
- Video metadata (title, channel, etc.) is available for formatting

### 8.3. Source Input Section

- The YouTubeSourceCard component is used in the SourceList to display YouTube videos
- The ContentProcessingService detects YouTube links in the source input
- YouTubeService handles fetching and registering YouTube videos as sources
- Consistent preview styling across the application
- Shared video fetching logic between components

## 9. YouTube Transcript Functionality

**Date Added:** 2025-04-21

### 9.1. Overview

The YouTube Transcript functionality enhances the YouTube Link Variable feature by adding the
ability to fetch and process transcripts from YouTube videos. This allows for improved summarization
and analysis of video content by providing text-based representations of the video's spoken content.

The system will detect the `MEDIA:YOUTUBE_TRANSCRIPT` variable in prompt instructions and
automatically fetch the transcript based on the URL provided in the `MEDIA:YOUTUBE_URL` variable.
This server-side processing ensures that transcripts are seamlessly integrated into the prompt
execution flow without requiring additional client-side implementation.

### 9.2. Server-Side Implementation

The transcript functionality will be implemented on the Go server to provide centralized processing,
caching, and error handling. This approach reduces client-side processing requirements and allows
for more efficient management of requests to YouTube's services.

#### 9.2.1. Go Package Integration

```go
// In go.mod
require (
    google.golang.org/api v0.126.0     // Official YouTube Data API
    github.com/chand1012/yt_transcript v0.1.1  // Unofficial API (backup)
    // other dependencies
)
```

#### 9.2.2. Transcript Service Strategy Pattern

```go
// TranscriptData represents a segment of a YouTube transcript
type TranscriptData struct {
    Text     string `json:"text"`
    Duration int    `json:"duration"`
    Offset   int    `json:"offset_ms"`
}

// TranscriptServiceInterface defines the interface for transcript service
type TranscriptServiceInterface interface {
    GetTranscript(videoURL, lang, country string) ([]TranscriptData, string, error)
    GetFullTranscriptText(videoURL, lang, country string) (string, error)
    CleanCache()
}

// TranscriptServiceStrategy represents a strategy for fetching transcripts
type TranscriptServiceStrategy interface {
    GetTranscript(videoID, lang, country string) ([]TranscriptSegment, string, error)
    GetName() string
}

// TranscriptSegment represents a segment returned by any strategy
type TranscriptSegment struct {
    Text     string
    Start    float64
    Duration float64
}

// OfficialAPIStrategy implements the official YouTube Data API
type OfficialAPIStrategy struct {
    service *youtube.Service
    apiKey  string
}

// NewOfficialAPIStrategy creates a new strategy using the official API
func NewOfficialAPIStrategy(apiKey string) (*OfficialAPIStrategy, error) {
    ctx := context.Background()
    service, err := youtube.NewService(ctx, option.WithAPIKey(apiKey))
    if err != nil {
        return nil, err
    }
    
    return &OfficialAPIStrategy{
        service: service,
        apiKey:  apiKey,
    }, nil
}

// GetTranscript fetches transcript using the official YouTube API
func (s *OfficialAPIStrategy) GetTranscript(videoID, lang, country string) ([]TranscriptSegment, string, error) {
    // Implementation using the official YouTube Data API
    // Fetch captions using service.Captions methods
    // ...
}

// ScrapingStrategy implements the HTML scraping approach
type ScrapingStrategy struct {
    httpClient *http.Client
    userAgent  string
}

// NewScrapingStrategy creates a new strategy using HTML scraping
func NewScrapingStrategy() *ScrapingStrategy {
    return &ScrapingStrategy{
        httpClient: &http.Client{Timeout: 10 * time.Second},
        userAgent:  "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    }
}

// GetTranscript fetches transcript using HTML scraping
func (s *ScrapingStrategy) GetTranscript(videoID, lang, country string) ([]TranscriptSegment, string, error) {
    // Implementation using HTML scraping (current approach)
    // ...
}

// FallbackTranscriptService handles YouTube transcript operations with multiple strategies
type FallbackTranscriptService struct {
    strategies []TranscriptServiceStrategy
    cache      map[string][]TranscriptData
    mutex      sync.RWMutex
    cacheTTL   time.Duration
    lastClear  time.Time
}

// NewFallbackTranscriptService creates a new transcript service with multiple strategies
func NewFallbackTranscriptService(apiKey string) (*FallbackTranscriptService, error) {
    // Create strategies in order of preference
    var strategies []TranscriptServiceStrategy
    
    // Try to create official API strategy first
    if apiKey != "" {
        officialStrategy, err := NewOfficialAPIStrategy(apiKey)
        if err == nil {
            strategies = append(strategies, officialStrategy)
        } else {
            log.Printf("WARNING: Failed to initialize YouTube API strategy: %v", err)
        }
    }
    
    // Always add scraping strategy as fallback
    strategies = append(strategies, NewScrapingStrategy())
    
    return &FallbackTranscriptService{
        strategies: strategies,
        cache:      make(map[string][]TranscriptData),
        cacheTTL:   24 * time.Hour,
        lastClear:  time.Now(),
    }, nil
}

// GetTranscript fetches a transcript for a YouTube video with caching and fallback
func (s *FallbackTranscriptService) GetTranscript(videoURL, lang, country string) ([]TranscriptData, string, error) {
    // Extract video ID
    videoID, err := ExtractVideoID(videoURL)
    if err != nil {
        return nil, "", err
    }

    // Check cache first
    s.mutex.RLock()
    if data, ok := s.cache[videoID]; ok {
        s.mutex.RUnlock()
        // Get title (can use any strategy, as we already have the transcript)
        title, _ := s.getVideoTitle(videoID)
        return data, title, nil
    }
    s.mutex.RUnlock()

    // Try each strategy in order until one succeeds
    var lastError error
    for _, strategy := range s.strategies {
        transcript, title, err := strategy.GetTranscript(videoID, lang, country)
        if err == nil {
            // Convert to our data structure
            var data []TranscriptData
            for _, seg := range transcript {
                data = append(data, TranscriptData{
                    Text:     seg.Text,
                    Duration: int(seg.Duration * 1000),  // Convert to milliseconds
                    Offset:   int(seg.Start * 1000),     // Convert to milliseconds
                })
            }

            // Cache the result
            s.mutex.Lock()
            s.cache[videoID] = data
            s.mutex.Unlock()
            
            log.Printf("Successfully fetched transcript using %s strategy", strategy.GetName())
            return data, title, nil
        }
        
        lastError = err
        log.Printf("Strategy %s failed: %v", strategy.GetName(), err)
    }
    
    return nil, "", fmt.Errorf("all transcript strategies failed, last error: %v", lastError)
}

// GetFullTranscriptText returns the full transcript as a single string
func (s *TranscriptService) GetFullTranscriptText(videoURL, lang, country string) (string, error) {
    transcript, _, err := s.GetTranscript(videoURL, lang, country)
    if err != nil {
        return "", err
    }

    var fullText strings.Builder
    for _, t := range transcript {
        fullText.WriteString(t.Text)
        fullText.WriteString(" ")
    }

    return fullText.String(), nil
}

// CleanCache removes expired entries from the cache
func (s *TranscriptService) CleanCache() {
    if time.Since(s.lastClear) < s.cacheTTL {
        return
    }

    s.mutex.Lock()
    defer s.mutex.Unlock()

    s.cache = make(map[string][]TranscriptData)
    s.lastClear = time.Now()
}
```

#### 9.2.3. API Endpoint

```go
// In router.go
func Setup(database db.SupabaseClientInterface, cfg *config.Config, executor *llm.Executor) *gin.Engine {
    // Existing setup code...

    // Initialize transcript service
    transcriptService := services.NewTranscriptService()

    // YouTube transcript routes
    youtube := protected.Group("/yt")
    {
        youtube.GET("/xs", handler.GetYouTubeTranscript(transcriptService))
        youtube.GET("/xs/text", handler.GetYouTubeTranscriptText(transcriptService))
    }

    // Return router
    return r
}
```

#### 9.2.4. Handler Functions

```go
// GetYouTubeTranscript handles requests for YouTube transcripts
func GetYouTubeTranscript(service *services.TranscriptService) gin.HandlerFunc {
    return func(c *gin.Context) {
        videoURL := c.Query("url")
        if videoURL == "" {
            c.JSON(http.StatusBadRequest, gin.H{
                "error": "Missing video URL",
            })
            return
        }

        lang := c.DefaultQuery("lang", "en")
        country := c.DefaultQuery("country", "US")

        transcript, title, err := service.GetTranscript(videoURL, lang, country)
        if err != nil {
            c.JSON(http.StatusInternalServerError, gin.H{
                "error": err.Error(),
            })
            return
        }

        c.JSON(http.StatusOK, gin.H{
            "title": title,
            "transcript": transcript,
        })
    }
}

// GetYouTubeTranscriptText handles requests for YouTube transcript as plain text
func GetYouTubeTranscriptText(service *services.TranscriptService) gin.HandlerFunc {
    return func(c *gin.Context) {
        videoURL := c.Query("url")
        if videoURL == "" {
            c.JSON(http.StatusBadRequest, gin.H{
                "error": "Missing video URL",
            })
            return
        }

        lang := c.DefaultQuery("lang", "en")
        country := c.DefaultQuery("country", "US")

        text, err := service.GetFullTranscriptText(videoURL, lang, country)
        if err != nil {
            c.JSON(http.StatusInternalServerError, gin.H{
                "error": err.Error(),
            })
            return
        }

        c.JSON(http.StatusOK, gin.H{
            "text": text,
        })
    }
}
```

### 9.3. Variable Processing Implementation

The system will implement a new variable `MEDIA:YOUTUBE_TRANSCRIPT` that can be used in prompt
templates. When this variable is detected, the server will:

1. Extract the YouTube URL from the `MEDIA:YOUTUBE_URL` variable
2. Fetch the transcript using the TranscriptService
3. Replace the `MEDIA:YOUTUBE_TRANSCRIPT` variable with the full transcript text

```go
// In template_processor.go

// ProcessYouTubeTranscriptVariables processes YouTube transcript variables
func (p *TemplateProcessor) ProcessYouTubeTranscriptVariables(variables map[string]interface{}) error {
    // Check if we have a YouTube transcript variable
    if _, hasTranscriptVar := variables["MEDIA:YOUTUBE_TRANSCRIPT"]; hasTranscriptVar {
        // Check if we have a YouTube URL variable
        youtubeURL, hasYouTubeURL := variables["MEDIA:YOUTUBE_URL"]
        if !hasYouTubeURL || youtubeURL == "" {
            return errors.New("MEDIA:YOUTUBE_TRANSCRIPT variable requires MEDIA:YOUTUBE_URL to be set")
        }

        // Get the transcript
        text, err := p.transcriptService.GetFullTranscriptText(youtubeURL.(string), "en", "US")
        if err != nil {
            return fmt.Errorf("failed to get YouTube transcript: %w", err)
        }

        // Set the transcript variable
        variables["MEDIA:YOUTUBE_TRANSCRIPT"] = text
    }

    return nil
}
```

This function will be called before template processing to ensure that the transcript is available when the template is rendered.

### 9.4. Integration with Template Processing

The template processor will be updated to handle YouTube transcript variables:

```go
// ProcessTemplate substitutes variables in a template string
func (p *TemplateProcessor) ProcessTemplate(templateText string, variables map[string]interface{}) (string, error) {
    // Process any special variables that need preprocessing
    if err := p.ProcessYouTubeTranscriptVariables(variables); err != nil {
        log.Printf("Warning: Failed to process YouTube transcript variables: %v", err)
        // Continue with processing, as we want to be resilient to failures
    }

    // Existing template processing code...
}
```

### 9.5. Entity System Integration

The entity system will be updated to include the new `MEDIA:YOUTUBE_TRANSCRIPT` variable:

```go
// In entity_utils.go

// Add to variable display names map
var variableDisplayNames = map[string]string{
    // Existing variables...
    "MEDIA:YOUTUBE_URL": "YouTube Video URL",
    "MEDIA:YOUTUBE_TITLE": "YouTube Video Title",
    "MEDIA:YOUTUBE_CHANNEL": "YouTube Channel",
    "MEDIA:YOUTUBE_DESCRIPTION": "YouTube Description",
    "MEDIA:YOUTUBE_THUMBNAIL": "YouTube Thumbnail",
    "MEDIA:YOUTUBE_TRANSCRIPT": "YouTube Transcript",
    // Other variables...
}

// Add to variable synonyms map
var variableSynonyms = map[string]string{
    // Existing synonyms...
    "YOUTUBE_TRANSCRIPT": "MEDIA:YOUTUBE_TRANSCRIPT",
    "YOUTUBE_VIDEO_TRANSCRIPT": "MEDIA:YOUTUBE_TRANSCRIPT",
    // Other synonyms...
}
```

### 9.6. Error Handling and Fallbacks

The implementation includes robust error handling and fallback mechanisms:

1. **Transcript Unavailability**: If a transcript cannot be fetched, the system will fall back to using only the video metadata for processing.

2. **Language Fallbacks**: If a transcript is not available in the requested language, the system will attempt to fetch it in English.

3. **Rate Limiting**: The server implements rate limiting to prevent excessive requests to YouTube's services.

4. **Caching**: Transcripts are cached to reduce the number of requests and improve performance.

### 9.7. Security Considerations

1. **API Access Control**: The transcript endpoints are protected behind authentication to prevent unauthorized access.

2. **Input Validation**: All user inputs are validated to prevent injection attacks.

3. **Error Message Sanitization**: Error messages are sanitized to prevent information leakage.

### 9.8. Multi-Strategy Transcript Service Implementation

The system implements a hybrid approach with both the official YouTube Data API and HTML scraping in a fallback pattern:

```go
// TranscriptServiceConfig holds configuration for the transcript service
type TranscriptServiceConfig struct {
    APIKey           string        // YouTube API key
    CacheTTL         time.Duration // Cache expiration time
    EnableScraping   bool          // Enable scraping method
    EnableOfficialAPI bool         // Enable official API method
}

// Factory method to create a transcript service with the fallback pattern
func NewTranscriptServiceWithAPIKey(apiKey string) TranscriptServiceInterface {
    config := NewDefaultTranscriptServiceConfig()
    config.APIKey = apiKey
    
    service, err := NewFallbackTranscriptService(config)
    if err != nil {
        // Fall back to legacy implementation if the fallback service fails
        return &LegacyTranscriptService{/* initialization */}
    }
    return service
}
```

The implementation uses the Strategy pattern to allow multiple transcript retrieval methods:

1. **Primary Strategy**: Official YouTube Data API
   - More reliable and stable
   - Less susceptible to YouTube UI changes
   - Requires API key and respects quota limits

2. **Fallback Strategy**: HTML Scraping Method
   - No API key required
   - Used when API limits are reached or API fails
   - May break when YouTube changes their page structure

The service tries each strategy in order until one succeeds, with robust caching to minimize repeated requests.

### 9.9. Scaling Considerations

The transcript service is designed to scale effectively:

1. **In-Memory Caching**: Transcripts are cached for 24 hours by default, dramatically reducing API and scraping requests.

2. **Configurable API Usage**: The system can be configured to use only the scraping method when API quotas are a concern, or to prioritize the official API for higher reliability.

3. **Graceful Degradation**: If one method reaches rate limits or fails, the system automatically tries alternative methods.

4. **Minimal Resource Requirements**: The service is designed to be lightweight, with controlled memory usage through time-based cache expiration.

5. **Monitoring and Logging**: Comprehensive logging helps identify API quota issues or scraping failures, allowing for proactive management.

For scaling to hundreds of thousands of users:

- The in-memory cache can be replaced with a distributed cache (Redis/Memcached)
- YouTube API quotas can be managed by monitoring usage and adjusting strategy preferences
- Multiple API keys can be rotated to increase effective quotas

### 9.10. Benefits of the Multi-Strategy Approach

The multi-strategy approach for transcript retrieval provides several key benefits:

1. **Increased Reliability**: By having multiple methods to retrieve transcripts, the system can continue functioning even if one method fails or is rate-limited.

2. **Adaptive to Changes**: If YouTube changes their website structure, breaking the scraping method, the official API will continue to work while updates are made.

3. **Optimal Performance**: The system starts with the most reliable method (official API) and falls back to alternatives only when necessary.

4. **Quota Management**: The official API has usage quotas, but by using it as the primary method and having a fallback, we can maximize reliability while controlling costs.

5. **Feature Coverage**: Different methods may provide different features or transcript formats; the hybrid approach ensures maximum coverage.

### 9.11. Future Enhancements

1. **Persistent Caching**: Implement database-backed caching for transcripts to persist across server restarts.

2. **Multiple Language Support**: Enhance the API to support fetching transcripts in multiple languages.

3. **Transcript Analysis**: Add capabilities to analyze transcripts for sentiment, topics, and key points.

4. **Additional Strategies**: Add more transcript retrieval methods from other third-party libraries as they become available.

5. **Quality Assessment**: Implement a scoring system to evaluate and compare transcript quality from different sources.

## 10. Conclusion

The YouTube Link Variable feature enhances the prompt creation experience by allowing seamless
integration of video content. By following familiar video preview patterns and leveraging the
existing entity system, this feature provides an intuitive way for users to reference YouTube videos
in their prompts. The implementation focuses on simplicity, performance, and user experience while
maintaining consistency with the rest of the application.

With the addition of transcript functionality, the feature now provides deeper integration with
YouTube content, enabling more sophisticated processing and analysis of video content. The
server-side implementation ensures efficient resource usage and centralized management of YouTube
API interactions.

The feature supports three primary use cases:

1. Using YouTube videos as variables in prompts with the MEDIA:YOUTUBE_URL format
2. Adding YouTube videos as input sources with dedicated source cards
3. Extracting and utilizing video transcripts via the MEDIA:YOUTUBE_TRANSCRIPT variable for enhanced content processing and summarization

The design prioritizes YouTube link detection before news article detection, ensuring proper
handling of both content types. The standardized MEDIA:YOUTUBE_URL format for variables provides a
clear way to reference video content in prompts, while the preview components offer a familiar and
intuitive interface for users. The YouTubeSourceCard component ensures consistent display of YouTube
videos in the source list, enhancing the overall user experience.
