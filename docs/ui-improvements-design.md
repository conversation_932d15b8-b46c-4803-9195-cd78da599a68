# Execution Results UI Improvements

**Version:** 1.1  
**Date:** April 22, 2025  
**Author:** Promz Team

## Document History

| Version | Date       | Description                                         |
| ------- | ---------- | --------------------------------------------------- |
| 1.0     | 2025-04-22 | Initial design document                             |
| 1.1     | 2025-04-22 | Added loading states, shimmer effects, and HTML-based source cards |

## 1. Overview

This design document outlines UI improvements to the execution results section displayed after a prompt has been executed. These improvements aim to enhance user experience, provide more context about the source content, and streamline result sharing and interaction options.

## 2. Current Implementation

The current implementation (`ExecutionResultsSection` widget) displays results in a modal-like window with:

- Error state handling
- Markdown/plain text result display
- Basic actions (retry, copy result, save as PDF)
- Simple provider information (provider name only)

## 3. Key UI Improvements

### 3.1. Source Card Integration

#### Current Challenge

Results are displayed without visual context of the original source material (YouTube, news article, etc.) that the prompt was based on.

#### Solution

Integrate source cards at the top of the results view using the existing `SourceCardFactory` with a new HTML-based implementation for consistent rendering across UI and PDF outputs.

```text
┌─────────────────────────────┐
│    ┌───────────────────┐    │
│    │   SOURCE CARD     │    │
│    └───────────────────┘    │
│                             │
│    Result                   │
│    ─────────────────────    │
│                             │
│    Lorem ipsum dolor sit    │
│    amet, consectetur...     │
│                             │
│    Provider: Google 2.5 Pro │
│    ─────────────────────    │
│    [Retry] [Copy] [Share]   │
└─────────────────────────────┘
```

#### Implementation Details

1. Update `SourceCardFactory` and all source card widgets to generate HTML representations
2. Extend the `ExecutionResultsSection` to display source cards using `flutter_html`
3. Add loading states with shimmer effects while source card data is being fetched
4. Add logic to select the most relevant source when multiple sources exist
5. Reuse the same HTML in both the UI and PDF generation

### 3.2. Enhanced Provider Details

#### Current Challenge

Provider information displays only the basic provider name (e.g., "Google", "OpenAI"), lacking model details.

#### Solution

Display more detailed provider information including model version:

- "Gemini 1.5 Pro" instead of just "google"
- "OpenAI GPT-4" instead of just "openai"

#### Implementation Details

1. Extend the `LlmExecuteResponse` model to include model name/version from the server
2. Update the LLM pricing tool (`parser.ts`) to provide user-friendly model names
3. The server will return the display_name from the llm_models database table
4. Update the UI to accommodate potentially longer provider strings

### 3.3. Contextual Action Buttons

#### Current Challenge

Action buttons are displayed regardless of context, and button labels could be more intuitive.

#### Solution

1. **Retry Button**: Show only when there's an error
2. **Copy Button**: Rename to "Copy" (with icon unchanged)
3. **Save Button**: Show several options including "Share", "Save to Topics", "Save to PDF"

#### Implementation Details

1. Conditional rendering of buttons based on state
2. Update button labels for clarity
3. Update "Save" button to connect to Topics, PDF generation etc.

### 3.4. PDF Generation Enhancements

#### Current Challenge

PDFs don't include source context information.

#### Solution

Include source card information in generated PDFs using the same HTML source as the UI.

#### Implementation Details

1. Modify `_saveToPdf` method to include source card HTML
2. Use the HTML-to-PDF converter to process combined source card and markdown content
3. Position source card at the top of the PDF document

### 3.5. Animation and Transitions

#### Current Challenge

Results appear instantly without visual feedback.

#### Solution

Add subtle animations for:

- Results loading
- Switching between error/success states
- Button interactions

#### Implementation Details

1. Add fade-in animation for results section
2. Use AnimatedContainer for smooth transitions between states
3. Add subtle scale animation for button interactions

### 3.6. Result Stats and Feedback

#### Current Challenge

No metrics or feedback mechanism for results.

#### Solution

1. Add word/character count for generated content
2. Add feedback mechanism (thumbs up/down) for result quality
3. Track user engagement with results

## 4. Technical Design

### 4.1. HTML-Based Source Card System

```dart
/// Class responsible for generating HTML representations of source cards
class SourceCardHtmlGenerator {
  static String generateForSource(InputSource source) {
    if (source.type == InputSourceType.youtubeVideo && source.metadata != null) {
      return _generateYouTubeSourceHtml(source);
    } else if (source.type == InputSourceType.news && 
               FileIconHelper.hasValidNewsMetadata(source)) {
      return _generateNewsSourceHtml(source);
    } else {
      return _generateDefaultSourceHtml(source);
    }
  }
  
  static String _generateYouTubeSourceHtml(InputSource source) {
    final metadata = source.metadata!;
    final title = metadata[MetadataKeys.title] ?? 'YouTube Video';
    final channelName = metadata[MetadataKeys.channelName] ?? 'Unknown Channel';
    final videoId = metadata[MetadataKeys.videoId];
    final thumbnailUrl = metadata[MetadataKeys.thumbnailUrl];
    
    return '''
    <div class="source-card youtube-card">
      <div class="thumbnail">
        ${thumbnailUrl != null ? '<img src="$thumbnailUrl" alt="$title" />' : 
        '<div class="placeholder-thumbnail"><i class="icon-youtube"></i></div>'}
      </div>
      <div class="content">
        <h3 class="title">$title</h3>
        <p class="channel"><i class="icon-user"></i> $channelName</p>
        ${videoId != null ? '<p class="link">https://youtu.be/$videoId</p>' : ''}
      </div>
    </div>
    ''';
  }
  
  static String _generateNewsSourceHtml(InputSource source) {
    final metadata = source.metadata!;
    final title = metadata[MetadataKeys.title] ?? 'News Article';
    final siteName = metadata[MetadataKeys.siteName] ?? 
        _extractDomainFromUrl(metadata[MetadataKeys.url] as String? ?? '');
    final url = metadata[MetadataKeys.url] as String? ?? '';
    final imageUrl = metadata['imageUrl']; // Using string key since it's not in MetadataKeys
    
    return '''
    <div class="source-card news-card">
      <div class="thumbnail">
        ${imageUrl != null ? '<img src="$imageUrl" alt="$title" />' : 
        '<div class="placeholder-thumbnail"><i class="icon-newspaper"></i></div>'}
      </div>
      <div class="content">
        <h3 class="title">$title</h3>
        <p class="site"><i class="icon-globe"></i> $siteName</p>
        ${url.isNotEmpty ? '<p class="link">$url</p>' : ''}
      </div>
    </div>
    ''';
  }
  
  static String _generateDefaultSourceHtml(InputSource source) {
    final title = source.fileName ?? 'Source';
    final typeDescription = FileIconHelper.getSourceTypeDescription(source) ?? '';
    final iconClass = _getIconClassForSourceType(source.type);
    
    return '''
    <div class="source-card default-card">
      <div class="icon">
        <i class="$iconClass"></i>
      </div>
      <div class="content">
        <h3 class="title">$title</h3>
        ${typeDescription.isNotEmpty ? '<p class="type">$typeDescription</p>' : ''}
      </div>
    </div>
    ''';
  }
  
  // Create a skeleton HTML for loading state
  static String generateSkeletonHtml() {
    return '''
    <div class="source-card skeleton-card">
      <div class="skeleton-thumbnail"></div>
      <div class="content">
        <div class="skeleton-title"></div>
        <div class="skeleton-subtitle"></div>
        <div class="skeleton-text"></div>
      </div>
    </div>
    ''';
  }
  
  static String _getIconClassForSourceType(InputSourceType type) {
    switch (type) {
      case InputSourceType.file: return 'icon-file';
      case InputSourceType.text: return 'icon-text';
      case InputSourceType.clipboard: return 'icon-clipboard';
      // Add more cases as needed
      default: return 'icon-document';
    }
  }
  
  static String _extractDomainFromUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.host;
    } catch (_) {
      return 'Unknown Source';
    }
  }
}
```

### 4.2. Source Card Loading States with Shimmer Effect

```dart
/// A widget that displays a loading state while a source card is being prepared
class SourceCardSkeleton extends StatelessWidget {
  final SourceCardType type;
  
  const SourceCardSkeleton({
    Key? key,
    this.type = SourceCardType.default,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4.0),
      child: Shimmer.fromColors(
        baseColor: Colors.grey[300]!,
        highlightColor: Colors.grey[100]!,
        child: _buildSkeletonByType(),
      ),
    );
  }
  
  Widget _buildSkeletonByType() {
    switch (type) {
      case SourceCardType.youtube:
        return _buildYoutubeCardSkeleton();
      case SourceCardType.news:
        return _buildNewsCardSkeleton();
      case SourceCardType.default:
      default:
        return _buildDefaultCardSkeleton();
    }
  }
  
  Widget _buildYoutubeCardSkeleton() {
    return Card(
      clipBehavior: Clip.antiAlias,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Thumbnail skeleton
            Container(
              width: 80,
              height: 60,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(4),
              ),
            ),
            
            const SizedBox(width: 12),
            
            // Content skeleton
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title skeleton
                  Container(
                    width: double.infinity,
                    height: 18,
                    color: Colors.white,
                  ),
                  
                  const SizedBox(height: 8),
                  
                  // Channel skeleton
                  Container(
                    width: 120,
                    height: 14,
                    color: Colors.white,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildNewsCardSkeleton() {
    return Card(
      clipBehavior: Clip.antiAlias,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Thumbnail skeleton
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(4),
              ),
            ),
            
            const SizedBox(width: 12),
            
            // Content skeleton
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title skeleton
                  Container(
                    width: double.infinity,
                    height: 18,
                    color: Colors.white,
                  ),
                  
                  const SizedBox(height: 8),
                  
                  // Source site skeleton
                  Container(
                    width: 80,
                    height: 14,
                    color: Colors.white,
                  ),
                  
                  const SizedBox(height: 6),
                  
                  // Excerpt skeleton
                  Container(
                    width: double.infinity,
                    height: 14,
                    color: Colors.white,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildDefaultCardSkeleton() {
    return Card(
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(4),
          ),
        ),
        title: Container(
          width: double.infinity,
          height: 18,
          color: Colors.white,
        ),
        subtitle: Container(
          width: 100,
          height: 14,
          margin: const EdgeInsets.only(top: 8),
          color: Colors.white,
        ),
      ),
    );
  }
}

enum SourceCardType {
  default,
  youtube,
  news,
}
```

### 4.3. Source Card Integration in UI

```dart
// In execution_results_section.dart
Widget _buildSourceCard(BuildContext context) {
  if (viewModel.sources.isEmpty) {
    return const SizedBox.shrink(); // No source to display
  }
  
  // Get the primary source (most relevant or first if multiple)
  final primarySource = _getPrimarySource(viewModel.sources);
  
  // Show loading state with shimmer effect if metadata is still being fetched
  if (_isLoadingMetadata(primarySource)) {
    return _buildLoadingSourceCard(primarySource.type);
  }
  
  // Generate HTML for the source card
  final sourceCardHtml = SourceCardHtmlGenerator.generateForSource(primarySource);
  
  // CSS styles for proper rendering
  final sourceCardCss = '''
  .source-card {
    display: flex;
    padding: 12px;
    margin-bottom: 16px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background-color: #f8f8f8;
  }
  
  // ...existing CSS styles...
  
  /* Skeleton card styles */
  .skeleton-card {
    display: flex;
    padding: 12px;
    margin-bottom: 16px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background-color: #f8f8f8;
  }
  
  .skeleton-thumbnail {
    width: 80px;
    height: 60px;
    margin-right: 12px;
    border-radius: 4px;
    background-color: #e0e0e0;
    animation: shimmer 1.5s infinite;
  }
  
  .skeleton-title {
    width: 100%;
    height: 18px;
    margin-bottom: 8px;
    border-radius: 2px;
    background-color: #e0e0e0;
    animation: shimmer 1.5s infinite;
  }
  
  .skeleton-subtitle {
    width: 60%;
    height: 14px;
    margin-bottom: 6px;
    border-radius: 2px;
    background-color: #e0e0e0;
    animation: shimmer 1.5s infinite;
  }
  
  .skeleton-text {
    width: 80%;
    height: 14px;
    border-radius: 2px;
    background-color: #e0e0e0;
    animation: shimmer 1.5s infinite;
  }
  
  @keyframes shimmer {
    0% {
      opacity: 0.5;
    }
    50% {
      opacity: 1.0;
    }
    100% {
      opacity: 0.5;
    }
  }
  ''';
  
  // Use flutter_html to render the HTML
  return Html(
    data: sourceCardHtml,
    style: {
      "html": Style.fromCss(sourceCardCss),
    },
    onLinkTap: (url, _, __, ___) {
      if (url != null) {
        launchUrl(Uri.parse(url));
      }
    },
  );
}

// Helper method to build loading source card with shimmer effect
Widget _buildLoadingSourceCard(InputSourceType sourceType) {
  SourceCardType skeletonType;
  
  switch (sourceType) {
    case InputSourceType.youtubeVideo:
      skeletonType = SourceCardType.youtube;
      break;
    case InputSourceType.news:
      skeletonType = SourceCardType.news;
      break;
    default:
      skeletonType = SourceCardType.default;
  }
  
  return SourceCardSkeleton(type: skeletonType);
}

// Helper method to check if metadata is still being fetched
bool _isLoadingMetadata(InputSource source) {
  if (source.type == InputSourceType.youtubeVideo) {
    // For YouTube videos, check if necessary metadata is present
    return source.metadata == null || 
           source.metadata![MetadataKeys.title] == null || 
           source.metadata![MetadataKeys.channelName] == null;
  } else if (source.type == InputSourceType.news) {
    // For news articles, check if necessary metadata is present
    return source.metadata == null || 
           source.metadata![MetadataKeys.title] == null;
  }
  
  // For other sources, check if content is null or empty
  return source.content == null || source.content!.isEmpty;
}
```

### 4.4. PDF Generation with HTML Source Cards

```dart
// Enhanced _saveToPdf method
Future<void> _saveToPdf(BuildContext context) async {
  try {
    // Show loading indicator
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Generating PDF...')),
      );
    }

    // Build HTML content combining source card and response text
    final StringBuilder htmlContent = StringBuilder();
    
    // Add CSS styles
    htmlContent.write('''
    <style>
      /* CSS styles for source cards */
      .source-card {
        display: flex;
        padding: 12px;
        margin-bottom: 16px;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        background-color: #f8f8f8;
      }
      
      /* ... additional CSS styles ... */
    </style>
    ''');
    
    // Add source card HTML if available
    if (viewModel.sources.isNotEmpty) {
      final primarySource = _getPrimarySource(viewModel.sources);
      
      // Only include source card if metadata is available
      if (!_isLoadingMetadata(primarySource)) {
        final sourceCardHtml = SourceCardHtmlGenerator.generateForSource(primarySource);
        htmlContent.write(sourceCardHtml);
        
        // Add separator
        htmlContent.write('<hr style="margin: 16px 0;"/>');
      }
    }
    
    // Add main content as HTML
    final processedText = viewModel.llmResponse!.processResponseText(
      viewModel.clientContextService,
    );
    
    // Convert markdown to HTML for the response content
    final responseHtml = markdownToHtml(processedText);
    htmlContent.write(responseHtml);
    
    // Add provider info
    htmlContent.write('''
    <div style="margin-top: 16px; text-align: right; color: #666; font-size: 12px;">
      Provider: ${_getFormattedProviderText(viewModel.llmResponse!)}
    </div>
    ''');
    
    // Convert HTML to PDF
    final document = await html_pdf.PdfGenerator().generateFromHtml(
      htmlContent.toString(),
      cssStyles: null, // CSS is already included inline
    );
    
    // Get temporary directory for saving the file
    final tempDir = await getTemporaryDirectory();

    // Use the suggested file name from the server response if available
    final fileName = viewModel.llmResponse!.getFullFileName();
    
    // Save PDF to temporary file
    final tempFile = File('${tempDir.path}/$fileName');
    await tempFile.writeAsBytes(await document.save());
    
    // Share the file with other apps
    if (context.mounted) {
      // Clear previous snackbar
      ScaffoldMessenger.of(context).hideCurrentSnackBar();

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('PDF generated successfully')),
      );

      // Share the file
      await Share.shareXFiles(
        [XFile(tempFile.path)],
        text: 'Promz Result',
        subject: fileName.replaceAll('.pdf', ''), 
      );
    }
  } catch (e, stackTrace) {
    // ... error handling ...
  }
}
```

### 4.5. Enhanced Provider Details

```dart
// In execution_results_section.dart
Widget _buildProviderInfo(BuildContext context) {
  final llmResponse = viewModel.llmResponse!;
  final providerText = _getFormattedProviderText(llmResponse);
  
  return Text(
    'Provider: $providerText',
    style: Theme.of(context).textTheme.bodySmall?.copyWith(
      color: Theme.of(context).colorScheme.onSurfaceVariant,
    ),
  );
}

String _getFormattedProviderText(LlmExecuteResponse response) {
  // The server will provide the formatted model name directly from the database
  // which is populated by the LLM pricing tool
  return response.provider;
}
```

## 5. LLM Model Name Updates in Pricing Tool

The LLM pricing tool (`parser.ts`) will be updated to generate user-friendly model names that will be stored in the database and used by the client:

```typescript
// Updated model in parser.ts
interface ModelPricingInfo {
  provider_id: string;
  model_id: string;
  display_name: string; // New field for user-friendly display name
  // ... other fields
}

// Logic to generate user-friendly display names
function generateDisplayName(provider: string, modelId: string): string {
  // For Google models
  if (provider.toLowerCase() === 'google') {
    if (modelId.includes('gemini-1.5-flash')) {
      return 'Gemini 1.5 Flash';
    } else if (modelId.includes('gemini-1.5-pro')) {
      return 'Gemini 1.5 Pro';
    } else if (modelId.includes('gemini-pro')) {
      return 'Gemini Pro';
    }
  }
  
  // For OpenAI models
  if (provider.toLowerCase() === 'openai') {
    if (modelId.includes('gpt-4')) {
      return 'GPT-4';
    } else if (modelId.includes('gpt-3.5') || modelId.includes('gpt-35')) {
      return 'GPT-3.5';
    }
  }
  
  // For other providers
  if (provider.toLowerCase() === 'anthropic') {
    if (modelId.includes('claude-3')) {
      const variant = modelId.includes('sonnet') ? 'Sonnet' : 
                     modelId.includes('haiku') ? 'Haiku' :
                     modelId.includes('opus') ? 'Opus' : '';
      return `Claude 3 ${variant}`.trim();
    }
  }
  
  // Generic fallback format
  return `${provider} ${modelId}`;
}

// Apply during processing
function processModels(models: ModelPricingInfo[]): ModelPricingInfo[] {
  return models.map(model => {
    return {
      ...model,
      display_name: generateDisplayName(model.provider_id, model.model_id),
    };
  });
}
```

When implementing this functionality, we have the option to:

1. Use the automated approach above based on pattern matching
2. Use an LLM to generate more natural names for models
3. Manually curate a mapping of model IDs to display names

The recommended approach is a combination of #1 and #3, where we have automated generation but manually override important models to ensure consistency.

## 6. Home Page Source Card Loading States

When the user shares documents with our app, we'll implement loading states for source cards to provide visual feedback during processing:

### 6.1. SourceList with Loading States

```dart
class SourceList extends StatelessWidget {
  final List<InputSource> sources;
  final Map<String, bool> isSourceLoading; // Track loading state by source ID
  final Function(InputSource) onSourceDeleted;

  const SourceList({
    Key? key,
    required this.sources,
    required this.onSourceDeleted,
    this.isSourceLoading = const {},
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (sources.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          Strings.availableSourcesTitle,
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: sources.length,
          itemBuilder: (context, index) {
            final source = sources[index];
            final sourceId = source.filePath ?? source.contentHash ?? index.toString();
            final isLoading = isSourceLoading[sourceId] ?? false;
            
            if (isLoading) {
              // Show loading skeleton based on source type
              SourceCardType skeletonType;
              
              if (source.type == InputSourceType.youtubeVideo) {
                skeletonType = SourceCardType.youtube;
              } else if (source.type == InputSourceType.news) {
                skeletonType = SourceCardType.news;
              } else {
                skeletonType = SourceCardType.default;
              }
              
              return SourceCardSkeleton(type: skeletonType);
            }
            
            // Use the factory to create the appropriate card for this source
            return SourceCardFactory.createCardForSource(
              source,
              onDelete: () => onSourceDeleted(source),
              context: context,
              index: index,
            );
          },
        ),
      ],
    );
  }
}
```

### 6.2. HomeViewModel Loading State Tracking

```dart
class HomeViewModel extends ChangeNotifier {
  // ... existing fields ...
  
  // Track loading state by source ID (file path or content hash)
  final Map<String, bool> _sourceLoadingStates = {};
  Map<String, bool> get sourceLoadingStates => _sourceLoadingStates;

  // ... existing methods ...
  
  // Updated method to handle source processing with loading state
  Future<bool> processSource(String input, 
      {InputSourceType sourceType = InputSourceType.manual}) async {
    
    appLog.debug('processSource() START - input: $input, sourceType: $sourceType', 
        name: _logName);

    // Generate ID for tracking loading state 
    final tempId = input;
    
    try {
      // Set loading state
      _sourceLoadingStates[tempId] = true;
      notifyListeners();

      // Use the ContentProcessingService to handle all processing
      final contentInfo = await _clientContextService.contentProcessingService.processInput(input);
      
      // ... existing processing logic ...
      
      // Source ID (file path or content hash)
      final sourceId = contentInfo.originalInput;
      
      // Update loading state to the proper ID
      _sourceLoadingStates.remove(tempId);
      _sourceLoadingStates[sourceId] = true;
      notifyListeners();
      
      // ... additional processing ...
      
      // Add to sources with the determined type and metadata
      _sources.add(InputSource(
        type: finalType,
        filePath: contentInfo.originalInput,
        fileName: contentInfo.fileName,
        mimeType: contentInfo.mimeType,
        content: contentInfo.content,
        contentHash: contentInfo.contentHash,
        metadata: metadata,
      ));
      
      // Clear loading state
      _sourceLoadingStates.remove(sourceId);
      notifyListeners();
      
      if (contentInfo.content.isNotEmpty) {
        await analyzeText();
      }

      appLog.debug('processSource() END - success', name: _logName);
      return true;
    } catch (e, stack) {
      // Clear loading state on error
      _sourceLoadingStates.remove(tempId);
      notifyListeners();
      
      appLog.error('processSource() END - Error', name: _logName, error: e, stackTrace: stack);
      return false;
    }
  }
}
```

### 6.3. HomePage Source List Update

```dart
// In home_page.dart
// Update the SourceList usage to include loading states
Widget _buildSourceList() {
  return SourceList(
    sources: viewModel.sources,
    isSourceLoading: viewModel.sourceLoadingStates,
    onSourceDeleted: (source) => viewModel.removeSource(source),
  );
}
```

## 7. HTML-to-Markdown Conversion

In some contexts, we may need to display a simplified version of a source card. For these cases, we'll implement an HTML-to-Markdown converter.

```dart
/// Converts HTML source card to Markdown format
class SourceCardMarkdownConverter {
  static String convertHtmlToMarkdown(String html) {
    // Use a HTML parser to extract key information
    // This is a simplified example - real implementation would use html parser
    
    // Extract title
    final titleMatch = RegExp(r'<h3 class="title">(.*?)</h3>').firstMatch(html);
    final title = titleMatch?.group(1) ?? 'Source';
    
    // Extract subtitle (channel/site/type)
    final subtitleMatch = RegExp(r'<p class="(channel|site|type)">(.*?)</p>').firstMatch(html);
    final subtitle = subtitleMatch?.group(2) ?? '';
    
    // Extract link if present
    final linkMatch = RegExp(r'<p class="link">(.*?)</p>').firstMatch(html);
    final link = linkMatch?.group(1);
    
    // Generate markdown from extracted components
    final markdownBuffer = StringBuffer();
    
    markdownBuffer.writeln('## $title');
    if (subtitle.isNotEmpty) {
      markdownBuffer.writeln('**$subtitle**');
    }
    if (link != null) {
      markdownBuffer.writeln('[Link]($link)');
    }
    markdownBuffer.writeln();
    
    return markdownBuffer.toString();
  }
}
```

## 8. Three-Tiered HTML-Based Approach

### 8.1. HTML as the Source of Truth

Our new approach uses HTML as the source of truth for all rich content, including source cards. This provides several benefits:

1. **Consistency**: Same visual representation across different contexts (UI, PDF)
2. **Flexibility**: HTML provides rich formatting options for complex layouts
3. **Reusability**: One definition that can be rendered in multiple ways

The implementation follows a three-tier approach:

#### 8.1.1. Author & Store as HTML

Rich cards are defined in HTML for full control:

- Support for thumbnails, layout, styles, icons, and more
- Common CSS styles for consistent appearance
- External resources handled properly

#### 8.1.2. Render in Flutter

- Use `flutter_html` to display the cards in the UI
- Apply consistent styling through shared CSS
- Handle interactive elements (links, buttons)
- Support for custom tags and widgets if needed

#### 8.1.3. Convert as Needed

- HTML → PDF: Direct conversion using HTML to PDF library
- HTML → Markdown: When simpler representation is needed
- Support for different output formats from the same source

### 8.2. Technical Feasibility Analysis

The key advantage of using HTML as the source of truth is having a single definition that works across multiple contexts (UI, PDF).

**Advantages:**

- Perfect visual match between UI and PDF outputs
- Rich formatting capabilities
- Common styling across different components
- Better accessibility and semantic structure

**Considerations:**

- HTML parsing and rendering performance
- Handling of external resources (images)
- Styling consistency across platforms
- Interactive element behavior

**Recommendation:** The HTML-based approach provides the best balance of visual consistency, implementation simplicity, and output quality.

## 9. Dependencies

The following dependencies will be needed for implementation:

```yaml
dependencies:
  flutter_html: ^3.0.0              # For rendering HTML in Flutter
  shimmer: ^3.0.0                   # For shimmer loading effects
  htmltopdfwidgets: ^1.0.0          # For converting HTML to PDF widgets
  html_to_markdown: ^1.0.0          # For converting HTML to Markdown
```

## 10. Implementation Plan

### 10.1. Phase 1: Core UI Updates and HTML Source Cards

1. Create `SourceCardHtmlGenerator` class for generating HTML source cards
2. Add `flutter_html` package and configure styles
3. Create loading states with shimmer effects for source cards
4. Update `SourceCardFactory` to support HTML generation
5. Update `ExecutionResultsSection` to include source card display using HTML
6. Modify button visibility and labels
7. Add word/character count display

### 10.2. Phase 2: Provider Details and Animation

1. Update `parser.ts` to generate display_name for LLM models
2. Modify the server API to include model display_name in responses
3. Add animations for state transitions
4. Implement feedback mechanism
5. Add share button functionality

### 10.3. Phase 3: PDF and Topics Integration

1. Implement HTML-based PDF generation
2. Create topic selection dialog
3. Connect share button to topics feature

## 11. Conclusion

The proposed UI improvements to the execution results section will enhance user experience by providing more context about the source content, more intuitive controls, and better integration with the Topics feature.

By using HTML as the source of truth for all rich content, we ensure consistency across different contexts (UI and PDF) while maintaining flexibility and reusability. This approach provides the best balance of visual fidelity, implementation simplicity, and output quality.

The addition of loading states with shimmer effects will significantly improve user feedback during content processing, particularly when sharing content from external sources. The combination of HTML rendering with `flutter_html` and properly designed loading states will create a polished and professional user experience.
