# Promz Design Document: Cross-Platform App Update Handling via Upgrader

**Version:** 1.1
**Date:** 2025-05-09 (Updated)

## Document History

| Version | Date       | Description                                         |
| ------- | ---------- | --------------------------------------------------- |
| 1.0     | 2025-05-09 | Initial design document                             |
| 1.1     | 2025-05-09 | Added application version display in Account view   |

**Objective:**  
 Implement a unified, user-friendly update notification mechanism across all platforms (iOS,
 Android, macOS, Windows, Web) using the `upgrader` Flutter plugin, with future extensibility for
 platform-specific logic and version feed integration (Appcast).

---

## 1. Tools & Plugins

* [Upgrader](https://pub.dev/packages/upgrader): For showing update dialogs.
* [package_info_plus](https://pub.dev/packages/package_info_plus): To retrieve current app version.
* [url_launcher](https://pub.dev/packages/url_launcher): To open app store or update URLs.
* Custom server endpoint or Appcast feed: For desktop platform versioning.

---

## 2. Current Implementation (Phase 1)

* Integrated `upgrader` in the app entry point.
* Default behavior: uses Play Store (Android) and iTunes API (iOS) for version checks.
* Displays update dialog when app version is behind the store version.

---

## 3. Future Extensions (Phase 2+)

### 3.1 Desktop Support (macOS / Windows)

* Implement Appcast XML feed hosted at `https://yourdomain.com/appcast.xml`.
* Configure `Upgrader`:

```dart
Upgrader(  
  useAppcast: true,  
  appcastConfig: AppcastConfiguration(  
    url: '<https://yourdomain.com/appcast.xml>',  
    supportedOS: \['macos', 'windows'\],  
  ),  
)
```

* For macOS: use Mac App Store URL as `enclosure` URL.
* For Windows: use Microsoft Store deep link.

---

### 3.2 Server-Side Version Check (for centralized control)

* Optional REST endpoint: `GET /api/version/latest?platform=android`
* Server responds with:

```json
{  
  "latestVersion": "1.2.0",  
  "minSupportedVersion": "1.0.0",  
  "updateType": "optional",  
  "downloadUrl": "https://..."  
}
```

* Enables custom update logic, analytics, and environment targeting.

---

### 3.3 Web Platform

* Add custom logic to check JavaScript-deployed app version.
* Optionally use service worker version tag or asset manifest versioning.
* Prompt user to refresh or show changelog.

---

## 4. Testing & QA Plan

* Android: Test with Play Store version mismatches.
* iOS: Validate with TestFlight and App Store listings.
* macOS/Windows: Validate Appcast-fed updates (hosted binary or store links).
* Web: Test service worker or client-side version check logic.

---

## 5. Future Considerations

* CI/CD-integrated Appcast generation during release builds.
* Admin dashboard for version control and update messaging.
* User-specific rollout gates (e.g., enterprise-only features).

---

## 6. Conclusion

`Upgrader` provides a consistent, low-friction solution for update prompting. By leveraging Appcast
and conditional platform logic, we can scale version control elegantly across all platforms while giving users a trusted upgrade path.

---

## 7. Application Version Display in Account View

### 7.1 Overview

Add a new section to the Account view that displays the current application version information, providing users with transparency about their installed app version.

### 7.2 Implementation Strategy

* Add a new expandable section titled "Application Information" to the Account view
* Use `package_info_plus` to retrieve app version details
* Display information in a clean, consistent card format matching existing UI patterns
* Optionally include a manual "Check for Updates" button

### 7.3 UI Design

* Follow existing expandable section pattern with icon and title
* Display key information in a card format:
  * App name
  * Version number (e.g., 1.2.3)
  * Build number
  * Platform information
  * Last update check timestamp (optional)

### 7.4 Code Implementation

```dart
// Add a new boolean for section expansion state
bool _isAppInfoSectionExpanded = false;

// Add the section to the Account view
_buildExpandableSection(
  title: 'Application Information',
  icon: Icons.info_outline,
  isExpanded: _isAppInfoSectionExpanded,
  onExpansionChanged: (value) {
    setState(() {
      _isAppInfoSectionExpanded = value;
    });
  },
  children: [
    FutureBuilder<PackageInfo>(
      future: PackageInfo.fromPlatform(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.done) {
          final info = snapshot.data!;
          return Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('App Name: ${info.appName}'),
                  Text('Version: ${info.version}'),
                  Text('Build Number: ${info.buildNumber}'),
                  Text('Platform: ${Platform.operatingSystem}'),
                  const SizedBox(height: 16),
                  ElevatedButton.icon(
                    icon: const Icon(Icons.refresh),
                    label: const Text('Check for Updates'),
                    onPressed: () {
                      // Use the AppUpdateService to check for updates
                      final clientContext = ref.read(clientContextServiceProvider).value;
                      if (clientContext != null) {
                        clientContext.appUpdateService.checkForUpdates(context);
                      }
                    },
                  ),
                ],
              ),
            ),
          );
        }
        return const Center(child: CircularProgressIndicator());
      },
    ),
  ],
);
```

## 8. Next Steps

* Finalize Appcast XML structure and hosting.
* Add platform-specific `url_launcher` logic if needed.
* Plan rollout for desktop and web update flows.
* Integrate server-side version check if more dynamic control is needed.
* Implement the Application Information section in the Account view.
