# Server-Side Processing Offload Design

**Version:** 1.1
**Date:** 2025-04-23

## Document History

| Version | Date       | Description                                        |
| ------- | ---------- | -------------------------------------------------- |
| 1.0     | 2025-04-22 | Initial design document                            |
| 1.1     | 2025-04-23 | Added attachment variable handling and streamlined |

## Overview

This design document outlines a strategy for offloading file processing from the Promz mobile client
to a server-side Go backend. This improvement addresses memory usage and performance issues
experienced on mobile devices when processing large files such as WhatsApp chat exports and other
content.

## Problem Statement

The current implementation of file processing in Promz has several limitations:

1. **Memory Constraints**: Processing large files (especially WhatsApp chat exports and ZIP files)
consumes significant memory on mobile devices, potentially leading to app crashes.

2. **Performance Issues**: Processing text extraction and analysis on the client side can be
CPU-intensive, causing UI freezes and poor user experience.

3. **Battery Drain**: Intensive processing operations decrease battery life on mobile devices.

4. **Scalability Limitations**: As we add more content processing capabilities (e.g., chat
analysis, summarization), client-side processing becomes increasingly unsustainable.

## Goals

1. Move resource-intensive file processing operations to the server
2. Maintain a responsive user interface during processing
3. Design a solution that works for various content types (WhatsApp chats, news articles, etc.)
4. Implement secure, temporary storage for user content
5. Ensure the solution gracefully degrades when offline

## Non-Goals

1. Long-term storage of user content (all data will be temporary)
2. Processing of sensitive user data without explicit consent
3. Replacing all client-side processing (small files can still be processed locally)

## Architecture Overview

### High-Level Architecture

![Server Offload Architecture](https://mermaid.ink/img/pako:eNp1kc1OwzAQhF_F2guVQP2DK3IpB5BijoioG2fjJrb8J9krVFXevU5ahCqB9rbzzc7srvGYKcFYM3N2qWvQhkLBfeWNp1CsmQsd8aMnq-wkktNcAtWSQBlx7vwjNFQXYf0BoWpCi2gYtWGKFaWRbHQk43uwBf8E7Xapn-OuJn0XCepWGXzS1DnI8yLD4m-HnO7QBB_n7Gh5832FpkceosYu2cdkBpshzE3baTJxZvY6flX7Cbf1y9m4zpRZSyT-0UcCc_AxXTY_bP9BN6F6JOVIlDEFHL9T5C_GF3tHYQyWaa4dv_w0Alvy37fLKm_46mgB-fHQYxg_AWvqgXk?type=png)

The system consists of:

1. **Client (Flutter App)**:

   - File selection UI
   - Upload mechanism
   - Status tracking
   - Result display

2. **Go Backend**:

   - File upload handling
   - Content detection
   - Processing services
   - Result management
   - Temporary storage

3. **Google Cloud Storage**:
   - Temporary file storage
   - Automatic expiration

### Process Flow

1. User selects a file in the mobile app
2. Client checks file size and complexity to determine processing location
3. For large files, client uploads to server
4. Server acknowledges receipt and begins processing
5. Client displays processing status
6. Server processes file and stores results temporarily
7. Client fetches results when processing is complete
8. Client displays processed data to user

## Design Details

### 1. API Endpoints

#### Upload Endpoint

```http
POST /upload
```

- Accepts multipart file upload
- Returns processing ID and initial status
- Authentication required

#### Status Endpoint

```http
GET /upload/status/{id}
```

- Returns current processing status
- Supports polling for updates
- Authentication required

#### Results Endpoint

```http
GET /upload/results/{id}
```

- Returns processed content and metadata
- Authentication required

### 2. Client-Side Implementation

#### File Upload Service

The `FileUploadService` will handle communication with the server:

```dart
class FileUploadService {
  static const _logName = 'FileUploadService';
  final http.Client _httpClient;
  final String _baseUrl;

  FileUploadService({
    required String baseUrl,
    http.Client? httpClient,
  }) : _baseUrl = baseUrl,
       _httpClient = httpClient ?? http.Client();

  /// Upload file to server for processing
  Future<FileUploadResponse> uploadFile(File file, {Map<String, dynamic>? metadata}) async {
    try {
      appLog.debug('Uploading file for processing: ${file.path}', name: _logName);

      final uri = Uri.parse('$_baseUrl/upload');

      // Create multipart request
      final request = http.MultipartRequest('POST', uri);

      // Add file
      final fileStream = http.ByteStream(file.openRead());
      final fileLength = await file.length();

      final multipartFile = http.MultipartFile(
        'file',
        fileStream,
        fileLength,
        filename: path.basename(file.path),
      );

      request.files.add(multipartFile);

      // Add metadata if provided
      if (metadata != null) {
        request.fields['metadata'] = jsonEncode(metadata);
      }

      // Send the request
      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      if (response.statusCode != 200) {
        throw Exception('Failed to upload file: ${response.statusCode}');
      }

      // Parse response
      final Map<String, dynamic> responseData = jsonDecode(response.body);

      return FileUploadResponse(
        id: responseData['id'] as String,
        status: responseData['status'] as String,
        estimatedTimeSeconds: responseData['estimatedTimeSeconds'] as int? ?? 10,
      );
    } catch (e, stack) {
      appLog.error('Error uploading file', name: _logName, error: e, stackTrace: stack);
      rethrow;
    }
  }

  /// Check processing status
  Future<FileProcessingStatus> checkStatus(String id) async {
    try {
      appLog.debug('Checking processing status for ID: $id', name: _logName);

      final uri = Uri.parse('$_baseUrl/upload/status/$id');
      final response = await _httpClient.get(uri);

      if (response.statusCode != 200) {
        throw Exception('Failed to check status: ${response.statusCode}');
      }

      final Map<String, dynamic> responseData = jsonDecode(response.body);

      return FileProcessingStatus(
        id: id,
        status: responseData['status'] as String,
        progress: responseData['progress'] as double? ?? 0.0,
        error: responseData['error'] as String?,
      );
    } catch (e, stack) {
      appLog.error('Error checking processing status', name: _logName, error: e, stackTrace: stack);
      rethrow;
    }
  }

  /// Get processing results
  Future<FileProcessingResult> getResults(String id) async {
    try {
      appLog.debug('Getting processing results for ID: $id', name: _logName);

      final uri = Uri.parse('$_baseUrl/upload/results/$id');
      final response = await _httpClient.get(uri);

      if (response.statusCode != 200) {
        throw Exception('Failed to get results: ${response.statusCode}');
      }

      final Map<String, dynamic> responseData = jsonDecode(response.body);

      return FileProcessingResult(
        id: id,
        contentType: responseData['contentType'] as String,
        metadata: responseData['metadata'] as Map<String, dynamic>? ?? {},
        content: responseData['content'] as String?,
        hasFullContent: responseData['hasFullContent'] as bool? ?? true,
        contentUrl: responseData['contentUrl'] as String?,
        expiresAt: responseData['expiresAt'] != null
            ? DateTime.fromMillisecondsSinceEpoch(responseData['expiresAt'] as int)
            : null,
      );
    } catch (e, stack) {
      appLog.error('Error getting processing results', name: _logName, error: e, stackTrace: stack);
      rethrow;
    }
  }

  /// Cancel processing
  Future<bool> cancelProcessing(String id) async {
    try {
      appLog.debug('Cancelling processing for ID: $id', name: _logName);

      final uri = Uri.parse('$_baseUrl/upload/$id');
      final response = await _httpClient.delete(uri);

      return response.statusCode == 200;
    } catch (e, stack) {
      appLog.error('Error cancelling processing', name: _logName, error: e, stackTrace: stack);
      return false;
    }
  }

  void dispose() {
    _httpClient.close();
  }
}
```

#### Response Models

```dart
class FileUploadResponse {
  final String id;
  final String status;
  final int estimatedTimeSeconds;

  FileUploadResponse({
    required this.id,
    required this.status,
    required this.estimatedTimeSeconds,
  });
}

class FileProcessingStatus {
  final String id;
  final String status;
  final double progress;
  final String? error;

  FileProcessingStatus({
    required this.id,
    required this.status,
    required this.progress,
    this.error,
  });

  bool get isCompleted => status == 'completed';
  bool get isFailed => status == 'failed';
  bool get isProcessing => status == 'processing' || status == 'queued';
}

class FileProcessingResult {
  final String id;
  final String contentType;
  final Map<String, dynamic> metadata;
  final String? content;
  final bool hasFullContent;
  final String? contentUrl;
  final DateTime? expiresAt;

  FileProcessingResult({
    required this.id,
    required this.contentType,
    required this.metadata,
    this.content,
    this.hasFullContent = true,
    this.contentUrl,
    this.expiresAt,
  });
}
```

#### Integration with Existing Services

##### Updated ZipService

```dart
class ZipService implements ContainerFileService {
  // Existing code...

  final FileUploadService? _uploadService;
  final int _localProcessingThresholdBytes = 5 * 1024 * 1024; // 5MB threshold

  ZipService(
    this._fileServices,
    this._attachmentRegistry,
    {FileUploadService? uploadService}
  ) : _uploadService = uploadService;

  @override
  Future<List<ProcessedFile>> extractFiles(String path) async {
    try {
      // Check file size
      final file = File(path);
      final fileSize = await file.length();

      // If file is large and we have an upload service, use server-side processing
      if (fileSize > _localProcessingThresholdBytes && _uploadService != null) {
        appLog.debug('File is large (${fileSize}), using server-side processing', name: _logName);
        return _extractFilesWithServer(path);
      }

      // Otherwise use local processing
      return _extractFilesLocally(path);
    } catch (e, stack) {
      appLog.error('Error processing ZIP file', name: _logName, error: e, stackTrace: stack);
      rethrow;
    }
  }

  Future<List<ProcessedFile>> _extractFilesLocally(String path) async {
    // Original implementation moved here
    final bytes = await File(path).readAsBytes();
    final archive = ZipDecoder().decodeBytes(bytes);
    final processedFiles = <ProcessedFile>[];

    // Existing extraction code...

    return processedFiles;
  }

  Future<List<ProcessedFile>> _extractFilesWithServer(String path) async {
    final file = File(path);
    final fileName = path.split(RegExp(r'[/\\]')).last;

    // Upload the file
    final uploadResponse = await _uploadService!.uploadFile(file, metadata: {
      'fileName': fileName,
      'mimeType': getMimeType(path),
      'processingType': 'extract',
    });

    // Create a placeholder processed file that references the server content
    final processedFile = ProcessedFile(
      name: fileName,
      content: 'ID:${uploadResponse.id}',
      isServerProcessed: true,
      serverId: uploadResponse.id,
      metadata: {
        'serverProcessing': true,
        'uploadId': uploadResponse.id,
        'status': 'processing',
        'estimatedTimeSeconds': uploadResponse.estimatedTimeSeconds,
      },
    );

    return [processedFile];
  }
}
```

##### Updated ContentProcessingService

```dart
class ContentProcessingService {
  // Add FileUploadService
  final FileUploadService? _fileUploadService;

  // Update constructor
  ContentProcessingService({
    required FileProcessingService fileProcessingService,
    required AttachmentRegistryService attachmentRegistry,
    required NewsArticleService newsArticleService,
    required ZipService zipService,
    required YouTubeService youtubeService,
    FileUploadService? fileUploadService,
  })  : _fileProcessingService = fileProcessingService,
        _attachmentRegistry = attachmentRegistry,
        _newsArticleService = newsArticleService,
        _zipService = zipService,
        _youtubeService = youtubeService,
        _fileUploadService = fileUploadService;

  // Size threshold for server-side processing in bytes (5MB)
  static const int _serverProcessingThresholdBytes = 5 * 1024 * 1024;

  // Check if a file should be processed server-side
  Future<bool> _shouldUseServerProcessing(String filePath) async {
    // Only use server processing if service is available
    if (_fileUploadService == null) return false;

    try {
      final file = File(filePath);
      if (await file.exists()) {
        final size = await file.length();
        return size > _serverProcessingThresholdBytes;
      }
    } catch (e) {
      appLog.warning('Error checking file size', name: _logName, error: e);
    }

    return false;
  }

  // Process with server for large files
  Future<InputContentInfo> _processWithServer(String filePath) async {
    try {
      appLog.debug('Processing file with server: $filePath', name: _logName);

      final file = File(filePath);
      final fileName = path.basename(filePath);

      // Upload file for processing
      final response = await _fileUploadService!.uploadFile(file);

      // Create a server attachment ID
      final attachmentId = 'server_${response.id}';

      // Register as a server attachment
      _attachmentRegistry.registerServerAttachment(
        id: attachmentId,
        serverId: response.id,
        type: 'server_processed',
        fileName: fileName,
        metadata: {
          'originalPath': filePath,
          'serverProcessing': true,
          'processingId': response.id,
          'status': response.status,
          'uploadTime': DateTime.now().millisecondsSinceEpoch,
        },
      );

      // Return minimal content info with server reference
      return InputContentInfo(
        type: InputType.file,
        originalInput: filePath,
        fileName: fileName,
        mimeType: _fileProcessingService.getMimeType(fileName),
        content: 'ID:${response.id}', // Use ID reference format
        contentHash: HashHelper.calculateStringHash(filePath), // Use file path as hash
        sourceType: InputSourceType.serverProcessed, // New source type
        attachmentId: attachmentId,
        isServerProcessed: true,
        processingId: response.id,
        metadata: {
          'serverProcessing': true,
          'processingId': response.id,
          'status': response.status,
        },
      );
    } catch (e, stack) {
      appLog.error('Error processing with server', name: _logName, error: e, stackTrace: stack);
      rethrow;
    }
  }

  // Update processInput to check file size and use server when appropriate
  Future<InputContentInfo> processInput(String input) async {
    appLog.debug('Processing input: ${_truncateForLogging(input)}', name: _logName);

    // Detect content type
    final detection = await _detectContentType(input);
    appLog.debug('Content detection result: $detection', name: _logName);

    // For file types, check if we should use server processing
    if (detection.primaryType == ContentType.file || detection.primaryType == ContentType.whatsapp) {
      try {
        // Check if this is a file path
        final file = File(input);
        if (await file.exists() && await _shouldUseServerProcessing(input)) {
          appLog.debug('Using server-side processing for large file', name: _logName);
          return _processWithServer(input);
        }
      } catch (e) {
        // Not a valid file path, continue with regular processing
        appLog.debug('Not a valid file path for server processing', name: _logName);
      }
    }

    // Continue with existing processing logic
    // ... (existing code)
  }
}
```

##### Updated AttachmentRegistryService

```dart
class AttachmentRegistryService {
  // Existing code...

  /// Register a server-side attachment reference
  void registerServerAttachment({
    required String id,
    required String serverId,
    required String type,
    required String fileName,
    Map<String, dynamic>? metadata,
  }) {
    appLog.debug('Registering server attachment: $id, serverId: $serverId, type: $type', name: _logName);

    final enhancedMetadata = metadata ?? {};
    enhancedMetadata['isServerProcessed'] = true;
    enhancedMetadata['serverId'] = serverId;

    _attachmentContents[id] = {
      'content': 'ID:$serverId', // Use ID reference format
      'type': type,
      'fileName': fileName,
      'displayName': _extractDisplayName(fileName, enhancedMetadata),
      'metadata': enhancedMetadata,
      'timestamp': DateTime.now().toIso8601String(),
      'isServerProcessed': true,
      'serverId': serverId,
    };
  }

  /// Check if an attachment content is a server reference
  bool isServerReference(String content) {
    return content.startsWith('ID:') && content.length > 3;
  }

  /// Extract server ID from reference
  String? extractServerId(String content) {
    if (isServerReference(content)) {
      return content.substring(3); // Remove 'ID:' prefix
    }
    return null;
  }

  /// Get attachment content, potentially from server
  Future<String?> getAttachmentContent(String id, FileUploadService? uploadService) async {
    final attachment = _attachmentContents[id];
    if (attachment == null) return null;

    final content = attachment['content'] as String?;
    if (content == null) return null;

    // Check if it's a server reference
    if (isServerReference(content) && uploadService != null) {
      final serverId = extractServerId(content);
      if (serverId != null) {
        try {
          // Fetch content from server
          final result = await uploadService.getResults(serverId);
          return result.content;
        } catch (e) {
          appLog.error('Failed to fetch server content for ID: $serverId',
              name: _logName, error: e);
          return 'Error loading content from server: ${e.toString()}';
        }
      }
    }

    // Return local content
    return content;
  }
}
```

### 3. Server-Side Implementation

#### Server Components

The Go backend will consist of the following components:

1. **Upload Handler**: Manages file uploads and initiates processing
2. **Storage Service**: Interfaces with Google Cloud Storage
3. **Content Processor**: Generic interface for content processing
4. **Type-Specific Processors**: Implementations for different content types

#### Upload Handler

```go
// upload_handler.go
package processing

import (
    "context"
    "encoding/json"
    "fmt"
    "io"
    "net/http"
    "os"
    "path/filepath"
    "time"

    "cloud.google.com/go/storage"
    "github.com/google/uuid"
    "github.com/gorilla/mux"
)

// ProcessingStatus represents the current status of file processing
type ProcessingStatus string

const (
    StatusQueued     ProcessingStatus = "queued"
    StatusProcessing ProcessingStatus = "processing"
    StatusCompleted  ProcessingStatus = "completed"
    StatusFailed     ProcessingStatus = "failed"
)

// ProcessingJob contains information about a file processing job
type ProcessingJob struct {
    ID        string          `json:"id"`
    FileName  string          `json:"fileName"`
    MimeType  string          `json:"mimeType"`
    Status    ProcessingStatus `json:"status"`
    Progress  float64         `json:"progress"`
    Error     string          `json:"error,omitempty"`
    CreatedAt time.Time       `json:"createdAt"`
    UpdatedAt time.Time       `json:"updatedAt"`
    ExpiresAt time.Time       `json:"expiresAt"`
    FilePath  string          `json:"-"` // Internal use only
    ResultID  string          `json:"resultId,omitempty"`
}

// UploadHandler handles file uploads and processing
type UploadHandler struct {
    StorageService *StorageService
    Processor      ContentProcessor
    jobs           map[string]*ProcessingJob
}

// NewUploadHandler creates a new upload handler
func NewUploadHandler(storageService *StorageService, processor ContentProcessor) *UploadHandler {
    return &UploadHandler{
        StorageService: storageService,
        Processor:      processor,
        jobs:           make(map[string]*ProcessingJob),
    }
}

// HandleUpload handles file upload requests
func (h *UploadHandler) HandleUpload(w http.ResponseWriter, r *http.Request) {
    // Parse multipart form
    err := r.ParseMultipartForm(32 << 20) // 32MB max memory
    if err != nil {
        http.Error(w, "Error parsing multipart form: "+err.Error(), http.StatusBadRequest)
        return
    }

    // Get file
    file, header, err := r.FormFile("file")
    if err != nil {
        http.Error(w, "Error getting file: "+err.Error(), http.StatusBadRequest)
        return
    }
    defer file.Close()

    // Create a unique ID for this job
    id := uuid.New().String()

    // Get metadata
    var metadata map[string]interface{}
    metadataStr := r.FormValue("metadata")
    if metadataStr != "" {
        if err := json.Unmarshal([]byte(metadataStr), &metadata); err != nil {
            http.Error(w, "Error parsing metadata: "+err.Error(), http.StatusBadRequest)
            return
        }
    }

    // Store file in temp location
    tempFilePath := filepath.Join(os.TempDir(), id+filepath.Ext(header.Filename))
    tempFile, err := os.Create(tempFilePath)
    if err != nil {
        http.Error(w, "Error creating temp file: "+err.Error(), http.StatusInternalServerError)
        return
    }

    _, err = io.Copy(tempFile, file)
    tempFile.Close()
    if err != nil {
        http.Error(w, "Error saving temp file: "+err.Error(), http.StatusInternalServerError)
        return
    }

    // Upload to GCS
    objectName := fmt.Sprintf("uploads/%s%s", id, filepath.Ext(header.Filename))
    if err := h.StorageService.UploadFile(r.Context(), tempFilePath, objectName); err != nil {
        http.Error(w, "Error uploading to storage: "+err.Error(), http.StatusInternalServerError)
        return
    }

    // Create job
    job := &ProcessingJob{
        ID:        id,
        FileName:  header.Filename,
        MimeType:  header.Header.Get("Content-Type"),
        Status:    StatusQueued,
        Progress:  0.0,
        CreatedAt: time.Now(),
        UpdatedAt: time.Now(),
        ExpiresAt: time.Now().Add(24 * time.Hour), // Expire after 24 hours
        FilePath:  objectName,
    }
    h.jobs[id] = job

    // Start processing in background
    go func() {
        // Update status to processing
        h.updateJobStatus(id, StatusProcessing, 0.1, "")

        // Process file
        resultID, err := h.Processor.ProcessFile(context.Background(), job, metadata)
        if err != nil {
            h.updateJobStatus(id, StatusFailed, 0.0, err.Error())
            return
        }

        // Update job with result ID
        h.jobs[id].ResultID = resultID
        h.updateJobStatus(id, StatusCompleted, 1.0, "")
    }()

    // Return response
    response := map[string]interface{}{
        "id":                  id,
        "status":              string(job.Status),
        "estimatedTimeSeconds": 30, // Default estimate
    }

    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(response)
}

// HandleStatus handles status check requests
func (h *UploadHandler) HandleStatus(w http.ResponseWriter, r *http.Request) {
    vars := mux.Vars(r)
    id := vars["id"]

    job, exists := h.jobs[id]
    if !exists {
        http.Error(w, "Job not found", http.StatusNotFound)
        return
    }

    response := map[string]interface{}{
        "id":       job.ID,
        "status":   string(job.Status),
        "progress": job.Progress,
    }

    if job.Error != "" {
        response["error"] = job.Error
    }

    if job.ResultID != "" {
        response["resultId"] = job.ResultID
    }

    w.Header().Set("Content-Type", "application/json")
    json.NewEncoder(w).Encode(response)
}

// HandleResults handles result retrieval requests
func (h *UploadHandler) HandleResults(w http.ResponseWriter, r *http.Request) {
    vars := mux.Vars(r)
    id := vars["id"]

    job, exists := h.jobs[id]
    if !exists {
        http.Error(w, "Job not found", http.StatusNotFound)
        return
    }

    if job.Status != StatusCompleted {
        http.Error(w, "Processing not complete", http.StatusBadRequest)
        return
    }

    // Retrieve results from storage
    result, err := h.Processor.GetResults(r.Context(), job)
    if err != nil {
  http.Error(w, "Error retrieving results: "+err.Error(), http.StatusInternalServerError)
  return
 }

 w.Header().Set("Content-Type", "application/json")
 json.NewEncoder(w).Encode(result)
}

// Helper method to update job status
func (h *UploadHandler) updateJobStatus(id string, status ProcessingStatus, progress float64, errorMsg string) {
    job, exists := h.jobs[id]
    if !exists {
        return
    }

    job.Status = status
    job.Progress = progress
    job.Error = errorMsg
    job.UpdatedAt = time.Now()
}
```

#### Content Processor Interface

```go
// processor.go
package processing

import (
    "context"
    "io"
)

// ContentType represents the detected content type
type ContentType string

const (
 ContentTypeUnknown    ContentType = "unknown"
 ContentTypeText       ContentType = "text"
 ContentTypeZip        ContentType = "zip"
 ContentTypeWhatsApp   ContentType = "whatsapp"
 ContentTypePDF        ContentType = "pdf"
 ContentTypeNewsArticle ContentType = "news"
)

// ProcessingResult contains the results of content processing
type ProcessingResult struct {
 ID          string                 `json:"id"`
 ContentType ContentType            `json:"contentType"`
 Content     string                 `json:"content,omitempty"`
 Metadata    map[string]interface{} `json:"metadata,omitempty"`
 Error       string                 `json:"error,omitempty"`
}

// ContentProcessor is the interface for content processors
type ContentProcessor interface {
 // ProcessFile processes a file and returns a result ID
 ProcessFile(ctx context.Context, job *ProcessingJob, metadata map[string]interface{}) (string, error)

 // GetResults retrieves processing results by job
 GetResults(ctx context.Context, job *ProcessingJob) (*ProcessingResult, error)

 // DetectContentType detects the content type of a file
 DetectContentType(ctx context.Context, reader io.Reader, filename string) (ContentType, error)
}
```

#### Storage Service

```go
// storage_service.go
package processing

import (
    "context"
    "fmt"
    "io"
    "os"
    "time"

 "cloud.google.com/go/storage"
)

// StorageService handles interactions with Google Cloud Storage
type StorageService struct {
 client     *storage.Client
 bucketName string
}

// NewStorageService creates a new storage service
func NewStorageService(ctx context.Context, bucketName string) (*StorageService, error) {
    client, err := storage.NewClient(ctx)
    if err != nil {
        return nil, fmt.Errorf("failed to create storage client: %v", err)
    }

    return &StorageService{
  client:     client,
  bucketName: bucketName,
 }, nil
}

// UploadFile uploads a file to GCS
func (s *StorageService) UploadFile(ctx context.Context, filePath string, objectName string) error {
    f, err := os.Open(filePath)
    if err != nil {
        return fmt.Errorf("failed to open file: %v", err)
    }
    defer f.Close()

    bucket := s.client.Bucket(s.bucketName)
    obj := bucket.Object(objectName)

    // Set expiration time (24 hours)
    attrs := storage.ObjectAttrsToUpdate{
        Metadata: map[string]string{
            "expiration": time.Now().Add(24 * time.Hour).Format(time.RFC3339),
        },
    }
    if err := obj.Update(ctx, attrs); err != nil {
        return fmt.Errorf("failed to set object attributes: %v", err)
    }

    // Upload the file
    w := obj.NewWriter(ctx)
    if _, err := io.Copy(w, f); err != nil {
        return fmt.Errorf("failed to copy file to storage: %v", err)
    }
    if err := w.Close(); err != nil {
        return fmt.Errorf("failed to finalize upload: %v", err)
    }

    return nil
}

// DownloadFile downloads a file from GCS
func (s *StorageService) DownloadFile(ctx context.Context, objectName string, destPath string) error {
    bucket := s.client.Bucket(s.bucketName)
    obj := bucket.Object(objectName)

    r, err := obj.NewReader(ctx)
    if err != nil {
        return fmt.Errorf("failed to open object: %v", err)
    }
    defer r.Close()

    f, err := os.Create(destPath)
    if err != nil {
        return fmt.Errorf("failed to create destination file: %v", err)
    }
    defer f.Close()

    if _, err := io.Copy(f, r); err != nil {
        return fmt.Errorf("failed to copy object to file: %v", err)
    }

    return nil
}

// GetObject gets an object from GCS as a reader
func (s *StorageService) GetObject(ctx context.Context, objectName string) (io.ReadCloser, error) {
    bucket := s.client.Bucket(s.bucketName)
    obj := bucket.Object(objectName)

    r, err := obj.NewReader(ctx)
    if err != nil {
        return nil, fmt.Errorf("failed to open object: %v", err)
    }
    return r, nil
}

// SaveResult saves processing results to GCS
func (s *StorageService) SaveResult(ctx context.Context, id string, result *ProcessingResult) error {
    bucket := s.client.Bucket(s.bucketName)
    obj := bucket.Object(fmt.Sprintf("results/%s", id))

    w := obj.NewWriter(ctx)
    defer w.Close()

    // Set expiration time (24 hours)
    attrs := storage.ObjectAttrsToUpdate{
        Metadata: map[string]string{
            "expiration": time.Now().Add(24 * time.Hour).Format(time.RFC3339),
        },
    }
    if err := obj.Update(ctx, attrs); err != nil {
        return fmt.Errorf("failed to set object attributes: %v", err)
    }

    // Convert result to JSON and save
    encoder := json.NewEncoder(w)
    if err := encoder.Encode(result); err != nil {
        return fmt.Errorf("failed to encode result: %v", err)
    }
    return nil
}

// GetResult retrieves processing results from GCS
func (s *StorageService) GetResult(ctx context.Context, id string) (*ProcessingResult, error) {
    bucket := s.client.Bucket(s.bucketName)
    obj := bucket.Object(fmt.Sprintf("results/%s", id))

    r, err := obj.NewReader(ctx)
    if err != nil {
        return nil, fmt.Errorf("failed to open result object: %v", err)
    }
    defer r.Close()

    var result ProcessingResult
    decoder := json.NewDecoder(r)
    if err := decoder.Decode(&result); err != nil {
        return nil, fmt.Errorf("failed to decode result: %v", err)
    }

    return &result, nil
}

// DeleteObject deletes an object from GCS
func (s *StorageService) DeleteObject(ctx context.Context, objectName string) error {
    bucket := s.client.Bucket(s.bucketName)
    obj := bucket.Object(objectName)

    if err := obj.Delete(ctx); err != nil {
        return fmt.Errorf("failed to delete object: %v", err)
    }

    return nil
}

// Close closes the storage client
func (s *StorageService) Close() error {
    return s.client.Close()
}
```

#### Server Router Setup

```go
// server.go
package main

import (
    "context"
    "log"
    "net/http"
    "os"
    "time"

    "github.com/gorilla/mux"
    "github.com/promz/api/processing"
)

func main() {
    ctx := context.Background()

    // Get bucket name from environment
    bucketName := os.Getenv("STORAGE_BUCKET")
    if bucketName == "" {
        log.Fatal("STORAGE_BUCKET environment variable not set")
    }

    // Create storage service
    storageService, err := processing.NewStorageService(ctx, bucketName)
    if err != nil {
        log.Fatalf("Failed to create storage service: %v", err)
    }
    defer storageService.Close()

    // Create content processor
    processor := processing.NewCompositeContentProcessor(storageService)

    // Create upload handler
    uploadHandler := processing.NewUploadHandler(storageService, processor)

    // Create router
    r := mux.NewRouter()

    // Register routes
    r.HandleFunc("/api/v1/upload", uploadHandler.HandleUpload).Methods("POST")
    r.HandleFunc("/api/v1/upload/status/{id}", uploadHandler.HandleStatus).Methods("GET")
    r.HandleFunc("/api/v1/upload/results/{id}", uploadHandler.HandleResults).Methods("GET")

    // Start server
    srv := &http.Server{
        Handler:      r,
        Addr:         ":8080",
        WriteTimeout: 15 * time.Second,
        ReadTimeout:  15 * time.Second,
    }

    log.Println("Server started on :8080")
    log.Fatal(srv.ListenAndServe())
}
```

### 4. File Type-Specific Processors

#### WhatsApp Chat Processor

```go
// whatsapp_processor.go
package processing

import (
    "archive/zip"
    "context"
    "fmt"
    "io"
    "io/ioutil"
    "os"
    "path/filepath"
    "regexp"
    "strings"
    "time"
)

// WhatsAppProcessor processes WhatsApp chat exports
type WhatsAppProcessor struct {
    storageService *StorageService
}

// NewWhatsAppProcessor creates a new WhatsApp processor
func NewWhatsAppProcessor(storageService *StorageService) *WhatsAppProcessor {
    return &WhatsAppProcessor{
        storageService: storageService,
    }
}

// ProcessFile processes a WhatsApp chat file
func (p *WhatsAppProcessor) ProcessFile(ctx context.Context, job *ProcessingJob, metadata map[string]interface{}) (string, error) {
    // Download the file from storage
    tempFilePath := filepath.Join(os.TempDir(), job.ID+filepath.Ext(job.FileName))
    defer os.Remove(tempFilePath)

    if err := p.storageService.DownloadFile(ctx, job.FilePath, tempFilePath); err != nil {
        return "", fmt.Errorf("failed to download file: %v", err)
    }

    // Check if it's a ZIP file
    var content string
    var extractedMetadata map[string]interface{}
}

// Check if it's a ZIP file
var content string
var extractedMetadata map[string]interface{}

if strings.HasSuffix(strings.ToLower(job.FileName), ".zip") {
    // Process as ZIP file
    chatContent, chatMetadata, err := p.processZipFile(tempFilePath)
    if err != nil {
        return "", fmt.Errorf("failed to process ZIP file: %v", err)
    }
    content = chatContent
    extractedMetadata = chatMetadata
} else {
    // Process as text file
    chatContent, chatMetadata, err := p.processTextFile(tempFilePath)
    if err != nil {
        return "", fmt.Errorf("failed to process text file: %v", err)
    }
    content = chatContent
    extractedMetadata = chatMetadata
}

// Create result
result := &ProcessingResult{
    ID:          job.ID,
    ContentType: ContentTypeWhatsApp,
    Content:     content,
    Metadata:    extractedMetadata,
}

// Save result
if err := p.storageService.SaveResult(ctx, job.ID, result); err != nil {
    return "", fmt.Errorf("failed to save result: %v", err)
}

return job.ID, nil
}

// GetResults retrieves processing results
func (p *WhatsAppProcessor) GetResults(ctx context.Context, job *ProcessingJob) (*ProcessingResult, error) {
 return p.storageService.GetResult(ctx, job.ID)
}

// ProcessZipFile processes a ZIP file containing WhatsApp chat
func (p *WhatsAppProcessor) processZipFile(filePath string) (string, map[string]interface{}, error) {
    // Open the zip file
    reader, err := zip.OpenReader(filePath)
    if err != nil {
        return "", nil, fmt.Errorf("failed to open zip file: %v", err)
    }
    defer reader.Close()

    // Look for WhatsApp chat files
    var chatContent string
    for _, file := range reader.File {
        fileName := strings.ToLower(file.Name)
        if strings.Contains(fileName, "chat") && strings.HasSuffix(fileName, ".txt") {
            // Open the file
            rc, err := file.Open()
            if err != nil {
                continue
            }
            defer rc.Close()

            // Read the content
            bytes, err := ioutil.ReadAll(rc)
            if err != nil {
                continue
            }

            chatContent = string(bytes)
            break
        }
    }

    // No chat file found
    if chatContent == "" {
        return "", nil, fmt.Errorf("no WhatsApp chat file found in ZIP")
    }

    // Extract metadata from the content
    metadata := p.extractChatMetadata(chatContent)

    return chatContent, metadata, nil
}

// ProcessTextFile processes a WhatsApp chat text file
func (p *WhatsAppProcessor) processTextFile(filePath string) (string, map[string]interface{}, error) {
    // Read the file
    content, err := ioutil.ReadFile(filePath)
    if err != nil {
        return "", nil, fmt.Errorf("failed to read text file: %v", err)
    }

    chatContent := string(content)

    // Extract metadata from the content
    metadata := p.extractChatMetadata(chatContent)

    return chatContent, metadata, nil
}

// ExtractChatMetadata extracts metadata from WhatsApp chat content
func (p *WhatsAppProcessor) extractChatMetadata(content string) map[string]interface{} {
    lines := strings.Split(content, "\n")
    participants := make(map[string]bool)
    var messageCount, mediaCount int
    var startDate, endDate time.Time

    // WhatsApp date format regex (covers most common formats)
    dateRegex := regexp.MustCompile(`(\d{1,2}[./]\d{1,2}[./]\d{2,4}),? (\d{1,2}:\d{2}(?::\d{2})?) ?(?:[AP]M)? - `)
    participantRegex := regexp.MustCompile(` - (.*?): `)

    // Extract group name
    groupName := p.extractGroupName(content)

    for _, line := range lines {
        dateMatch := dateRegex.FindStringSubmatch(line)
        if dateMatch != nil {
            messageCount++

            // Try to parse the date
            if len(dateMatch) >= 3 {
                dateStr := dateMatch[1]
                timeStr := dateMatch[2]

                date := p.parseDateTime(dateStr, timeStr)
                if !date.IsZero() {
                    if startDate.IsZero() || date.Before(startDate) {
                        startDate = date
                    }
                    if endDate.IsZero() || date.After(endDate) {
                        endDate = date
                    }
                }
            }
        }

        participantMatch := participantRegex.FindStringSubmatch(line)
        if participantMatch != nil && len(participantMatch) >= 2 {
            participant := participantMatch[1]
            participants[participant] = true
        }

        if strings.Contains(line, "<Media omitted>") {
            mediaCount++
        }
    }

    // Convert participants map to slice
    participantsList := make([]string, 0, len(participants))
    for participant := range participants {
        participantsList = append(participantsList, participant)
    }

    // Create metadata
    metadata := map[string]interface{}{
        "participants":    participantsList,
        "participantCount": len(participantsList),
        "messageCount":    messageCount,
        "mediaCount":      mediaCount,
    }

    if !startDate.IsZero() {
        metadata["startDate"] = startDate.Format(time.RFC3339)
    }

    if !endDate.IsZero() {
        metadata["endDate"] = endDate.Format(time.RFC3339)
    }

    if startDate.Year() > 1 && endDate.Year() > 1 {
        metadata["duration"] = endDate.Sub(startDate).Hours() / 24
    }

    if groupName != "" {
        metadata["groupName"] = groupName
    }

    return metadata
}

// ExtractGroupName extracts the group name from chat content
func (p *WhatsAppProcessor) extractGroupName(content string) string {
    // Look at first few lines
    lines := strings.Split(content, "\n")
    if len(lines) > 10 {
        lines = lines[:10]
    }

    // Common patterns in WhatsApp exports
    patterns := []*regexp.Regexp{
        regexp.MustCompile(`Group: "(.*?)"`),
        regexp.MustCompile(`Chat: "(.*?)"`),
        regexp.MustCompile(`WhatsApp Chat - (.*?)$`),
        regexp.MustCompile(`\[.*?\] .* created group "(.*?)"`),
    }

    for _, line := range lines {
        for _, pattern := range patterns {
            match := pattern.FindStringSubmatch(line)
            if len(match) >= 2 && match[1] != "" {
                return strings.TrimSpace(match[1])
            }
        }
    }

    return ""
}

// ParseDateTime tries to parse a date and time string
func (p *WhatsAppProcessor) parseDateTime(dateStr, timeStr string) time.Time {
    // Try different date formats
    formats := []string{
        "02/01/2006 15:04",
        "1/2/2006 15:04",
        "02/01/06 15:04",
        "1/2/06 15:04",
        "02.01.2006 15:04",
        "1.2.2006 15:04",
        "02.01.06 15:04",
        "1.2.06 15:04",
    }

    for _, format := range formats {
        date, err := time.Parse(format, dateStr+" "+timeStr)
        if err == nil {
            return date
        }
    }

    return time.Time{}
}
```

## User Experience Considerations

### Progress Indication

1. **Upload Progress**: Show upload progress during the initial file transfer
2. **Processing Status**: Display a progress indicator during server-side processing
3. **Timeout Handling**: Provide feedback if processing takes longer than expected

### Error Handling

1. **Upload Failures**: Graceful handling of network failures during upload
2. **Processing Errors**: Clear error messages for various processing failures
3. **Fallback Mechanism**: Option to try client-side processing if server processing fails

### Offline Support

1. **Connection Detection**: Detect when the app is offline
2. **Automatic Fallback**: Use client-side processing when offline
3. **Upload Queue**: Option to queue large files for processing when back online

## Security Considerations

### Data Protection

1. **Encryption**: All data is encrypted in transit and at rest
2. **Authentication**: All API endpoints require authentication
3. **Authorization**: Users can only access their own data
4. **Data Retention**: Automatic deletion of files after 24 hours

### Privacy Considerations

1. **Minimal Data Collection**: Only process the data required for the task
2. **User Consent**: Clear indication that files will be processed on the server
3. **Transparency**: Clear policies on how data is handled

## Implementation Timeline

### Phase 1: Core Infrastructure (2 weeks)

1. Set up Google Cloud Storage bucket with appropriate lifecycle policies
2. Implement Go server upload and processing endpoints
3. Create client-side FileUploadService and response models
4. Basic WhatsApp content processor implementation

### Phase 2: Integration with Existing Services (2 weeks)

1. Update ZipService to use server-side processing
2. Update ContentProcessingService to use server offloading
3. Implement AttachmentRegistryService enhancements
4. Add error handling and fallback mechanisms

### Phase 3: Testing and Optimization (2 weeks)

1. Performance testing with various file sizes
2. Security review and hardening
3. User experience optimization
4. Documentation and deployment guides

### Phase 4: Additional Content Processors (2 weeks)

1. PDF content processor
2. News article processor
3. YouTube video processor
4. Generic text processor

## Attachment Variable Handling

### Overview

To support seamless integration of server-processed attachments with the LLM prompt execution flow, we need a standardized way to reference attachments in prompts and convert them to their contents when executing the prompt. This section outlines the design for handling attachment variables.

### Client-Side Implementation

#### Attachment Variable Format

When an attachment is processed by the server, it will be registered with a variable in the following format:

- Single attachment: `ATTACHMENT:UPLOAD_ID`
- Multiple attachments: `{#1}ATTACHMENT:UPLOAD_ID`, `{#2}ATTACHMENT:UPLOAD_ID`, etc.

Where `UPLOAD_ID` is the server-side job ID for the processed attachment.

#### Implementation Details

1. **AttachmentRegistryService**:
   - Added `getAllServerAttachments()` method to retrieve all server-processed attachments
   - Server attachments are stored with their server ID and metadata

2. **ClientContextService**:
   - Added `_addServerAttachmentVariables()` method to add attachment variables to the context
   - Handles both single and multiple attachment scenarios with appropriate variable naming
   - Integrates with existing variable resolution mechanism

```dart
// Example of adding server attachment variables
void _addServerAttachmentVariables(Map<String, String> variables) {
  final serverAttachments = attachmentRegistry.getAllServerAttachments();
  
  if (serverAttachments.isEmpty) return;
  
  if (serverAttachments.length == 1) {
    // Single attachment - use simple format
    final attachment = serverAttachments.first;
    final serverId = attachment['serverId'];
    variables['ATTACHMENT:UPLOAD_ID'] = serverId;
  } else {
    // Multiple attachments - use prefix notation
    for (int i = 0; i < serverAttachments.length; i++) {
      final attachment = serverAttachments[i];
      final serverId = attachment['serverId'];
      variables['{#${i+1}}ATTACHMENT:UPLOAD_ID'] = serverId;
    }
  }
}
```

### Server-Side Implementation

#### Variable Conversion Middleware

A new middleware component will be added to the server to handle the conversion of attachment variables:

```go
// AttachmentVariableProcessor processes attachment variables in the request
func AttachmentVariableProcessor(next http.Handler) http.Handler {
  return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
    // Extract request body
    var requestBody map[string]interface{}
    // ...
    
    // Process variables in the request
    processAttachmentVariables(requestBody)
    
    // Continue with the processed request
    next.ServeHTTP(w, r)
  })
}

// Process attachment variables in the request body
func processAttachmentVariables(body map[string]interface{}) {
  variables := body["variables"].(map[string]interface{})
  
  for key, value := range variables {
    // Check for attachment variables
    if strings.Contains(key, "ATTACHMENT:UPLOAD_ID") {
      uploadId := value.(string)
      content := fetchAttachmentContent(uploadId)
      
      // Replace UPLOAD_ID with CONTENTS
      newKey := strings.Replace(key, "UPLOAD_ID", "CONTENTS", 1)
      variables[newKey] = content
    }
  }
}
```

#### Integration with Existing Processing Flow

The attachment variable processor will be integrated into the existing request processing flow:

1. Client sends a request with variables including `ATTACHMENT:UPLOAD_ID`
2. Server middleware detects these variables
3. For each attachment variable, the server fetches the corresponding content
4. Variables are converted from `ATTACHMENT:UPLOAD_ID` to `ATTACHMENT:CONTENTS`
5. The processed request continues through the normal execution flow

## Conclusion

This server-side processing offload design will significantly improve the user experience of the Promz application by:

1. Reducing memory usage on mobile devices
2. Improving application responsiveness
3. Supporting processing of larger files
4. Enabling more advanced processing features in the future
5. Providing seamless integration of server-processed attachments with LLM prompts

The implementation follows a phased approach that allows for incremental deployment and testing. By utilizing Google Cloud Storage with automatic expiration policies, we ensure that user data is handled securely and only stored for the minimum time necessary.

This architecture also provides a foundation for future enhancements to the content processing capabilities of the application without compromising mobile performance.
