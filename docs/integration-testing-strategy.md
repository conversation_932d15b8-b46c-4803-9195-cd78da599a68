# Promz Client Integration Testing Strategy

**Version:** 1.0
**Date:** 2025-04-30

## Document History

| Version | Date       | Description                                                             |
| ------- | ---------- | ----------------------------------------------------------------------- |
| 1.0     | 2025-04-30 | Initial integration testing strategy document for client-side services. |

## 1. Overview

This document outlines the strategy for integration testing key services within the Promz Flutter
client application. Unlike unit tests which isolate components, these integration tests verify the
collaboration between multiple components, including interactions with the file system and real
network services (where specified).

The initial focus is on creating integration tests for the `ContentProcessingService` to validate
its core orchestration logic with more realistic dependencies.

## 2. Testing Goals

1. **End-to-End Validation (Service Level):** Verify that `ContentProcessingService` correctly
orchestrates interactions between its internal logic and its direct dependencies for various input
types.
2. **Real-World Scenario Simulation:** Test the service under conditions closer to production,
including actual file I/O and network calls to external services (YouTube, News APIs, Promz backend).
3. **Regression Detection:** Catch regressions in the interaction points between
`ContentProcessingService` and its dependencies, especially those involving network or file system
operations.

## 3. Scope

### In Scope

* Testing the `processInput` method of `ContentProcessingService`.
* Verifying the correct delegation to internal processing methods (`_processYouTubeVideo`,
`_processNewsArticle`, `_processFile`, `_processText`, `_processUrlInput`).
* Testing with various input types:
  * Valid file paths (using temporary real files).
  * URLs (YouTube, News Articles, Generic).
  * Direct text content.
* Interaction with the real `DuplicateDetectionRegistry`.
* Interaction with a **mocked** `LicenseManagerService`.
* Interaction with **real** network-dependent services:
  * `YouTubeService`
  * `NewsArticleService`
  * `FileUploadService` (including `validateFileSize`)
  * `FileProcessingClient`
  * `UrlResolver` (and its underlying HTTP client)
* Interaction with the real file system for file path inputs.
* Running tests on desktop platforms (Windows/macOS).

### Out of Scope

* Unit testing individual helper methods within `ContentProcessingService` (these should have
separate unit tests if complex).
* Testing the UI layer that *calls* `ContentProcessingService`.
* Exhaustive testing of every possible edge case of the *external* services (e.g., every YouTube
API error). The focus is on the client's interaction *with* them.
* Mobile-specific platform integration testing (e.g., specific Android/iOS file permissions,
secure storage implementations).
* Performance testing (these tests prioritize functional correctness over speed).
* Running these specific tests as part of the standard CI pipeline on every commit (due to potential
slowness and flakiness).

## 4. Testing Approach

### 4.1. Test Type

* **Flutter Integration Tests:** Leveraging the `flutter_test` framework but focusing on
service-level integration rather than UI interaction.

### 4.2. Test Location

* Tests will reside in a dedicated directory to distinguish them from faster unit tests:
`client/test/integration/`.
* Specific test file:
`client/test/integration/core/services/content_processing_service_integration_test.dart`.

### 4.3. Key Principles

* **Targeted Mocking:** Only mock dependencies where absolutely necessary to isolate the system
under test or avoid undesirable side effects (e.g., `LicenseManagerService`). Dependencies
involving file system or network (as specified in Scope) will use real implementations.
* **Real Data:** Use temporary files with actual content for file path tests. Use real URLs for
URL tests.
* **Test Isolation:** While using real services, strive to make tests independent. Avoid tests
that rely on the side effects of previously run tests.
* **Descriptive Naming:** Test names should clearly describe the scenario being tested.
* **Focus on Orchestration:** Verify that `processInput` calls the correct downstream services and
handles their responses (success or failure) appropriately.

### 4.4. Execution Strategy

* These tests are intended for **manual execution** during development or as part of a
**pre-release validation suite**.
* They should **not** be included in the default `flutter test` command run during CI due to their
potential for slowness and network-related flakiness. Consider using tags (`@Tags(['integration'])`)
and running them explicitly: `flutter test --tags integration`.

## 5. Tools and Technologies

* **Test Framework:** `flutter_test`
* **Mocking Library:** `mocktail` (for `LicenseManagerService`)
* **File System:** `dart:io`, `package:path` (for creating/managing temporary test files)
* **Test Runner:** `flutter test` (with potential tagging)

## 6. Platform Considerations (Desktop)

* **File Paths:** Use `package:path` to ensure cross-platform compatibility when constructing and
asserting file paths.
* **Network:** Assumes the desktop environment has active internet connectivity. Tests may fail if
external services are down or rate limits are hit.
* **Environment:** Tests should run consistently on both Windows and macOS developer machines.

## 7. Key Scenarios for `ContentProcessingService`

* **License Checks:**
  * Input processing fails when mocked `isLicenseValid()` returns false.
  * Input processing proceeds when mocked `isLicenseValid()` returns true.
* **Input Type Handling:**
  * Valid temporary file path -> `_processFile` path is triggered, real `validateFileSize` and
  `_fileProcessingClient` calls occur.
  * YouTube URL -> `_processYouTubeVideo` path is triggered, real `YouTubeService` calls occur.
  * News Article URL -> `_processNewsArticle` path is triggered, real `NewsArticleService` calls
  occur.
  * Generic URL -> `_processUrlInput` path is triggered, real `UrlResolver` calls occur.
  * Plain Text -> `_processText` path is triggered.
* **Duplicate Detection:**
  * Processing the same content (file or text) twice results in `isDuplicate: true` on the second
  attempt.
  * Processing different content results in `isDuplicate: false`.
* **Error Handling:**
  * Test how `processInput` handles errors from real network calls (e.g., invalid URL, network
  timeout - though simulating timeouts reliably is hard).
  * Test handling of `FileSizeLimitExceededException` from the real `validateFileSize` call.

## 8. Reporting and Metrics

* **Execution Results:** Pass/Fail status reported via the standard `flutter test` output.
* **Flakiness Monitoring:** Manually track any tests that fail intermittently due to network
issues. If a test becomes consistently flaky, it may need re-evaluation or potential (temporary)
mocking if it blocks release validation.
* **Coverage:** Code coverage metrics are less critical for these integration tests compared to
unit tests. The focus is on validating the interaction points.

## 9. Conclusion

This integration testing strategy provides a way to validate the `ContentProcessingService` with
higher fidelity by using real network and file system interactions where appropriate. While
accepting the trade-offs of speed and potential flakiness, these tests offer valuable confidence in
the service's orchestration logic, particularly for release validation on desktop platforms. They
complement unit tests by verifying the collaboration between components in a more realistic environment.
