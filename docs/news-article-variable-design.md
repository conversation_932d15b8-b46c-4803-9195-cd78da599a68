# News Article Variable Feature Design

**Version:** 1.0  
**Date:** 2025-04-19

## 1. Overview

The News Article Variable feature enhances the variable system by adding support for news article
entities. This allows users to reference news articles in their prompts by URL, with the system
automatically extracting and displaying relevant metadata in a standardized preview format. This
feature builds upon the existing entity variable system while providing a specialized interface for
news article selection.

## 2. Goals

- Enable users to include news articles as variables in prompts
- Provide an intuitive URL input interface with preview functionality
- Support article metadata extraction for rich previews
- Integrate with the existing entity variable system
- Maintain consistency with social media-style link previews
- Minimize external dependencies by leveraging existing services

## 3. Core Components

### 3.1. News Article Input

A specialized input widget for news article variables that:

- Accepts URL input with validation
- Provides clipboard integration for easy URL pasting
- Shows article previews with title, source, and excerpt
- Supports full article content fetching for variable resolution
- Maintains state for selected articles

### 3.2. Link Preview Component

A reusable component that displays article metadata in a standardized format:

- Shows article title, source name, and excerpt
- Supports optional image display
- Offers both standard and compact layouts
- Follows familiar social media preview patterns
- Provides clear visual hierarchy for content elements

## 4. Technical Implementation

### 4.1. NewsArticleService Extensions

```dart
/// Fetch just the metadata for an article without the full content
/// This is a lightweight operation suitable for previews
Future<Map<String, dynamic>> fetchArticlePreview(String url) async {
  try {
    appLog.debug('Fetching article preview from URL: $url', name: _logName);

    // Make a GET request to fetch HTML (with a short timeout)
    final response = await _httpClient.get(
      Uri.parse(url),
      headers: {'User-Agent': defaultUserAgent},
    ).timeout(const Duration(seconds: 5));

    // Get the final URL after redirects
    final finalUrl = response.request?.url.toString() ?? url;

    // Parse the HTML
    final document = html_parser.parse(response.body);

    // Extract metadata from Open Graph tags
    final title = _extractMetadata(document, ['og:title', 'twitter:title']) ??
                 document.querySelector('title')?.text ??
                 'Article from ${Uri.parse(finalUrl).host}';

    final description = _extractMetadata(document, ['og:description', 'twitter:description', 'description']) ?? '';
    final imageUrl = _extractMetadata(document, ['og:image', 'twitter:image']);
    final siteName = _extractMetadata(document, ['og:site_name']) ?? Uri.parse(finalUrl).host;

    return {
      MetadataKeys.url: url,
      MetadataKeys.finalUrl: finalUrl,
      MetadataKeys.title: title,
      MetadataKeys.excerpt: description,
      MetadataKeys.imageUrl: imageUrl,
      MetadataKeys.siteName: siteName,
    };
  } catch (e, stack) {
    appLog.error('Error fetching article preview',
      name: _logName, error: e, stackTrace: stack);

    // Return basic information based on the URL
    return {
      MetadataKeys.url: url,
      MetadataKeys.title: 'Article from ${Uri.parse(url).host}',
      MetadataKeys.siteName: Uri.parse(url).host,
    };
  }
}
```

### 4.2. UI Components

#### 4.2.1. NewsLinkPreview Widget

```dart
/// A reusable widget for displaying news article link previews
/// Similar to how social media platforms display shared links
class NewsLinkPreview extends StatelessWidget {
  final Map<String, dynamic> metadata;
  final VoidCallback? onTap;
  final bool compact;

  const NewsLinkPreview({
    Key? key,
    required this.metadata,
    this.onTap,
    this.compact = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final title = metadata[MetadataKeys.title] ?? 'Article';
    final siteName = metadata[MetadataKeys.siteName] ??
                     Uri.parse(metadata[MetadataKeys.url] ?? '').host;
    final excerpt = metadata[MetadataKeys.excerpt] ?? '';
    final imageUrl = metadata[MetadataKeys.imageUrl];

    // Compact layout (horizontal) or standard layout (vertical)
    if (compact) {
      return Card(
        clipBehavior: Clip.antiAlias,
        elevation: 1.0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.0),
          side: BorderSide(color: Colors.grey.shade300, width: 1.0),
        ),
        child: InkWell(
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Image (if available)
                if (imageUrl != null)
                  ClipRRect(
                    borderRadius: BorderRadius.circular(4.0),
                    child: SizedBox(
                      width: 80,
                      height: 80,
                      child: Image.network(
                        imageUrl,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) => Container(
                          color: Colors.grey.shade200,
                          child: Icon(Icons.article, size: 24),
                        ),
                      ),
                    ),
                  ),

                SizedBox(width: imageUrl != null ? 12.0 : 0),

                // Text content
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      SizedBox(height: 4),
                      Text(
                        siteName,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.grey.shade700,
                            ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        excerpt,
                        style: Theme.of(context).textTheme.bodySmall,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    } else {
      // Standard vertical layout
      return Card(
        clipBehavior: Clip.antiAlias,
        elevation: 1.0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.0),
          side: BorderSide(color: Colors.grey.shade300, width: 1.0),
        ),
        child: InkWell(
          onTap: onTap,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              // Image (if available)
              if (imageUrl != null)
                AspectRatio(
                  aspectRatio: 16 / 9,
                  child: Image.network(
                    imageUrl,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => Container(
                      color: Colors.grey.shade200,
                      child: Center(child: Icon(Icons.article, size: 32)),
                    ),
                  ),
                ),

              // Text content
              Padding(
                padding: const EdgeInsets.all(12.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 4),
                    Text(
                      siteName,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey.shade700,
                          ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      excerpt,
                      style: Theme.of(context).textTheme.bodyMedium,
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    }
  }
}
```

#### 4.2.2. NewsArticleInput Widget

```dart
class NewsArticleInput extends StatefulWidget {
  final String initialValue;
  final void Function(Variable) onSelected;
  final NewsArticleService newsService;
  final Variable variable;

  const NewsArticleInput({
    Key? key,
    required this.initialValue,
    required this.onSelected,
    required this.newsService,
    required this.variable,
  }) : super(key: key);

  @override
  State<NewsArticleInput> createState() => _NewsArticleInputState();
}

class _NewsArticleInputState extends State<NewsArticleInput> {
  static const _logName = 'NewsArticleInput';

  late TextEditingController _urlController;
  bool _isLoading = false;
  bool _isValidUrl = false;
  Map<String, dynamic>? _previewMetadata;
  String? _selectedArticleId;

  @override
  void initState() {
    super.initState();
    _urlController = TextEditingController(text: widget.initialValue);

    // If we have an initial value, try to validate it
    if (widget.initialValue.isNotEmpty) {
      if (_isUrlValid(widget.initialValue)) {
        _validateUrl(widget.initialValue);
      } else {
        // It might be an article ID, not a URL
        _selectedArticleId = widget.initialValue;
        // Here we would ideally fetch the article details by ID
      }
    }
  }

  @override
  void dispose() {
    _urlController.dispose();
    super.dispose();
  }

  Future<void> _pasteFromClipboard() async {
    final data = await Clipboard.getData(Clipboard.kTextPlain);
    if (data != null && data.text != null) {
      final text = data.text!;
      setState(() {
        _urlController.text = text;
      });
      _validateUrl(text);
    }
  }

  void _validateUrl(String url) {
    final isValid = _isUrlValid(url);

    setState(() {
      _isValidUrl = isValid;
      if (!isValid) {
        _previewMetadata = null;
      }
    });

    if (isValid) {
      _fetchPreview(url);
    }
  }

  bool _isUrlValid(String url) {
    if (url.isEmpty) return false;

    try {
      final uri = Uri.parse(url);
      return uri.scheme == 'http' || uri.scheme == 'https';
    } catch (e) {
      return false;
    }
  }

  Future<void> _fetchPreview(String url) async {
    if (!_isValidUrl) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Use the lightweight preview method
      final metadata = await widget.newsService.fetchArticlePreview(url);

      setState(() {
        _previewMetadata = metadata;
        _isLoading = false;
      });
    } catch (e, stack) {
      appLog.error('Error fetching article preview',
        name: _logName, error: e, stackTrace: stack);
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _selectArticle() async {
    if (_previewMetadata == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Fetch and register the full article
      final url = _previewMetadata![MetadataKeys.url] ?? _urlController.text;
      final fullMetadata = await widget.newsService.fetchAndRegisterArticleWithMetadata(
        url: url,
        title: _previewMetadata![MetadataKeys.title],
        description: _previewMetadata![MetadataKeys.excerpt],
      );

      // Update the variable with the article ID
      final articleId = fullMetadata[MetadataKeys.id];
      if (articleId != null) {
        _selectedArticleId = articleId;
        widget.onSelected(widget.variable.copyWithValue(articleId));
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e, stack) {
      appLog.error('Error selecting article',
        name: _logName, error: e, stackTrace: stack);
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _clearSelection() {
    setState(() {
      _urlController.clear();
      _previewMetadata = null;
      _selectedArticleId = null;
      _isValidUrl = false;
    });

    // Update the variable with empty value
    widget.onSelected(widget.variable.copyWithValue(''));
  }

  @override
  Widget build(BuildContext context) {
    // If we already have a selected article, show its details
    if (_selectedArticleId != null && _previewMetadata != null) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          NewsLinkPreview(
            metadata: _previewMetadata!,
          ),
          SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: Text(
                  'Article selected',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.green,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              TextButton.icon(
                icon: Icon(Icons.edit),
                label: Text('Change'),
                onPressed: _clearSelection,
              ),
            ],
          ),
        ],
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // URL Input Field
        TextField(
          controller: _urlController,
          decoration: InputDecoration(
            hintText: 'Enter news article URL',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.link),
            suffixIcon: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (_urlController.text.isNotEmpty)
                  IconButton(
                    icon: Icon(Icons.clear),
                    onPressed: _clearSelection,
                  ),
                IconButton(
                  icon: Icon(Icons.content_paste),
                  onPressed: _pasteFromClipboard,
                ),
              ],
            ),
          ),
          onChanged: _validateUrl,
        ),

        SizedBox(height: 8),

        // Loading Indicator
        if (_isLoading)
          Center(child: CircularProgressIndicator(strokeWidth: 2.0)),

        // Preview
        if (_previewMetadata != null && !_isLoading)
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              NewsLinkPreview(
                metadata: _previewMetadata!,
              ),
              SizedBox(height: 8),
              ElevatedButton(
                onPressed: _selectArticle,
                child: Text('Use This Article'),
              ),
            ],
          ),
      ],
    );
  }
}
```

### 4.3. EntityVariableField Integration

Update to the `EntityVariableField` class to handle news entity types:

```dart
// Inside EntityVariableField.build method
if (entityType == EntityType.finance) {
  // Existing stock symbol implementation
  // ...
} else if (entityType == EntityType.news) {
  // Get entityDetectionService and newsArticleService from clientContextService
  final entityDetectionService = clientContextService.entityDetection;
  final newsArticleService = clientContextService.newsArticle;

  return Padding(
    padding: const EdgeInsets.symmetric(vertical: 8.0),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Icon(
              Icons.article,
              size: 18,
            ),
            const SizedBox(width: 8),
            Text(
              variable.prefixedDisplayName,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        NewsArticleInput(
          initialValue: initialValue ?? '',
          newsService: newsArticleService,
          variable: variable,
          onSelected: (updatedVariable) {
            onChanged(updatedVariable);
          },
        ),
      ],
    ),
  );
} else {
  // Default field for other entity types
  // ...
}
```

## 5. User Flows

### 5.1. News Article Selection

1. User encounters a variable of type "news" in the VariablesDialog
2. The system displays a specialized input field with URL input box
3. User enters or pastes a news article URL
4. System validates URL format immediately
5. If valid, system fetches article preview data (title, source, excerpt)
6. Preview is displayed in a card format similar to social media link previews
7. User clicks "Use This Article" to confirm selection
8. System fetches full article content and registers it with the entity system
9. Variable is updated with article ID for resolution in the prompt
10. User sees confirmation that the article is selected

### 5.2. Changing Selected Article

1. User has previously selected an article
2. System displays the selected article preview with a "Change" button
3. User clicks "Change" to select a different article
4. System clears the current selection and shows the URL input field
5. User enters a new URL and the process continues as in 5.1

### 5.3. Error Handling

1. User enters an invalid URL
2. System immediately shows validation error
3. User enters a valid URL but article cannot be fetched
4. System shows appropriate error message with fallback information
5. User can retry or enter a different URL

## 6. Implementation Phases

### 6.1. Phase 1 (Current Implementation)

- Basic URL input with validation
- Article preview component with title, source, and excerpt
- Integration with EntityVariableField
- Full article content fetching and registration
- Error handling and loading states

### 6.2. Future Enhancements

- Support for article search by keyword
- Integration with popular news APIs for content discovery
- Caching of frequently accessed articles
- Enhanced metadata extraction for better previews
- Support for paywalled content handling
- Offline mode with saved article content

## 7. Design Considerations

### 7.1. Privacy

- Only process URLs explicitly entered by users
- No browser history integration
- Clear indication when external content is being fetched
- No tracking of user browsing habits
- Minimal data storage (only what's necessary for functionality)

### 7.2. Performance

- Lightweight preview fetching before full content
- Proper loading states during network operations
- Graceful error handling for invalid URLs or failed fetches
- Optimized image loading with error fallbacks
- Minimal network requests (preview first, full content only when needed)

### 7.3. User Experience

- Familiar social media-style link previews
- Clear visual hierarchy in preview cards
- Intuitive clipboard integration for URL pasting
- Responsive design for different screen sizes
- Consistent styling with the rest of the application

### 7.4. Accessibility

- Proper contrast ratios for text content
- Meaningful alternative text for images
- Keyboard navigation support
- Screen reader compatibility
- Proper focus management

## 8. Integration Points

### 8.1. Entity System

- News articles are registered as entities with the EntityDetectionService
- Article content is made available for entity detection in text
- Entity variables can reference news articles by ID

### 8.2. Variable Resolution

- When a prompt is executed, news article variables are resolved to their content
- Full article content is available for processing
- Metadata (title, source, etc.) is available for formatting

### 8.3. Source Input Section

- The NewsLinkPreview component can be reused in the SourceInputSection
- Consistent preview styling across the application
- Shared article fetching logic between components

## 9. Conclusion

The News Article Variable feature enhances the prompt creation experience by allowing seamless
integration of news content. By following familiar link preview patterns and leveraging the existing
entity system, this feature provides an intuitive way for users to reference external content in
their prompts. The implementation focuses on simplicity, performance, and user experience while
maintaining consistency with the rest of the application.

The modular design allows for future enhancements while providing immediate value with the core
functionality. By reusing components like the NewsLinkPreview widget, we ensure consistent styling
and behavior across different parts of the application.
