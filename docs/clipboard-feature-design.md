# Clipboard Feature Design

**Version:** 1.0
**Date:** 2025-04-10

## 1. Overview

The Clipboard feature in Promz allows users to access and use clipboard content in the application.
This document outlines the design changes required to fix privacy and permission issues,
particularly on iOS, where continuous clipboard access triggers permission dialogs.

## 2. Problem Statement

The current implementation has several issues:

1. **Automatic Polling:** The application continuously checks for clipboard content every 2 seconds,
   triggering iOS permission dialogs repeatedly.
2. **Privacy Concerns:** Modern mobile operating systems (iOS 14+ and Android 12+) have strict
   privacy protections for clipboard access.
3. **Duplicate Methods:** The ClipboardService contains duplicate functionality (`hasContent()`
   and `hasClipboardText()`).
4. **User Experience:** Frequent permission dialogs create a poor user experience and raise privacy
   concerns.

## 3. Solution Strategy

### 3.1 Event-Based Clipboard Access

Switch from automatic polling to event-based clipboard access:

- **User-Initiated Only:** Check clipboard content ONLY when explicitly triggered by user actions
- **Remove Timer-Based Polling:** Eliminate the timer mechanism in ClipboardHandler
- **Maintain Existing UI:** No UI changes at this phase, only modify the underlying mechanism

### 3.2 Implementation Changes

#### ClipboardHandler Class

- Remove the automatic timer-based checking mechanism
- Add methods for explicit user-triggered clipboard checks
- Maintain the same public API for compatibility with existing code
- Convert internal implementation to avoid automatic polling

#### ClipboardService Class

- Consolidate duplicate methods (`hasContent()` and `hasClipboardText()`)
- Maintain the same public API for backward compatibility
- Add clear documentation about platform-specific clipboard access behavior

## 4. Technical Implementation

### 4.1 ClipboardHandler Changes

1. **Remove Timer-Based Polling:**

   - Remove `_clipboardCheckTimer` and all timer-related code
   - Eliminate automatic scheduling of clipboard checks

2. **Add User-Triggered Methods:**

   - Create a new `checkClipboardContent()` method that only runs when user actions trigger it
   - Maintain existing API with backward compatibility

3. **Update Internal Implementation:**
   - Convert `startClipboardCheck` to store the callback without starting a timer
   - Make `_checkClipboard` a one-time operation without scheduling

### 4.2 ClipboardService Cleanup

1. **Method Consolidation:**

   - Keep `hasContent()` as the primary method
   - Make `hasClipboardText()` a simple wrapper around `hasContent()`

2. **Documentation:**
   - Add clear documentation about platform-specific clipboard behaviors
   - Document best practices for clipboard access

## 5. User Flows

### 5.1 Current User Flow

1. Application starts
2. ClipboardHandler begins automatic polling
3. Every 2 seconds, the clipboard is checked for content
4. On iOS, this triggers a permission dialog for each check
5. UI updates when clipboard content changes

### 5.2 New User Flow

1. Application starts
2. ClipboardHandler initializes but doesn't start polling
3. User performs an action requiring clipboard access (taps "Paste" button, etc.)
4. ClipboardHandler checks clipboard content once
5. Permission dialog appears only once per user action
6. UI updates based on clipboard content

## 6. Implementation Timeline

**Phase 1 (Current):**

- Remove automatic polling
- Implement user-triggered clipboard checks
- Clean up duplicate methods
- Add proper documentation

**Future Phases (Not in Current Scope):**

- UI improvements for clipboard interactions
- Enhanced clipboard content handling
- Platform-specific optimizations

## 7. Benefits

1. **Improved User Experience:** Eliminates repetitive permission dialogs
2. **Privacy Compliance:** Follows Apple's and Google's privacy guidelines
3. **Battery Optimization:** Reduces unnecessary background operations
4. **Cleaner Code:** Eliminates duplicate methods and clarifies responsibilities

## 8. Implementation Notes

- No UI changes in the current phase
- No preference settings required as we're adopting a user-action-only approach
- Code changes focused on internal mechanisms while maintaining existing APIs
- Important to test on both iOS and Android platforms after implementation
