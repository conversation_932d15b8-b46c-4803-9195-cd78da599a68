Please use the following key points to generate a suggested file name on the server:

## Components of the File Name ##

* Prompt Name: Use a simplified version of the prompt name (e.g., comparative-stock-analysis) to clearly indicate the type or subject of the analysis.
* Internationalized Date Stamp: Incorporate a date stamp in the YYYYMMDD format (e.g., 20250323) to ensure the file is timestamped in an internationally recognized format.
* Variable Summary: Include essential variable information (e.g., key identifiers like stock tickers such as AAPL and MSFT) to provide context about the file contents.
* File Extension: Use a consistent file extension (e.g., .txt, .md) that reflects the file type.
* Hyphens: Use hyphens as a word separator both in the prompt name, and as we combine multiple components.

Recommended Naming Pattern

The suggested structure for the file name is as follows:

```
<prompt-name>-<YYYYMMDD>-<variable_summary>.<extension>
```

Example:
For a comparative stock analysis performed on March 23, 2025, with stocks AAPL and MSFT, the file name might be:

```
comparative-stock-analysis-20250323-AAPL-MSFT.pdf
```
