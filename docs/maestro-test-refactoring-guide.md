# Maestro Test Refactoring Guide

## Overview

This guide documents the refactoring of our Maestro test suite to eliminate code duplication and implement DRY (Don't Repeat Yourself) principles while maintaining test readability and reliability.

## Problem Analysis

### Code Duplication Identified

1. **Navigation Bar Assertions** (6 files affected)
   - `"Home\nTab 1 of 5"`, `"Discover\nTab 2 of 5"`, etc.
   - Repeated across: app_launch.yaml, navigation_helpers.yaml, all auth test files

2. **Authentication State Verification** (5 files affected)
   - `assertVisible: "Sign in"` (unauthenticated)
   - `assertNotVisible: "Sign in"` (authenticated)
   - `assertVisible: "@"` (user profile loaded)

3. **Common UI Patterns** (multiple files)
   - `waitForAnimationToEnd: timeout: 3000`
   - Navigation tap sequences
   - Screenshot naming patterns

## Refactored Architecture

### New Modular Components

#### 1. `navigation_assertions.yaml`

**Purpose**: Centralized navigation bar verification logic
**Contains**:

- Complete navigation bar visibility verification
- Individual tab verification flows
- Navigation with verification flows

```yaml
# Usage examples:
- runFlow: ../common/navigation_assertions.yaml  # Verify all tabs
- runFlow: ../common/navigation_assertions.yaml  # Navigate to Home with verification
```

#### 2. `auth_state_assertions.yaml`

**Purpose**: Authentication state verification components
**Contains**:

- Unauthenticated state verification
- Authenticated state verification
- Authentication flow indicators
- Profile loading verification

```yaml
# Usage examples:
- runFlow: ../common/auth_state_assertions.yaml  # Verify unauthenticated state
- runFlow: ../common/auth_state_assertions.yaml  # Verify user profile loaded
```

#### 3. `ui_actions.yaml`

**Purpose**: Common UI interaction patterns
**Contains**:

- Standard wait times
- Basic navigation actions
- Common button interactions

```yaml
# Usage examples:
- runFlow: ../common/ui_actions.yaml  # Standard animation wait
- runFlow: ../common/ui_actions.yaml  # Navigate to Account tab (basic)
```

### Updated File Structure

```text
testing/maestro/android/
├── common/
│   ├── app_launch.yaml              # ✅ Refactored to use modular components
│   ├── navigation_assertions.yaml   # 🆕 Navigation verification library
│   ├── auth_state_assertions.yaml   # 🆕 Authentication state library
│   ├── ui_actions.yaml             # 🆕 Common UI actions library
│   ├── navigation_helpers.yaml     # ⚠️  Deprecated, uses new components
│   └── assertions.yaml             # 📝 Existing, may need refactoring
├── authentication/
│   ├── 01_pre_login_state.yaml           # 📝 Needs refactoring
│   ├── 01_pre_login_state_refactored.yaml # 🆕 Example refactored version
│   ├── 02_login_flow.yaml                # 📝 Needs refactoring
│   ├── 03_post_login_state.yaml          # 📝 Needs refactoring
│   ├── 04_navigation_access.yaml         # 📝 Needs refactoring
│   └── 05_logout_flow.yaml               # 📝 Needs refactoring
└── test_suites/
    └── authentication_suite.yaml    # 📝 May need updates
```

## Implementation Strategy

### Phase 1: Core Components (✅ Complete)

- [x] Create `navigation_assertions.yaml`
- [x] Create `auth_state_assertions.yaml`
- [x] Create `ui_actions.yaml`
- [x] Update `app_launch.yaml` to use new components
- [x] Create example refactored test file

### Phase 2: Gradual Migration (🔄 In Progress)

- [ ] Refactor authentication test files one by one
- [ ] Update existing `assertions.yaml` to use new patterns
- [ ] Test each refactored file to ensure functionality
- [ ] Update documentation

### Phase 3: Cleanup (📋 Planned)

- [ ] Remove deprecated patterns
- [ ] Consolidate remaining duplications
- [ ] Update test suite configurations
- [ ] Create migration guide for future tests

## Usage Patterns

### Before Refactoring

```yaml
# Duplicated in multiple files
- assertVisible: "Home\nTab 1 of 5"
- assertVisible: "Discover\nTab 2 of 5"
- assertVisible: "Portfolio\nTab 3 of 5"
- assertVisible: "Account\nTab 4 of 5"
- assertVisible: "About\nTab 5 of 5"
```

### After Refactoring

```yaml
# Single line, reusable across all tests
- runFlow: ../common/navigation_assertions.yaml
```

### Specific Component Usage

```yaml
# For specific navigation verification
- runFlow: ../common/navigation_assertions.yaml  # Verify Home tab selected

# For authentication state checks
- runFlow: ../common/auth_state_assertions.yaml  # Verify unauthenticated state

# For common UI actions
- runFlow: ../common/ui_actions.yaml  # Standard animation wait
```

## Benefits Achieved

### 1. **Reduced Duplication**

- Navigation assertions: 6 files → 1 component
- Authentication checks: 5 files → 1 component
- Common UI patterns: Multiple files → 1 component

### 2. **Improved Maintainability**

- Single source of truth for common patterns
- Changes propagate automatically to all tests
- Easier to update when UI changes

### 3. **Enhanced Readability**

- Test files focus on test-specific logic
- Common patterns are abstracted away
- Clear separation of concerns

### 4. **Better Consistency**

- Standardized timeout values
- Consistent assertion patterns
- Uniform error handling

## Migration Guidelines

### For Existing Tests

1. **Identify duplicated patterns** in your test file
2. **Check if a component exists** in the common directory
3. **Replace duplicated code** with `runFlow: ../common/component.yaml`
4. **Test the refactored file** to ensure functionality
5. **Update documentation** if needed

### For New Tests

1. **Check existing components** before writing new assertions
2. **Use modular components** wherever possible
3. **Only write custom logic** for test-specific requirements
4. **Follow established patterns** for consistency

## Best Practices

### 1. **Component Design**

- Each component should have a single responsibility
- Components should be self-contained and reusable
- Use descriptive comments for each flow section

### 2. **Flow Selection**

- Use specific flow sections when you need only part of a component
- Use complete components for standard verification patterns
- Combine multiple components for complex scenarios

### 3. **Backward Compatibility**

- Keep deprecated files during transition period
- Mark deprecated files clearly
- Provide migration path in comments

### 4. **Testing Strategy**

- Test each refactored file individually
- Verify that component changes don't break existing tests
- Use test suites to validate complete flows

## Future Enhancements

### Planned Improvements

1. **Smart Component Selection**: Automatically choose appropriate flow sections
2. **Parameterized Components**: Pass parameters to components for flexibility
3. **Error Handling Components**: Standardized error recovery patterns
4. **Performance Components**: Optimized wait times and assertions

### Extension Points

1. **Platform-Specific Components**: iOS-specific assertion libraries
2. **Feature-Specific Components**: Components for specific app features
3. **Environment Components**: Different components for dev/staging/prod

## Conclusion

This refactoring significantly reduces code duplication while maintaining test clarity and reliability. The modular approach makes our test suite more maintainable and easier to extend as the application grows.
