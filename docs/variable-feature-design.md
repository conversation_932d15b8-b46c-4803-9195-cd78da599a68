# Variable Management Feature Design

## Overview

The variable management system allows templates to include dynamic variables that can be replaced with actual values during template processing. The system supports both simple variables and prefixed variables for handling multiple instances of the same variable type.

## Variable Format

### Basic Variable Format

- Variables are denoted with double curly braces: `{{CATEGORY:NAME}}`
- Example: `{{FINANCE:TICKER}}` represents a stock ticker symbol

### Prefixed Variable Format

- For multiple instances of the same variable type, prefixes are used: `{{PREFIX}CATEGORY:NAME}}`
- Supported formats:
  - Legacy format: `{#N}` where N is a number or identifier
  - Recommended format: `{$N}` where N is a number or identifier
- Both client-side and server-side now support both formats
- Examples:
  - Legacy format: `{{#1}FINANCE:TICKER}}` - First stock ticker
  - Recommended format: `{{$1}FINANCE:TICKER}}` - First stock ticker

> **Important:** While both formats are supported for backward compatibility, the `{$N}` format is recommended for new code as it's more compatible with Go templates.

## Client-Side Implementation

The client uses `EntityUtils` for handling variables with both prefix formats:

```dart
// Key functions:
String? extractPrefix(String variableName)      // Gets the prefix from a variable (supports both {#N} and {$N})
String extractBaseName(String variableName)     // Gets the base name without prefix
String getCanonicalName(String variableName)    // Normalizes variable names and adds {$N} prefix if needed
```

## Server-Side Implementation

The server implements support for both prefix formats in Go:

```go
// Key functions:
func ExtractPrefix(variableName string) string          // Gets the prefix from a variable (supports both {#N} and {$N})
func ExtractBaseName(variableName string) string        // Gets the base name without prefix
func AddPrefixToVariable(prefix, baseName string) string // Combines prefix and base name
func SanitizeTemplateVariableName(name string) string   // Makes variable names compatible with Go templates
                                                        // (replaces # with "num" and $ with "var")
```

## Template Processing

1. Variables are extracted from templates using regular expressions
2. For each variable:
   - The prefix is extracted (if present)
   - The base name is extracted
   - Values are looked up from the variable mapping
   - Appropriate transformations are applied (e.g., financial data enrichment)
   - The variable is replaced with its value in the template

## Recent Enhancements

1. **Go Template Compatibility**: The `#` character is now sanitized in variable names on the server as it's not compatible with Go templates. It's replaced with `num` while preserving the variable's uniqueness.

2. **Dual Prefix Support (Server-Side Only)**: The server now recognizes both `{#N}` and `{$N}` prefix formats, making the transition to the newer `$` prefix format possible while maintaining backward compatibility.

3. **Financial Data Enrichment**: When a `FINANCE:TICKER` variable is encountered, related data (company name, sector, industry) are automatically added with the same prefix.

## Implementation Status

1. **Server-Side**: Full support for both `{#N}` and `{$N}` prefix formats
2. **Client-Side**: Full support for both `{#N}` and `{$N}` prefix formats
3. **Completed Work**: Updated client-side EntityUtils to support the `{$N}` format

## Best Practices

1. Use meaningful prefixes that indicate the role of the variable (e.g., `{$1}` for the first item, `{$2}` for the second).

2. Prefer the `{$N}` prefix format over `{#N}` as it's more compatible with Go templates.

3. Keep variable names consistent across your application.

4. Use the canonical name format (`CATEGORY:NAME`) for all variables to ensure proper processing.

## Future Improvements

1. Add support for default values in variable definitions
2. Implement variable validation based on type
3. Add support for computed variables based on other variables
4. Create a visual editor for template variables
5. Implement variable suggestions based on context

## Appendix: Detailed Design Document

**Version:** 1.2
**Date:** 2024-05-15

## 1. Overview

The Variables feature in Promz enables dynamic, customizable prompt templates with replaceable elements. Users can define variables in their prompts that can be replaced with specific values during execution. This design document outlines both the existing variable system and the new prefix enhancement that allows multiple instances of the same variable type in a single prompt.

## 2. Goals

- Provide a flexible template system for prompts
- Support both entity variables (linked to known entities) and custom variables
- Enable multiple instances of the same variable type via prefixes
- Maintain backward compatibility with existing templates
- Offer an intuitive UI for variable definition and resolution
- Support centralized variable value management across the application
- Enhance content generation by allowing variable substitution

## 3. Core Concepts

### 3.1. Variable Types

1. **Entity Variables**

   - Linked to known entity types (finance, location, person, etc.)
   - Provide specialized input options based on entity type
   - Follow the format `{{CATEGORY:ENTITY}}`
   - Examples: `{{FINANCE:TICKER}}`, `{{LOCATION:CITY}}`

2. **Custom Variables**

   - User-defined variables without specific entity associations
   - Accept free-form text input
   - Follow the format `{{VARIABLE_NAME}}`
   - Example: `{{USER_NAME}}`

3. **Prefixed Variables** (New)
   - Allow multiple instances of the same variable type
   - Add unique identifiers to distinguish between instances
   - Follow the format `{{prefix}CATEGORY:ENTITY}}` or `{{prefix}VARIABLE_NAME}}`
   - Examples: `{{$1}FINANCE:TICKER}}`, `{{$buy}FINANCE:TICKER}}`
   - Legacy format (supported but not recommended for new code): `{{#1}FINANCE:TICKER}}`

### 3.2. Variable Lifecycle

1. **Definition**: Variables are defined in prompt templates using the syntax described above.
2. **Extraction**: The system extracts and catalogs all variables in a prompt.
3. **Display**: Variables are shown to users with human-readable display names.
4. **Value Assignment**: Users assign values to variables through the VariablesDialog.
5. **Resolution**: Variable placeholders are replaced with their assigned values.
6. **Persistence**: Variable values are stored centrally for reuse across prompts.

## 4. Variable Prefix Implementation

### 4.1. Prefix Syntax

**Current Client-Side Implementation:**
