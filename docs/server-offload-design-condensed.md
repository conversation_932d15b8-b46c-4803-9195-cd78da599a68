# Server-Side Processing Offload Design

**Version:** 1.1
**Date:** 2025-04-23

## Document History

| Version | Date       | Description                                        |
| ------- | ---------- | -------------------------------------------------- |
| 1.0     | 2025-04-22 | Initial design document                            |
| 1.1     | 2025-04-23 | Added attachment variable handling and streamlined |

## Overview

This design document outlines a strategy for offloading file processing from the Promz mobile client
to a server-side Go backend. This improvement addresses memory usage and performance issues
experienced on mobile devices when processing large files such as WhatsApp chat exports and other
content.

## Problem Statement

The current implementation of file processing in Promz has several limitations:

1. **Memory Constraints**: Processing large files (especially WhatsApp chat exports and ZIP files)
consumes significant memory on mobile devices, potentially leading to app crashes.

2. **Performance Issues**: Processing text extraction and analysis on the client side can be
CPU-intensive, causing UI freezes and poor user experience.

3. **Battery Drain**: Intensive processing operations decrease battery life on mobile devices.

4. **Scalability Limitations**: As we add more content processing capabilities (e.g., chat
analysis, summarization), client-side processing becomes increasingly unsustainable.

## Goals

1. Move resource-intensive file processing operations to the server
2. Maintain a responsive user interface during processing
3. Design a solution that works for various content types (WhatsApp chats, news articles, etc.)
4. Implement secure, temporary storage for user content
5. Ensure the solution gracefully degrades when offline

## Non-Goals

1. Long-term storage of user content (all data will be temporary)
2. Processing of sensitive user data without explicit consent
3. Replacing all client-side processing (small files can still be processed locally)

## Architecture Overview

### High-Level Architecture

![Server Offload Architecture](https://mermaid.ink/img/pako:eNp1kc1OwzAQhF_F2guVQP2DK3IpB5BijoioG2fjJrb8J9krVFXevU5ahCqB9rbzzc7srvGYKcFYM3N2qWvQhkLBfeWNp1CsmQsd8aMnq-wkktNcAtWSQBlx7vwjNFQXYf0BoWpCi2gYtWGKFaWRbHQk43uwBf8E7Xapn-OuJn0XCepWGXzS1DnI8yLD4m-HnO7QBB_n7Gh5832FpkceosYu2cdkBpshzE3baTJxZvY6flX7Cbf1y9m4zpRZSyT-0UcCc_AxXTY_bP9BN6F6JOVIlDEFHL9T5C_GF3tHYQyWaa4dv_w0Alvy37fLKm_46mgB-fHQYxg_AWvqgXk?type=png)

The system consists of:

1. **Client (Flutter App)**:
   - File selection UI
   - Upload mechanism
   - Status tracking
   - Result display

2. **Go Backend**:
   - File upload handling
   - Content detection
   - Processing services
   - Result management
   - Temporary storage

3. **Google Cloud Storage**:
   - Temporary file storage
   - Automatic expiration

### Process Flow

1. User selects a file in the mobile app
2. Client checks file size and complexity to determine processing location
3. For large files, client uploads to server
4. Server acknowledges receipt and begins processing
5. Client displays processing status
6. Server processes file and stores results temporarily
7. Client fetches results when processing is complete
8. Client displays processed data to user

## Design Details

### 1. API Endpoints

#### Upload Endpoint

```http
POST /upload
```

- Accepts multipart file upload
- Returns processing ID and initial status
- Authentication required

#### Status Endpoint

```http
GET /upload/status/{id}
```

- Returns current processing status
- Supports polling for updates
- Authentication required

#### Results Endpoint

```http
GET /upload/results/{id}
```

- Returns processed content and metadata
- Authentication required

### 2. Client-Side Implementation

#### File Upload Service

The `FileUploadService` handles communication with the server for file uploads and processing:

- **Key Responsibilities**:
  - Upload files to server
  - Check processing status
  - Retrieve processing results
  - Cancel processing if needed

#### Response Models

The client uses several models to track processing:

| Model | Purpose | Key Fields |
|-------|---------|------------|
| `FileUploadResponse` | Initial upload response | id, status, estimatedTimeSeconds |
| `FileProcessingStatus` | Status updates | id, status, progress, estimatedTimeRemaining |
| `FileProcessingResult` | Final results | id, status, content, contentType, metadata |

#### Integration with Existing Services

The server processing capability integrates with existing services:

- **ZipService**: Determines whether to process locally or on server based on file size
- **ContentProcessingService**: Manages processing decisions and results
- **WhatsAppService**: Handles WhatsApp chat extraction with server offloading
- **FileProcessingViewModel**: Displays processing status and results

### 3. Server-Side Implementation

#### Upload Handler

The server provides an upload handler that:
- Validates incoming files
- Stores files temporarily
- Initiates appropriate processing based on file type
- Returns a job ID for status tracking

#### Processing Service

The processing service:
- Manages a queue of processing jobs
- Dispatches jobs to appropriate processors based on file type
- Tracks job status and progress
- Stores results for later retrieval

#### Content Processors

The server implements various content processors:

| Processor | Purpose |
|-----------|---------|
| `ZipProcessor` | Extracts and processes ZIP archives |
| `WhatsAppProcessor` | Parses WhatsApp chat exports |
| `PDFProcessor` | Extracts text from PDF documents |
| `ImageProcessor` | Performs OCR on images |
| `TextProcessor` | Analyzes and processes text content |

#### Storage Management

The server uses Google Cloud Storage for temporary file storage:
- Files are stored with unique identifiers
- Automatic expiration policies ensure data is removed after processing
- Signed URLs provide secure access to results

### 4. Attachment Variable Handling

#### Variable Format

The system supports attachment variables in prompts using the format:

- Single attachment: `ATTACHMENT:UPLOAD_ID`
- Multiple attachments: `{#1}ATTACHMENT:UPLOAD_ID`, `{#2}ATTACHMENT:UPLOAD_ID`, etc.

Where `UPLOAD_ID` is the server-side job ID for the processed attachment.

#### Client-Side Integration

The client handles attachment variables by:
- Registering server-processed attachments in the `AttachmentRegistryService`
- Adding attachment variables to the context via `ClientContextService`
- Including attachment variables in LLM requests

#### Server-Side Processing

The server processes attachment variables through:
- Middleware that detects attachment variables in requests
- Conversion of `ATTACHMENT:UPLOAD_ID` to `ATTACHMENT:CONTENTS`
- Integration with the existing LLM execution flow

## Security Considerations

1. **Temporary Storage**: All uploaded files are stored temporarily and automatically deleted after processing
2. **Authentication**: All API endpoints require authentication
3. **Data Minimization**: Only necessary data is processed and stored
4. **Secure Transmission**: All data is transmitted over HTTPS
5. **Access Control**: Results are only accessible to the user who uploaded the file

## Error Handling

1. **Upload Failures**: Graceful degradation to local processing when possible
2. **Processing Errors**: Clear error messages and fallback options
3. **Network Issues**: Retry mechanisms with exponential backoff
4. **Timeout Handling**: Cancellation of long-running jobs with user notification

## Performance Considerations

1. **Parallel Processing**: Server can process multiple files simultaneously
2. **Resource Allocation**: Processing jobs are allocated appropriate resources based on file size and type
3. **Caching**: Results are cached to prevent redundant processing
4. **Compression**: Results are compressed before transmission to reduce bandwidth usage

## Conclusion

This server-side processing offload design will significantly improve the user experience of the Promz application by:

1. Reducing memory usage on mobile devices
2. Improving application responsiveness
3. Supporting processing of larger files
4. Enabling more advanced processing features in the future
5. Providing seamless integration of server-processed attachments with LLM prompts

The implementation follows a phased approach that allows for incremental deployment and testing. By utilizing Google Cloud Storage with automatic expiration policies, we ensure that user data is handled securely and only stored for the minimum time necessary.

This architecture also provides a foundation for future enhancements to the content processing capabilities of the application without compromising mobile performance.
