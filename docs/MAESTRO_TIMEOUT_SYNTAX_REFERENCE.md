# Maestro Timeout Syntax Reference Guide

Agent Reference File for Augment Code and IDE Integration

This document provides definitive timeout syntax rules for Maestro UI testing framework, based on troubleshooting sessions where "Unknown Property: timeout" errors were resolved in the Promz project authentication tests.

## Quick Reference Summary

**Key Rule**: Most Maestro commands do NOT accept timeout parameters. Only specific waiting commands support timeouts.

## Timeout Syntax Rules

### ✅ Commands That ACCEPT Timeout Parameters

| Command | Syntax | Purpose |
|---------|--------|---------|
| `waitForAnimationToEnd` | `timeout: milliseconds` | Wait for animations to complete |
| `extendedWaitUntil` | `timeout: milliseconds` | Wait for element to appear/disappear |
| `scrollUntilVisible` | `timeout: milliseconds` | Scroll until element is visible |

### ❌ Commands That DO NOT Accept Timeout Parameters

| Command | Reason | Alternative |
|---------|--------|-------------|
| `assertVisible` | Auto-waits for elements | Use `extendedWaitUntil` first |
| `assertNotVisible` | Auto-waits for elements | Use `extendedWaitUntil` first |
| `tapOn` | Immediate action | Use `extendedWaitUntil` first |
| `inputText` | Immediate action | Use `extendedWaitUntil` first |
| `takeScreenshot` | Immediate action | N/A |
| `launchApp` | Immediate action | N/A |

## Correct Syntax Examples

### ✅ Valid Timeout Usage

```yaml
# waitForAnimationToEnd with timeout
- waitForAnimationToEnd:
    timeout: 5000

# extendedWaitUntil with timeout
- extendedWaitUntil:
    visible: "Expected Text"
    timeout: 10000

# extendedWaitUntil for element to disappear
- extendedWaitUntil:
    notVisible: "Loading..."
    timeout: 15000
```

### ❌ Invalid Timeout Usage (Will Cause Errors)

```yaml
# ❌ WRONG - assertVisible does not accept timeout
- assertVisible:
    text: "Sign in"
    timeout: 5000

# ❌ WRONG - tapOn does not accept timeout
- tapOn:
    text: "Continue with Google"
    timeout: 3000

# ❌ WRONG - assertNotVisible does not accept timeout
- assertNotVisible:
    text: "Loading..."
    timeout: 10000
```

## Recommended Patterns

### Pattern 1: Timeout Control with Assertions

When you need timeout control for assertions, use this pattern:

```yaml
# ✅ CORRECT - Wait with timeout, then assert
- extendedWaitUntil:
    visible: "Expected Text"
    timeout: 10000
- assertVisible: "Expected Text"
```

### Pattern 2: Wait for Element Before Action

When you need to wait for an element before interacting:

```yaml
# ✅ CORRECT - Wait for element, then interact
- extendedWaitUntil:
    visible: "Continue with Google"
    timeout: 15000
- tapOn: "Continue with Google"
```

### Pattern 3: Animation Handling

For UI transitions and animations:

```yaml
# ✅ CORRECT - Wait for animations to complete
- tapOn: "Account"
- waitForAnimationToEnd:
    timeout: 3000
- assertVisible: "Account"
```

## Real-World Fix Examples

### Example 1: Authentication Progress

```yaml
# ❌ BEFORE (Caused "Unknown Property: timeout" error)
- assertVisible:
    text: "Authenticating..."
    timeout: 15000

# ✅ AFTER (Fixed)
- extendedWaitUntil:
    visible: "Authenticating..."
    timeout: 15000
- assertVisible: "Authenticating..."
```

### Example 2: Profile Loading

```yaml
# ❌ BEFORE (Invalid syntax)
- assertVisible:
    text: ".*@.*"  # User email pattern
    timeout: 10000

# ✅ AFTER (Fixed with simplified selector)
- extendedWaitUntil:
    visible: "@"  # Email indicator
    timeout: 10000
- assertVisible: "@"
```

### Example 3: Sign-in State Verification

```yaml
# ❌ BEFORE (Invalid timeout usage)
- assertVisible:
    text: "Sign in"
    timeout: 5000

# ✅ AFTER (Simplified - assertVisible auto-waits)
- assertVisible: "Sign in"

# ✅ ALTERNATIVE (If explicit timeout needed)
- extendedWaitUntil:
    visible: "Sign in"
    timeout: 5000
- assertVisible: "Sign in"
```

## Best Practices

### 1. Timeout Values

- **Short operations**: 3-5 seconds
- **Network operations**: 15-30 seconds  
- **OAuth flows**: 60-120 seconds
- **Animations**: 2-5 seconds

### 2. Selector Strategy

- Use exact text matches when possible
- Avoid complex regex patterns
- Use simple indicators (e.g., "@" for email) instead of full patterns

### 3. Error Prevention

- Always check Maestro documentation for command syntax
- Use `extendedWaitUntil` when you need timeout control
- Keep assertion commands simple without timeout parameters

### 4. Test Reliability

- Use `waitForAnimationToEnd` after navigation actions
- Implement proper wait patterns for dynamic content
- Test on both emulators and real devices

## Common Error Messages

### "Unknown Property: timeout"

**Cause**: Using timeout parameter on commands that don't support it
**Solution**: Remove timeout or use `extendedWaitUntil` pattern

### "Element not found"

**Cause**: Element not loaded or incorrect selector
**Solution**: Add `extendedWaitUntil` before assertion

### "Timeout exceeded"

**Cause**: Element didn't appear within specified time
**Solution**: Increase timeout or check selector accuracy

## Command Categories

### Immediate Actions (No Timeout Support)

- `tapOn`, `doubleTapOn`, `longPressOn`
- `inputText`, `eraseText`
- `scroll`, `swipe`
- `takeScreenshot`
- `launchApp`, `killApp`

### Assertion Commands (Auto-Wait, No Timeout)

- `assertVisible`, `assertNotVisible`
- `assertTrue`

### Wait Commands (Timeout Support)

- `waitForAnimationToEnd`
- `extendedWaitUntil`
- `scrollUntilVisible`

## Integration Notes

This reference is designed for:

- **Augment Code AI**: Use these patterns when generating Maestro test code
- **IDE Integration**: Provide syntax validation and auto-completion
- **Developer Reference**: Quick lookup for correct timeout syntax
- **Code Review**: Validate Maestro test syntax before merge

## Version Compatibility

This reference is based on Maestro framework as of 2024 and may need updates for future versions. Always verify against the official Maestro documentation for the latest syntax rules.
