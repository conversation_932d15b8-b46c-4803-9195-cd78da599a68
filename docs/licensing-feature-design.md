# Licensing Feature Design

**Version:** 1.0  
**Date:** 2025-04-06

## 1. Overview

The Licensing feature in Promz enables a tiered access model with Free, Pro, Trial, Team, and Enterprise editions. This document outlines the technical implementation of the licensing system, including server-side verification, client-side integration, and subscription management.

## 2. Goals

- Implement a robust, server-centric licensing system with multiple tiers
- Support trial periods with automatic conversion to paid plans
- Enable seamless license verification across platforms
- Provide a flexible framework for affiliate/reseller distribution
- Ensure secure handling of license data and subscription status
- Integrate with LLM model access permissions based on license tier

## 3. Core Concepts

### 3.1. License Types

1. **Free License**

   - Basic functionality with limited features
   - No expiration date
   - Limited to 50 executions per month and basic execution capabilities
   - Prompt creation and sharing only with public visibility
   - No team collaboration features

2. **Pro License**

   - All of Free features
   - Full functionality for individual users and small teams
   - Monthly or annual subscription
   - Unlimited prompts and advanced execution capabilities
   - Basic analytics and priority support
   - Prompt creation and sharing with public and private visibility
   - No team collaboration features
   - Ability to hide instructions as the prompts are shared

3. **Trial License**

   - Time-limited access to Pro features (30 days)
   - Automatic conversion to Pro unless canceled
   - Full Pro functionality during trial period
   - Rate limited to 100 executions per month
   - Prompt creation and sharing only with public visibility
   - No team collaboration features

4. **Team Edition**

   - All of Pro features
   - Team collaboration features
   - Custom pricing based on user count

5. **Team Trial Edition**

   - Time-limited access to Team features (30 days)
   - Automatic conversion to Team unless canceled
   - Full Team functionality during trial period
   - Rate limited to 500 executions per month
   - Prompt creation and sharing only with public visibility
   - No team collaboration features

6. **Enterprise License**
   - All of Pro features
   - Advanced functionality for large organizations
   - Custom pricing based on user count
   - Team collaboration, custom integrations, and premium support
   - Advanced analytics and dedicated account management

### 3.2. License Lifecycle

1. **Creation**: Licenses are created through user registration, trial activation, or purchase
2. **Verification**: Server-side verification confirms license validity and features
3. **Expiration**: Time-limited licenses (Pro Trial, Team Trial) expire based on subscription status
4. **Renewal**: Automatic renewal for subscriptions with valid payment methods
5. **Cancellation**: User-initiated cancellation with grace period until end of billing cycle

## 4. Technical Implementation

### 4.1. Database Schema

**Server (PostgreSQL):**

```sql
-- Licenses table
CREATE TABLE licenses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v7(),
    user_id UUID NOT NULL REFERENCES auth.users(id),
    api_key UUID NOT NULL UNIQUE DEFAULT uuid_generate_v4(),
    license_type TEXT NOT NULL CHECK (license_type IN ('none', 'free', 'trial', 'pro', 'team', 'team_trial', 'enterprise')),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    expiry_date TIMESTAMPTZ,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    metadata JSONB,
    version INTEGER NOT NULL DEFAULT 1
);

CREATE INDEX idx_licenses_user_id ON licenses(user_id);
CREATE INDEX idx_licenses_api_key ON licenses(api_key);
CREATE INDEX idx_licenses_expiry_date ON licenses(expiry_date);

-- User tiers table (for LLM model access)
CREATE TABLE user_tiers (
    tier_id TEXT PRIMARY KEY, -- Tier identifier (e.g., "free", "pro", "enterprise")
    tier_name TEXT NOT NULL,  -- User-friendly tier name
    tier_description TEXT,    -- Description of the tier
    monthly_token_limit BIGINT, -- Monthly token limit (NULL for unlimited)
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Model permissions by tier
CREATE TABLE user_model_permissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v7(),
    tier_id TEXT NOT NULL REFERENCES user_tiers(tier_id) ON DELETE CASCADE,
    provider_id TEXT NOT NULL,
    model_id TEXT NOT NULL,
    is_allowed BOOLEAN NOT NULL DEFAULT false,
    quota_tokens BIGINT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    UNIQUE(tier_id, provider_id, model_id),
    FOREIGN KEY (provider_id, model_id) REFERENCES llm_models(provider_id, model_id) ON DELETE CASCADE
);
```

### 4.2. Server-Side Implementation

#### Database Functions

1. **get_or_create_license_enhanced**

   - Retrieves existing license or creates a new one
   - Handles license type assignment based on user status
   - Sets appropriate expiration for time-limited licenses
   - Example:

     ```sql
     CREATE OR REPLACE FUNCTION get_or_create_license_enhanced(
       p_user_id UUID,
       p_license_type TEXT DEFAULT 'free',
       p_days_valid INT DEFAULT 36500
     )
     RETURNS JSON
     LANGUAGE plpgsql
     SECURITY DEFINER
     AS $$
     DECLARE
       license_record RECORD;
       new_api_key UUID;
       result JSON;
       is_new BOOLEAN := false;
     BEGIN
       -- First, check if the user already has a license of this type
       SELECT * INTO license_record
       FROM public.licenses
       WHERE user_id = p_user_id
       AND license_type = p_license_type
       LIMIT 1;

       -- If license exists, check if it's active
       IF license_record IS NOT NULL THEN
         -- If license is active, return it
         IF license_record.is_active THEN
           result := json_build_object(
             'is_new', false,
             'api_key', license_record.api_key,
             'is_active', license_record.is_active,
             'license_id', license_record.id,
             'expiry_date', license_record.expiry_date,
             'license_type', license_record.license_type
           );
           RETURN result;
         ELSE
           -- If license exists but is inactive, reactivate it with a new expiry date
           UPDATE public.licenses
           SET is_active = true,
               expiry_date = CASE
                               WHEN LOWER(p_license_type) = 'free'
                               THEN now() + (p_days_valid || ' days')::interval
                               ELSE now() + (30 || ' days')::interval
                             END,
               updated_at = now()
           WHERE id = license_record.id
           RETURNING * INTO license_record;

           is_new := false;
         END IF;
       ELSE
         -- Generate a new API key as UUID directly
         new_api_key := gen_random_uuid();

         -- Create a new license
         INSERT INTO public.licenses (
           user_id,
           license_type,
           is_active,
           expiry_date,
           api_key
         ) VALUES (
           p_user_id,
           p_license_type,
           true,
           CASE
             WHEN LOWER(p_license_type) = 'free'
             THEN now() + (p_days_valid || ' days')::interval
             ELSE now() + (30 || ' days')::interval
           END,
           new_api_key
         ) RETURNING * INTO license_record;

         is_new := true;
       END IF;

       -- Return the license information
       result := json_build_object(
         'is_new', is_new,
         'api_key', license_record.api_key,
         'is_active', license_record.is_active,
         'license_id', license_record.id,
         'expiry_date', license_record.expiry_date,
         'license_type', license_record.license_type
       );

       RETURN result;
     END;
     $$;
     ```

2. **verify_api_key_enhanced**

   - Verifies API key and returns detailed license information
   - Handles UUID type casting for API keys
   - Returns license status, type, and expiry information
   - Example:

     ```sql
     CREATE OR REPLACE FUNCTION verify_api_key_enhanced(p_api_key TEXT)
     RETURNS JSON
     LANGUAGE plpgsql
     SECURITY DEFINER
     AS $$
     DECLARE
       license_record RECORD;
       result JSON;
       is_free BOOLEAN;
     BEGIN
       -- Look for the license directly in the licenses table by API key
       SELECT * INTO license_record
       FROM public.licenses
       WHERE api_key = p_api_key::uuid
       LIMIT 1;

       -- If no license found with this API key, return invalid
       IF license_record IS NULL THEN
         result := json_build_object(
           'valid', false,
           'reason', 'invalid_key',
           'error_code', 'LICENSE_NOT_FOUND'
         );
         RETURN result;
       END IF;

       -- Check if license is active
       IF NOT license_record.is_active THEN
         result := json_build_object(
           'valid', false,
           'reason', 'inactive_license',
           'error_code', 'LICENSE_INACTIVE',
           'license_id', license_record.id,
           'user_id', license_record.user_id,
           'license_type', license_record.license_type
         );
         RETURN result;
       END IF;

       -- Check if license is free (free licenses don't expire)
       is_free := LOWER(license_record.license_type) = 'free';

       -- Check if license is expired (only for non-free licenses)
       IF NOT is_free AND license_record.expiry_date < now() THEN
         -- Update license to inactive
         UPDATE public.licenses
         SET is_active = false
         WHERE id = license_record.id;

         result := json_build_object(
           'valid', false,
           'reason', 'expired',
           'error_code', 'LICENSE_EXPIRED',
           'license_id', license_record.id,
           'user_id', license_record.user_id,
           'license_type', license_record.license_type,
           'expiry_date', license_record.expiry_date,
           'api_key', license_record.api_key
         );
         RETURN result;
       END IF;

       -- License is valid - return comprehensive data for caching
       result := json_build_object(
         'valid', true,
         'license_id', license_record.id,
         'user_id', license_record.user_id,
         'license_type', license_record.license_type,
         'is_active', license_record.is_active,
         'expiry_date', license_record.expiry_date,
         'created_at', license_record.created_at,
         'updated_at', license_record.updated_at,
         'is_free', is_free,
         'api_key', license_record.api_key
       );

       RETURN result;
     END;
     $$;
     ```

3. **check_license_status**

   - Checks license status by user ID without requiring API key
   - Used for internal server operations and status checks
   - Prioritizes Pro licenses over other types
   - Example:

     ```sql
     CREATE OR REPLACE FUNCTION check_license_status(p_user_id UUID)
     RETURNS JSON
     LANGUAGE plpgsql
     SECURITY DEFINER
     AS $$
     DECLARE
       license_record RECORD;
       result JSON;
       is_free BOOLEAN;
     BEGIN
       -- Get the user's most recent active license
       SELECT * INTO license_record
       FROM public.licenses
       WHERE user_id = p_user_id
       AND is_active = true
       ORDER BY
         CASE WHEN LOWER(license_type) = 'pro' THEN 1
              WHEN LOWER(license_type) = 'trial' THEN 2
              ELSE 3
         END,
         created_at DESC
       LIMIT 1;

       -- If no license found, return no_license
       IF license_record IS NULL THEN
         result := json_build_object(
           'has_license', false,
           'reason', 'no_license',
           'error_code', 'NO_LICENSE_FOUND'
         );
         RETURN result;
       END IF;

       -- Check if license is free (free licenses don't expire)
       is_free := LOWER(license_record.license_type) = 'free';

       -- Check if license is expired (only for non-free licenses)
       IF NOT is_free AND license_record.expiry_date < now() THEN
         -- Update license to inactive
         UPDATE public.licenses
         SET is_active = false
         WHERE id = license_record.id;

         result := json_build_object(
           'has_license', false,
           'reason', 'expired',
           'error_code', 'LICENSE_EXPIRED',
           'license_id', license_record.id,
           'license_type', license_record.license_type,
           'expiry_date', license_record.expiry_date
         );
         RETURN result;
       END IF;

       -- License is valid
       result := json_build_object(
         'has_license', true,
         'license_id', license_record.id,
         'license_type', license_record.license_type,
         'is_active', license_record.is_active,
         'expiry_date', license_record.expiry_date,
         'created_at', license_record.created_at,
         'updated_at', license_record.updated_at,
         'is_free', is_free,
         'api_key', license_record.api_key,
         'days_remaining',
           CASE
             WHEN is_free THEN 36500
             ELSE GREATEST(0, EXTRACT(DAY FROM (license_record.expiry_date - now())))::integer
           END
       );

       RETURN result;
     END;
     $$;
     ```

#### API Endpoints

1. **/license/verify**

   - Enhanced verification endpoint with comprehensive response data
   - Handles both API key and user ID verification methods
   - Returns detailed license information including status, type, and expiry
   - Example response:

     ```json
     {
       "valid": true,
       "license_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
       "user_id": "u1v2w3x4-y5z6-7890-abcd-ef1234567890",
       "license_type": "pro",
       "is_active": true,
       "expiry_date": "2026-04-06T00:00:00Z",
       "created_at": "2025-04-06T00:00:00Z",
       "updated_at": "2025-04-06T00:00:00Z",
       "is_free": false,
       "api_key": "k1l2m3n4-o5p6-7890-abcd-ef1234567890"
     }
     ```

2. **/license/status**

   - New endpoint to check license status by user ID
   - Used for client-side status checks after authentication
   - Example response:

     ```json
     {
       "has_license": true,
       "license_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
       "license_type": "trial",
       "is_active": true,
       "expiry_date": "2025-05-06T00:00:00Z",
       "created_at": "2025-04-06T00:00:00Z",
       "updated_at": "2025-04-06T00:00:00Z",
       "is_free": false,
       "api_key": "k1l2m3n4-o5p6-7890-abcd-ef1234567890",
       "days_remaining": 30
     }
     ```

3. **/license/get-or-create**

   - Improved endpoint for license creation and retrieval
   - Supports different license types (free, trial, pro, team, enterprise)
   - Handles expiration dates and activation status
   - Example request:

     ```json
     {
       "user_id": "u1v2w3x4-y5z6-7890-abcd-ef1234567890",
       "license_type": "trial"
     }
     ```

   - Example response:

     ```json
     {
       "is_new": true,
       "api_key": "k1l2m3n4-o5p6-7890-abcd-ef1234567890",
       "is_active": true,
       "license_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
       "expiry_date": "2025-05-06T00:00:00Z",
       "license_type": "trial"
     }
     ```

### 4.3. LLM Model Access Integration

The licensing system integrates with the LLM model access control system to determine which AI models a user can access based on their license tier.

1. **UserContext in Execution Flow**

   - The `ExecuteRequest` includes a `UserContext` field containing license information
   - The `ModelSelectionService` uses this context to select appropriate models
   - Example implementation:

     ```go
     // UserContext contains user-specific information for model selection
     type UserContext struct {
         UserID    string
         Tier      string // Derived from license type
         APIKey    string // User's API key for verification
         TokenQuota int64  // Available token quota based on tier
     }

     // ModelSelectionService interface
     type ModelSelectionService interface {
         SelectModelForProvider(ctx context.Context, userCtx *UserContext,
                              providerName string, preferredModel string) (*ModelInfo, error)
     }
     ```

2. **User Tiers to License Types Mapping**

   - Each license type maps to a user tier for LLM access
   - Free license → free tier
   - Trial license → trial tier
   - Pro license → pro tier
   - Team license → team tier
   - Enterprise license → enterprise tier

3. **Model Access Control**

   - The `user_model_permissions` table defines which models are available to each tier
   - Each tier has specific quota limits for token usage
   - The `available_models_by_tier` view simplifies querying available models
   - The executor attempts to select models based on the user's tier permissions

4. **Execution Flow with License Integration**

   - When a prompt execution request is received:
     1. The user's license is verified via their API key
     2. License information is converted to a `UserContext` with appropriate tier
     3. `ModelSelectionService` selects an appropriate model based on the user's tier
     4. If the user doesn't have access to the requested model, fallback to an available model
     5. If no models are available for the user's tier, return an appropriate error

5. **Token Usage Tracking**
   - Monthly token limits are enforced based on the user's tier
   - Usage is tracked and reset monthly
   - Exceeding limits may result in rate limiting or requiring an upgrade

### 4.4. Client-Side Implementation

#### Models

1. **LicenseType Enum**

   ```dart
   enum LicenseType {
     none,
     free,
     trial,
     pro,
     team,
     team_trial,
     enterprise;

     static LicenseType getLicenseTypeFromString(String? value) {
       if (value == null) return LicenseType.none;

       switch (value.toLowerCase()) {
         case 'free': return LicenseType.free;
         case 'trial': return LicenseType.trial;
         case 'pro': return LicenseType.pro;
         case 'team': return LicenseType.team;
         case 'team_trial': return LicenseType.team_trial;
         case 'enterprise': return LicenseType.enterprise;
         default: return LicenseType.none;
       }
     }

     static String formatLicenseType(LicenseType? type, {bool hasLicense = false}) {
       if (!hasLicense || type == null) return 'No License';

       switch (type) {
         case LicenseType.free: return 'Free';
         case LicenseType.trial: return 'Pro Trial';
         case LicenseType.pro: return 'Pro';
         case LicenseType.team: return 'Team';
         case LicenseType.team_trial: return 'Team Trial';
         case LicenseType.enterprise: return 'Enterprise';
         default: return 'Unknown';
       }
     }
   }
   ```

2. **UserLicense Model**

   ```dart
   class UserLicense {
     final bool hasLicense;
     final LicenseType licenseType;
     final DateTime? expiresAt;
     final bool isActive;
     final int? daysRemaining;

     const UserLicense({
       this.hasLicense = false,
       this.licenseType = LicenseType.none,
       this.expiresAt,
       this.isActive = false,
       this.daysRemaining,
     });

     factory UserLicense.empty() {
       return const UserLicense(
         hasLicense: false,
         licenseType: LicenseType.none,
         isActive: false,
       );
     }

     factory UserLicense.fromJson(Map<String, dynamic> json) {
       return UserLicense(
         hasLicense: json['has_license'] ?? false,
         licenseType: LicenseType.getLicenseTypeFromString(json['license_type']),
         expiresAt: json['expiry_date'] != null
             ? DateTime.parse(json['expiry_date'])
             : null,
         isActive: json['is_active'] ?? false,
         daysRemaining: json['days_remaining'],
       );
     }

     UserLicense copyWith({
       bool? hasLicense,
       LicenseType? licenseType,
       DateTime? expiresAt,
       bool? isActive,
       int? daysRemaining,
     }) {
       return UserLicense(
         hasLicense: hasLicense ?? this.hasLicense,
         licenseType: licenseType ?? this.licenseType,
         expiresAt: expiresAt ?? this.expiresAt,
         isActive: isActive ?? this.isActive,
         daysRemaining: daysRemaining ?? this.daysRemaining,
       );
     }
   }
   ```

#### Services

1. **LicenseManagerService**

   - Handles license verification and management
   - Interacts with server API for license operations
   - Provides methods for checking license status and creating licenses
   - Example implementation:

     ```dart
     class LicenseManagerService {
       static const _logName = 'LicenseManagerService';
       final ApiClient _apiClient;

       LicenseManagerService({ApiClient? apiClient})
           : _apiClient = apiClient ?? ApiClient();

       Future<UserLicense> verifyLicense() async {
         try {
           final user = Supabase.instance.client.auth.currentUser;
           if (user == null) {
             appLog.debug('No authenticated user found', name: _logName);
             return UserLicense.empty();
           }

           final response = await _apiClient.get('/license/status');

           if (response.statusCode == 200) {
             final data = json.decode(response.body);
             return UserLicense.fromJson(data);
           } else {
             appLog.error(
               'License verification failed: ${response.statusCode}',
               name: _logName
             );
             return UserLicense.empty();
           }
         } catch (e) {
           appLog.error('Error verifying license', name: _logName, error: e);
           return UserLicense.empty();
         }
       }

       Future<bool> ensureUserHasFreeLicense() async {
         try {
           final user = Supabase.instance.client.auth.currentUser;
           if (user == null) {
             appLog.debug('No authenticated user found', name: _logName);
             return false;
           }

           final response = await _apiClient.post(
             '/license/get-or-create',
             body: json.encode({
               'user_id': user.id,
               'license_type': 'free'
             })
           );

           return response.statusCode == 200;
         } catch (e) {
           appLog.error('Error creating free license', name: _logName, error: e);
           return false;
         }
       }

       Future<bool> generateProTrial() async {
         try {
           final user = Supabase.instance.client.auth.currentUser;
           if (user == null) {
             appLog.debug('No authenticated user found', name: _logName);
             return false;
           }

           final response = await _apiClient.post(
             '/license/get-or-create',
             body: json.encode({
               'user_id': user.id,
               'license_type': 'trial'
             })
           );

           return response.statusCode == 200;
         } catch (e) {
           appLog.error('Error generating pro trial', name: _logName, error: e);
           return false;
         }
       }
     }
     ```

2. **UserProfileService**

   - Centralized service for managing user profile data
   - Integrates with license management for profile updates
   - Example implementation:

     ```dart
     void updateProfileFromLicense(UserLicense? license) {
       if (license == null) return;

       // Update profile with license data
       _profile = _profile.copyWith(
         hasLicense: license.hasLicense,
         licenseType: license.licenseType,
         licenseExpiresAt: license.expiresAt,
       );

       // Save to cache and notify listeners
       _saveToCache();
       notifyListeners();
     }
     ```

#### ViewModels

1. **UserProfileViewModel**

   - Handles user authentication and license management
   - Provides methods for signing in, signing out, and managing licenses
   - Example implementation:

     ```dart
     Future<bool> createFreeLicense() async {
       state = state.clearError().setLoading(true);

       try {
         final licenseService = _ref.read(licenseManagerProvider);

         // Create a free license
         final success = await licenseService.ensureUserHasFreeLicense();

         if (success) {
           appLog.debug('Free license created successfully', name: _logName);

           // Refresh license state
           final _ = _ref.refresh(licenseStateProvider);

           state = state.setLoading(false);
           return true;
         } else {
           appLog.debug('Failed to create free license', name: _logName);
           state = state.copyWith(
             isLoading: false,
             errorMessage: 'Failed to create free license. Please try again later.',
           );
           return false;
         }
       } catch (e) {
         appLog.error('Error creating free license', name: _logName, error: e);
         state = state.copyWith(
           isLoading: false,
           errorMessage: 'Error: ${e.toString()}',
         );
         return false;
       }
     }
     ```

### 4.5. API Configuration

1. **Server URL Configuration**

   - Debug mode: `http://localhost:8080`
   - Release mode: `https://api.promz.ai`
   - Android emulator: `http://********:8080`
   - iOS/macOS: `http://localhost:8080`

2. **Authentication Token Handling**

   - Normal operation: Uses Supabase session access token
   - Debug mode: Falls back to a mock token when no session is available
   - Includes improved error handling and logging

3. **License Verification Flow**
   - Enhanced endpoints use `/license/` prefix
   - Server-side verification with detailed response data
   - Client handles authentication failures gracefully in debug mode

## 5. User Flows

### 5.1. New User Registration

1. User signs up with Google or Apple authentication
2. Authentication success triggers license creation
3. Free license is automatically created for new users
4. User profile is updated with license information
5. UI reflects the user's license status

### 5.2. Pro Trial Activation

1. User with Free license requests Pro trial
2. Client calls `/license/get-or-create` with `trial` type
3. Server creates a 30-day trial license
4. Client refreshes license state and updates UI
5. User receives confirmation of trial activation

### 5.3. License Verification

1. App startup triggers license verification
2. Client calls `/license/status` endpoint
3. Server verifies license status and returns details
4. Client updates user profile with license information
5. UI components adapt based on license features

## 6. Affiliate/Reseller Model

### 6.1. Implementation

1. **License Key Generation**

   - Server generates unique license keys for affiliates
   - Keys are linked to specific license types and durations
   - Example endpoint: `/license/generate-affiliate-keys`

2. **Redemption Process**

   - Users enter affiliate license keys in the app
   - Client validates and activates the license
   - Example endpoint: `/license/redeem-key`

3. **Tracking and Analytics**
   - Server tracks license key usage and affiliate performance
   - Affiliates receive reports on key redemptions and revenue
   - Example endpoint: `/affiliate/performance`

## 7. Security Considerations

1. **API Key Protection**

   - API keys are stored as UUIDs in the database
   - Keys are never exposed in client-side code
   - All verification happens server-side

2. **License Data Integrity**

   - Database constraints ensure valid license types
   - Transaction support for license operations
   - Version tracking for license updates

3. **Subscription Data Security**
   - Payment information handled by third-party providers
   - Minimal payment data stored in the application
   - Secure handling of subscription status

## 8. Future Improvements

1. **License Feature Flags**

   - Implement granular feature flags for different license tiers
   - Allow dynamic enabling/disabling of features
   - Support A/B testing of premium features

2. **Team License Management**

   - Add support for team admin to manage member licenses
   - Implement seat-based licensing for Enterprise tier
   - Provide usage analytics for team administrators

3. **Subscription Management UI**

   - Create dedicated UI for managing subscriptions
   - Support upgrading/downgrading between tiers
   - Provide billing history and invoice access

4. **Offline License Verification**
   - Implement secure offline license verification
   - Cache license status for offline usage
   - Periodic online verification with grace period

## 9. Implementation Timeline

1. **Phase 1 (Q2 2025)**

   - Basic license types (Free, Pro, Enterprise)
   - Server-side verification endpoints
   - Client-side integration

2. **Phase 2 (Q3 2025)**

   - Trial license implementation
   - Subscription management
   - Basic affiliate model

3. **Phase 3 (Q4 2025)**
   - Advanced affiliate features
   - Team licensing
   - Feature flags system

## 10. Conclusion

The licensing system provides a robust foundation for monetization while ensuring a seamless user experience. By implementing a server-centric approach with comprehensive client integration, we can support various license types and subscription models while maintaining security and scalability.

---
