# Popular Prompts Feature Design

**Version:** 2.0
**Date:** 2025-04-24

## Document History

| Version | Date       | Description                                         |
| ------- | ---------- | --------------------------------------------------- |
| 2.0     | 2025-04-24 | Updated to include websocket implementation         |
| 1.1     | 2025-03-29 | Added endpoints for prompt usage tracking           |
| 1.0     | 2025-02-14 | Initial design document                             |

## 0. Introduction

This document outlines the design for the "Popular Prompts" feature in the Promz application. The
goal is to display prompts that are frequently used by the user community, moving beyond the current
local-only implementation.

The updated design introduces real-time updates using WebSockets and efficient data transfer using
gRPC to replace the current REST API with polling approach. This enhancement will improve
performance, reduce server load, and provide a better user experience across all platforms (mobile,
desktop, and web).

## 1. Goals

- Implement server-side tracking of prompt usage, including anonymized regional context.
- Provide real-time updates of popular prompts using WebSockets.
- Implement efficient data transfer using gRPC for recording usage and fetching popular prompts.
- Ensure cross-platform compatibility (mobile, desktop, and web).
- Maintain backward compatibility during transition.
- Display popular prompts in the client UI with immediate updates.

## 2. MVP (Minimum Viable Product)

### 2.1. Defining Popularity

For the MVP, popularity will be determined by a simple count of usage events recorded for each prompt.

### 2.2. Client-Side Implementation (Flutter)

- **Local Usage Tracking:** ✅ DONE
  - `local_prompt_usage` table implemented
  - Usage events recorded through `PromptUsageService`
- **Synchronization:** ✅ DONE
  - `BackgroundTaskService` implements periodic syncing
  - Usage events are batched and sent to server
  - `popularPrompts` cache is updated periodically
- **Geo-Data:** 🔄 IN PROGRESS
  - Need to add country code retrieval from device locale
  - Implementation planned using `intl` package
- **Fetching Popular Prompts:**
  - The `HomeViewModel` (or a dedicated service) will periodically (e.g., once daily, configurable
    rate limit) call a new server API endpoint to get the list of popular prompts.
  - The fetched list (likely prompt IDs) will be used to retrieve full prompt details from the local
    database or another service.
  - The `HomeViewModel` will update its `popularPrompts` list, triggering a UI refresh in
    `SourceInputSection` -> `PromptList`.
- **UI:**
  - The existing `PromptList` widget will be used to display the popular prompts fetched from the
    server.

### 3. Server-Side Implementation (Go)

- **Database (PostgreSQL):**
  - A new table, `prompt_usage_log`, will be added to the server schema.
  - Schema: `log_id UUID PRIMARY KEY DEFAULT uuid_generate_v7(), prompt_id UUID NOT NULL REFERENCES prompts(id) ON DELETE CASCADE, event_type TEXT NOT NULL CHECK(event_type IN ('selected', 'executed')), timestamp TIMESTAMP WITH TIME ZONE NOT NULL, country_code TEXT NULL`
  - Index on `prompt_id`, `timestamp`, and potentially `country_code`.
  - A cache table, `popular_prompts`, will store pre-calculated popular prompts: `id UUID PRIMARY KEY, prompt_id UUID REFERENCES prompts(id) ON DELETE CASCADE, rating INTEGER DEFAULT 0, server_sync_at TIMESTAMP WITH TIME ZONE`
- **API Endpoints:**
  - `POST /usage/prompt`:
    - Accepts a JSON array of usage events from the client: `[{ "prompt_id": "...", "event_type": "...", "timestamp": "...", "country_code": "..." }, ...]` (country_code is optional).
    - Validates the input.
    - Inserts the records into the `prompt_usage_log` table, including the `country_code`. **Crucially, it does NOT store any user-identifying information from the client request in this MVP.**
    - Returns a success status (e.g., 204 No Content).
  - `GET /popular-prompts`:
    - Accepts optional query parameters: `limit` (default 10), `days` (e.g., 30, for future weighting).
    - Returns a JSON array of popular prompt objects (or just IDs): `[{ "prompt_id": "...", "score": ... }, ...]`.
- **Services:**
  - A new `PopularityService` will encapsulate the logic for recording usage and calculating popularity.
  - Methods include:
    - `RecordUsageEvents`: Records prompt usage events to the database
    - `GetPopularPrompts`: Retrieves popular prompts based on a time period
    - `UpdatePopularPromptsCache`: Updates the cached popular prompts in the database
  - A new `BackgroundService` will periodically update the popular prompts cache.

### 3.4. Database Functions

- **get_popular_prompts:**
  - Calculates popular prompts based on usage count within a specified time period
  - Parameters: `p_limit` (default 10), `p_days` (default 30)
  - Returns a table with `prompt_id` and `score` columns
  - Implementation uses the `prompt_usage_log` table to count events within the specified time period

- **record_prompt_usage_events:**
  - Records prompt usage events in the `prompt_usage_log` table
  - Parameters: `p_events` (JSON array of prompt usage events)
  - Handles event data with `prompt_id`, `event_type`, `timestamp`, and `country_code`
  - Returns success status or error information

- **update_popular_prompts_cache:**
  - Updates the `popular_prompts` table with the latest popular prompts
  - Parameters: `p_prompts` (JSON array of popular prompts with `prompt_id` and `score`)
  - Clears existing cache and inserts new popular prompts
  - Updates the `server_sync_at` timestamp to track when the cache was last updated

### 3.5. Background Processing

- A `BackgroundService` runs on the server to periodically update the popular prompts cache:
  - Runs at configurable intervals (default: every 24 hours)
  - Calls `PopularityService.UpdatePopularPromptsCache` to refresh the cache
  - Uses a timeout context to ensure the operation completes within a reasonable time
  - Provides logging for monitoring and debugging

- The caching mechanism ensures:
  - Efficient retrieval of popular prompts without recalculating on each request
  - Persistence across server restarts
  - Consistent cache across multiple server instances
  - Clear tracking of when the cache was last updated

### 3.6. Data Flow

1. **Client:** User selects/executes a prompt.
2. **Client:** `PromptUsageService` records `selected` or `executed` event in `local_prompt_usage`, including the device's country code.
3. **Client (Periodically):** `PromptUsageService` checks `local_prompt_usage`. If not empty, sends batched usage data to `POST /usage/prompt`.
4. **Server:** API receives data, validates, and stores it anonymously (including country code) in `prompt_usage_log` using the `record_prompt_usage_events` function.
5. **Server:** API returns success to the client.
6. **Client:** `PromptUsageService` clears `local_prompt_usage`.
7. **Server (Background):** `BackgroundService` periodically calls `PopularityService.UpdatePopularPromptsCache` to refresh the cache.
8. **Server (Background):** `PopularityService` gets popular prompts using `get_popular_prompts` and updates the cache using `update_popular_prompts_cache`.
9. **Client (Periodically):** `HomeViewModel` calls `GET /popular-prompts`.
10. **Server:** API returns the list of popular prompts from the `popular_prompts` cache table.
11. **Client:** `HomeViewModel` updates its state, UI refreshes.

## 4. Real-time Updates with WebSockets

### 4.1. Server-Side Implementation

- **WebSocket Handler:**
  - Create a new WebSocket handler in Go to manage client connections
  - Implement a topic-based subscription system
  - Add authentication and authorization for connections

```go
// WebSocketHandler manages WebSocket connections
type WebSocketHandler struct {
    clients    map[string]*Client
    register   chan *Client
    unregister chan *Client
    broadcast  chan *Message
}

// Client represents a connected WebSocket client
type Client struct {
    conn      *websocket.Conn
    userID    string
    topics    map[string]bool
}

// Message represents a message to be broadcast to clients
type Message struct {
    topic   string
    payload interface{}
}
```

- **Integration with Existing Services:**
  - Modify `PopularityService` to broadcast updates when popular prompts change
  - Add a mechanism to trigger broadcasts when the popular prompts cache is updated
  - Implement proper error handling and reconnection logic

### 4.2. Client-Side Implementation

- **WebSocket Service:**
  - Create a new WebSocket service in Flutter to manage connections
  - Implement topic-based subscription
  - Handle connection lifecycle based on app state

```dart
class WebSocketService {
  final WebSocketChannel _channel;
  final StreamController<Map<String, dynamic>> _messageController;
  final Map<String, StreamController> _topicControllers = {};
  
  // Subscribe to specific topics
  Stream<T> subscribe<T>(String topic, T Function(Map<String, dynamic>) converter) {
    // Implementation
  }
  
  // Connection lifecycle management
  void connect() {
    // Implementation
  }
  
  void disconnect() {
    // Implementation
  }
}
```

- **Integration with Existing Services:**
  - Update `PromptUsageService` to use WebSockets for fetching popular prompts
  - Modify `HomeViewModel` to subscribe to real-time updates
  - Implement proper connection lifecycle management based on app state

### 4.3. Data Flow

1. **Server:** `PopularityService` updates the popular prompts cache
2. **Server:** WebSocket handler broadcasts the update to all clients subscribed to the "popular_prompts" topic
3. **Client:** WebSocket service receives the update and notifies subscribers
4. **Client:** `HomeViewModel` updates its state, UI refreshes immediately

## 5. Efficient Data Transfer with gRPC

### 5.1. Protocol Buffer Definitions

```protobuf
syntax = "proto3";

package popularity;

service PopularityService {
  // Record usage events for prompts
  rpc RecordUsageEvents(UsageEventsRequest) returns (UsageEventsResponse);
  
  // Get popular prompts
  rpc GetPopularPrompts(PopularPromptsRequest) returns (PopularPromptsResponse);
  
  // Subscribe to popular prompts updates
  rpc SubscribeToPopularPrompts(SubscriptionRequest) returns (stream PopularPromptsUpdate);
}

message UsageEvent {
  string prompt_id = 1;
  string event_type = 2;
  int64 timestamp = 3;
  string country_code = 4;
}

message UsageEventsRequest {
  repeated UsageEvent events = 1;
}

message UsageEventsResponse {
  bool success = 1;
  string error = 2;
}

message PopularPromptsRequest {
  int32 limit = 1;
  int32 days = 2;
}

message PopularPrompt {
  string prompt_id = 1;
  int32 score = 2;
  // Additional prompt data fields
  string title = 3;
  string content = 4;
  repeated string tags = 5;
}

message PopularPromptsResponse {
  repeated PopularPrompt prompts = 1;
}

message SubscriptionRequest {
  string user_id = 1;
}

message PopularPromptsUpdate {
  repeated PopularPrompt prompts = 1;
}
```

### 5.2. Server-Side Implementation

- **gRPC Service:**
  - Implement the gRPC service defined in the Protocol Buffers
  - Integrate with existing business logic
  - Add streaming support for real-time updates

- **Integration with Existing Services:**
  - Update `PopularityService` to use gRPC for recording usage and fetching popular prompts
  - Maintain backward compatibility with REST endpoints during transition

### 5.3. Client-Side Implementation

- **gRPC Client:**
  - Generate Dart code from Protocol Buffers
  - Implement gRPC client in Flutter
  - Add proper error handling and reconnection logic

- **Integration with Existing Services:**
  - Update `PromptUsageService` to use gRPC for recording usage and fetching popular prompts
  - Maintain backward compatibility with REST endpoints during transition

## 6. Cross-Platform Considerations

### 6.1. Mobile Platforms (Android/iOS)

- **Battery Optimization:**
  - Implement connection management based on app lifecycle events
  - Use keep-alive mechanisms to maintain connections efficiently
  - Implement reconnection logic with exponential backoff

- **Offline Support:**
  - Cache popular prompts locally for offline access
  - Queue usage events when offline and sync when online

### 6.2. Desktop Platforms (macOS/Windows)

- **Performance Optimization:**
  - Maintain persistent connections for real-time updates
  - Implement more aggressive caching strategies
  - Use platform-specific optimizations where available

### 6.3. Web Platform

- **WebSocket Support:**
  - Use native WebSockets for real-time updates
  - Implement fallback to polling when WebSockets are unavailable

- **gRPC Support:**
  - Use gRPC-Web for compatibility with web browsers
  - Implement fallback to REST when gRPC-Web is unavailable

## 7. Implementation Timeline

### Phase 1: WebSockets for Real-time Updates (2-3 Weeks)

1. **Week 1:** Implement server-side WebSocket handler and integration with existing services
2. **Week 2:** Implement client-side WebSocket service and integration with existing services
3. **Week 3:** Testing, optimization, and bug fixes

### Phase 2: gRPC for Efficient Data Transfer (3-4 Weeks)

1. **Week 1-2:** Define Protocol Buffers and implement server-side gRPC service
2. **Week 3:** Generate Dart code and implement client-side gRPC client
3. **Week 4:** Integration, testing, and optimization

## 8. Future Considerations

- **Rating System:**
  - Add UI elements (e.g., thumbs up/down) for users to rate prompts
  - Create a new table (e.g., `prompt_ratings`) on the server
  - Add gRPC methods for submitting and retrieving ratings
  - Incorporate ratings into the popularity calculation

- **Advanced Popularity Algorithm:**
  - Weight recent usage/ratings more heavily (e.g., using the `days` parameter or an exponential decay function)
  - Incorporate `country_code` into popularity calculations (e.g., popular prompts per region)
  - Consider personalization (though this requires user identification)

- **Usage Anonymization:** Formalize the anonymization strategy if user linking is ever considered

- **Configuration:** Make connection parameters and update frequencies configurable via the server

## 9. Open Questions / Decisions

- Determine the appropriate authentication mechanism for WebSocket connections
- Decide on the frequency of popular prompts updates via WebSockets
- Establish fallback strategies when advanced protocols are unavailable
- Define the approach for handling connection issues and reconnection attempts
