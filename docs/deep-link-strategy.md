# Deep Link Strategy

**Version:** 1.0  
**Date:** 2025-05-07

## 1. Overview

This document outlines the deep linking strategy for the Promz application, including URL formats, platform-specific implementations, and UUID shortening techniques. Deep links enable seamless navigation to specific content within the application from external sources, enhancing the sharing experience.

## 2. URL Formats

### 2.1. Web URL Format

```text
https://www.promz.ai/p/{shortId}
```

Example: `https://www.promz.ai/p/AAYy8i`

This format is used for web-based sharing and is designed to be:

- Memorable and concise
- SEO-friendly
- Compatible with social media platforms

### 2.2. Mobile Deep Link Format

```text
promz://p/{shortId}
```

Example: `promz://p/AAYy8i`

This format is used for mobile app deep linking and follows a path-based approach to:

- Preserve case sensitivity
- Follow standard URI conventions
- Maintain consistency with web URL structure

### 2.3. Legacy Format Support

```text
promz://{shortId}
```

Example: `promz://AAYy8i`

This format is supported for backward compatibility but is deprecated in favor of the path-based approach.

## 3. UUID Shortening Strategy

### 3.1. Algorithm

The UUID shortening algorithm uses base64Url encoding to compress 32-character UUIDs into 5-6 character strings:

1. Remove hyphens from the UUID
2. Convert the hex string to bytes
3. Apply base64Url encoding
4. Take the first 6 characters

This approach provides:

- Compact, URL-friendly identifiers
- Sufficient uniqueness for sharing purposes
- Case-sensitive encoding for maximum information density

### 3.2. Case Sensitivity Considerations

The shortening algorithm produces case-sensitive IDs that may include both uppercase and lowercase letters. The path-based approach (`promz://p/{shortId}`) ensures that case sensitivity is preserved, as URI path components maintain their original case, unlike hostnames which are normalized to lowercase.

## 4. Platform-Specific Implementation

### 4.1. Android

Android implementation uses intent filters in the AndroidManifest.xml:

```xml
<intent-filter>
    <action android:name="android.intent.action.VIEW" />
    <category android:name="android.intent.category.DEFAULT" />
    <category android:name="android.intent.category.BROWSABLE" />
    <data android:scheme="promz" />
    <data android:pathPattern="/p/*" />
</intent-filter>
```

This configuration handles both new path-based links and legacy format links.

### 4.2. iOS

iOS implementation uses URL scheme registration in Info.plist:

```xml
<dict>
    <key>CFBundleTypeRole</key>
    <string>Editor</string>
    <key>CFBundleURLName</key>
    <string>promz</string>
    <key>CFBundleURLSchemes</key>
    <array>
        <string>promz</string>
    </array>
</dict>
```

Additionally, Universal Links are configured for web URLs:

```xml
<key>com.apple.developer.associated-domains</key>
<array>
    <string>applinks:www.promz.ai</string>
    <string>applinks:promz.ai</string>
</array>
```

### 4.3. Windows

Windows implementation uses custom protocol registration through the Windows Registry:

```text
HKEY_CLASSES_ROOT
   promz
      (Default) = "URL:Promz Protocol"
      URL Protocol = ""
      shell
         open
            command
               (Default) = "{app path}" "%1"
```

This allows the application to handle `promz://` protocol links on Windows.

## 5. Deep Link Handling Flow

1. User receives or clicks a deep link
2. Operating system routes the link to the Promz application
3. Application extracts the short ID from the URL
4. Short ID is expanded to a UUID
5. Application checks if the prompt exists in the local database
6. If found, user is navigated to the prompt
7. If not found, user is prompted to search online

## 6. Security Considerations

### 6.1. Input Validation

All deep link parameters are validated before processing to prevent injection attacks.

### 6.2. Authentication

Deep links to protected content require authentication before displaying the content.

### 6.3. Rate Limiting

Server-side rate limiting is implemented to prevent abuse of the sharing endpoints.

## 7. Analytics

Deep link interactions are tracked to measure:

- Click-through rates from different sources
- Conversion rates for shared content
- Platform distribution of deep link usage
- Success/failure rates of deep link resolution

## 8. Future Enhancements

### 8.1. Deferred Deep Linking

Future implementation will support deferred deep linking, allowing users without the app installed to be directed to the app store and then to the specific content after installation.

### 8.2. Dynamic Links

Support for dynamic links that can adapt based on user context, device, and other factors.

### 8.3. Branch-Specific Links

Support for branch-specific deep links for A/B testing and targeted campaigns.

---

This document should be reviewed and updated as the deep linking strategy evolves.
