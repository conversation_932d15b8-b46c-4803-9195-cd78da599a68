# Promz Product Requirements Document

## 1. Introduction

### 1.1 Purpose

Promz is a comprehensive platform for managing and sharing AI prompts, featuring a Flutter-based
client application, a Go-based API server, and a Flutter web admin interface. This document outlines
the product requirements for Promz, highlighting both implemented features (MVP) and planned future
enhancements.

### 1.2 Scope

This PRD covers the core functionality of the Promz platform, including the mobile client
application, API server, and admin interface. It defines the requirements for prompt management,
variable handling, entity detection, user authentication, license management, and other key
features.

### 1.3 Product Vision

Promz aims to provide a seamless experience for managing and sharing AI prompts across different
platforms, enabling users to create, organize, and execute AI prompts with intelligent variable
handling and entity detection.

## 2. User Personas

### 2.1 Individual Users

- AI enthusiasts who want to create and manage prompts for personal use
- Professionals who use AI tools regularly and need to organize their prompts
- Content creators who want to share prompts with their audience
- Individuals who are interested in AI and want to execute AI prompts without prior knowledge

### 2.2 Team Users

- Teams that collaborate on AI prompt creation and management
- Organizations that need to share prompts internally
- Businesses that want to standardize their AI interactions

## 3. Feature Requirements

| Feature                       | Description                               | MVP |
| ----------------------------- | ----------------------------------------- | --- |
| **Prompt Management**         | Create, view, edit, and delete prompts    | Yes |
| **Variable System**           | Support for template variables in prompts | Yes |
| **Entity Detection**          | Automatic detection of entities in text   | Yes |
| **Prompt Suggestions**        | AI-powered prompt suggestions             | Yes |
| **LLM Execution**             | Execute prompts with LLM providers        | Yes |
| **User Authentication**       | Sign in with Google, Apple, or email      | Yes |
| **License Management**        | Free, trial, and paid license tiers       | Yes |
| **WhatsApp Export**           | Integration with WhatsApp                 | No  |
| **Custom Prompts**            | "Use Your Own Prompt" feature             | No  |
| **Topics/Portfolio**          | Organize LLM outputs into logical groups  | No  |
| **Google Drive Integration**  | Save outputs to Google Drive              | No  |
| **Nested Topics**             | Parent-child topic relationships          | No  |
| **Real-time Collaboration**   | Collaborative prompt editing              | No  |
| **Advanced Entity Detection** | Enhanced entity recognition capabilities  | No  |
| **Cross-platform Support**    | Web and desktop applications              | No  |

## 4. Detailed Requirements

### 4.1 Prompt Management

#### 4.1.1 Prompt Storage

- **Database Schema**: Store prompts with title, category, subtitle, source, keywords, and metadata
- **Synchronization**: Support for local and cloud storage with synchronization
- **Version Control**: Track prompt versions and changes

#### 4.1.2 Prompt Creation and Editing

- **Title and Description**: Allow users to set a title and description for each prompt
- **Categories**: Organize prompts into categories
- **Keywords**: Add keywords for better searchability
- **Source Tracking**: Track the source of each prompt (local or cloud)

#### 4.1.3 Prompt Usage

- **Usage Tracking**: Track when prompts are used
- **Popularity Metrics**: Calculate popularity based on usage
- **Recent Prompts**: Show recently used prompts

### 4.2 Variable System

#### 4.2.1 Variable Types

- **Entity Variables**: Variables linked to known entity types (finance, location, person, etc.)
- **Custom Variables**: User-defined variables
- **Prefixed Variables**: Support for multiple instances of the same variable type via prefixes

#### 4.2.2 Variable Resolution

- **Template Format**: Support for `{{CATEGORY:ENTITY}}` format
- **Variable Extraction**: Extract variables from templates
- **Value Storage**: Centralized variable value storage
- **Normalized Lookups**: Support for normalized variable name lookups

#### 4.2.3 Variable UI

- **Variables Dialog**: Modal interface for editing template variables
- **Variables Section**: Compact display of active variables as chips
- **Autocomplete Field**: Intelligent text input with suggestions

### 4.3 Entity Detection

#### 4.3.1 Entity Types

- **Financial Entities**: Stock tickers, company names, etc.
- **Location Entities**: Cities, countries, etc.
- **Person Entities**: Names, titles, etc.

#### 4.3.2 Detection Methods

- **Template Pattern**: Detect entities that follow the template pattern `{{TYPE:ENTITY}}`
- **Dictionary Matching**: Match entities against dictionaries
- **Fuzzy Matching**: Support for approximate matches

#### 4.3.3 Entity Processing

- **Entity Normalization**: Normalize entities for consistent processing
- **Entity Display**: User-friendly display of entities
- **Entity Transformation**: Transform entities for use in prompts

### 4.4 Prompt Suggestions

#### 4.4.1 Suggestion Methods

- **Keyword Matching**: Match prompts based on keywords
- **Category Awareness**: Consider categories in suggestions
- **Relevance Ranking**: Score-based relevance ranking
- **Recent and Popular**: Track recent and popular prompts

#### 4.4.2 Suggestion UI

- **Real-time Suggestions**: Show suggestions as users type
- **Suggestion Display**: Display suggestions with relevance information
- **Selection Handling**: Handle selection of suggestions

### 4.5 LLM Execution

#### 4.5.1 Providers

- **OpenAI**: Integration with OpenAI models
- **Google AI (Gemini)**: Integration with Google's Gemini models
- **Provider Fallback**: Support for fallback between providers

#### 4.5.2 Execution Parameters

- **Max Tokens**: Control response length
- **Temperature**: Control randomness/creativity
- **Model Selection**: Select specific models based on user tier

#### 4.5.3 Response Handling

- **Text Processing**: Process and display LLM responses
- **File Naming**: Generate suggested file names for saving responses
- **Response Validation**: Validate responses for quality

### 4.6 User Authentication

#### 4.6.1 Authentication Methods

- **Google OAuth**: Sign in with Google
- **Apple OAuth**: Sign in with Apple (iOS/macOS)
- **Email/Password**: Traditional email and password authentication

#### 4.6.2 Session Management

- **Token Refresh**: Automatic token refresh
- **Session Storage**: Secure storage of session information
- **Auth State**: Track and propagate authentication state

### 4.7 License Management

#### 4.7.1 License Types

- **Free**: Basic functionality with limitations
- **Trial**: Full functionality for a limited time
- **Pro**: Full functionality with premium features
- **Enterprise**: Advanced features for organizations

#### 4.7.2 License Verification

- **API Key**: Use API keys for license verification
- **Expiration Handling**: Handle license expiration
- **Tier Management**: Manage user tiers for feature access

### 4.8 Topics Feature (Planned)

#### 4.8.1 Data Model

- **Topic Structure**: UUID, name, description, storage type, timestamps
- **Storage Types**: Local and Google Drive

#### 4.8.2 Storage Architecture

- **Local Storage**: Map topics to folders in app's documents directory
- **Google Drive**: Map topics to folders in Google Drive

#### 4.8.3 Components

- **TopicsService**: Topic CRUD operations, folder management
- **GoogleDriveService**: Drive authentication, folder operations
- **ViewModels**: Topic list and detail management
- **Views**: Topic list and detail display

## 5. Non-Functional Requirements

### 5.1 Performance

#### 5.1.1 Response Time

- **UI Responsiveness**: UI should respond within 100ms
- **LLM Execution**: LLM execution should complete within reasonable time (based on model)
- **Suggestion Generation**: Suggestions should appear within 300ms

#### 5.1.2 Optimization

- **Lazy Loading**: Lazy loading for large datasets
- **Caching**: Caching strategies for frequent operations
- **Debouncing**: Debouncing for text input and search

### 5.2 Security

#### 5.2.1 Authentication

- **OAuth Security**: Secure OAuth implementation
- **Token Storage**: Secure storage of authentication tokens
- **API Key Protection**: Protection of API keys

#### 5.2.2 Data Protection

- **Encryption**: Encryption of sensitive data
- **Secure Communication**: HTTPS for all API communication
- **Input Validation**: Validation of all user inputs

### 5.3 Reliability

#### 5.3.1 Error Handling

- **Graceful Degradation**: Graceful handling of errors
- **Offline Support**: Support for offline operation
- **Retry Mechanisms**: Automatic retry for failed operations

#### 5.3.2 Data Integrity

- **Transaction Support**: Transaction support for database operations
- **Backup and Recovery**: Support for data backup and recovery
- **Conflict Resolution**: Resolution of synchronization conflicts

### 5.4 Usability

#### 5.4.1 User Interface

- **Intuitive Design**: Intuitive and easy-to-use interface
- **Accessibility**: Support for accessibility features
- **Platform Guidelines**: Adherence to platform-specific design guidelines

#### 5.4.2 User Experience

- **Onboarding**: Smooth onboarding experience
- **Feedback**: Clear feedback for user actions
- **Help and Documentation**: Comprehensive help and documentation

## 6. Technical Architecture

### 6.1 Client Architecture

#### 6.1.1 Core Components

- **ClientContextService (CCS)**: Central service for application context
- **ViewModels**: MVVM pattern with Riverpod for state management
- **Services**: Specialized services for specific functionalities

#### 6.1.2 State Management

- **Riverpod**: State management with Riverpod
- **State Flow**: Predictable state updates and flow

#### 6.1.3 Database Integration

- **SQLite**: Local database with abstraction layer
- **Supabase**: Cloud synchronization with Supabase

### 6.2 API Architecture

#### 6.2.1 Server Components

- **Router**: Request routing with Gin
- **Handlers**: Request handling for different endpoints
- **Middleware**: Authentication and other middleware

#### 6.2.2 LLM Integration

- **Executor**: Execution of prompts with LLM providers
- **Providers**: Integration with different LLM providers
- **Template Processing**: Processing of templates with variables

#### 6.2.3 Database Integration

- **Supabase Client**: Integration with Supabase
- **Repository Pattern**: Repository pattern for data access
- **Migration Support**: Support for schema migrations

## 7. Future Considerations

### 7.1 Enhanced Features

- **Real-time Collaboration**: Collaborative prompt editing
- **Advanced Entity Detection**: Enhanced entity recognition
- **Improved Suggestion Algorithms**: Better prompt suggestions
- **Cross-platform Support**: Web and desktop applications
- **Plugin Support**: Extensible plugin architecture

### 7.2 Integration Opportunities

- **WhatsApp Export**: Integration with WhatsApp
- **Additional Storage Providers**: Support for more storage providers
- **API Ecosystem**: Expanded API for third-party integration

### 7.3 Business Model

- **Subscription Tiers**: Different subscription tiers for different user needs
- **Enterprise Features**: Advanced features for enterprise users
- **Marketplace**: Marketplace for prompt sharing and monetization

## 8. Conclusion

Promz is designed to provide a comprehensive solution for AI prompt management and execution. The
MVP includes core functionality for prompt management, variable handling, entity detection, LLM
execution, and user authentication. Future enhancements will focus on expanding these capabilities
and adding new features to improve the user experience and provide more value to users.
