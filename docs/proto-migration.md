# Protocol Buffer Migration Plan

**Version:** 1.0
**Date:** 2025-04-26

## Document History

| Version | Date       | Description             |
| ------- | ---------- | ----------------------- |
| 1.0     | 2025-04-26 | Initial design document |

## 1. Overview

This document outlines the migration plan for adopting Protocol Buffers (protobuf) for
server-client communication in the Promz application. The current system uses a mix of REST APIs
and custom serialization formats, leading to inefficiencies and maintainability issues.

## 2. Goals

- Adopt protobuf for all server-client communication
- Implement gRPC for efficient, typed communication
- Replace REST APIs with gRPC services
- Standardize serialization format for all data transfer
- Ensure backward compatibility during transition

## 3. Implementation Plan

This section details the steps to migrate the client-side `AttachmentRegistryService` from using
`Map<String, dynamic>` to Protocol Buffers (`ProcessingResult` message defined in
`api/proto/v1/content_processing.proto`).

### Phase 1: Introduce Protobuf Types & New Service Methods

1. **Generate/Verify Protobuf Code:**

   - Ensure the Dart code for `content_processing.proto` and its dependencies is correctly
     generated using the `scripts/generate_protos.ps1` script. The generated code should reside
     in a shared package (likely `packages/promz_common`).
   - Verify that the `ProcessingResult` class and related metadata messages
     (`ZipMetadata`, `WhatsAppMetadata`, `FileMetadata`, `ProcessingStatus`) are available for
     use in the client codebase.

2. **Update `AttachmentRegistryService` Internal Storage:**

   - Modify the `_attachmentContents` map within `AttachmentRegistryService` from
     `Map<String, dynamic>` to `Map<String, ProcessingResult>`. The key will remain the unique
     attachment `id`.

3. **Create New Protobuf-Based Methods:**

   - Introduce new methods that operate on the `ProcessingResult` type:
     - `void registerAttachmentProto({required String id, required ProcessingResult result})`:
       Replaces `registerAttachment`. Takes a fully populated `ProcessingResult`.
     - `void registerServerAttachmentProto({required String id, required ProcessingResult initialResult, FileProcessingClient? client})`:
       Replaces `registerServerAttachment`. Takes an initial `ProcessingResult` (likely with
       `job_id` and `status = PENDING`) and optionally the client for status monitoring.
     - `Future<ProcessingResult?> getAttachmentProto(String id)`: Replaces `getAttachment`.
       Returns the `ProcessingResult` object.
     - `Future<String?> getAttachmentContentProto(String id)`: A helper to specifically get the
       content string (which might be direct content, `ID:job_id`, or `URL:content_url`) from
       the `ProcessingResult`.
     - Update other relevant methods like `getMostRecentAttachmentByType`,
       `getAllServerAttachments` to return or work with `ProcessingResult`.

4. **Deprecate Old Methods:**

   - Add the `@Deprecated('Use registerAttachmentProto instead')` annotation to
     `registerAttachment`.
   - Add the `@Deprecated('Use registerServerAttachmentProto instead')` annotation to
     `registerServerAttachment`.
   - Add the `@Deprecated('Use getAttachmentProto instead')` annotation to `getAttachment`.
   - Deprecate other methods that rely on the old map structure (e.g.,
     `getMostRecentAttachmentByType`, `getAllServerAttachments` if they return the raw map).

5. **Adapt Internal Logic:**
   - Rewrite `_extractDisplayName` to extract the display name from the relevant fields within
     the `ProcessingResult` object (e.g., `general_metadata['fileName']`,
     `whatsapp_metadata.group_name`, `file_metadata` fields). Define a clear precedence order.
   - Modify `_monitorProcessingStatus` to:
     - Accept `ProcessingResult` or `id`.
     - Fetch status updates using the `job_id` from the `ProcessingResult`.
     - Update the _existing_ `ProcessingResult` object stored in `_attachmentContents` with the
       new status, content (e.g., changing `content` from `ID:job_id` to a `URL:content_url` or
       the actual content upon completion), metadata, and error messages received from the
       server stream/polling.
     - Ensure state changes trigger UI updates via Riverpod (the service itself might need to
       become a Notifier or interact with one).
   - Update `isServerReference`, `isUrlReference`, `extractServerId`, `extractContentUrl` to
     potentially read from the `ProcessingResult.content` field or dedicated status fields if
     the representation changes.

### Phase 2: Gradual Migration of Call Sites

1. **Identify Call Sites:** Use IDE tools or search (`grep`) to find all locations in the
   `client/lib/` directory where the deprecated methods of `AttachmentRegistryService` are called.
2. **Prioritize Migration:** Group call sites by feature (e.g., file upload UI, chat view
   attachment handling, variable resolution). Migrate feature by feature.
3. **Update Call Sites:**
   - For each call site:
     - Replace calls to deprecated methods with their new `Proto` counterparts.
     - Ensure that the data previously passed/retrieved as `Map<String, dynamic>` is now
       correctly structured as a `ProcessingResult` object. This might involve changes in
       ViewModels or other services that prepare or consume attachment data.
     - Map existing metadata keys to appropriate fields in `ProcessingResult` (e.g.,
       `fileName` -> `general_metadata['fileName']`, `isServerProcessed` -> inferred from
       `status` or a dedicated field if added).
     - Adjust UI components that display attachment information (like display name, status,
       type) to read from the `ProcessingResult` object provided by the updated service
       methods.
4. **Testing:** After migrating each feature or logical group of call sites, perform thorough
   testing to ensure attachment registration, display, status updates, and content retrieval
   work correctly with the new protobuf structure.

### Phase 3: Refinement and Cleanup

1. **Final Verification:** Once all call sites have been migrated, perform end-to-end testing of
   all features involving attachments.
2. **Remove Deprecated Code:** Delete the `@Deprecated` methods (`registerAttachment`,
   `registerServerAttachment`, `getAttachment`, etc.) from `AttachmentRegistryService`.
3. **Remove Compatibility Layers:** Remove any temporary data conversion logic introduced during
   the migration phase.
4. **Code Review:** Conduct a final code review focusing on the `AttachmentRegistryService` and
   the migrated call sites to ensure consistency and correctness.
5. **Documentation Update:** Update any relevant developer documentation or comments to reflect the
   use of `ProcessingResult`.

### Considerations

- **Protobuf Location:** Generated Dart code should be in `packages/promz_common` for sharing
  between client/server if needed, or a client-specific proto generation path.
- **State Management (Riverpod):** `AttachmentRegistryService` might need to be refactored into a
  `Notifier` (e.g., `StateNotifier` or `ChangeNotifier`) provided by Riverpod, so that updates to
  `ProcessingResult` objects (especially from `_monitorProcessingStatus`) automatically trigger
  UI rebuilds in widgets watching the attachment state.
- **Data Mapping Definition:** Maintain a clear mapping document or comments outlining how fields
  from the old `Map` structure correspond to fields in `ProcessingResult`.
- **Error Handling:** Ensure robust error handling during protobuf serialization/deserialization
  and when interacting with the gRPC client.
- **Content Handling:** Decide how the actual attachment content vs. references (`ID:job_id`,
  `URL:content_url`) will be stored within the `ProcessingResult.content` field during different
  stages of processing.
- **Testing:** Create test coverage that is sufficiently high level so that they can be created
  with old `Map` structure and are updated to use the new `ProcessingResult` structure.
- **Performance:** Consider the performance implications of the new protobuf structure, especially
  in terms of memory usage and data transfer.
- **Documentation:** Document the new protobuf structure and how it is used in the client and
  server.

### Migration Timeline

1. **Phase 1 (1-2 days):**
   - Introduce `ProcessingResult` and related protobuf types.
   - Update `AttachmentRegistryService` internal storage.
   - Create new protobuf-based methods.
   - Deprecate old methods.
   - Adapt internal logic.
2. **Phase 2 (2-3 days):**
   - Identify and update all call sites in the client codebase.
   - Test migrated features.
3. **Phase 3 (1-2 days):**
   - Final verification and testing.
   - Remove deprecated code.
   - Remove compatibility layers.
   - Code review and documentation update.
