# Promz Development Tasks

## Completed Tasks

### Client-Side (Flutter)

- [x] Refactored LLM execution service to use shared logic from promz_common
  - [x] Restored API key header logic (`Authorization` and `X-API-Key`) for server compatibility
  - [x] Restored and centralized error handling for 'no eligible models' (tier/model restriction) with proper exception propagation
  - [x] Deduplicated and unified exception classes for LLM errors (now in promz_common)
  - [x] Updated imports and removed legacy exception code from client
- [x] Fix sharing functionality (Android & iOS)
  - [x] Update iOS Info.plist to support sharing various file types
  - [x] Add document types and UTI declarations for sharing
- [x] Fix app icons on iOS
  - [x] Update iOS app icons using flutter_launcher_icons
- [x] Improve UI look and feel on iOS
  - [x] Add platform-specific utilities for consistent UI
  - [x] Implement Cupertino widgets for iOS
- [x] Fix theme font sizes
  - [x] Ensure theme font sizes are properly applied to all text elements
  - [x] Fix hardcoded font sizes in UI components
- [x] Add Share button to prompt list page
  - [x] Update PromzPromptCard to include share button
  - [x] Implement share functionality using ShareService
- [x] Remove the prompts button from the bottom navigation bar
- [x] Clean and finalize the popular prompts feature
  - [x] Pull popular prompts from the server
  - [x] Upload prompt usage to the server
- [x] Variable Management
  - [x] Create variable extraction utility functions
  - [x] Implement variable prefix handling in EntityUtils with `{#N}` format
  - [x] Add support for variable prefix rendering in templates
  - [x] Update client-side EntityUtils to support `{$N}` format (keeping `{#N}` for backward compatibility)
- [x] Financial Data Integration
  - [x] Implement FinancialDataService with ticker info lookup
  - [x] Create EnrichFinancialVariables function to add related financial data
  - [x] Add proper prefix handling for financial variables
  - [x] Fix template rendering with prefixed financial variables
- [x] Template Processing
  - [x] Implement template processor with variable substitution
  - [x] Add support for Go template syntax
  - [x] Fix issues with special characters in variable names
  - [x] Support multiple prefixed variables in the same template
- [x] Entity Detection
  - [x] Implement entity detection for financial entities
  - [x] Support location entities
  - [x] Add person entity detection
  - [x] Implement time-based entity detection
- [x] Variable Display and Management
  - [x] Move VariablesSection from AutocompleteField to SourceInputSection
  - [x] Fix variable display at startup for shared URLs
  - [x] Ensure VariablesSection rebuilds when variables change
- [x] VariablesDialog Enhancements
  - [x] Add Execute button that saves changes, closes dialog, and triggers prompt execution
  - [x] Implement proper dismissal behavior (restore values when clicking outside/X, keep changes when clicking Save/Execute)
  - [x] Integrate with AutocompleteField and VariablesSection
- [x] Documentation
  - [x] Document variable prefix format and usage
- [x] Unify Markdown editor across admin and client apps using shared MarkdownEditor from promz_common
  - [x] Remove old MarkdownEditorWidget and update all usages to new API
  - [x] Update prompt_detail_screen, prompt_edit_screen, expandable_prompt_item, and smoke_test for new editor
  - [x] Minimal testing for stability (smoke test)
  - [x] Fix RenderFlex overflow in MarkdownEditor by wrapping content in Expanded
- [x] Modernize and robustify admin dev script (run_admin.ps1)
  - [x] Add build_runner, json_serializable, json_annotation to admin pubspec.yaml
  - [x] Auto-detect if code generation is needed and only run build_runner if required
  - [x] Remove redundant codegen menu option and fix PowerShell $gFiles.Count bug
  - [x] Clean up and simplify menu, improve user feedback and error handling
- [x] YouTube Link Variable Feature
  - [x] Implement URL utility methods for YouTube links
  - [x] Create YouTube service for fetching video metadata
  - [x] Implement UI components for YouTube link input and preview
  - [x] Integrate with existing entity variable system
  - [x] Add support for YouTube variables in EntityUtils
  - [x] Update ContentProcessingService to properly detect YouTube links

### Server-Side (Go)

- [x] Popular Prompts Feature
  - [x] Database table and migrations
  - [x] API Endpoints implementation (POST /usage/prompt, GET /popular-prompts)
  - [x] PopularityService implementation (usage recording, score calculation, cache management)
- [x] Template Processing
  - [x] Implement variable substitution in the template processor
  - [x] Fix `#` character handling in Go template variables by replacing with `num` on server-side
  - [x] Enhance server-side template processor to support both `{#N}` and `{$N}` prefix formats
  - [x] Update server-side variable_utils.go to handle multiple prefix formats
- [x] Portfolio Functionality
  - [x] Implement basic database operations (create, list, check existence, delete)

## MVP To-Do Items

### Client-Side MVP Tasks

- [x] Finalize the current set of prompts
  - [x] Remove low-value and open ended prompts (coding etc.)
  - [x] Ensure they are fully ready and clean them up as needed
  - [x] Set temperature and other parameters
  - [x] Add variables for each of the high value (AKA remaining) prompts
- [ ] Support .promz format
  - [ ] Allow for export of prompts
  - [ ] Allow for sharing of prompts
- [ ] Implement the most basic Portfolio functionality
  - [x] Create "Coming Soon" page with feature preview
  - [ ] Implement TopicsService in Flutter client
  - [ ] Create basic UI for topic management
  - [ ] Add topic selection when saving LLM output
- [x] WhatsApp export integration
- [ ] Add a new feature called "Use Your Own Prompt"
  - [ ] Includes user input prompt to be taken in correctly
  - [ ] Has a specific set of instructions
  - [ ] Ignore prompt selection and not be passed down into the prompt in those cases
- [x] Finalize license workflow
  - [x] Fix getting a trial license
  - [x] A trial license on top of Free should work as expected
  - [x] Ignore any licenses that have already expired
- [x] Finalize the user profile page
  - [x] Add "Begin Tour" button to Account view
  - [x] Combine it with Settings? Or call it About Promz?
  - [x] Add more value proposition information through onboarding screens
  - [ ] Talk about what's coming in the future?
- [ ] Complete the fit and finish of the application
  - [x] Support Dark theme fully
  - [x] Make all the fonts and colors to be consistent
- [ ] Update the colors, themes and everything to match a consistent look and feel
- [ ] Add Apple Sign In
- [ ] Look into Play Store and App Store registrations
- [ ] Consider making a YouTube video explaining the purpose and value proposition for Promz

### Server-Side MVP Tasks

- [ ] Implement .promz format support
  - [ ] Define the format specification
  - [ ] Create API endpoints for importing/exporting prompts

## Ongoing Tasks

### Variable Management

- [ ] Update client-side documentation to recommend `{$N}` instead of `{#N}` for new variable prefixes
- [ ] Add unit tests for variable prefix handling

### Financial Data Integration

- [x] Implement FinancialDataService with ticker info lookup
- [x] Create EnrichFinancialVariables function to add related financial data
- [x] Add proper prefix handling for financial variables
- [x] Fix template rendering with prefixed financial variables

### Template Processing

- [x] Implement template processor with variable substitution
- [x] Add support for Go template syntax
- [x] Fix issues with special characters in variable names
- [x] Support multiple prefixed variables in the same template

### Entity Detection

- [x] Implement entity detection for financial entities
- [x] Support location entities
- [x] Add person entity detection
- [x] Implement time-based entity detection

### Variable Display and Management

- [x] Move VariablesSection from AutocompleteField to SourceInputSection
- [x] Fix variable display at startup for shared URLs
- [x] Ensure VariablesSection rebuilds when variables change

### VariablesDialog Enhancements

- [x] Add Execute button that saves changes, closes dialog, and triggers prompt execution
- [x] Implement proper dismissal behavior (restore values when clicking outside/X, keep changes when clicking Save/Execute)
- [x] Integrate with AutocompleteField and VariablesSection

### Ongoing Tasks - Client-Side

- [ ] Add more financial data sources beyond SP500
- [ ] Add caching for frequently used templates
- [ ] Improve entity detection accuracy with ML model

### Ongoing Tasks - UI Enhancements

- [ ] Improve variable editing dialog with better UX
- [ ] Add visual indicators for different entity types
- [ ] Implement drag-and-drop for variable reordering
- [ ] Add syntax highlighting for templates

### Ongoing Tasks - Documentation

- [x] Document variable prefix format and usage
- [ ] Create user guide for template variables
- [ ] Add API documentation for variable handling
- [ ] Document best practices for entity detection

### Ongoing Tasks - Infrastructure

- [ ] Set up CI/CD pipeline for automated testing
- [ ] Implement database migrations for schema changes
- [ ] Create Docker containers for development environment
- [ ] Configure monitoring and logging for production

### Ongoing Tasks - Performance Optimization

- [ ] Implement caching for entity detection
- [ ] Optimize template rendering for large templates
- [ ] Add batch processing for multiple templates
- [ ] Profile and optimize memory usage

### Popular Prompts Feature

- [x] Local Storage implementation
- [x] Service Layer implementation
- [x] Usage Recording implementation
- [x] Background task scheduling implementation
- [x] Periodic sync implementation
- [x] Country code support
- [x] Database table and migrations
- [x] API Endpoints implementation (POST /usage/prompt, GET /popular-prompts)
- [x] PopularityService implementation (usage recording, score calculation, cache management)

### Future Improvements

- [ ] Improve variable type detection for better display formatting
- [ ] Implement caching for variable values to improve performance with large numbers of variables
- [ ] Add unit and widget tests for variable synchronization logic
- [ ] Consider adding a visual indicator when variables are being processed or loaded
- [ ] Enhance the VariablesDialog UI with better categorization of variables
- [ ] Allow for rating of prompts (Phase 2 of Popular Prompts)

### Ongoing Tasks - API Key Setup Experience

- [x] Replace abrupt API key dialog with a smoother onboarding flow for first-time users
- [x] Implement an onboarding experience with value proposition screens
- [x] Add a "Begin Tour" button in the Account view for users who skip the initial onboarding
- [ ] Add contextual explanation about why the API key is needed and how to obtain one
- [ ] Implement a non-intrusive persistent banner instead of a modal dialog for API key reminders
- [ ] Allow users to explore the app UI in a limited mode even without an API key
- [ ] Consider adding a "demo mode" with sample responses for users without an API key
- [ ] Improve error handling for invalid API keys with clear feedback
- [ ] Add a "Test API Key" button in the setup screen to validate the key before saving

### Ongoing Tasks - Code Cleanup

- [ ] Implement share intent handling logic in HomePage.\_registerShareIntentHandler (home_page.dart:43)
- [ ] Add support for more variable types beyond the current implementation
- [ ] Navigate to profile page when profile button is clicked (home_page.dart:83)
- [ ] Change background color or do other layout adjustments based on constraints (home_page.dart:151)
- [ ] Process the selected content with the prompt (prompt_list_view.dart:82)

### Ongoing Tasks - Topics Integration

- [x] Implement database schema and functions
- [x] Test database operations through Supabase
- [x] Create Portfolio "Coming Soon" page
- [ ] ~Implement TopicsService layer in Flutter~ (Postponed)
- [ ] ~Create TopicsViewModel~ (Postponed)
- [ ] ~Build basic topic management UI~ (Postponed)
- [ ] Integrate with LLM output saving workflow
- [ ] Implement local storage folder management
- [ ] Add Google Drive integration

### Ongoing Tasks - Tools

- [ ] Finish the LLM pricing tool implementation. The core project setup is complete, but the tool needs to be fully functional.

### Ongoing Tasks - Admin & Shared Architecture

- [ ] Role-Based Access Control (RBAC): Implement granular permissions for different admin roles
- [ ] Advanced search/filtering for prompts and categories in admin
- [ ] Bulk operations: Add batch actions for prompt/category management in admin
- [ ] Analytics dashboard for admin activity and content usage (directional)
- [x] Markdown editor unification: Audit and either integrate EnhancedMarkdownEditor or remove unused markdown widgets
- [ ] Automated sync and validation tools for ensuring client/admin data consistency
- [x] Improve Prompt Edit UI with better organization and visual hierarchy
- [ ] Add category management to Prompt Edit Screen
- [ ] Implement entity template insertion toolbar in the markdown editor
- [ ] Add validation for properly formatted entity templates in the prompt instructions
- [ ] Support variable editing/preview when editing prompt instructions
- [ ] Visual indicators for entity templates in markdown preview mode

### Ongoing Tasks - API Server

- [ ] **API Server**: Replace polling in `StreamUploadUpdates` gRPC handler with GCP Pub/Sub for real-time updates. Requires processor changes to publish events.
