# Topics Feature Design

**Version:** 1.0
**Date:** 2024-02-14

## 1. Overview

The Topics feature allows users to organize their LLM outputs into logical groups, with support for both local storage and Google Drive integration. Each topic maps to a folder that contains PDF outputs from LLM interactions.

## 2. Goals

- Implement basic topic management (create, view, navigate)
- Support local storage and Google Drive integration
- Enable easy navigation to stored content
- Maintain a simple, flat organization structure
- Provide seamless PDF file handling

## 3. MVP Implementation

### 3.1. Data Model

```dart
class Topic {
  final String id;          // UUID
  final String name;        // Display name and folder name
  final String description; // Optional description
  final StorageType type;   // LOCAL or GOOGLE_DRIVE
  final DateTime createdAt;
  final DateTime updatedAt;

  // For Google Drive topics
  final String? driveRootPath;  // Default: 'promz/'
}

enum StorageType {
  local,
  googleDrive
}
```

### 3.2. Storage Architecture

#### Local Storage

- Topics stored in SQLite database
- Each topic maps to a folder in app's documents directory
- PDFs stored directly in topic folders
- Folder structure: `{app_documents}/topics/{topic_name}/`

#### Google Drive

- Topics stored in SQLite database
- Each topic maps to a folder in Google Drive
- Default root path: `promz/`
- Folder structure: `promz/{topic_name}/`
- No local caching of PDFs

### 3.3. Components

#### Services

1. **TopicsService:**

   - Topic CRUD operations
   - Folder management (local/Drive)
   - PDF file operations
   - Location-aware file opening

2. **GoogleDriveService:**
   - Drive authentication
   - Folder operations
   - PDF upload/navigation
   - Root folder configuration

#### ViewModels

1. **TopicsViewModel:**

   - Topic list management
   - Folder creation/validation
   - Navigation coordination
   - Storage type handling

2. **TopicDetailViewModel:**
   - Topic content display
   - PDF file listing
   - File navigation handling
   - Upload status tracking

#### Views

1. **TopicsView:**

   - Topic list display
   - Create topic button
   - Storage type indicator
   - Navigation to details

2. **TopicDetailView:**
   - Topic information
   - PDF file list
   - Navigate to files
   - Upload status indicators

### 3.4. User Flows

1. **Topic Creation:**

   ```text
   Select "New Topic" ->
   Enter name & description ->
   Choose storage type ->
   (If Drive) Confirm/change root folder ->
   Create topic & folder structure
   ```

2. **PDF Storage:**

   ```text
   Generate LLM output ->
   Select "Save as PDF" ->
   Choose topic ->
   Save to appropriate location ->
   Show success/failure feedback
   ```

3. **Content Navigation:**

   ```text
   Select topic ->
   View PDF list ->
   Select PDF ->
   Open in system viewer (local) or Drive (Google Drive)
   ```

### 3.5. File Naming Convention

```text
Format: {short_title}_{topic_id_short}_{hash}.pdf
Example: analyze_report_t5f3_a7b2.pdf

Components:
- short_title: Max 20 chars, keeping first 3-4 significant words
- topic_id_short: First 4 chars of topic ID (for grouping)
- hash: First 4 chars of content hash (for uniqueness)
```

### 3.6. Error Handling

- Drive folder not found: Prompt for new location
- File upload failure: Show retry dialog
- Permission issues: Clear instructions for resolution
- Network errors: Appropriate feedback with retry options

## 4. Future Considerations (Phase 2)

### 4.1. Nested Topics

- Parent-child topic relationships
- Hierarchical navigation
- Maintain folder structure sync
- Migration path from flat structure

### 4.2. Enhanced Features

- Topic sharing
- Batch operations
- Search across topics
- Topic templates
- PDF preview
- Multiple storage providers

### 4.3. Google Drive Integration

- Folder deletion confirmation
- Two-way sync detection
- Conflict resolution
- Offline capabilities

## 5. Technical Considerations

### 5.1. Database Schema

**Server (Supabase/PostgreSQL):**

```sql
CREATE TABLE topics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v7(),
    user_id UUID NOT NULL REFERENCES auth.users(id),
    name TEXT NOT NULL,
    description TEXT,
    storage_type TEXT NOT NULL CHECK (storage_type IN ('local', 'google_drive')),
    drive_root_path TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    version INTEGER NOT NULL DEFAULT 1
);

-- Index for efficient topic lookups and sync
CREATE INDEX idx_topics_user_id ON topics(user_id);
CREATE INDEX idx_topics_deleted_at ON topics(deleted_at);
CREATE INDEX idx_topics_updated_at ON topics(updated_at);
```

**Client (SQLite):**

```sql
CREATE TABLE topics (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    storage_type TEXT NOT NULL,
    drive_root_path TEXT,
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL,
    deleted_at INTEGER,
    version INTEGER NOT NULL DEFAULT 1
);

-- Indexes for efficient lookups and sync
CREATE INDEX idx_topics_user_id ON topics(user_id);
CREATE INDEX idx_topics_deleted_at ON topics(deleted_at);
CREATE INDEX idx_topics_updated_at ON topics(updated_at);
```

Key differences between server and client schemas:

- UUID vs TEXT for ID fields
- TIMESTAMPTZ vs INTEGER for timestamps
- Server has auth.users foreign key reference
- Both maintain same indexes for consistency

### 5.2. Storage Management

1. **Local Storage:**

   - Use platform-specific app documents directory
   - Implement cleanup for orphaned files
   - Handle storage permission changes
   - Monitor available space

2. **Google Drive:**
   - Implement exponential backoff for retries
   - Cache folder IDs temporarily for performance
   - Handle token refresh and auth errors
   - Monitor quota usage

### 5.3. Performance Considerations

- Lazy load PDF list in topics
- Cache folder existence checks
- Batch upload operations
- Compress PDFs when possible
- Clean up temporary files

### 5.4. Security Considerations

- Sanitize topic names for filesystem safety
- Handle Google Drive auth token securely
- Validate file operations
- Protect sensitive content
