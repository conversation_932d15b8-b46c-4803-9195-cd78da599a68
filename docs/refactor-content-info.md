# Refactoring Plan: Replace InputContentInfo with Direct InputSource Handling

**Date:** 2025-05-03

**Goal:** Simplify the content processing flow by removing the intermediate `InputContentInfo` data
structure and centralizing `InputSource` creation, duplicate detection, and notification within the
`ContentProcessingService`.

**Motivation:** The current approach of returning `InputContentInfo` from `processInput` adds
complexity for callers (`HomeViewModel`, `SharedContentHandler`) who then need to interpret this
intermediate state. Centralizing the logic will make the `ContentProcessingService` API cleaner and
easier to use, directly returning the relevant `InputSource` or indicating failure.

**Revised Strategy (Incorporating Feedback):**

1. **Modify `processInput` Signature:**
    * Change the return type from `Future<InputContentInfo>` to `Future<InputSource?>`.
    * A non-null `InputSource` return value indicates success (either a new source was created or an
      existing duplicate was found).
    * A `null` return value indicates a processing *failure* (e.g., network error, invalid input,
      license issue).

2. **Internalize Processing Logic:**
    * Keep the core content type detection and routing logic within `processInput`.
    * Refactor internal processing methods (`_processFileInternal`, `_processTextInternal`, etc.) to
      focus *only* on extracting content, metadata (title, URL, file path, mime type), and
      calculating the `contentHash`.
    * These internal methods should return the raw extracted information (e.g., via a `Map<String,
      dynamic>` or a private helper class). They will **not** create `InputSource` instances,
      perform duplicate checks, or call `notifySourceProcessed`.

3. **Centralize Duplicate Check & Source Handling in `processInput`:**
    * After an internal processing method returns the extracted info (hash, metadata), `processInput`
     will perform the duplicate check against existing sources.
    * **If Duplicate:**
        * Find the *existing* `InputSource` based on `contentHash` (or potentially `filePath`).
        * Log the duplication.
        * Return the **existing** `InputSource` object.
        * **Do not** emit this via the `_sourceProcessedController` stream.
    * **If NOT a Duplicate:**
        * Construct the **new** `InputSource` object using the gathered information.
        * Persist/register the new `InputSource` (e.g., save to repository/state).
        * Emit the **new** `InputSource` via the `_sourceProcessedController` stream using
         `notifySourceProcessed(newSource)`.
        * Return the **new** `InputSource` object.
    * **If Processing Fails:**
        * Log the error.
        * Return `null`.

4. **Ensure `InputSource` has Necessary Fields:**
    * Review `InputSource` model (`client/lib/features/input_selection/models/input_source.dart`).
    * Ensure it contains all necessary *persistent* fields (e.g., `title`, `sourceUrl`, `filePath`,
      `mimeType`, `contentHash`, `attachmentId`).
    * If `InputSource` uses a nested `processingResult`, consider promoting essential fields
      directly onto `InputSource` and removing the nested object.

5. **Update Callers:**
    * Modify `HomeViewModel`, `SharedContentHandler`, and any other callers of `processInput` to
     expect `Future<InputSource?>`.
    * Handle the returned `InputSource?`:
        * Non-null: Use for immediate feedback (e.g., "Duplicate found: [Source Name]").
        * Null: Indicate processing failure (e.g., show error message).
    * Continue relying primarily on the `sourceProcessed` stream for updating UI lists with
      *newly added* sources.

6. **Refactor Internal Processing Methods:**
    * Go through `_processFile`, `_processText`, `_processUrlInput`, `_processYouTubeVideo`,
     `_processNewsArticle`.
    * Remove logic related to creating `InputSource` instances.
    * Remove calls to `notifySourceProcessed`.
    * Ensure they return a consistent structure (like `Map<String, dynamic>`) containing the
      necessary raw data (`contentHash`, `attachmentId`, `sourceType`, `fileName`, `mimeType`,
      `title`, `sourceUrl`, etc.).

7. **Remove `InputContentInfo`:**
    * Once all usages are refactored, delete the `input_content_info.dart` file and the
     `InputContentInfo` class.

**Benefits:**

* Simplified public API for `ContentProcessingService`.
* Reduced complexity for callers.
* Centralized logic for `InputSource` creation, duplicate handling, and
  notification.
* Clearer distinction between successful processing (returning an `InputSource`) and failure
  (returning `null`).
