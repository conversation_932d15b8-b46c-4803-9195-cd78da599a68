# Sharing Feature Design

**Version:** 1.0  
**Date:** 2025-04-07

## 1. Overview

The Sharing feature enables users to share prompts and workflows while supporting affiliate promotion and sponsored content. This design document outlines the technical implementation of content sharing, affiliate management, and sponsored content display across the platform.

## 2. Goals

- Implement robust prompt and workflow sharing mechanisms
- Support affiliate promotion through sponsored content
- Provide analytics tracking for shared and sponsored content
- Enable tiered access to sponsored content filtering based on license type
- Create affiliate management dashboard for tracking performance
- Ensure clear visual differentiation for sponsored content

## 3. Core Concepts

### 3.1. Content Types

1. **Standard Prompts**

   - User-created prompts shared publicly or privately
   - Basic sharing with no monetization
   - Available to all users

2. **Sponsored Prompts**

   - Affiliate-created prompts with promotional intent
   - Visually marked as sponsored content
   - May link to external resources or workflows
   - Subject to placement rules and analytics tracking

3. **Workflows**
   - Multi-step prompt sequences with defined outcomes
   - Can be standard or sponsored
   - May include external integration points

### 3.2. Sharing Modes

1. **Public Sharing**

   - Content visible to all platform users
   - Appears in search results and category listings
   - Subject to community ratings and feedback

2. **Private Sharing**

   - Content shared with specific users or teams
   - Requires Pro license or higher
   - Not visible in public listings
   - Access controlled via sharing permissions

3. **Affiliate Sharing**
   - Sponsored content from verified affiliates
   - Prominently displayed in dedicated sections
   - May appear in regular listings with sponsored tags
   - Subject to placement algorithms and user preferences

### 3.3. Affiliate System

1. **Affiliate Types**

   - Individual content creators
   - Business partners
   - Domain experts (e.g., recruiters, marketers)
   - Enterprise solution providers

2. **Affiliate Tiers**

   - Basic: Limited sponsored content placement
   - Premium: Enhanced visibility and analytics
   - Enterprise: Custom integration and white-labeling options

3. **Content Categories**
   - Job search and recruitment
   - Marketing and copywriting
   - Legal and compliance
   - Education and training
   - Technical and development
   - Custom categories for specific domains

## 4. Technical Implementation

### 4.1. Database Schema

**Server (PostgreSQL):**

```sql
-- Shared content table
CREATE TABLE shared_content (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v7(),
    user_id UUID NOT NULL REFERENCES auth.users(id),
    content_type TEXT NOT NULL CHECK (content_type IN ('prompt', 'workflow')),
    title TEXT NOT NULL,
    description TEXT,
    content JSONB NOT NULL,
    is_public BOOLEAN NOT NULL DEFAULT false,
    is_sponsored BOOLEAN NOT NULL DEFAULT false,
    category_id UUID REFERENCES content_categories(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    version INTEGER NOT NULL DEFAULT 1
);

-- Content categories
CREATE TABLE content_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v7(),
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    display_order INTEGER NOT NULL DEFAULT 0,
    parent_id UUID REFERENCES content_categories(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Affiliate profiles
CREATE TABLE affiliate_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v7(),
    user_id UUID NOT NULL REFERENCES auth.users(id) UNIQUE,
    company_name TEXT,
    website_url TEXT,
    logo_url TEXT,
    tier TEXT NOT NULL CHECK (tier IN ('basic', 'premium', 'enterprise')),
    approved BOOLEAN NOT NULL DEFAULT false,
    approval_date TIMESTAMPTZ,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Sponsored content
CREATE TABLE sponsored_content (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v7(),
    content_id UUID NOT NULL REFERENCES shared_content(id),
    affiliate_id UUID NOT NULL REFERENCES affiliate_profiles(id),
    external_url TEXT,
    campaign_id TEXT,
    start_date TIMESTAMPTZ NOT NULL,
    end_date TIMESTAMPTZ,
    daily_impression_limit INTEGER,
    total_impression_limit INTEGER,
    priority INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Content sharing permissions
CREATE TABLE content_permissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v7(),
    content_id UUID NOT NULL REFERENCES shared_content(id),
    shared_with_user_id UUID REFERENCES auth.users(id),
    shared_with_team_id UUID REFERENCES teams(id),
    permission_type TEXT NOT NULL CHECK (permission_type IN ('view', 'edit', 'admin')),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    CONSTRAINT user_or_team_constraint CHECK (
        (shared_with_user_id IS NULL AND shared_with_team_id IS NOT NULL) OR
        (shared_with_user_id IS NOT NULL AND shared_with_team_id IS NULL)
    )
);

-- Content analytics
CREATE TABLE content_analytics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v7(),
    content_id UUID NOT NULL REFERENCES shared_content(id),
    user_id UUID REFERENCES auth.users(id),
    event_type TEXT NOT NULL CHECK (event_type IN ('view', 'click', 'execute', 'share', 'conversion')),
    event_date TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    source TEXT,
    metadata JSONB
);

-- Sponsored content settings
CREATE TABLE sponsored_content_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v7(),
    setting_key TEXT NOT NULL UNIQUE,
    setting_value JSONB NOT NULL,
    description TEXT,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_by UUID REFERENCES auth.users(id)
);

-- Create indexes for efficient queries
CREATE INDEX idx_shared_content_user_id ON shared_content(user_id);
CREATE INDEX idx_shared_content_category_id ON shared_content(category_id);
CREATE INDEX idx_shared_content_is_public ON shared_content(is_public);
CREATE INDEX idx_shared_content_is_sponsored ON shared_content(is_sponsored);
CREATE INDEX idx_sponsored_content_affiliate_id ON sponsored_content(affiliate_id);
CREATE INDEX idx_content_analytics_content_id ON content_analytics(content_id);
CREATE INDEX idx_content_analytics_event_type ON content_analytics(event_type);
CREATE INDEX idx_content_permissions_content_id ON content_permissions(content_id);
```

### 4.2. Server-Side Implementation

#### API Endpoints

1. **/content/share**

   - Creates or updates shared content
   - Handles permissions and visibility settings
   - Validates content structure and metadata
   - Example request:

     ```json
     {
       "content_type": "prompt",
       "title": "Advanced Resume Parser",
       "description": "Parse resumes to extract key information",
       "content": {
         /* prompt data */
       },
       "is_public": true,
       "category_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
       "permissions": [
         {
           "shared_with_user_id": "u1v2w3x4-y5z6-7890-abcd-ef1234567890",
           "permission_type": "view"
         }
       ]
     }
     ```

2. **/content/discover**

   - Returns shared content based on filters
   - Includes sponsored content based on user's license
   - Supports pagination and sorting
   - Tracks impression analytics
   - Example response:

     ```json
     {
       "items": [
         {
           "id": "c1d2e3f4-g5h6-7890-abcd-ef1234567890",
           "title": "Advanced Resume Parser",
           "description": "Parse resumes to extract key information",
           "user": {
             "id": "u1v2w3x4-y5z6-7890-abcd-ef1234567890",
             "display_name": "Jane Smith"
           },
           "is_sponsored": false,
           "category": {
             "id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
             "name": "Recruitment"
           },
           "rating": 4.8,
           "usage_count": 1250
         },
         {
           "id": "s1t2u3v4-w5x6-7890-abcd-ef1234567890",
           "title": "Ultimate Job Description Generator",
           "description": "Create compelling job descriptions instantly",
           "user": {
             "id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
             "display_name": "RecruitPro"
           },
           "is_sponsored": true,
           "affiliate": {
             "company_name": "RecruitPro Solutions",
             "logo_url": "https://example.com/logo.png"
           },
           "external_url": "https://recruitpro.example.com/tool",
           "category": {
             "id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
             "name": "Recruitment"
           },
           "rating": 4.5,
           "usage_count": 3200
         }
       ],
       "total_count": 245,
       "page": 1,
       "page_size": 20
     }
     ```

3. **/affiliate/dashboard**

   - Provides affiliate performance metrics
   - Returns content analytics and conversion data
   - Supports filtering by date range and campaigns
   - Example response:

     ```json
     {
       "summary": {
         "total_impressions": 12500,
         "total_clicks": 1850,
         "total_executions": 950,
         "total_conversions": 125,
         "click_through_rate": 14.8,
         "conversion_rate": 6.76
       },
       "content_performance": [
         {
           "content_id": "s1t2u3v4-w5x6-7890-abcd-ef1234567890",
           "title": "Ultimate Job Description Generator",
           "impressions": 5200,
           "clicks": 780,
           "executions": 420,
           "conversions": 65,
           "click_through_rate": 15.0,
           "conversion_rate": 8.33
         }
       ],
       "daily_metrics": [
         {
           "date": "2025-04-06",
           "impressions": 450,
           "clicks": 68,
           "executions": 35,
           "conversions": 5
         }
       ]
     }
     ```

4. **/admin/sponsored-settings**

   - Manages sponsored content display settings
   - Controls visibility rules by license tier
   - Sets placement limits and priorities
   - Example request:

     ```json
     {
       "setting_key": "sponsored_content_rules",
       "setting_value": {
         "free_tier": {
           "can_filter": false,
           "max_sponsored_per_page": 3,
           "max_sponsored_per_category": 2,
           "show_premium_sponsors": true
         },
         "pro_tier": {
           "can_filter": true,
           "max_sponsored_per_page": 2,
           "max_sponsored_per_category": 1,
           "show_premium_sponsors": true
         },
         "enterprise_tier": {
           "can_filter": true,
           "default_filter_enabled": true,
           "max_sponsored_per_page": 0,
           "max_sponsored_per_category": 0,
           "show_premium_sponsors": false
         }
       }
     }
     ```

#### Database Functions

1. **get_recommended_content**

   - Returns personalized content recommendations
   - Includes appropriate sponsored content based on user's license
   - Considers user behavior and preferences
   - Example:

     ```sql
     CREATE OR REPLACE FUNCTION get_recommended_content(
       p_user_id UUID,
       p_license_type TEXT,
       p_category_id UUID DEFAULT NULL,
       p_limit INTEGER DEFAULT 10
     )
     RETURNS TABLE (
       content_id UUID,
       title TEXT,
       description TEXT,
       content_type TEXT,
       is_sponsored BOOLEAN,
       category_id UUID,
       category_name TEXT,
       user_id UUID,
       user_display_name TEXT,
       affiliate_id UUID,
       affiliate_name TEXT,
       external_url TEXT,
       relevance_score FLOAT
     ) AS $$
     BEGIN
       -- Implementation details
     END;
     $$ LANGUAGE plpgsql;
     ```

2. **track_content_analytics**

   - Records analytics events for content interactions
   - Supports different event types (view, click, execute, etc.)
   - Handles rate limiting and deduplication
   - Example:

     ```sql
     CREATE OR REPLACE FUNCTION track_content_analytics(
       p_content_id UUID,
       p_user_id UUID,
       p_event_type TEXT,
       p_source TEXT DEFAULT NULL,
       p_metadata JSONB DEFAULT '{}'
     )
     RETURNS BOOLEAN AS $$
     BEGIN
       -- Implementation details
     END;
     $$ LANGUAGE plpgsql;
     ```

### 4.3. Client-Side Implementation

#### Models

1. **SharedContent Model**

   ```dart
   class SharedContent {
     final String id;
     final String title;
     final String description;
     final String contentType; // 'prompt' or 'workflow'
     final Map<String, dynamic> content;
     final bool isPublic;
     final bool isSponsored;
     final String categoryId;
     final String categoryName;
     final UserProfile user;
     final AffiliateProfile? affiliate;
     final String? externalUrl;
     final double rating;
     final int usageCount;
     final DateTime createdAt;
     final DateTime updatedAt;

     // Constructor and methods
   }
   ```

2. **AffiliateProfile Model**

   ```dart
   class AffiliateProfile {
     final String id;
     final String userId;
     final String? companyName;
     final String? websiteUrl;
     final String? logoUrl;
     final String tier; // 'basic', 'premium', 'enterprise'
     final bool approved;
     final DateTime? approvalDate;
     final DateTime createdAt;

     // Constructor and methods
   }
   ```

3. **ContentAnalytics Model**

   ```dart
   class ContentAnalytics {
     final String contentId;
     final String eventType; // 'view', 'click', 'execute', 'share', 'conversion'
     final DateTime eventDate;
     final String? source;
     final Map<String, dynamic>? metadata;

     // Constructor and methods
   }
   ```

#### Services

1. **SharingService**

   - Handles content sharing operations
   - Manages permissions and visibility
   - Tracks analytics events
   - Example implementation:

     ```dart
     class SharingService {
       final ApiClient _apiClient;
       final AnalyticsService _analyticsService;

       Future<SharedContent> shareContent({
         required String contentType,
         required String title,
         required String description,
         required Map<String, dynamic> content,
         required bool isPublic,
         String? categoryId,
         List<ContentPermission>? permissions
       }) async {
         // Implementation details
       }

       Future<List<SharedContent>> discoverContent({
         String? categoryId,
         String? query,
         bool includeSponsored = true,
         int page = 1,
         int pageSize = 20
       }) async {
         // Implementation details
       }

       Future<bool> trackContentEvent({
         required String contentId,
         required String eventType,
         String? source,
         Map<String, dynamic>? metadata
       }) async {
         // Implementation details
       }
     }
     ```

2. **AffiliateService**

   - Manages affiliate profile and content
   - Provides dashboard metrics and analytics
   - Handles campaign management
   - Example implementation:

     ```dart
     class AffiliateService {
       final ApiClient _apiClient;

       Future<AffiliateProfile> getAffiliateProfile() async {
         // Implementation details
       }

       Future<AffiliateProfile> updateAffiliateProfile({
         String? companyName,
         String? websiteUrl,
         String? logoUrl
       }) async {
         // Implementation details
       }

       Future<AffiliateMetrics> getAffiliateMetrics({
         DateTime? startDate,
         DateTime? endDate,
         String? campaignId
       }) async {
         // Implementation details
       }

       Future<List<SponsoredContent>> getSponsoredContent() async {
         // Implementation details
       }
     }
     ```

3. **SponsoredContentService**

   - Manages sponsored content display rules
   - Handles filtering based on user preferences
   - Controls sponsored content placement
   - Example implementation:

     ```dart
     class SponsoredContentService {
       final ApiClient _apiClient;
       final UserProfileService _userProfileService;

       Future<SponsoredContentSettings> getSponsoredContentSettings() async {
         // Implementation details
       }

       bool shouldShowSponsoredContent() {
         // Check user's license and preferences
       }

       int getMaxSponsoredItemsPerPage() {
         // Based on user's license tier
       }

       Future<bool> updateSponsoredContentPreferences({
         required bool showSponsored
       }) async {
         // Implementation details
       }
     }
     ```

#### ViewModels

1. **ContentSharingViewModel**

   - Manages content sharing workflow
   - Handles permissions and visibility settings
   - Provides validation and feedback
   - Example implementation:

     ```dart
     class ContentSharingViewModel extends StateNotifier<ContentSharingState> {
       final SharingService _sharingService;
       final CategoryService _categoryService;

       Future<bool> shareContent({
         required String contentType,
         required String title,
         required String description,
         required Map<String, dynamic> content,
         required bool isPublic,
         String? categoryId,
         List<ContentPermission>? permissions
       }) async {
         // Implementation details
       }

       Future<List<Category>> getCategories() async {
         // Implementation details
       }

       Future<List<UserProfile>> searchUsers(String query) async {
         // Implementation details
       }
     }
     ```

2. **ContentDiscoveryViewModel**

   - Manages content discovery and browsing
   - Handles search and filtering
   - Tracks content interactions
   - Example implementation:

     ```dart
     class ContentDiscoveryViewModel extends StateNotifier<ContentDiscoveryState> {
       final SharingService _sharingService;
       final SponsoredContentService _sponsoredService;

       Future<void> loadContent({
         String? categoryId,
         String? query,
         bool refresh = false
       }) async {
         // Implementation details
       }

       Future<void> loadMoreContent() async {
         // Implementation details for pagination
       }

       Future<bool> trackContentView(String contentId) async {
         // Implementation details
       }

       Future<bool> trackContentClick(String contentId) async {
         // Implementation details
       }

       Future<bool> toggleSponsoredContentVisibility() async {
         // Implementation details
       }
     }
     ```

3. **AffiliateDashboardViewModel**

   - Provides affiliate performance metrics
   - Manages sponsored content campaigns
   - Handles analytics visualization
   - Example implementation:

     ```dart
     class AffiliateDashboardViewModel extends StateNotifier<AffiliateDashboardState> {
       final AffiliateService _affiliateService;

       Future<void> loadDashboardData({
         DateTime? startDate,
         DateTime? endDate,
         String? campaignId
       }) async {
         // Implementation details
       }

       Future<void> loadContentPerformance() async {
         // Implementation details
       }

       Future<bool> updateAffiliateProfile({
         String? companyName,
         String? websiteUrl,
         String? logoUrl
       }) async {
         // Implementation details
       }
     }
     ```

### 4.4. UI Components

1. **SponsoredContentBadge**

   - Visual indicator for sponsored content
   - Consistent styling across the app
   - Tooltip with explanation
   - Example implementation:

     ```dart
     class SponsoredContentBadge extends StatelessWidget {
       final Color? backgroundColor;
       final Color? textColor;
       final double? fontSize;

       @override
       Widget build(BuildContext context) {
         return Container(
           padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
           decoration: BoxDecoration(
             color: backgroundColor ?? Theme.of(context).colorScheme.secondary.withOpacity(0.1),
             borderRadius: BorderRadius.circular(4),
             border: Border.all(
               color: Theme.of(context).colorScheme.secondary.withOpacity(0.3),
             ),
           ),
           child: Text(
             'Sponsored',
             style: TextStyle(
               color: textColor ?? Theme.of(context).colorScheme.secondary,
               fontSize: fontSize ?? 10,
               fontWeight: FontWeight.w500,
             ),
           ),
         );
       }
     }
     ```

2. **SponsoredContentCard**

   - Displays sponsored content with clear visual differentiation
   - Includes affiliate information and branding
   - Tracks interaction analytics
   - Example implementation:

     ```dart
     class SponsoredContentCard extends StatelessWidget {
       final SharedContent content;
       final Function(String) onContentView;
       final Function(String) onContentClick;

       @override
       Widget build(BuildContext context) {
         // Implementation details
       }

       void _trackView() {
         onContentView(content.id);
       }

       void _trackClick() {
         onContentClick(content.id);
       }
     }
     ```

3. **SponsoredContentBanner**

   - Prominent display for featured sponsored content
   - Appears at top of category listings or search results
   - Supports rich media and call-to-action buttons
   - Example implementation:

     ```dart
     class SponsoredContentBanner extends StatelessWidget {
       final SharedContent content;
       final Function(String) onBannerView;
       final Function(String) onBannerClick;

       @override
       Widget build(BuildContext context) {
         // Implementation details
       }
     }
     ```

4. **AffiliateDashboardView**

   - Comprehensive dashboard for affiliates
   - Displays performance metrics and analytics
   - Provides content management tools
   - Example implementation:

     ```dart
     class AffiliateDashboardView extends ConsumerWidget {
       @override
       Widget build(BuildContext context, WidgetRef ref) {
         final dashboardState = ref.watch(affiliateDashboardProvider);

         // Implementation details
       }
     }
     ```

## 5. User Flows

### 5.1. Content Sharing

1. User creates or selects existing prompt/workflow
2. User selects "Share" option
3. User configures sharing settings (public/private)
4. User selects category and adds description
5. For private sharing, user selects specific recipients
6. System shares content and provides confirmation
7. Shared content appears in recipient's shared content section

### 5.2. Sponsored Content Creation (Affiliate)

1. Affiliate accesses affiliate dashboard
2. Affiliate selects "Create Sponsored Content"
3. Affiliate creates or selects existing prompt/workflow
4. Affiliate configures campaign settings (duration, limits)
5. Affiliate adds external URL if applicable
6. System submits content for approval (if required)
7. Once approved, content appears in sponsored listings

### 5.3. Content Discovery

1. User browses content by category or search
2. System displays mix of standard and sponsored content
3. Sponsored content is clearly marked and visually distinct
4. User can filter sponsored content (if license permits)
5. System tracks impressions for analytics
6. User can click/execute content
7. System tracks interactions for analytics

### 5.4. External Conversion

1. User views sponsored content
2. User clicks on external link/action button
3. System tracks outbound click
4. User is directed to external resource
5. External system may track conversion
6. Analytics are updated for affiliate reporting

## 6. License Tier Integration

### 6.1. Free Tier

- Cannot filter out sponsored content
- Sees maximum number of sponsored items
- Limited to public sharing only
- Cannot hide instructions when sharing

### 6.2. Pro Tier

- Can filter sponsored content (opt-out)
- Sees reduced number of sponsored items
- Can share privately with individuals
- Can hide instructions when sharing

### 6.3. Team Tier

- All Pro tier capabilities
- Can share with team members
- Team-wide sponsored content settings

### 6.4. Enterprise Tier

- Sponsored content hidden by default
- Option to completely disable sponsored content
- Unlimited private sharing
- Custom sharing permissions and workflows

## 7. Analytics and Reporting

### 7.1. Content Performance Metrics

- Impressions: Number of times content is displayed
- Clicks: User interactions with content
- Executions: Number of times content is run
- Shares: Number of times content is shared by others
- Conversions: External actions completed (if trackable)

### 7.2. Affiliate Dashboard

- Performance overview with key metrics
- Content-specific performance data
- Temporal analysis (daily, weekly, monthly)
- Conversion tracking and attribution
- Comparison against benchmarks
- Export capabilities for reporting

### 7.3. Admin Analytics

- Platform-wide engagement metrics
- Affiliate performance comparison
- Category performance analysis
- User segment analysis
- Revenue and conversion reporting

## 8. Security and Privacy Considerations

### 8.1. Content Permissions

- Granular permission system for shared content
- Proper validation of access rights
- Audit logging for permission changes
- Prevention of permission escalation

### 8.2. Affiliate Verification

- Identity verification for affiliates
- Content approval workflows
- Compliance with advertising regulations
- Monitoring for policy violations

### 8.3. Data Protection

- Privacy-preserving analytics
- Compliance with data protection regulations
- Secure handling of user interaction data
- Clear disclosure of sponsored content

## 9. Implementation Phases

### 9.1. Phase 1 (Q2 2025)

- Basic content sharing (public/private)
- Sponsored content display with visual indicators
- Simple affiliate profiles and content creation
- Basic analytics tracking

### 9.2. Phase 2 (Q3 2025)

- Enhanced affiliate dashboard with detailed metrics
- Advanced sponsored content placement algorithms
- License-based filtering options
- External conversion tracking

### 9.3. Phase 3 (Q4 2025)

- AI-powered content recommendations
- Advanced analytics and reporting
- A/B testing for sponsored content
- Enhanced monetization options

## 10. Deep Linking Strategy

The sharing feature relies on a robust deep linking strategy to enable seamless content sharing across platforms. This strategy includes standardized URL formats, platform-specific implementations, and UUID shortening techniques.

### 10.1. URL Formats

1. **Web URL Format**
   - Standard format: `https://www.promz.ai/p/{shortId}`
   - Used for web-based sharing and social media distribution

2. **Mobile Deep Link Format**
   - Standard format: `promz://p/{shortId}`
   - Path-based approach to preserve case sensitivity
   - Consistent with web URL structure for unified experience

3. **Legacy Format Support**
   - Format: `promz://{shortId}`
   - Maintained for backward compatibility

For detailed information on the deep linking implementation, including platform-specific configurations, UUID shortening algorithm, and security considerations, refer to the [Deep Link Strategy](deep-link-strategy.md) document.

## 11. Conclusion

The sharing feature with affiliate promotion capabilities provides a robust foundation for content distribution while creating monetization opportunities. By implementing clear visual differentiation for sponsored content and respecting user preferences based on license tiers, we balance promotional needs with user experience. The comprehensive analytics system ensures both affiliates and administrators can track performance and optimize content strategies.

---
