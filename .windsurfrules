# Development Guidelines

This file contains a consolidated set of rules to guide AI coding agents. It is tailored for the
"Promz" application, covering both client and server-side aspects, with specific guidance on code
quality, UI/UX principles, and performance considerations.

## Project: Promz

- **Name:** Promz: AI Made Easy
- **Description:** A Flutter-based client application for prompt management and collaboration, with
  a Go-based API server.
- **Architecture:**
  - **Client:** MVVM (Model-View-ViewModel) with Riverpod for state management (Flutter).
  - **Server:** Go-based API with gRPC, websocket and REST endpoints.
  - **Database:** Supabase for cloud storage and SQLite for local storage.
  - **Client-Server Communication:** gRPC for real-time updates, websocket for bi-directional
    communication and REST for standard API calls.
- **Key Priorities:**
  - Platform-specific UI with intuitive interactions (Client)
  - MVVM implementation with clear separation of concerns (Client)
  - Database optimization and efficient data handling (Client & Server)
  - Secure authentication flows (Client & Server)
  - Streaming API integration for real-time updates (Client & Server)
  - Real-time collaboration with conflict resolution (Server)
  - Reliable cloud synchronization (Server)
  - Entity detection and enhancement for autocomplete suggestions (Client & Server)

## Collaboration and Simplicity Principles

- **Strategy Before Implementation:**

  - Always begin by proposing and discussing potential approaches rather than jumping straight to
    code.
  - Ask clarifying questions to fully understand requirements before suggesting implementation
    details.
  - Present multiple solution options with their tradeoffs when appropriate.
  - Wait for explicit agreement on a strategy before proceeding with detailed implementation.

- **Simplicity Over Complexity:**

  - Prefer simple, readable and uncomplicated solutions even if they're less "clever" or elegant.
  - Apply the principle of least surprise - code should be predictable and straightforward.
  - Avoid premature optimization or over-engineering.
  - Recognize when a simpler solution, even if less "perfect," would be more maintainable.
  - Challenge yourself to eliminate unnecessary abstractions and complexity.

- **Collaborative Development:**

  - Approach each task as a partnership rather than a service delivery.
  - Check for understanding and agreement at key decision points.
  - Present implementation plans in plain language before writing extensive code.
  - Be receptive to feedback and willing to pivot approaches when needed.

- **Iterative Refinement:**
  - Start with the simplest working solution, then refine only as needed.
  - Break complex problems into smaller, more manageable steps.
  - Validate understanding of requirements at each stage before proceeding.
  - Document assumptions and design decisions to facilitate discussion.

## Code Modification Guidelines

- **Minimal Changes Principle:**

  - When adding new functionality, make minimal changes to existing code structure.
  - Preserve existing method signatures, parameter names, and return types whenever possible.
  - Avoid reformatting, reorganizing, or refactoring existing code unless specifically requested.
  - Do not modify comments, documentation, or code style unless necessary for the task at hand.
  - When adding new services or components, follow the existing patterns and naming conventions.

- **Service Integration:**

  - When adding new services to existing service containers (e.g., ClientContextService), maintain the existing initialization pattern.
  - Add new fields and getters in the same style and location as existing ones.
  - If a service needs to be started or initialized, do so in the existing initialization methods.
  - Preserve the existing dependency injection patterns for testability.

- **Mobile-First Approach:**
  - Remember that the application primarily targets mobile platforms (iOS and Android).
  - We support Mac OS and Windows as secondary platforms.
  - Optimize for mobile performance, battery life, and memory usage.
  - Consider the impact of background tasks on mobile device resources.

## Compiler Error Handling Guidelines

- **Strict Compliance:**
  All generated code must compile without errors. Warnings (such as missing const usage for Dart)
  should be fixed automatically unless it's the first edition of a file.
- **Auto-Correction Mandate:**
  If a compiler error is detected, immediately apply an auto-fix routine.

## Core Coding Guidelines

### Code Changes and Refactoring

- **IMPORTANT:** Any change that affects multiple files (typically 3+ files) is considered a
  refactor and requires explicit confirmation from the human before proceeding.
- Before implementing any refactor, provide a clear plan outlining all files to be modified.
- Present alternatives when refactoring is required, including minimal-change approaches.
- Document all refactored components with clear explanations of the changes made.
- Look for existing code that can be reused instead of writing new code.

### Consistent Coding Style

- Follow platform-specific best practices for all languages (Dart/Flutter and Go).
- Ensure code is modular, readable, and self-documented.
- Use named parameters for widgets with more than 2 parameters in Dart/Flutter.
- Prefer const constructors where possible in Dart/Flutter.
- Implement proper dispose methods for controllers, streams, and other resources.
- Limit all generated code lines to a maximum of 100 characters.
- Use comments only to add meaningful context for complex logic or explain "why" rather than "what".
- Preserve existing comments unless they become invalid due to code changes.

### UI/UX Principles

- **Visual Hierarchy:**
  - Use meaningful icons to distinguish between different types of content/actions
  - Apply consistent styling for similar elements across the application
  - Employ visual cues (colors, icons, typography) to communicate importance and functionality
- **User Feedback:**
  - Provide clear feedback for user interactions (loading states, success/error messages)
  - Design responsive UIs that adapt to different screen sizes and orientations
  - Show meaningful error messages that guide users toward resolution
- **Component Styling:**
  - Use the app's color palette consistently through ColorScheme references
  - Apply appropriate padding and spacing for comfortable reading and interaction
  - Include appropriate visual feedback for interactive elements (hover, press states)

### Database and Backend Architecture

- **Supabase Integration:**

  - The project uses Supabase as the primary database and backend service.
  - Supabase client is initialized using environment variables `PROMZ_SUPABASE_URL` and `PROMZ_SUPABASE_KEY`.
  - All direct database operations should use the `SupabaseClient` interface defined in `api/internal/repository/db/supabase.go`.
  - Never use raw SQL queries directly; always use the appropriate Supabase client methods.

- **Service Layer Architecture:**

  - Follow the adapter pattern when integrating with external services or databases.
  - The `InstructionsService` requires a database connection to fetch prompt instructions and
    parameters.
  - When implementing services that need database access, create adapters that use the
    `SupabaseClientInterface` instead of direct SQL connections.
  - Maintain clear separation between domain models, repository interfaces, and service
    implementations.

- **LLM Provider Management:**

  - The application supports multiple LLM providers (OpenAI, Google AI) with graceful fallback.
  - Always include a mock provider for development and testing environments.
  - LLM providers are initialized based on available API keys from environment variables.
  - The LLM executor requires an `InstructionsService` to fetch prompt-specific instructions and
    parameters.

- **Environment Configuration:**
  - Required environment variables:
    - `PROMZ_SUPABASE_URL`: URL for Supabase
    - `PROMZ_SUPABASE_KEY`: Key for Supabase
    - `OPENAI_API_KEY`: (Optional) API key for OpenAI provider
    - `GOOGLE_AI_API_KEY`: (Optional) API key for Google AI provider
    - `PORT`: (Optional, defaults to 8080) Port for the server to listen on
  - Use the `godotenv` package to load environment variables from `.env` file in development.
  - Always validate required environment variables at application startup.

### Performance Optimization

- **Efficient Data Handling:**
  - Use pagination for large datasets
  - Implement caching for expensive computations and network requests
  - Apply debouncing for input handling and search functionality
  - Optimize list rendering with proper keys and lazy loading
- **Resource Management:**
  - Properly dispose of controllers, streams, and other resources
  - Use const constructors for static widgets to improve rebuild performance
  - Minimize rebuilds by using selective state management approaches
- **Memory Considerations:**
  - Be cautious with large in-memory data structures
  - Use efficient data structures appropriate for the task
  - Implement proper error handling for memory-intensive operations

### Dependency and Environment Management

- Validate that all dependencies are correctly defined and managed.
- Keep dependencies updated while ensuring compatibility.
- Follow semantic versioning principles.

### String and Resource Management

- **Single Source of Truth:**
  - All string constants must be defined in the common package
    (`packages/promz_common/lib/strings.dart`).
  - Never create duplicate string constant files in client or server directories.
  - When adding new strings, always add them to the existing `Strings` class in the common package.
  - Organize strings by logical categories with clear section comments.

- **String Usage:**
  - Import the common package strings with `import 'package:promz_common/promz_common.dart'`.
  - Access strings directly via the `Strings` class (e.g., `Strings.appTitle`).
  - Never use hardcoded string literals for user-facing text.
  - For client-specific or server-specific strings, still add them to the common package but with
    appropriate section comments.

- **Localization Preparation:**
  - Keep all strings in the common package to simplify future localization efforts.
  - Use descriptive constant names that reflect the content and purpose of the string.
  - Avoid concatenating strings; use string interpolation or formatted strings instead.

### Automated Testing and Validation

- Aim for a minimum of 80% code coverage in Dart/Flutter projects.
- Ensure tests complete in under 2 minutes.
- Write independent, focused tests that verify specific functionality.
- Test edge cases and error conditions, not just happy paths.
- Mock external dependencies appropriately.

### Mock Implementation Best Practices

#### Common Principles (Both Go & Flutter)

- Define interfaces for services before implementing concrete types
- Design interfaces around behavior rather than implementation details
- Use dependency injection to make services testable
- Ensure mock behavior matches the expectations in test assertions
- Document any special mock behavior that differs from the real implementation

#### Go Server Mocks

- **Organization:**
  - Keep all mock implementations in dedicated files with clear naming (e.g., `mock_service.go`)
  - Consolidate mock logic into a single location to prevent conflicts and redundancy
  - Use a consistent naming pattern: `Mock{ServiceName}` for mock types

- **Implementation:**
  - Implement mocks with function fields that can be customized for different test scenarios
  - Provide sensible default implementations that return zero values or empty collections
  - Include helper methods to configure common test scenarios (e.g., `WithSuccessResponse()`)
  - For variables that might be missing in production, decide on a consistent approach (return empty strings vs. omit fields)
  - Consider using the builder pattern for complex mock configurations

#### Flutter/Dart Client Mocks

- **Organization:**
  - Use the `mockito` or `mocktail` packages for generating mocks
  - Keep mocks in a separate `test/mocks` directory with a consistent structure
  - Consider using `build_runner` to generate mocks from interfaces

- **Implementation:**
  - Use annotations like `@GenerateMocks` with mockito to generate mock classes
  - Implement `setUp` and `tearDown` methods to reset mocks between tests
  - Use `when().thenReturn()` syntax for defining mock behavior
  - For async methods, use `when().thenAnswer()` with `Future.value()` or `Stream.value()`
  - Consider using test fixtures to set up common mock scenarios

## Language-Specific Rules

### Dart/Flutter (Client)

- **File Organization:** Follow the `lib/` structure: `views/`, `viewmodels/`, `models/`,
  `services/`, `repositories/`, `utils/`, `constants/`.

- **Avoiding Deprecated APIs:**

  - Avoid using deprecated Flutter/Dart APIs in new code
  - Replace `Color.withOpacity()` with `Color.withAlpha((opacity * 255).round())` to avoid precision
    loss
  - Update existing code to use current APIs when modifying related sections
  - Check Flutter documentation for the latest recommended approaches

- **State Management Requirements:**

  - **IMPORTANT:** Use only Riverpod, not Provider. We have migrated away from Provider.
  - When modifying existing code that uses Provider, convert it to use Riverpod.
  - Use `ConsumerWidget` instead of `StatelessWidget` for widgets that need state.
  - Access state with `ref.watch(provider)` instead of `Provider.of<T>(context)`.
  - Use `ref.read(provider)` for one-time accesses (like in callbacks).
  - Define providers at the top level as
    `final myProvider = ChangeNotifierProvider((_) => MyModel())`.

- **MVVM Implementation:**

  - **Views:**

    - Keep business logic minimal or non-existent
    - Use BuildContext extensions for accessing ViewModels
    - Implement responsive UI that adapts to different screen sizes
    - Follow Material Design 3 guidelines for Android and Cupertino for iOS
    - Use proper widget composition to avoid deep nesting
    - Leverage StatelessWidget with Consumer pattern for state-dependent UI

  - **ViewModels:**

    - Implement business logic and state management
    - Expose state through Riverpod providers
    - Handle data transformation and validation
    - Implement proper error handling and logging
    - Use immutable state objects when possible
    - Provide clear API for views to interact with

  - **Models:**

    - Implement immutable data structures with copyWith methods
    - Use factory constructors for different creation patterns
    - Implement proper equality and hash code methods
    - Use JSON serialization for data transfer
    - Implement proper validation in constructors or factory methods
    - Use named constructors for different initialization scenarios

  - **Services:**

    - Implement business logic that spans multiple features
    - Use dependency injection for testability
    - Implement proper error handling and logging
    - Use interfaces for service contracts
    - Implement caching and optimization strategies
    - Handle asynchronous operations properly

  - **Key UI Components:**

    - **Variables System:**

      - VariablesDialog: Modal interface for editing template variables with proper state management
      - VariablesSection: Compact display of active variables as chips with edit functionality
      - Variable resolution in templates using the {{CATEGORY:ENTITY}} format
      - Support for both entity-based and custom variables
      - Proper handling of variable state during editing and dismissal
      - Includes Save button to persist changes
      - Includes Execute button to save changes and trigger prompt execution
      - Restores original values when canceled
      - Uses Material Design components

    - **Autocomplete System:**

      - AutocompleteField: Advanced text input with real-time suggestions
      - Intelligent entity detection and suggestion prioritization
      - Debounced suggestion requests for performance
      - Platform-specific animations and behaviors
      - Proper overlay management for suggestions

    - **Prompt Management:**
      - Prompt list with filtering and sorting capabilities
      - Detailed prompt view with editing capabilities
      - Support for template variables and instructions
      - Category-based organization
      - Recent and popular prompts tracking

### Flutter-Specific Guidelines

- **Widget Composition:**

  - Break complex UI into smaller, reusable widgets
  - Use composition over inheritance for widget customization
  - Implement proper widget keys for efficient rebuilds
  - Use const constructors for static widgets
  - Leverage SliverList and SliverGrid for large collections
  - Use CustomScrollView for complex scrollable layouts

- **State Management with Riverpod:**

  - Use ConsumerWidget instead of StatelessWidget for state-dependent UI
  - Leverage ref.watch() for reactive state updates
  - Use ref.read() for one-time state access in callbacks
  - Implement proper state immutability with copyWith patterns
  - Use StateNotifierProvider for complex state management
  - Leverage Provider.family for parameterized providers
  - Use Provider.autoDispose for providers that should be disposed when not in use

- **Asynchronous Operations:**

  - Use FutureProvider for asynchronous data loading
  - Implement proper loading, error, and success states
  - Use debouncing for frequent operations like text input
  - Handle cancellation properly for long-running operations
  - Implement retry mechanisms for network operations
  - Use AsyncValue for representing asynchronous state

- **Platform-Specific Considerations:**

  - Use platform-specific widgets (Material for Android, Cupertino for iOS)
  - Implement proper back button handling for Android
  - Use platform-specific animations and transitions
  - Handle keyboard properly on both platforms
  - Implement proper text selection and editing
  - Use platform-specific icons and visual styles

- **Performance Optimization:**
  - Use const constructors for static widgets
  - Implement proper widget keys for efficient rebuilds
  - Use ListView.builder for large lists
  - Implement pagination for large data sets
  - Use caching for expensive computations
  - Implement proper image caching and optimization
  - Use memory-efficient data structures
  - Avoid unnecessary rebuilds with selective state updates

### Mobile-Specific Guidelines

- **Responsive Design:**

  - Use MediaQuery to adapt to different screen sizes
  - Implement proper landscape and portrait layouts
  - Use LayoutBuilder for responsive widget sizing
  - Implement proper keyboard handling
  - Use proper text scaling for accessibility
  - Implement proper widget constraints

- **Navigation:**

  - Use Navigator 2.0 for complex navigation scenarios
  - Implement proper deep linking support
  - Use named routes for better maintainability
  - Implement proper back button handling
  - Use transitions appropriate for each platform
  - Implement proper error handling for navigation

- **Offline Support:**

  - Implement proper caching for offline access
  - Use optimistic updates for better user experience
  - Implement proper synchronization when back online
  - Handle network errors gracefully
  - Provide clear feedback for offline state
  - Implement proper retry mechanisms

- **Security:**
  - Store sensitive data securely using platform-specific secure storage
  - Implement proper authentication and authorization
  - Use HTTPS for all network requests
  - Implement proper input validation
  - Handle sensitive data properly in memory
  - Implement proper error handling without exposing sensitive information

### Testing Guidelines

#### Unit Testing

- **Business Logic**: Test all business logic with unit tests.
- **View Models**: Test view models with mocked dependencies.
- **Services**: Test services with mocked external dependencies.
- **Models**: Test model serialization and validation.
- **Utilities**: Test utility functions thoroughly.
- **Edge Cases**: Cover edge cases and error conditions.
- **Parameterized Tests**: Use parameterized tests for similar test cases.

#### Widget Testing

- **Component Testing**: Test individual widgets in isolation.
- **Integration Testing**: Test widget interactions within screens.
- **Navigation Testing**: Test navigation flows between screens.
- **Form Validation**: Test form validation and submission.
- **Error States**: Test error handling and recovery.
- **Loading States**: Test loading indicators and transitions.
- **Accessibility**: Test accessibility features and semantics.

#### Integration Testing

- **End-to-End Flows**: Test complete user flows.
- **Database Integration**: Test database operations.
- **API Integration**: Test API interactions with mock servers.
- **File System**: Test file system operations.
- **Permissions**: Test permission requests and handling.
- **Background Tasks**: Test background task execution.
- **Deep Links**: Test deep link handling.

## State Management

- **State Management:**

  - Use Provider/Riverpod for dependency injection
  - Prefer StateNotifier for complex state management
  - Implement proper state restoration
  - Handle loading/error states consistently
  - Use appropriate scoping for providers

- **Widget Implementation:**

  - Use semantically appropriate widgets (Button vs GestureDetector, etc.)
  - Apply consistent visual styling across similar components
  - Use meaningful icons to distinguish between different types of content
  - Implement appropriate feedback for user interactions
  - Ensure responsive layouts that work across device sizes

- **Error Handling:**

  - Use `appLog` for logging errors with appropriate log levels
  - Implement user-facing error messages that are helpful
  - Gracefully handle edge cases and unexpected inputs
  - Provide recovery paths where possible

- **Entity Detection and Template Handling:**

  - Implement detection for various entity types (finance, location, organization, etc.)
  - Use a consistent template format: `{{CATEGORY:ENTITY}}` for internal representation
  - Support fuzzy matching for entity detection with appropriate thresholds
  - Provide user-friendly display text for detected entities
  - Apply proper text replacement strategies for entities:
    - Replace single-word entities at the current cursor position
    - Handle multi-word entity templates appropriately
  - Integrate with suggestion mechanisms for seamless user experience
  - Cache detected entities to improve performance
  - Apply debouncing to prevent excessive processing during typing

- **Testing:**
  - **Organization:** `test/unit/`, `test/widget/`, `test/integration/`, `test/mocks/`,
    `test/helpers/`
  - **Strategy:** Unit tests for business logic, widget tests for UI, integration tests for flows
  - **Required Scenarios:** Authentication, data management, offline behavior, UI/UX responsiveness

## Go (Server)

- **API Design:**

  - Use clear, descriptive endpoint names.
  - Follow RESTful principles for CRUD operations.
  - Use RPC-style endpoints for complex operations.
  - Implement proper request validation.
  - Return meaningful error responses with appropriate HTTP status codes.
  - Document all endpoints with Swagger/OpenAPI.

- **Database Interactions:**

  - Use connection pooling with `pgxpool` for PostgreSQL.
  - Implement transactions for atomic operations.
  - Handle database errors gracefully with appropriate logging.
  - Optimize queries and use appropriate indexing.
  - Implement defensive measures against SQL injection.
  - Implement JWT-based authentication and authorization.
  - Validate all input data against schemas.
  - Secure real-time collaborative editing endpoints.
  - Implement rate limiting for public endpoints.
  - Use HTTPS for all communications.

- **Real-Time Synchronization:**

  - Implement versioning and conflict resolution strategies.
  - Use WebSockets for real-time updates.
  - Optimize for low-latency operations.
  - Implement heartbeat mechanisms to detect disconnections.
  - Design fallback mechanisms for when WebSockets aren't available.

- **Testing:**
  - **Unit Testing:** Aim for high test coverage, especially for core business logic.
  - **Integration Testing:** Test end-to-end API workflows.
  - **Security Testing:** Test authentication flows and authorization rules.
  - **Performance Testing:** Benchmark critical operations and optimize as needed.
  - **Real-Time Testing:** Simulate multiple users with concurrent operations.

## SQL

- **Database Migration:**

  - Prefer direct SQL schema execution over ORM migrations.
  - Use cross-database data dumping with manual SQL transformation rather than third-party tools.
  - Version all migrations and make them repeatable.
  - Implement schema version verification to ensure database integrity.
  - Track schema changes in a dedicated schema_version table.

- **Data Type Handling:**

  - Handle PostgreSQL to SQLite conversions carefully, especially for complex types.
  - Pay special attention to TIMESTAMP (convert to INTEGER milliseconds in SQLite), JSONB (store as
    TEXT), BOOLEAN (0/1 in SQLite), and UUID types.
  - Convert timestamp literals using consistent format (milliseconds since epoch for SQLite).
  - Use explicit type definitions and conversions rather than relying on implicit behavior.
  - Implement appropriate validation for data type conversions.

- **Data Export Process:**

  - Export reference tables first, then dependent tables, then junction tables to preserve
    referential integrity.
  - Focus on essential elements when converting between databases (CREATE TABLE and INSERT
    statements).
  - Skip PostgreSQL-specific constructs like functions, extensions, and sequences when exporting
    to SQLite.
  - Preserve schema relationships and constraints during conversion.
  - Implement thorough verification steps after database creation or migration.
  - Keep diagnostic information and logs for troubleshooting database operations.

- **UUID Management:**
  - Use consistent UUID generation (prefer v7 UUIDs for time-sortable identifiers).
  - Handle UUID storage efficiently in SQLite databases (as TEXT).
  - Ensure UUID field mapping is consistent between client and server.

## Database Architecture

- **Schema Design:**

  - Maintain backward compatibility when evolving schemas.
  - Document schema changes thoroughly with migration scripts.
  - Prefer explicit naming over inferred conventions (e.g., "subtitle" instead of "content").
  - Design schema for both server-side (PostgreSQL) and client-side (SQLite) compatibility.

- **Cross-Platform Considerations:**

  - Design database operations to work across platforms (Android, iOS, Mac, Windows and Web).
  - Ensure SQLite implementations work consistently on all client platforms.
  - Consider platform-specific limits and restrictions when designing schemas and queries.
  - Implement retry logic and graceful degradation for database operations.
  - Provide clear error messages to users when database operations fail.

- **Development Tools:**
  - Maintain scripts for common database operations (export, import, migration).
  - Use parametrized queries to prevent SQL injection.
  - Create utility functions for common database operations.

## Logging

- **Principles:**

  - Use concise and performance-conscious logging.
  - Follow specified log levels (Debug, Info, Warning, Error).
  - Include method entry/exit points in debug logs.
  - Log all errors with appropriate context.
  - Use structured logging where supported.

- **Implementation:**
  - Always use `appLog` instead of `print` or `debugPrint`.
  - Include appropriate log levels based on message importance.
  - Provide context with named loggers.
  - Include stack traces for errors.
  - Don't over-log in performance-critical paths.

## Content Processing

- **Text Analysis:**

  - Use asynchronous processing for large texts.
  - Support multiple content types (chat, financial, technical).
  - Maintain configurable thresholds and weights.
  - Implement appropriate error handling for parsing failures.

- **File Processing:**

  - Support PDF, Text, WhatsApp, and ZIP files.
  - Implement content-based deduplication.
  - Use streaming approaches for large files.
  - Validate file integrity before processing.

- **Performance:**

  - Stream large files rather than loading entirely into memory.
  - Dispose of resources properly after use.
  - Use efficient data structures for content representation.
  - Implement caching for expensive operations.

- **Error Handling:**
  - Handle invalid file formats gracefully.
  - Manage encoding failures with appropriate fallbacks.
  - Address memory constraints with chunked processing.
  - Implement timeouts for long-running operations.

## Debugging Practices

- **Logging Standards:**

  - Use consistent log tags for each component (e.g., "PromptSuggestionService",
    "EntityDetectionService")
  - Include context information in log messages (method name, operation being performed)
  - Log at appropriate levels (verbose for development details, info for general flow, warning for
    potential issues, error for failures)
  - Avoid logging sensitive information like user data or authentication tokens

- **Troubleshooting Strategies:**
  - Always check logcat output first when encountering issues
  - Use strategic logging to trace execution flow through complex operations
  - For entity detection issues, log detected entities and their properties
  - For database issues, log query parameters and results
  - When debugging UI issues, log state changes and widget rebuilds

## Lessons Learned & Best Practices

### API Key Security

- **Never hardcode API keys** or sensitive credentials in client code
- Use platform-specific secure storage (Flutter Secure Storage, Keychain, etc.) for storing credentials
- Implement proper initialization checks to prompt users for missing credentials
- Add middleware on server-side to verify API keys with appropriate error responses
- Use appropriate HTTP headers (like X-API-Key) rather than including keys in request bodies

### Database Interface Compatibility

- Define clear interfaces for database access that work across different database libraries
- When using multiple database libraries (e.g. database/sql and pgx), create explicit adapter patterns
- Prefer dependency injection of database interfaces over concrete implementations
- Document database interface requirements clearly to avoid compatibility issues
- Test database code with both real and mock implementations to ensure interface compliance

### Package Organization in Go

- Avoid duplicate function/variable names within the same package, even across different files
- Consider using subpackages for better separation when functionality grows
- Use clear naming conventions for functions with similar purposes in different contexts
- Document shared package variables and their initialization requirements
- Follow the principle of "accept interfaces, return structs" for maximum flexibility

### Flutter Async Context Safety

- Always check `context.mounted` before using a BuildContext after an await statement
- Design ViewModels to avoid passing BuildContext whenever possible
- For dialogs and navigation after async operations, implement safe patterns using state management
- Consider using async builders (FutureBuilder, StreamBuilder) for UI that depends on async operations
- Remember that any awaited operation might complete after the widget is disposed

### Cross-Platform Error Handling

- Translate technical errors into user-friendly messages
- Categorize common error types (network, authentication, permission) and handle each appropriately
- Provide actionable error messages with clear next steps for users
- Implement retry mechanisms with exponential backoff for transient failures
- Log detailed technical errors while showing simplified messages to users
- Design error states as part of your application's UI system for consistent presentation

### Entity Template Processing

- Use consistent template formats for different entity types (e.g., `{{FINANCE:SP500}}`)
- Implement efficient parsing of entity templates with appropriate error handling
- Consider caching detected entities to improve performance
- Provide clear visual indicators for template entities in UI
- Support both entity detection (raw text to templates) and entity expansion (templates to display text)
- Handle ambiguity with appropriate context clues and disambiguation strategies
- Implement fuzzy matching with configurable thresholds based on entity type

## Client Architecture Components

### ClientContextService (CCS)

- **Purpose:**

  - Central service for managing application context and state
  - Handles prompt variables, entity detection, and suggestions
  - Provides unified access to core services and features
  - Maintains application-wide state and context

- **Key Responsibilities:**

  - Prompt variable management through VariableManager
  - Entity detection and processing
  - Prompt suggestions and recommendations
  - State synchronization across components

- **Usage Guidelines:**
  - Initialize CCS at app startup using `ClientContextService.initialize()`
  - Access through factory constructor `ClientContextService()`
  - Use dependency injection for components requiring CCS
  - Avoid direct manipulation of internal state
  - Properly handle async operations with BuildContext
