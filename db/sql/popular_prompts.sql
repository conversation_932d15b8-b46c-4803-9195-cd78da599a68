-- Table: local_prompt_usage
-- Description: Stores local prompt usage events before syncing to the server. This schema is for
-- local client usage as it gets converted to SQLite and synced to the server directly using this
-- table or schema.
CREATE TABLE IF NOT EXISTS local_prompt_usage (
    id SERIAL PRIMARY KEY,
    prompt_id TEXT NOT NULL,
    event_type TEXT NOT NULL CHECK (event_type IN ('selected', 'executed')),
    used_at INTEGER NOT NULL,
    country_code TEXT NULL
);

-- Table: prompt_usage_log
-- Description: Stores prompt usage events from clients for popularity tracking.
CREATE TABLE IF NOT EXISTS prompt_usage_log (
    log_id UUID PRIMARY KEY DEFAULT uuid_generate_v7(),
    prompt_id UUID NOT NULL REFERENCES prompts(id) ON DELETE CASCADE,
    event_type TEXT NOT NULL CHECK (event_type IN ('selected', 'executed')),
    used_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    country_code TEXT NULL
);

-- Function: update_popular_prompts_cache
-- Description: Updates the popularity_score column in prompts table with the latest popular prompts scores
-- Parameters:
--   p_prompts: JSON array of popular prompts with prompt_id and score
-- Returns: Success status

CREATE OR REPLACE FUNCTION update_popular_prompts_cache(
    p_prompts JSONB
)
RETURNS JSONB AS $$
DECLARE
    v_prompt JSONB;
    v_prompt_id UUID;
    v_score INTEGER;
    v_result JSONB;
    v_update_count INTEGER := 0;
BEGIN
    -- Update popularity scores directly in the prompts table
    FOR v_prompt IN SELECT * FROM jsonb_array_elements(p_prompts)
    LOOP
        v_prompt_id := (v_prompt->>'prompt_id')::UUID;
        v_score := (v_prompt->>'popularity_score')::INTEGER;
        
        UPDATE prompts 
        SET popularity_score = v_score, server_sync_at = CURRENT_TIMESTAMP
        WHERE id = v_prompt_id;
        
        GET DIAGNOSTICS v_update_count = ROW_COUNT;
        
        -- If prompt doesn't exist, log it
        IF v_update_count = 0 THEN
            RAISE NOTICE 'Prompt ID % not found in prompts table', v_prompt_id;
        END IF;
    END LOOP;
    
    v_result := jsonb_build_object('success', true, 'count', jsonb_array_length(p_prompts));
    RETURN v_result;
EXCEPTION WHEN OTHERS THEN
    v_result := jsonb_build_object('error', SQLERRM);
    RETURN v_result;
END;
$$ LANGUAGE plpgsql;

-- Function: record_prompt_usage_events
-- Description: Records prompt usage events in the prompt_usage_log table
-- Parameters:
--   p_events: JSON array of prompt usage events with prompt_id, event_type, timestamp, and country_code
-- Returns: Success status

CREATE OR REPLACE FUNCTION record_prompt_usage_events(
    p_events JSONB
)
RETURNS JSONB AS $$
DECLARE
    v_event JSONB;
    v_prompt_id UUID;
    v_event_type TEXT;
    v_timestamp TIMESTAMP WITH TIME ZONE;
    v_country_code TEXT;
    v_count INTEGER := 0;
    v_result JSONB;
BEGIN
    -- Insert each event into the prompt_usage_log table
    FOR v_event IN SELECT * FROM jsonb_array_elements(p_events)
    LOOP
        BEGIN
            v_prompt_id := (v_event->>'prompt_id')::UUID;
            v_event_type := v_event->>'event_type';
            
            -- Use provided timestamp or current timestamp
            IF v_event->>'used_at' IS NOT NULL THEN
                v_timestamp := (v_event->>'used_at')::TIMESTAMP WITH TIME ZONE;
            ELSE
                v_timestamp := CURRENT_TIMESTAMP;
            END IF;
            
            v_country_code := v_event->>'country_code';
            
            INSERT INTO prompt_usage_log (log_id, prompt_id, event_type, used_at, country_code)
            VALUES (uuid_generate_v7(), v_prompt_id, v_event_type, v_timestamp, v_country_code);
            
            v_count := v_count + 1;
        EXCEPTION WHEN OTHERS THEN
            -- Log the error but continue processing other events
            RAISE NOTICE 'Error processing event: %', SQLERRM;
        END;
    END LOOP;
    
    v_result := jsonb_build_object('success', true, 'count', v_count);
    RETURN v_result;
EXCEPTION WHEN OTHERS THEN
    v_result := jsonb_build_object('error', SQLERRM);
    RETURN v_result;
END;
$$ LANGUAGE plpgsql;

-- Function: get_prompt_usage_events
-- Description: Retrieves prompt usage events from the last N days
-- Parameters:
--   p_days: Number of days to look back for usage data (default: 30)
-- Returns: Table with prompt_id, event_type, used_at, and country_code

CREATE OR REPLACE FUNCTION get_prompt_usage_events(
    p_days INTEGER DEFAULT 30
)
RETURNS TABLE (
    prompt_id UUID,
    event_type TEXT,
    used_at TIMESTAMP WITH TIME ZONE,
    country_code TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        pul.prompt_id,
        pul.event_type,
        pul.used_at,
        pul.country_code
    FROM 
        prompt_usage_log pul
    WHERE 
        pul.used_at > (CURRENT_TIMESTAMP - (p_days || ' days')::INTERVAL)
    ORDER BY 
        pul.used_at DESC;
END;
$$ LANGUAGE plpgsql;

-- Function: get_cached_popular_prompts
-- Description: Retrieves popular prompts directly from the prompts table
-- Parameters:
--   p_limit: Maximum number of prompts to return (default: 10)
-- Returns: Table with prompt_id and popularity_score columns

CREATE OR REPLACE FUNCTION get_cached_popular_prompts(
    p_limit INTEGER DEFAULT 10
)
RETURNS TABLE (
    prompt_id TEXT,
    popularity_score INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id::TEXT,
        p.popularity_score
    FROM 
        prompts p
    WHERE
        p.popularity_score > 0
    ORDER BY 
        p.popularity_score DESC
    LIMIT 
        p_limit;
END;
$$ LANGUAGE plpgsql;

-- Function: get_popular_prompts_with_data
-- Description: Retrieves popular prompts with complete prompt data in a single query
-- Parameters:
--   p_limit: Maximum number of prompts to return (default: 10)
--   p_days: Number of days to look back for usage data (default: 30)
-- Returns: Table with complete prompt data including popularity score

CREATE OR REPLACE FUNCTION get_popular_prompts_with_data(
    p_limit INTEGER DEFAULT 10,
    p_days INTEGER DEFAULT 30
)
RETURNS TABLE (
    id UUID,
    title TEXT,
    subtitle TEXT,
    source TEXT,
    created_at TIMESTAMP WITH TIME ZONE,
    is_synced BOOLEAN,
    category_id UUID,
    keywords JSONB,
    variables JSONB,
    version INTEGER,
    popularity_score INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.title,
        p.subtitle,
        p.source,
        p.created_at,
        p.is_synced,
        p.category_id,
        p.keywords,
        p.variables,
        p.version,
        p.popularity_score
    FROM 
        prompts p
    WHERE
        p.popularity_score > 0
    ORDER BY 
        p.popularity_score DESC
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql;
