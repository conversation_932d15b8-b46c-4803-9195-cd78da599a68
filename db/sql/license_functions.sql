-- Function to get or create a license with improved error handling and response
CREATE OR REPLACE FUNCTION get_or_create_license_enhanced(
  p_user_id UUID,
  p_license_type TEXT DEFAULT 'free',
  p_days_valid INT DEFAULT 36500
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  license_record RECORD;
  new_api_key UUID;
  result JSON;
  is_new BOOLEAN := false;
BEGIN
  -- First, check if the user already has a license of this type
  SELECT * INTO license_record
  FROM public.licenses
  WHERE user_id = p_user_id
  AND license_type = p_license_type
  LIMIT 1;
  
  -- If license exists, check if it's active
  IF license_record IS NOT NULL THEN
    -- If license is active, return it
    IF license_record.is_active THEN
      result := json_build_object(
        'is_new', false,
        'api_key', license_record.api_key,
        'is_active', license_record.is_active,
        'license_id', license_record.id,
        'expiry_date', license_record.expiry_date,
        'license_type', license_record.license_type
      );
      RETURN result;
    ELSE
      -- If license exists but is inactive, reactivate it with a new expiry date
      UPDATE public.licenses
      SET is_active = true,
          expiry_date = CASE 
                          WHEN LOWER(p_license_type) = 'free' 
                          THEN now() + (p_days_valid || ' days')::interval
                          ELSE now() + (30 || ' days')::interval
                        END,
          updated_at = now()
      WHERE id = license_record.id
      RETURNING * INTO license_record;
      
      is_new := false;
    END IF;
  ELSE
    -- Generate a new API key as UUID directly (without converting to text)
    new_api_key := gen_random_uuid();
    
    -- Create a new license
    INSERT INTO public.licenses (
      user_id,
      license_type,
      is_active,
      expiry_date,
      api_key
    ) VALUES (
      p_user_id,
      p_license_type,
      true,
      CASE 
        WHEN LOWER(p_license_type) = 'free' 
        THEN now() + (p_days_valid || ' days')::interval
        ELSE now() + (30 || ' days')::interval
      END,
      new_api_key
    ) RETURNING * INTO license_record;
    
    is_new := true;
  END IF;
  
  -- Return the license information
  result := json_build_object(
    'is_new', is_new,
    'api_key', license_record.api_key,
    'is_active', license_record.is_active,
    'license_id', license_record.id,
    'expiry_date', license_record.expiry_date,
    'license_type', license_record.license_type,
    'api_key', license_record.api_key,
  );
  
  RETURN result;
EXCEPTION WHEN OTHERS THEN
  -- Handle any errors
  result := json_build_object(
    'error', SQLERRM,
    'error_code', SQLSTATE
  );
  RETURN result;
END;
$$;

-- Function to verify an API key with enhanced response data and caching support
CREATE OR REPLACE FUNCTION verify_api_key_enhanced(p_api_key TEXT)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  license_record RECORD;
  result JSON;
  is_free BOOLEAN;
BEGIN
  -- Look for the license directly in the licenses table by API key
  -- This is more efficient than using the vault for simple verification
  SELECT * INTO license_record
  FROM public.licenses
  WHERE api_key = p_api_key::uuid
  LIMIT 1;
  
  -- If no license found with this API key, return invalid
  IF license_record IS NULL THEN
    result := json_build_object(
      'valid', false,
      'reason', 'invalid_key',
      'error_code', 'LICENSE_NOT_FOUND'
    );
    RETURN result;
  END IF;
  
  -- Check if license is active
  IF NOT license_record.is_active THEN
    result := json_build_object(
      'valid', false,
      'reason', 'inactive_license',
      'error_code', 'LICENSE_INACTIVE',
      'license_id', license_record.id,
      'user_id', license_record.user_id,
      'license_type', license_record.license_type
    );
    RETURN result;
  END IF;
  
  -- Check if license is free (free licenses don't expire)
  is_free := LOWER(license_record.license_type) = 'free';
  
  -- Check if license is expired (only for non-free licenses)
  IF NOT is_free AND license_record.expiry_date < now() THEN
    -- Update license to inactive
    UPDATE public.licenses
    SET is_active = false
    WHERE id = license_record.id;
    
    result := json_build_object(
      'valid', false,
      'reason', 'expired',
      'error_code', 'LICENSE_EXPIRED',
      'license_id', license_record.id,
      'user_id', license_record.user_id,
      'license_type', license_record.license_type,
      'expiry_date', license_record.expiry_date,
      'api_key', license_record.api_key
    );
    RETURN result;
  END IF;
  
  -- License is valid - return comprehensive data for caching
  result := json_build_object(
    'valid', true,
    'license_id', license_record.id,
    'user_id', license_record.user_id,
    'license_type', license_record.license_type,
    'is_active', license_record.is_active,
    'expiry_date', license_record.expiry_date,
    'created_at', license_record.created_at,
    'updated_at', license_record.updated_at,
    'is_free', is_free,
    'api_key', license_record.api_key
  );
  
  RETURN result;
END;
$$;

-- Function to check license status without requiring the API key
-- This allows checking license status by user_id which is safer
CREATE OR REPLACE FUNCTION check_license_status(p_user_id UUID)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  license_record RECORD;
  result JSON;
  is_free BOOLEAN;
BEGIN
  -- Get the user's most recent active license
  SELECT * INTO license_record
  FROM public.licenses
  WHERE user_id = p_user_id
  AND is_active = true
  ORDER BY 
    CASE WHEN LOWER(license_type) = 'pro' THEN 1
         WHEN LOWER(license_type) = 'trial' THEN 2
         ELSE 3
    END,
    created_at DESC
  LIMIT 1;
  
  -- If no license found, return no_license
  IF license_record IS NULL THEN
    result := json_build_object(
      'has_license', false,
      'reason', 'no_license',
      'error_code', 'NO_LICENSE_FOUND'
    );
    RETURN result;
  END IF;
  
  -- Check if license is free (free licenses don't expire)
  is_free := LOWER(license_record.license_type) = 'free';
  
  -- Check if license is expired (only for non-free licenses)
  IF NOT is_free AND license_record.expiry_date < now() THEN
    -- Update license to inactive
    UPDATE public.licenses
    SET is_active = false
    WHERE id = license_record.id;
    
    result := json_build_object(
      'has_license', false,
      'reason', 'expired',
      'error_code', 'LICENSE_EXPIRED',
      'license_id', license_record.id,
      'license_type', license_record.license_type,
      'expiry_date', license_record.expiry_date
    );
    RETURN result;
  END IF;
  
  -- License is valid
  result := json_build_object(
    'has_license', true,
    'license_id', license_record.id,
    'license_type', license_record.license_type,
    'is_active', license_record.is_active,
    'expiry_date', license_record.expiry_date,
    'created_at', license_record.created_at,
    'updated_at', license_record.updated_at,
    'is_free', is_free,
    'api_key', license_record.api_key,
    'days_remaining', 
      CASE 
        WHEN is_free THEN 36500
        ELSE GREATEST(0, EXTRACT(DAY FROM (license_record.expiry_date - now())))::integer
      END
  );
  
  RETURN result;
END;
$$;

-- Grant execute permissions on these functions
DO $$
BEGIN
  EXECUTE 'GRANT EXECUTE ON FUNCTION public.verify_api_key_enhanced(TEXT) TO anon, authenticated, service_role';
  EXECUTE 'GRANT EXECUTE ON FUNCTION public.get_or_create_license_enhanced(UUID, TEXT, INT) TO authenticated, service_role';
  EXECUTE 'GRANT EXECUTE ON FUNCTION public.check_license_status(UUID) TO authenticated, service_role';
END $$;
