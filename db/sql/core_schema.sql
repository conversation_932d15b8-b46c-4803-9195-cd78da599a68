-- Enable UUID extension and create uuid_generate_v7 function
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
-- Create a simpler uuid_generate_v7 function
CREATE OR REPLACE FUNCTION uuid_generate_v7() RETURNS uuid AS $$
DECLARE v_time bigint;
v_timestamp timestamp with time zone;
v_random uuid;
BEGIN -- Get current timestamp
v_timestamp := clock_timestamp();
-- Convert timestamp to microseconds since epoch
v_time := (
    EXTRACT(
        EPOCH
        FROM v_timestamp
    ) * 1000000 + EXTRACT(
        MICROSECONDS
        FROM v_timestamp
    )
)::bigint;
-- Get a random UUID for the remaining bits
v_random := uuid_generate_v4();
-- Combine timestamp and random bits using concatenation
RETURN encode(
    int8send(v_time) || -- First 8 bytes for timestamp
    substring(
        v_random::text::bytea
        from 9 for 8
    ),
    -- Last 8 bytes from random UUID
    'hex'
)::uuid;
END;
$$ LANGUAGE plpgsql VOLATILE;
-- Create schema_version table for tracking migrations
CREATE TABLE IF NOT EXISTS schema_version (
    version INTEGER PRIMARY KEY,
    description TEXT NOT NULL,
    applied_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
-- Insert current version (updating from version 5 to 7)
INSERT INTO schema_version (version, description)
VALUES (
        8,
        'Add a table for S&P 500 Tickers.'
    ) ON CONFLICT (version) DO UPDATE 
    SET description = 'Add a table for S&P 500 Tickers.';
-- Create tables
CREATE TABLE IF NOT EXISTS categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v7(),
    title TEXT NOT NULL,
    subtitle TEXT,
    icon TEXT,
    keywords JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
CREATE TABLE IF NOT EXISTS prompts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v7(),
    title TEXT NOT NULL,
    category_id UUID REFERENCES categories(id),
    subtitle TEXT NOT NULL,
    source TEXT NOT NULL CHECK (source IN ('local', 'cloud')),
    keywords JSONB DEFAULT '[]',
    metadata JSONB DEFAULT '{}',
    variables JSONB DEFAULT '[]',
    version INTEGER DEFAULT 1,
    is_synced BOOLEAN DEFAULT false,
    popularity_score INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_used_at TIMESTAMP WITH TIME ZONE,
    server_sync_at TIMESTAMP WITH TIME ZONE
);
-- Note: tags and prompt_tags tables have been removed
CREATE TABLE IF NOT EXISTS keyword_mappings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v7(),
    category_id UUID REFERENCES categories(id),
    keyword TEXT NOT NULL,
    weight REAL DEFAULT 1.0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
CREATE TABLE IF NOT EXISTS sp500_tickers (
    id SERIAL PRIMARY KEY,
    symbol VARCHAR(10) UNIQUE NOT NULL,
    company_name TEXT NOT NULL,
    sector TEXT,
    industry TEXT,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
CREATE TABLE IF NOT EXISTS prompt_instructions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v7(),
    prompt_id UUID NOT NULL REFERENCES prompts(id) ON DELETE CASCADE,
    instruction_template TEXT,
    parameters JSONB DEFAULT '{}',
    usage_notes TEXT,
    provider TEXT, -- Preferred provider (OpenAI, Google, etc.)
    inherit_from_category BOOLEAN DEFAULT false,
    version INTEGER DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(prompt_id) -- One instruction set per prompt
);

-- Create indices for better performance
CREATE INDEX IF NOT EXISTS idx_prompts_category ON prompts(category_id);
CREATE INDEX IF NOT EXISTS idx_prompts_updated_at ON prompts(updated_at);
CREATE INDEX IF NOT EXISTS idx_prompts_keywords ON prompts USING GIN (keywords jsonb_path_ops);
CREATE INDEX IF NOT EXISTS idx_prompts_variables ON prompts USING GIN (variables jsonb_path_ops);
CREATE INDEX IF NOT EXISTS idx_categories_keywords ON categories USING GIN (keywords jsonb_path_ops);
-- Removed tags indices
-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column() RETURNS TRIGGER AS $$ BEGIN NEW.updated_at = CURRENT_TIMESTAMP;
RETURN NEW;
END;
$$ language 'plpgsql';
-- Create triggers for updated_at
DROP TRIGGER IF EXISTS update_prompts_updated_at ON prompts;
CREATE TRIGGER update_prompts_updated_at BEFORE
UPDATE ON prompts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
DROP TRIGGER IF EXISTS update_categories_updated_at ON categories;
CREATE TRIGGER update_categories_updated_at BEFORE
UPDATE ON categories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
DROP TRIGGER IF EXISTS update_prompt_instructions_updated_at ON prompt_instructions;
CREATE TRIGGER update_prompt_instructions_updated_at BEFORE
UPDATE ON prompt_instructions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
-- Add comments for documentation on the parameters columns
COMMENT ON COLUMN prompt_instructions.parameters IS 
'JSON object with LLM parameters like max_tokens, temperature, etc. that override category defaults';
-- Add comment for the variables field in prompts
COMMENT ON COLUMN prompts.variables IS
'JSON object with template variables used in the prompt title or text';