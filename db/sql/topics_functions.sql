-- Enable UUID generation
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create topics table if it doesn't exist
CREATE TABLE IF NOT EXISTS topics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v7(),
    user_id UUID NOT NULL REFERENCES auth.users(id),
    name TEXT NOT NULL,
    description TEXT,
    storage_type TEXT NOT NULL CHECK (storage_type IN ('local', 'google_drive')),
    drive_root_path TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    version INTEGER NOT NULL DEFAULT 1
);

-- Create a partial unique index for active topics
CREATE UNIQUE INDEX idx_topics_active_name ON topics (user_id, name) WHERE deleted_at IS NULL;

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_topics_user_id ON topics(user_id);
CREATE INDEX IF NOT EXISTS idx_topics_deleted_at ON topics(deleted_at);
CREATE INDEX IF NOT EXISTS idx_topics_updated_at ON topics(updated_at);

-- Function to create a new topic
CREATE OR REPLACE FUNCTION create_topic(
    p_user_id UUID,
    p_name TEXT,
    p_description TEXT,
    p_storage_type TEXT,
    p_drive_root_path TEXT DEFAULT 'promz/'
) RETURNS TABLE (
    id UUID,
    user_id UUID,
    name TEXT,
    description TEXT,
    storage_type TEXT,
    drive_root_path TEXT,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    deleted_at TIMESTAMPTZ,
    version INTEGER
) LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
    RETURN QUERY
    INSERT INTO topics (
        user_id,
        name,
        description,
        storage_type,
        drive_root_path
    )
    VALUES (
        p_user_id,
        p_name,
        p_description,
        p_storage_type,
        CASE WHEN p_storage_type = 'google_drive' THEN p_drive_root_path ELSE NULL END
    )
    RETURNING *;
END;
$$;

-- Function to get topics for a user
CREATE OR REPLACE FUNCTION get_topics(
    p_user_id UUID
)
RETURNS TABLE (
    id UUID,
    user_id UUID,
    name TEXT,
    description TEXT,
    storage_type TEXT,
    drive_root_path TEXT,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    deleted_at TIMESTAMPTZ,
    version INTEGER
) LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
    RETURN QUERY
    SELECT *
    FROM topics t
    WHERE t.user_id = p_user_id
    AND t.deleted_at IS NULL
    ORDER BY t.created_at DESC;
END;
$$;

-- Function to check if a topic exists for a user
CREATE OR REPLACE FUNCTION topic_exists(
    p_user_id UUID,
    p_name TEXT
) RETURNS BOOLEAN LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 
        FROM topics 
        WHERE user_id = p_user_id 
        AND name = p_name 
        AND deleted_at IS NULL
    );
END;
$$;

-- Function to soft delete a topic
CREATE OR REPLACE FUNCTION delete_topic(
    p_user_id UUID,
    p_topic_id UUID
) RETURNS BOOLEAN LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
    UPDATE topics
    SET deleted_at = NOW(),
        version = version + 1
    WHERE id = p_topic_id
    AND user_id = p_user_id
    AND deleted_at IS NULL;
    
    RETURN FOUND;
END;
$$;

-- Revoke execute permissions from public
REVOKE EXECUTE ON FUNCTION create_topic(UUID, TEXT, TEXT, TEXT, TEXT) FROM PUBLIC;
REVOKE EXECUTE ON FUNCTION get_topics(UUID) FROM PUBLIC;
REVOKE EXECUTE ON FUNCTION topic_exists(UUID, TEXT) FROM PUBLIC;
REVOKE EXECUTE ON FUNCTION delete_topic(UUID, UUID) FROM PUBLIC;

-- Grant execute permissions to the authenticator role
GRANT EXECUTE ON FUNCTION create_topic(UUID, TEXT, TEXT, TEXT, TEXT) TO authenticator;
GRANT EXECUTE ON FUNCTION get_topics(UUID) TO authenticator;
GRANT EXECUTE ON FUNCTION topic_exists(UUID, TEXT) TO authenticator;
GRANT EXECUTE ON FUNCTION delete_topic(UUID, UUID) TO authenticator;
