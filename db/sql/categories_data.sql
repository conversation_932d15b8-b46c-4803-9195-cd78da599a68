-- Categories seed data for Promz application
-- Run this after schema.sql to populate the database
BEGIN;

-- Insert all categories
INSERT INTO categories (id, title, subtitle, keywords)
VALUES (
    '550e8400-e29b-41d4-a716-446655440000',
    'Summarize',
    'Generate concise summaries of text or topics',
    '["summary", "summarize", "brief", "concise", "overview"]'::jsonb
) ON CONFLICT (id) DO
UPDATE
SET title = excluded.title,
subtitle = excluded.subtitle,
keywords = excluded.keywords;

INSERT INTO categories (id, title, subtitle, keywords)
VALUES (
    '550e8400-e29b-41d4-a716-446655440002',
    'Stock Insights',
    'Popular stock-related topics for analysis and discussion',
    '["stock", "insights", "analysis", "discussion", "popular"]'::jsonb
) ON CONFLICT (id) DO
UPDATE
SET title = excluded.title,
subtitle = excluded.subtitle,
keywords = excluded.keywords;

COMMIT;
