-- This file contains SQL functions related to LLM model pricing and tier-based access

-- Function: get_eligible_models_for_tier
-- Returns all models available for a specific user tier, sorted by priority
CREATE OR REPLACE FUNCTION public.get_eligible_models_for_tier(
    p_tier_id TEXT,
    p_preferred_model TEXT DEFAULT NULL
)
RETURNS TABLE (
    provider_id TEXT,
    model_id TEXT,
    display_name TEXT,
    description TEXT,
    is_free BOOLEAN,
    cost_input_per_million_tokens FLOAT8,
    cost_output_per_million_tokens FLOAT8,
    capabilities JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        lm.provider_id,
        lm.model_id,
        lm.display_name,
        lm.description,
        lm.is_free,
        lm.cost_input_per_million_tokens,
        lm.cost_output_per_million_tokens,
        lm.capabilities
    FROM 
        available_models_by_tier amt
    JOIN 
        llm_models lm ON amt.provider_id = lm.provider_id AND amt.model_id = lm.model_id
    WHERE 
        amt.tier_id = p_tier_id
    ORDER BY 
        -- Preferred model gets highest priority if it exists
        CASE WHEN lm.model_id = p_preferred_model THEN 0 ELSE 1 END,
        -- Group by provider to keep models from same provider together
        lm.provider_id,
        -- For free users, prioritize free models
        CASE WHEN p_tier_id = 'free' THEN 
            CASE WHEN lm.is_free THEN 0 ELSE 1 END
        ELSE 0 END,
        -- Sort by total cost (cheaper models first)
        (lm.cost_input_per_million_tokens + lm.cost_output_per_million_tokens) ASC,
        -- Use model_id as a tiebreaker for models with identical costs
        lm.model_id DESC;
END;
$$ LANGUAGE plpgsql;

-- Function: get_best_model_for_provider
-- Returns the best model for a specific provider and tier, optionally preferring a specific model
CREATE OR REPLACE FUNCTION public.get_best_model_for_provider(
    p_tier_id TEXT,
    p_provider_id TEXT,
    p_preferred_model TEXT DEFAULT NULL
)
RETURNS TABLE (
    provider_id TEXT,
    model_id TEXT,
    display_name TEXT,
    description TEXT,
    is_free BOOLEAN,
    cost_input_per_million_tokens FLOAT8,
    cost_output_per_million_tokens FLOAT8,
    capabilities JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        lm.provider_id,
        lm.model_id,
        lm.display_name,
        lm.description,
        lm.is_free,
        lm.cost_input_per_million_tokens,
        lm.cost_output_per_million_tokens,
        lm.capabilities
    FROM 
        available_models_by_tier amt
    JOIN 
        llm_models lm ON amt.provider_id = lm.provider_id AND amt.model_id = lm.model_id
    WHERE 
        amt.tier_id = p_tier_id AND
        lm.provider_id = p_provider_id
    ORDER BY 
        -- Preferred model gets highest priority if it exists
        CASE WHEN lm.model_id = p_preferred_model THEN 0 ELSE 1 END,
        -- For free users, prioritize free models
        CASE WHEN p_tier_id = 'free' THEN 
            CASE WHEN lm.is_free THEN 0 ELSE 1 END
        ELSE 0 END,
        -- Sort by total cost (cheaper models first)
        (lm.cost_input_per_million_tokens + lm.cost_output_per_million_tokens) ASC,
        -- Use model_id as a tiebreaker for models with identical costs
        lm.model_id DESC
    LIMIT 1;
END;
$$ LANGUAGE plpgsql;

-- Function to calculate token usage cost
CREATE OR REPLACE FUNCTION public.calculate_token_usage_cost(
    p_provider_id TEXT,
    p_model_id TEXT,
    p_input_tokens INTEGER,
    p_output_tokens INTEGER
)
RETURNS FLOAT8 AS $$
DECLARE
    v_input_cost FLOAT8;
    v_output_cost FLOAT8;
    v_total_cost FLOAT8;
BEGIN
    -- Get the cost per million tokens for this model
    SELECT 
        cost_input_per_million_tokens,
        cost_output_per_million_tokens
    INTO 
        v_input_cost,
        v_output_cost
    FROM
        llm_models
    WHERE
        provider_id = p_provider_id AND
        model_id = p_model_id;
        
    -- Calculate the cost
    v_total_cost := (v_input_cost * p_input_tokens / 1000000) + 
                   (v_output_cost * p_output_tokens / 1000000);
                   
    RETURN v_total_cost;
END;
$$ LANGUAGE plpgsql;
