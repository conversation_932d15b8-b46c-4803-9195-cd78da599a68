-- Insert initial model data and permissions

-- Insert default user tiers if they don't exist
INSERT INTO public.user_tiers (tier_id, tier_name, tier_description, monthly_token_limit) 
VALUES 
    ('free', 'Free', 'Basic access with limited models and monthly token limit', 500000),
    ('trial', 'Pro Trial', 'Trial access to pro features with increased token limit', 1000000),
    ('pro', 'Pro', 'Full access to all models with high token limit', 5000000)
ON CONFLICT (tier_id) DO NOTHING;

-- Add some initial model data
INSERT INTO public.llm_models (
    provider_id,
    model_id,
    display_name,
    description,
    is_free,
    cost_input_per_million_tokens,
    cost_output_per_million_tokens,
    max_tokens,
    context_window,
    capabilities
) VALUES
-- Google models (add gemini alias for backward compatibility)
('gemini', 'gemini-1.5-flash', 'Gemini 1.5 Flash', 'Fast and efficient model for general text generation tasks', 
 true, 0.00, 0.00, 2048, 16384, '["text-generation", "chat"]'),

('gemini', 'gemini-1.5-pro', 'Gemini 1.5 Pro', 'Advanced model for complex reasoning and generation tasks', 
 false, 7.00, 21.00, 8192, 32768, '["text-generation", "chat", "reasoning", "code"]'),

('google', 'gemini-1.5-flash', 'Gemini 1.5 Flash', 'Fast and efficient model for general text generation tasks', 
 true, 0.00, 0.00, 2048, 16384, '["text-generation", "chat"]'),

('google', 'gemini-1.5-pro', 'Gemini 1.5 Pro', 'Advanced model for complex reasoning and generation tasks', 
 false, 7.00, 21.00, 8192, 32768, '["text-generation", "chat", "reasoning", "code"]'),

-- OpenAI models
('openai', 'gpt-3.5-turbo', 'GPT-3.5 Turbo', 'Fast and efficient text model for general tasks', 
 true, 0.50, 1.50, 4096, 16384, '["text-generation", "chat"]'),

('openai', 'gpt-4', 'GPT-4', 'Advanced reasoning, complex tasks, and detailed explanations', 
 false, 10.00, 30.00, 8192, 32768, '["text-generation", "chat", "reasoning", "code"]'),

-- Anthropic models
('anthropic', 'claude-haiku', 'Claude Haiku', 'Fast and efficient text model for simple tasks', 
 true, 0.00, 0.00, 4096, 12288, '["text-generation", "chat"]'),

('anthropic', 'claude-opus', 'Claude Opus', 'Most powerful Claude model for complex tasks', 
 false, 15.00, 75.00, 4096, 32768, '["text-generation", "chat", "reasoning", "code"]'),

-- Mistral models
('mistral', 'mistral-small', 'Mistral Small', 'Efficient model for general tasks', 
 true, 0.00, 0.00, 4096, 8192, '["text-generation", "chat"]'),

('mistral', 'mistral-large', 'Mistral Large', 'Advanced model with strong reasoning capabilities', 
 false, 8.00, 24.00, 4096, 32768, '["text-generation", "chat", "reasoning", "code"]')

ON CONFLICT (provider_id, model_id) DO UPDATE 
SET 
    display_name = EXCLUDED.display_name,
    description = EXCLUDED.description,
    is_free = EXCLUDED.is_free,
    cost_input_per_million_tokens = EXCLUDED.cost_input_per_million_tokens,
    cost_output_per_million_tokens = EXCLUDED.cost_output_per_million_tokens,
    max_tokens = EXCLUDED.max_tokens,
    context_window = EXCLUDED.context_window,
    capabilities = EXCLUDED.capabilities,
    updated_at = now();

-- Add permissions for different user tiers
INSERT INTO public.user_model_permissions (
    tier_id,
    provider_id,
    model_id,
    is_allowed,
    quota_tokens
) VALUES
-- Free tier permissions for Google/Gemini
('free', 'gemini', 'gemini-1.5-flash', true, 500000),
('free', 'gemini', 'gemini-1.5-pro', false, null),
('free', 'google', 'gemini-1.5-flash', true, 500000),
('free', 'google', 'gemini-1.5-pro', false, null),

-- Free tier permissions for OpenAI
('free', 'openai', 'gpt-3.5-turbo', true, 500000),
('free', 'openai', 'gpt-4', false, null),

-- Free tier permissions for others
('free', 'anthropic', 'claude-haiku', true, 500000),
('free', 'anthropic', 'claude-opus', false, null),
('free', 'mistral', 'mistral-small', true, 500000),
('free', 'mistral', 'mistral-large', false, null),

-- Trial tier permissions for Google/Gemini
('trial', 'gemini', 'gemini-1.5-flash', true, 1000000),
('trial', 'gemini', 'gemini-1.5-pro', true, 200000),
('trial', 'google', 'gemini-1.5-flash', true, 1000000),
('trial', 'google', 'gemini-1.5-pro', true, 200000),

-- Trial tier permissions for OpenAI
('trial', 'openai', 'gpt-3.5-turbo', true, 1000000),
('trial', 'openai', 'gpt-4', true, 100000),

-- Trial tier permissions for others
('trial', 'anthropic', 'claude-haiku', true, 1000000),
('trial', 'anthropic', 'claude-opus', true, 100000),
('trial', 'mistral', 'mistral-small', true, 1000000),
('trial', 'mistral', 'mistral-large', true, 200000),

-- Pro tier permissions for Google/Gemini
('pro', 'gemini', 'gemini-1.5-flash', true, null),
('pro', 'gemini', 'gemini-1.5-pro', true, null),
('pro', 'google', 'gemini-1.5-flash', true, null),
('pro', 'google', 'gemini-1.5-pro', true, null),

-- Pro tier permissions for OpenAI
('pro', 'openai', 'gpt-3.5-turbo', true, null),
('pro', 'openai', 'gpt-4', true, null),

-- Pro tier permissions for others
('pro', 'anthropic', 'claude-haiku', true, null),
('pro', 'anthropic', 'claude-opus', true, null),
('pro', 'mistral', 'mistral-small', true, null),
('pro', 'mistral', 'mistral-large', true, null)

ON CONFLICT (tier_id, provider_id, model_id) DO UPDATE 
SET 
    is_allowed = EXCLUDED.is_allowed,
    quota_tokens = EXCLUDED.quota_tokens,
    updated_at = now();
