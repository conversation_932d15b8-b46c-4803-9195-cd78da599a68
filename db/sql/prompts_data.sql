INSERT INTO public.prompts (
    id,
    category_id,
    title,
    subtitle,
    source,
    keywords,
    variables,
    version,
    is_synced,
    created_at,
    updated_at,
    server_sync_at,
    last_used_at,
    metadata
)
VALUES
(
    '00062ed4-bbd1-769c-2d66-6237342d3465',
    '550e8400-e29b-41d4-a716-446655440000',
    'Summarize this article',
    'Provide a brief summary of the following article.',
    'cloud',
    '["summary","article","brief"]',
    '["NEWS:ARTICLE_URL"]',
    '1',
    'false',
    '2025-02-23 20:04:19.592821+00',
    '2025-03-25 02:31:21.434769+00',
    null,
    null,
    null
)
ON CONFLICT (id) DO UPDATE
SET
title = excluded.title,
subtitle = excluded.subtitle,
source = excluded.source,
keywords = excluded.keywords,
variables = excluded.variables,
version = excluded.version,
is_synced = excluded.is_synced,
updated_at = excluded.updated_at,
server_sync_at = excluded.server_sync_at,
last_used_at = excluded.last_used_at,
metadata = excluded.metadata;

INSERT INTO public.prompts (
    id,
    category_id,
    title,
    subtitle,
    source,
    keywords,
    variables,
    version,
    is_synced,
    created_at,
    updated_at,
    server_sync_at,
    last_used_at,
    metadata
)
VALUES
(
    '00062ed4-bbd2-0258-2d61-3133392d3435',
    '550e8400-e29b-41d4-a716-446655440000',
    'Summarize the meeting notes',
    'Summarize the key points from this meeting''s notes.',
    'cloud',
    '["summary","meeting","notes","key points"]',
    '[]',
    '1',
    'false',
    '2025-02-23 20:04:19.592821+00',
    '2025-03-24 17:28:02.086894+00',
    null,
    null,
    null
)
ON CONFLICT (id) DO UPDATE
SET
title = excluded.title,
subtitle = excluded.subtitle,
source = excluded.source,
keywords = excluded.keywords,
variables = excluded.variables,
version = excluded.version,
is_synced = excluded.is_synced,
updated_at = excluded.updated_at,
server_sync_at = excluded.server_sync_at,
last_used_at = excluded.last_used_at,
metadata = excluded.metadata;

INSERT INTO public.prompts (
    id,
    category_id,
    title,
    subtitle,
    source,
    keywords,
    variables,
    version,
    is_synced,
    created_at,
    updated_at,
    server_sync_at,
    last_used_at,
    metadata
)
VALUES
(
    '00062ed4-bbd2-02ae-2d38-3862382d3431',
    '550e8400-e29b-41d4-a716-446655440000',
    'Summarize the product review',
    'Summarize this product review in a few sentences.',
    'cloud',
    '["summary","product","review"]',
    '[]',
    '1',
    'false',
    '2025-02-23 20:04:19.592821+00',
    '2025-03-24 17:28:02.086894+00',
    null,
    null,
    null
)
ON CONFLICT (id) DO UPDATE
SET
title = excluded.title,
subtitle = excluded.subtitle,
source = excluded.source,
keywords = excluded.keywords,
variables = excluded.variables,
version = excluded.version,
is_synced = excluded.is_synced,
updated_at = excluded.updated_at,
server_sync_at = excluded.server_sync_at,
last_used_at = excluded.last_used_at,
metadata = excluded.metadata;

INSERT INTO public.prompts (
    id,
    category_id,
    title,
    subtitle,
    source,
    keywords,
    variables,
    version,
    is_synced,
    created_at,
    updated_at,
    server_sync_at,
    last_used_at,
    metadata
)
VALUES
(
    '00062ed4-bbd2-11de-2d34-6566662d3439',
    '550e8400-e29b-41d4-a716-446655440002',
    'Analyze stock performance',
    'Provide an assessment of stock performance.',
    'cloud',
    '["stock","analysis","performance"]',
    '["FINANCE:TICKER"]',
    '1',
    'false',
    '2025-02-23 20:04:19.592821+00',
    '2025-03-24 18:23:29.475386+00',
    null,
    null,
    null
)
ON CONFLICT (id) DO UPDATE
SET
title = excluded.title,
subtitle = excluded.subtitle,
source = excluded.source,
keywords = excluded.keywords,
variables = excluded.variables,
version = excluded.version,
is_synced = excluded.is_synced,
updated_at = excluded.updated_at,
server_sync_at = excluded.server_sync_at,
last_used_at = excluded.last_used_at,
metadata = excluded.metadata;

INSERT INTO public.prompts (
    id,
    category_id,
    title,
    subtitle,
    source,
    keywords,
    variables,
    version,
    is_synced,
    created_at,
    updated_at,
    server_sync_at,
    last_used_at,
    metadata
)
VALUES
(
    '00062ed4-bbd2-123c-2d32-3064662d3464',
    '550e8400-e29b-41d4-a716-446655440002',
    'Stock market predictions',
    'Predict the stock market trends for the next month based on recent data.',
    'cloud',
    '["stock","market","predictions","trends"]',
    '[]',
    '1',
    'false',
    '2025-02-23 20:04:19.592821+00',
    '2025-03-24 17:28:02.086894+00',
    null,
    null,
    null
)
ON CONFLICT (id) DO UPDATE
SET
title = excluded.title,
subtitle = excluded.subtitle,
source = excluded.source,
keywords = excluded.keywords,
variables = excluded.variables,
version = excluded.version,
is_synced = excluded.is_synced,
updated_at = excluded.updated_at,
server_sync_at = excluded.server_sync_at,
last_used_at = excluded.last_used_at,
metadata = excluded.metadata;

INSERT INTO public.prompts (
    id,
    category_id,
    title,
    subtitle,
    source,
    keywords,
    variables,
    version,
    is_synced,
    created_at,
    updated_at,
    server_sync_at,
    last_used_at,
    metadata
)
VALUES
(
    '00062ed4-bbd2-1264-2d36-3139342d3430',
    '550e8400-e29b-41d4-a716-446655440002',
    'Compare Apple vs Microsoft stock',
    'Compare the stock performance of Apple and Microsoft in the past year.',
    'cloud',
    '["stock","apple","microsoft","comparison"]',
    '[]',
    '1',
    'false',
    '2025-02-23 20:04:19.592821+00',
    '2025-03-24 17:28:02.086894+00',
    null,
    null,
    null
)
ON CONFLICT (id) DO UPDATE
SET
title = excluded.title,
subtitle = excluded.subtitle,
source = excluded.source,
keywords = excluded.keywords,
variables = excluded.variables,
version = excluded.version,
is_synced = excluded.is_synced,
updated_at = excluded.updated_at,
server_sync_at = excluded.server_sync_at,
last_used_at = excluded.last_used_at,
metadata = excluded.metadata;

INSERT INTO public.prompts (
    id,
    category_id,
    title,
    subtitle,
    source,
    keywords,
    variables,
    version,
    is_synced,
    created_at,
    updated_at,
    server_sync_at,
    last_used_at,
    metadata
)
VALUES
(
    '00062ed4-bbd3-5678-2d36-3139342d9876',
    '550e8400-e29b-41d4-a716-446655440000',
    'Summarize this YouTube video',
    'Create a smart, bulleted summary of the key points from this YouTube video.',
    'cloud',
    '["summary","youtube","video","key points"]',
    '["MEDIA:YOUTUBE_URL"]',
    '1',
    'false',
    '2025-02-23 20:04:19.592821+00',
    '2025-03-24 17:28:02.086894+00',
    null,
    null,
    null
)
ON CONFLICT (id) DO UPDATE
SET
title = excluded.title,
subtitle = excluded.subtitle,
source = excluded.source,
keywords = excluded.keywords,
variables = excluded.variables,
version = excluded.version,
is_synced = excluded.is_synced,
updated_at = excluded.updated_at,
server_sync_at = excluded.server_sync_at,
last_used_at = excluded.last_used_at,
metadata = excluded.metadata;
