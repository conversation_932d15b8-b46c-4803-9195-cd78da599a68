-- Create tables for LLM models, user tiers, and tier-model access

-- Create llm_models table to store model information including pricing
CREATE TABLE IF NOT EXISTS public.llm_models (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v7(),
    provider_id TEXT NOT NULL,
    model_id TEXT NOT NULL,
    display_name TEXT,
    description TEXT,
    is_free BOOLEAN NOT NULL DEFAULT false,
    cost_input_per_million_tokens FLOAT8 NOT NULL DEFAULT 0,
    cost_output_per_million_tokens FLOAT8 NOT NULL DEFAULT 0,
    max_tokens INTEGER,
    context_window INTEGER,
    capabilities JSONB,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    last_pricing_update TIMESTAMPTZ NOT NULL DEFAULT now(),
    UNIQUE(provider_id, model_id)
);

-- Table: user_tiers
-- Contains information about user subscription tiers
CREATE TABLE IF NOT EXISTS public.user_tiers (
    tier_id TEXT PRIMARY KEY, -- Tier identifier (e.g., "free", "pro", "enterprise")
    tier_name TEXT NOT NULL,  -- User-friendly tier name
    tier_description TEXT,    -- Description of the tier
    monthly_token_limit BIGINT, -- Monthly token limit (NULL for unlimited)
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

-- Create user_model_permissions table to map user tiers to allowed models
CREATE TABLE IF NOT EXISTS public.user_model_permissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v7(),
    tier_id TEXT NOT NULL REFERENCES user_tiers(tier_id) ON DELETE CASCADE,
    provider_id TEXT NOT NULL,
    model_id TEXT NOT NULL,
    is_allowed BOOLEAN NOT NULL DEFAULT false,
    quota_tokens BIGINT, -- Changed to BIGINT for consistency with user_tiers
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    UNIQUE(tier_id, provider_id, model_id),
    FOREIGN KEY (provider_id, model_id) REFERENCES llm_models(provider_id, model_id) ON DELETE CASCADE
);

-- Drop the table definition since we're replacing it with a view
DROP VIEW IF EXISTS public.available_models_by_tier;

-- Create a view for easy model selection based on tier
CREATE OR REPLACE VIEW public.available_models_by_tier AS
SELECT
    ump.tier_id,
    lm.provider_id,
    lm.model_id,
    lm.display_name,
    lm.description,
    lm.is_free,
    lm.cost_input_per_million_tokens,
    lm.cost_output_per_million_tokens,
    lm.max_tokens,
    lm.context_window,
    lm.capabilities,
    ump.is_allowed,
    ump.quota_tokens
FROM
    user_model_permissions ump
JOIN
    llm_models lm ON ump.provider_id = lm.provider_id AND ump.model_id = lm.model_id
WHERE
    ump.is_allowed = true
ORDER BY
    ump.tier_id, lm.is_free DESC, lm.provider_id, lm.model_id;
