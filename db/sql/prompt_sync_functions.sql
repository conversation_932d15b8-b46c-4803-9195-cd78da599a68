-- Create a function to get paginated prompts
CREATE OR REPLACE FUNCTION get_prompts_paginated(p_limit integer, p_offset integer)
RETURNS SETOF prompts
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT *
  FROM prompts
  OR<PERSON>R BY created_at DESC
  LIMIT p_limit
  OFFSET p_offset;
END;
$$;

-- <PERSON> execute permission on the function to authenticated users
GRANT EXECUTE ON FUNCTION get_prompts_paginated(integer, integer) TO authenticated;
