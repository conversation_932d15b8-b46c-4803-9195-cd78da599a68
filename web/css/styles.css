/* 
  Promz Static Website Styles
  Main stylesheet for the Promz marketing website
*/

/* Base Variables */
:root {
  --primary-color: #4a6cf7;
  --primary-dark: #3959d9;
  --secondary-color: #6c757d;
  --success-color: #28a745;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  --info-color: #17a2b8;
  --light-color: #f8f9fa;
  --dark-color: #212529;
  --body-color: #f9fbfd;
  --body-text: #4d5d77;
  --heading-color: #262d3f;
  --border-color: #e5eaef;
  
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  --font-heading: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  
  --spacing-xs: 0.5rem;
  --spacing-sm: 1rem;
  --spacing-md: 1.5rem;
  --spacing-lg: 2rem;
  --spacing-xl: 3rem;
  
  --border-radius: 0.375rem;
  --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Reset & Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-primary);
  font-size: 16px;
  line-height: 1.6;
  color: var(--body-text);
  background-color: var(--body-color);
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-heading);
  color: var(--heading-color);
  margin-bottom: var(--spacing-sm);
  font-weight: 700;
  line-height: 1.3;
}

h1 {
  font-size: 2.5rem;
}

h2 {
  font-size: 2rem;
}

h3 {
  font-size: 1.5rem;
}

p {
  margin-bottom: var(--spacing-md);
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color 0.3s ease;
}

a:hover {
  color: var(--primary-dark);
}

ul, ol {
  margin-bottom: var(--spacing-md);
  padding-left: var(--spacing-lg);
}

img {
  max-width: 100%;
  height: auto;
}

/* Container */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

/* Header */
header {
  background-color: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: var(--spacing-sm) 0;
  position: sticky;
  top: 0;
  z-index: 100;
}

header .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo img {
  height: 40px;
}

nav ul {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
}

nav ul li {
  margin-left: var(--spacing-md);
}

nav ul li a {
  color: var(--dark-color);
  font-weight: 500;
  padding: 0.5rem;
  transition: color 0.3s ease;
}

nav ul li a:hover, 
nav ul li a.active {
  color: var(--primary-color);
}

/* Hero Section */
.hero {
  padding: var(--spacing-xl) 0;
  background-color: var(--primary-color);
  color: white;
  text-align: center;
}

.hero h1, .hero h2 {
  color: white;
}

.hero h1 {
  font-size: 3rem;
  margin-bottom: var(--spacing-xs);
}

.hero h2 {
  font-size: 1.5rem;
  font-weight: 400;
  margin-bottom: var(--spacing-md);
}

.hero p {
  font-size: 1.25rem;
  margin-bottom: var(--spacing-lg);
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.cta-buttons {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
}

.btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius);
  font-weight: 600;
  text-align: center;
  transition: all 0.3s ease;
}

.btn.primary {
  background-color: white;
  color: var(--primary-color);
}

.btn.primary:hover {
  background-color: var(--light-color);
  transform: translateY(-2px);
}

.btn.secondary {
  background-color: transparent;
  color: white;
  border: 2px solid white;
}

.btn.secondary:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

/* Features Section */
.features {
  padding: var(--spacing-xl) 0;
  background-color: white;
}

.features h2 {
  text-align: center;
  margin-bottom: var(--spacing-lg);
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
}

.feature-card {
  background-color: var(--light-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--box-shadow);
}

.feature-icon {
  font-size: 2.5rem;
  margin-bottom: var(--spacing-sm);
}

/* Download Section */
.download {
  padding: var(--spacing-xl) 0;
  background-color: var(--light-color);
  text-align: center;
}

.download h2 {
  margin-bottom: var(--spacing-xs);
}

.download p {
  margin-bottom: var(--spacing-lg);
}

.download-options {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: var(--spacing-md);
}

.download-button {
  display: flex;
  align-items: center;
  background-color: white;
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  min-width: 250px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.download-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.platform-icon {
  font-size: 2rem;
  margin-right: var(--spacing-sm);
}

.platform-info {
  text-align: left;
}

.platform-info h3 {
  margin-bottom: 0.25rem;
}

.platform-info p {
  margin-bottom: 0;
  color: var(--secondary-color);
  font-size: 0.875rem;
}

/* Page Header */
.page-header {
  background-color: var(--primary-color);
  color: white;
  padding: var(--spacing-lg) 0;
  text-align: center;
}

.page-header h1 {
  color: white;
  margin-bottom: 0.5rem;
}

.page-header p {
  margin-bottom: 0;
  opacity: 0.9;
}

/* About Content */
.about-content, .legal-content {
  padding: var(--spacing-xl) 0;
  background-color: white;
}

.about-section, .legal-section {
  margin-bottom: var(--spacing-xl);
}

.about-section h2, .legal-section h2 {
  margin-bottom: var(--spacing-md);
  color: var(--primary-color);
}

.about-section ul, .legal-section ul {
  margin-bottom: var(--spacing-md);
}

.legal-section h3 {
  margin-top: var(--spacing-lg);
  margin-bottom: var(--spacing-sm);
}

/* Footer */
footer {
  background-color: var(--dark-color);
  color: white;
  padding: var(--spacing-xl) 0 var(--spacing-lg);
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-xl);
}

.footer-logo img {
  height: 40px;
  margin-bottom: var(--spacing-sm);
}

.footer-logo p {
  opacity: 0.7;
  font-size: 0.875rem;
}

.footer-links h3, .footer-contact h3 {
  color: white;
  margin-bottom: var(--spacing-md);
  font-size: 1.25rem;
}

.footer-links ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links ul li {
  margin-bottom: var(--spacing-xs);
}

.footer-links ul li a {
  color: rgba(255, 255, 255, 0.7);
  transition: color 0.3s ease;
}

.footer-links ul li a:hover {
  color: white;
}

.footer-contact p {
  margin-bottom: var(--spacing-sm);
  color: rgba(255, 255, 255, 0.7);
}

.social-links {
  display: flex;
  gap: var(--spacing-sm);
}

.social-icon {
  color: rgba(255, 255, 255, 0.7);
  transition: color 0.3s ease;
}

.social-icon:hover {
  color: white;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  h1 {
    font-size: 2rem;
  }
  
  h2 {
    font-size: 1.75rem;
  }
  
  .hero h1 {
    font-size: 2.5rem;
  }
  
  .hero h2 {
    font-size: 1.25rem;
  }
  
  .cta-buttons {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
  
  nav ul {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  nav ul li {
    margin: 0 0.5rem;
  }
  
  .download-options {
    flex-direction: column;
    align-items: center;
  }
}

@media (max-width: 576px) {
  header .container {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
  
  .hero h1 {
    font-size: 2rem;
  }
  
  .feature-grid {
    grid-template-columns: 1fr;
  }
}