/* beta.css - Improved for readability and mobile-friendliness */

body {
    font-family: 'Segoe UI', '<PERSON><PERSON>', 'Helvetica Neue', Arial, sans-serif;
    background: #f6f8fa;
    color: #222;
    margin: 0;
    padding: 0;
}

.container {
    max-width: 700px;
    margin: 32px auto 32px auto;
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 2px 16px rgba(0,0,0,0.07);
    padding: 32px 20px 40px 20px;
}

h1 {
    color: #2c3e50;
    font-size: 2rem;
    text-align: center;
    margin-bottom: 32px;
}

ol {
    padding-left: 24px;
}

ol li {
    margin-bottom: 40px;
    background: #f9fafb;
    border-radius: 10px;
    padding: 28px 18px 26px 18px;
    box-shadow: 0 1px 4px rgba(0,0,0,0.04);
    position: relative;
    transition: background 0.2s;
}

ol li:nth-child(odd) {
    background: #f9fafb;
}

ol li:nth-child(even) {
    background: #e8f0fe;
}

ol li:last-child {
    margin-bottom: 0;
}

img.instruction-img {
    display: block;
    margin: 18px auto 0 auto;
    max-width: 350px;
    width: 90vw;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.10);
    cursor: zoom-in;
    transition: box-shadow 0.2s;
}

img.instruction-img:hover {
    box-shadow: 0 4px 16px rgba(0,0,0,0.18);
}

a {
    color: #1976d2;
    text-decoration: underline;
    word-break: break-all;
}

a:visited {
    color: #512da8;
}

.troubleshooting {
    margin-top: 40px;
    background: #e3f2fd;
    padding: 20px 18px;
    border-radius: 10px;
    border-left: 4px solid #1976d2;
    font-size: 1rem;
}

footer {
    margin-top: 40px;
    text-align: center;
    font-size: 0.95em;
    color: #7f8c8d;
    border-top: 1px solid #eee;
    padding-top: 20px;
}

@media (max-width: 600px) {
    .container {
        padding: 12px 2vw 24px 2vw;
        margin: 12px 0 12px 0;
    }
    h1 {
        font-size: 1.3rem;
    }
    ol li {
        padding: 12px 6px 12px 10px;
        font-size: 1rem;
    }
    .troubleshooting {
        padding: 12px 6px;
        font-size: 0.98rem;
    }
}