<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Download PromZ - AI Made Easy</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="icon" href="favicon.ico">
    <style>
        .download-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
            background-color: #f8f9fa;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .download-buttons {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 1.5rem;
            margin: 2rem 0;
        }
        
        .download-button {
            display: flex;
            align-items: center;
            padding: 1rem 2rem;
            background-color: #6200ee;
            color: white;
            border-radius: 8px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.2s ease;
        }
        
        .download-button:hover {
            background-color: #3700b3;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        
        .download-button img {
            margin-right: 10px;
            height: 24px;
        }
        
        .prompt-info {
            margin-top: 2rem;
            padding: 1rem;
            background-color: #e3f2fd;
            border-radius: 8px;
            border-left: 4px solid #2196f3;
        }
        
        @media (max-width: 600px) {
            .download-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">
                <a href="index.html">
                    <img src="images/logo.png" alt="PromZ Logo">
                </a>
            </div>
            <nav>
                <ul>
                    <li><a href="index.html">Home</a></li>
                    <li><a href="about.html">About</a></li>
                    <li><a href="contact.html">Contact</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main>
        <section class="hero">
            <div class="container">
                <h1>Someone shared a prompt with you!</h1>
                <p>Download PromZ to access this prompt and many more.</p>
            </div>
        </section>

        <section>
            <div class="container">
                <div class="download-container">
                    <h2>Download PromZ</h2>
                    <p>PromZ is an AI assistant that helps you create, manage, and share prompts for various AI tasks. Download the app to access the shared prompt and discover many more.</p>
                    
                    <div class="download-buttons">
                        <a href="#" class="download-button" id="ios-download">
                            <img src="images/apple-logo.svg" alt="Apple logo">
                            Download for iOS
                        </a>
                        <a href="#" class="download-button" id="android-download">
                            <img src="images/android-logo.svg" alt="Android logo">
                            Download for Android
                        </a>
                        <a href="#" class="download-button" id="windows-download">
                            <img src="images/windows-logo.svg" alt="Windows logo">
                            Download for Windows
                        </a>
                    </div>
                    
                    <div class="prompt-info" id="prompt-info">
                        <h3>Shared Prompt</h3>
                        <p>Someone shared a prompt with you. Download the app to access it!</p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <img src="images/logo.png" alt="PromZ Logo">
                </div>
                <div class="footer-links">
                    <ul>
                        <li><a href="terms.html">Terms of Service</a></li>
                        <li><a href="privacy.html">Privacy Policy</a></li>
                    </ul>
                </div>
            </div>
            <div class="copyright">
                <p>&copy; 2025 PromZ. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Get the ID from the URL
            const urlParams = new URLSearchParams(window.location.search);
            let shortId = urlParams.get('id');
            
            // Check if we're using path format (promz.ai/p/ID)
            if (!shortId) {
                const pathParts = window.location.pathname.split('/');
                if (pathParts.length >= 3 && pathParts[1] === 'p') {
                    shortId = pathParts[2];
                }
            }
            
            if (shortId) {
                // Update the prompt info section
                document.getElementById('prompt-info').innerHTML = `
                    <h3>Shared Prompt</h3>
                    <p>Someone shared a prompt with ID: ${shortId}</p>
                    <p>Download the app to access this prompt and many more!</p>
                `;
                
                // Set download links with the ID
                const androidLink = `intent://www.promz.ai/p/${shortId}#Intent;scheme=https;package=ai.promz;end`;
                document.getElementById('ios-download').href = 'https://apps.apple.com/app/promz/idXXXXXXXXXX';
                document.getElementById('android-download').href = androidLink;
                document.getElementById('windows-download').href = 'https://www.microsoft.com/store/apps/XXXXXXXXX';
                
                // For Android - check if we should auto-redirect
                const isAndroid = /Android/i.test(navigator.userAgent);
                if (isAndroid) {
                    // Redirect to the Android app
                    window.location.href = androidLink;
                    
                    // Fallback timer - if redirect fails, show download page
                    setTimeout(function() {
                        // If we're still here, open the Play Store
                        window.location.href = 'https://play.google.com/store/apps/details?id=ai.promz';
                    }, 2000);
                }
            }
        });
    </script>
</body>
</html>
