<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PromZ - Redirecting...</title>
    <link rel="icon" href="/favicon.ico">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            margin: 0;
            padding: 20px;
            text-align: center;
            background-color: #f8f9fa;
        }
        
        .loader {
            border: 5px solid #f3f3f3;
            border-top: 5px solid #6200ee;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        h1 {
            color: #333;
            margin-bottom: 10px;
        }
        
        p {
            color: #666;
            max-width: 600px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="loader"></div>
    <h1>Redirecting to PromZ</h1>
    <p>Please wait while we redirect you to the appropriate destination...</p>
    <p id="fallback-message" style="display: none;">
        If you're not redirected automatically, 
        <a href="/download.html" id="fallback-link">click here</a>.
    </p>
    
    <script src="/js/deeplink-handler.js"></script>
    <script>
        // Show fallback message after 3 seconds
        setTimeout(function() {
            document.getElementById('fallback-message').style.display = 'block';
            
            // Update the fallback link with the ID if available
            const path = window.location.pathname;
            const shortId = path.startsWith('/p/') ? path.substring(3) : path.replace(/^\/+/, '');
            if (shortId) {
                document.getElementById('fallback-link').href = `/download.html?id=${shortId}`;
            }
        }, 3000);
    </script>
</body>
</html>
