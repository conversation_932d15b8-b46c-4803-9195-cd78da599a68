import 'package:flutter/foundation.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:promz_common/config/api_config.dart';
import 'package:promz_common/promz_common.dart';

void main() {
  AppLogger.setLogLevelForTest(LogLevel.error);

  group('ApiConfig', () {
    // Save original values to restore after tests
    late bool originalForceLocalDevelopmentUrl;

    setUp(() {
      // Store original values
      originalForceLocalDevelopmentUrl = ApiConfig.forceLocalDevelopmentUrl;

      // Reset to default values for testing
      ApiConfig.forceLocalDevelopmentUrl = false;
    });

    tearDown(() {
      // Restore original values
      ApiConfig.forceLocalDevelopmentUrl = originalForceLocalDevelopmentUrl;
    });

    group('Protocol handling', () {
      test('should use https in release mode', () {
        // Mock release mode
        debugDefaultTargetPlatformOverride = TargetPlatform.android;

        // Force release mode behavior
        ApiConfig.forceLocalDevelopmentUrl = false;

        expect(ApiConfig.protocol, 'https');
        expect(ApiConfig.wsProtocol, 'wss');

        // Reset platform override
        debugDefaultTargetPlatformOverride = null;
      });

      test('should use http in development mode', () {
        // Force development mode behavior
        ApiConfig.forceLocalDevelopmentUrl = true;

        expect(ApiConfig.protocol, 'http');
        expect(ApiConfig.wsProtocol, 'ws');
      });
    });

    group('baseUrl construction', () {
      test('should include protocol in baseUrl', () {
        // Force development mode for predictable protocol
        ApiConfig.forceLocalDevelopmentUrl = true;

        final baseUrl = ApiConfig.baseUrl;
        expect(baseUrl.startsWith('http://'), isTrue);
      });

      test('should include port in development mode', () {
        // Force development mode
        ApiConfig.forceLocalDevelopmentUrl = true;

        final baseUrl = ApiConfig.baseUrl;
        expect(baseUrl.contains(':8080'), isTrue);
      });

      test('should not include port in production mode', () {
        // Force production mode behavior
        ApiConfig.forceLocalDevelopmentUrl = false;

        final baseUrl = ApiConfig.baseUrl;
        // Production URL should not have a port specified
        expect(baseUrl.contains(':8080'), isFalse);
      });
    });

    group('Custom base host', () {
      tearDown(() {
        // Reset custom base host after each test
        ApiConfig.setCustomBaseHost('');
      });

      test('should strip protocol when setting custom host', () {
        ApiConfig.setCustomBaseHost('https://custom.example.com');

        // Force production mode to use the custom host
        ApiConfig.forceLocalDevelopmentUrl = false;

        expect(ApiConfig.baseHost, 'custom.example.com');
        // Use HTTP protocol by default unless it's the production host
        expect(ApiConfig.baseUrl, 'http://custom.example.com');
      });

      test('should handle host with trailing slash', () {
        ApiConfig.setCustomBaseHost('custom.example.com/');

        // Force production mode to use the custom host
        ApiConfig.forceLocalDevelopmentUrl = false;

        expect(ApiConfig.baseHost, 'custom.example.com');
      });

      test('should set custom host from URL', () {
        ApiConfig.setCustomBaseUrl('https://api.example.org:8443/path');

        // Force production mode to use the custom host
        ApiConfig.forceLocalDevelopmentUrl = false;

        expect(ApiConfig.baseHost, 'api.example.org');
        // The port from the URL is not preserved, only the host
        // Custom hosts use HTTP protocol by default unless it's the production host
        expect(ApiConfig.baseUrl, 'http://api.example.org');
      });
    });

    group('Local development detection', () {
      test('should detect localhost as local development', () {
        expect(ApiConfig.isLocalDevelopment('localhost'), isTrue);
      });

      test('should detect 127.0.0.1 as local development', () {
        expect(ApiConfig.isLocalDevelopment('127.0.0.1'), isTrue);
      });

      test('should detect ******** as local development', () {
        expect(ApiConfig.isLocalDevelopment('********'), isTrue);
      });

      test('should not detect production host as local development', () {
        expect(ApiConfig.isLocalDevelopment('api.promz.ai'), isFalse);
      });

      test('should detect any host as local when forceLocalDevelopmentUrl is true', () {
        ApiConfig.forceLocalDevelopmentUrl = true;
        expect(ApiConfig.isLocalDevelopment('api.promz.ai'), isTrue);
      });
    });

    group('URL construction for different services', () {
      test('should construct proper WebSocket URL', () {
        // Force development mode for predictable protocol
        ApiConfig.forceLocalDevelopmentUrl = true;

        final wsUrl = ApiConfig.webSocketUrl;
        expect(wsUrl.startsWith('ws://'), isTrue);
        expect(wsUrl.endsWith('/ws'), isTrue);
      });

      test('should construct proper gRPC URL', () {
        // Force development mode
        ApiConfig.forceLocalDevelopmentUrl = true;

        final grpcUrl = ApiConfig.grpcUrl;
        // gRPC URL should not include protocol
        expect(grpcUrl.startsWith('http://'), isFalse);
        expect(grpcUrl.startsWith('https://'), isFalse);
        // Should include port
        expect(grpcUrl.contains(':50051'), isTrue);
      });

      test('should determine secure gRPC based on environment', () {
        // Force production mode with default production host
        ApiConfig.forceLocalDevelopmentUrl = false;
        // In production mode with default host, we should use secure gRPC
        expect(ApiConfig.baseHost, 'api.promz.ai');
        expect(ApiConfig.useSecureGrpc, isTrue);

        // Force development mode
        ApiConfig.forceLocalDevelopmentUrl = true;
        expect(ApiConfig.useSecureGrpc, isFalse);
      });
    });

    group('API endpoint construction', () {
      test('should construct proper license endpoints', () {
        // Force development mode for predictable protocol
        ApiConfig.forceLocalDevelopmentUrl = true;

        expect(ApiConfig.licenseStatusEndpoint.startsWith('http://'), isTrue);
        expect(ApiConfig.licenseStatusEndpoint.endsWith('/license/status'), isTrue);

        expect(ApiConfig.licenseVerifyEndpoint.startsWith('http://'), isTrue);
        expect(ApiConfig.licenseVerifyEndpoint.endsWith('/license/verify'), isTrue);
      });

      test('should construct proper usage endpoints', () {
        // Force development mode for predictable protocol
        ApiConfig.forceLocalDevelopmentUrl = true;

        expect(ApiConfig.promptUsageEndpoint.startsWith('http://'), isTrue);
        expect(ApiConfig.promptUsageEndpoint.endsWith('/usage/prompt'), isTrue);
      });
    });
  });
}
