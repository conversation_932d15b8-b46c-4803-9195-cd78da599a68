import 'package:flutter_test/flutter_test.dart';
import 'package:promz_common/promz_common.dart';

void main() {
  AppLogger.setLogLevelForTest(LogLevel.error);
  group('EntityUtils', () {
    group('extractPrefix', () {
      test('should extract prefix from legacy format {#N}', () {
        // Arrange
        const variableName = '{#1}FINANCE:TICKER';

        // Act
        final prefix = EntityUtils.extractPrefix(variableName);

        // Assert
        expect(prefix, '1');
      });

      test('should extract prefix from new format {\$N}', () {
        // Arrange
        const variableName = '{\$2}FINANCE:TICKER';

        // Act
        final prefix = EntityUtils.extractPrefix(variableName);

        // Assert
        expect(prefix, '2');
      });

      test('should return null for variable without prefix', () {
        // Arrange
        const variableName = 'FINANCE:TICKER';

        // Act
        final prefix = EntityUtils.extractPrefix(variableName);

        // Assert
        expect(prefix, null);
      });

      test('should handle alphanumeric prefixes', () {
        // Arrange
        const variableName = '{\$buy}FINANCE:TICKER';

        // Act
        final prefix = EntityUtils.extractPrefix(variableName);

        // Assert
        expect(prefix, 'buy');
      });
    });

    group('extractBaseName', () {
      test('should extract base name from legacy format {#N}', () {
        // Arrange
        const variableName = '{#1}FINANCE:TICKER';

        // Act
        final baseName = EntityUtils.extractBaseName(variableName);

        // Assert
        expect(baseName, 'FINANCE:TICKER');
      });

      test('should extract base name from new format {\$N}', () {
        // Arrange
        const variableName = '{\$2}FINANCE:TICKER';

        // Act
        final baseName = EntityUtils.extractBaseName(variableName);

        // Assert
        expect(baseName, 'FINANCE:TICKER');
      });

      test('should return original name for variable without prefix', () {
        // Arrange
        const variableName = 'FINANCE:TICKER';

        // Act
        final baseName = EntityUtils.extractBaseName(variableName);

        // Assert
        expect(baseName, 'FINANCE:TICKER');
      });
    });

    group('getCanonicalName', () {
      test('should add \$ prefix to canonical name', () {
        // Arrange
        const variableName = '{#1}FINANCE:TICKER';

        // Act
        final canonicalName = EntityUtils.getCanonicalName(variableName);

        // Assert
        expect(canonicalName, '{\$1}FINANCE:TICKER');
      });

      test('should preserve \$ prefix in canonical name', () {
        // Arrange
        const variableName = '{\$2}FINANCE:TICKER';

        // Act
        final canonicalName = EntityUtils.getCanonicalName(variableName);

        // Assert
        expect(canonicalName, '{\$2}FINANCE:TICKER');
      });

      test('should handle variable without prefix', () {
        // Arrange
        const variableName = 'FINANCE:TICKER';

        // Act
        final canonicalName = EntityUtils.getCanonicalName(variableName);

        // Assert
        expect(canonicalName, 'FINANCE:TICKER');
      });

      test('should normalize variable names to uppercase', () {
        // Arrange
        const variableName = '{\$1}finance:ticker';

        // Act
        final canonicalName = EntityUtils.getCanonicalName(variableName);

        // Assert
        expect(canonicalName, '{\$1}FINANCE:TICKER');
      });
    });

    group('getDisplayName', () {
      test('should handle legacy format {#N} in display name', () {
        // Arrange
        const variableName = '{#1}FINANCE:TICKER';

        // Act
        final displayName = EntityUtils.getDisplayName(variableName: variableName);

        // Assert
        expect(displayName, '1. Stock');
      });

      test('should handle new format {\$N} in display name', () {
        // Arrange
        const variableName = '{\$2}FINANCE:TICKER';

        // Act
        final displayName = EntityUtils.getDisplayName(variableName: variableName);

        // Assert
        expect(displayName, '2. Stock');
      });

      test('should handle entity with prefix', () {
        // Arrange
        final entity = Entity(
          text: 'AAPL',
          canonicalText: 'AAPL',
          displayText: 'Apple Inc.',
          type: EntityType.finance,
          prefix: '3',
        );

        // Act
        final displayName = EntityUtils.getDisplayName(entity: entity);

        // Assert
        expect(displayName, '3. Aapl');
      });
    });
  });
}
