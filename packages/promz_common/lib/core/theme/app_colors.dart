import 'package:flutter/material.dart';

/// Application color palette
///
/// This class defines the standard colors used throughout the application
/// to ensure consistency in the UI.
class AppColors {
  /// Primary brand color
  static const Color primary = Color(0xFF3D5AFE);

  /// Secondary brand color
  static const Color secondary = Color(0xFF00C853);

  /// Background color for the app
  static const Color background = Color(0xFFF5F7FA);

  /// Surface color for cards and other elevated components
  static const Color surface = Colors.white;

  /// Error color for error states
  static const Color error = Color(0xFFD50000);

  /// Warning color for warning states
  static const Color warning = Color(0xFFFFAB00);

  /// Success color for success states
  static const Color success = Color(0xFF00C853);

  /// Info color for informational states
  static const Color info = Color(0xFF2196F3);

  /// Text color for primary text
  static const Color textPrimary = Color(0xFF212121);

  /// Text color for secondary text
  static const Color textSecondary = Color(0xFF757575);

  /// Text color for disabled elements
  static const Color textDisabled = Color(0xFFBDBDBD);

  /// Divider color
  static const Color divider = Color(0xFFE0E0E0);

  /// Color for icons
  static const Color icon = Color(0xFF616161);

  /// Color for disabled icons
  static const Color iconDisabled = Color(0xFFBDBDBD);

  /// Color for borders
  static const Color border = Color(0xFFE0E0E0);

  /// Color for disabled elements
  static const Color disabled = Color(0xFFEEEEEE);

  /// Color for overlay shadows
  static const Color shadow = Color(0x40000000);
}
