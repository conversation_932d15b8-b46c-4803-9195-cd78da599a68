import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

/// Application theme definitions for light and dark mode
class AppTheme {
  /// Light theme for the app
  static ThemeData get lightTheme {
    return ThemeData(
      brightness: Brightness.light,
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: Colors.indigo,
        brightness: Brightness.light,
      ),
      textTheme: GoogleFonts.nunitoSansTextTheme(ThemeData.light().textTheme),
      appBarTheme: const AppBarTheme(
        centerTitle: false,
        elevation: 0,
      ),
      cardTheme: const CardTheme(
        elevation: 2,
        margin: EdgeInsets.symmetric(vertical: 8, horizontal: 0),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        ),
      ),
      // Add standardized dialog theme
      dialogTheme: DialogTheme(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        elevation: 3,
        titleTextStyle: GoogleFonts.nunitoSans(
          fontWeight: FontWeight.bold,
          fontSize: 20,
          color: Colors.black87,
        ),
        contentTextStyle: GoogleFonts.nunitoSans(
          fontSize: 16,
          color: Colors.black87,
        ),
        actionsPadding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      ),
      // Add chip theme for consistent category chips
      chipTheme: ChipThemeData(
        backgroundColor: ColorScheme.fromSeed(
          seedColor: Colors.indigo,
          brightness: Brightness.light,
        ).primaryContainer,
        labelStyle: GoogleFonts.nunitoSans(
          fontSize: 12,
          color: ColorScheme.fromSeed(
            seedColor: Colors.indigo,
            brightness: Brightness.light,
          ).onPrimaryContainer,
        ),
        padding: const EdgeInsets.all(8),
      ),
    );
  }

  /// Dark theme for the app
  static ThemeData get darkTheme {
    return ThemeData(
      brightness: Brightness.dark,
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: Colors.indigo,
        brightness: Brightness.dark,
      ),
      textTheme: GoogleFonts.nunitoSansTextTheme(ThemeData.dark().textTheme),
      appBarTheme: const AppBarTheme(
        centerTitle: false,
        elevation: 0,
      ),
      cardTheme: const CardTheme(
        elevation: 2,
        margin: EdgeInsets.symmetric(vertical: 8, horizontal: 0),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        ),
      ),
      // Add standardized dialog theme for dark mode
      dialogTheme: DialogTheme(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        elevation: 3,
        titleTextStyle: GoogleFonts.nunitoSans(
          fontWeight: FontWeight.bold,
          fontSize: 20,
          color: Colors.white,
        ),
        contentTextStyle: GoogleFonts.nunitoSans(
          fontSize: 16,
          color: Colors.white70,
        ),
        actionsPadding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      ),
      // Add chip theme for consistent category chips in dark mode
      chipTheme: ChipThemeData(
        backgroundColor: ColorScheme.fromSeed(
          seedColor: Colors.indigo,
          brightness: Brightness.dark,
        ).primaryContainer,
        labelStyle: GoogleFonts.nunitoSans(
          fontSize: 12,
          color: ColorScheme.fromSeed(
            seedColor: Colors.indigo,
            brightness: Brightness.dark,
          ).onPrimaryContainer,
        ),
        padding: const EdgeInsets.all(8),
      ),
    );
  }

  /// Constants for dialog styling that can't be part of ThemeData
  static const dialogHeaderPadding = EdgeInsets.fromLTRB(24, 16, 16, 16);
  static const dialogContentPadding = EdgeInsets.fromLTRB(24, 20, 24, 16);
  static const dialogBorderRadius = 16.0;
}
