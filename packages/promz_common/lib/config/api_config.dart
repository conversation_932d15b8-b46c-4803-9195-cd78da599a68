import 'dart:io' show Platform;
import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:http/http.dart' as http;
import 'package:promz_common/promz_common.dart';

/// API configuration values for the application
class ApiConfig {
  /// Safe check for Android platform
  static bool get isAndroid => !kIsWeb && Platform.isAndroid;

  /// Custom base host for release builds, can be set at runtime (without protocol or port)
  static String? _customBaseHost;

  /// Default production host
  static const String _defaultProductionHost = 'api.promz.ai';

  /// Flag to force using local development URLs even in release mode
  static bool _forceLocalDevelopmentUrl = false;

  /// Getter for local development URL flag
  static bool get forceLocalDevelopmentUrl => _forceLocalDevelopmentUrl;

  /// Setter for local development URL flag
  static set forceLocalDevelopmentUrl(bool value) {
    // Only log and perform actions if the value is actually changing
    if (_forceLocalDevelopmentUrl != value) {
      _forceLocalDevelopmentUrl = value;

      if (value) {
        appLog.info('Forcing local development URL for API', name: 'ApiConfig');
      } else {
        appLog.info('Using production URL for API', name: 'ApiConfig');
      }

      // Clear any custom base host when switching to ensure proper host selection
      if (!value) {
        // When switching to production, ensure we're using the production host
        _customBaseHost = null;
      }

      // Log the new base URL for debugging
      appLog.info('API base URL is now: $baseUrl', name: 'ApiConfig');
    }
  }

  static String get adminApiKey {
    return dotenv.env['PROMZ_ADMIN_API_KEY'] ?? '';
  }

  /// Get the base host (without protocol or port)
  static String get baseHost {
    // First priority: explicit setting via forceLocalDevelopmentUrl
    if (forceLocalDevelopmentUrl) {
      return _getLocalDevelopmentHost();
    }

    // Second priority: if explicitly set to NOT use local development URL,
    // use production host regardless of release mode
    if (_forceLocalDevelopmentUrl == false) {
      // Use custom host if set, otherwise default production host
      if (_customBaseHost != null && _customBaseHost!.isNotEmpty) {
        return _customBaseHost!;
      } else {
        return _defaultProductionHost;
      }
    }

    // Third priority: debug vs release mode default behavior
    // In debug mode, use local development host by default
    if (!kReleaseMode) {
      return _getLocalDevelopmentHost();
    }

    // In release mode, use the custom base host if set, otherwise use default
    if (_customBaseHost != null && _customBaseHost!.isNotEmpty) {
      return _customBaseHost!;
    } else {
      return _defaultProductionHost;
    }
  }

  /// Gets the appropriate local development host based on platform
  static String _getLocalDevelopmentHost() {
    if (isAndroid) {
      return '********'; // Special IP for Android Emulator to reach host machine
    } else {
      return 'localhost'; // For all other platforms
    }
  }

  /// Check if we're using a local development environment
  static bool isLocalDevelopment(String? host) {
    // If no host provided, use the current baseHost
    final hostToCheck = host ?? baseHost;

    // Check common local development hosts
    return hostToCheck == 'localhost' ||
        hostToCheck == '********' ||
        hostToCheck == '127.0.0.1' ||
        forceLocalDevelopmentUrl;
  }

  /// Check if the current configuration is for local development
  static bool get isLocalDevelopmentEnvironment {
    return isLocalDevelopment(null);
  }

  /// REST API port (typically 8080 for development, standard ports for production)
  static int? get restApiPort {
    // For local development, always use port 8080
    if (forceLocalDevelopmentUrl || (!kReleaseMode && _forceLocalDevelopmentUrl != false)) {
      return 8080;
    }

    // For production, use standard HTTP port
    return null;
  }

  /// WebSocket port (same as REST API port by default)
  static int? get websocketPort {
    return restApiPort;
  }

  /// Port for the gRPC server (typically 50051)
  static int get grpcPort {
    return 50051;
  }

  /// Determines if a port should be included in a URL
  static bool shouldIncludePort(int? port) {
    return port != null;
  }

  /// Get protocol (http/https) based on release mode and configuration
  static String get protocol {
    // Only use HTTPS in release mode when not forced to local development
    // Or when specifically using the production host
    if ((kReleaseMode && !forceLocalDevelopmentUrl) || baseHost == _defaultProductionHost) {
      return 'https';
    }
    return 'http';
  }

  /// WebSocket protocol (ws/wss) based on HTTP protocol
  static String get wsProtocol {
    return protocol == 'https' ? 'wss' : 'ws';
  }

  /// Base URL for the REST API server (with protocol and port)
  static String get baseUrl {
    final host = baseHost;
    final port = restApiPort;

    return shouldIncludePort(port) ? '$protocol://$host:$port' : '$protocol://$host';
  }

  /// Set a custom base host (without protocol or port)
  static void setCustomBaseHost(String host) {
    var cleanHost = host;
    if (cleanHost.contains('://')) {
      final uri = Uri.parse(host);
      cleanHost = uri.host;
    }

    if (cleanHost.endsWith('/')) {
      cleanHost = cleanHost.substring(0, cleanHost.length - 1);
    }

    _customBaseHost = cleanHost;
    appLog.info('Set custom base host: $_customBaseHost', name: 'ApiConfig');
  }

  /// For backward compatibility - set the full URL (will be parsed to extract host)
  static void setCustomBaseUrl(String url) {
    try {
      final uri = Uri.parse(url);
      _customBaseHost = uri.host;
      appLog.info('Set custom base host from URL: $_customBaseHost', name: 'ApiConfig');
    } catch (e) {
      appLog.error('Failed to parse URL: $url - $e', name: 'ApiConfig');
      _customBaseHost = url;
    }
  }

  /// Check connectivity to the primary URL and log result
  static Future<bool> checkConnectivity() async {
    if (!kReleaseMode && _customBaseHost == null) return true;

    try {
      // Try to connect to the health endpoint of the primary URL
      final response = await http
          .get(
            Uri.parse('$baseUrl/health'),
          )
          .timeout(const Duration(seconds: 5));

      // If successful, log and return true
      if (response.statusCode == 200) {
        appLog.info('Successfully connected to server: $baseUrl', name: 'ApiConfig');
        return true;
      } else {
        appLog.warning('Server returned status code ${response.statusCode}', name: 'ApiConfig');
        return false;
      }
    } catch (e) {
      // If there's an error (timeout, connection refused, etc.), log and return false
      appLog.error('Connection to $baseUrl failed: $e', name: 'ApiConfig');
      return false;
    }
  }

  // Other configuration values
  static const int timeoutSeconds = 30;
  static const String apiVersion = 'v1';
  static const bool requiresAuth = true;
  static const bool enableLogging = true;

  /// End points for various API operations
  static String get promptUsageEndpoint => '$baseUrl/usage/prompt';
  static String get popularPromptsEndpoint => '$baseUrl/popular-prompts';
  static String get licenseStatusEndpoint => '$baseUrl/license/status';
  static String get licenseVerifyEndpoint => '$baseUrl/license/verify';
  static String get licenseGetOrCreateFreeEndpoint => '$baseUrl/license/get-or-create-free';
  static String get licenseGetOrCreateTrialEndpoint => '$baseUrl/license/get-or-create-trial';

  /// gRPC server URL with port (without protocol)
  static String get grpcUrl {
    final host = baseHost;
    final port = grpcPort;
    return '$host:$port';
  }

  /// Determines if secure gRPC credentials should be used
  /// This ensures consistency in protocol selection across different URL types
  static bool get useSecureGrpc {
    // Keep this consistent with the protocol getter logic
    // Use secure credentials whenever protocol is https
    return protocol == 'https';
  }

  /// WebSocket URL for real-time updates (with protocol, host, port, and path)
  static String get webSocketUrl {
    try {
      final host = baseHost;
      final port = websocketPort;
      return shouldIncludePort(port) ? '$wsProtocol://$host:$port/ws' : '$wsProtocol://$host/ws';
    } catch (e) {
      appLog.error('Failed to create WebSocket URL: $e', name: 'ApiConfig');
      return 'ws://localhost:8080/ws';
    }
  }

  /// Get the production URL (for display purposes)
  static String getProductionUrl() {
    return 'https://$_defaultProductionHost';
  }

  /// Get the current base URL (for display purposes)
  static String getBaseUrl() {
    return baseUrl;
  }

  /// Get the current gRPC URL (for display purposes)
  static String getGrpcUrl() {
    return grpcUrl;
  }

  /// Get the current WebSocket URL (for display purposes)
  static String getWebSocketUrl() {
    return webSocketUrl;
  }
}
