class Strings {
  // Application identification
  /// Short app name used for OS-level identification (launcher, task switcher)
  static const String appTitle = 'Promz';

  // Navigation and common strings
  static const String backButtonLabel = 'Go Back';
  static const String confirmButtonLabel = 'Confirm';
  static const String retryButtonLabel = 'Retry';
  static const String dismissButtonLabel = 'Dismiss';
  static const String paginationFormat = '%d / %d';

  // Page titles
  static const String discoverPageTitle = 'Discover';
  static const String portfolioPageTitle = 'Portfolio';
  static const String accountPageTitle = 'Account';
  static const String errorPageTitle = 'Error';
  static const String userProfile = 'User Profile';
  static const String promptDetailTitle = 'Prompt Detail';

  // Account view strings
  static const String signIn = 'Sign In';
  static const String signUp = 'Sign Up';
  static const String signOut = 'Sign Out';
  static const String continueWithGoogle = 'Continue with Google';
  static const String continueWithApple = 'Continue with <PERSON>';
  static const String continueWithMicrosoft = 'Continue with <PERSON>';
  static const String signInWithAccount = 'Sign in with your account';
  static const String chooseSignInMethod = 'Choose one of the following sign-in methods:';
  static const String accountLinkingNote =
      'Your account will be automatically linked to your license.';
  static const String signOutConfirmationTitle = 'Sign Out';
  static const String signOutConfirmationMessage = 'Are you sure you want to sign out?';

  // Onboarding strings
  static const String onboardingSkip = 'Skip';
  static const String onboardingBack = 'Back';
  static const String onboardingNext = 'Next';
  static const String onboardingGetStarted = 'Get Started';
  static const String onboardingTitle1 = 'Smarter AI, Real Results';
  static const String onboardingDesc1 =
      'Promz connects you with expert-crafted prompts that deliver results:';
  static const List<String> onboardingBullets1 = [
    'Save hours with pre-optimized prompts',
    'Get consistent, high-quality responses',
    'Solve real problems without prompt engineering',
    'Access vetted prompts from experts'
  ];
  static const String onboardingTitle2 = 'Discover. Share. Sync.';
  static const String onboardingDesc2 = 'Build your personal AI toolkit with powerful features:';
  static const List<String> onboardingBullets2 = [
    'Discover trending prompts from the community',
    'Organize outputs into searchable topics',
    'Share your best prompts with friends and colleagues',
    'Access your prompts across all your devices — on different platforms'
  ];
  static const String newToPromz = 'New to Promz?';
  static const String takeTour = 'Take a quick tour to learn about the key features and benefits.';
  static const String beginTour = 'Begin Tour';

  // License and account information
  static const String whyCreateAccount = 'Why create an account?';
  static const String accountBenefitsText =
      'Creating an account allows you to access premium features and save your preferences across devices. Your account is also linked to your license, giving you access to advanced AI capabilities.';
  static const String freeLicenseTitle = 'Free License';
  static const String proTrialTitle = '30-Day Pro Trial';
  static const String freeLicenseFeatures =
      '• Basic features and functionality\n• Limited set of LLM models\n• Ad-supported experience';
  static const String proLicenseFeatures =
      '• Full access to all Pro features\n• Premium LLM models\n• Ad-free experience\n• 30 days of Pro features\n• No payment upfront';
  static const String continueWithFree = 'Continue with Free';
  static const String upgradeToPro = 'Upgrade to 30-Day Pro Trial';
  static const String chooseLicenseType = 'Choose Your License Type';
  static const String selectLicenseType = 'Select the license type that best fits your needs:';
  static const String daysRemaining = '%d days remaining';
  static const String settingUpAccount = 'Setting up your account...';
  static const String successfulSignIn = 'Successfully signed in!';

  // Settings strings
  static const String themeSettingsTitle = 'Theme';
  static const String systemThemeLabel = 'System';
  static const String lightThemeLabel = 'Light';
  static const String darkThemeLabel = 'Dark';
  static const String fontSizeSettingsTitle = 'Font Size';
  static const String titleFontSizeLabel = 'Title Font Size';
  static const String bodyFontSizeLabel = 'Body Font Size';
  static const String promptExecutionSettingsTitle = 'Prompt Execution';
  static const String confirmPromptExecutionLabel = 'Confirm Before Executing Prompts';
  static const String confirmPromptExecutionDescription =
      'Show a confirmation dialog before executing prompts';

  // Error messages
  static const String failedToLoadCategories = 'Failed to load categories';
  static const String failedToLoadTopics = 'Failed to load topics';
  static const String failedToProcessZipFile = 'Failed to process ZIP file: %s';

  // Home page strings
  /// Full app name with tagline for display in the app's navigation bar
  static const String appBarTitle = 'Promz: AI Made Easy';
  static const String suggestedPromptsTitle = 'Suggested Prompts';
  static const String extractedKeywordsTitle = 'Extracted Keywords';
  static const String sourceInputSectionTitle = 'What do you want to achieve today?';
  static const String sourceInputFieldHint = 'Type to search for a prompt';
  static const String sourcesTitle = 'Sources';
  static const String areYouSureExecutePrompt = 'Are you sure you want to execute?';
  static const String sourceItemFormat = 'Source %d';
  static const String executingPromptText = 'Executing prompt...';
  static const String resultText = 'Result';
  static const String errorText = 'Error';
  static const String retryText = 'Retry';
  static const String saveText = 'Save';
  static const String copyText = 'Copy';
  static const String shareText = 'Share';
  static const String copiedToClipboardText = 'Copied to clipboard';
  static const String noResultsText = 'No results to display';
  static const String recentPromptsTitle = 'Recent Prompts';
  static const String popularPromptsTitle = 'Popular Prompts';

  // Topics strings
  static const String topicRelevanceLabel = 'Relevance: ';
  static const String topicSourceLabel = 'Source: ';

  // Navigation bar labels
  static const String homeNavLabel = 'Home';
  static const String discoverNavLabel = 'Discover';
  static const String portfolioNavLabel = 'Portfolio';
  static const String accountNavLabel = 'Account';

  // Input selection strings
  static const String confirmContentTitle = 'Confirm Content';
  static const String confirmContentMessage = 'The following content will be used:';
  static const String useContentButtonLabel = 'Use Content';
  static const String whatsappChatTitle = 'WhatsApp Chat Export';
  static const String processingWhatsappMessage = 'Processing WhatsApp chat export...';
  static const String messageCountFormat = 'Messages: %s';
  static const String fileSizeFormat = 'File Size: %s';
  static const String sharedFromFormat = 'Shared from: %s';
  static const String noSharedContentTitle = 'No shared content available';
  static const String noSharedContentMessage = 'Share content from another app';
  static const String previewNotAvailable = 'Preview not available';

  // Action strings
  static const String cancel = 'Cancel';
  static const String save = 'Save';
  static const String delete = 'Delete';
  static const String execute = 'Execute';

  // Suggestion subtitles
  static const String promptSuggestionSubtitle = 'Suggested prompt';
  static const String stockSymbolSubtitle = 'Stock symbol';
  static const String hashtagSuggestionSubtitle = 'Related hashtag';
  static const String companyNameSubtitle = 'Company name';
  static const String keywordMatchSubtitle = 'Keyword match';
  static const String categoryWordSubtitle = 'Category word';
  static const String promptWordSubtitle = 'Prompt word';

  // Discover page strings
  static const String loadingCategories = 'Loading categories...';
  static const String categoryListTitle = 'Choose a Category';
  static const String continueWithSelectedCategory = 'Continue';
  static const String promptListTitle = 'Choose a Prompt';
  static const String continueWithSelectedPrompt = 'Continue';
  static const String newPromptTooltip = 'New Prompt';

  // Chat source card strings
  static const String processingFile = 'Processing file...';
  static const String groupChatParticipants = '%d participants';
  static const String directMessage = 'Direct message';
  static const String chatConversation = 'Chat conversation';
  static const String messageCount = 'Messages: %d';

  // Prompt detail strings
  static const String promptContentLabel = 'Prompt Content';
  static const String executePromptButtonLabel = 'Execute Prompt';
  static const String sharePromptTooltip = 'Share Prompt';
  static const String promptVariablesLabel = 'Variables';
}
