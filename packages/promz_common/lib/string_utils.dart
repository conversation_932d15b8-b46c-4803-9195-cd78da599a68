/// Utility functions for string manipulation.

/// Unescapes a string by replacing escaped characters with their actual character representations.
///
/// This function converts:
/// - '\\n' to '\n' (newline)
/// - '\\\'' to '\'' (single quote)
/// - '\\"' to '"' (double quote)
/// - '\\\\' to '\\' (backslash)
String unescapeString(String input) {
  if (input.isEmpty) {
    return input;
  }

  return input
      .replaceAll('\\n', '\n')
      .replaceAll('\\\'', '\'')
      .replaceAll('\\"', '"')
      .replaceAll('\\\\', '\\');
}

/// Escapes a string by replacing special characters with their escaped representations.
///
/// This function converts:
/// - '\n' to '\\n' (newline)
/// - '\'' to '\\\'' (single quote)
/// - '"' to '\\"' (double quote)
/// - '\\' to '\\\\' (backslash)
String escapeString(String input) {
  if (input.isEmpty) {
    return input;
  }

  return input
      .replaceAll('\\', '\\\\') // Must be first to avoid double-escaping
      .replaceAll('\n', '\\n')
      .replaceAll('\'', '\\\'')
      .replaceAll('"', '\\"');
}

String truncateForLogging(String text) {
  if (text.length <= 50) return text;
  return '${text.substring(0, 47)}...';
}
