/// Exception thrown when an LLM API call fails with a known error.
class LlmApiException implements Exception {
  final String message;
  final int statusCode;

  LlmApiException(this.message, this.statusCode);

  @override
  String toString() => message;
}

/// Exception thrown when no eligible models are available for the user's tier
class NoEligibleModelException extends LlmApiException {
  final String? originalError;
  final String? providerId;

  NoEligibleModelException(String message, int statusCode, {this.originalError, this.providerId})
      : super(message, statusCode);

  bool get isProviderError => providerId != null || (originalError?.contains('provider') ?? false);
  bool get isModelError => originalError?.contains('model') ?? false;
  bool get isTierError =>
      originalError?.contains('tier') ?? true; // Default to true for upgrade prompt

  String getProviderName() {
    if (providerId != null) {
      return providerId!.toUpperCase();
    }
    if (originalError?.contains('openai') ?? false) return 'OpenAI';
    if (originalError?.contains('gemini') ?? false) return 'Gemini';
    if (originalError?.contains('anthropic') ?? false) return 'Anthropic';
    if (originalError?.contains('mistral') ?? false) return 'Mistral';
    return 'Unknown';
  }
}

/// Exception for server errors with a message
class ServerErrorWithMessageException extends LlmApiException {
  ServerErrorWithMessageException(String message, int statusCode) : super(message, statusCode);
}

/// Exception for generic server errors
class ServerErrorException extends LlmApiException {
  final String? responseBody;
  ServerErrorException(String message, int statusCode, {this.responseBody})
      : super(message, statusCode);
}
