import 'package:promz_common/llm/base_context_service.dart';

/// Represents a response from the LLM provider
class LlmExecuteResponse {
  /// The text response from the LLM
  final String text;

  /// The provider that generated the response
  final String provider;

  /// Additional metadata about the response
  final Map<String, dynamic>? metadata;

  /// The number of tokens used to generate the response
  final int? tokens;

  /// The reason why the response was finished
  final String? finishReason;

  /// The suggested file name for saving the response
  final String? suggestedFileName;

  /// Creates a new LlmExecuteResponse
  LlmExecuteResponse({
    required this.text,
    required this.provider,
    this.metadata,
    this.tokens,
    this.finishReason,
    this.suggestedFileName,
  });

  // Regex patterns for format markers
  final startMarkerRegex = RegExp(
    r'^\s*(?:\*\*)?Output\s+format:\s+Markdown(?:\*\*)?\s*\n?',
    caseSensitive: false,
  );
  final endMarkerRegex = RegExp(
    r'\n?\s*(?:\*\*)?Output\s+format:\s+Markdown(?:\*\*)?\s*$',
    caseSensitive: false,
  );

  /// Checks if the response is in markdown format
  bool get isMarkdown {
    final trimmedText = text.trim();

    // Check for format markers at the beginning
    if (startMarkerRegex.hasMatch(trimmedText)) {
      return true;
    }

    // Check for format markers at the end
    if (endMarkerRegex.hasMatch(trimmedText)) {
      return true;
    }

    // Check for common markdown patterns
    return _containsMarkdownPatterns(trimmedText);
  }

  /// Helper method to detect common markdown patterns
  bool _containsMarkdownPatterns(String content) {
    // Split the content into lines for analysis
    final lines = content.split('\n');

    // Count of lines with markdown patterns
    int markdownLineCount = 0;

    // Regular expressions for common markdown patterns
    final headerRegex = RegExp(r'^\s*#{1,6}\s+.+');
    final listRegex = RegExp(r'^\s*[-*+]\s+.+');
    final numberedListRegex = RegExp(r'^\s*\d+\.\s+.+');
    final codeBlockRegex = RegExp(r'^\s*```');
    final linkRegex = RegExp(r'\[.+\]\(.+\)');
    final imageRegex = RegExp(r'!\[.+\]\(.+\)');
    final emphasisRegex = RegExp(r'(\*\*|__).+(\*\*|__)|\*[^*]+\*|_[^_]+_');
    final blockquoteRegex = RegExp(r'^\s*>\s+.+');
    final tableRegex = RegExp(r'^\s*\|.+\|');
    final tableSeparatorRegex = RegExp(r'^\s*\|[\s-:]+\|');
    final horizontalRuleRegex = RegExp(r'^\s*(---|\*\*\*|___)\s*$');

    // Check each line for markdown patterns
    for (final line in lines) {
      if (headerRegex.hasMatch(line) ||
          listRegex.hasMatch(line) ||
          numberedListRegex.hasMatch(line) ||
          codeBlockRegex.hasMatch(line) ||
          linkRegex.hasMatch(line) ||
          imageRegex.hasMatch(line) ||
          emphasisRegex.hasMatch(line) ||
          blockquoteRegex.hasMatch(line) ||
          tableRegex.hasMatch(line) ||
          tableSeparatorRegex.hasMatch(line) ||
          horizontalRuleRegex.hasMatch(line)) {
        markdownLineCount++;
      }
    }

    // Calculate markdown density
    final nonEmptyLines = lines.where((line) => line.trim().isNotEmpty).length;
    if (nonEmptyLines == 0) return false;

    final markdownDensity = markdownLineCount / nonEmptyLines;

    // Return true if density exceeds threshold (20%)
    return markdownDensity > 0.2;
  }

  /// Gets the content without the markdown header
  String get enhancedMarkdownContent {
    // If not markdown, return as is
    if (!isMarkdown) {
      return text;
    }

    final trimmedText = text.trim();

    // Remove format markers from the beginning and end
    String cleanedText = trimmedText;

    // Remove marker from beginning if present
    if (startMarkerRegex.hasMatch(cleanedText)) {
      cleanedText = cleanedText.replaceFirst(startMarkerRegex, '');
    }

    // Remove marker from end if present
    if (endMarkerRegex.hasMatch(cleanedText)) {
      cleanedText = cleanedText.replaceFirst(endMarkerRegex, '');
    }

    return cleanedText.trim();
  }

  /// Process any untranslated variables in the response text
  String processResponseText(BaseContextService contextService) {
    String content = isMarkdown ? enhancedMarkdownContent : text;

    // Use the ClientContextService to transform any remaining template variables
    return contextService.transformTitleWithAllValues(content);
  }

  /// Gets the appropriate file extension based on content type
  String getFileExtension() {
    // Use PDF for markdown content, TXT for plain text
    return isMarkdown ? 'pdf' : 'txt';
  }

  /// Gets the full file name with appropriate extension
  String getFullFileName() {
    if (suggestedFileName == null || suggestedFileName!.isEmpty) {
      // Generate a default file name if none provided
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      return 'promz-$timestamp.${getFileExtension()}';
    }

    // If the suggested file name already has an extension, use it as is
    if (suggestedFileName!.contains('.')) {
      return suggestedFileName!;
    }

    // Otherwise, add the appropriate extension
    return '$suggestedFileName.${getFileExtension()}';
  }

  /// Creates a LlmExecuteResponse from JSON
  factory LlmExecuteResponse.fromJson(Map<String, dynamic> json) {
    // Handle both response formats - the old format (result, model) and the new format (text, provider)
    return LlmExecuteResponse(
      // Try to get 'text' first, fall back to 'result' if not available
      text: json['text'] as String? ?? json['result'] as String,

      // Try to get 'provider' first, fall back to 'model' if not available
      provider: json['provider'] as String? ?? json['model'] as String,

      // Handle metadata if available
      metadata: json['metadata'] != null ? Map<String, dynamic>.from(json['metadata']) : null,

      // Handle tokens
      tokens: json['tokens'] as int?,

      // Handle finish_reason if available
      finishReason: json['finish_reason'] as String?,

      // Handle suggested_file_name if available
      suggestedFileName: json['suggested_file_name'] as String?,
    );
  }

  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'text': text,
      'provider': provider,
      if (metadata != null) 'metadata': metadata,
      if (tokens != null) 'tokens': tokens,
      if (finishReason != null) 'finish_reason': finishReason,
      if (suggestedFileName != null) 'suggested_file_name': suggestedFileName,
    };
  }

  @override
  String toString() {
    return 'LlmExecuteResponse{text: ${text.substring(0, text.length > 50 ? 50 : text.length)}..., provider: $provider, tokens: $tokens, finishReason: $finishReason, suggestedFileName: $suggestedFileName}';
  }
}
