class LlmExecuteRequest {
  final String? promptId;
  final String? categoryId;
  final String promptContent;
  final List<SourceContent> sourceContent;
  final int? maxTokens;
  final double? temperature;
  final String? provider;
  final Map<String, String>? options;
  final String? apiKey;
  final Map<String, dynamic>? variables;

  LlmExecuteRequest({
    this.promptId,
    this.categoryId,
    required this.promptContent,
    this.sourceContent = const [],
    this.maxTokens,
    this.temperature,
    this.provider,
    this.options,
    this.apiKey,
    this.variables,
  });

  Map<String, dynamic> toJson() {
    final sourcesList =
        sourceContent.isNotEmpty ? sourceContent.map((s) => s.content).toList() : null;

    return {
      'prompt_content': promptContent,
      'api_key': apiKey,
      if (sourcesList != null) 'sources': sourcesList,
      if (promptId != null) 'prompt_id': promptId,
      if (categoryId != null) 'category_id': categoryId,
      if (maxTokens != null) 'max_tokens': maxTokens,
      if (temperature != null) 'temperature': temperature,
      if (provider != null) 'provider': provider,
      if (options != null) 'options': options,
      if (variables != null) 'variables': variables,
    };
  }
}

class SourceContent {
  final String type;
  final String content;
  final String? fileName;
  final String? contentType;

  SourceContent({
    required this.type,
    required this.content,
    this.fileName,
    this.contentType,
  });

  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'content': content,
      if (fileName != null) 'file_name': fileName,
      if (contentType != null) 'content_type': contentType,
    };
  }
}
