import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:promz_common/promz_common.dart';
import 'package:promz_common/config/api_config.dart';

/// Service for executing LLM operations.
///
/// This service is used by the admin application to execute LLM operations.
/// It uses the Promz LLM execution service internally for consistent
/// error handling and request formatting while maintaining the admin-specific API.
class PromzLlmExecutionService {
  final String _logName = 'PromzLlmExecutionService';

  final String apiBaseUrl;
  final String promzApiKey;

  PromzLlmExecutionService({required this.apiBaseUrl, required this.promzApiKey});

  Future<LlmExecuteResponse> execute(LlmExecuteRequest request) async {
    // Ensure the URL has a proper protocol
    final String fullUrl = _ensureProtocol('$apiBaseUrl/llm/execute');
    final url = Uri.parse(fullUrl);

    final headers = <String, String>{
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $promzApiKey',
      'X-API-Key': promzApiKey,
    };
    final body = jsonEncode(request.toJson());

    final response = await http.post(url, headers: headers, body: body);
    if (response.statusCode == 200) {
      return LlmExecuteResponse.fromJson(jsonDecode(response.body));
    } else {
      // Enhanced error handling for 'no eligible models' and server errors
      if (response.statusCode == 500) {
        try {
          final errorResponse = jsonDecode(response.body);
          if (errorResponse is Map<String, dynamic> &&
              errorResponse.containsKey('error') &&
              errorResponse['error'] is String) {
            final errorMessage = errorResponse['error'].toString();
            if (errorMessage.contains('no eligible') &&
                (errorMessage.contains('models available') ||
                    errorMessage.contains('available for free tier'))) {
              String? providerId;
              if (errorMessage.contains('openai')) {
                providerId = 'openai';
              } else if (errorMessage.contains('google') || errorMessage.contains('gemini')) {
                providerId = 'google';
              } else if (errorMessage.contains('anthropic')) {
                providerId = 'anthropic';
              } else if (errorMessage.contains('mistral')) {
                providerId = 'mistral';
              }
              throw NoEligibleModelException(
                'No eligible models available for your current tier. Please upgrade to Pro for full access to all models.',
                response.statusCode,
                originalError: errorMessage,
                providerId: providerId,
              );
            }

            appLog.error('LLM error: $errorMessage', name: _logName);
            throw ServerErrorWithMessageException(errorMessage, response.statusCode);
          }
        } catch (parseError) {
          if (parseError is NoEligibleModelException) {
            rethrow;
          }
        }
      }
      // Fallback: check for 'no eligible models' in body
      final responseBody = response.body;
      if (responseBody.contains('no eligible') &&
          (responseBody.contains('models available') ||
              responseBody.contains('available for free tier'))) {
        String? providerId;
        if (responseBody.contains('openai')) {
          providerId = 'openai';
        } else if (responseBody.contains('gemini') || responseBody.contains('google')) {
          providerId = 'google';
        } else if (responseBody.contains('anthropic')) {
          providerId = 'anthropic';
        } else if (responseBody.contains('mistral')) {
          providerId = 'mistral';
        }
        throw NoEligibleModelException(
          'No eligible models available for your current tier. Please upgrade to Pro for full access to all models.',
          response.statusCode,
          originalError: responseBody,
          providerId: providerId,
        );
      }

      appLog.error('Server error: ${response.statusCode}', name: _logName);
      throw ServerErrorException(
          'Server URL: $fullUrl, error: ${response.statusCode}', response.statusCode,
          responseBody: response.body);
    }
  }

  /// Ensures the URL has a proper protocol (http:// or https://)
  String _ensureProtocol(String url) {
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url; // URL already has a protocol
    }

    // If URL already has a protocol but not http/https, strip it
    if (url.contains('://')) {
      final parts = url.split('://');
      url = parts[1];
    }

    // Use the protocol from ApiConfig or default to https
    final protocol = ApiConfig.protocol;
    return '$protocol://$url';
  }
}
