// Shared DisplayItem class for use across admin and client apps
import 'package:promz_common/promz_common.dart';

enum DisplayItemType {
  promptTitle,
  keywordMatch,
  keywordWord,
  promptWord,
  stockSymbol,
  companyName,
  categoryWord,
  entity,
}

/// Represents a display item that can be shown in the prompt list or suggestion list.
class DisplayItem {
  static const _logName = 'DisplayItem';

  final String text;
  final String displayText;
  final String? subtitle;
  final DisplayItemType type;
  final PromptModel? prompt;
  final double? relevanceScore;
  final List<String>? matchedKeywords;
  final Entity? entity;

  DisplayItem({
    required this.text,
    required this.displayText,
    this.subtitle,
    required this.type,
    this.prompt,
    this.relevanceScore,
    this.matchedKeywords,
    this.entity,
  });

  /// Create a display item from a prompt model with transformed variables
  factory DisplayItem.fromPromptModel(PromptModel prompt, dynamic clientContextService) {
    // Note: We're keeping this method flexible to work with both client and admin context services
    // The clientContextService is typed as dynamic to avoid tight coupling, but it should support
    // the transformTitleWithAllValues method.

    final displayText = clientContextService?.transformTitleWithAllValues != null
        ? clientContextService.transformTitleWithAllValues(prompt.title)
        : prompt.title;

    // Similarly transform subtitle if present
    final transformedSubtitle = prompt.subtitle != null && prompt.subtitle!.isNotEmpty
        ? (clientContextService?.transformTitleWithAllValues != null
            ? clientContextService.transformTitleWithAllValues(prompt.subtitle!)
            : prompt.subtitle!)
        : prompt.subtitle;

    return DisplayItem(
      text: prompt.title, // Keep original template text for insertion
      displayText: displayText, // Use transformed text for display
      subtitle: transformedSubtitle,
      type: DisplayItemType.promptTitle,
      prompt: prompt,
      relevanceScore: prompt.relevanceScore,
      matchedKeywords: prompt.matchedKeywords,
    );
  }

  /// Transform this DisplayItem with variable values from a context service
  /// Returns a new DisplayItem with transformed display text if needed, or this item if no transformation is needed
  DisplayItem transformWithValues(dynamic clientContextService) {
    // Early return if the text doesn't contain template variables
    if (!text.contains('{{')) {
      return this;
    }

    // Only proceed if the context service has the required method
    if (clientContextService?.transformTitleWithAllValues == null) {
      return this;
    }

    // Transform the display text using the client context service
    final transformedText = clientContextService.transformTitleWithAllValues(text);

    // If the text didn't change, return the original item
    if (transformedText == displayText) {
      return this;
    }

    appLog.debug('Transformed DisplayItem: "$displayText" -> "$transformedText"', name: _logName);

    // Return a new DisplayItem with the transformed display text
    return DisplayItem(
      text: text, // Keep original text for insertion
      displayText: transformedText, // Use transformed text for display
      subtitle: subtitle,
      type: type,
      prompt: prompt,
      relevanceScore: relevanceScore,
      matchedKeywords: matchedKeywords,
      entity: entity,
    );
  }
}
