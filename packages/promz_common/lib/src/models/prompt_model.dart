// Shared PromptModel class for use across admin and client apps
class PromptModel {
  final String id;
  final String title;
  final List<String> keywords;
  final String? subtitle;
  final double? relevanceScore;
  final List<String>? matchedKeywords;
  final String? categoryId;
  final String? categoryName;
  final List<String> variables;

  const PromptModel({
    required this.id,
    required this.title,
    this.keywords = const [],
    this.subtitle,
    this.relevanceScore,
    this.matchedKeywords,
    this.categoryId,
    this.categoryName,
    this.variables = const [],
  });

  /// Factory constructor to create a PromptModel from JSON
  factory PromptModel.fromJson(Map<String, dynamic> json) {
    return PromptModel(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      keywords: json['keywords'] is List
          ? List<String>.from(json['keywords'])
          : json['keywords']?.toString().split(',').map((e) => e.trim()).toList() ?? [],
      subtitle: json['subtitle'],
      categoryId: json['category_id'],
      categoryName: json['category_name'],
      variables: json['variables'] is List ? List<String>.from(json['variables']) : [],
    );
  }

  double get score => relevanceScore ?? 0;

  /// Checks if this prompt has any template variables defined
  bool get hasTemplateVariables => variables.isNotEmpty;

  PromptModel copyWith({
    String? id,
    String? title,
    List<String>? keywords,
    String? subtitle,
    double? relevanceScore,
    List<String>? matchedKeywords,
    String? categoryId,
    String? categoryName,
    List<String>? variables,
  }) {
    return PromptModel(
      id: id ?? this.id,
      title: title ?? this.title,
      keywords: keywords ?? this.keywords,
      subtitle: subtitle ?? this.subtitle,
      relevanceScore: relevanceScore ?? this.relevanceScore,
      matchedKeywords: matchedKeywords ?? this.matchedKeywords,
      categoryId: categoryId ?? this.categoryId,
      categoryName: categoryName ?? this.categoryName,
      variables: variables ?? this.variables,
    );
  }
}
