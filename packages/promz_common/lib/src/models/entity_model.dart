import 'package:promz_common/promz_common.dart';

/// Defines different types of entities that can be detected in text
enum EntityType {
  /// Financial entities like stock tickers and company names
  finance,

  /// Geographic locations like countries, cities, etc.
  location,

  /// Individual people, notable figures
  person,

  /// Time periods or specific time references
  timeframe,

  /// News articles with URL and contents
  news,

  /// File attachments and their contents
  attachment,

  /// Conversation content from chat exports
  conversation,

  /// YouTube videos with video ID and metadata
  youtubeVideo,

  /// Generic entity type for miscellaneous entities
  generic
}

/// Represents a detected entity in text input
class Entity {
  /// The full text of the entity as detected
  final String text;

  /// The entity text in canonical form for template reference
  final String canonicalText;

  /// The display text for human-readable format
  final String displayText;

  /// The category type of this entity
  final EntityType type;

  /// The start position of entity in original text, if detected
  final int? startPosition;

  /// The end position of entity in original text, if detected
  final int? endPosition;

  /// Confidence score of entity detection (0.0-1.0)
  final double confidence;

  /// Additional context or metadata for the entity
  final Map<String, dynamic>? metadata;

  /// New field for prefixed variables
  final String? prefix;

  const Entity({
    required this.text,
    required this.canonicalText,
    required this.displayText,
    required this.type,
    this.startPosition,
    this.endPosition,
    this.confidence = 1.0,
    this.metadata,
    this.prefix,
  });

  /// Creates template representation in the format {{CATEGORY:ENTITY}}
  /// or with prefix {#PREFIX}CATEGORY:ENTITY
  String get templateText {
    if (prefix != null) {
      return '{#$prefix}${type.name.toUpperCase()}:$canonicalText';
    } else {
      return '{{${type.name.toUpperCase()}:$canonicalText}}';
    }
  }

  /// Generates template for non-entity custom variables
  /// This is a static utility method to help with template generation
  static String generateCustomTemplate(String name, {String? prefix}) {
    if (prefix != null) {
      return '{#$prefix}$name';
    } else {
      return '{{$name}}';
    }
  }

  /// Convenience method to check if entity has position information
  bool get hasPosition => startPosition != null && endPosition != null;

  /// For financial entities, get the stock symbol
  String? get stockSymbol {
    if (type == EntityType.finance) {
      return metadata?['symbol'] as String?;
    }
    return null;
  }

  /// For financial entities, get the company name
  String? get companyName {
    if (type == EntityType.finance) {
      return metadata?['companyName'] as String?;
    }
    return null;
  }

  /// For financial entities, get the entity subtype
  String? get entitySubtype {
    if (type == EntityType.finance) {
      return metadata?['entitySubtype'] as String?;
    }
    return null;
  }

  /// For news entities, get the article URL
  String? get articleUrl {
    if (type == EntityType.news) {
      return metadata?['url'] as String?;
    }
    return null;
  }

  /// For YouTube video entities, get the video ID
  String? get videoId {
    if (type == EntityType.youtubeVideo) {
      return metadata?[MetadataKeys.videoId] as String?;
    }
    return null;
  }

  /// For YouTube video entities, get the video URL
  String? get videoUrl {
    if (type == EntityType.youtubeVideo) {
      return metadata?[MetadataKeys.url] as String?;
    }
    return null;
  }

  /// For YouTube video entities, get the video title
  String? get videoTitle {
    if (type == EntityType.youtubeVideo) {
      return metadata?[MetadataKeys.title] as String?;
    }
    return null;
  }

  /// For YouTube video entities, get the channel name
  String? get channelName {
    if (type == EntityType.youtubeVideo) {
      return metadata?[MetadataKeys.channelName] as String?;
    }
    return null;
  }

  /// For news entities, get the article contents
  String? get articleContents {
    if (type == EntityType.news) {
      return metadata?['contents'] as String?;
    }
    return null;
  }

  /// For conversation entities, get the conversation contents
  String? get conversationContents {
    if (type == EntityType.conversation) {
      return metadata?['contents'] as String?;
    }
    return null;
  }

  /// For conversation entities, get the platform (e.g., WhatsApp, Telegram)
  String? get platform {
    if (type == EntityType.conversation) {
      return metadata?['platform'] as String?;
    }
    return null;
  }

  /// For conversation entities, get the group name
  String? get groupName {
    if (type == EntityType.conversation) {
      return metadata?['groupName'] as String?;
    }
    return null;
  }

  /// For conversation entities, get the participants list
  List<String>? get participants {
    if (type == EntityType.conversation && metadata?['participants'] != null) {
      return List<String>.from(metadata?['participants'] as List);
    }
    return null;
  }

  /// For conversation entities, get the message count
  int? get messageCount {
    if (type == EntityType.conversation) {
      return metadata?['messageCount'] as int?;
    }
    return null;
  }

  /// Factory for creating finance entities with stock metadata
  factory Entity.stockTicker({
    required String symbol,
    required String companyName,
    int? startPosition,
    int? endPosition,
    double confidence = 1.0,
  }) {
    // Handle symbol format (ensure it has $ prefix)
    final cleanSymbol = EntityUtils.cleanStockSymbol(symbol);

    // Create enhanced display format with stock chart emoji and company name
    final enhancedDisplay = '📈 $cleanSymbol ($companyName)';

    return Entity(
      text: cleanSymbol,
      canonicalText: cleanSymbol,
      displayText: enhancedDisplay,
      type: EntityType.finance,
      startPosition: startPosition,
      endPosition: endPosition,
      confidence: confidence,
      metadata: {
        'symbol': cleanSymbol,
        'companyName': companyName,
        'entitySubtype': 'stock',
      },
    );
  }

  /// Factory for creating finance entities from company name
  factory Entity.companyName({
    required String companyName,
    required String symbol,
    int? startPosition,
    int? endPosition,
    double confidence = 1.0,
  }) {
    // Handle symbol format (ensure it has $ prefix)
    final cleanSymbol = EntityUtils.cleanStockSymbol(symbol);

    return Entity(
      text: companyName,
      canonicalText: companyName,
      displayText: '$companyName ($cleanSymbol)',
      type: EntityType.finance,
      startPosition: startPosition,
      endPosition: endPosition,
      confidence: confidence,
      metadata: {
        'symbol': cleanSymbol,
        'companyName': companyName,
        'entitySubtype': 'company',
      },
    );
  }

  /// Factory for creating news entities with article metadata
  factory Entity.newsArticle({
    required String url,
    required String contents,
    int? startPosition,
    int? endPosition,
    double confidence = 1.0,
  }) {
    return Entity(
      text: url,
      canonicalText: url,
      displayText: 'News Article: $url',
      type: EntityType.news,
      startPosition: startPosition,
      endPosition: endPosition,
      confidence: confidence,
      metadata: {
        'url': url,
        'contents': contents,
      },
    );
  }

  /// Factory for creating YouTube video entities with metadata
  factory Entity.youtubeVideo({
    required String videoId,
    required String url,
    required String title,
    required String channelName,
    String? thumbnailUrl,
    String? description,
    int? startPosition,
    int? endPosition,
    double confidence = 1.0,
  }) {
    return Entity(
      text: url,
      canonicalText: videoId,
      displayText: '▶️ $title - $channelName',
      type: EntityType.youtubeVideo,
      startPosition: startPosition,
      endPosition: endPosition,
      confidence: confidence,
      metadata: {
        MetadataKeys.videoId: videoId,
        MetadataKeys.url: url,
        MetadataKeys.title: title,
        MetadataKeys.channelName: channelName,
        if (thumbnailUrl != null) MetadataKeys.thumbnailUrl: thumbnailUrl,
        if (description != null) MetadataKeys.description: description,
      },
    );
  }

  /// Factory for creating conversation entities with chat metadata
  factory Entity.conversation({
    required String contents,
    String? platform,
    String? groupName,
    List<String>? participants,
    int? messageCount,
    String? startDate,
    String? endDate,
    int? startPosition,
    int? endPosition,
    double confidence = 1.0,
  }) {
    final displayName = groupName ?? 'Chat Conversation';
    final platformDisplay = platform != null ? ' ($platform)' : '';

    return Entity(
      text: displayName,
      canonicalText: displayName,
      displayText: '💬 $displayName$platformDisplay',
      type: EntityType.conversation,
      startPosition: startPosition,
      endPosition: endPosition,
      confidence: confidence,
      metadata: {
        'contents': contents,
        'platform': platform,
        'groupName': groupName,
        'participants': participants,
        'messageCount': messageCount,
        'startDate': startDate,
        'endDate': endDate,
      },
    );
  }

  /// Factory for creating entities from additional metadata
  factory Entity.fromAdditionalInfo(Map<String, dynamic>? additionalInfo, {String? symbol}) {
    if (additionalInfo == null) {
      throw ArgumentError('additionalInfo cannot be null');
    }
    if (additionalInfo['symbol'] != null || additionalInfo['companyName'] != null) {
      return Entity.companyName(
        companyName: additionalInfo['companyName'] ?? '',
        symbol: additionalInfo['symbol'] ?? symbol ?? '',
      );
    } else if (additionalInfo['url'] != null && additionalInfo[MetadataKeys.videoId] != null) {
      return Entity.youtubeVideo(
        videoId: additionalInfo[MetadataKeys.videoId] ?? '',
        url: additionalInfo['url'] ?? '',
        title: additionalInfo[MetadataKeys.title] ?? 'YouTube Video',
        channelName: additionalInfo[MetadataKeys.channelName] ?? 'YouTube',
        thumbnailUrl: additionalInfo[MetadataKeys.thumbnailUrl],
        description: additionalInfo[MetadataKeys.description],
      );
    } else if (additionalInfo['url'] != null) {
      return Entity.newsArticle(
        url: additionalInfo['url'] ?? '',
        contents: additionalInfo['contents'] ?? '',
      );
    } else if (additionalInfo['platform'] != null || additionalInfo['groupName'] != null) {
      return Entity.conversation(
        contents: additionalInfo['contents'] ?? '',
        platform: additionalInfo['platform'],
        groupName: additionalInfo['groupName'],
        participants: additionalInfo['participants'] != null
            ? List<String>.from(additionalInfo['participants'])
            : null,
        messageCount: additionalInfo['messageCount'],
        startDate: additionalInfo['startDate'],
        endDate: additionalInfo['endDate'],
      );
    } else {
      throw ArgumentError('Invalid entity metadata provided');
    }
  }

  /// Copy with method
  Entity copyWith({
    String? text,
    String? canonicalText,
    String? displayText,
    EntityType? type,
    double? confidence,
    int? startPosition,
    int? endPosition,
    Map<String, dynamic>? metadata,
    String? prefix,
  }) {
    return Entity(
      text: text ?? this.text,
      canonicalText: canonicalText ?? this.canonicalText,
      displayText: displayText ?? this.displayText,
      type: type ?? this.type,
      confidence: confidence ?? this.confidence,
      startPosition: startPosition ?? this.startPosition,
      endPosition: endPosition ?? this.endPosition,
      metadata: metadata ?? this.metadata,
      prefix: prefix ?? this.prefix,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Entity &&
          runtimeType == other.runtimeType &&
          type == other.type &&
          canonicalText == other.canonicalText;

  @override
  int get hashCode => type.hashCode ^ canonicalText.hashCode;

  @override
  String toString() => 'Entity($type: $canonicalText, confidence: $confidence)';
}
