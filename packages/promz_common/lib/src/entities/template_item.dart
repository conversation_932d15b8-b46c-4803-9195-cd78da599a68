/// A class representing an entity template that can be inserted into text
class TemplateItem {
  /// User-friendly label for the template
  final String label;

  /// The actual template text to insert (e.g., {{FINANCE:SP500_SYMBOL}})
  final String template;

  /// Description of what the template represents
  final String description;

  /// Optional icon identifier for UI rendering
  final String? icon;

  /// Optional group/category name for organizing templates
  final String? group;

  /// Creates a new template item
  const TemplateItem({
    required this.label,
    required this.template,
    required this.description,
    this.icon,
    this.group,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TemplateItem && runtimeType == other.runtimeType && template == other.template;

  @override
  int get hashCode => template.hashCode;

  @override
  String toString() => 'TemplateItem($label: $template)';
}
