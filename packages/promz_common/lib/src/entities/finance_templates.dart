import 'package:promz_common/src/entities/template_item.dart';

/// Finance-related entity templates that can be inserted into prompt text
class FinanceTemplates {
  /// List of all available finance templates
  static const List<TemplateItem> templates = [
    TemplateItem(
      label: 'Company Symbol',
      template: '{{FINANCE:SP500_SYMBOL}}',
      description: 'Stock ticker symbol for S&P 500 companies',
    ),
    TemplateItem(
      label: 'Company Name',
      template: '{{FINANCE:COMPANY_NAME}}',
      description: 'Full company name for S&P 500 companies',
    ),
    TemplateItem(
      label: 'Industry',
      template: '{{FINANCE:INDUSTRY}}',
      description: 'Industry sector category for S&P 500 companies',
    ),
  ];

  /// Gets a template by its template string (e.g., "{{FINANCE:SP500_SYMBOL}}")
  static TemplateItem? getByTemplate(String template) {
    try {
      return templates.firstWhere((item) => item.template == template);
    } catch (_) {
      return null;
    }
  }

  /// Gets a template by its label
  static TemplateItem? getByLabel(String label) {
    try {
      return templates.firstWhere(
        (item) => item.label.toLowerCase() == label.toLowerCase(),
      );
    } catch (_) {
      return null;
    }
  }
}
