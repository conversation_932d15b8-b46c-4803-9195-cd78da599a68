import 'package:promz_common/src/entities/finance_templates.dart';
import 'package:promz_common/src/entities/template_item.dart';

/// Main class to access all available entity templates
class EntityTemplates {
  /// Get a list of all available entity templates
  static List<TemplateItem> get all => [
        ...FinanceTemplates.templates,
        // Add more template categories as they are developed
      ];

  /// Get all templates organized by category
  static Map<String, List<TemplateItem>> get byCategory => {
        'Finance': FinanceTemplates.templates,
        // Add more categories as they are developed
      };

  /// Find a template by its template text
  static TemplateItem? findByTemplate(String template) {
    try {
      return all.firstWhere((item) => item.template == template);
    } catch (_) {
      return null;
    }
  }

  /// Find a template by its label
  static TemplateItem? findByLabel(String label) {
    try {
      return all.firstWhere(
        (item) => item.label.toLowerCase() == label.toLowerCase(),
      );
    } catch (_) {
      return null;
    }
  }
}
