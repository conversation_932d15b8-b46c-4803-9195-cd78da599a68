import 'package:flutter/material.dart';

/// A consistent dialog component for the Promz app that provides standard styling
/// with options for a colored header, title, icon, and more.
class PromzDialog extends StatelessWidget {
  /// The title text to display in the header
  final String? title;

  /// An icon to display in the header (typically a category icon)
  final IconData? headerIcon;

  /// The content of the dialog
  final Widget? content;

  /// Widget list for action buttons at the bottom of the dialog
  final List<Widget>? actions;

  /// Whether to show a close button in the header
  final bool showCloseButton;

  /// Callback when the close button is pressed
  final VoidCallback? onClose;

  /// Optional color for the header background (uses primaryContainer by default)
  final Color? headerColor;

  /// Optional color for header content (icons, text)
  final Color? headerContentColor;

  /// Optional padding for the content section
  final EdgeInsetsGeometry contentPadding;

  /// Dialog width constraint (optional)
  final double? width;

  /// Dialog maximum height constraint (optional)
  final double? maxHeight;

  /// Creates a consistently styled dialog for the Promz app.
  ///
  /// [title] is the text displayed in the header.
  /// [headerIcon] is an optional icon displayed in the header.
  /// [content] is the main content widget of the dialog.
  /// [actions] is a list of action widgets displayed at the bottom.
  /// [showCloseButton] determines whether to show a close button in the header.
  /// [onClose] is called when the close button is pressed.
  /// [headerColor] sets a custom background color for the header.
  /// [headerContentColor] sets a custom color for the header content.
  /// [contentPadding] sets custom padding for the content section.
  const PromzDialog({
    Key? key,
    this.title,
    this.headerIcon,
    this.content,
    this.actions,
    this.showCloseButton = true,
    this.onClose,
    this.headerColor,
    this.headerContentColor,
    this.contentPadding = const EdgeInsets.fromLTRB(24, 20, 24, 16),
    this.width,
    this.maxHeight,
  }) : super(key: key);

  /// Helper method to show a PromzDialog
  static Future<T?> show<T>({
    required BuildContext context,
    String? title,
    IconData? headerIcon,
    Widget? content,
    List<Widget>? actions,
    bool showCloseButton = true,
    VoidCallback? onClose,
    Color? headerColor,
    Color? headerContentColor,
    EdgeInsetsGeometry contentPadding = const EdgeInsets.fromLTRB(24, 20, 24, 16),
    double? width,
    double? maxHeight,
    bool barrierDismissible = true,
  }) {
    return showDialog<T>(
      context: context,
      barrierDismissible: barrierDismissible,
      builder: (context) => PromzDialog(
        title: title,
        headerIcon: headerIcon,
        content: content,
        actions: actions,
        showCloseButton: showCloseButton,
        onClose: onClose ?? () => Navigator.of(context).pop(),
        headerColor: headerColor,
        headerContentColor: headerContentColor,
        contentPadding: contentPadding,
        width: width,
        maxHeight: maxHeight,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveHeaderColor = headerColor ?? theme.colorScheme.primaryContainer;
    final effectiveHeaderContentColor = headerContentColor ?? theme.colorScheme.onPrimaryContainer;

    Widget dialog = AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      titlePadding: EdgeInsets.zero,
      // Custom colored header
      title: title != null || headerIcon != null || showCloseButton
          ? Container(
              decoration: BoxDecoration(
                color: effectiveHeaderColor,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              padding: const EdgeInsets.fromLTRB(24, 16, 16, 16),
              child: Row(
                children: [
                  // Header icon if provided
                  if (headerIcon != null) ...[
                    Icon(
                      headerIcon,
                      color: effectiveHeaderContentColor,
                    ),
                    const SizedBox(width: 12),
                  ],
                  // Title text
                  if (title != null)
                    Expanded(
                      child: Text(
                        title!,
                        style: theme.textTheme.titleLarge?.copyWith(
                          color: effectiveHeaderContentColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  // Close button
                  if (showCloseButton)
                    IconButton(
                      icon: Icon(
                        Icons.close,
                        color: effectiveHeaderContentColor,
                      ),
                      onPressed: onClose ?? () => Navigator.of(context).pop(),
                      tooltip: 'Close',
                    ),
                ],
              ),
            )
          : null,
      content: content != null
          ? SingleChildScrollView(
              child: content,
            )
          : null,
      contentPadding: content != null ? contentPadding : EdgeInsets.zero,
      actions: actions,
      actionsPadding: actions != null && actions!.isNotEmpty
          ? const EdgeInsets.fromLTRB(16, 0, 16, 16)
          : EdgeInsets.zero,
    );

    // Apply constraints if provided
    if (width != null || maxHeight != null) {
      dialog = Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        insetPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
        child: ConstrainedBox(
          constraints: BoxConstraints(
            maxWidth: width ?? double.infinity,
            maxHeight: maxHeight ?? double.infinity,
          ),
          child: dialog,
        ),
      );
    }

    return dialog;
  }

  /// Get category icon based on category name
  static IconData getCategoryIcon(String? category) {
    if (category == null || category.isEmpty) {
      return Icons.lightbulb_outline;
    }

    final lowerCategory = category.toLowerCase();

    if (lowerCategory.contains('writing')) return Icons.edit_note;
    if (lowerCategory.contains('code') || lowerCategory.contains('programming')) return Icons.code;
    if (lowerCategory.contains('creative')) return Icons.auto_awesome;
    if (lowerCategory.contains('business')) return Icons.business;
    if (lowerCategory.contains('marketing')) return Icons.campaign;
    if (lowerCategory.contains('academic')) return Icons.school;
    if (lowerCategory.contains('social')) return Icons.people;
    if (lowerCategory.contains('email')) return Icons.email;

    // Default icon
    return Icons.lightbulb_outline;
  }
}
