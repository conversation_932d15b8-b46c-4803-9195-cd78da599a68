import 'package:flutter/material.dart';
import 'markdown_display.dart';

/// A widget that provides markdown editing and preview functionality
class MarkdownEditor extends StatefulWidget {
  /// The controller for the text field
  final TextEditingController controller;

  /// The label for the text field
  final String label;

  /// Minimum number of lines to show in edit mode
  final int? minLines;

  /// Maximum number of lines to show in edit mode
  final int? maxLines;

  /// Whether the field is read only
  final bool readOnly;

  /// Validation function
  final String? Function(String?)? validator;

  /// Callback when text changes
  final VoidCallback? onChanged;

  /// Whether to initially show in preview mode
  final bool initialPreviewMode;

  /// Whether to show the edit/preview toggle
  final bool showToggle;

  const MarkdownEditor({
    super.key,
    required this.controller,
    required this.label,
    this.minLines,
    this.maxLines,
    this.readOnly = false,
    this.validator,
    this.onChanged,
    this.initialPreviewMode = false,
    this.showToggle = true,
  });

  @override
  State<MarkdownEditor> createState() => MarkdownEditorState();
}

class MarkdownEditorState extends State<MarkdownEditor> {
  late bool _isPreviewMode;
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _isPreviewMode = widget.initialPreviewMode;

    // If readonly, default to preview mode
    if (widget.readOnly && !widget.initialPreviewMode) {
      _isPreviewMode = true;
    }
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  /// Inserts text at the current cursor position
  void insertTextAtCursor(String text) {
    if (widget.readOnly) return;

    final TextEditingController controller = widget.controller;
    final TextSelection selection = controller.selection;

    // If there's no valid selection, insert at the end
    final int start = selection.isValid ? selection.start : controller.text.length;
    final int end = selection.isValid ? selection.end : controller.text.length;

    final String currentText = controller.text;

    final String newText = currentText.replaceRange(
      start,
      end,
      text,
    );

    // Calculate new cursor position after insertion
    final int cursorPosition = start + text.length;

    controller.value = TextEditingValue(
      text: newText,
      selection: TextSelection.collapsed(offset: cursorPosition),
    );

    if (widget.onChanged != null) {
      widget.onChanged!();
    }

    // Ensure focus returns to the text field
    _focusNode.requestFocus();
  }

  /// Inserts markdown formatting around the selected text
  void insertMarkdownFormatting(String prefix, [String suffix = '']) {
    if (widget.readOnly) return;

    final TextEditingController controller = widget.controller;
    final TextSelection selection = controller.selection;

    if (!selection.isValid) {
      // If no selection, just insert prefix+suffix at cursor position
      insertTextAtCursor('$prefix$suffix');
      return;
    }

    final String selectedText = controller.text.substring(selection.start, selection.end);
    final String newText = '$prefix$selectedText$suffix';

    controller.value = TextEditingValue(
      text: controller.text.replaceRange(selection.start, selection.end, newText),
      selection: TextSelection.collapsed(offset: selection.start + newText.length),
    );

    if (widget.onChanged != null) {
      widget.onChanged!();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Edit/Preview toggle
        if (widget.showToggle)
          Row(
            children: [
              Text(widget.label, style: theme.textTheme.bodyLarge),
              const Spacer(),
              if (!widget.readOnly)
                SegmentedButton<bool>(
                  segments: const [
                    ButtonSegment(
                      value: false,
                      icon: Icon(Icons.edit_outlined),
                      label: Text('Edit'),
                    ),
                    ButtonSegment(
                      value: true,
                      icon: Icon(Icons.preview_outlined),
                      label: Text('Preview'),
                    ),
                  ],
                  selected: {_isPreviewMode},
                  onSelectionChanged: (Set<bool> newSelection) {
                    setState(() {
                      _isPreviewMode = newSelection.first;
                    });
                  },
                  style: SegmentedButton.styleFrom(
                    backgroundColor: colorScheme.surfaceContainerHighest,
                  ),
                ),
            ],
          ),
        if (widget.showToggle) const SizedBox(height: 8),

        // Formatting toolbar for edit mode
        if (!_isPreviewMode && !widget.readOnly) _buildFormatToolbar(),

        // Either show editor or preview based on mode
        Expanded(
          child: _isPreviewMode || widget.readOnly ? _buildPreviewMode() : _buildEditMode(),
        ),
      ],
    );
  }

  Widget _buildFormatToolbar() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            // Bold
            IconButton(
              icon: const Icon(Icons.format_bold),
              tooltip: 'Bold',
              onPressed: () => insertMarkdownFormatting('**', '**'),
            ),
            // Italic
            IconButton(
              icon: const Icon(Icons.format_italic),
              tooltip: 'Italic',
              onPressed: () => insertMarkdownFormatting('_', '_'),
            ),
            // Heading 1
            IconButton(
              icon: const Icon(Icons.title),
              tooltip: 'Heading 1',
              onPressed: () => insertMarkdownFormatting('# '),
            ),
            // Heading 2
            IconButton(
              icon: const Icon(Icons.text_fields),
              tooltip: 'Heading 2',
              onPressed: () => insertMarkdownFormatting('## '),
            ),
            // Bulleted list
            IconButton(
              icon: const Icon(Icons.format_list_bulleted),
              tooltip: 'Bulleted List',
              onPressed: () => insertMarkdownFormatting('- '),
            ),
            // Numbered list
            IconButton(
              icon: const Icon(Icons.format_list_numbered),
              tooltip: 'Numbered List',
              onPressed: () => insertMarkdownFormatting('1. '),
            ),
            // Link
            IconButton(
              icon: const Icon(Icons.link),
              tooltip: 'Insert Link',
              onPressed: () => insertMarkdownFormatting('[Link text](', ')'),
            ),
            // Code
            IconButton(
              icon: const Icon(Icons.code),
              tooltip: 'Insert Code',
              onPressed: () => insertMarkdownFormatting('`', '`'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEditMode() {
    return TextFormField(
      controller: widget.controller,
      focusNode: _focusNode,
      decoration: InputDecoration(
        border: const OutlineInputBorder(),
        filled: widget.readOnly,
        fillColor: widget.readOnly ? Colors.grey.shade100 : null,
      ),
      validator: widget.validator,
      readOnly: widget.readOnly,
      minLines: widget.minLines,
      maxLines: widget.maxLines,
      onChanged: (value) {
        if (widget.onChanged != null) {
          widget.onChanged!();
        }
      },
      keyboardType: TextInputType.multiline,
    );
  }

  Widget _buildPreviewMode() {
    // Calculate the approximate height based on minLines or maxLines
    const double lineHeight = 20.0; // Approximate line height
    final int effectiveMinLines = widget.minLines ?? 5;
    final int effectiveMaxLines = widget.maxLines ?? 30;

    final double minHeight = effectiveMinLines * lineHeight;
    final double maxHeight = effectiveMaxLines * lineHeight;

    return MarkdownDisplay(
      content: widget.controller.text,
      minHeight: minHeight,
      maxHeight: maxHeight,
    );
  }
}
