import 'package:flutter/material.dart';

/// A reusable card widget that displays an icon, title, and description.
///
/// This widget is designed for consistent presentation of information across
/// the application. It supports both light and dark themes automatically.
class PromzCard extends StatelessWidget {
  /// The icon to display on the left side of the card
  final IconData icon;

  /// The title text to display (will be shown in bold)
  final String title;

  /// The description text to display below the title
  final String description;

  /// Optional callback when the card is tapped
  final VoidCallback? onTap;

  /// Fixed height for the card
  final double height;

  /// Optional custom background color for the card
  final Color? customColor;

  /// Creates a PromzCard with the required icon, title, and description.
  ///
  /// The [onTap] parameter is optional. If provided, the card will be tappable.
  /// The default [height] is 88.0, which accommodates most content comfortably.
  const PromzCard({
    super.key,
    required this.icon,
    required this.title,
    required this.description,
    this.onTap,
    this.height = 88.0,
    this.customColor,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      color: customColor,
      child: InkWell(
        onTap: onTap,
        child: SizedBox(
          height: height,
          child: ListTile(
            leading: Icon(
              icon,
              size: 32,
              color: theme.colorScheme.primary,
            ),
            title: Text(
              title,
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            subtitle: Padding(
              padding: const EdgeInsets.only(top: 4.0),
              child: Text(description),
            ),
          ),
        ),
      ),
    );
  }
}
