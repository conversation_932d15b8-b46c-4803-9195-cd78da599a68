import 'package:flutter/material.dart';
import 'package:promz_common/promz_common.dart';

/// A widget for selecting and inserting entity templates
class EntityTemplateSelector extends StatelessWidget {
  /// Callback when an entity template is selected
  final void Function(String templateText) onTemplateSelected;

  const EntityTemplateSelector({
    super.key,
    required this.onTemplateSelected,
  });

  @override
  Widget build(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.add_circle_outline),
      tooltip: 'Insert Entity Template',
      onPressed: () {
        _showTemplateSelector(context);
      },
    );
  }

  void _showTemplateSelector(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Insert Entity Template'),
        content: SizedBox(
          width: 400,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(bottom: 16.0),
                child: Text(
                  'Finance Templates',
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Flexible(
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: FinanceTemplates.templates.length,
                  itemBuilder: (context, index) {
                    final template = FinanceTemplates.templates[index];
                    return ListTile(
                      title: Text(template.label),
                      subtitle: Text(template.description),
                      trailing: Text(
                        template.template,
                        style: TextStyle(
                          fontFamily: 'monospace',
                          color: colorScheme.primary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      onTap: () {
                        onTemplateSelected(template.template);
                        Navigator.of(context).pop();
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('CANCEL'),
          ),
        ],
      ),
    );
  }
}
