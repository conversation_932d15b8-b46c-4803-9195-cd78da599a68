import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:promz_common/promz_common.dart';

/// A widget that displays markdown content
class MarkdownDisplay extends StatelessWidget {
  static const _logName = 'MarkdownDisplay';

  /// The markdown content to display
  final String content;

  /// Minimum height of the container
  final double? minHeight;

  /// Maximum height of the container
  final double? maxHeight;

  const MarkdownDisplay({
    super.key,
    required this.content,
    this.minHeight,
    this.maxHeight,
  });

  @override
  Widget build(BuildContext context) {
    // Add debug logging
    appLog.debug(
        'MarkdownDisplay content: ${content.substring(0, content.length > 100 ? 100 : content.length)}...',
        name: _logName);

    // Calculate the approximate height based on lines if not provided
    const double lineHeight = 20.0; // Approximate line height
    final double effectiveMinHeight = minHeight ?? (5 * lineHeight);
    final double effectiveMaxHeight = maxHeight ?? (30 * lineHeight);

    // Create a markdown style sheet that matches the app's theme
    final markdownStyleSheet = MarkdownStyleSheet.fromTheme(Theme.of(context)).copyWith(
      textScaler: const TextScaler.linear(1.0),
      p: Theme.of(context).textTheme.bodyMedium,
      h1: Theme.of(context).textTheme.headlineMedium,
      h2: Theme.of(context).textTheme.titleLarge,
      h3: Theme.of(context).textTheme.titleMedium,
      h4: Theme.of(context).textTheme.titleSmall,
      h5: Theme.of(context).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.bold),
      h6: Theme.of(context).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.bold),
      em: const TextStyle(fontStyle: FontStyle.italic),
      strong: const TextStyle(fontWeight: FontWeight.bold),
      blockquote: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Colors.grey.shade700,
            fontStyle: FontStyle.italic,
          ),
      code: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontFamily: 'monospace',
            backgroundColor: Colors.grey.shade200,
          ),
    );

    return Container(
      constraints: BoxConstraints(
        minHeight: effectiveMinHeight,
        maxHeight: effectiveMaxHeight,
      ),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade400),
        borderRadius: BorderRadius.circular(4.0),
      ),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: MarkdownBody(
          data: content,
          styleSheet: markdownStyleSheet,
          selectable: true,
          onTapLink: (text, href, title) {
            // Handle link taps if needed
            if (href != null) {
              // Launch URL with url_launcher or show in WebView
            }
          },
        ),
      ),
    );
  }
}
