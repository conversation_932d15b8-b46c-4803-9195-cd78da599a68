import 'dart:io' show Platform;
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

/// A platform-aware progress indicator that displays a spinner appropriate for the current platform.
///
/// On iOS, it shows a CupertinoActivityIndicator, and on other platforms it shows a CircularProgressIndicator.
class PromzProgressIndicator extends StatelessWidget {
  /// The radius/size of the spinner
  final double size;

  /// Optional padding around the spinner
  final EdgeInsetsGeometry? padding;

  /// Color of the spinner (only applies to CircularProgressIndicator)
  final Color? color;

  /// Creates a platform-aware progress indicator.
  ///
  /// [size] controls the size/radius of the indicator.
  /// [padding] adds optional spacing around the indicator.
  /// [color] sets the color of the indicator (only for non-iOS platforms).
  const PromzProgressIndicator({
    super.key,
    this.size = 16.0,
    this.padding,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    final Widget indicator = Platform.isIOS
        ? CupertinoActivityIndicator(radius: size)
        : CircularProgressIndicator(
            strokeWidth: 3.0,
            color: color ?? Theme.of(context).colorScheme.primary,
          );

    if (padding != null) {
      return Padding(
        padding: padding!,
        child: indicator,
      );
    }

    return indicator;
  }
}
