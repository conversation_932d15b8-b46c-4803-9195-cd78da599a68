import 'package:flutter/material.dart';
import 'package:promz_common/promz_common.dart';

/// A widget that combines markdown editing with entity template insertion
class EnhancedMarkdownEditor extends StatelessWidget {
  /// The controller for the text field
  final TextEditingController controller;

  /// The label for the text field
  final String label;

  /// Minimum number of lines to show in edit mode
  final int? minLines;

  /// Maximum number of lines to show in edit mode
  final int? maxLines;

  /// Whether the field is read only
  final bool readOnly;

  /// Validation function
  final String? Function(String?)? validator;

  /// Callback when text changes
  final VoidCallback? onChanged;

  const EnhancedMarkdownEditor({
    super.key,
    required this.controller,
    required this.label,
    this.minLines,
    this.maxLines,
    this.readOnly = false,
    this.validator,
    this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    final markdownEditorKey = GlobalKey<MarkdownEditorState>();
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header row with entity template toolbar
        Row(
          children: [
            Text('Entity Templates:', style: theme.textTheme.labelLarge),
            const SizedBox(width: 8),
            EntityTemplateSelector(
              onTemplateSelected: (templateText) {
                _insertTemplate(markdownEditorKey.currentState, templateText);
              },
            ),
            const Spacer(),
          ],
        ),
        const SizedBox(height: 8),

        // Markdown editor with key for accessing its state
        MarkdownEditor(
          key: markdownEditorKey,
          controller: controller,
          label: label,
          minLines: minLines,
          maxLines: maxLines,
          readOnly: readOnly,
          validator: validator,
          onChanged: onChanged,
        ),
      ],
    );
  }

  /// Insert template at cursor position
  void _insertTemplate(MarkdownEditorState? editorState, String templateText) {
    if (editorState == null) return;

    editorState.insertTextAtCursor(templateText);

    // Trigger onChanged callback if provided
    if (onChanged != null) {
      onChanged!();
    }
  }
}
