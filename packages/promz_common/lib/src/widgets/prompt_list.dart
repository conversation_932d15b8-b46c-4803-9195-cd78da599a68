import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:promz_common/promz_common.dart';
import 'package:promz_common/llm/base_context_service.dart';

/// A reusable widget for displaying a list of prompts
///
/// This widget can be used in both client and admin apps, with additional
/// functionality for admin through the editMode parameter.
class PromptList extends ConsumerWidget {
  final String title;
  final List<DisplayItem> suggestedItems;
  final bool isLoading;
  final Function(DisplayItem) onPromptSelected;
  final Function(DisplayItem)? onPromptShare;
  final Function(String)? onKeywordSelected;
  final Function(DisplayItem)? onPromptEdit;
  final int? selectedIndex;
  final bool shrinkWrap;
  final ScrollPhysics? physics;
  final Widget? emptyStateWidget;
  final List<PromptModel>? _prompts; // Store original prompt models when using factory constructor
  final bool editMode; // New parameter for admin functionality
  final BaseContextService contextService; // Type-safe for both client and admin

  const PromptList({
    super.key,
    required this.title,
    required this.suggestedItems,
    required this.isLoading,
    required this.onPromptSelected,
    this.onPromptShare,
    this.onPromptEdit,
    this.onKeywordSelected,
    this.selectedIndex,
    this.shrinkWrap = false,
    this.physics,
    this.emptyStateWidget,
    this.editMode = false,
    required this.contextService,
  }) : _prompts = null;

  /// Private constructor for the factory method
  const PromptList._withPrompts({
    required this.title,
    required this.suggestedItems,
    required this.isLoading,
    required this.onPromptSelected,
    this.onPromptShare,
    this.onPromptEdit,
    this.onKeywordSelected,
    this.selectedIndex,
    this.shrinkWrap = false,
    this.physics,
    this.emptyStateWidget,
    required List<PromptModel> prompts,
    this.editMode = false,
    required this.contextService,
  }) : _prompts = prompts;

  /// Factory constructor to create from list of PromptModels
  factory PromptList.fromPromptModels({
    required String title,
    required List<PromptModel> prompts,
    required bool isLoading,
    required Function(DisplayItem) onPromptSelected,
    Function(DisplayItem)? onPromptShare,
    Function(DisplayItem)? onPromptEdit,
    Function(String)? onKeywordSelected,
    int? selectedIndex,
    bool shrinkWrap = false,
    ScrollPhysics? physics,
    Widget? emptyStateWidget,
    bool editMode = false,
    required BaseContextService contextService,
  }) {
    return PromptList._withPrompts(
      title: title,
      suggestedItems: const [], // Empty initially, we'll create from prompts in build
      isLoading: isLoading,
      onPromptSelected: onPromptSelected,
      onPromptShare: onPromptShare,
      onPromptEdit: onPromptEdit,
      onKeywordSelected: onKeywordSelected,
      selectedIndex: selectedIndex,
      shrinkWrap: shrinkWrap,
      physics: physics,
      emptyStateWidget: emptyStateWidget,
      prompts: prompts, // Store the prompts for use in build method
      editMode: editMode,
      contextService: contextService,
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    // First determine which items to transform - either from suggestedItems or by creating from _prompts
    List<DisplayItem> itemsToTransform = suggestedItems;

    // If we were created with prompts, create DisplayItems from them
    if (_prompts != null && _prompts!.isNotEmpty) {
      itemsToTransform =
          _prompts!.map((prompt) => DisplayItem.fromPromptModel(prompt, contextService)).toList();
    }

    // Now transform items with current variable values if the service supports it
    final displayItems = itemsToTransform.map((item) {
      // Use the transformWithValues method to handle template variables if available
      return item.transformWithValues(contextService);
    }).toList();

    // Show empty state if no items
    if (displayItems.isEmpty && emptyStateWidget != null) {
      return emptyStateWidget!;
    }

    // For empty list with no custom widget
    if (displayItems.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.search_off,
                size: 48,
                color: theme.colorScheme.tertiary,
              ),
              const SizedBox(height: 16),
              Text(
                'No prompts found.',
                style: theme.textTheme.titleMedium,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    // Create a column or list view based on shrinkWrap
    Widget content;
    if (shrinkWrap) {
      content = ListView.builder(
        shrinkWrap: true,
        physics: physics ?? const NeverScrollableScrollPhysics(),
        padding: EdgeInsets.zero,
        itemCount: displayItems.length,
        itemBuilder: (context, index) {
          final item = displayItems[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: 2),
            child: PromptCard.fromDisplayItem(
              displayItem: item,
              contextService: contextService,
              isSelected: selectedIndex == index,
              customColor: selectedIndex == index ? theme.colorScheme.primaryContainer : null,
              onTap: () => onPromptSelected(item),
              onShare: onPromptShare != null ? () => onPromptShare!(item) : null,
              onEdit: editMode && onPromptEdit != null ? () => onPromptEdit!(item) : null,
              showEditButton: editMode,
            ),
          );
        },
      );
    } else {
      content = Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ...displayItems.asMap().entries.map((entry) {
            final index = entry.key;
            final item = entry.value;
            return Padding(
              padding: const EdgeInsets.only(bottom: 2),
              child: PromptCard.fromDisplayItem(
                displayItem: item,
                contextService: contextService,
                isSelected: selectedIndex == index,
                customColor: selectedIndex == index ? theme.colorScheme.primaryContainer : null,
                onTap: () => onPromptSelected(item),
                onShare: onPromptShare != null ? () => onPromptShare!(item) : null,
                onEdit: editMode && onPromptEdit != null ? () => onPromptEdit!(item) : null,
                showEditButton: editMode,
              ),
            );
          }),
        ],
      );
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Theme.of(context).colorScheme.primary,
              ),
        ),
        const SizedBox(height: 8),
        if (shrinkWrap) content else Flexible(child: content),
      ],
    );
  }
}
