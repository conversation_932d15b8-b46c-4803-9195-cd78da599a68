import 'package:flutter/material.dart';
import 'page_section_title.dart';

/// A reusable page layout that provides consistent structure across different views.
///
/// This widget creates a standard page layout with a title section at the top,
/// an optional description, scrollable content area, and an optional action area
/// at the bottom.
class PromzPageView extends StatelessWidget {
  /// The title to display at the top of the page
  final String title;

  /// Optional emoji to display alongside the title
  final String? emoji;

  /// Optional description text to display below the title
  final String? description;

  /// The main content of the page
  final Widget content;

  /// Optional widget to display at the bottom of the page (buttons, pagination, etc.)
  final Widget? actions;

  /// Padding around the entire page content
  final EdgeInsetsGeometry padding;

  /// Whether the content should be scrollable
  final bool scrollable;

  /// Physics for the scroll view
  final ScrollPhysics? physics;

  /// Text style for the description
  final TextStyle? descriptionStyle;

  /// Creates a standard page layout.
  ///
  /// [title] is required and will be displayed prominently at the top.
  /// [emoji] is an optional emoji icon to display next to the title.
  /// [description] is optional text displayed below the title.
  /// [content] is the main content of the page.
  /// [actions] is an optional widget displayed at the bottom of the page.
  /// [padding] controls the padding around the entire content area.
  /// [scrollable] determines if the content should be in a scroll view.
  /// [physics] allows customization of the scroll behavior when [scrollable] is true.
  /// [descriptionStyle] allows customization of the description text style.
  const PromzPageView({
    super.key,
    required this.title,
    this.emoji,
    this.description,
    required this.content,
    this.actions,
    this.padding = const EdgeInsets.all(16.0),
    this.scrollable = true,
    this.physics,
    this.descriptionStyle,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    final contentWidget = Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        // Title section
        PageSectionTitle(
          title: title,
          emoji: emoji,
          padding: const EdgeInsets.only(bottom: 8.0),
        ),

        // Optional description
        if (description != null) ...[
          Text(
            description!,
            style: descriptionStyle ?? theme.textTheme.bodyLarge,
          ),
          const SizedBox(height: 24),
        ],

        // Main content
        Flexible(
          child: scrollable ? content : Expanded(child: content),
        ),

        // Optional action area
        if (actions != null) ...[
          const SizedBox(height: 16),
          actions!,
        ],
      ],
    );

    if (scrollable) {
      return Padding(
        padding: padding,
        child: SingleChildScrollView(
          physics: physics,
          child: contentWidget,
        ),
      );
    } else {
      return Padding(
        padding: padding,
        child: contentWidget,
      );
    }
  }
}
