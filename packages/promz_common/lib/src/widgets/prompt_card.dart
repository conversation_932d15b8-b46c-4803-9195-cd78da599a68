import 'package:flutter/material.dart';
import 'package:promz_common/promz_common.dart';

/// A standardized card for displaying prompts across the app
///
/// This widget encapsulates the common UI elements for prompt display:
/// - Consistent styling with standard Card
/// - Relevance score visual indicators
/// - Proper handling of transformed variable text
/// - Optional edit button for admin functionality
class PromptCard extends StatelessWidget {
  /// The display item to render (contains prompt information)
  final DisplayItem? displayItem;

  /// The prompt model to render (alternative to displayItem)
  final PromptModel? promptModel;

  /// The title text (used when neither displayItem nor promptModel is provided)
  final String? title;

  /// The subtitle text (used when neither displayItem nor promptModel is provided)
  final String? subtitle;

  /// Callback when the card is tapped
  final VoidCallback? onTap;

  /// Optional color override for the card
  final Color? customColor;

  /// Whether this card is currently selected
  final bool isSelected;

  /// The client context service instance - needs to support transformTitleForDisplay method
  final dynamic contextService;

  /// Callback when the share button is tapped
  final VoidCallback? onShare;

  /// Callback when the edit button is tapped (for admin functionality)
  final VoidCallback? onEdit;

  /// Whether to show the edit button
  final bool showEditButton;

  /// Constructor for creating a card from a DisplayItem
  const PromptCard.fromDisplayItem({
    super.key,
    required this.displayItem,
    this.onTap,
    this.onShare,
    this.onEdit,
    this.customColor,
    this.isSelected = false,
    this.contextService,
    this.showEditButton = false,
  })  : promptModel = null,
        title = null,
        subtitle = null;

  /// Constructor for creating a card from a PromptModel
  const PromptCard.fromPromptModel({
    super.key,
    required this.promptModel,
    this.onTap,
    this.onShare,
    this.onEdit,
    this.customColor,
    this.isSelected = false,
    this.contextService,
    this.showEditButton = false,
  })  : displayItem = null,
        title = null,
        subtitle = null;

  /// Constructor for creating a card with raw title/subtitle
  const PromptCard.raw({
    super.key,
    required this.title,
    this.subtitle,
    this.onTap,
    this.onShare,
    this.onEdit,
    this.customColor,
    this.isSelected = false,
    this.contextService,
    this.showEditButton = false,
  })  : displayItem = null,
        promptModel = null;

  /// Check if the clientContextService can transform titles
  bool get _canTransformTitles {
    return contextService != null &&
        (contextService is! String) && // Ensure it's not a string or primitive
        contextService.transformTitleWithValues is Function;
  }

  /// Helper to determine the final color based on relevance score and custom color
  Color? _determineCardColor(BuildContext context) {
    final theme = Theme.of(context);

    // If a custom color is provided, use that
    if (customColor != null) {
      return customColor;
    }

    // If selected, use the primary container color
    if (isSelected) {
      return theme.colorScheme.primaryContainer;
    }

    // Otherwise, use the default card color
    return null;
  }

  /// Get the display text, transformed if needed
  String _getDisplayText(BuildContext context) {
    if (displayItem != null) {
      return displayItem!.displayText;
    } else if (promptModel != null) {
      // If we have a clientContextService that can transform titles, use it
      if (_canTransformTitles) {
        try {
          return contextService.transformTitleWithValues(
              promptModel!.title, contextService.variableValues ?? {});
        } catch (e) {
          // If transformation fails, fall back to the original title
          return promptModel!.title;
        }
      }
      return promptModel!.title;
    } else {
      return title ?? '';
    }
  }

  /// Get the display subtitle, transformed if needed
  String _getDisplaySubtitle(BuildContext context) {
    if (displayItem != null) {
      return displayItem!.subtitle ?? '';
    } else if (promptModel != null) {
      return promptModel!.subtitle ?? '';
    } else {
      return subtitle ?? '';
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      color: _determineCardColor(context),
      margin: EdgeInsets.zero,
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Row(
            children: [
              // Quote icon for prompts
              Icon(
                Icons.format_quote,
                color: theme.colorScheme.primary,
                size: 24,
              ),
              const SizedBox(width: 16),

              // Text content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _getDisplayText(context),
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (_getDisplaySubtitle(context).isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 4.0),
                        child: Text(
                          _getDisplaySubtitle(context),
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                  ],
                ),
              ),

              // Edit button (for admin functionality)
              if (showEditButton && onEdit != null)
                IconButton(
                  icon: Icon(
                    Icons.edit,
                    color: theme.colorScheme.primary,
                    size: 20,
                  ),
                  onPressed: onEdit,
                  tooltip: 'Edit prompt',
                ),

              // Share button
              if (onShare != null)
                IconButton(
                  icon: Icon(
                    Icons.share,
                    color: theme.colorScheme.primary,
                    size: 20,
                  ),
                  onPressed: onShare,
                  tooltip: 'Share prompt',
                ),
            ],
          ),
        ),
      ),
    );
  }
}
