import 'dart:io' show Platform;
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

/// A platform-aware button that automatically renders as a CupertinoButton on iOS
/// and as an ElevatedButton on other platforms.
class PromzButton extends StatelessWidget {
  /// The text label to display on the button
  final String label;

  /// Function called when the button is pressed
  final VoidCallback? onPressed;

  /// Optional icon to display at the start of the button
  final IconData? icon;

  /// Optional width to constrain the button
  final double? width;

  /// Optional height for the button
  final double? height;

  /// Background color (optional, uses theme's primary color by default)
  final Color? backgroundColor;

  /// Foreground/text color (optional, uses theme's onPrimary color by default)
  final Color? foregroundColor;

  /// Creates a platform-aware button.
  ///
  /// [label] is the text to display on the button.
  /// [onPressed] is called when the button is tapped. If null, the button appears disabled.
  /// [icon] if provided, displays an icon before the label text.
  /// [width] if provided, constrains the button width.
  /// [height] if provided, sets the button height.
  /// [backgroundColor] sets a custom background color (used only for Material buttons).
  /// [foregroundColor] sets a custom text/icon color.
  const PromzButton({
    super.key,
    required this.label,
    this.onPressed,
    this.icon,
    this.width,
    this.height,
    this.backgroundColor,
    this.foregroundColor,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Platform-specific button implementation
    Widget button = Platform.isIOS ? _buildCupertinoButton(context) : _buildMaterialButton(theme);

    // Apply width constraint if specified
    if (width != null) {
      button = SizedBox(width: width, child: button);
    }

    return button;
  }

  /// Build a Cupertino-styled button for iOS
  Widget _buildCupertinoButton(BuildContext context) {
    final child = icon != null
        ? Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(icon, size: 18),
              const SizedBox(width: 8),
              Text(label),
            ],
          )
        : Text(label);

    return CupertinoButton.filled(
      onPressed: onPressed,
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 10.0),
      minSize: height ?? 36.0,
      child: child,
    );
  }

  /// Build a Material-styled button for Android and other platforms
  Widget _buildMaterialButton(ThemeData theme) {
    final effectiveBackgroundColor = backgroundColor ?? theme.colorScheme.primary;
    final effectiveForegroundColor = foregroundColor ?? theme.colorScheme.onPrimary;

    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: effectiveBackgroundColor,
        foregroundColor: effectiveForegroundColor,
        minimumSize: Size(0, height ?? 36.0),
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 0),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      child: icon != null
          ? Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(icon, size: 18),
                const SizedBox(width: 8),
                Text(label),
              ],
            )
          : Text(label),
    );
  }
}
