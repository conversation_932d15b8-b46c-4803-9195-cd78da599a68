import 'dart:io' show Platform;
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:promz_common/strings.dart';

/// Enum for supported social auth providers
enum SocialAuthProvider {
  google,
  microsoft,
  apple,
}

/// A specialized button for social authentication that follows platform-specific design guidelines.
///
/// This widget renders auth buttons according to each provider's brand guidelines
/// and adapts to platform-specific styling (Material on Android, Cupertino on iOS).
class SocialAuthButton extends StatelessWidget {
  /// The social auth provider for this button
  final SocialAuthProvider provider;

  /// Callback when the button is pressed
  final VoidCallback? onPressed;

  /// Whether the button is in a loading state
  final bool isLoading;

  /// Optional override for the button text
  final String? labelOverride;

  /// Optional background color override
  final Color? backgroundColor;

  /// Optional text color override
  final Color? textColor;

  /// Creates a social authentication button.
  ///
  /// [provider] determines which social provider styling to use.
  /// [onPressed] is called when the button is tapped. If null, the button appears disabled.
  /// [isLoading] if true, shows a loading indicator instead of the provider icon.
  /// [labelOverride] optional text override for the button label.
  const SocialAuthButton({
    super.key,
    required this.provider,
    this.onPressed,
    this.isLoading = false,
    this.labelOverride,
    this.backgroundColor,
    this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    return Platform.isIOS ? _buildCupertinoButton(context) : _buildMaterialButton(context);
  }

  /// Builds a Cupertino-styled button for iOS
  Widget _buildCupertinoButton(BuildContext context) {
    final providerConfig = _getProviderConfig();
    final effectiveBackgroundColor = backgroundColor ?? providerConfig.backgroundColor;
    final effectiveForegroundColor = textColor ?? providerConfig.foregroundColor;

    return Container(
      width: double.infinity,
      height: 50,
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      decoration: BoxDecoration(
        color: effectiveBackgroundColor,
        borderRadius: BorderRadius.circular(8),
      ),
      child: CupertinoButton(
        onPressed: isLoading ? null : onPressed,
        padding: EdgeInsets.zero,
        borderRadius: BorderRadius.circular(8),
        child: _buildButtonContent(context, effectiveForegroundColor),
      ),
    );
  }

  /// Builds a Material-styled button for Android and other platforms
  Widget _buildMaterialButton(BuildContext context) {
    final providerConfig = _getProviderConfig();
    final effectiveBackgroundColor = backgroundColor ?? providerConfig.backgroundColor;
    final effectiveForegroundColor = textColor ?? providerConfig.foregroundColor;

    return Container(
      width: double.infinity,
      height: 50,
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: effectiveBackgroundColor,
          foregroundColor: effectiveForegroundColor,
          elevation: 1,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          padding: EdgeInsets.zero,
        ),
        child: _buildButtonContent(context, effectiveForegroundColor),
      ),
    );
  }

  /// Builds the content of the button (icon + label)
  Widget _buildButtonContent(BuildContext context, Color foregroundColor) {
    final providerConfig = _getProviderConfig();
    final buttonLabel = labelOverride ?? providerConfig.label;

    return Stack(
      alignment: Alignment.center,
      children: [
        // Label
        Text(
          isLoading ? 'Signing in...' : buttonLabel,
          textAlign: TextAlign.center,
          style: TextStyle(
            color: foregroundColor,
            fontWeight: FontWeight.w500,
            fontSize: 16,
          ),
        ),

        // Icon or loading indicator on the left
        Positioned(
          left: 16,
          child: isLoading
              ? SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(foregroundColor),
                  ),
                )
              : providerConfig.iconWidget,
        ),
      ],
    );
  }

  /// Gets the configuration for the specified provider
  _ProviderConfig _getProviderConfig() {
    switch (provider) {
      case SocialAuthProvider.google:
        return _ProviderConfig(
          label: Strings.continueWithGoogle,
          backgroundColor: const Color(0xFFFFFFFF),
          foregroundColor: const Color(0xFF000000),
          iconWidget: const Icon(
            Icons.g_mobiledata,
            color: Color(0xFF4285F4),
            size: 24,
          ),
        );

      case SocialAuthProvider.microsoft:
        return _ProviderConfig(
          label: Strings.continueWithMicrosoft,
          backgroundColor: const Color(0xFF2F2F2F),
          foregroundColor: const Color(0xFFFFFFFF),
          iconWidget: const Icon(
            Icons.window,
            color: Color(0xFF0078D4),
            size: 20,
          ),
        );

      case SocialAuthProvider.apple:
        return _ProviderConfig(
          label: Strings.continueWithApple,
          backgroundColor: const Color(0xFF000000),
          foregroundColor: const Color(0xFFFFFFFF),
          iconWidget: const Icon(
            Icons.apple,
            color: Colors.white,
            size: 20,
          ),
        );
    }
  }
}

/// Helper class to store provider-specific configuration
class _ProviderConfig {
  final String label;
  final Color backgroundColor;
  final Color foregroundColor;
  final Widget iconWidget;

  const _ProviderConfig({
    required this.label,
    required this.backgroundColor,
    required this.foregroundColor,
    required this.iconWidget,
  });
}
