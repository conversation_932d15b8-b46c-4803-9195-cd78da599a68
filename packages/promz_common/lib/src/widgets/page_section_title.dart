import 'package:flutter/material.dart';

/// A styled section title widget for consistent heading display across the app.
///
/// This widget provides a consistent style for section titles, with optional emoji prefixes.
class PageSectionTitle extends StatelessWidget {
  /// The title text to display
  final String title;

  /// Optional emoji prefix to show before the title
  final String? emoji;

  /// Optional custom style to override the default styling
  final TextStyle? style;

  /// Optional padding around the title
  final EdgeInsetsGeometry padding;

  /// Creates a styled section title.
  ///
  /// [title] is the text of the section title.
  /// [emoji] is an optional emoji prefix to display before the title.
  /// [style] allows customization of the text style.
  /// [padding] controls the spacing around the title (defaults to 16px all around).
  const PageSectionTitle({
    super.key,
    required this.title,
    this.emoji,
    this.style,
    this.padding = const EdgeInsets.only(bottom: 16.0),
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Combine emoji and title if emoji is provided
    final displayText = emoji != null ? '$emoji $title' : title;

    // Use provided style or default to theme's titleLarge with primary color and bold
    final effectiveStyle = style ??
        theme.textTheme.titleLarge?.copyWith(
          color: theme.colorScheme.primary,
          fontWeight: FontWeight.bold,
        );

    return Padding(
      padding: padding,
      child: Text(
        displayText,
        style: effectiveStyle,
      ),
    );
  }
}
