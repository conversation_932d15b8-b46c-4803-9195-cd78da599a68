/// Log levels for the application
///
/// Defines the severity levels for logging messages
enum LogLevel {
  /// Detailed information, typically useful for debugging
  debug,

  /// General information about application operation
  info,

  /// Potentially harmful situations that might lead to an error
  warning,

  /// Error events that might still allow the application to continue
  error,

  /// No logging
  none,
}
