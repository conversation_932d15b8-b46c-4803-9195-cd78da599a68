import 'dart:developer' as dev;
import 'package:flutter/foundation.dart';
import 'log_level.dart';

final appLog = AppLogger(defaultName: 'Promz');

/// AppLogger provides logging functionality with different log levels
///
/// This class supports both static and instance methods for logging,
/// and includes features for structured logging and test log level configuration.
class AppLogger {
  /// The default name to use for logs when no name is provided
  final String defaultName;

  /// Internal test log level for filtering logs during tests
  static LogLevel? _testLogLevel;

  /// Private constructor for singleton pattern
  AppLogger._({this.defaultName = 'Promz'});

  /// Factory constructor to get instance with custom default name
  factory AppLogger({String defaultName = 'Promz'}) {
    return AppLogger._(defaultName: defaultName);
  }

  /// Sets the log level for tests and returns the previous level
  ///
  /// This is useful for controlling log output during automated tests
  static LogLevel? setLogLevelForTest(LogLevel level) {
    var oldLevel = _testLogLevel;
    _testLogLevel = level;
    return oldLevel;
  }

  /// Suppresses all error logs for tests and returns the previous level
  ///
  /// This is useful for ignoring expected error logs during tests
  static LogLevel? suppressErrorsForTest() {
    return setLogLevelForTest(LogLevel.none);
  }

  /// Main logging method that handles all log types
  ///
  /// Parameters:
  /// - [message]: The message to log
  /// - [name]: Optional name/tag for the log (defaults to 'Promz')
  /// - [error]: Optional error object to include
  /// - [stackTrace]: Optional stack trace to include
  /// - [level]: Log level (defaults to info)
  /// - [data]: Optional structured data to include with the log
  static void log(
    String message, {
    String? name,
    Object? error,
    StackTrace? stackTrace,
    LogLevel level = LogLevel.info,
    Map<String, dynamic>? data,
  }) {
    // In test mode, only show logs at or above the test log level
    if (_testLogLevel != null && level.index < _testLogLevel!.index) {
      return;
    }

    // Skip debug logs in release mode
    if (!kDebugMode && level == LogLevel.debug) return;

    // Add data to message if provided
    final messageWithData = data != null
        ? '$message - ${data.entries.map((e) => '${e.key}: ${e.value}').join(', ')}'
        : message;

    final timestamp = DateTime.now().toIso8601String();
    final prefix = '${name ?? 'Promz'}[$level]';

    // Only log to console if it meets the test level requirement
    if (_testLogLevel == null || level.index >= _testLogLevel!.index) {
      debugPrint('$timestamp $prefix: $messageWithData');
      if (error != null) {
        debugPrint('$timestamp $prefix Error: $error');
        if (stackTrace != null) {
          debugPrint('$timestamp $prefix StackTrace: $stackTrace');
        }
      }

      // Also log using dart:developer for IDE console
      dev.log(
        messageWithData,
        name: name ?? 'Promz',
        error: error,
        stackTrace: stackTrace,
        level: _getLogLevel(level),
        time: DateTime.now(),
      );
    }
  }

  /// Converts LogLevel enum to int values used by dart:developer
  static int _getLogLevel(LogLevel level) {
    switch (level) {
      case LogLevel.debug:
        return 500;
      case LogLevel.info:
        return 800;
      case LogLevel.warning:
        return 900;
      case LogLevel.error:
        return 1000;
      case LogLevel.none:
        return 2000;
    }
  }

  /// Logs a debug message
  void debug(
    String message, {
    String? name,
    Object? error,
    StackTrace? stackTrace,
    Map<String, dynamic>? data,
  }) {
    AppLogger.log(
      message,
      name: name ?? defaultName,
      error: error,
      stackTrace: stackTrace,
      level: LogLevel.debug,
      data: data,
    );
  }

  /// Logs an info message
  void info(
    String message, {
    String? name,
    Object? error,
    StackTrace? stackTrace,
    Map<String, dynamic>? data,
  }) {
    AppLogger.log(
      message,
      name: name ?? defaultName,
      error: error,
      stackTrace: stackTrace,
      level: LogLevel.info,
      data: data,
    );
  }

  /// Logs a warning message
  void warning(
    String message, {
    String? name,
    Object? error,
    StackTrace? stackTrace,
    Map<String, dynamic>? data,
  }) {
    AppLogger.log(
      message,
      name: name ?? defaultName,
      error: error,
      stackTrace: stackTrace,
      level: LogLevel.warning,
      data: data,
    );
  }

  /// Logs an error message
  void error(
    String message, {
    String? name,
    Object? error,
    StackTrace? stackTrace,
    Map<String, dynamic>? data,
  }) {
    AppLogger.log(
      message,
      name: name ?? defaultName,
      error: error,
      stackTrace: stackTrace,
      level: LogLevel.error,
      data: data,
    );
  }
}
