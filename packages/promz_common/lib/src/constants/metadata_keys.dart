/// Defines constants for common metadata keys used across services.
class MetadataKeys {
  // Common
  static const String id = 'id';
  static const String type = 'type';
  static const String content = 'content';
  static const String title = 'title';
  static const String url = 'url';
  static const String timestamp = 'timestamp';
  static const String variables = 'variables'; // Key for the nested variables map

  // File/Attachment specific
  static const String fileName = 'fileName';
  static const String mimeType = 'mimeType';
  static const String filePath = 'filePath';

  // News specific
  static const String finalUrl = 'finalUrl';
  static const String excerpt = 'excerpt'; // Also used for description
  static const String siteName = 'siteName';
  static const String author = 'author';
  static const String html = 'html'; // Raw HTML content if available

  // YouTube specific
  static const String videoId = 'videoId';
  static const String watchUrl = 'watchUrl';
  static const String embedUrl = 'embedUrl';
  static const String thumbnailUrl = 'thumbnailUrl';
  static const String channelName = 'channelName';
  static const String description = 'description';

  // Finance specific
  static const String symbol = 'symbol';
  static const String companyName = 'companyName';
  static const String cleanCompanyName = 'cleanCompanyName';
  static const String entitySubtype = 'entitySubtype'; // e.g., 'stock', 'company'

  // Error specific
  static const String error = 'error';
}
