import 'package:promz_common/promz_common.dart';

/// Check if a string is a valid URL
bool isValidUrl(String text) {
  if (text.isEmpty) return false;

  try {
    final uri = Uri.parse(text.trim());
    return uri.hasScheme && (uri.scheme == 'http' || uri.scheme == 'https');
  } catch (_) {
    return false;
  }
}

/// Regular expression for matching HTTP or HTTPS URLs in text.
/// Also matches www. prefixed URLs that don't have a scheme.
final RegExp urlRegex = RegExp(
  r'https?://[^\s/$.?#].[^\s]*|www\.[^\s/$.?#].[^\s]*',
  caseSensitive: false,
);

/// Extracts the first URL from a text string.
///
/// Returns null if no URL is found.
/// Automatically adds https:// prefix to www. URLs.
String? extractFirstUrl(String text) {
  if (text.isEmpty) return null;

  final match = urlRegex.firstMatch(text);
  if (match == null) return null;

  String url = match.group(0)!;

  // Add https:// prefix to www. URLs
  if (url.startsWith('www.')) {
    url = 'https://$url';
  }

  return url;
}

/// Checks if the text contains a URL.
bool containsUrl(String text) {
  return urlRegex.hasMatch(text);
}

/// Checks if text is exactly a URL with nothing else
bool isExactlyUrl(String text) {
  final trimmed = text.trim();
  final url = extractFirstUrl(trimmed);
  return url != null && url.length == trimmed.length;
}

/// Checks if a given host is a Promz domain (www.promz.ai or promz.ai).
bool isPromzDomain(String? host) {
  if (host == null) return false;
  final lowerHost = host.toLowerCase();
  return lowerHost == 'www.promz.ai' || lowerHost == 'promz.ai';
}

/// Extracts all URLs from text content
///
/// Returns a list of all URLs found in the content
List<String> extractAllUrls(String content) {
  if (content.isEmpty) return [];

  final matches = urlRegex.allMatches(content);
  return matches.map((match) {
    String url = match.group(0)!;

    // Add https:// prefix to www. URLs
    if (url.startsWith('www.')) {
      url = 'https://$url';
    }

    return url;
  }).toList();
}

/// Checks if the content follows the "Title + URL" pattern common in shared articles.
///
bool matchesTitleUrlPattern(String content) {
  // Handle multi-line content by joining with spaces
  final normalizedContent = content.trim().replaceAll(RegExp(r'\s+'), ' ');

  final match = urlRegex.firstMatch(normalizedContent);
  if (match == null) return false;

  // Get URL and its position
  final url = match.group(0) ?? '';
  final urlStartIndex = match.start;

  // Check if URL is a significant part of the content
  final contentLength = normalizedContent.length;
  final urlLength = url.length;

  // If URL is more than 15% of the content, it's likely significant
  final isUrlSignificant = urlLength / contentLength > 0.15;

  // Check if URL is near the end (in the last 40% of the content)
  final isUrlNearEnd = urlStartIndex > contentLength * 0.6;

  // If URL is near the end or significant, check the text before it
  if ((isUrlNearEnd || isUrlSignificant) && urlStartIndex > 0) {
    final textBeforeUrl = normalizedContent.substring(0, urlStartIndex).trim();

    // Check if the text before URL looks like a title
    // - Has reasonable length (not just a few characters)
    // - Contains spaces (likely multiple words)
    // - Not too long (< 300 characters)
    // - Has proper capitalization (at least first letter is capital)
    final hasReasonableLength = textBeforeUrl.length > 10;
    final containsSpaces = textBeforeUrl.contains(' ');
    final isNotTooLong = textBeforeUrl.length < 300;
    final hasProperCapitalization =
        textBeforeUrl.isNotEmpty && textBeforeUrl[0] == textBeforeUrl[0].toUpperCase();

    // More flexible check - require most but not all conditions
    return (hasReasonableLength && containsSpaces && (isNotTooLong || hasProperCapitalization));
  }

  return false;
}

/// Extract a human-readable site name from a URL
///
/// Examples:
/// - https://www.nytimes.com/article -> "New York Times"
/// - https://www.bbc.co.uk/news -> "BBC"
/// - https://medium.com/article -> "Medium"
///
/// This is useful for providing a fallback site name when metadata extraction fails
String extractSiteName(String url) {
  try {
    final uri = Uri.parse(url.trim());
    final host = uri.host.toLowerCase();

    // Handle common domains with special formatting
    final knownDomains = <String, String>{
      'nytimes.com': 'New York Times',
      'washingtonpost.com': 'Washington Post',
      'wsj.com': 'Wall Street Journal',
      'bbc.com': 'BBC',
      'bbc.co.uk': 'BBC',
      'theguardian.com': 'The Guardian',
      'cnn.com': 'CNN',
      'foxnews.com': 'Fox News',
      'npr.org': 'NPR',
      'reuters.com': 'Reuters',
      'bloomberg.com': 'Bloomberg',
      'apnews.com': 'AP News',
      'aljazeera.com': 'Al Jazeera',
      'ft.com': 'Financial Times',
    };

    // Check for known domains
    for (final entry in knownDomains.entries) {
      if (host.endsWith(entry.key)) {
        return entry.value;
      }
    }

    // For unknown domains, extract and format the domain name
    // Remove www. prefix if present
    String domain = host.startsWith('www.') ? host.substring(4) : host;

    // Extract the main part of the domain (before the TLD)
    final parts = domain.split('.');
    if (parts.length > 1) {
      domain = parts[parts.length - 2]; // Get the part before the TLD
    }

    // Convert to title case and handle special cases
    if (domain.length <= 3) {
      // For short domains like 'cnn', 'bbc', convert to uppercase
      return domain.toUpperCase();
    } else {
      // For longer domains, capitalize first letter of each word, replacing hyphens
      return domain.split('-').map((word) {
        if (word.isEmpty) return '';
        return word[0].toUpperCase() + word.substring(1);
      }).join(' ');
    }
  } catch (e) {
    appLog.warning('Error extracting site name from URL: $url', error: e);
    return 'Web Site';
  }
}
