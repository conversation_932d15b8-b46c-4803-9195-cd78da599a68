import 'package:promz_common/promz_common.dart';

/// Utility class for handling template operations
class TemplateUtils {
  static const _logName = 'TemplateUtils';

  /// Regular expressions for template detection
  static final RegExp fullTemplateRegex = RegExp(r'\{\{([A-Za-z_]+):([^}]+)\}\}');
  static final RegExp shortTemplateRegex = RegExp(r'\{\{([A-Za-z0-9_]+)\}\}');
  static final _templateRegex = RegExp(r'\{\{(([A-Z_]+):)?([A-Z0-9_]+)\}\}');
  static final _prefixedTemplateRegex = RegExp(r'\{#([a-zA-Z0-9]+)\}(([A-Z_]+):)?([A-Z0-9_]+)');

  /// Transform variable name into a readable format
  static String makeReadable(String variableName) {
    // Replace underscores with spaces
    var result = variableName.replaceAll('_', ' ');

    // Convert to title case
    result = toTitleCase(result);

    // Add an article ('a' or 'an') based on first letter
    final firstChar = result.isNotEmpty ? result[0].toLowerCase() : '';
    final article = 'aeiou'.contains(firstChar) ? 'an' : 'a';

    return '$article $result';
  }

  /// Convert text to title case
  static String toTitleCase(String text) {
    if (text.isEmpty) return text;

    return text.toLowerCase().split(' ').map((word) {
      if (word.isEmpty) return '';
      return word[0].toUpperCase() + word.substring(1);
    }).join(' ');
  }

  /// Check if text contains any templates
  static bool containsTemplates(String text) {
    return fullTemplateRegex.hasMatch(text) || shortTemplateRegex.hasMatch(text);
  }

  /// Extract all template variables from text
  static List<TemplateMatch> extractTemplates(String text) {
    appLog.debug('START: Extracting templates from text of length ${text.length}', name: _logName);

    final List<TemplateMatch> templates = [];

    // Extract standard templates using {{TYPE:NAME}} pattern
    final matches = _templateRegex.allMatches(text);
    for (final match in matches) {
      final type = match.group(2);
      final name = match.group(3)!;

      templates.add(TemplateMatch(
        fullMatch: match.group(0)!,
        name: name,
        type: type,
        startPosition: match.start,
        endPosition: match.end,
        isShortForm: type == null,
        prefix: null, // Standard templates have no prefix
      ));
    }

    // Extract prefixed templates using {#prefix}TYPE:NAME pattern
    final prefixedMatches = _prefixedTemplateRegex.allMatches(text);
    for (final match in prefixedMatches) {
      final prefix = match.group(1);
      final type = match.group(3);
      final name = match.group(4)!;

      templates.add(TemplateMatch(
        fullMatch: match.group(0)!,
        name: name,
        type: type,
        startPosition: match.start,
        endPosition: match.end,
        isShortForm: type == null,
        prefix: prefix,
      ));
    }

    appLog.debug('END: Extracted ${templates.length} templates', name: _logName);
    return templates;
  }

  /// Create a template string in the full format
  static String createFullTemplate(String type, String name) {
    return '{{$type:$name}}';
  }

  /// Create a template string in the short format
  static String createShortTemplate(String name) {
    return '{{$name}}';
  }

  /// Replace a template in text at specific positions
  static String replaceTemplateAtPosition(
    String text,
    int startPosition,
    int endPosition,
    String replacement,
  ) {
    if (startPosition < 0 || endPosition > text.length || startPosition >= endPosition) {
      appLog.error(
          'Invalid position range: start=$startPosition, end=$endPosition, text length=${text.length}',
          name: _logName);
      return text;
    }

    return text.substring(0, startPosition) + replacement + text.substring(endPosition);
  }

  /// Replace templates in text with actual values
  static String replaceTemplatesWithValues(String text, Map<String, String> values) {
    String result = text;

    // Extract all templates
    final templates = extractTemplates(text);

    // Sort by position in reverse order to avoid position shifts
    templates.sort((a, b) => b.startPosition.compareTo(a.startPosition));

    // Replace each template with its value if available
    for (final template in templates) {
      final value = values[template.name];
      if (value != null) {
        result = replaceTemplateAtPosition(
          result,
          template.startPosition,
          template.endPosition,
          value,
        );
      }
    }

    return result;
  }

  /// Transform a title for display by replacing template patterns with user-friendly text
  static String transformTitleForDisplay(String title) {
    // Extract templates using regex
    String result = title;
    final regex = RegExp(r'\{\{([^:}]+)(?::([^}]+))?\}\}');
    final matches = regex.allMatches(title).toList();

    // Replace templates with display text in reverse order to avoid position shifts
    for (int i = matches.length - 1; i >= 0; i--) {
      final match = matches[i];
      final category = match.group(1);
      final name = match.group(2) ?? category;

      String displayText;
      if (category != null && name != null) {
        // Use VariableManager to get display text
        displayText = EntityUtils.getDisplayName(variableName: name);
      } else {
        // Fallback if we can't extract properly
        displayText = name ?? '[?]';
      }

      result = result.replaceRange(match.start, match.end, displayText);
    }

    return result;
  }

  /// Transform a title by replacing template patterns with actual values
  static String transformTitleWithValues(String title, Map<String, String> values) {
    if (values.isEmpty) {
      return title;
    }

    String result = title;

    // Extract all templates
    final templates = extractTemplates(title);

    // Sort templates by position in reverse order to avoid position shifts
    templates.sort((a, b) => b.startPosition.compareTo(a.startPosition));

    // Replace each template with its value if available
    for (final template in templates) {
      // First try direct match with template name
      String? value = values[template.name];

      // If not found, try with canonical name
      if (value == null) {
        final canonicalName = EntityUtils.getCanonicalName(template.name);
        value = values[canonicalName];
      }

      // If still not found and it's a finance variable, try all finance synonyms
      if (value == null && template.type?.toUpperCase() == 'FINANCE') {
        // Try all finance synonyms
        for (final entry in values.entries) {
          if (EntityUtils.isFinanceVariable(entry.key)) {
            value = entry.value;
            break;
          }
        }
      }

      // If we found a value, replace the template
      if (value != null) {
        result = replaceTemplateAtPosition(
          result,
          template.startPosition,
          template.endPosition,
          value,
        );
      }
    }

    return result;
  }
}

/// Represents a matched template in text
class TemplateMatch {
  /// The type of the template (e.g., 'FINANCE', 'LOCATION')
  final String? type;

  /// The name of the variable (e.g., 'SP500_SYMBOL', 'CITY')
  final String name;

  /// The full template match (e.g., '{{FINANCE:SP500_SYMBOL}}' or '{{CITY}}')
  final String fullMatch;

  /// Start position in the original text
  final int startPosition;

  /// End position in the original text
  final int endPosition;

  /// Whether this is a short form template ({{NAME}} vs {{TYPE:NAME}})
  final bool isShortForm;

  /// New field for prefix
  final String? prefix;

  const TemplateMatch({
    required this.name,
    required this.fullMatch,
    required this.startPosition,
    required this.endPosition,
    required this.isShortForm,
    this.type,
    this.prefix, // Added prefix parameter
  });

  @override
  String toString() => 'TemplateMatch(type: $type, name: $name, template: $fullMatch)';

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TemplateMatch &&
          runtimeType == other.runtimeType &&
          type == other.type &&
          name == name &&
          fullMatch == fullMatch;

  @override
  int get hashCode => Object.hash(type, name, fullMatch);
}
