/// Normalizes text for processing by converting to lowercase, replacing hyphens/underscores
/// with spaces, and removing punctuation.
String normalizeText(String text) {
  // Convert to lowercase
  String normalized = text.toLowerCase();

  // Replace certain characters
  normalized = normalized
      .replaceAll(r'-', ' ') // Replace hyphens with spaces
      .replaceAll(r'_', ' ') // Replace underscores with spaces
      .replaceAll(RegExp(r'[^\w\s]+'), ''); // Remove punctuation

  return normalized;
}
