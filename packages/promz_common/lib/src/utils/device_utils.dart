import 'package:promz_common/promz_common.dart';

/// Utility functions for device-related operations
class DeviceUtils {
  static const String _logName = 'DeviceUtils';

  /// Device emulator status
  static bool? _isEmulator;

  /// Set the emulator status from client code
  static void setEmulatorStatus(bool isEmulator) {
    _isEmulator = isEmulator;
    appLog.debug('Emulator status set to: $isEmulator', name: _logName);
  }

  /// Check if the app is running on an emulator
  ///
  /// Returns the cached emulator status, or null if not yet determined
  static bool? isEmulator() {
    return _isEmulator;
  }

  /// Check if the app is running on an emulator
  ///
  /// Returns false if the status is not yet determined
  static bool isEmulatorOrDefault() {
    return _isEmulator ?? false;
  }
}
