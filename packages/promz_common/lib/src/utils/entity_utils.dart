import 'package:promz_common/src/logging/app_logger.dart';
import 'package:promz_common/src/models/display_item.dart';
import 'package:promz_common/src/models/entity_model.dart';

/// Utilities for entity processing and manipulation
///
/// This class provides utilities for working with entities and variables in templates.
/// It supports both the legacy {#N} prefix format and the new {$N} prefix format for variables.
/// The new {$N} format is more compatible with Go templates and should be preferred for new code.
///
/// Example variable formats:
/// - Simple variable: FINANCE:TICKER
/// - Legacy prefixed variable: {#1}FINANCE:TICKER
/// - New prefixed variable: {$1}FINANCE:TICKER
/// Variable source types for categorization
enum VariableSourceType {
  /// Variables from input sources (WhatsApp, News, etc.)
  inputSource,

  /// System-provided variables
  system,

  /// User-defined variables
  userDefined,
}

class EntityUtils {
  static const _logName = 'EntityUtils';

  /// Regular expression to match prefixed variables
  /// Supports both {#N} and {$N} formats for backward compatibility
  /// Matches both uppercase and lowercase variable names with or without colons
  static final _prefixRegex = RegExp(r'\{([#$])([a-zA-Z0-9]+)\}([A-Za-z0-9_]+:[A-Za-z0-9_]+|\w+)');

  /// Map of canonical variable names to their display text
  static final Map<String, String> _displayTextMap = {
    // Finance variables
    'FINANCE:TICKER': 'Stock',
    'FINANCE:COMPANY_NAME': 'Company',
    'FINANCE:INDUSTRY': 'Industry',

    // Location variables
    'LOCATION:CITY': 'City',
    'LOCATION:COUNTRY': 'Country',
    'LOCATION:PLACE': 'Location',

    // Time variables
    'TIME:DATE': 'Date',
    'TIME:YEAR': 'Year',
    'TIME:MONTH': 'Month',
    'TIME:DAY': 'Day',
    'TIME:PERIOD': 'Time Period',

    // Person variables
    'PERSON:NAME': 'Person Name',

    // News article variables
    'NEWS:ARTICLE_URL': 'Article URL',
    'NEWS:ARTICLE_CONTENTS': 'Article Contents',

    // YouTube video variables
    'MEDIA:YOUTUBE_URL': 'YouTube Video URL',
    'MEDIA:YOUTUBE_TITLE': 'YouTube Video Title',
    'MEDIA:YOUTUBE_CHANNEL': 'YouTube Channel',
    'MEDIA:YOUTUBE_DESCRIPTION': 'YouTube Description',
    'MEDIA:YOUTUBE_THUMBNAIL': 'YouTube Thumbnail',
    'MEDIA:YOUTUBE_TRANSCRIPT': 'YouTube Transcript',

    // Attachment variables
    'ATTACHMENT:CONTENTS': 'Attachment Contents',

    // Conversation variables
    'CONVERSATION:CONTENTS': 'Chat Contents',
    'CONVERSATION:PARTICIPANTS': 'Chat Participants',
    'CONVERSATION:MESSAGE_COUNT': 'Message Count',
    'CONVERSATION:PLATFORM': 'Chat Platform',
    'CONVERSATION:GROUP_NAME': 'Group Name',
  };

  /// Map of common variable name to their canonical names. The one on the right is the canonical
  /// name.
  static final Map<String, String> _variableCanonicalNames = {
    // Finance ticker synonyms
    'SP500_SYMBOL': 'FINANCE:TICKER',
    'STOCK_SYMBOL': 'FINANCE:TICKER',
    'TICKER': 'FINANCE:TICKER',
    'STOCK_TICKER': 'FINANCE:TICKER',
    'COMPANY_SYMBOL': 'FINANCE:TICKER',

    // Finance company synonyms
    'SP500_COMPANY_NAME': 'FINANCE:COMPANY_NAME',
    'COMPANY_NAME': 'FINANCE:COMPANY_NAME',
    'COMPANY': 'FINANCE:COMPANY_NAME',

    // Finance industry synonyms
    'INDUSTRY_NAME': 'FINANCE:INDUSTRY',
    'INDUSTRY': 'FINANCE:INDUSTRY',
    'SP500_INDUSTRY': 'FINANCE:INDUSTRY',

    // Finance sector synonyms
    'SECTOR': 'FINANCE:SECTOR',
    'INDUSTRY_SECTOR': 'FINANCE:SECTOR',

    // Location synonyms
    'CITY': 'LOCATION:CITY',
    'COUNTRY': 'LOCATION:COUNTRY',
    'LOCATION': 'LOCATION:PLACE',
    'PLACE': 'LOCATION:PLACE',

    // Time synonyms
    'DATE': 'TIME:DATE',
    'YEAR': 'TIME:YEAR',
    'MONTH': 'TIME:MONTH',
    'DAY': 'TIME:DAY',
    'TIME_PERIOD': 'TIME:PERIOD',
    'PERIOD': 'TIME:PERIOD',

    // Person synonyms
    'NAME': 'PERSON:NAME',
    'PERSON': 'PERSON:NAME',
    'PERSON_NAME': 'PERSON:NAME',

    // News article synonyms
    'ARTICLE_URL': 'NEWS:ARTICLE_URL',
    'URL': 'NEWS:ARTICLE_URL',
    'ARTICLE_CONTENTS': 'NEWS:ARTICLE_CONTENTS',

    // YouTube video synonyms
    'YOUTUBE_URL': 'MEDIA:YOUTUBE_URL',
    'YOUTUBE_VIDEO': 'MEDIA:YOUTUBE_URL',
    'YOUTUBE_VIDEO_URL': 'MEDIA:YOUTUBE_URL',
    'YOUTUBE_TITLE': 'MEDIA:YOUTUBE_TITLE',
    'YOUTUBE_VIDEO_TITLE': 'MEDIA:YOUTUBE_TITLE',
    'YOUTUBE_CHANNEL': 'MEDIA:YOUTUBE_CHANNEL',
    'YOUTUBE_DESCRIPTION': 'MEDIA:YOUTUBE_DESCRIPTION',
    'YOUTUBE_THUMBNAIL': 'MEDIA:YOUTUBE_THUMBNAIL',
    'YOUTUBE_TRANSCRIPT': 'MEDIA:YOUTUBE_TRANSCRIPT',
    'YOUTUBE_VIDEO_TRANSCRIPT': 'MEDIA:YOUTUBE_TRANSCRIPT',

    // Attachments
    'CONTENTS': 'ATTACHMENT:CONTENTS',
    'DOCUMENT_CONTENTS': 'ATTACHMENT:CONTENTS',

    // Conversation synonyms
    'CHAT_CONTENTS': 'CONVERSATION:CONTENTS',
    'WHATSAPP_CONTENTS': 'CONVERSATION:CONTENTS',
    'CHAT_PARTICIPANTS': 'CONVERSATION:PARTICIPANTS',
    'WHATSAPP_PARTICIPANTS': 'CONVERSATION:PARTICIPANTS',
    'MESSAGE_COUNT': 'CONVERSATION:MESSAGE_COUNT',
    'CHAT_PLATFORM': 'CONVERSATION:PLATFORM',
    'CHAT_GROUP_NAME': 'CONVERSATION:GROUP_NAME',
    'GROUP_NAME': 'CONVERSATION:GROUP_NAME',
  };

  /// Map of entity categories to their corresponding EntityType
  ///
  /// This map is used to map entity categories to their corresponding EntityType
  ///
  /// Example:
  /// ```dart
  /// final entityType = _entityTypeMap['FINANCE'];
  /// print(entityType); // EntityType.finance
  /// ```
  static final Map<String, EntityType> _entityTypeMap = {
    'FINANCE': EntityType.finance,
    'LOCATION': EntityType.location,
    'PERSON': EntityType.person,
    'TIME': EntityType.timeframe,
    'NEWS': EntityType.news,
    'MEDIA': EntityType.youtubeVideo,
    'ATTACHMENT': EntityType.attachment,
    'CONVERSATION': EntityType.conversation,
  };

  /// Map of variable prefixes to their source type
  static final Map<String, VariableSourceType> _variableSourceMap = {
    'NEWS:': VariableSourceType.inputSource,
    'MEDIA:': VariableSourceType.inputSource,
    'ATTACHMENT:': VariableSourceType.inputSource,
    'CONVERSATION:': VariableSourceType.inputSource,
    'FINANCE:': VariableSourceType.system,
    'LOCATION:': VariableSourceType.system,
    'PERSON:': VariableSourceType.system,
    'TIME:': VariableSourceType.system,
  };

  /// Creates a DisplayItem from an Entity
  ///
  /// This replaces the previous DisplayItem.fromEntity factory constructor
  /// and creates a DisplayItem with properties properly set based on the entity type
  static DisplayItem createDisplayItemFromEntity(Entity entity) {
    // Determine the correct suggestion type based on entity properties
    DisplayItemType type = DisplayItemType.entity;
    String text = entity.text;
    String? subtitle;

    // Check if this is a financial entity
    if (entity.type == EntityType.finance) {
      final entitySubtype = entity.entitySubtype;

      // Handle stock symbol entities
      if (entitySubtype == 'stock') {
        type = DisplayItemType.stockSymbol;
        subtitle = 'Stock Symbol';
      }
      // Handle company name entities
      else if (entitySubtype == 'company') {
        type = DisplayItemType.companyName;
        // Use clean company name for replacement if available
        text = entity.metadata?['cleanCompanyName'] as String? ?? entity.text;
        subtitle = 'Company';
      }
    }

    appLog.debug(
      'Created display item from entity: type=$type, text="$text", displayText="${entity.displayText}"',
      name: _logName,
    );

    return DisplayItem(
      text: text,
      displayText: entity.displayText,
      subtitle: subtitle ?? 'Financial entity',
      type: type,
      entity: entity,
      relevanceScore: entity.confidence,
    );
  }

  /// Extract prefix from a variable name if present
  /// Returns the prefix without the braces and prefix character (# or $)
  static String? extractPrefix(String variableName) {
    final match = _prefixRegex.firstMatch(variableName);
    if (match != null && match.groupCount >= 2) {
      // match.group(1) is the prefix character (# or $)
      // match.group(2) is the prefix value (e.g., 1, 2, etc.)
      return match.group(2);
    }
    return null;
  }

  /// Extract the base variable name without prefix
  static String extractBaseName(String variableName) {
    appLog.debug('Extracting base name from: $variableName', name: _logName);

    final match = _prefixRegex.firstMatch(variableName);
    if (match != null && match.groupCount >= 3) {
      // match.group(3) is the base name after the prefix
      final baseName = match.group(3)!;
      appLog.debug('Extracted base name via regex: $baseName', name: _logName);
      return baseName;
    }

    appLog.debug('No prefix found, returning original: $variableName', name: _logName);
    return variableName;
  }

  /// Get the entity type for a variable name
  static EntityType? getEntityTypeForVariable(String variableName) {
    // First extract base name without prefix
    final baseName = extractBaseName(variableName);

    // Then normalize the name
    final normalizedName = _normalizeVariableName(baseName);

    // If it's already qualified with a category, extract and map it
    if (normalizedName.contains(':')) {
      final category = normalizedName.split(':')[0];
      return _entityTypeMap[category];
    }

    // Find the synonym if it exists
    final synonym = _variableCanonicalNames[normalizedName];
    if (synonym != null) {
      return getEntityTypeForVariable(synonym);
    }

    return null;
  }

  /// Get the canonical name for a variable if a synonym exists
  static String getCanonicalName(String variableName) {
    appLog.debug('Getting canonical name for: $variableName', name: _logName);

    // Extract prefix if present
    final prefix = extractPrefix(variableName);
    appLog.debug('Extracted prefix: $prefix', name: _logName);

    // Extract base name without prefix
    final baseName = extractBaseName(variableName);
    appLog.debug('Extracted base name: $baseName', name: _logName);

    final normalizedName = _normalizeVariableName(baseName);
    appLog.debug('Normalized name: $normalizedName', name: _logName);

    // Get the canonical base name (without prefix)
    String canonicalBaseName;

    // First check if this is already a fully qualified name
    if (normalizedName.contains(':')) {
      final parts = normalizedName.split(':');
      appLog.debug('Split parts: $parts', name: _logName);

      if (parts.length == 2) {
        final category = parts[0];
        final name = parts[1];
        appLog.debug('Category: $category, Name: $name', name: _logName);

        // Check if this combination exists in our synonym map
        final combined = '$category:$name';
        appLog.debug('Combined: $combined', name: _logName);
        appLog.debug('Is in display map? ${_displayTextMap.containsKey(combined)}', name: _logName);

        if (_displayTextMap.containsKey(combined)) {
          // Important: Keep the full combined name including the type part
          canonicalBaseName = combined;
          appLog.debug('Using combined name from display map: $canonicalBaseName', name: _logName);
        } else {
          // Keep the full normalized name with category and type
          canonicalBaseName = normalizedName;
          appLog.debug('Using normalized name: $canonicalBaseName', name: _logName);
        }
      } else {
        canonicalBaseName = normalizedName;
        appLog.debug('Using normalized name (parts length != 2): $canonicalBaseName',
            name: _logName);
      }
    } else {
      // Check for direct synonyms - this should return the full canonical name with type
      canonicalBaseName = _variableCanonicalNames[normalizedName] ?? normalizedName;
      appLog.debug('Using name from synonyms or original: $canonicalBaseName', name: _logName);
    }

    // Reattach prefix if it existed
    // Use the new {$N} format for new prefixes, but keep backward compatibility
    String canonicalName =
        (prefix != null ? '{\$' + prefix + '}' + canonicalBaseName : canonicalBaseName)
            .toUpperCase();
    appLog.debug('Final canonical name for $variableName is $canonicalName', name: _logName);
    return canonicalName;
  }

  /// Check if a variable is a financial entity
  static bool isFinanceVariable(String variableName) {
    return getEntityTypeForVariable(variableName) == EntityType.finance;
  }

  /// Get all known synonyms for a canonical name
  static List<String> getCanonicalNames(String canonicalName) {
    final normalized = _normalizeVariableName(canonicalName);

    return _variableCanonicalNames.entries
        .where((entry) => entry.value == normalized)
        .map((entry) => entry.key)
        .toList();
  }

  /// Removes common company name suffixes like Inc., Corp, etc.
  /// to create a cleaner, more readable company name
  static String cleanCompanyName(String companyName) {
    // Define regex patterns to match common company suffixes
    final suffixRegex = RegExp(
      r'\s+(Inc\.?|Corporation|Corp\.?|Company|Co\.?|Ltd\.?|Limited|LLC|LP|LLP|PLC|S\.A\.?|'
      r'N\.V\.?|Group|Holding|Holdings|Incorporated|International|Enterprises|Technologies|Technology)$',
      caseSensitive: false,
    );

    // Remove the suffix
    String cleanName = companyName.replaceAll(suffixRegex, '');

    // Remove "(The)" if it exists at the end
    cleanName = cleanName.replaceAll(RegExp(r'\s+\(The\)$'), '');

    // If name ends with a comma (from removing a suffix), remove it
    return cleanName.replaceAll(RegExp(r',\s*$'), '');
  }

  /// Cleans a stock symbol by removing any $ prefix
  static String cleanStockSymbol(String symbol) {
    String cleanSymbol = symbol.startsWith(r'$') ? symbol.substring(1) : symbol;
    return cleanSymbol.toUpperCase();
  }

  /// Get the display text for a variable or entity
  static String getDisplayName({Entity? entity, String? variableName}) {
    appLog.debug('START: Getting display name for entity: $entity or variable: $variableName',
        name: _logName);

    // First extract any prefix from the variable name
    String? prefix;
    String baseVariableName;

    if (variableName != null) {
      prefix = extractPrefix(variableName);
      baseVariableName = extractBaseName(variableName);
    } else if (entity != null) {
      prefix = entity.prefix;
      baseVariableName = entity.text;
    } else {
      return 'Unknown'; // No input provided
    }

    // Get display name without prefix first
    String displayName;

    // First, try to get the display name using the canonical name without prefix
    final canonicalNameWithoutPrefix =
        entity != null ? getCanonicalName(entity.text) : getCanonicalName(baseVariableName);

    // Remove any prefix from the canonical name for lookup
    String lookupName = canonicalNameWithoutPrefix;
    if (lookupName.contains('{#') || lookupName.contains('{' + r'$')) {
      // Extract just the base part after any prefix
      final prefixMatch = _prefixRegex.firstMatch(lookupName);
      if (prefixMatch != null && prefixMatch.groupCount >= 3) {
        lookupName = prefixMatch.group(3)!;
      }
    }

    // Look up display name in our map
    if (_displayTextMap.containsKey(lookupName)) {
      displayName = _displayTextMap[lookupName]!;
      appLog.debug('Found display name in map: $displayName', name: _logName);
    } else if (baseVariableName.isNotEmpty) {
      // If not found in map, generate a readable name
      displayName = _makeReadableName(baseVariableName);
      appLog.debug('Generated readable name: $displayName', name: _logName);
    } else if (entity != null) {
      // Use entity type as fallback
      switch (entity.type) {
        case EntityType.finance:
          displayName = 'Financial Entity';
          break;
        case EntityType.location:
          displayName = 'Location';
          break;
        case EntityType.person:
          displayName = 'Person';
          break;
        case EntityType.timeframe:
          displayName = 'Time Period';
          break;
        default:
          displayName = 'Variable';
      }
    } else {
      displayName = 'Unknown';
    }

    // If a prefix exists, append it to the display name
    if (prefix != null && prefix.isNotEmpty) {
      displayName = '$prefix. $displayName';
    }
    appLog.debug('END: Final display name: $displayName', name: _logName);
    return displayName;
  }

  /// Get a display value for an entity
  static String getDisplayValueForEntity(Entity entity) {
    if (entity.type == EntityType.finance) {
      final entitySubtype = entity.entitySubtype;
      if (entitySubtype == 'stock') {
        final symbol = entity.stockSymbol ?? '';
        final companyName = entity.companyName ?? '';
        return '$symbol - $companyName';
      } else if (entitySubtype == 'company') {
        return entity.displayText;
      }
    }
    return entity.displayText;
  }

  /// Get the actual value to be used when an entity is selected
  static String getValueForEntity(Entity entity) {
    if (entity.type == EntityType.finance) {
      final entitySubtype = entity.entitySubtype;
      if (entitySubtype == 'stock') {
        return entity.stockSymbol ?? entity.text;
      } else if (entitySubtype == 'company') {
        return entity.text;
      }
    }
    return entity.text;
  }

  /// Normalize a variable name to uppercase with standard formatting
  static String _normalizeVariableName(String variableName) {
    return variableName.toUpperCase().trim();
  }

  /// Make a variable name human-readable
  static String _makeReadableName(String variableName) {
    // If this is a fully qualified name, extract just the name part
    if (variableName.contains(':')) {
      variableName = variableName.split(':')[1];
    }

    // Replace underscores with spaces
    String readable = variableName.replaceAll('_', ' ');

    // Convert to Title Case
    readable = readable.split(' ').map((word) {
      if (word.isEmpty) return '';
      return word[0].toUpperCase() + word.substring(1).toLowerCase();
    }).join(' ');

    return readable;
  }

  /// Determine if a variable is from an input source
  static bool isInputSourceVariable(String variableName) {
    final canonicalName = getCanonicalName(variableName);

    // Check by prefix
    for (final prefix in _variableSourceMap.keys) {
      if (canonicalName.startsWith(prefix) &&
          _variableSourceMap[prefix] == VariableSourceType.inputSource) {
        return true;
      }
    }

    return false;
  }

  /// Get the source type for a variable
  static VariableSourceType getVariableSourceType(String variableName) {
    final canonicalName = getCanonicalName(variableName);

    // Check by prefix
    for (final prefix in _variableSourceMap.keys) {
      if (canonicalName.startsWith(prefix)) {
        return _variableSourceMap[prefix]!;
      }
    }

    // Default to user-defined if no match
    return VariableSourceType.userDefined;
  }

  /// Filter a list of variables by source type
  static List<String> filterVariablesBySourceType(
      List<String> variables, VariableSourceType sourceType,
      {bool exclude = false}) {
    return variables.where((variable) {
      final isMatchingType = getVariableSourceType(variable) == sourceType;
      return exclude ? !isMatchingType : isMatchingType;
    }).toList();
  }

  /// Get all input source variable prefixes
  static List<String> getInputSourcePrefixes() {
    return _variableSourceMap.entries
        .where((entry) => entry.value == VariableSourceType.inputSource)
        .map((entry) => entry.key)
        .toList();
  }
}
