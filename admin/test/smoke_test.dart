import 'package:flutter_test/flutter_test.dart';
import 'package:promz_common/promz_common.dart';
import 'package:flutter/material.dart';

void main() {
  testWidgets('MarkdownEditor builds', (WidgetTester tester) async {
    final controller = TextEditingController();
    controller.text = 'Hello **Markdown**!';
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: MarkdownEditor(
            controller: controller,
            label: 'Prompt Instruction',
            onChanged: () {},
            readOnly: false,
          ),
        ),
      ),
    );
    expect(find.byType(MarkdownEditor), findsOneWidget);
  });
}
