/// Utility functions for working with models in the admin app
class ModelUtils {
  /// Map a list of JSON objects to a list of model objects using the provided fromJson function
  static List<T> mapList<T>(List<dynamic> jsonList, T Function(Map<String, dynamic>) fromJson) {
    return jsonList.map((json) => fromJson(json as Map<String, dynamic>)).toList();
  }

  /// Map a single JSON object to a model object using the provided fromJson function
  static T mapSingle<T>(Map<String, dynamic> json, T Function(Map<String, dynamic>) fromJson) {
    return fromJson(json);
  }
}
