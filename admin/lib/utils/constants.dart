import 'package:supabase_flutter/supabase_flutter.dart';

final supabase = Supabase.instance.client;

// Table names
const String promptsTable = 'prompts';
const String promptInstructionsTable = 'prompt_instructions';
const String categoriesTable = 'categories';

// Column names for prompts table (should always align with database schema)
const String promptIdColumn = 'id';
const String promptNameColumn = 'title';
const String promptDescriptionColumn = 'subtitle';
const String promptCategoryIdColumn = 'category_id';
// Note: category_name column doesn't exist in the database schema
const String promptCreatedAtColumn = 'created_at';
const String promptUpdatedAtColumn = 'updated_at';
const String promptVersionColumn = 'version';

// Column names for prompt_instructions table
const String instructionIdColumn = 'id';
const String instructionPromptIdColumn = 'prompt_id';
const String instructionMarkdownColumn = 'instruction_template';
const String instructionCreatedAtColumn = 'created_at';
const String instructionUpdatedAtColumn = 'updated_at';
const String instructionVersionColumn = 'version';
