import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:promz_common/promz_common.dart';
import 'package:pz_admin/models/prompt.dart';
import 'package:pz_admin/providers/prompt_providers.dart';
import 'package:pz_admin/providers/service_providers.dart';
import 'package:pz_admin/screens/prompt_edit_screen.dart';
import 'package:pz_admin/widgets/loading_indicator.dart';

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen> {
  @override
  void initState() {
    super.initState();
    // Fetch prompts and instructions when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await ref.read(promptProvider.notifier).fetchPrompts();
      await ref.read(promptProvider.notifier).fetchAllInstructions();
    });
  }

  List<PromptModel> _convertToPromptModels(List<Prompt> adminPrompts) {
    return adminPrompts.map((prompt) {
      // Convert the Prompt object to a Map<String, dynamic> to use our utility method
      final promptJson = prompt.toJson();
      return PromptModel.fromJson(promptJson);
    }).toList();
  }

  void _handlePromptSelected(DisplayItem item) {
    if (item.prompt != null) {
      // Navigate directly to prompt edit screen in edit mode
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => PromptEditScreen(promptId: item.prompt!.id)),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final isLoading = ref.watch(promptLoadingProvider);
    final error = ref.watch(promptErrorProvider);
    final prompts = ref.watch(promptsProvider);
    final adminContextService = ref.watch(adminContextServiceProvider);

    return Scaffold(
      appBar: AppBar(title: const Text('Promz Admin')),
      body:
          isLoading
              ? const LoadingIndicator()
              : prompts.isEmpty && error != null
              ? Center(child: Text('Error: $error'))
              : Padding(
                padding: const EdgeInsets.all(16.0),
                child: PromptList.fromPromptModels(
                  title: 'All Prompts',
                  prompts: _convertToPromptModels(prompts),
                  isLoading: isLoading,
                  onPromptSelected: _handlePromptSelected,
                  editMode: true,
                  contextService: adminContextService,
                ),
              ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const PromptEditScreen()),
          );
        },
        child: const Icon(Icons.add),
      ),
    );
  }
}
