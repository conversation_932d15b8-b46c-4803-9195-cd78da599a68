import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:promz_common/promz_common.dart';
import 'package:pz_admin/models/category.dart';
import 'package:pz_admin/models/prompt.dart';
import 'package:pz_admin/providers/category_providers.dart';
import 'package:pz_admin/providers/prompt_providers.dart';
import 'package:pz_admin/widgets/add_category_dialog.dart';
import 'package:pz_admin/widgets/admin_app_bar.dart';
import 'package:pz_admin/widgets/llm_edit_controls.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class PromptEditScreen extends ConsumerStatefulWidget {
  final String? promptId;
  const PromptEditScreen({super.key, this.promptId});

  @override
  ConsumerState<PromptEditScreen> createState() => _PromptEditScreenState();
}

class _PromptEditScreenState extends ConsumerState<PromptEditScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  late TextEditingController _controller;
  String _markdownText = '';
  String? _llmError;
  bool _isEditMode = false;
  bool _didLoad = false;
  String? _promptId;
  String? _selectedCategoryId;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController();

    // Fetch categories when the screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(categoryProvider.notifier).fetchCategories();

      // Debug: Check database schema to verify column names
      _checkDatabaseSchema();
    });
  }

  // Debug method to check database schema
  Future<void> _checkDatabaseSchema() async {
    try {
      final supabase = Supabase.instance.client;

      // Check a specific prompt to see its structure
      if (widget.promptId != null) {
        // Cast the nullable String to non-nullable String before using it
        final promptId = widget.promptId!;
        final promptData = await supabase.from('prompts').select('*').eq('id', promptId).single();
        final categoryId = promptData['category_id'];

        // Try a direct update to set the category
        if (categoryId == null) {
          try {
            // Cast the nullable String to non-nullable String before using it
            final promptId = widget.promptId!;
            await supabase
                .from('prompts')
                .update({'category_id': '000632fe-0327-e81c-2d36-6138612d3435'})
                .eq('id', promptId)
                .select();
          } catch (e) {
            appLog.error('Direct update failed: $e');
          }
        }
      }
    } catch (e) {
      appLog.error('Error checking schema: $e');
    }

    if (widget.promptId != null) {
      _isEditMode = true;
      _promptId = widget.promptId;
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        final prompts = ref.read(promptsProvider);
        final prompt = prompts.firstWhere(
          (p) => p.id == widget.promptId,
          orElse:
              () => Prompt(
                id: '',
                title: '',
                subtitle: '',
                createdAt: DateTime.now(),
                updatedAt: DateTime.now(),
              ),
        );
        if (prompt.id.isNotEmpty) {
          _nameController.text = prompt.title;
          _descriptionController.text = prompt.subtitle;

          // Get category information if available
          if (prompt.categoryId != null && prompt.categoryId!.isNotEmpty) {
            _selectedCategoryId = prompt.categoryId;
          }

          // Fetch instruction for this prompt
          await ref.read(promptProvider.notifier).fetchInstructionForPrompt(widget.promptId!);
          final instruction = ref.read(promptInstructionProvider(widget.promptId!));
          if (instruction != null) {
            _markdownText = instruction.markdown;
            _controller.text = instruction.markdown;
          }
        }
        setState(() {
          _didLoad = true;
        });
      });
    } else {
      _controller.text = _markdownText;
      _didLoad = true;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _controller.dispose();
    super.dispose();
  }

  Future<void> _showAddCategoryDialog() async {
    final newCategory = await showDialog<Category>(
      context: context,
      builder: (context) => const AddCategoryDialog(),
    );

    if (newCategory != null) {
      setState(() {
        _selectedCategoryId = newCategory.id;
      });
    }
  }

  void _savePrompt() {
    if (_formKey.currentState!.validate()) {
      final notifier = ref.read(promptProvider.notifier);

      if (_isEditMode && _promptId != null) {
        // Update existing prompt
        notifier.updatePrompt(
          _promptId!,
          _nameController.text,
          _descriptionController.text,
          _markdownText,
          categoryId: _selectedCategoryId,
        );
      } else {
        // Create new prompt
        notifier.createPrompt(
          _nameController.text,
          _descriptionController.text,
          _markdownText,
          categoryId: _selectedCategoryId,
        );
      }

      Navigator.pop(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    final isLoading = ref.watch(promptLoadingProvider);
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AdminAppBar(
        title: _isEditMode ? 'Edit Prompt' : 'Create New Prompt',
        actionButtonLabel: 'Save',
        actionButtonIcon: Icons.save,
        onActionPressed: _didLoad ? _savePrompt : null,
        isLoading: isLoading || !_didLoad,
      ),
      body:
          !_didLoad
              ? const Center(child: CircularProgressIndicator())
              : Form(
                key: _formKey,
                child: SingleChildScrollView(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildMetadataSection(theme),
                        const SizedBox(height: 24),
                        // Not using Expanded since we're in a SingleChildScrollView
                        _buildContentSection(theme, isLoading),
                      ],
                    ),
                  ),
                ),
              ),
    );
  }

  Widget _buildMetadataSection(ThemeData theme) {
    return Card(
      elevation: 2,
      margin: EdgeInsets.zero,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Prompt Details',
              style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Name',
                border: OutlineInputBorder(),
                hintText: 'Enter a descriptive name for this prompt',
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a name';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description',
                border: OutlineInputBorder(),
                hintText: 'Provide a brief description of what this prompt does',
              ),
              maxLines: 2,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a description';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            // Category dropdown with 'Add new' option
            Consumer(
              builder: (context, ref, child) {
                final categories = ref.watch(categoriesProvider);
                final isLoading = ref.watch(categoryLoadingProvider);

                // Check if the selected category ID exists in the categories list
                final categoryExists =
                    _selectedCategoryId == null ||
                    _selectedCategoryId == 'add_new' ||
                    categories.any((c) => c.id == _selectedCategoryId);

                // Log for debugging
                if (_selectedCategoryId != null && !categoryExists) {
                  appLog.warning(
                    'Selected category ID $_selectedCategoryId not found in categories list',
                  );
                  // Reset the selected category if it doesn't exist
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    setState(() {
                      _selectedCategoryId = null;
                    });
                  });
                }

                return DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'Category',
                    border: OutlineInputBorder(),
                    hintText: 'Select a category or add new',
                  ),
                  value: categoryExists ? _selectedCategoryId : null,
                  items: [
                    // Add a special item for creating a new category
                    const DropdownMenuItem<String>(
                      value: 'add_new',
                      child: Row(
                        children: [
                          Icon(Icons.add_circle_outline),
                          SizedBox(width: 8),
                          Text('Add new category'),
                        ],
                      ),
                    ),
                    // Divider
                    if (categories.isNotEmpty)
                      const DropdownMenuItem<String>(enabled: false, child: Divider()),
                    // Existing categories
                    ...categories.map(
                      (category) =>
                          DropdownMenuItem<String>(value: category.id, child: Text(category.name)),
                    ),
                  ],
                  onChanged:
                      isLoading
                          ? null
                          : (value) {
                            if (value == 'add_new') {
                              // Show dialog to create a new category
                              _showAddCategoryDialog();
                            } else {
                              setState(() {
                                _selectedCategoryId = value;
                              });
                            }
                          },
                  hint:
                      isLoading
                          ? const Row(
                            children: [
                              SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(strokeWidth: 2),
                              ),
                              SizedBox(width: 8),
                              Text('Loading categories...'),
                            ],
                          )
                          : const Text('Select a category'),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContentSection(ThemeData theme, bool isLoading) {
    return Card(
      elevation: 2,
      margin: EdgeInsets.zero,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Prompt Instructions',
                  style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
                ),
                if (_llmError != null)
                  Chip(
                    label: const Text('Error in AI processing'),
                    backgroundColor: theme.colorScheme.errorContainer,
                    labelStyle: TextStyle(color: theme.colorScheme.onErrorContainer),
                  ),
              ],
            ),
            const SizedBox(height: 16),
            LlmEditControls(
              markdownController: _controller,
              enabled: true,
              onError: (error) {
                setState(() {
                  _llmError = error;
                });
              },
            ),
            const SizedBox(height: 16),
            // Use SizedBox with fixed height instead of Expanded
            SizedBox(
              height: 400, // Minimum height for the editor
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(color: theme.colorScheme.outlineVariant),
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: [
                    BoxShadow(
                      // ignore: deprecated_member_use
                      color: theme.colorScheme.shadow.withOpacity(0.1),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(7),
                  child: MarkdownEditor(
                    controller: _controller,
                    label: 'Prompt Instruction',
                    onChanged: () {
                      _markdownText = _controller.text;
                    },
                    readOnly: false,
                  ),
                ),
              ),
            ),
            if (_llmError != null)
              Padding(
                padding: const EdgeInsets.only(top: 12.0),
                child: Text(
                  _llmError!,
                  style: TextStyle(color: theme.colorScheme.error, fontSize: 12),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
