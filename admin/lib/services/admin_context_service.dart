import 'package:flutter/material.dart';
import 'package:promz_common/llm/base_context_service.dart';

/// A minimal implementation of ClientContextService for the admin app
/// This is needed because the PromptList widget from promz_common expects access to ClientContextService
class AdminContextService extends ChangeNotifier implements BaseContextService {
  // Simple map to store variable values
  final Map<String, String> _variableValues = {};

  // Getter for variable values
  Map<String, String> get variableValues => Map.unmodifiable(_variableValues);

  // Transform a title with variable values
  String transformTitleWithValues(String title, Map<String, String> values) {
    // For the admin app, we don't need to transform titles
    return title;
  }

  // Transform a title with all available variable values
  // This method is called by DisplayItem.fromPromptModel
  @override
  String transformTitleWithAllValues(String title) {
    // For the admin app, we don't need to transform titles
    return title;
  }

  // Get all variable values (used by transformTitleWithAllValues)
  Map<String, String> getAllVariableValues() {
    return variableValues;
  }
}
