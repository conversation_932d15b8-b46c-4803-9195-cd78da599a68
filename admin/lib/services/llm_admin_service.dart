import 'package:promz_common/promz_common.dart';

/// Service for handling LLM operations in the admin application.
///
/// Uses the common PromzLlmExecutionService internally for consistent
/// error handling and request formatting while maintaining the admin-specific API.
class LlmAdminService {
  final String _logName = 'LlmAdminService';

  final String baseUrl;
  final String adminApiKey;
  late final PromzLlmExecutionService _llmExecutionService;

  final String _metaPrompt =
      'You are an AI prompt engineering expert tasked with evaluating and enhancing prompts '
      'provided by users. Each task includes a "User Specified Task:" and a potentially '
      'markdown-formatted prompt labeled "Markdown:". Your primary objective is to revise the '
      'provided markdown to:\n\n'
      '1. Align with the user’s specified task.\n'
      '2. Elevate prompt quality and structure for optimal performance when interpreted by '
      '   language models.\n'
      '3. Ensure the prompt designates the model as an expert in the relevant subject area. If'
      '   such context is missing, add it where necessary.\n'
      '4. Embed all additional enhancement requests listed below if not already present.\n\n'
      '✅ ## **Additional Enhancement Requirements** ##\n\n'
      'If absent in the original prompt, integrate the following elements:\n\n'
      '* **Tone and Style:** Maintain a professional yet accessible tone. Avoid unnecessary jargon'
      '   while keeping the analysis engaging and insightful.\n'
      '* **Visual Elements:** Recommend using tables or bullet points to showcase key data or '
      '   ideas clearly.\n'
      '* **Summary or Key Takeaways:** Begin the output with a short summary or list of key '
      '   insights to highlight the main points.\n'
      '* **Comparative Analysis:** Where applicable, suggest including competitor comparisons '
      '   for context.\n'
      '* **Actionable Insights:** Emphasize recommendations or next steps derived from the '
      '   content.\n'
      '* **Forward-Looking Perspective:** Highlight future events or factors relevant to the '
      '   prompt\'s topic (e.g., product launches, policy changes).\n'
      '* **Readability:** Use clear section headings, bullet points, and numbered lists for '
      '   easy skimming.\n'
      '* **Markdown Formatting:** Instruct the LLM to output content using markdown with:\n'
      '  * Section headings\n'
      '  * Bullet points\n'
      '  * Numbered lists\n'
      '  * Emphasis where appropriate\n\n'
      '🛑 ## **Variable Preservation Directive** ##\n\n'
      'Placeholder Integrity: Do not alter, remove, or reformat any content inside double curly'
      'braces (e.g., {{NEWS:ARTICLE_URL}}). These are dynamic tokens used in downstream systems '
      'and must remain exactly as written.\n\n'
      '✨ ## **Final Note** ##\n\n'
      'Always maintain the intent and structure of the original prompt, while upgrading clarity, '
      'instruction strength, and formatting to ensure reliable and high-quality LLM responses.';

  /// Creates a new instance of LlmAdminService.
  ///
  /// The [baseUrl] parameter specifies the base URL for API calls.
  /// The [adminApiKey] parameter provides the API key for authentication.
  LlmAdminService({required this.baseUrl, required this.adminApiKey}) {
    _llmExecutionService = PromzLlmExecutionService(apiBaseUrl: baseUrl, promzApiKey: adminApiKey);
  }

  /// Edits markdown content using AI assistance.
  ///
  /// The [markdown] parameter contains the original markdown content.
  /// The [instruction] parameter provides guidance for the editing.
  /// The [model] parameter specifies which LLM model to use.
  ///
  /// Returns a map containing the edited content and any additional metadata.
  /// Throws exceptions for various error conditions.
  Future<Map<String, dynamic>> editMarkdown({
    required String markdown,
    required String instruction,
    required String provider,
  }) async {
    try {
      // Create a request object for the common execution service
      final request = LlmExecuteRequest(
        promptContent: '$_metaPrompt\n\nUser Specified Task: $instruction\n\nMarkdown: $markdown',
        sourceContent: const [],
        maxTokens: 4000, // Reasonable max tokens for editing content
        temperature: 0.7, // Default temperature for editing tasks
        provider: provider,
      );

      // Execute the request using the common service
      final response = await _llmExecutionService.execute(request);
      appLog.debug('LLM response: ${response.text}', name: _logName);

      // Map the response to the expected admin format
      return {
        'editedContent': response.text,
        'provider': response.provider,
        'usage': response.tokens,
      };
    } on NoEligibleModelException catch (e) {
      // Handle "no eligible models" error specifically
      throw Exception(
        'LLM Edit failed: No eligible models available for ${e.providerId ?? "selected provider"}. ${e.message}',
      );
    } on ServerErrorWithMessageException catch (e) {
      // Handle server errors with specific messages
      throw Exception('LLM Edit failed: ${e.message}');
    } on ServerErrorException catch (e) {
      // Handle generic server errors
      throw Exception(
        'LLM Edit failed: Server returned status ${e.statusCode}. ${e.responseBody ?? ""}',
      );
    } catch (e) {
      // Handle any other exceptions
      throw Exception('LLM Edit failed: $e');
    }
  }
}
