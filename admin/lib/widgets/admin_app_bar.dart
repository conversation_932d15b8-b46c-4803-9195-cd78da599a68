import 'package:flutter/material.dart';

/// A reusable app bar widget for Promz Admin screens.
///
/// Provides consistent styling and functionality across the admin interface
/// with support for primary actions and loading states.
class AdminAppBar extends StatelessWidget implements PreferredSizeWidget {
  /// The title displayed in the app bar.
  final String title;

  /// Primary action button text. If null, no action button is shown.
  final String? actionButtonLabel;

  /// Icon to display on the action button.
  final IconData? actionButtonIcon;

  /// Callback when the action button is pressed.
  final VoidCallback? onActionPressed;

  /// Whether an operation is in progress, disables the action button.
  final bool isLoading;

  /// Optional additional actions to display in the app bar.
  final List<Widget>? additionalActions;

  const AdminAppBar({
    super.key,
    required this.title,
    this.actionButtonLabel,
    this.actionButtonIcon,
    this.onActionPressed,
    this.isLoading = false,
    this.additionalActions,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return AppBar(
      title: Text(title),
      actions: [
        if (actionButtonLabel != null && onActionPressed != null)
          Padding(
            padding: const EdgeInsets.only(right: 16.0),
            child: FilledButton.icon(
              onPressed: isLoading ? null : onActionPressed,
              icon: Icon(actionButtonIcon ?? Icons.check),
              label: Text(actionButtonLabel!),
            ),
          ),
        if (additionalActions != null) ...additionalActions!,
      ],
      backgroundColor: theme.colorScheme.surface,
      foregroundColor: theme.colorScheme.onSurface,
      elevation: 2,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
