import 'package:flutter/material.dart';
import 'package:promz_common/config/api_config.dart';
import 'package:pz_admin/services/llm_admin_service.dart';

class LlmEditControls extends StatefulWidget {
  final TextEditingController markdownController;
  final bool enabled;
  final String? initialProvider;
  final String? initialInstruction;
  final Function(String?)? onError;

  const LlmEditControls({
    super.key,
    required this.markdownController,
    this.enabled = true,
    this.initialProvider,
    this.initialInstruction,
    this.onError,
  });

  @override
  State<LlmEditControls> createState() => _LlmEditControlsState();
}

class _LlmEditControlsState extends State<LlmEditControls> {
  final _instructionController = TextEditingController();
  String? _llmError;
  bool _llmLoading = false;
  String _selectedProvider = 'google';
  final List<String> _providers = ['google', 'openai', 'anthropic'];
  bool _hasValidInput = false;

  @override
  void initState() {
    super.initState();
    if (widget.initialProvider != null) _selectedProvider = widget.initialProvider!;
    if (widget.initialInstruction != null) {
      _instructionController.text = widget.initialInstruction!;
    }

    // Listen to changes in both controllers to update button state
    _instructionController.addListener(_updateButtonState);
    widget.markdownController.addListener(_updateButtonState);

    // Set initial button state
    _updateButtonState();
  }

  void _updateButtonState() {
    final hasInstructions = _instructionController.text.trim().isNotEmpty;
    final hasMarkdownContent = widget.markdownController.text.trim().isNotEmpty;

    // We still track valid input state for internal use
    final newState = hasInstructions && hasMarkdownContent;
    if (newState != _hasValidInput) {
      setState(() {
        _hasValidInput = newState;
      });
    }
  }

  @override
  void dispose() {
    _instructionController.removeListener(_updateButtonState);
    widget.markdownController.removeListener(_updateButtonState);
    _instructionController.dispose();
    super.dispose();
  }

  Future<void> _processWithLlm() async {
    // Only check for loading state, not input validation
    if (_llmLoading) return;

    // Ensure we have instructions
    final hasInstructions = _instructionController.text.trim().isNotEmpty;
    final hasMarkdownContent = widget.markdownController.text.trim().isNotEmpty;
    if (!hasInstructions && !hasMarkdownContent) {
      setState(() {
        _llmError = 'Please provide instructions';
      });
      if (widget.onError != null) widget.onError!('Please provide instructions');
      return;
    }

    setState(() {
      _llmLoading = true;
      _llmError = null;
      if (widget.onError != null) widget.onError!(null);
    });

    try {
      final service = LlmAdminService(
        baseUrl: ApiConfig.baseUrl,
        adminApiKey: ApiConfig.adminApiKey,
      );
      final resp = await service.editMarkdown(
        markdown: widget.markdownController.text,
        instruction: _instructionController.text,
        provider: _selectedProvider,
      );
      setState(() {
        widget.markdownController.text = resp['editedContent'] ?? '';
        _llmError = null;
      });
    } catch (e) {
      final errorMsg = e.toString();
      setState(() {
        _llmError = errorMsg;
      });
      if (widget.onError != null) widget.onError!(errorMsg);
    } finally {
      setState(() {
        _llmLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.enabled) return const SizedBox.shrink();

    final theme = Theme.of(context);

    return Container(
      decoration: BoxDecoration(
        // ignore: deprecated_member_use
        color: theme.colorScheme.surfaceContainerHighest.withOpacity(0.3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: theme.colorScheme.outlineVariant),
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'AI-Assisted Editing',
            style: theme.textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                width: 110, // Fixed width instead of Expanded with flex
                height: 40, // Explicit height for consistency
                child: DropdownButtonFormField<String>(
                  value: _selectedProvider,
                  isExpanded: true, // Make dropdown use all available width
                  icon: const Icon(Icons.arrow_drop_down, size: 20), // Smaller icon
                  items:
                      _providers
                          .map(
                            (provider) => DropdownMenuItem(
                              value: provider,
                              child: Text(
                                provider,
                                style: const TextStyle(fontSize: 13), // Smaller text
                                overflow: TextOverflow.ellipsis, // Handle text overflow
                              ),
                            ),
                          )
                          .toList(),
                  onChanged: (val) {
                    if (val != null) setState(() => _selectedProvider = val);
                  },
                  decoration: InputDecoration(
                    labelText: 'Provider',
                    border: const OutlineInputBorder(),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 12,
                    ), // Smaller padding
                    isDense: true,
                    filled: true,
                    fillColor: theme.colorScheme.surface,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                flex: 3,
                child: SizedBox(
                  height: 40, // Match dropdown height
                  child: TextField(
                    controller: _instructionController,
                    decoration: InputDecoration(
                      labelText: 'Instructions for AI',
                      hintText: 'Describe how to improve the content...',
                      border: const OutlineInputBorder(),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 12,
                      ), // Match dropdown padding
                      isDense: true,
                      filled: true,
                      fillColor: theme.colorScheme.surface,
                      suffixIcon:
                          _instructionController.text.trim().isEmpty
                              ? null
                              : IconButton(
                                onPressed: () {
                                  _instructionController.clear();
                                  _updateButtonState();
                                },
                                icon: const Icon(Icons.clear, size: 16), // Smaller icon
                                tooltip: 'Clear',
                              ),
                    ),
                    onChanged: (_) => _updateButtonState(),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              _buildProcessButton(theme),
            ],
          ),
          if (_llmError != null)
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Text(
                _llmError!,
                style: TextStyle(color: theme.colorScheme.error, fontSize: 12),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildProcessButton(ThemeData theme) {
    // Determine button text based on markdown content
    final hasMarkdownContent = widget.markdownController.text.trim().isNotEmpty;
    final hasInstructions = _instructionController.text.trim().isNotEmpty;
    final buttonText = hasMarkdownContent ? 'Revise' : 'Generate';

    // Button availability logic:
    // - When no markdown content: enable only if instructions are available
    // - When markdown content exists: always enable
    final bool isButtonEnabled = hasMarkdownContent || hasInstructions;

    return SizedBox(
      height: 40, // Match dropdown height
      child: ElevatedButton(
        onPressed: _llmLoading || !isButtonEnabled ? null : _processWithLlm,
        style: ElevatedButton.styleFrom(
          backgroundColor: theme.colorScheme.secondary,
          foregroundColor: theme.colorScheme.onSecondary,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
          elevation: 2,
        ),
        child:
            _llmLoading
                ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
                : Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(Icons.auto_awesome, size: 16), // Smaller icon
                    const SizedBox(width: 6), // Smaller spacing
                    Text(
                      buttonText,
                      style: theme.textTheme.labelMedium, // Smaller text
                    ),
                  ],
                ),
      ),
    );
  }
}
