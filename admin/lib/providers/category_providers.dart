import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:promz_common/promz_common.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/category.dart';
import '../utils/constants.dart';
import '../utils/model_utils.dart';

// State for the category management feature
class CategoryState {
  final List<Category> categories;
  final bool isLoading;
  final String? error;

  const CategoryState({this.categories = const [], this.isLoading = false, this.error});

  CategoryState copyWith({List<Category>? categories, bool? isLoading, String? error}) {
    return CategoryState(
      categories: categories ?? this.categories,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
    );
  }

  // Helper to clear the error
  CategoryState clearError() => copyWith(error: '');
}

// A StateNotifier for managing the Category state
class CategoryNotifier extends StateNotifier<CategoryState> {
  CategoryNotifier() : super(const CategoryState());

  final supabase = Supabase.instance.client;

  // Fetch all categories from the database
  Future<void> fetchCategories() async {
    state = state.copyWith(isLoading: true, error: '');

    try {
      appLog.info('Fetching categories from table: $categoriesTable');
      final response = await supabase
          .from(categoriesTable)
          .select()
          .order('title', ascending: true); // Order by 'title' column, not 'name'

      appLog.info('Categories response: $response');
      final categories = ModelUtils.mapList<Category>(response, Category.fromJson);
      appLog.info('Parsed ${categories.length} categories');
      state = state.copyWith(categories: categories, isLoading: false);
    } catch (e) {
      state = state.copyWith(error: 'Failed to fetch categories: $e', isLoading: false);
    }
  }

  // Create a new category
  Future<Category?> createCategory(String name, String? description, String? iconName) async {
    state = state.copyWith(isLoading: true, error: '');

    try {
      // Build the data object with only non-null values
      final Map<String, dynamic> categoryData = {
        'title': name, // 'name' field maps to 'title' column
      };

      // Only add optional fields if they have values
      if (description != null) {
        categoryData['subtitle'] = description; // 'description' maps to 'subtitle' column
      }

      if (iconName != null) {
        categoryData['icon'] = iconName; // 'icon_name' maps to 'icon' column
      }

      // Initialize empty keywords array as required by schema
      categoryData['keywords'] = [];

      // Let Supabase handle timestamps with default values if possible
      // If your table requires these fields, uncomment these lines
      // final now = DateTime.now().toIso8601String();
      // categoryData['created_at'] = now;
      // categoryData['updated_at'] = now;

      // For debugging - print what we're sending
      appLog.info('Creating category with data: $categoryData');

      final response = await supabase.from(categoriesTable).insert(categoryData).select().single();

      final newCategory = Category.fromJson(response);

      // Update the categories list
      final updatedCategories = [...state.categories, newCategory];
      state = state.copyWith(categories: updatedCategories, isLoading: false);

      return newCategory;
    } catch (e) {
      appLog.error('Error creating category: $e');
      state = state.copyWith(error: 'Failed to create category: $e', isLoading: false);
      return null;
    }
  }

  // Update an existing category
  Future<void> updateCategory(String id, String name, String? description, String? iconName) async {
    state = state.copyWith(isLoading: true, error: '');

    try {
      // Build update data with correct column names
      final Map<String, dynamic> updateData = {
        'title': name, // 'name' maps to 'title' column
        'updated_at': DateTime.now().toIso8601String(),
      };

      // Only add optional fields if they have values
      if (description != null) {
        updateData['subtitle'] = description; // 'description' maps to 'subtitle' column
      }

      if (iconName != null) {
        updateData['icon'] = iconName; // 'icon_name' maps to 'icon' column
      }

      appLog.info('Updating category with data: $updateData');
      await supabase.from(categoriesTable).update(updateData).eq('id', id);

      // Update the category in the list
      final updatedCategories =
          state.categories.map((category) {
            if (category.id == id) {
              return category.copyWith(
                name: name,
                description: description,
                iconName: iconName,
                updatedAt: DateTime.now(),
              );
            }
            return category;
          }).toList();

      state = state.copyWith(categories: updatedCategories, isLoading: false);
    } catch (e) {
      state = state.copyWith(error: 'Failed to update category: $e', isLoading: false);
    }
  }

  // Delete a category
  Future<void> deleteCategory(String id) async {
    state = state.copyWith(isLoading: true, error: '');

    try {
      await supabase.from(categoriesTable).delete().eq('id', id);

      // Remove the category from the list
      final updatedCategories = state.categories.where((category) => category.id != id).toList();
      state = state.copyWith(categories: updatedCategories, isLoading: false);
    } catch (e) {
      state = state.copyWith(error: 'Failed to delete category: $e', isLoading: false);
    }
  }
}

// Create the StateNotifierProvider to expose the CategoryNotifier
final categoryProvider = StateNotifierProvider<CategoryNotifier, CategoryState>((ref) {
  return CategoryNotifier();
});

// Convenience providers for accessing specific parts of the state
final categoriesProvider = Provider<List<Category>>((ref) {
  return ref.watch(categoryProvider).categories;
});

final categoryLoadingProvider = Provider<bool>((ref) {
  return ref.watch(categoryProvider).isLoading;
});

final categoryErrorProvider = Provider<String?>((ref) {
  return ref.watch(categoryProvider).error;
});
