import 'package:flutter/material.dart';
import '../models/prompt.dart';
import '../models/prompt_instruction.dart';
import '../utils/constants.dart';

class PromptProvider extends ChangeNotifier {
  List<Prompt> _prompts = [];
  final Map<String, PromptInstruction> _instructions = {};
  bool _isLoading = false;
  String? _error;

  List<Prompt> get prompts => _prompts;
  PromptInstruction? getInstruction(String promptId) => _instructions[promptId];
  bool get isLoading => _isLoading;
  String? get error => _error;

  Future<void> fetchPrompts() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final response = await supabase
          .from(promptsTable)
          .select()
          .order(promptCreatedAtColumn, ascending: false);

      _prompts = response.map<Prompt>((json) => Prompt.fromJson(json)).toList();
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = 'Failed to fetch prompts: $e';
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> fetchAllInstructions() async {
    if (_prompts.isEmpty) {
      await fetchPrompts();
    }

    if (_prompts.isEmpty) return;

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final response = await supabase
          .from(promptInstructionsTable)
          .select()
          .inFilter(instructionPromptIdColumn, _prompts.map((p) => p.id).toList());

      final instructions =
          response.map<PromptInstruction>((json) => PromptInstruction.fromJson(json)).toList();

      for (final instruction in instructions) {
        _instructions[instruction.promptId] = instruction;
      }

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = 'Failed to fetch instructions: $e';
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> fetchInstructionForPrompt(String promptId) async {
    if (_instructions.containsKey(promptId)) return;

    _isLoading = true;
    notifyListeners();

    try {
      final response =
          await supabase
              .from(promptInstructionsTable)
              .select()
              .eq(instructionPromptIdColumn, promptId)
              .single();

      _instructions[promptId] = PromptInstruction.fromJson(response);
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = 'Failed to fetch instruction: $e';
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> updateInstructionMarkdown(
    String instructionId,
    String promptId,
    String markdown,
  ) async {
    _isLoading = true;
    notifyListeners();

    try {
      await supabase
          .from(promptInstructionsTable)
          .update({
            instructionMarkdownColumn: markdown,
            instructionUpdatedAtColumn: DateTime.now().toIso8601String(),
          })
          .eq(instructionIdColumn, instructionId);

      if (_instructions.containsKey(promptId)) {
        final instruction = _instructions[promptId]!;
        _instructions[promptId] = PromptInstruction(
          id: instruction.id,
          promptId: instruction.promptId,
          markdown: markdown,
          createdAt: instruction.createdAt,
          updatedAt: DateTime.now(),
        );
      }

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = 'Failed to update instruction: $e';
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> createPrompt(String name, String description, String markdown) async {
    _isLoading = true;
    notifyListeners();

    try {
      // Create prompt
      final promptResponse =
          await supabase
              .from(promptsTable)
              .insert({
                promptNameColumn: name,
                promptDescriptionColumn: description,
                promptCreatedAtColumn: DateTime.now().toIso8601String(),
                promptUpdatedAtColumn: DateTime.now().toIso8601String(),
              })
              .select()
              .single();

      final prompt = Prompt.fromJson(promptResponse);

      // Create instruction
      await supabase.from(promptInstructionsTable).insert({
        instructionPromptIdColumn: prompt.id,
        instructionMarkdownColumn: markdown,
        instructionCreatedAtColumn: DateTime.now().toIso8601String(),
        instructionUpdatedAtColumn: DateTime.now().toIso8601String(),
      });

      // Refresh prompts
      await fetchPrompts();

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = 'Failed to create prompt: $e';
      _isLoading = false;
      notifyListeners();
    }
  }
}
