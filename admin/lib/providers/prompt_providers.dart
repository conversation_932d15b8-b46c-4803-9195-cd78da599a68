import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:promz_common/promz_common.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/category.dart';
import '../models/prompt.dart';
import '../models/prompt_instruction.dart';
import '../utils/constants.dart';
import '../utils/model_utils.dart';

// State for the prompt management feature
class PromptState {
  final List<Prompt> prompts;
  final Map<String, PromptInstruction> instructions;
  final bool isLoading;
  final String? error;

  const PromptState({
    this.prompts = const [],
    this.instructions = const {},
    this.isLoading = false,
    this.error,
  });

  PromptState copyWith({
    List<Prompt>? prompts,
    Map<String, PromptInstruction>? instructions,
    bool? isLoading,
    String? error,
  }) {
    return PromptState(
      prompts: prompts ?? this.prompts,
      instructions: instructions ?? this.instructions,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
    );
  }

  // Helper to clear the error
  PromptState clearError() => copyWith(error: '');
}

// A StateNotifier for managing the Prompt state
class PromptNotifier extends StateNotifier<PromptState> {
  PromptNotifier() : super(const PromptState());

  final supabase = Supabase.instance.client;

  // Fetch all prompts from the database
  Future<void> fetchPrompts() async {
    state = state.copyWith(isLoading: true, error: '');

    try {
      // First, fetch all categories to get their names
      final categoriesResponse = await supabase.from(categoriesTable).select();

      final categories = ModelUtils.mapList<Category>(categoriesResponse, Category.fromJson);
      appLog.info('Fetched ${categories.length} categories for prompt lookup');

      // Then fetch all prompts
      final response = await supabase
          .from(promptsTable)
          .select()
          .order(promptCreatedAtColumn, ascending: false);

      // Simply parse the prompts from JSON
      final prompts = ModelUtils.mapList<Prompt>(response, Prompt.fromJson);

      state = state.copyWith(prompts: prompts, isLoading: false);
    } catch (e) {
      appLog.error('Failed to fetch prompts: $e');
      state = state.copyWith(error: 'Failed to fetch prompts: $e', isLoading: false);
    }
  }

  // Fetch instructions for all prompts
  Future<void> fetchAllInstructions() async {
    if (state.prompts.isEmpty) {
      await fetchPrompts();
    }

    if (state.prompts.isEmpty) return;

    state = state.copyWith(isLoading: true, error: '');

    try {
      final response = await supabase
          .from(promptInstructionsTable)
          .select()
          .inFilter(instructionPromptIdColumn, state.prompts.map((p) => p.id).toList());

      final fetchedInstructions = ModelUtils.mapList<PromptInstruction>(
        response,
        PromptInstruction.fromJson,
      );

      final newInstructions = Map<String, PromptInstruction>.from(state.instructions);
      for (final instruction in fetchedInstructions) {
        newInstructions[instruction.promptId] = instruction;
      }

      state = state.copyWith(instructions: newInstructions, isLoading: false);
    } catch (e) {
      state = state.copyWith(error: 'Failed to fetch instructions: $e', isLoading: false);
    }
  }

  // Fetch a single instruction for a prompt
  Future<void> fetchInstructionForPrompt(String promptId) async {
    if (state.instructions.containsKey(promptId)) return;

    state = state.copyWith(isLoading: true, error: '');

    try {
      final response =
          await supabase
              .from(promptInstructionsTable)
              .select()
              .eq(instructionPromptIdColumn, promptId)
              .single();

      final newInstructions = Map<String, PromptInstruction>.from(state.instructions);
      newInstructions[promptId] = ModelUtils.mapSingle<PromptInstruction>(
        response,
        PromptInstruction.fromJson,
      );

      state = state.copyWith(instructions: newInstructions, isLoading: false);
    } catch (e) {
      state = state.copyWith(error: 'Failed to fetch instruction: $e', isLoading: false);
    }
  }

  // Update the markdown content of an instruction
  Future<void> updateInstructionMarkdown(
    String instructionId,
    String promptId,
    String markdown,
  ) async {
    state = state.copyWith(isLoading: true, error: '');

    try {
      // Get current instruction to increment version
      final currentInstruction = state.instructions[promptId];
      final newVersion = (currentInstruction?.version ?? 0) + 1;

      await supabase
          .from(promptInstructionsTable)
          .update({
            instructionMarkdownColumn: markdown,
            instructionUpdatedAtColumn: DateTime.now().toIso8601String(),
            instructionVersionColumn: newVersion, // Increment version
          })
          .eq(instructionIdColumn, instructionId);

      final newInstructions = Map<String, PromptInstruction>.from(state.instructions);
      if (newInstructions.containsKey(promptId)) {
        final instruction = newInstructions[promptId]!;
        newInstructions[promptId] = PromptInstruction(
          id: instruction.id,
          promptId: instruction.promptId,
          markdown: markdown,
          createdAt: instruction.createdAt,
          updatedAt: DateTime.now(),
          version: newVersion, // Update version in local state
        );
      }

      state = state.copyWith(instructions: newInstructions, isLoading: false);
    } catch (e) {
      state = state.copyWith(error: 'Failed to update instruction: $e', isLoading: false);
    }
  }

  // Create a new prompt
  Future<void> createPrompt(
    String name,
    String description,
    String markdown, {
    String? categoryId,
  }) async {
    state = state.copyWith(isLoading: true, error: '');

    try {
      // Create prompt first with category information if provided
      final Map<String, dynamic> promptData = {
        promptNameColumn: name,
        promptDescriptionColumn: description,
        promptCreatedAtColumn: DateTime.now().toIso8601String(),
        promptUpdatedAtColumn: DateTime.now().toIso8601String(),
        promptVersionColumn: 1, // Initial version
        'source': 'cloud', // Required field with default value
      };

      // Add category ID if provided (we don't store category_name in the database)
      if (categoryId != null) {
        promptData[promptCategoryIdColumn] = categoryId;
        appLog.info('Setting category ID to: $categoryId for new prompt');
      }

      final promptResponse = await supabase.from(promptsTable).insert(promptData).select().single();

      final prompt = ModelUtils.mapSingle<Prompt>(promptResponse, Prompt.fromJson);

      // Only create instruction if markdown is not empty
      if (markdown.trim().isNotEmpty) {
        await supabase.from(promptInstructionsTable).insert({
          instructionPromptIdColumn: prompt.id,
          instructionMarkdownColumn: markdown,
          instructionCreatedAtColumn: DateTime.now().toIso8601String(),
          instructionUpdatedAtColumn: DateTime.now().toIso8601String(),
          instructionVersionColumn: 1, // Initial version
        });
      }

      // Refresh prompts
      await fetchPrompts();
      state = state.copyWith(isLoading: false);
    } catch (e) {
      state = state.copyWith(error: 'Failed to create prompt: $e', isLoading: false);
    }
  }

  // Update an existing prompt
  Future<void> updatePrompt(
    String promptId,
    String name,
    String description,
    String markdown, {
    String? categoryId,
  }) async {
    state = state.copyWith(isLoading: true, error: '');

    try {
      // Get current prompt to check if title, subtitle, or category has changed
      final currentPrompt = state.prompts.firstWhere((p) => p.id == promptId);

      // Always consider it changed if we're setting a category and it wasn't set before,
      // or if we're changing to a different category, or if we're clearing a category
      final hasPromptContentChanged =
          currentPrompt.title != name ||
          currentPrompt.subtitle != description ||
          categoryId !=
              currentPrompt
                  .categoryId; // This will catch all category changes, including null to non-null

      // Force update for debugging - remove this in production
      final forceUpdate = true;

      if (hasPromptContentChanged || forceUpdate) {
        // Increment prompt version if content has changed
        final newVersion = currentPrompt.version + 1;

        // Prepare update data
        final Map<String, dynamic> updateData = {
          promptNameColumn: name,
          promptDescriptionColumn: description,
          promptUpdatedAtColumn: DateTime.now().toIso8601String(),
          promptVersionColumn: newVersion, // Increment version
          'source': 'cloud', // Ensure source field is set consistently
        };

        // Always include category information in the update using the correct column constants
        // If categoryId is null, explicitly set it to null in the database
        updateData[promptCategoryIdColumn] = categoryId;

        // Log the category being set
        appLog.info('Setting category ID to: $categoryId');

        // Note: We're not updating category_name as it doesn't exist in the database schema
        // The error in logs confirms this: "Could not find the 'category_name' column of 'prompts'"

        // Ensure we're not missing any required fields
        if (!updateData.containsKey('source')) {
          updateData['source'] = 'cloud'; // Required field with default value
        }

        // Update prompt with incremented version and category info - with detailed error handling
        try {
          // Perform the update operation
          await supabase.from(promptsTable).update(updateData).eq('id', promptId);

          appLog.info('Update completed successfully');
        } catch (updateError) {
          // Try a direct update with only the category ID field
          try {
            appLog.info('Attempting simplified update with only category_id field');
            final simplifiedUpdate = {
              'category_id': categoryId,
              // Removed category_name as it doesn't exist in the schema
            };
            await supabase.from(promptsTable).update(simplifiedUpdate).eq('id', promptId);
            appLog.info('Simplified update completed successfully');
          } catch (directError) {
            appLog.error('Simplified update also failed: $directError');
          }
        }
      }

      // Check if instruction exists for this prompt
      final hasInstruction = state.instructions.containsKey(promptId);

      if (hasInstruction) {
        // Update existing instruction
        final instruction = state.instructions[promptId]!;

        // Only update if markdown content has changed
        if (instruction.markdown != markdown) {
          await updateInstructionMarkdown(instruction.id, promptId, markdown);
        }
      } else {
        // Create new instruction for existing prompt with initial version = 1
        await supabase.from(promptInstructionsTable).insert({
          instructionPromptIdColumn: promptId,
          instructionMarkdownColumn: markdown,
          instructionCreatedAtColumn: DateTime.now().toIso8601String(),
          instructionUpdatedAtColumn: DateTime.now().toIso8601String(),
          instructionVersionColumn: 1, // Initial version
        });

        // Fetch the newly created instruction
        await fetchInstructionForPrompt(promptId);
      }

      // Refresh prompts to update the UI
      await fetchPrompts();

      // Print the updated prompts to verify category information is included
      final updatedPrompts = state.prompts;
      final updatedPrompt = updatedPrompts.firstWhere(
        (p) => p.id == promptId,
        orElse:
            () => Prompt(
              id: '',
              title: '',
              subtitle: '',
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            ),
      );

      appLog.info(
        'After refresh - Updated prompt: ${updatedPrompt.title}, Category ID: ${updatedPrompt.categoryId}',
      );
    } catch (e) {
      state = state.copyWith(error: 'Failed to update prompt: $e', isLoading: false);
    }
  }
}

// Create the StateNotifierProvider to expose the PromptNotifier
final promptProvider = StateNotifierProvider<PromptNotifier, PromptState>((ref) {
  return PromptNotifier();
});

// Convenience providers for accessing specific parts of the state
final promptsProvider = Provider<List<Prompt>>((ref) {
  return ref.watch(promptProvider).prompts;
});

final promptLoadingProvider = Provider<bool>((ref) {
  return ref.watch(promptProvider).isLoading;
});

final promptErrorProvider = Provider<String?>((ref) {
  return ref.watch(promptProvider).error;
});

// Helper provider to get an instruction for a specific prompt
final promptInstructionProvider = Provider.family<PromptInstruction?, String>((ref, promptId) {
  return ref.watch(promptProvider).instructions[promptId];
});
