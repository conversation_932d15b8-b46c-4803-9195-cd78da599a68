class PromptInstruction {
  final String id;
  final String promptId;
  final String markdown;
  final DateTime createdAt;
  final DateTime updatedAt;
  final int version;

  PromptInstruction({
    required this.id,
    required this.promptId,
    required this.markdown,
    required this.createdAt,
    required this.updatedAt,
    this.version = 1,
  });

  factory PromptInstruction.fromJson(Map<String, dynamic> json) {
    return PromptInstruction(
      id: json['id'] ?? '',
      promptId: json['prompt_id'] ?? '',
      markdown: json['instruction_template'] ?? '',
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updated_at'] ?? DateTime.now().toIso8601String()),
      version: json['version'] ?? 1,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'prompt_id': promptId,
      'instruction_template': markdown,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'version': version,
    };
  }
}
