import 'dart:convert';

class Category {
  final String id;
  final String name; // Maps to 'title' in database
  final String? description; // Maps to 'subtitle' in database
  final String? iconName; // Maps to 'icon' in database
  final List<String> keywords;
  final DateTime createdAt;
  final DateTime updatedAt;

  Category({
    required this.id,
    required this.name,
    this.description,
    this.iconName,
    this.keywords = const [],
    required this.createdAt,
    required this.updatedAt,
  });

  factory Category.fromJson(Map<String, dynamic> json) {
    // Handle keywords which might be a JSON array in string form or a native list
    List<String> parseKeywords(dynamic keywordsData) {
      if (keywordsData == null) return [];
      if (keywordsData is List) return List<String>.from(keywordsData);
      if (keywordsData is String) {
        try {
          final parsed = jsonDecode(keywordsData);
          if (parsed is List) return List<String>.from(parsed);
        } catch (_) {}
      }
      return [];
    }

    return Category(
      id: json['id'] ?? '',
      name: json['title'] ?? '', // Maps from 'title' in database
      description: json['subtitle'], // Maps from 'subtitle' in database
      iconName: json['icon'], // Maps from 'icon' in database
      keywords: parseKeywords(json['keywords']),
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updated_at'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': name, // Maps to 'title' in database
      'subtitle': description, // Maps to 'subtitle' in database
      'icon': iconName, // Maps to 'icon' in database
      'keywords': keywords,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  Category copyWith({
    String? id,
    String? name,
    String? description,
    String? iconName,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Category(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      iconName: iconName ?? this.iconName,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
