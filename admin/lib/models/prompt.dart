class Prompt {
  final String id;
  final String title;
  final String subtitle;
  final DateTime createdAt;
  final DateTime updatedAt;
  final int version;
  final String? categoryId;

  Prompt({
    required this.id,
    required this.title,
    required this.subtitle,
    required this.createdAt,
    required this.updatedAt,
    this.version = 1,
    this.categoryId,
  });

  factory Prompt.fromJson(Map<String, dynamic> json) {
    return Prompt(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      subtitle: json['subtitle'] ?? '',
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updated_at'] ?? DateTime.now().toIso8601String()),
      version: json['version'] ?? 1,
      categoryId: json['category_id'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {
      'id': id,
      'title': title,
      'subtitle': subtitle,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'version': version,
    };

    if (categoryId != null) {
      data['category_id'] = categoryId;
    }

    return data;
  }
}
