# Promz

Promz is a comprehensive platform for managing and sharing prompts, featuring a Flutter-based client application, a Go-based API server, and a Flutter web admin interface.

## Table of Contents

1. [Introduction](#introduction)
2. [Getting Started](#getting-started)
3. [Client (Promz Mobile)](#client-promz-mobile)
4. [API](#api)
5. [Admin (Promz DB Admin)](#admin-promz-db-admin)
6. [Debugging](#debugging)
7. [Resources](#resources)

## Introduction

Promz is designed to provide a seamless experience for managing and sharing prompts across different platforms. The project includes a mobile client, an API server, and a web-based admin interface.

## Getting Started

### For Developers

1. **Development Setup**

   - Clone the repository
   - Install Flutter SDK
   - Run `flutter pub get` in the `client` and `admin` directories to install dependencies
   - Ensure Android SDK is installed with emulator component
   - Set `ANDROID_HOME` environment variable

2. **Development Workflow**
   - Create feature branches from `main`
   - Follow testing standards in [.github/copilot-instructions.md](.github/copilot-instructions.md)
   - Ensure tests pass before submitting PRs

For detailed setup instructions, refer to the specific README files for each component:

- [Client (Promz Mobile)](client/README.md)
- [API](api/scripts/README.md)
- [Admin (Promz DB Admin)](admin/README.md)

## Client (Promz Mobile)

A Flutter-based mobile application for managing and sharing prompts with trusted friends. For detailed setup and usage instructions, refer to the [Client README](client/README.md).

## API

This directory contains platform-specific client scripts for interacting with the Promz API. For detailed setup and usage instructions, refer to the [API README](api/scripts/README.md).

## Admin (Promz DB Admin)

A Flutter web application for managing prompts and categories for the Promz platform. For detailed setup and usage instructions, refer to the [Admin README](admin/README.md).

## Debugging

### Using Logcat/Pidcat

For effective debugging of the Promz application, we recommend using logcat (or preferably pidcat) to filter and view logs:

1. **Basic Logcat Usage**
   ```
   adb logcat | Select-String "Promz"
   ```

2. **Using Pidcat (Recommended)**
   
   Pidcat provides a more readable and colorized output format for Android logs. To use pidcat:
   
   ```
   # Install pidcat if not already installed
   pip install pidcat
   
   # Filter logs for the Promz application
   pidcat ai.promz
   
   # Filter logs for specific components
   pidcat ai.promz | Select-String "PromptSuggestions"
   pidcat ai.promz | Select-String "EntityDetection"
   pidcat ai.promz | Select-String "ClientContext"
   ```

3. **Common Debugging Filters**
   
   | Component | Command |
   |-----------|---------|
   | Prompt Suggestions | `pidcat ai.promz \| Select-String "PromptSuggestions"` |
   | Entity Detection | `pidcat ai.promz \| Select-String "EntityDetection"` |
   | Database Operations | `pidcat ai.promz \| Select-String "Database"` |
   | Authentication | `pidcat ai.promz \| Select-String "Auth"` |
   | Sync Operations | `pidcat ai.promz \| Select-String "Sync"` |

For more detailed debugging information, refer to the [Client README](client/README.md).

## Resources

For Flutter development help:

- [Lab: Write your first Flutter app](https://docs.flutter.dev/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://docs.flutter.dev/cookbook)
- [Online documentation](https://docs.flutter.dev/)
