# Promz Testing Suite

This directory contains comprehensive testing resources for the Promz application, including manual test cases, integration testing strategies, and automated UI tests using Maestro.

## Directory Structure

```
testing/
├── README.md                           # This file
├── integration-testing-strategy.md     # Strategy for service-level integration tests
├── manual_ui_test_cases.md            # Manual UI test cases for comprehensive testing
├── test_execution_progress.md         # Progress tracking for test execution
├── test_session_persistence.md        # Session persistence testing documentation
└── maestro/                           # Maestro automated UI tests
    ├── README.md                      # Maestro-specific documentation
    ├── android/                       # Android-specific test flows
    │   ├── authentication/            # Authentication flow tests
    │   │   ├── 01_pre_login_state.yaml
    │   │   ├── 02_login_flow.yaml
    │   │   ├── 03_post_login_state.yaml
    │   │   ├── 04_navigation_access.yaml
    │   │   └── 05_logout_flow.yaml
    │   └── common/                    # Reusable test components
    │       ├── app_launch.yaml
    │       ├── navigation_helpers.yaml
    │       └── assertions.yaml
    └── test_suites/                   # Test suite configurations
        └── authentication_suite.yaml
```

## Test Types

### Manual Testing
- **manual_ui_test_cases.md**: Comprehensive manual test cases covering all app functionality
- **integration-testing-strategy.md**: Strategy for testing service interactions

### Automated UI Testing (Maestro)
- **Authentication Flows**: Complete login/logout testing with state verification
- **Navigation Testing**: Verify UI changes based on authentication state
- **Cross-platform Support**: Tests designed for Android with iOS considerations

## Getting Started with Maestro Tests

### Quick Start (Recommended)
```bash
# Automated setup - installs everything and verifies setup
./testing/scripts/setup_test_environment.sh --all

# Interactive test runner with menu system
./testing/scripts/run_maestro_tests.sh
```

### Manual Setup
1. Install Maestro CLI: `curl -fsSL "https://get.maestro.mobile.dev" | bash`
2. Build and install the Promz Android app on your device/emulator
3. Ensure device is connected and accessible via `adb devices`

### Running Tests
```bash
# Interactive menu (recommended)
./testing/scripts/run_maestro_tests.sh

# Direct commands
maestro test testing/maestro/android/authentication/01_pre_login_state.yaml
maestro test testing/maestro/test_suites/authentication_suite.yaml
maestro test testing/maestro/android/authentication/

# Debug mode
maestro test --debug testing/maestro/android/authentication/01_pre_login_state.yaml
```

### Quick Reference
- **Setup Guide**: `testing/maestro/QUICKSTART.md`
- **Troubleshooting**: `testing/maestro/TROUBLESHOOTING.md`
- **Detailed Docs**: `testing/maestro/README.md`

### Test Development Guidelines
1. **Maintainable Selectors**: Use text-based selectors when possible for better maintainability
2. **Wait Conditions**: Include appropriate waits for network operations and animations
3. **Clear Assertions**: Each test should have clear success/failure criteria
4. **Modular Design**: Use nested flows for common operations
5. **Documentation**: Each test file includes clear descriptions of what it verifies

## Contributing
When adding new tests:
1. Follow the established directory structure
2. Use descriptive file names with numeric prefixes for execution order
3. Include comprehensive comments explaining test objectives
4. Test on both emulator and real devices when possible
5. Update this README when adding new test categories
