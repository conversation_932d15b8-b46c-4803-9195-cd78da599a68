# Register Protocol Handler for Promz
# This script registers the promz:// protocol handler for the Windows application
# Run as administrator to ensure registry changes can be made

# Prompt for admin rights if not already running as admin
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInR<PERSON>([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Warning "This script requires Administrator privileges to modify the registry."
    Write-Host "Please run this script as Administrator."
    Start-Sleep -Seconds 3
    exit
}

try {
    $appName = "Promz"
    $protocolName = "promz"
    $exePath = (Get-Item "$PSScriptRoot\..\client\build\windows\runner\Release\promz.exe").FullName
    
    # Check if the .exe exists at the specified location
    if (-not (Test-Path $exePath)) {
        Write-Error "Application executable not found at path: $exePath"
        exit 1
    }
    
    Write-Host "Registering protocol handler '$protocolName' for $appName..."
    Write-Host "Using executable path: $exePath"
    
    # Create the protocol key
    New-Item -Path "HKCU:\Software\Classes\$protocolName" -Force | Out-Null
    
    # Set the default value of the protocol key
    Set-ItemProperty -Path "HKCU:\Software\Classes\$protocolName" -Name "(Default)" -Value "URL:$appName Protocol"
    
    # Add URL Protocol to the protocol key
    Set-ItemProperty -Path "HKCU:\Software\Classes\$protocolName" -Name "URL Protocol" -Value ""
    
    # Create the DefaultIcon key and set its default value
    New-Item -Path "HKCU:\Software\Classes\$protocolName\DefaultIcon" -Force | Out-Null
    Set-ItemProperty -Path "HKCU:\Software\Classes\$protocolName\DefaultIcon" -Name "(Default)" -Value "$exePath,0"
    
    # Create the command key structure
    New-Item -Path "HKCU:\Software\Classes\$protocolName\shell\open\command" -Force | Out-Null
    
    # Set the command to execute when the protocol is invoked
    Set-ItemProperty -Path "HKCU:\Software\Classes\$protocolName\shell\open\command" -Name "(Default)" -Value "`"$exePath`" `"%1`""
    
    Write-Host "Protocol handler registration successful!"
    Write-Host "The application can now be launched via $protocolName:// links"
} catch {
    Write-Error "An error occurred while registering the protocol handler: $_"
    exit 1
}