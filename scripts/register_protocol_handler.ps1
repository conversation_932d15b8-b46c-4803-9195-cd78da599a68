# Register PromZ as a protocol handler for promz.ai URLs
# Must be run with administrator privileges

# Get the directory of the script
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Definition
$appPath = Join-Path (Split-Path -Parent $scriptPath) "client\build\windows\runner\Release\promz.exe"

# Check if the app exists
if (-not (Test-Path $appPath)) {
    Write-Host "Error: PromZ application not found at $appPath"
    Write-Host "Please build the application first."
    exit 1
}

# Define the protocol scheme
$protocolName = "promzai"
$protocolDescription = "PromZ.ai URL Protocol"

# Create registry entries
Write-Host "Registering $protocolName protocol handler..."

# Main protocol registration
New-Item -Path "HKCU:\Software\Classes\$protocolName" -Force | Out-Null
Set-ItemProperty -Path "HKCU:\Software\Classes\$protocolName" -Name "(Default)" -Value $protocolDescription

# URL protocol marker
Set-ItemProperty -Path "HKCU:\Software\Classes\$protocolName" -Name "URL Protocol" -Value ""

# Default icon
New-Item -Path "HKCU:\Software\Classes\$protocolName\DefaultIcon" -Force | Out-Null
Set-ItemProperty -Path "HKCU:\Software\Classes\$protocolName\DefaultIcon" -Name "(Default)" -Value "`"$appPath`",0"

# Command to execute
New-Item -Path "HKCU:\Software\Classes\$protocolName\shell\open\command" -Force | Out-Null
Set-ItemProperty -Path "HKCU:\Software\Classes\$protocolName\shell\open\command" -Name "(Default)" -Value "`"$appPath`" `"%1`""

# Register for https://promz.ai URLs
Write-Host "Registering https://promz.ai URL handler..."

# Create AppUserModelId for the app
$appUserModelId = "PromZ.App"

# Set the AppUserModelId for the application
New-Item -Path "HKCU:\Software\Classes\PromZAIURL\Application" -Force | Out-Null
Set-ItemProperty -Path "HKCU:\Software\Classes\PromZAIURL\Application" -Name "AppUserModelId" -Value $appUserModelId

# Register the app as a handler for https://promz.ai URLs
New-Item -Path "HKCU:\Software\Classes\.promzai" -Force | Out-Null
Set-ItemProperty -Path "HKCU:\Software\Classes\.promzai" -Name "(Default)" -Value "PromZAIURL"

New-Item -Path "HKCU:\Software\Classes\PromZAIURL" -Force | Out-Null
Set-ItemProperty -Path "HKCU:\Software\Classes\PromZAIURL" -Name "(Default)" -Value "PromZ.ai URL"
Set-ItemProperty -Path "HKCU:\Software\Classes\PromZAIURL" -Name "FriendlyTypeName" -Value "PromZ.ai URL"

New-Item -Path "HKCU:\Software\Classes\PromZAIURL\shell\open\command" -Force | Out-Null
Set-ItemProperty -Path "HKCU:\Software\Classes\PromZAIURL\shell\open\command" -Name "(Default)" -Value "`"$appPath`" `"%1`""

# Register the app as a web browser
New-Item -Path "HKCU:\Software\Clients\StartMenuInternet\PromZ" -Force | Out-Null
Set-ItemProperty -Path "HKCU:\Software\Clients\StartMenuInternet\PromZ" -Name "(Default)" -Value "PromZ"

New-Item -Path "HKCU:\Software\Clients\StartMenuInternet\PromZ\Capabilities" -Force | Out-Null
Set-ItemProperty -Path "HKCU:\Software\Clients\StartMenuInternet\PromZ\Capabilities" -Name "ApplicationDescription" -Value "PromZ AI Application"
Set-ItemProperty -Path "HKCU:\Software\Clients\StartMenuInternet\PromZ\Capabilities" -Name "ApplicationName" -Value "PromZ"

New-Item -Path "HKCU:\Software\Clients\StartMenuInternet\PromZ\Capabilities\URLAssociations" -Force | Out-Null
Set-ItemProperty -Path "HKCU:\Software\Clients\StartMenuInternet\PromZ\Capabilities\URLAssociations" -Name "https" -Value "PromZAIURL"

New-Item -Path "HKCU:\Software\RegisteredApplications" -Force | Out-Null
Set-ItemProperty -Path "HKCU:\Software\RegisteredApplications" -Name "PromZ" -Value "Software\Clients\StartMenuInternet\PromZ\Capabilities"

Write-Host "Protocol handler registration complete."
Write-Host "PromZ can now handle promz.ai URLs."
