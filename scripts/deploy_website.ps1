# Script to deploy website files to a Google Cloud Storage bucket configured for static website hosting.
# This script automates the process, adds error handling, and uses parameters for configuration.

param(
    [Parameter(Mandatory=$false)]
    [string]$BucketName = "www.promz.ai",

    [Parameter(Mandatory=$false)]
    [string]$ProjectID = "promz-ai",

    [Parameter(Mandatory=$false)]
    [string]$SourceDirectoryRelative = "..\web" # Relative path from this script to the web source directory
)

# Function to check if a command exists
function Test-CommandExists {
    param([string]$CommandName)
    # Corrected comparison order for $null
    return $null -ne (Get-Command $CommandName -ErrorAction SilentlyContinue)
}

# Function to execute a command and check for errors
# Function to execute a command and check for errors
function Invoke-CommandWithErrorCheck {
    param(
        [string]$Command,
        [string]$StepDescription
    )
    Write-Host "`nStep: $StepDescription"
    Write-Host "Executing: $Command"

    # Capture both standard output and standard error
    $Result = Invoke-Expression "$Command 2>&1"

    # Check the exit code
    if ($LASTEXITCODE -ne 0) {
        # Check if the error is specifically "No URLs matched" from gsutil (exit code 1)
        if ($LASTEXITCODE -eq 1 -and ($Result -match "CommandException: No URLs matched")) {
            Write-Warning "No files matched the pattern for step '$StepDescription'. Command: $Command. Continuing..."
            # Reset LASTEXITCODE for this specific non-fatal warning case if needed,
            # but typically just continuing is sufficient as the function doesn't explicitly return it.
        } else {
            # For all other errors, treat them as fatal
            Write-Error "Error executing command: $Command. Exit code: $LASTEXITCODE. Output/Error: $Result. Deployment aborted."
            exit 1 # Exit the script with a non-zero code indicating failure
        }
    } else {
        # If exit code is 0, print success message and any output
        Write-Host "Step completed successfully."
        if ($Result) {
            Write-Host "Output: $Result"
        }
    }
}

# --- Prerequisites Check ---
Write-Host "Checking prerequisites..."
if (-not (Test-CommandExists "gcloud")) {
    Write-Error "gcloud command not found. Please install Google Cloud SDK and ensure it's in your PATH."
    exit 1
}
if (-not (Test-CommandExists "gsutil")) {
    Write-Error "gsutil command not found. Please install Google Cloud SDK and ensure it's in your PATH."
    exit 1
}
Write-Host "Prerequisites met."

# --- Configuration ---
$SourcePath = Join-Path -Path $PSScriptRoot -ChildPath $SourceDirectoryRelative | Resolve-Path
$RedirectConfigFile = Join-Path -Path $SourcePath -ChildPath "redirect-config.xml"

Write-Host "Starting website deployment..."
Write-Host "Project ID: $ProjectID"
Write-Host "Bucket Name: $BucketName"
Write-Host "Source Directory: $SourcePath"

# --- Deployment Steps ---

# Step 1: Set Google Cloud Project
# Assumes user is already authenticated via `gcloud auth login` or service account.
Invoke-CommandWithErrorCheck -Command "gcloud config set project $ProjectID" -StepDescription "Set Google Cloud project"

# Step 2: Sync website files (including deleting removed files)
Invoke-CommandWithErrorCheck -Command "gsutil -m rsync -r -d `"$SourcePath`" gs://$BucketName" -StepDescription "Sync website files to bucket"

# Step 3: Set Content Types
# Use **/* pattern to catch files in subdirectories recursively
Invoke-CommandWithErrorCheck -Command "gsutil -m setmeta -h 'Content-Type:text/html' gs://$BucketName/**/*.html" -StepDescription "Set Content-Type for HTML files"
Invoke-CommandWithErrorCheck -Command "gsutil -m setmeta -h 'Content-Type:text/css' gs://$BucketName/**/*.css" -StepDescription "Set Content-Type for CSS files"
Invoke-CommandWithErrorCheck -Command "gsutil -m setmeta -h 'Content-Type:application/javascript' gs://$BucketName/**/*.js" -StepDescription "Set Content-Type for JavaScript files"
Invoke-CommandWithErrorCheck -Command "gsutil -m setmeta -h 'Content-Type:image/svg+xml' gs://$BucketName/**/*.svg" -StepDescription "Set Content-Type for SVG files"
# Add common image types for beta/images/
Invoke-CommandWithErrorCheck -Command "gsutil -m setmeta -h 'Content-Type:image/png' gs://$BucketName/beta/images/**/*.png" -StepDescription "Set Content-Type for PNG files in beta/images"
Invoke-CommandWithErrorCheck -Command "gsutil -m setmeta -h 'Content-Type:image/jpeg' gs://$BucketName/beta/images/**/*.jpg" -StepDescription "Set Content-Type for JPG files in beta/images"
Invoke-CommandWithErrorCheck -Command "gsutil -m setmeta -h 'Content-Type:image/jpeg' gs://$BucketName/beta/images/**/*.jpeg" -StepDescription "Set Content-Type for JPEG files in beta/images"
Invoke-CommandWithErrorCheck -Command "gsutil -m setmeta -h 'Content-Type:image/gif' gs://$BucketName/beta/images/**/*.gif" -StepDescription "Set Content-Type for GIF files in beta/images"
Invoke-CommandWithErrorCheck -Command "gsutil -m setmeta -h 'Content-Type:image/webp' gs://$BucketName/beta/images/**/*.webp" -StepDescription "Set Content-Type for WEBP files in beta/images"
# Specific files
Invoke-CommandWithErrorCheck -Command "gsutil -m setmeta -h 'Content-Type:application/json' gs://$BucketName/.well-known/apple-app-site-association" -StepDescription "Set Content-Type for Apple App Site Association"
Invoke-CommandWithErrorCheck -Command "gsutil -m setmeta -h 'Content-Type:text/plain' gs://$BucketName/robots.txt" -StepDescription "Set Content-Type for robots.txt"
Invoke-CommandWithErrorCheck -Command "gsutil -m setmeta -h 'Content-Type:application/xml' gs://$BucketName/sitemap.xml" -StepDescription "Set Content-Type for sitemap.xml"
Invoke-CommandWithErrorCheck -Command "gsutil -m setmeta -h 'Content-Type:image/x-icon' gs://$BucketName/favicon.ico" -StepDescription "Set Content-Type for favicon.ico"
# Add other specific file types as needed

# Step 4: Set Cache Control
Invoke-CommandWithErrorCheck -Command "gsutil -m setmeta -h 'Cache-Control:public, max-age=86400' gs://$BucketName/**/*.html" -StepDescription "Set Cache-Control for HTML files (1 day)"
Invoke-CommandWithErrorCheck -Command "gsutil -m setmeta -h 'Cache-Control:public, max-age=2592000' gs://$BucketName/**/*.css" -StepDescription "Set Cache-Control for CSS files (30 days)"
Invoke-CommandWithErrorCheck -Command "gsutil -m setmeta -h 'Cache-Control:public, max-age=2592000' gs://$BucketName/**/*.js" -StepDescription "Set Cache-Control for JavaScript files (30 days)"
Invoke-CommandWithErrorCheck -Command "gsutil -m setmeta -h 'Cache-Control:public, max-age=31536000' gs://$BucketName/images/*" -StepDescription "Set Cache-Control for root images (1 year)" # Assuming images are directly under /images
Invoke-CommandWithErrorCheck -Command "gsutil -m setmeta -h 'Cache-Control:public, max-age=31536000' gs://$BucketName/beta/images/**/*" -StepDescription "Set Cache-Control for beta images (1 year)" # Covers all files in beta/images
Invoke-CommandWithErrorCheck -Command "gsutil -m setmeta -h 'Cache-Control:no-cache' gs://$BucketName/.well-known/apple-app-site-association" -StepDescription "Set no-cache for Apple App Site Association"

# Step 5: Configure URL redirects (if config file exists)
if (Test-Path $RedirectConfigFile) {
    Invoke-CommandWithErrorCheck -Command "gsutil web set -m `"$RedirectConfigFile`" gs://$BucketName" -StepDescription "Configure URL redirects using $RedirectConfigFile"
} else {
    Write-Warning "Redirect configuration file not found at $RedirectConfigFile. Skipping redirect configuration."
}

# Step 6: Set default index and error documents
Invoke-CommandWithErrorCheck -Command "gsutil web set -m index.html -e redirect.html gs://$BucketName" -StepDescription "Set default index and error documents"

# --- Completion ---
Write-Host "`n-------------------------------------"
Write-Host "Website deployment to gs://$BucketName completed successfully!"
Write-Host "-------------------------------------"
Write-Host "Verification steps:"
Write-Host "- Access the website: https://$BucketName or https://storage.googleapis.com/$BucketName/"
Write-Host "- If using a custom domain, ensure DNS CNAME points to c.storage.googleapis.com"
Write-Host "- Test deep links if applicable."
