#!/bin/zsh

# Environment variables needed for the API to run
export PROMZ_DB_CONNECTION="postgresql://postgres.djqmdekosqnyxoqujxpm:<EMAIL>:5432/postgres"
export PROMZ_OPENAI_API_KEY="********************************************************************************************************************************************************************"
export PROMZ_GOOGLE_API_KEY="AIzaSyDLn4s--JsAOAf-RUBoTrr9z1tauJrk8zg"
export PROMZ_SUPABASE_URL="https://djqmdekosqnyxoqujxpm.supabase.co"
export PROMZ_SUPABASE_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRqcW1kZWtvc3FueXhvcXVqeHBtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzkzMDk1NDksImV4cCI6MjA1NDg4NTU0OX0.gXloMCWb_UN5fol9EZQoJTBK9_Ou_I0E6jVbfgN1mms"
export PROMZ_STORAGE_BUCKET="promz-content-upload"
export PROMZ_TRUSTED_PROXIES="127.0.0.1,::1"
export GRPC_PORT="50051"

# Variables for the API server
API_BASE_URL="http://localhost:8080"
LOG_DIR="$HOME/Library/Application Support/promz"
LOG_FILE="$LOG_DIR/api.log"

# ANSI Color Codes
GREEN='[0;32m'
YELLOW='[0;33m'
RED='[0;31m'
BLUE='[0;34m'
MAGENTA='[0;35m'
CYAN='[0;36m'
NC='[0m' # No Color

Start-ApiServer() {
    local mode="mock" # mock | debug | real
    local verbose_logging=false
    local debug_port=2345
    local process_pid=""

    while (( "$#" )); do
        case "$1" in
            --debug)
                mode="debug"
                verbose_logging=true # Debug implies verbose
                shift
                ;;
            --debug-port)
                if [[ -n "$2" && "$2" != --* ]]; then
                    debug_port="$2"
                    shift 2
                else
                    echo -e "${RED}Error: --debug-port requires an argument.${NC}" >&2
                    return 1
                fi
                ;;
            --real)
                mode="real"
                shift
                ;;
            --verbose)
                verbose_logging=true
                shift
                ;;
            *)
                echo -e "${RED}Error: Unsupported flag $1 in Start-ApiServer${NC}" >&2
                return 1
                ;;
        esac
    done

    echo -e "${YELLOW}Starting API server...${NC}"

    # Set execution mode env var
    if [[ "$mode" == "real" ]]; then
        export PROMZ_USE_REAL_EXECUTION="true"
        echo -e "${YELLOW}Using REAL execution mode (will make actual LLM API calls)${NC}"
    else
        export PROMZ_USE_REAL_EXECUTION="false"
        echo -e "${CYAN}Using MOCK execution mode (will display request details only)${NC}"
    fi
    
    # Set logging verbosity env var
    if [[ "$verbose_logging" == "true" || "$mode" == "debug" ]]; then
        export PROMZ_DEBUG="true"
        echo -e "${MAGENTA}Using VERBOSE logging mode (will show detailed logs)${NC}"
    else
        export PROMZ_DEBUG="false"
        echo -e "${GREEN}Using CONCISE logging mode (minimal logs)${NC}"
    fi

    # Create log directory if it doesn't exist
    mkdir -p "$LOG_DIR"
    echo -e "=== API Server Started: $(date) ===
" > "$LOG_FILE"

    local script_file_path
    script_file_path="${(%):-%x}" # Path to the script being sourced/run (script_utils.sh)
    local containing_script_dir
    containing_script_dir="$(dirname "$script_file_path")" # Directory of script_utils.sh
    local project_root
    project_root="$(cd "$containing_script_dir/.." && pwd)" # Project root is parent of the scripts directory
    
    local api_server_path="$project_root/api/cmd/server"
    
    if [[ ! -d "$api_server_path" ]]; then
        echo -e "${RED}Error: Cannot find API server path at: $api_server_path${NC}" >&2
        return 1
    fi
    
    # Run in a subshell to handle cd and backgrounding
    (
        cd "$api_server_path" || exit 1
        if [[ "$mode" == "debug" ]]; then
            echo "Starting dlv debug server on port $debug_port..."
            dlv debug --headless --listen=":$debug_port" --api-version=2 --accept-multiclient >> "$LOG_FILE" 2>&1 &
        else
            go run . >> "$LOG_FILE" 2>&1 &
        fi
        process_pid=$!
        echo -e "${GREEN}API server started with PID: $process_pid${NC}"
        echo "Log file: $LOG_FILE"
    )
    
    sleep 2 # Give the server a moment to start
    
    if Test-ApiServerRunning; then
        echo -e "${GREEN}API server is running successfully!${NC}"
    else
        echo -e "${YELLOW}Warning: API server may not have started correctly. Check the log file: $LOG_FILE${NC}"
    fi
    
    return 0
}

Stop-ApiServer() {
    echo -e "${YELLOW}Stopping API server...${NC}"
    local stopped_a_process=false

    # Stop process on gRPC port
    local grpc_port_to_check="${GRPC_PORT:-}"
    if [[ -n "$grpc_port_to_check" ]]; then
        echo -e "${CYAN}Checking for process on gRPC port $grpc_port_to_check...${NC}"
        local grpc_pids
        grpc_pids=$(lsof -iTCP:"$grpc_port_to_check" -sTCP:LISTEN -P -n -t 2>/dev/null)
        if [[ -n "$grpc_pids" ]]; then
            for pid in $grpc_pids; do
                local process_name
                process_name=$(ps -p "$pid" -o comm=)
                echo "Found process $process_name (PID: $pid) listening on gRPC port $grpc_port_to_check."
                if kill -0 "$pid" 2>/dev/null; then # Check if process exists before killing
                   kill -9 "$pid"
                   echo -e "${GREEN}Stopped process (PID: $pid) on gRPC port $grpc_port_to_check successfully.${NC}"
                   stopped_a_process=true
                else
                   echo -e "${YELLOW}Process (PID: $pid) on gRPC port $grpc_port_to_check already stopped.${NC}"
                fi
            done
        else
            echo -e "${GREEN}No process found listening on gRPC port $grpc_port_to_check.${NC}"
        fi
    else
        echo -e "${YELLOW}GRPC_PORT environment variable not set or empty. Skipping gRPC port check.${NC}"
    fi

    # Stop process on HTTP port (8080)
    local http_port=8080
    echo -e "${CYAN}Checking for process on HTTP port $http_port...${NC}"
    local http_pids
    http_pids=$(lsof -iTCP:"$http_port" -sTCP:LISTEN -P -n -t 2>/dev/null)
    if [[ -n "$http_pids" ]]; then
        for pid in $http_pids; do
            # Check if this PID was already handled by gRPC port stop
            local already_stopped=false
            if [[ -n "$grpc_pids" ]]; then
                for grpc_pid_iter in $grpc_pids; do
                    if [[ "$pid" == "$grpc_pid_iter" ]]; then
                        # If we are here, it means the kill command above might not have been issued if the process was already gone
                        # or it was killed. We check if it's still alive.
                        if ! kill -0 "$pid" 2>/dev/null; then
                           echo -e "${GREEN}Process (PID: $pid) on HTTP port $http_port was already stopped (was also using gRPC port).${NC}"
                           already_stopped=true
                           break
                        fi
                    fi
                done
            fi

            if [[ "$already_stopped" == "false" ]]; then
                local process_name
                process_name=$(ps -p "$pid" -o comm=)
                echo "Found process $process_name (PID: $pid) listening on HTTP port $http_port."
                 if kill -0 "$pid" 2>/dev/null; then
                    kill -9 "$pid"
                    echo -e "${GREEN}Stopped process (PID: $pid) on HTTP port $http_port successfully.${NC}"
                    stopped_a_process=true
                else
                    echo -e "${YELLOW}Process (PID: $pid) on HTTP port $http_port already stopped.${NC}"
                fi
            fi
        done
    else
        echo -e "${GREEN}No process found listening on HTTP port $http_port.${NC}"
    fi
    
    if [[ "$stopped_a_process" == "false" && -z "$grpc_pids" && -z "$http_pids" ]]; then
        echo -e "${GREEN}API server does not appear to be running.${NC}"
    fi
    return 0
}

Test-ApiServerRunning() {
    # Check by port using lsof
    if lsof -iTCP:8080 -sTCP:LISTEN -P -n -t >/dev/null 2>&1; then
        # echo -e "${GREEN}API server process found listening on port 8080.${NC}" # Optional: too verbose for just a test
        # Additionally, check health endpoint
        if curl -s -m 2 "$API_BASE_URL/health" 2>/dev/null | grep '"status":"ok"' > /dev/null; then
            # echo -e "${GREEN}API server is responding to health checks.${NC}" # Optional
            return 0 # True
        fi
    fi
    return 1 # False
}

Test-Prerequisites() {
    local prerequisites_ok=true
    # Check if Go is installed
    if ! command -v go >/dev/null 2>&1; then
        echo -e "${RED}Go is required. Download from: https://golang.org/dl/${NC}" >&2
        prerequisites_ok=false
    fi

    # Check if dlv is installed (for debug mode)
    if ! command -v dlv >/dev/null 2>&1; then
        echo -e "${YELLOW}Delve (dlv) is not installed. Debug mode for Go will not be available.${NC}" >&2
        echo -e "${YELLOW}Install with: go install github.com/go-delve/delve/cmd/dlv@latest${NC}" >&2
        # Not setting prerequisites_ok to false, as it's only for debug
    fi
    
    # Check if lsof is installed
    if ! command -v lsof >/dev/null 2>&1; then
        echo -e "${RED}lsof is required for stopping the server and checking its status. Please install it.${NC}" >&2
        prerequisites_ok=false
    fi

    # Check if curl is installed
     if ! command -v curl >/dev/null 2>&1; then
        echo -e "${RED}curl is required for API client operations. Please install it.${NC}" >&2
        prerequisites_ok=false
    fi
    
    # Check if jq is installed (useful for run_server.sh)
    if ! command -v jq >/dev/null 2>&1; then
        echo -e "${YELLOW}jq is recommended for JSON processing in API client operations. Consider installing it.${NC}" >&2
    fi


    # Check environment variables
    if [[ -z "$PROMZ_SUPABASE_URL" || -z "$PROMZ_SUPABASE_KEY" ]]; then
        echo -e "${RED}Missing PROMZ_SUPABASE_URL or PROMZ_SUPABASE_KEY. These should be set in this script or your environment.${NC}" >&2
        prerequisites_ok=false
    fi

    if [[ "$prerequisites_ok" == "false" ]]; then
        echo -e "${RED}One or more prerequisites are missing. Please install them and try again.${NC}" >&2
        return 1
    fi

    # Create log directory if it doesn't exist (already done in Start-ApiServer, but good for standalone check)
    mkdir -p "$LOG_DIR"
    return 0
}

# Example of how to get existing API process PIDs (not directly used by menu but good for util)
Get-ExistingApiProcessPids() {
    lsof -iTCP:8080 -sTCP:LISTEN -P -n -t 2>/dev/null
}
