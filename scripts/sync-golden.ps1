# Import shared git helper functions
Import-Module "$PSScriptRoot\Git-SyncHelpers.psm1"

# Navigate to the repository root
Set-Location "C:\projects\promz"

# Initialize submodules if not already initialized
git submodule update --init --recursive

# Check for pending changes and pushes in main repo
Write-Host "Checking for pending changes and pushes..."
if (-not (Test-UnpushedCommits)) {
    exit 1
}

# Get the list of submodules
$submodules = git config --file .gitmodules --get-regexp path | ForEach-Object { $_ -split " " | Select-Object -Last 1 }

foreach ($submodule in $submodules) {
    Write-Host "`nProcessing submodule: $submodule"
    
    # Navigate into the submodule
    Set-Location $submodule
    
    # Store original branch name
    $originalBranch = git rev-parse --abbrev-ref HEAD
    Write-Host "Current branch: $originalBranch"

    # Fetch everything
    Write-Host "Fetching updates for $submodule..."
    git fetch --all --tags
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Warning: Failed to fetch updates for $submodule. Proceeding with existing state."
    }

    # Check for unpushed commits in submodule
    if (-not (Test-UnpushedCommits -RepoName $submodule)) {
        exit 1
    }

    # Check if 'golden-build' tag exists
    $goldenBuildExists = git tag --list "golden-build"

    if ($goldenBuildExists) {
        Write-Host "Golden build found in $submodule. Attempting to check out golden build..."
        
        # Try to checkout golden-build
        git checkout golden-build
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Warning: Failed to checkout golden-build in $submodule."
            $fallbackConfirmation = Read-Host "Do you want to fall back to main branch? (y/n)"
            if ($fallbackConfirmation -eq 'y') {
                Write-Host "Falling back to main branch..."
                git checkout main
                git pull origin main
                if ($LASTEXITCODE -ne 0) {
                    Write-Host "Error: Failed to update main branch. Please check $submodule manually."
                    exit 1
                }
            } else {
                Write-Host "Keeping current state of $submodule. Please check manually."
                git checkout $originalBranch
            }
        } else {
            # Verify the state after checkout
            $subStatus = git status
            if ($subStatus -match "Changes not staged for commit:" -or $subStatus -match "Untracked files:") {
                Write-Host "Warning: Unexpected changes found after checking out golden-build in $submodule."
                $fallbackConfirmation = Read-Host "Do you want to fall back to main branch? (y/n)"
                if ($fallbackConfirmation -eq 'y') {
                    Write-Host "Falling back to main branch..."
                    git checkout main
                    git pull origin main
                } else {
                    Write-Host "Keeping current state. Please check $submodule manually."
                    git checkout $originalBranch
                }
            } else {
                Write-Host "Successfully synced $submodule to golden-build"
            }
        }
    } else {
        Write-Host "No golden build found in $submodule. Updating to latest main..."
        git checkout main
        git pull origin main
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Error: Failed to update main branch in $submodule. Please check manually."
            exit 1
        }
    }

    # Navigate back to the root directory
    Set-Location ..
}

Write-Host "`nSubmodule processing complete."
Write-Host "Please verify the state of all repositories manually before proceeding with your work."
