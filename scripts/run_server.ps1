# Windows Client Script for Promz Provider API
Set-StrictMode -Version Latest
$ErrorActionPreference = "Stop"

#region Colors for better readability
$GREEN = "`e[0;32m"
$YELLOW = "`e[0;33m"
$RED = "`e[0;31m"
$BLUE = "`e[0;34m"
$NC = "`e[0m" # No Color
#endregion

# Check platform
if ($PSVersionTable.Platform -and $PSVersionTable.Platform -ne "Win32NT") {
    Write-Host "${RED}❌ This script is for Windows only. Use unix_client.sh for Linux/macOS.${NC}"
    exit 1
}

# Import the API server utils
. (Join-<PERSON> (Split-Path $PSScriptRoot) "./scripts/script_utils.ps1")

# Constants
$API_BASE_URL = "http://localhost:8080"
$API_KEY = "30212873-5ab8-4f1a-83c1-1f13121b6549"

# API Operations
function Invoke-ApiRequest {
    param (
        [string]$Endpoint,
        [string]$Method = 'Get',
        $Body
    )
    
    $params = @{
        Uri = "$API_BASE_URL/$Endpoint"
        Method = $Method
        Headers = @{
            'X-API-Key' = $API_KEY
        }
    }
    if ($Body) {
        $params['Body'] = $Body | ConvertTo-Json
        $params['ContentType'] = 'application/json'
    }
    
    try {
        Invoke-RestMethod @params
    }
    catch {
        Write-Host "${RED}❌ API request failed: $($_.Exception.Message)${NC}"
        if ($_.Exception.Response) {
            try {
                $reader = [System.IO.StreamReader]::new($_.Exception.Response.GetResponseStream())
                $responseContent = $reader.ReadToEnd()
                Write-Host "${YELLOW}⚠️ Response content: $responseContent${NC}"
                $reader.Close()
            } catch {
                Write-Host "${RED}❌ Could not read response content: $_${NC}"
            }
        }
    }
}

function Read-MultilineInput {
    Write-Host @"

${YELLOW}Enter your prompt below:
- Press Enter twice after typing ':end:' to finish
- Press Ctrl+C to cancel
You can include blank lines in your prompt.
${NC}
"@

    $user_input = @()
    $end_marker = ':end:'
    try {
        while ($true) {
            $line = $Host.UI.ReadLine()

            # Add the line (including empty lines)
            $user_input += $line

            # Check for end marker
            if ($line -eq $end_marker) {
                break
            }
        }
        
        # Remove the last line if it's our end marker
        if ($user_input.Count -gt 0 -and $user_input[-1] -eq $end_marker) {
            $user_input = $user_input[0..($user_input.Count - 2)]
        }
        
        return $user_input -join "`n"
    }
    catch [System.Management.Automation.RuntimeException] {
        # This happens when Ctrl+C is pressed
        Write-Host "`n${YELLOW}Input cancelled.${NC}"
        return $null
    }
}

function Show-ApiMenu {
    while ($true) {
        Clear-Host
        Write-Host "${BLUE}===============================${NC}"
        Write-Host "${BLUE}  🚀 Promz API Server Tool 🚀  ${NC}"
        Write-Host "${BLUE}===============================${NC}"
        Write-Host "${GREEN}=== API Server Operations ===${NC}"
        Write-Host "${GREEN}[1] Start API Server (Mock mode - concise logs)${NC}"
        Write-Host "${GREEN}[2] Start API Server (Mock mode - verbose logs)${NC}"
        Write-Host "${GREEN}[3] Start API Server (Debug mode - verbose logs)${NC}"
        Write-Host "${GREEN}[4] Start API Server (Real execution - concise logs)${NC}"
        Write-Host "${GREEN}[5] Start API Server (Real execution - verbose logs)${NC}"
        Write-Host "${GREEN}[6] Stop API Server${NC}"
        Write-Host "${GREEN}[7] Test API Health${NC}"
        Write-Host "${GREEN}=== API Client Operations ===${NC}"
        Write-Host "${GREEN}[8] Get Prompt by ID${NC}"
        Write-Host "${GREEN}[9] Create New Prompt${NC}"
        Write-Host "${GREEN}[10] Execute LLM Request${NC}"
        Write-Host "${GREEN}[11] List All Prompts${NC}"
        Write-Host "${GREEN}=== Other ===${NC}"
        Write-Host "${GREEN}[0] Exit${NC}"
        Write-Host "${BLUE}===============================${NC}"
        
        $choice = Read-Host "Choose an option (0-10)"

        switch ($choice) {
            '1' {
                Start-ApiServer
                exit
            }
            '2' {
                Start-ApiServer -VerboseLogging
                exit
            }
            '3' {
                Start-ApiServer -Debug -VerboseLogging
                exit
            }
            '4' {
                Start-ApiServer -UseRealExecution
                exit
            }
            '5' {
                Start-ApiServer -UseRealExecution -VerboseLogging
                exit
            }
            '6' {
                Stop-ApiServer
            }
            '7' {
                Write-Host "`n${YELLOW}Testing API health...${NC}"
                try {
                    $health = Invoke-RestMethod -Uri "$API_BASE_URL/health" -Method Get
                    Write-Host "${GREEN}API Health Status: $($health.status)${NC}"
                } catch {
                    Write-Host "${RED}❌ API health check failed: $_${NC}"
                }
                Read-Host "Press Enter to continue..."
            }
            '8' {
                if (-not (Test-ApiServerRunning)) {
                    Write-Host "${RED}❌ API server is not running. Please start it first.${NC}"
                    Read-Host "Press Enter to continue..."
                    continue
                }
                
                $promptId = Read-Host "Enter prompt ID"
                if ([string]::IsNullOrWhiteSpace($promptId)) {
                    Write-Host "${YELLOW}Empty prompt ID, operation cancelled.${NC}"
                    Read-Host "Press Enter to continue..."
                    continue
                }
                
                Write-Host "`n${YELLOW}Fetching prompt with ID: $promptId...${NC}"
                
                try {
                    $response = Invoke-ApiRequest "api/prompts/$promptId" 'Get'
                    if ($response) {
                        Write-Host "`n${BLUE}Prompt Details:${NC}"
                        Write-Host "ID: $($response.id)"
                        Write-Host "Title: $($response.title)"
                        Write-Host "Content: $($response.content)"
                        Write-Host "Tags: $($response.tags -join ', ')"
                    }
                } catch {
                    Write-Host "${RED}❌ Error fetching prompt: $_${NC}"
                }
                Read-Host "Press Enter to continue..."
            }
            '9' {
                if (-not (Test-ApiServerRunning)) {
                    Write-Host "${RED}❌ API server is not running. Please start it first.${NC}"
                    Read-Host "Press Enter to continue..."
                    continue
                }
                
                Write-Host "`n${YELLOW}Create a new prompt:${NC}"
                $title = Read-Host "Enter prompt title"
                $content = Read-MultilineInput
                
                if ([string]::IsNullOrWhiteSpace($title) -or [string]::IsNullOrWhiteSpace($content)) {
                    Write-Host "${YELLOW}Title or content is empty, operation cancelled.${NC}"
                    Read-Host "Press Enter to continue..."
                    continue
                }
                
                $tagsInput = Read-Host "Enter tags (comma-separated)"
                $tags = $tagsInput -split ',' | ForEach-Object { $_.Trim() } | Where-Object { $_ }
                
                $promptData = @{
                    title = $title
                    content = $content
                    tags = $tags
                }
                
                $response = Invoke-ApiRequest 'api/prompts' 'Post' $promptData
                if ($response -and $response.id) {
                    Write-Host "`n${GREEN}✅ Prompt created successfully with ID: $($response.id)${NC}"
                } else {
                    Write-Host "${RED}❌ Failed to create prompt${NC}"
                }
                Read-Host "Press Enter to continue..."
            }
            '10' {
                if (-not (Test-ApiServerRunning)) {
                    Write-Host "${RED}❌ API server is not running. Please start it first.${NC}"
                    Read-Host "Press Enter to continue..."
                    continue
                }
                
                Write-Host "`n${YELLOW}Execute an LLM request:${NC}"
                $prompt = Read-MultilineInput
                
                if ([string]::IsNullOrWhiteSpace($prompt)) {
                    Write-Host "${YELLOW}Empty prompt, operation cancelled.${NC}"
                    Read-Host "Press Enter to continue..."
                    continue
                }
                
                $params = @{
                    prompt = $prompt
                }
                
                $response = Invoke-ApiRequest 'api/llm/execute' 'Post' $params
                if ($response) {
                    Write-Host "`n${BLUE}LLM Response:${NC}"
                    Write-Host "Model: $($response.model)"
                    Write-Host "Tokens: $($response.tokens)"
                    Write-Host "`n$($response.result)"
                }
                Read-Host "Press Enter to continue..."
            }
            '11' {
                if (-not (Test-ApiServerRunning)) {
                    Write-Host "${RED}❌ API server is not running. Please start it first.${NC}"
                    Read-Host "Press Enter to continue..."
                    continue
                }
                
                Write-Host "`n${YELLOW}Fetching all prompts...${NC}"
                
                # Note: This is a mock implementation since the API doesn't have a list endpoint yet
                # In a real implementation, you would call the API endpoint that lists all prompts
                
                $mockPrompts = @(
                    @{
                        id = "sample1"
                        title = "Sample Prompt 1"
                        subtitle = "This is sample prompt 1"
                        tags = @("sample", "test")
                    },
                    @{
                        id = "sample2"
                        title = "Sample Prompt 2"
                        subtitle = "This is sample prompt 2"
                        tags = @("sample", "demo")
                    },
                    @{
                        id = "blabla"
                        title = "Sample Prompt"
                        subtitle = "This is a sample prompt"
                        tags = @("sample", "test")
                    }
                )
                
                Write-Host "`n${GREEN}Available Prompts:${NC}"
                foreach ($prompt in $mockPrompts) {
                    Write-Host "${BLUE}ID: $($prompt.id)${NC}"
                    Write-Host "Title: $($prompt.title)"
                    Write-Host "Subtitle: $($prompt.subtitle)"
                    Write-Host "Tags: $($prompt.tags -join ', ')"
                    Write-Host ""
                }
                
                Write-Host "${YELLOW}Note: This is showing mock data. Implement a real API endpoint for listing all prompts.${NC}"
                Read-Host "Press Enter to continue..."
            }
            '0' {
                Write-Host "${GREEN}👋 Goodbye!${NC}"
                exit
            }
            default { 
                Write-Host "${RED}❌ Invalid choice${NC}" 
                Read-Host "Press Enter to continue..."
            }
        }
    }
}

# Main execution
Test-Prerequisites
Show-ApiMenu
