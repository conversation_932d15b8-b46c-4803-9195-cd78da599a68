#!/bin/zsh

# Zsh Client Script for Promz Provider API

# Source the API server utils
# Get the directory of the current script
SCRIPT_DIR="$(cd "$(dirname "${(%):-%x}")" && pwd)"
UTILS_PATH="$SCRIPT_DIR/script_utils.sh"

if [[ -f "$UTILS_PATH" ]]; then
    source "$UTILS_PATH"
else
    echo -e "${RED}Error: script_utils.sh not found at $UTILS_PATH${NC}" >&2
    exit 1
fi

# API Operations
Invoke-ApiRequest() {
    local endpoint="$1"
    local method="${2:-GET}" # Default to GET if not provided
    local body_data="$3"
    local response_json
    local http_status

    local curl_args=(
        -s -w "%{http_code}" # -s for silent, -w to output HTTP status code
        -X "$method"
        -H "X-API-Key: $API_KEY"
    )

    if [[ -n "$body_data" ]]; then
        curl_args+=(-H "Content-Type: application/json" -d "$body_data")
    fi

    # Perform the request and capture both body and status code
    # The last line of output from curl (due to -w) will be the status code.
    # All other lines are the body.
    response_with_status=$(curl "${curl_args[@]}" "$API_BASE_URL/$endpoint")
    
    # Extract status code (last line)
    http_status=$(echo "$response_with_status" | tail -n 1)
    # Extract body (everything except last line)
    response_json=$(echo "$response_with_status" | sed '$d')

    if [[ "$http_status" -ge 200 && "$http_status" -lt 300 ]]; then
        echo "$response_json" # Output JSON response on success
        return 0
    else
        echo -e "${RED}API request failed with status: $http_status${NC}" >&2
        echo -e "${YELLOW}Response: $response_json${NC}" >&2
        return 1
    fi
}

Read-MultilineInput() {
    echo -e "\n${YELLOW}Enter your prompt below:
- Type ':end:' on a new line and press Enter to finish.
- Press Ctrl+D on an empty line or Ctrl+C to cancel.
You can include blank lines in your prompt.${NC}"

    local lines=()
    local line
    while IFS= read -r line; do
        if [[ "$line" == ":end:" ]]; then
            break
        fi
        lines+=("$line")
    done
    
    # Check if input was cancelled (Ctrl+D on empty line results in empty lines array if nothing was typed)
    # Ctrl+C will typically terminate the loop or script section, handled by trap if set.
    # If lines array is empty and the last command (read) failed, it might be Ctrl+D.
    if [[ ${#lines[@]} -eq 0 && $? -ne 0 && -z "$line" ]]; then # Heuristic for Ctrl+D
        echo -e "\n${YELLOW}Input cancelled.${NC}"
        return 1 # Indicate cancellation
    fi

    printf '%s\n' "${lines[@]}"
    return 0
}

Show-ApiMenu() {
    while true; do
        clear # Use clear for Zsh/macOS
        echo -e "${BLUE}===============================${NC}"
        echo -e "${BLUE}  🚀 Promz API Server Tool 🚀  ${NC}"
        echo -e "${BLUE}===============================${NC}"
        echo -e "${GREEN}=== API Server Operations ===${NC}"
        echo -e "${GREEN}[1] Start API Server (Mock mode - concise logs)${NC}"
        echo -e "${GREEN}[2] Start API Server (Mock mode - verbose logs)${NC}"
        echo -e "${GREEN}[3] Start API Server (Debug mode - verbose logs)${NC}"
        echo -e "${GREEN}[4] Start API Server (Real execution - concise logs)${NC}"
        echo -e "${GREEN}[5] Start API Server (Real execution - verbose logs)${NC}"
        echo -e "${GREEN}[6] Stop API Server${NC}"
        echo -e "${GREEN}[7] Test API Health${NC}"
        echo -e "${GREEN}=== API Client Operations ===${NC}"
        echo -e "${GREEN}[8] Get Prompt by ID${NC}"
        echo -e "${GREEN}[9] Create New Prompt${NC}"
        echo -e "${GREEN}[10] Execute LLM Request${NC}"
        echo -e "${GREEN}[11] List All Prompts (Mocked)${NC}"
        echo -e "${GREEN}=== Other ===${NC}"
        echo -e "${GREEN}[0] Exit${NC}"
        echo -e "${BLUE}===============================${NC}"
        
        local choice
        read -r "choice?Choose an option (0-11): "

        case "$choice" in
            1) Start-ApiServer ; exit ;; # Exit after starting to let server run
            2) Start-ApiServer --verbose ; exit ;; 
            3) Start-ApiServer --debug --verbose ; exit ;; 
            4) Start-ApiServer --real ; exit ;; 
            5) Start-ApiServer --real --verbose ; exit ;; 
            6) Stop-ApiServer ;; 
            7) 
                echo -e "\n${YELLOW}Testing API health...${NC}"
                local health_response
                health_response=$(curl -s -X GET "$API_BASE_URL/health")
                if echo "$health_response" | jq -e '.status == "ok"' > /dev/null; then
                    echo -e "${GREEN}API Health Status: ok${NC}"
                else
                    echo -e "${RED}API health check failed.${NC}"
                    echo -e "${YELLOW}Response: $health_response${NC}"
                fi
                read -k1 -s "?Press any key to continue..." && echo
                ;;
            8) 
                if ! Test-ApiServerRunning; then
                    echo -e "${RED}API server is not running. Please start it first.${NC}"
                    read -k1 -s "?Press any key to continue..." && echo
                    continue
                fi
                
                local prompt_id
                read -r "prompt_id?Enter prompt ID: "
                if [[ -z "$prompt_id" ]]; then
                    echo -e "${YELLOW}Empty prompt ID, operation cancelled.${NC}"
                    read -k1 -s "?Press any key to continue..." && echo
                    continue
                fi
                
                echo -e "\n${YELLOW}Fetching prompt with ID: $prompt_id...${NC}"
                local response
                response=$(Invoke-ApiRequest "api/prompts/$prompt_id" "GET")
                if [[ $? -eq 0 && -n "$response" ]]; then
                    echo -e "\n${BLUE}Prompt Details:${NC}"
                    echo "ID: $(echo "$response" | jq -r '.id')"
                    echo "Title: $(echo "$response" | jq -r '.title')"
                    echo "Content: $(echo "$response" | jq -r '.content')"
                    echo "Tags: $(echo "$response" | jq -r '.tags | join(", ")')"
                fi
                read -k1 -s "?Press any key to continue..." && echo
                ;;
            9) 
                if ! Test-ApiServerRunning; then
                    echo -e "${RED}API server is not running. Please start it first.${NC}"
                    read -k1 -s "?Press any key to continue..." && echo
                    continue
                fi
                
                echo -e "\n${YELLOW}Create a new prompt:${NC}"
                local title content tags_input
                read -r "title?Enter prompt title: "
                content=$(Read-MultilineInput)
                if [[ $? -ne 0 || -z "$title" || -z "$content" ]]; then # Check Read-MultilineInput status
                    echo -e "${YELLOW}Title or content is empty, or input cancelled. Operation cancelled.${NC}"
                    read -k1 -s "?Press any key to continue..." && echo
                    continue
                fi
                
                read -r "tags_input?Enter tags (comma-separated): "
                local tags_json
                tags_json=$(echo "$tags_input" | tr ',' '\n' | jq -R . | jq -s .)
                
                local prompt_data
                # Use jq to construct the JSON payload safely
                prompt_data=$(jq -n --arg title "$title" --arg content "$content" --argjson tags "$tags_json" \
                                '{title: $title, content: $content, tags: $tags}')
                
                local create_response
                create_response=$(Invoke-ApiRequest 'api/prompts' 'POST' "$prompt_data")
                if [[ $? -eq 0 && $(echo "$create_response" | jq -r '.id') != "null" ]]; then
                    echo -e "\n${GREEN}✅ Prompt created successfully with ID: $(echo "$create_response" | jq -r '.id')${NC}"
                else
                    echo -e "${RED}❌ Failed to create prompt${NC}"
                fi
                read -k1 -s "?Press any key to continue..." && echo
                ;;
            10) 
                if ! Test-ApiServerRunning; then
                    echo -e "${RED}API server is not running. Please start it first.${NC}"
                    read -k1 -s "?Press any key to continue..." && echo
                    continue
                fi
                
                echo -e "\n${YELLOW}Execute an LLM request:${NC}"
                local llm_prompt
                llm_prompt=$(Read-MultilineInput)
                if [[ $? -ne 0 || -z "$llm_prompt" ]]; then
                    echo -e "${YELLOW}Empty prompt or input cancelled. Operation cancelled.${NC}"
                    read -k1 -s "?Press any key to continue..." && echo
                    continue
                fi
                
                local llm_params
                llm_params=$(jq -n --arg prompt "$llm_prompt" '{prompt: $prompt}')
                
                local llm_response
                llm_response=$(Invoke-ApiRequest 'api/llm/execute' 'POST' "$llm_params")
                if [[ $? -eq 0 && -n "$llm_response" ]]; then
                    echo -e "\n${BLUE}LLM Response:${NC}"
                    echo "Model: $(echo "$llm_response" | jq -r '.model')"
                    echo "Tokens: $(echo "$llm_response" | jq -r '.tokens')"
                    echo -e "\n$(echo "$llm_response" | jq -r '.result')"
                fi
                read -k1 -s "?Press any key to continue..." && echo
                ;;
            11) 
                if ! Test-ApiServerRunning; then
                    echo -e "${RED}API server is not running. Please start it first.${NC}"
                    read -k1 -s "?Press any key to continue..." && echo
                    continue
                fi
                
                echo -e "\n${YELLOW}Fetching all prompts... (Mocked Data)${NC}"
                
                # Mocked data as in PowerShell script
                cat << EOF
${GREEN}Available Prompts:${NC}
${BLUE}ID: sample1${NC}
Title: Sample Prompt 1
Subtitle: This is sample prompt 1
Tags: sample, test

${BLUE}ID: sample2${NC}
Title: Sample Prompt 2
Subtitle: This is sample prompt 2
Tags: sample, demo

${BLUE}ID: blabla${NC}
Title: Sample Prompt
Subtitle: This is a sample prompt
Tags: sample, test

${YELLOW}Note: This is showing mock data. Implement a real API endpoint for listing all prompts.${NC}
EOF
                read -k1 -s "?Press any key to continue..." && echo
                ;;
            0) 
                echo -e "${GREEN}👋 Goodbye!${NC}"
                exit 0
                ;;
            *) 
                echo -e "${RED}❌ Invalid choice${NC}" 
                read -k1 -s "?Press any key to continue..." && echo
                ;;
        esac
    done
}

# Main execution
if ! Test-Prerequisites; then
    exit 1
fi
Show-ApiMenu
