# Protocol Buffer Generation Script for Promz
Set-StrictMode -Version Latest
$ErrorActionPreference = "Stop"

# Set up directories
$PROJ_ROOT = Split-Path $PSScriptRoot -Parent
$PROTO_DIR = Join-Path $PROJ_ROOT "api\proto\v1"
$GO_OUT_DIR = Join-Path $PROJ_ROOT "api\proto\gen"
$DART_OUT_DIR = Join-Path $PROJ_ROOT "client\lib\generated"

Write-Host "Starting Protocol Buffer code generation..." -ForegroundColor Cyan

# Ensure output directories exist
if (-not (Test-Path $DART_OUT_DIR)) {
    Write-Host "Creating Dart output directory: $DART_OUT_DIR" -ForegroundColor Yellow
    New-Item -ItemType Directory -Path $DART_OUT_DIR -Force | Out-Null
}

# Check if protoc is installed
try {
    $protocVersion = protoc --version
    Write-Host "Found Protocol Buffer compiler: $protocVersion" -ForegroundColor Green
} catch {
    Write-Host "Error: Protocol Buffer compiler (protoc) not found." -ForegroundColor Red
    Write-Host "Please install protoc using: choco install protoc" -ForegroundColor Yellow
    exit 1
}

# Generate Go code
Write-Host "Generating Go code from proto files..." -ForegroundColor Cyan
try {
    Push-Location $PROJ_ROOT
    
    # Create output directory
    if (-not (Test-Path $GO_OUT_DIR)) {
        New-Item -ItemType Directory -Path $GO_OUT_DIR -Force | Out-Null
    }
    
    # Generate Go code from proto files
    protoc --proto_path=$PROTO_DIR `
           --go_out=paths=source_relative:$GO_OUT_DIR `
           --go-grpc_out=paths=source_relative:$GO_OUT_DIR `
           "$PROTO_DIR\common.proto" "$PROTO_DIR\content_upload.proto" "$PROTO_DIR\file_metadata.proto" "$PROTO_DIR\whatsapp_metadata.proto" "$PROTO_DIR\websocket.proto"
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Successfully generated Go code." -ForegroundColor Green
    } else {
        Write-Host "Error generating Go code. Exit code: $LASTEXITCODE" -ForegroundColor Red
        exit $LASTEXITCODE
    }
    Pop-Location
} catch {
    Write-Host "Error generating Go code: $_" -ForegroundColor Red
    exit 1
}

# Check if Flutter is installed and add required dependencies
Write-Host "Checking Flutter installation and adding dependencies..." -ForegroundColor Cyan
try {
    Push-Location (Join-Path $PROJ_ROOT "client")
    
    # Check if Flutter is installed
    $flutterVersion = flutter --version
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Found Flutter installation: $($flutterVersion -split '\n' | Select-Object -First 1)" -ForegroundColor Green
        
        # Add required dependencies
        Write-Host "Adding required Dart dependencies..." -ForegroundColor Cyan
        flutter pub add protobuf
        flutter pub add grpc
        flutter pub add fixnum
        
        # Activate protoc_plugin globally
        Write-Host "Activating protoc_plugin for Dart..." -ForegroundColor Cyan
        flutter pub global activate protoc_plugin
        
        # Add pub cache bin to PATH temporarily
        $pubCacheBin = Join-Path $env:USERPROFILE "AppData\Local\Pub\Cache\bin"
        $env:PATH += ";$pubCacheBin"
        
        Write-Host "Pub cache bin added to PATH: $pubCacheBin" -ForegroundColor Green
    } else {
        Write-Host "Warning: Flutter not found. Skipping Dart code generation." -ForegroundColor Yellow
        Pop-Location
        exit 0
    }
    Pop-Location
} catch {
    Write-Host "Error setting up Flutter dependencies: $_" -ForegroundColor Red
    exit 1
}

# Generate Dart code
Write-Host "Generating Dart code from proto files..." -ForegroundColor Cyan
try {
    Push-Location $PROJ_ROOT
    protoc --proto_path=$PROTO_DIR `
           --dart_out=grpc:$DART_OUT_DIR `
           "$PROTO_DIR\*.proto"
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Successfully generated Dart code." -ForegroundColor Green
    } else {
        Write-Host "Error generating Dart code. Exit code: $LASTEXITCODE" -ForegroundColor Red
        exit $LASTEXITCODE
    }
    Pop-Location
} catch {
    Write-Host "Error generating Dart code: $_" -ForegroundColor Red
    exit 1
}

Write-Host "Protocol Buffer code generation completed successfully!" -ForegroundColor Green
Write-Host "Go code generated in: $GO_OUT_DIR" -ForegroundColor Cyan
Write-Host "Dart code generated in: $DART_OUT_DIR" -ForegroundColor Cyan
