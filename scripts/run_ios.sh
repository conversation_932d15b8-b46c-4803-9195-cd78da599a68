#!/bin/bash

# iOS Client Script for Promz
# This script provides functionalities to build, run, and test the Flutter application on iOS simulators and devices.

# Exit immediately if a command exits with a non-zero status.
set -e

# --- Configuration ---

# Get the absolute path of the script's directory
SCRIPTS_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Navigate to the project root directory (assuming scripts is a child of project root)
PROJECT_ROOT_DIR="$(cd "$SCRIPTS_DIR/.." && pwd)"
CLIENT_DIR="$PROJECT_ROOT_DIR/client"
COMMON_PKG_DIR="$PROJECT_ROOT_DIR/packages/promz_common"
ORIGINAL_DIR="$(pwd)"

# Variables to store selected simulator UDID
SELECTED_SIM_UDID=""
SELECTED_SIM_NAME=""

# --- Helper Functions ---

# Function to print messages
info() {
  echo -e "\033[1;34m[INFO]\033[0m $1"
}

warn() {
  echo -e "\033[1;33m[WARN]\033[0m $1"
}

error() {
  echo -e "\033[1;31m[ERROR]\033[0m $1" >&2
}

check_dependencies() {
  info "Checking dependencies..."
  local missing_deps=0
  if ! command -v flutter &> /dev/null; then
    error "Flutter SDK not found. Please install Flutter and add it to your PATH."
    missing_deps=1
  fi
  if ! command -v xcrun &> /dev/null; then
    error "Xcode command line tools (xcrun) not found. Please install Xcode and ensure command line tools are selected."
    missing_deps=1
  fi
  if ! command -v jq &> /dev/null; then
    warn "jq (JSON processor) not found. Attempting to install using Homebrew..."
    if command -v brew &> /dev/null; then
      brew install jq || { error "Failed to install jq. Please install it manually."; missing_deps=1; }
    else
      error "Homebrew not found. Please install jq manually."
      missing_deps=1
    fi
  fi
  if [ "$missing_deps" -eq 1 ]; then
    exit 1
  fi
  info "All essential dependencies are present."
}

navigate_to_client_dir() {
  info "Navigating to client directory: $CLIENT_DIR"
  cd "$CLIENT_DIR" || { error "Failed to navigate to client directory: $CLIENT_DIR"; exit 1; }
}

navigate_to_common_pkg_dir() {
  info "Navigating to common package directory: $COMMON_PKG_DIR"
  cd "$COMMON_PKG_DIR" || { error "Failed to navigate to common package directory: $COMMON_PKG_DIR"; exit 1; }
}

cleanup_and_exit() {
  info "Restoring original directory: $ORIGINAL_DIR"
  cd "$ORIGINAL_DIR"
  if [ ! -z "$SELECTED_SIM_UDID" ]; then
    read -p "Do you want to shut down the simulator '$SELECTED_SIM_NAME'? (y/N): " shutdown_choice
    if [[ "$shutdown_choice" =~ ^[Yy]$ ]]; then
      shutdown_simulator "$SELECTED_SIM_UDID"
    fi
  fi
  info "Exiting script."
  exit 0
}

# Trap EXIT signal to ensure cleanup
trap cleanup_and_exit EXIT
# Trap INT and TERM signals for graceful exit
trap 'echo; warn "Script interrupted."; cleanup_and_exit' INT TERM


# --- Simulator Management Functions ---

list_simulators() {
  info "Fetching available iOS simulators..."
  # Only show booted or shutdown simulators that are for iOS
  xcrun simctl list devices available --json | jq -r '.devices[] | map(select(.isAvailable == true and .name != null and .udid != null and .state != "Unavailable")) | .[]? | "\(.name) (\(.state)) - UDID: \(.udid)"'
}

select_simulator() {
  info "Available iOS Simulators:"
  # Get simulators, filter for iOS, and format for selection
  # Ensure we only list iOS simulators and not watchOS, tvOS etc.
  # The jq query below attempts to filter for devices that are part of an iOS runtime.
  # This might need adjustment based on the exact output structure of `xcrun simctl list devices --json`
  # For simplicity, we'll list all available and let user pick. A more robust filter might be needed.
  
  local simulators
  simulators=$(xcrun simctl list devices available --json | jq -r '
    .devices | to_entries[] | .key as $runtime | .value[] |
    select(.isAvailable == true and ($runtime | contains("iOS"))) |
    "\(.name) (\(.state)) - UDID: \(.udid)"
  ')

  if [ -z "$simulators" ]; then
    error "No available iOS simulators found. Please create one in Xcode."
    return 1
  fi

  echo "$simulators" | nl # Numbered list
  local count
  count=$(echo "$simulators" | wc -l)

  local choice
  while true; do
    read -p "Select a simulator by number (or 0 to skip): " choice
    if [[ "$choice" -ge 0 && "$choice" -le "$count" ]]; then
      break
    else
      warn "Invalid selection. Please try again."
    fi
  done

  if [ "$choice" -eq 0 ]; then
    SELECTED_SIM_UDID=""
    SELECTED_SIM_NAME=""
    info "Simulator selection skipped."
    return 0
  fi

  SELECTED_SIM_UDID=$(echo "$simulators" | sed -n "${choice}p" | awk -F'UDID: ' '{print $2}')
  SELECTED_SIM_NAME=$(echo "$simulators" | sed -n "${choice}p" | awk -F' \\(' '{print $1}')
  info "Selected simulator: $SELECTED_SIM_NAME (UDID: $SELECTED_SIM_UDID)"
}

boot_simulator() {
  local sim_udid="$1"
  if [ -z "$sim_udid" ]; then
    error "No simulator UDID provided to boot."
    return 1
  fi

  info "Checking status of simulator $SELECTED_SIM_NAME..."
  local sim_state
  sim_state=$(xcrun simctl list devices --json | jq -r --arg UDID "$sim_udid" '(.devices[][] | select(.udid == $UDID) | .state)')

  if [ "$sim_state" == "Booted" ]; then
    info "Simulator $SELECTED_SIM_NAME is already booted."
    # Bring to front
    open -a Simulator --args -CurrentDeviceUDID "$sim_udid"
    return 0
  fi

  info "Booting simulator $SELECTED_SIM_NAME (UDID: $sim_udid)..."
  if xcrun simctl boot "$sim_udid"; then
    info "Simulator booted successfully. Opening..."
    # The `open` command launches the Simulator app and focuses on the booted device.
    open -a Simulator --args -CurrentDeviceUDID "$sim_udid"
    info "Waiting a few seconds for the simulator to be fully ready..."
    sleep 10 # Give some time for the simulator to be fully operational
  else
    error "Failed to boot simulator $SELECTED_SIM_NAME."
    return 1
  fi
}

ensure_simulator_booted() {
  if [ -z "$SELECTED_SIM_UDID" ]; then
    info "No simulator selected. Please select or start a simulator first."
    select_simulator || return 1
    if [ -z "$SELECTED_SIM_UDID" ]; then # User skipped selection
        return 1
    fi
  fi
  boot_simulator "$SELECTED_SIM_UDID"
}

shutdown_simulator() {
  local sim_udid="$1"
  if [ -z "$sim_udid" ]; then
    warn "No simulator UDID provided to shut down."
    return 1
  fi
  info "Shutting down simulator $SELECTED_SIM_NAME (UDID: $sim_udid)..."
  xcrun simctl shutdown "$sim_udid" || warn "Failed to shut down simulator, it might have been already off."
  info "Simulator $SELECTED_SIM_NAME shut down."
}

# --- Build and Run Functions ---

update_database_schema() {
  info "Updating database schema (common package)..."
  navigate_to_common_pkg_dir
  flutter pub get
  dart run build_runner build --delete-conflicting-outputs
  if [ $? -ne 0 ]; then error "Failed to update database schema for common package."; cd "$ORIGINAL_DIR"; return 1; fi
  info "Common package schema updated."

  info "Updating database schema (client)..."
  navigate_to_client_dir
  flutter pub get
  dart run build_runner build --delete-conflicting-outputs
  if [ $? -ne 0 ]; then error "Failed to update database schema for client."; cd "$ORIGINAL_DIR"; return 1; fi
  info "Client schema updated."
  cd "$ORIGINAL_DIR"
}

run_flutter_clean() {
  navigate_to_client_dir
  info "Running 'flutter clean'..."
  flutter clean
  if [ $? -ne 0 ]; then error "flutter clean failed."; cd "$ORIGINAL_DIR"; return 1; fi
  info "Flutter clean successful."
  cd "$ORIGINAL_DIR"
}

run_client_tests() {
  navigate_to_client_dir
  info "Running client tests..."
  flutter test
  if [ $? -ne 0 ]; then error "Client tests failed."; cd "$ORIGINAL_DIR"; return 1; fi
  info "Client tests passed."
  cd "$ORIGINAL_DIR"
  return 0
}

run_common_tests() {
  navigate_to_common_pkg_dir
  info "Running promz_common tests..."
  flutter test
  if [ $? -ne 0 ]; then error "promz_common tests failed."; cd "$ORIGINAL_DIR"; return 1; fi
  info "promz_common tests passed."
  cd "$ORIGINAL_DIR"
  return 0
}

run_all_tests() {
  run_client_tests || return 1
  run_common_tests || return 1
  info "All tests passed successfully!"
}

run_benchmarks() {
  navigate_to_client_dir
  info "Running benchmarks..."
  # Assuming benchmark tag and path are similar to Android version
  flutter test --tags benchmark test/benchmark/keyword_extraction_benchmark.dart
  if [ $? -ne 0 ]; then error "Benchmarks failed."; cd "$ORIGINAL_DIR"; return 1; fi
  info "Benchmarks completed successfully."
  cd "$ORIGINAL_DIR"
}

build_app_simulator_debug() {
  ensure_simulator_booted || return 1
  
  info "--- Starting iOS build preparation ---"
  
  navigate_to_client_dir
  
  info "[1/5] Running 'flutter clean' in client directory..."
  flutter clean
  if [ $? -ne 0 ]; then error "'flutter clean' failed."; cd "$ORIGINAL_DIR"; return 1; fi

  info "[2/5] Running 'flutter pub get' in client directory..."
  flutter pub get
  if [ $? -ne 0 ]; then error "'flutter pub get' in client failed."; cd "$ORIGINAL_DIR"; return 1; fi

  info "[3/5] Preparing iOS project in: $CLIENT_DIR/ios"
  cd "$CLIENT_DIR/ios" || { error "Failed to navigate to $CLIENT_DIR/ios"; cd "$ORIGINAL_DIR"; return 1; }

  info "Removing old CocoaPods artifacts (Podfile.lock, Pods/, Runner.xcworkspace)..."
  rm -rf "Podfile.lock"
  rm -rf "Pods/"
  rm -rf "Runner.xcworkspace"

  info "Installing CocoaPods dependencies..."
  local pod_install_cmd="pod install"
  if [[ "$(uname -m)" == "arm64" ]]; then
    info "Apple Silicon detected, using 'arch -x86_64 pod install'."
    pod_install_cmd="arch -x86_64 pod install"
  fi

  # Try with repo-update first for freshness, then fallback.
  if ! $pod_install_cmd --repo-update; then
    warn "Initial '$pod_install_cmd --repo-update' failed. Trying without --repo-update..."
    if ! $pod_install_cmd; then
        error "Pod installation failed. Please check CocoaPods setup and error messages."
        cd "$CLIENT_DIR" 
        flutter doctor
        cd "$ORIGINAL_DIR"
        return 1
    fi
  fi
  info "CocoaPods installation successful."

  info "[4/5] Navigating back to client directory for build command."
  cd "$CLIENT_DIR" 

  info "[5/5] --- iOS build preparation complete. Attempting build... ---"
  info "Building debug app for simulator $SELECTED_SIM_NAME..."
  flutter build ios --debug -d "$SELECTED_SIM_UDID"
  if [ $? -ne 0 ]; then 
    error "Debug build for simulator failed. Check Xcode and Flutter logs."
    warn "If issues persist, you might need to open $CLIENT_DIR/ios/Runner.xcworkspace in Xcode to resolve signing or other project settings."
    cd "$ORIGINAL_DIR"
    return 1
  fi
  
  info "Debug build for simulator successful. App path: build/ios/iphonesimulator/Runner.app"
  cd "$ORIGINAL_DIR"
}

install_app_simulator() {
  ensure_simulator_booted || return 1
  local app_path="$CLIENT_DIR/build/ios/iphonesimulator/Runner.app"
  if [ ! -d "$app_path" ]; then
    error "App not found at $app_path. Please build the app for simulator first."
    return 1
  fi
  info "Installing app to simulator $SELECTED_SIM_NAME..."
  xcrun simctl install "$SELECTED_SIM_UDID" "$app_path"
  if [ $? -ne 0 ]; then error "Failed to install app on simulator."; return 1; fi
  info "App installed successfully on $SELECTED_SIM_NAME."
  info "Attempting to launch app..."
  local bundle_id
  # Attempt to get bundle ID from Info.plist - this is a common way
  # Might need to adjust if your Info.plist path or bundle ID key is different
  bundle_id=$(/usr/libexec/PlistBuddy -c "Print CFBundleIdentifier" "$app_path/Info.plist")
  if [ -n "$bundle_id" ]; then
    xcrun simctl launch "$SELECTED_SIM_UDID" "$bundle_id"
    info "App launched (or attempted to launch)."
  else
    warn "Could not determine bundle ID to launch the app. Please launch manually from the simulator."
  fi
}

build_app_device_debug() {
  navigate_to_client_dir
  info "Building debug app for physical iOS device..."
  warn "Ensure a physical iOS device is connected and properly configured for development in Xcode."
  flutter build ios --debug
  if [ $? -ne 0 ]; then error "Debug build for device failed. Check Xcode for signing issues."; cd "$ORIGINAL_DIR"; return 1; fi
  info "Debug build for device successful. Connect device and run from Xcode or 'flutter run'."
  cd "$ORIGINAL_DIR"
}

build_app_device_release() {
  navigate_to_client_dir
  info "Building release app for physical iOS device..."
  warn "Ensure a physical iOS device is connected and properly configured for development in Xcode with release signing."
  flutter build ios --release
  if [ $? -ne 0 ]; then error "Release build for device failed. Check Xcode for signing issues."; cd "$ORIGINAL_DIR"; return 1; fi
  info "Release build for device successful. Output .app can be found in build/ios/iphoneos/Runner.app."
  info "This .app is typically used for ad-hoc distribution or further processing into an .ipa."
  cd "$ORIGINAL_DIR"
}

build_ipa_release() {
  navigate_to_client_dir
  info "Building release IPA for distribution..."
  warn "This command requires an 'ExportOptions.plist' file for specifying export options (e.g., method: app-store, ad-hoc, development, enterprise)."
  warn "Place your 'ExportOptions.plist' in the '$CLIENT_DIR/ios/' directory."
  local export_options_path="$CLIENT_DIR/ios/ExportOptions.plist"

  if [ ! -f "$export_options_path" ]; then
    error "ExportOptions.plist not found at $export_options_path."
    info "You can create one via Xcode archive & export process and then reuse it, or create manually."
    info "Example for Ad Hoc:"
    echo """
    <?xml version=\"1.0\" encoding=\"UTF-8\"?>
    <!DOCTYPE plist PUBLIC \"-//Apple//DTD PLIST 1.0//EN\" \"http://www.apple.com/DTDs/PropertyList-1.0.dtd\">
    <plist version=\"1.0\">
    <dict>
        <key>method</key>
        <string>ad-hoc</string>
        <key>teamID</key>
        <string>ai.promz.app</string>
        <key>signingStyle</key>
        <string>manual</string>
        <key>signingCertificate</key>
        <string>Apple Distribution: Your Name (TEAMID)</string>
        <key>provisioningProfiles</key>
        <dict>
                <key>YOUR_BUNDLE_ID</key>
                <string>Your Ad Hoc Provisioning Profile Name</string>
        </dict>
    </dict>
    </plist>
    """
    return 1
  fi

  flutter build ipa --release --export-options-plist="$export_options_path"
  if [ $? -ne 0 ]; then error "IPA build failed. Check Xcode for signing/archiving issues."; cd "$ORIGINAL_DIR"; return 1; fi
  info "IPA built successfully. Output .ipa can be found in build/ios/ipa/."
  cd "$ORIGINAL_DIR"
}


# --- Main Menu ---

show_menu() {
  while true; do
    echo -e "\n\033[1;32mFlutter iOS Build and Run Script (Promz)\033[0m"
    echo "Project Root: $PROJECT_ROOT_DIR"
    echo "Client Dir:   $CLIENT_DIR"
    if [ -n "$SELECTED_SIM_NAME" ]; then
      echo -e "Current Simulator: \033[1;33m$SELECTED_SIM_NAME (UDID: $SELECTED_SIM_UDID)\033[0m"
    else
      echo "Current Simulator: None selected"
    fi
    echo "-------------------------------------------"
    echo "iOS Simulator Management:"
    echo "  1. List Available Simulators"
    echo "  2. Select Simulator"
    echo "  3. Boot Selected Simulator"
    echo "  4. Shutdown Selected Simulator"
    echo "Build & Test Operations (Client: $CLIENT_DIR):"
    echo "  5. Update Database Schema (Common & Client)"
    echo "  6. Run All Tests (Client & Common)"
    echo "  7. Run Client Tests Only"
    echo "  8. Run Common Package Tests Only"
    echo "  9. Run Benchmarks"
    echo " 10. Flutter Clean (Client)"
    echo "Build for Simulator:"
    echo " 11. [+Tests] Build Debug .app for Simulator & Install"
    echo " 12. [-Tests] Build Debug .app for Simulator & Install"
    echo " 13. Install existing Debug .app to Simulator"
    echo "Build for Physical Device / Distribution:"
    echo " 14. Build Debug .app for Device (requires Xcode setup)"
    echo " 15. Build Release .app for Device (requires Xcode setup)"
    echo " 16. Build Release .ipa for Distribution (requires ExportOptions.plist)"
    echo "-------------------------------------------"
    echo "  0. Exit"
    echo "-------------------------------------------"

    local choice
    read -p "Enter your choice (0-16): " choice

    case $choice in
      1) list_simulators ;;
      2) select_simulator ;;
      3)
        if [ -z "$SELECTED_SIM_UDID" ]; then warn "No simulator selected. Use option 2 first."; else boot_simulator "$SELECTED_SIM_UDID"; fi
        ;;
      4)
        if [ -z "$SELECTED_SIM_UDID" ]; then warn "No simulator selected."; else shutdown_simulator "$SELECTED_SIM_UDID"; fi
        ;;
      5) update_database_schema ;;
      6) run_all_tests ;;
      7) run_client_tests ;;
      8) run_common_tests ;;
      9) run_benchmarks ;;
     10) run_flutter_clean ;;
     11)
        run_all_tests && build_app_simulator_debug && install_app_simulator
        ;;
     12)
        build_app_simulator_debug && install_app_simulator
        ;;
     13)
        install_app_simulator
        ;;
     14) build_app_device_debug ;;
     15) build_app_device_release ;;
     16) build_ipa_release ;;
      0) cleanup_and_exit ;;
      *) warn "Invalid choice. Please try again." ;;
    esac
    if [ "$choice" != "0" ]; then
        read -n 1 -s -r -p "Press any key to continue..."
        echo
    fi
  done
}

# --- Script Entry Point ---
check_dependencies
info "Script initialized. Original directory: $ORIGINAL_DIR"
show_menu