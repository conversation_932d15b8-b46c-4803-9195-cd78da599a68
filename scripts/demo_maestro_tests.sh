#!/bin/bash
# Demo Script for Maestro UI Testing
# Demonstrates the complete testing workflow for new users

# --- Global Variables ---
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT_DIR="$(cd "$SCRIPT_DIR/../.." && pwd)"

# --- Color Functions ---
info() { echo -e "\033[1;32m[INFO]\033[0m $1"; }
warn() { echo -e "\033[1;33m[WARN]\033[0m $1"; }
error() { echo -e "\033[1;31m[ERROR]\033[0m $1"; }
success() { echo -e "\033[1;32m[SUCCESS]\033[0m $1"; }
demo() { echo -e "\033[1;35m[DEMO]\033[0m $1"; }

# --- Demo Functions ---
show_intro() {
  clear
  echo -e "\033[1;32m"
  echo "╔══════════════════════════════════════════════════════════════╗"
  echo "║                 Maestro UI Testing Demo                     ║"
  echo "║                  Promz Android App                          ║"
  echo "╚══════════════════════════════════════════════════════════════╝"
  echo -e "\033[0m"
  echo ""
  echo "This demo will guide you through setting up and running"
  echo "comprehensive UI tests for the Promz Android app using Maestro."
  echo ""
  echo "What you'll learn:"
  echo "  • How to set up the testing environment"
  echo "  • How to run individual tests and test suites"
  echo "  • How to debug test failures"
  echo "  • How to interpret test results"
  echo ""
  echo -n "Press Enter to continue..."
  read
}

demo_prerequisites() {
  demo "Step 1: Checking Prerequisites"
  echo ""
  echo "First, let's verify that your system is ready for testing:"
  echo ""
  
  echo "Running prerequisite check..."
  if "$SCRIPT_DIR/setup_test_environment.sh" --verify; then
    success "Prerequisites check completed!"
  else
    warn "Some prerequisites are missing. Let's fix them..."
    echo ""
    echo "Would you like to run the automated setup? (y/N)"
    read setup_confirm
    if [[ "$setup_confirm" =~ ^[Yy]$ ]]; then
      "$SCRIPT_DIR/setup_test_environment.sh" --all
    else
      info "You can run setup later with: $SCRIPT_DIR/setup_test_environment.sh"
    fi
  fi
  
  echo ""
  echo -n "Press Enter to continue..."
  read
}

demo_test_structure() {
  demo "Step 2: Understanding Test Structure"
  echo ""
  echo "The Maestro tests are organized as follows:"
  echo ""
  echo "📁 testing/maestro/"
  echo "├── 📁 android/authentication/     # Authentication test flows"
  echo "│   ├── 01_pre_login_state.yaml   # Verify unauthenticated UI"
  echo "│   ├── 02_login_flow.yaml        # Test Google OAuth login"
  echo "│   ├── 03_post_login_state.yaml  # Verify authenticated UI"
  echo "│   ├── 04_navigation_access.yaml # Test authenticated navigation"
  echo "│   └── 05_logout_flow.yaml       # Test logout process"
  echo "├── 📁 common/                     # Reusable test components"
  echo "└── 📁 test_suites/               # Test suite configurations"
  echo ""
  echo "Each test file contains:"
  echo "  • Clear objectives and descriptions"
  echo "  • Step-by-step test actions"
  echo "  • Assertions to verify expected behavior"
  echo "  • Screenshots for debugging"
  echo ""
  echo -n "Press Enter to continue..."
  read
}

demo_individual_test() {
  demo "Step 3: Running an Individual Test"
  echo ""
  echo "Let's run the first test: Pre-Login State Verification"
  echo ""
  echo "This test will:"
  echo "  • Launch the Promz app"
  echo "  • Verify the unauthenticated UI state"
  echo "  • Test navigation between tabs"
  echo "  • Take screenshots for verification"
  echo ""
  echo "Expected duration: ~2 minutes"
  echo ""
  echo -n "Run the test now? (y/N): "
  read run_test
  
  if [[ "$run_test" =~ ^[Yy]$ ]]; then
    echo ""
    info "Starting test execution..."
    cd "$PROJECT_ROOT_DIR/testing" || exit 1
    
    if maestro test maestro/android/authentication/01_pre_login_state.yaml; then
      success "Test completed successfully!"
      echo ""
      echo "Screenshots saved to: testing/screenshots/"
      echo "You can view them with the test runner utilities menu."
    else
      error "Test failed. Check the output above for details."
      echo ""
      echo "Common issues:"
      echo "  • App not installed: Run setup script"
      echo "  • Device not connected: Check 'adb devices'"
      echo "  • UI changes: Update test selectors"
    fi
    
    cd - > /dev/null
  else
    info "Skipping test execution. You can run it later with:"
    echo "  ./testing/scripts/run_maestro_tests.sh"
  fi
  
  echo ""
  echo -n "Press Enter to continue..."
  read
}

demo_test_suite() {
  demo "Step 4: Understanding Test Suites"
  echo ""
  echo "Test suites run multiple tests in sequence:"
  echo ""
  echo "🔄 Authentication Suite Flow:"
  echo "  1. Pre-Login State    (2 min)  ✓ Automated"
  echo "  2. Login Flow         (5 min)  ⚠ Manual OAuth required"
  echo "  3. Post-Login State   (2 min)  ✓ Automated"
  echo "  4. Navigation Access  (3 min)  ✓ Automated"
  echo "  5. Logout Flow        (2 min)  ✓ Automated"
  echo ""
  echo "Total time: ~15 minutes (including manual OAuth)"
  echo ""
  echo "The login test requires manual interaction:"
  echo "  • Google OAuth flow will open"
  echo "  • You'll need to complete authentication"
  echo "  • Test will continue automatically after login"
  echo ""
  echo "Would you like to see the test suite configuration? (y/N)"
  read show_config
  
  if [[ "$show_config" =~ ^[Yy]$ ]]; then
    echo ""
    info "Test suite configuration:"
    cat "$PROJECT_ROOT_DIR/testing/maestro/test_suites/authentication_suite.yaml"
  fi
  
  echo ""
  echo -n "Press Enter to continue..."
  read
}

demo_interactive_runner() {
  demo "Step 5: Interactive Test Runner"
  echo ""
  echo "The interactive test runner provides a menu-driven interface:"
  echo ""
  echo "🎯 Main Features:"
  echo "  • Run individual tests or complete suites"
  echo "  • Debug mode with verbose output"
  echo "  • Prerequisites checking"
  echo "  • Screenshot management"
  echo "  • Device information and utilities"
  echo ""
  echo "🛠️ Utilities:"
  echo "  • Check prerequisites"
  echo "  • View device information"
  echo "  • Launch Maestro Studio (interactive testing)"
  echo "  • View and clean screenshots"
  echo "  • Install/update Maestro"
  echo ""
  echo "Would you like to launch the interactive runner now? (y/N)"
  read launch_runner
  
  if [[ "$launch_runner" =~ ^[Yy]$ ]]; then
    echo ""
    info "Launching interactive test runner..."
    exec "$SCRIPT_DIR/run_maestro_tests.sh"
  else
    info "You can launch it later with: $SCRIPT_DIR/run_maestro_tests.sh"
  fi
  
  echo ""
  echo -n "Press Enter to continue..."
  read
}

demo_debugging() {
  demo "Step 6: Debugging and Troubleshooting"
  echo ""
  echo "When tests fail, here's how to debug:"
  echo ""
  echo "🔍 Debug Tools:"
  echo "  • Debug mode: maestro test --debug <test_file>"
  echo "  • Screenshots: Automatically captured at key points"
  echo "  • Maestro Studio: Interactive testing environment"
  echo "  • Device logs: adb logcat | grep -i promz"
  echo ""
  echo "📋 Common Issues:"
  echo "  • Element not found → UI changed, update selectors"
  echo "  • Timeout errors → Increase wait times"
  echo "  • OAuth failures → Use real device, check connectivity"
  echo "  • App crashes → Check device logs and app state"
  echo ""
  echo "📚 Resources:"
  echo "  • Troubleshooting guide: testing/maestro/TROUBLESHOOTING.md"
  echo "  • Quick start guide: testing/maestro/QUICKSTART.md"
  echo "  • Maestro documentation: https://maestro.mobile.dev/"
  echo ""
  echo -n "Press Enter to continue..."
  read
}

demo_conclusion() {
  demo "Demo Complete!"
  echo ""
  success "You're now ready to run comprehensive UI tests for Promz!"
  echo ""
  echo "🚀 Next Steps:"
  echo "  1. Run the setup script if you haven't already"
  echo "  2. Start with individual tests to get familiar"
  echo "  3. Run the complete authentication suite"
  echo "  4. Explore debug mode and Maestro Studio"
  echo "  5. Create custom tests for new features"
  echo ""
  echo "📖 Quick Commands:"
  echo "  • Setup: ./testing/scripts/setup_test_environment.sh --all"
  echo "  • Run tests: ./testing/scripts/run_maestro_tests.sh"
  echo "  • Debug: maestro test --debug <test_file>"
  echo "  • Interactive: maestro studio"
  echo ""
  echo "🎯 Remember:"
  echo "  • Authentication tests require manual OAuth interaction"
  echo "  • Screenshots are saved for debugging"
  echo "  • Use real devices for OAuth testing when possible"
  echo "  • Check troubleshooting guide for common issues"
  echo ""
  echo "Happy testing! 🧪✨"
}

# --- Main Demo Flow ---
main() {
  show_intro
  demo_prerequisites
  demo_test_structure
  demo_individual_test
  demo_test_suite
  demo_interactive_runner
  demo_debugging
  demo_conclusion
}

# --- Script Entry Point ---
case "${1:-}" in
  --quick)
    demo_individual_test
    ;;
  --suite)
    demo_test_suite
    ;;
  --debug)
    demo_debugging
    ;;
  "")
    main
    ;;
  *)
    echo "Usage: $0 [--quick|--suite|--debug]"
    echo "  --quick  Demo individual test"
    echo "  --suite  Demo test suite"
    echo "  --debug  Demo debugging"
    echo "  (no args) Full demo"
    exit 1
    ;;
esac
