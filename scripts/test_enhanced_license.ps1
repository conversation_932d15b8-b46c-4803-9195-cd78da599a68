# Test script for enhanced license functions
# This script tests the new license management functions on both the database and API server

# Source the environment variables
. "$PSScriptRoot\..\..\scripts\script_utils.ps1"

# Supabase credentials
$SupabaseUrl = $env:PROMZ_SUPABASE_URL
$SupabaseKey = $env:PROMZ_SUPABASE_KEY
$ApiServerUrl = "http://localhost:8080"

# Check if Supabase credentials are set
if (-not $SupabaseUrl -or -not $SupabaseKey) {
    Write-Host "❌ Supabase credentials not found in environment variables." -ForegroundColor Red
    Write-Host "Please set PROMZ_SUPABASE_URL and PROMZ_SUPABASE_KEY environment variables." -ForegroundColor Red
    exit 1
}

# Function to test the enhanced verify_api_key function directly in Supabase
function Test-EnhancedVerifyApiKey {
    param (
        [string]$apiKey
    )

    Write-Host "`n🔍 Testing enhanced API key verification via Supabase RPC..." -ForegroundColor Cyan
    
    # Construct the request URL
    $url = "$SupabaseUrl/rest/v1/rpc/verify_api_key_enhanced"
    
    # Prepare the request body
    $body = @{
        p_api_key = $apiKey
    } | ConvertTo-Json
    
    # Prepare headers
    $headers = @{
        "Content-Type" = "application/json"
        "apikey" = $SupabaseKey
        "Authorization" = "Bearer $SupabaseKey"
    }
    
    Write-Host "Request URL: $url" -ForegroundColor DarkGray
    Write-Host "Headers:" -ForegroundColor DarkGray
    $headers.Keys | ForEach-Object {
        Write-Host "  $_ = $($_ -eq 'Authorization' -or $_ -eq 'apikey' ? '****' : $headers[$_])" -ForegroundColor DarkGray
    }
    
    try {
        $response = Invoke-RestMethod -Uri $url -Method Post -Headers $headers -Body $body -ContentType "application/json"
        Write-Host "✅ Request successful!" -ForegroundColor Green
        Write-Host "Response:" -ForegroundColor DarkGray
        $response | ConvertTo-Json -Depth 3 | Write-Host -ForegroundColor DarkGray
        
        if ($response.valid -eq $true) {
            Write-Host "✅ API key is valid!" -ForegroundColor Green
            Write-Host "User ID: $($response.user_id)" -ForegroundColor Green
            Write-Host "License Type: $($response.license_type)" -ForegroundColor Green
            Write-Host "Expiry Date: $($response.expiry_date)" -ForegroundColor Green
            Write-Host "Is Free: $($response.is_free)" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ API key is invalid!" -ForegroundColor Red
            Write-Host "Reason: $($response.reason)" -ForegroundColor Red
            Write-Host "Error Code: $($response.error_code)" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ Request failed!" -ForegroundColor Red
        Write-Host "Error Details:" -ForegroundColor Red
        Write-Host "  Message:`n$($_.Exception.Message)" -ForegroundColor Red
        
        # Check if the function doesn't exist
        if ($_.Exception.Message -match "Could not find the function") {
            Write-Host "❌ The 'verify_api_key_enhanced' function does not exist or you don't have permission to access it." -ForegroundColor Red
            Write-Host "Please ensure the function is created in the Supabase database." -ForegroundColor Red
        }
        
        return $false
    }
}

# Function to test the enhanced API server endpoint for API key verification
function Test-EnhancedApiServerVerification {
    param (
        [string]$apiKey,
        [string]$apiServerUrl = "http://localhost:8080"
    )

    Write-Host "`n🔍 Testing enhanced API key verification via API server..." -ForegroundColor Cyan
    
    # Construct the request URL
    $url = "$apiServerUrl/license/verify"
    
    # Prepare the request body
    $body = @{
        api_key = $apiKey
    } | ConvertTo-Json
    
    # Prepare headers - no auth required for this test
    $headers = @{
        "Content-Type" = "application/json"
    }
    
    Write-Host "Request URL: $url" -ForegroundColor DarkGray
    
    try {
        $response = Invoke-RestMethod -Uri $url -Method Post -Headers $headers -Body $body
        Write-Host "✅ Request successful!" -ForegroundColor Green
        Write-Host "Response:" -ForegroundColor DarkGray
        $response | ConvertTo-Json -Depth 3 | Write-Host -ForegroundColor DarkGray
        
        if ($response.valid -eq $true) {
            Write-Host "✅ API key is valid!" -ForegroundColor Green
            Write-Host "User ID: $($response.user_id)" -ForegroundColor Green
            Write-Host "License Type: $($response.license_type)" -ForegroundColor Green
            Write-Host "Expiry Date: $($response.expiry_date)" -ForegroundColor Green
            Write-Host "Is Free: $($response.is_free)" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ API key is invalid!" -ForegroundColor Red
            Write-Host "Reason: $($response.reason)" -ForegroundColor Red
            Write-Host "Error Code: $($response.error_code)" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ Request failed!" -ForegroundColor Red
        Write-Host "Error Details:" -ForegroundColor Red
        Write-Host "  Message:`n$($_.Exception.Message)" -ForegroundColor Red
        
        Write-Host "❌ The API server might not be running or the endpoint is not available." -ForegroundColor Red
        Write-Host "Please ensure the API server is running at $apiServerUrl" -ForegroundColor Red
        
        return $false
    }
}

# Function to test the check_license_status function
function Test-CheckLicenseStatus {
    param (
        [string]$userId = "006f85f7-ce10-4f1a-8f84-4546a890cfd1" # Valid UUID format
    )

    Write-Host "`n🔍 Testing license status check via Supabase RPC..." -ForegroundColor Cyan
    
    # Construct the request URL
    $url = "$SupabaseUrl/rest/v1/rpc/check_license_status"
    
    # Prepare the request body
    $body = @{
        p_user_id = $userId
    } | ConvertTo-Json
    
    # Prepare headers
    $headers = @{
        "Content-Type" = "application/json"
        "apikey" = $SupabaseKey
        "Authorization" = "Bearer $SupabaseKey"
    }
    
    Write-Host "Request URL: $url" -ForegroundColor DarkGray
    
    try {
        $response = Invoke-RestMethod -Uri $url -Method Post -Headers $headers -Body $body -ContentType "application/json"
        Write-Host "✅ Request successful!" -ForegroundColor Green
        Write-Host "Response:" -ForegroundColor DarkGray
        $response | ConvertTo-Json -Depth 3 | Write-Host -ForegroundColor DarkGray
        
        if ($response.has_license -eq $true) {
            Write-Host "✅ User has a valid license!" -ForegroundColor Green
            Write-Host "License Type: $($response.license_type)" -ForegroundColor Green
            Write-Host "Expiry Date: $($response.expiry_date)" -ForegroundColor Green
            Write-Host "Days Remaining: $($response.days_remaining)" -ForegroundColor Green
            Write-Host "Is Free: $($response.is_free)" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ User does not have a valid license!" -ForegroundColor Red
            Write-Host "Reason: $($response.reason)" -ForegroundColor Red
            Write-Host "Error Code: $($response.error_code)" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ Request failed!" -ForegroundColor Red
        Write-Host "Error Details:" -ForegroundColor Red
        Write-Host "  Message:`n$($_.Exception.Message)" -ForegroundColor Red
        
        return $false
    }
}

# Function to test the API server endpoint for license status check
function Test-ApiServerLicenseStatus {
    param (
        [string]$userId = "006f85f7-ce10-4f1a-8f84-4546a890cfd1", # Valid UUID format
        [string]$apiServerUrl = "http://localhost:8080"
    )

    Write-Host "`n🔍 Testing license status check via API server..." -ForegroundColor Cyan
    
    # Construct the request URL
    $url = "$apiServerUrl/license/status"
    
    # Prepare the request body
    $body = @{
        user_id = $userId
    } | ConvertTo-Json
    
    # Prepare headers - no auth required for this test
    $headers = @{
        "Content-Type" = "application/json"
    }
    
    Write-Host "Request URL: $url" -ForegroundColor DarkGray
    
    try {
        $response = Invoke-RestMethod -Uri $url -Method Post -Headers $headers -Body $body
        Write-Host "✅ Request successful!" -ForegroundColor Green
        Write-Host "Response:" -ForegroundColor DarkGray
        $response | ConvertTo-Json -Depth 3 | Write-Host -ForegroundColor DarkGray
        
        if ($response.has_license -eq $true) {
            Write-Host "✅ User has a valid license!" -ForegroundColor Green
            Write-Host "License Type: $($response.license_type)" -ForegroundColor Green
            Write-Host "Expiry Date: $($response.expiry_date)" -ForegroundColor Green
            Write-Host "Days Remaining: $($response.days_remaining)" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ User does not have a valid license!" -ForegroundColor Red
            Write-Host "Reason: $($response.reason)" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ Request failed!" -ForegroundColor Red
        Write-Host "Error Details:" -ForegroundColor Red
        Write-Host "  Message:`n$($_.Exception.Message)" -ForegroundColor Red
        
        Write-Host "❌ The API server might not be running or the endpoint is not available." -ForegroundColor Red
        Write-Host "Please ensure the API server is running at $apiServerUrl" -ForegroundColor Red
        
        return $false
    }
}

# Function to test the get_or_create_license_enhanced function
function Test-GetOrCreateLicenseEnhanced {
    param (
        [string]$userId = "006f85f7-ce10-4f1a-8f84-4546a890cfd1", # Valid UUID format
        [string]$licenseType = "free",
        [int]$daysValid = 36500
    )

    Write-Host "`n🔍 Testing get_or_create_license_enhanced RPC function..." -ForegroundColor Cyan
    
    # Construct the request URL
    $url = "$SupabaseUrl/rest/v1/rpc/get_or_create_license_enhanced"
    
    # Prepare the request body
    $body = @{
        p_user_id = $userId
        p_license_type = $licenseType
        p_days_valid = $daysValid
    } | ConvertTo-Json
    
    # Prepare headers
    $headers = @{
        "Content-Type" = "application/json"
        "apikey" = $SupabaseKey
        "Authorization" = "Bearer $SupabaseKey"
    }
    
    Write-Host "Request URL: $url" -ForegroundColor DarkGray
    
    try {
        $response = Invoke-RestMethod -Uri $url -Method Post -Headers $headers -Body $body -ContentType "application/json"
        Write-Host "✅ Request successful!" -ForegroundColor Green
        Write-Host "Response:" -ForegroundColor DarkGray
        $response | ConvertTo-Json -Depth 3 | Write-Host -ForegroundColor DarkGray
        
        if ($response.api_key) {
            Write-Host "✅ License created or retrieved successfully!" -ForegroundColor Green
            Write-Host "Is New: $($response.is_new)" -ForegroundColor Green
            Write-Host "License Type: $($response.license_type)" -ForegroundColor Green
            Write-Host "Expiry Date: $($response.expiry_date)" -ForegroundColor Green
            Write-Host "API Key: $('*' * [Math]::Min(5, $response.api_key.Length))..." -ForegroundColor Green
            return $response.api_key
        } else {
            Write-Host "❌ Failed to create or retrieve license!" -ForegroundColor Red
            return $null
        }
    } catch {
        Write-Host "❌ Request failed!" -ForegroundColor Red
        Write-Host "Error Details:" -ForegroundColor Red
        Write-Host "  Message:`n$($_.Exception.Message)" -ForegroundColor Red
        
        # Check if the function doesn't exist
        if ($_.Exception.Message -match "Could not find the function") {
            Write-Host "❌ The 'get_or_create_license_enhanced' function does not exist or you don't have permission to access it." -ForegroundColor Red
            Write-Host "Please ensure the function is created in the Supabase database." -ForegroundColor Red
        }
        
        return $null
    }
}

# Function to test the API server endpoint for get or create license
function Test-ApiServerGetOrCreateLicense {
    param (
        [string]$userId = "006f85f7-ce10-4f1a-8f84-4546a890cfd1", # Valid UUID format
        [string]$licenseType = "free",
        [int]$daysValid = 36500,
        [string]$apiServerUrl = "http://localhost:8080"
    )

    Write-Host "`n🔍 Testing get or create license via API server..." -ForegroundColor Cyan
    
    # Construct the request URL
    $url = "$apiServerUrl/license/get-or-create"
    
    # Prepare the request body
    $body = @{
        user_id = $userId
        license_type = $licenseType
        days_valid = $daysValid
    } | ConvertTo-Json
    
    # Prepare headers - no auth required for this test
    $headers = @{
        "Content-Type" = "application/json"
    }
    
    Write-Host "Request URL: $url" -ForegroundColor DarkGray
    
    try {
        $response = Invoke-RestMethod -Uri $url -Method Post -Headers $headers -Body $body
        Write-Host "✅ Request successful!" -ForegroundColor Green
        Write-Host "Response:" -ForegroundColor DarkGray
        $response | ConvertTo-Json -Depth 3 | Write-Host -ForegroundColor DarkGray
        
        if ($response.api_key) {
            Write-Host "✅ License created or retrieved successfully!" -ForegroundColor Green
            Write-Host "Is New: $($response.is_new)" -ForegroundColor Green
            Write-Host "License Type: $($response.license_type)" -ForegroundColor Green
            Write-Host "Expiry Date: $($response.expiry_date)" -ForegroundColor Green
            Write-Host "API Key: $('*' * [Math]::Min(5, $response.api_key.Length))..." -ForegroundColor Green
            return $response.api_key
        } else {
            Write-Host "❌ Failed to create or retrieve license!" -ForegroundColor Red
            return $null
        }
    } catch {
        Write-Host "❌ Request failed!" -ForegroundColor Red
        Write-Host "Error Details:" -ForegroundColor Red
        Write-Host "  Message:`n$($_.Exception.Message)" -ForegroundColor Red
        
        Write-Host "❌ The API server might not be running or the endpoint is not available." -ForegroundColor Red
        Write-Host "Please ensure the API server is running at $apiServerUrl" -ForegroundColor Red
        
        return $null
    }
}

# Function to test the complete enhanced license workflow
function Test-EnhancedLicenseWorkflow {
    param (
        [string]$userId = "006f85f7-ce10-4f1a-8f84-4546a890cfd1", # Valid UUID format
        [string]$licenseType = "free"
    )

    Write-Host "`n🔍 Testing complete enhanced license workflow..." -ForegroundColor Cyan
    
    Write-Host "`nStep 1: Creating a license for user..." -ForegroundColor Cyan
    
    # Create a license using get_or_create_license_enhanced
    $apiKey = Test-GetOrCreateLicenseEnhanced -userId $userId -licenseType $licenseType
    
    if ($apiKey) {
        Write-Host "`nStep 2: Verifying the API key..." -ForegroundColor Cyan
        
        # Verify the API key
        $isValid = Test-EnhancedVerifyApiKey -apiKey $apiKey
        
        if ($isValid) {
            Write-Host "`nStep 3: Checking license status..." -ForegroundColor Cyan
            
            # Check license status
            $hasLicense = Test-CheckLicenseStatus -userId $userId
            
            if ($hasLicense) {
                Write-Host "`n✅ Complete enhanced license workflow test passed!" -ForegroundColor Green
                return $true
            } else {
                Write-Host "`n❌ License status check failed!" -ForegroundColor Red
                return $false
            }
        } else {
            Write-Host "`n❌ API key verification failed!" -ForegroundColor Red
            return $false
        }
    } else {
        Write-Host "`n❌ Failed to create license!" -ForegroundColor Red
        return $false
    }
}

# Function to test the complete enhanced license workflow via API server
function Test-EnhancedLicenseWorkflowViaApiServer {
    param (
        [string]$userId = "006f85f7-ce10-4f1a-8f84-4546a890cfd1", # Valid UUID format
        [string]$licenseType = "free",
        [string]$apiServerUrl = "http://localhost:8080"
    )

    Write-Host "`n🔍 Testing complete enhanced license workflow via API server..." -ForegroundColor Cyan
    
    Write-Host "`nStep 1: Creating a license for user via API server..." -ForegroundColor Cyan
    
    # Create a license using API server
    $apiKey = Test-ApiServerGetOrCreateLicense -userId $userId -licenseType $licenseType -apiServerUrl $apiServerUrl
    
    if ($apiKey) {
        Write-Host "`nStep 2: Verifying the API key via API server..." -ForegroundColor Cyan
        
        # Verify the API key
        $isValid = Test-EnhancedApiServerVerification -apiKey $apiKey -apiServerUrl $apiServerUrl
        
        if ($isValid) {
            Write-Host "`nStep 3: Checking license status via API server..." -ForegroundColor Cyan
            
            # Check license status
            $hasLicense = Test-ApiServerLicenseStatus -userId $userId -apiServerUrl $apiServerUrl
            
            if ($hasLicense) {
                Write-Host "`n✅ Complete enhanced license workflow test via API server passed!" -ForegroundColor Green
                return $true
            } else {
                Write-Host "`n❌ License status check via API server failed!" -ForegroundColor Red
                return $false
            }
        } else {
            Write-Host "`n❌ API key verification via API server failed!" -ForegroundColor Red
            return $false
        }
    } else {
        Write-Host "`n❌ Failed to create license via API server!" -ForegroundColor Red
        return $false
    }
}

# Display menu
function Show-Menu {
    Clear-Host
    Write-Host "=== Enhanced License Functions Test Menu ===" -ForegroundColor Cyan
    Write-Host "1. Test verify_api_key_enhanced RPC function" -ForegroundColor Yellow
    Write-Host "2. Test check_license_status RPC function" -ForegroundColor Yellow
    Write-Host "3. Test get_or_create_license_enhanced RPC function" -ForegroundColor Yellow
    Write-Host "4. Test complete enhanced license workflow (Supabase)" -ForegroundColor Yellow
    Write-Host "5. Test enhanced API key verification via API server" -ForegroundColor Yellow
    Write-Host "6. Test license status check via API server" -ForegroundColor Yellow
    Write-Host "7. Test get or create license via API server" -ForegroundColor Yellow
    Write-Host "8. Test complete enhanced license workflow via API server" -ForegroundColor Yellow
    Write-Host "9. Run all tests" -ForegroundColor Yellow
    Write-Host "10. Exit" -ForegroundColor Yellow
    Write-Host "=============================================" -ForegroundColor Cyan
}

# Main script
$apiKey = ""
$userId = "006f85f7-ce10-4f1a-8f84-4546a890cfd1" # Default test user ID
$licenseType = "free"

do {
    Show-Menu
    $choice = Read-Host "Enter your choice (1-10)"
    
    switch ($choice) {
        "1" {
            if (-not $apiKey) {
                $apiKey = Read-Host "Enter API key to verify (or press Enter to create a new one)"
                if (-not $apiKey) {
                    $apiKey = Test-GetOrCreateLicenseEnhanced -userId $userId -licenseType $licenseType
                }
            }
            Test-EnhancedVerifyApiKey -apiKey $apiKey
            Read-Host "Press Enter to continue"
        }
        "2" {
            $inputUserId = Read-Host "Enter user ID (or press Enter for default)"
            if (-not $inputUserId) { $inputUserId = $userId }
            Test-CheckLicenseStatus -userId $inputUserId
            Read-Host "Press Enter to continue"
        }
        "3" {
            $inputUserId = Read-Host "Enter user ID (or press Enter for default)"
            if (-not $inputUserId) { $inputUserId = $userId }
            $inputLicenseType = Read-Host "Enter license type (or press Enter for 'free')"
            if (-not $inputLicenseType) { $inputLicenseType = "free" }
            $apiKey = Test-GetOrCreateLicenseEnhanced -userId $inputUserId -licenseType $inputLicenseType
            Read-Host "Press Enter to continue"
        }
        "4" {
            $inputUserId = Read-Host "Enter user ID (or press Enter for default)"
            if (-not $inputUserId) { $inputUserId = $userId }
            $inputLicenseType = Read-Host "Enter license type (or press Enter for 'free')"
            if (-not $inputLicenseType) { $inputLicenseType = "free" }
            Test-EnhancedLicenseWorkflow -userId $inputUserId -licenseType $inputLicenseType
            Read-Host "Press Enter to continue"
        }
        "5" {
            if (-not $apiKey) {
                $apiKey = Read-Host "Enter API key to verify (or press Enter to create a new one)"
                if (-not $apiKey) {
                    $apiKey = Test-GetOrCreateLicenseEnhanced -userId $userId -licenseType $licenseType
                }
            }
            $serverUrl = Read-Host "Enter API server URL (or press Enter for default)"
            if (-not $serverUrl) { $serverUrl = $ApiServerUrl }
            Test-EnhancedApiServerVerification -apiKey $apiKey -apiServerUrl $serverUrl
            Read-Host "Press Enter to continue"
        }
        "6" {
            $inputUserId = Read-Host "Enter user ID (or press Enter for default)"
            if (-not $inputUserId) { $inputUserId = $userId }
            $serverUrl = Read-Host "Enter API server URL (or press Enter for default)"
            if (-not $serverUrl) { $serverUrl = $ApiServerUrl }
            Test-ApiServerLicenseStatus -userId $inputUserId -apiServerUrl $serverUrl
            Read-Host "Press Enter to continue"
        }
        "7" {
            $inputUserId = Read-Host "Enter user ID (or press Enter for default)"
            if (-not $inputUserId) { $inputUserId = $userId }
            $inputLicenseType = Read-Host "Enter license type (or press Enter for 'free')"
            if (-not $inputLicenseType) { $inputLicenseType = "free" }
            $serverUrl = Read-Host "Enter API server URL (or press Enter for default)"
            if (-not $serverUrl) { $serverUrl = $ApiServerUrl }
            $apiKey = Test-ApiServerGetOrCreateLicense -userId $inputUserId -licenseType $inputLicenseType -apiServerUrl $serverUrl
            Read-Host "Press Enter to continue"
        }
        "8" {
            $inputUserId = Read-Host "Enter user ID (or press Enter for default)"
            if (-not $inputUserId) { $inputUserId = $userId }
            $inputLicenseType = Read-Host "Enter license type (or press Enter for 'free')"
            if (-not $inputLicenseType) { $inputLicenseType = "free" }
            $serverUrl = Read-Host "Enter API server URL (or press Enter for default)"
            if (-not $serverUrl) { $serverUrl = $ApiServerUrl }
            Test-EnhancedLicenseWorkflowViaApiServer -userId $inputUserId -licenseType $inputLicenseType -apiServerUrl $serverUrl
            Read-Host "Press Enter to continue"
        }
        "9" {
            Write-Host "`n🔍 Running all tests..." -ForegroundColor Cyan
            
            # First, create a license and get the API key
            $apiKey = Test-GetOrCreateLicenseEnhanced -userId $userId -licenseType $licenseType
            
            if ($apiKey) {
                # Test Supabase RPC functions
                Test-EnhancedVerifyApiKey -apiKey $apiKey
                Test-CheckLicenseStatus -userId $userId
                
                # Test API server endpoints
                Test-EnhancedApiServerVerification -apiKey $apiKey
                Test-ApiServerLicenseStatus -userId $userId
                Test-ApiServerGetOrCreateLicense -userId $userId -licenseType $licenseType
                
                # Test complete workflows
                Test-EnhancedLicenseWorkflow -userId $userId -licenseType $licenseType
                Test-EnhancedLicenseWorkflowViaApiServer -userId $userId -licenseType $licenseType
            } else {
                Write-Host "❌ Failed to create initial license for testing!" -ForegroundColor Red
            }
            
            Read-Host "Press Enter to continue"
        }
        "10" {
            Write-Host "Exiting..." -ForegroundColor Cyan
            exit
        }
        default {
            Write-Host "Invalid choice. Please try again." -ForegroundColor Red
            Read-Host "Press Enter to continue"
        }
    }
} while ($true)
