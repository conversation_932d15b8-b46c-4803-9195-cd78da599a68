#!/bin/bash
# Script to fix all Maestro timeout syntax errors systematically

# --- Global Variables ---
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT_DIR="$(cd "$SCRIPT_DIR/../.." && pwd)"
MAESTRO_DIR="$PROJECT_ROOT_DIR/testing/maestro"

# --- Color Functions ---
info() { echo -e "\033[1;32m[INFO]\033[0m $1"; }
warn() { echo -e "\033[1;33m[WARN]\033[0m $1"; }
error() { echo -e "\033[1;31m[ERROR]\033[0m $1"; }
success() { echo -e "\033[1;32m[SUCCESS]\033[0m $1"; }

# --- Fix Functions ---
fix_duplicate_timeouts() {
  local file="$1"
  info "Fixing duplicate timeout lines in: $(basename "$file")"
  
  # Remove duplicate timeout lines that appear consecutively
  sed -i '' '/timeout: [0-9]*$/N;s/\(timeout: [0-9]*\)\n *timeout: [0-9]*/\1/' "$file"
}

fix_assertvisible_timeouts() {
  local file="$1"
  info "Fixing assertVisible timeout issues in: $(basename "$file")"
  
  # This is complex to do with sed, so we'll handle it manually for each file
  # For now, just report the issues
  local count=$(grep -c "assertVisible:" "$file" | head -1)
  if [ "$count" -gt 0 ]; then
    warn "Found $count assertVisible commands in $file - needs manual review"
  fi
}

# --- Main Fix Function ---
fix_all_timeout_issues() {
  info "Starting comprehensive Maestro timeout syntax fixes..."
  
  # Find all YAML files in the maestro directory
  find "$MAESTRO_DIR" -name "*.yaml" | while read -r file; do
    info "Processing: $file"
    
    # Fix duplicate timeouts
    fix_duplicate_timeouts "$file"
    
    # Check for assertVisible timeout issues
    fix_assertvisible_timeouts "$file"
  done
  
  success "Timeout syntax fixes completed!"
}

# --- Verification Function ---
verify_syntax() {
  info "Verifying Maestro syntax..."
  
  # Check for remaining timeout issues
  local timeout_issues=$(find "$MAESTRO_DIR" -name "*.yaml" -exec grep -l "assertVisible:.*timeout\|assertNotVisible:.*timeout\|tapOn:.*timeout" {} \;)
  
  if [ -n "$timeout_issues" ]; then
    error "Found remaining timeout syntax issues in:"
    echo "$timeout_issues"
    return 1
  else
    success "No timeout syntax issues found!"
    return 0
  fi
}

# --- Script Entry Point ---
case "${1:-}" in
  --fix)
    fix_all_timeout_issues
    ;;
  --verify)
    verify_syntax
    ;;
  --all)
    fix_all_timeout_issues
    verify_syntax
    ;;
  "")
    echo "Usage: $0 [--fix|--verify|--all]"
    echo "  --fix     Fix timeout syntax issues"
    echo "  --verify  Verify syntax is correct"
    echo "  --all     Fix and verify"
    exit 1
    ;;
  *)
    error "Unknown option: $1"
    exit 1
    ;;
esac
