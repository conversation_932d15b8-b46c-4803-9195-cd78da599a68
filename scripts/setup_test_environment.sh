#!/bin/bash
# Test Environment Setup Script for Maestro UI Testing
# Helps set up the testing environment and install prerequisites

# --- Global Variables ---
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT_DIR="$(cd "$SCRIPT_DIR/../.." && pwd)"
CLIENT_DIR="$PROJECT_ROOT_DIR/client"

# --- Color Functions ---
info() { echo -e "\033[1;32m[INFO]\033[0m $1"; }
warn() { echo -e "\033[1;33m[WARN]\033[0m $1"; }
error() { echo -e "\033[1;31m[ERROR]\033[0m $1"; }
success() { echo -e "\033[1;32m[SUCCESS]\033[0m $1"; }

# --- Installation Functions ---
install_maestro() {
  info "Installing Maestro CLI..."
  
  if command -v maestro &> /dev/null; then
    local current_version
    current_version=$(maestro --version 2>/dev/null || echo "unknown")
    warn "Maestro is already installed (version: $current_version)"
    echo -n "Update to latest version? (y/N): "
    read update_confirm
    if [[ ! "$update_confirm" =~ ^[Yy]$ ]]; then
      return 0
    fi
  fi
  
  info "Downloading and installing Maestro..."
  if curl -fsSL "https://get.maestro.mobile.dev" | bash; then
    success "Maestro installation completed"
    info "Please restart your terminal or run: source ~/.bashrc"
    
    # Verify installation
    if command -v maestro &> /dev/null; then
      local new_version
      new_version=$(maestro --version 2>/dev/null || echo "unknown")
      success "Maestro version: $new_version"
    fi
  else
    error "Maestro installation failed"
    return 1
  fi
}

check_android_sdk() {
  info "Checking Android SDK installation..."
  
  if [[ -z "$ANDROID_HOME" ]]; then
    error "ANDROID_HOME environment variable not set"
    echo "Please install Android SDK and set ANDROID_HOME:"
    echo "  1. Download Android Studio or SDK tools"
    echo "  2. Set ANDROID_HOME to SDK location"
    echo "  3. Add \$ANDROID_HOME/platform-tools to PATH"
    return 1
  fi
  
  if ! command -v adb &> /dev/null; then
    error "ADB not found in PATH"
    echo "Please add Android SDK platform-tools to your PATH:"
    echo "  export PATH=\$ANDROID_HOME/platform-tools:\$PATH"
    return 1
  fi
  
  success "Android SDK found at: $ANDROID_HOME"
  info "ADB version: $(adb version | head -1)"
  return 0
}

setup_emulator() {
  info "Setting up Android emulator..."
  
  if [[ -z "$ANDROID_HOME" ]]; then
    error "ANDROID_HOME not set. Please install Android SDK first."
    return 1
  fi
  
  local avdmanager="$ANDROID_HOME/cmdline-tools/latest/bin/avdmanager"
  local emulator="$ANDROID_HOME/emulator/emulator"
  
  if [[ ! -x "$avdmanager" ]]; then
    error "AVD Manager not found at $avdmanager"
    echo "Please install Android SDK command-line tools"
    return 1
  fi
  
  if [[ ! -x "$emulator" ]]; then
    error "Emulator not found at $emulator"
    echo "Please install Android Emulator through SDK Manager"
    return 1
  fi
  
  local emulator_name="Maestro_Test_Device"
  
  # Check if emulator already exists
  if "$emulator" -list-avds | grep -q "$emulator_name"; then
    success "Test emulator '$emulator_name' already exists"
    return 0
  fi
  
  info "Creating test emulator: $emulator_name"
  echo "This will create an Android emulator optimized for UI testing"
  echo -n "Continue? (y/N): "
  read create_confirm
  
  if [[ "$create_confirm" =~ ^[Yy]$ ]]; then
    # Create AVD with reasonable specs for testing
    if yes | "$avdmanager" create avd \
      -n "$emulator_name" \
      -k "system-images;android-34;google_apis;x86_64" \
      --device "pixel_6" \
      --force; then
      success "Test emulator created successfully"
      info "Start emulator with: $emulator -avd $emulator_name"
    else
      error "Failed to create emulator"
      return 1
    fi
  fi
}

build_and_install_app() {
  info "Building and installing Promz app..."
  
  if [[ ! -d "$CLIENT_DIR" ]]; then
    error "Client directory not found: $CLIENT_DIR"
    return 1
  fi
  
  # Check if Flutter is installed
  if ! command -v flutter &> /dev/null; then
    error "Flutter not found. Please install Flutter SDK."
    return 1
  fi
  
  # Check device connectivity
  local device_count
  device_count=$(adb devices | grep -E "(device|emulator)" | wc -l)
  
  if [ "$device_count" -eq 0 ]; then
    error "No Android devices or emulators found"
    echo "Please connect a device or start an emulator first"
    return 1
  fi
  
  info "Found $device_count connected device(s)"
  
  cd "$CLIENT_DIR" || return 1
  
  info "Building debug APK..."
  if flutter build apk --debug; then
    success "APK build completed"
    
    local apk_path="build/app/outputs/flutter-apk/app-debug.apk"
    if [[ -f "$apk_path" ]]; then
      info "Installing APK on device..."
      if adb install -r "$apk_path"; then
        success "App installed successfully"
      else
        error "App installation failed"
        return 1
      fi
    else
      error "APK file not found: $apk_path"
      return 1
    fi
  else
    error "APK build failed"
    return 1
  fi
  
  cd - > /dev/null
}

verify_setup() {
  info "Verifying test environment setup..."
  local setup_ok=true
  
  # Check Maestro
  if command -v maestro &> /dev/null; then
    success "✓ Maestro CLI installed"
  else
    error "✗ Maestro CLI not found"
    setup_ok=false
  fi
  
  # Check Android SDK
  if command -v adb &> /dev/null; then
    success "✓ Android SDK (ADB) available"
  else
    error "✗ Android SDK (ADB) not found"
    setup_ok=false
  fi
  
  # Check device connectivity
  local device_count
  device_count=$(adb devices | grep -E "(device|emulator)" | wc -l)
  
  if [ "$device_count" -gt 0 ]; then
    success "✓ $device_count Android device(s) connected"
  else
    warn "⚠ No Android devices connected"
    setup_ok=false
  fi
  
  # Check app installation
  if adb shell pm list packages | grep -q "ai.promz"; then
    success "✓ Promz app installed"
  else
    warn "⚠ Promz app not installed"
    setup_ok=false
  fi
  
  if [ "$setup_ok" = true ]; then
    success "Test environment is ready!"
    info "You can now run: $SCRIPT_DIR/run_maestro_tests.sh"
  else
    warn "Test environment setup incomplete"
    echo "Please resolve the issues above before running tests"
  fi
  
  return 0
}

show_menu() {
  while true; do
    echo -e "\n\033[1;32mMaestro Test Environment Setup\033[0m"
    echo "Project Root: $PROJECT_ROOT_DIR"
    echo "-------------------------------------------"
    echo "  1. Install/Update Maestro CLI"
    echo "  2. Check Android SDK Setup"
    echo "  3. Setup Test Emulator"
    echo "  4. Build and Install Promz App"
    echo "  5. Verify Complete Setup"
    echo "  6. Run Full Setup (All Steps)"
    echo "-------------------------------------------"
    echo "  0. Exit"
    echo "-------------------------------------------"
    
    local choice
    echo -n "Enter your choice (0-6): "
    read choice
    
    case $choice in
      1) install_maestro ;;
      2) check_android_sdk ;;
      3) setup_emulator ;;
      4) build_and_install_app ;;
      5) verify_setup ;;
      6) 
        info "Running full setup..."
        install_maestro && \
        check_android_sdk && \
        setup_emulator && \
        build_and_install_app && \
        verify_setup
        ;;
      0) 
        info "Setup complete. You can now run Maestro tests."
        exit 0 
        ;;
      *) warn "Invalid choice. Please try again." ;;
    esac
    
    echo ""
    echo -n "Press any key to continue..."
    read -n 1 -s
    echo
  done
}

# --- Script Entry Point ---
case "${1:-}" in
  --maestro)
    install_maestro
    ;;
  --android)
    check_android_sdk
    ;;
  --emulator)
    setup_emulator
    ;;
  --app)
    build_and_install_app
    ;;
  --verify)
    verify_setup
    ;;
  --all)
    install_maestro && check_android_sdk && setup_emulator && build_and_install_app && verify_setup
    ;;
  "")
    show_menu
    ;;
  *)
    echo "Usage: $0 [--maestro|--android|--emulator|--app|--verify|--all]"
    exit 1
    ;;
esac
