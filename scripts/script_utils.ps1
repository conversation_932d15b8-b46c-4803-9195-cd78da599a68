# Set environment variables needed for the API to run
$env:PROMZ_DB_CONNECTION="postgresql://postgres.djqmdekosqnyxoqujxpm:<EMAIL>:5432/postgres"
$env:PROMZ_OPENAI_API_KEY="********************************************************************************************************************************************************************"
$env:PROMZ_GOOGLE_API_KEY="AIzaSyDLn4s--JsAOAf-RUBoTrr9z1tauJrk8zg"
$env:PROMZ_YOUTUBE_API_KEY="AIzaSyA2k_TJtGumpX0SQsRGsi7m7Whhb_gsbkQ"
$env:PROMZ_SUPABASE_URL="https://djqmdekosqnyxoqujxpm.supabase.co"
$env:PROMZ_SUPABASE_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRqcW1kZWtvc3FueXhvcXVqeHBtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzkzMDk1NDksImV4cCI6MjA1NDg4NTU0OX0.gXloMCWb_UN5fol9EZQoJTBK9_Ou_I0E6jVbfgN1mms"
$env:PROMZ_STORAGE_BUCKET="promz-content-upload"
$env:PROMZ_TRUSTED_PROXIES="127.0.0.1,::1"
$env:GRPC_PORT="50051"

# Variables for the API server
$API_BASE_URL = "http://localhost:8080"
$LOG_FILE = (Join-Path $env:APPDATA "promz\api.log")

function Start-ApiServer {
    param (
        [switch]$Debug,
        [int]$DebugPort = 2345,  # Default delve debug port
        [string]$LogFile = (Join-Path $env:APPDATA "promz\api.log"), # Add LogFile parameter
        [switch]$UseRealExecution, # Add parameter to enable real execution
        [switch]$VerboseLogging # Add parameter to enable verbose logging
    )

    Write-Host "Starting API server..." -ForegroundColor Yellow

    # Save the current location to restore it later
    $originalLocation = Get-Location

    # Set execution mode
    if ($UseRealExecution) {
        $env:PROMZ_USE_REAL_EXECUTION = "true"
        Write-Host "Using REAL execution mode (will make actual LLM API calls)" -ForegroundColor Yellow
    } else {
        $env:PROMZ_USE_REAL_EXECUTION = "false"
        Write-Host "Using MOCK execution mode (will display request details only)" -ForegroundColor Cyan
    }
    
    # Set logging verbosity
    if ($VerboseLogging -or $Debug) {
        $env:PROMZ_DEBUG = "true"
        Write-Host "Using VERBOSE logging mode (will show detailed logs)" -ForegroundColor Magenta
    } else {
        $env:PROMZ_DEBUG = "false"
        Write-Host "Using CONCISE logging mode (minimal logs)" -ForegroundColor Green
    }

    # Initialize log
    Set-Content $LogFile "=== API Server Started: $(Get-Date) ===`n"

    # Change to the server directory
    $projectRoot = Split-Path $PSScriptRoot  # This gives us C:\projects\promz
    $apiServerPath = Join-Path $projectRoot "api\cmd\server"
    
    # Ensure the path exists
    if (-not (Test-Path $apiServerPath)) {
        Write-Error "Cannot find API server path at: $apiServerPath"
        # Restore original location before returning
        Set-Location $originalLocation
        return
    }
    
    Set-Location $apiServerPath

    # Determine the command to execute
    if ($Debug) {
        $process = Start-Process -FilePath "dlv" -ArgumentList "debug --headless --listen=:$DebugPort --api-version=2 --accept-multiclient" -NoNewWindow -PassThru
    } else {
        $process = Start-Process -FilePath "go" -ArgumentList "run ." -NoNewWindow -PassThru
    }

    Write-Host "API server started with PID: $($process.Id)" -ForegroundColor Green
    Start-Sleep -Seconds 2  # Give the server a moment to start
    
    # Check if the server is running
    if (Test-ApiServerRunning) {
        Write-Host "API server is running successfully!" -ForegroundColor Green
    } else {
        Write-Warning "API server may not have started correctly. Check the log file: $LogFile"
    }
    
    # Restore the original location
    Set-Location $originalLocation
    
    # Return to the menu instead of exiting
    return
}

function Stop-ApiServer {
    Write-Host "Stopping API server..." -ForegroundColor Yellow

    # Save the current location to restore it later
    $originalLocation = Get-Location

    # --- Stop process on gRPC port --- 
    $grpcPort = $env:GRPC_PORT # Get gRPC port from environment variable
    if (-not [string]::IsNullOrWhiteSpace($grpcPort)) {
        Write-Host "Checking for process on gRPC port $grpcPort..." -ForegroundColor Cyan
        $grpcConnection = Get-NetTCPConnection -ErrorAction SilentlyContinue | Where-Object {$_.LocalPort -eq $grpcPort -and $_.State -eq 'Listen'}
        # Initialize $grpcProcess to null to avoid uninitialized variable errors
        $grpcProcess = $null
        if ($grpcConnection) {
            $grpcProcess = Get-Process -Id $grpcConnection.OwningProcess -ErrorAction SilentlyContinue
            if ($grpcProcess) {
                Write-Host "Found process $($grpcProcess.ProcessName) (PID: $($grpcProcess.Id)) listening on gRPC port $grpcPort."
                try {
                    Stop-Process -Id $grpcProcess.Id -Force
                    Write-Host "Stopped process on gRPC port $grpcPort successfully." -ForegroundColor Green
                } catch {
                    Write-Warning "Error stopping process on gRPC port $grpcPort $($_)" 
                }
            } else {
                Write-Warning "Could not get process details for connection on gRPC port $grpcPort."
            }
        } else {
            Write-Host "No process found listening on gRPC port $grpcPort." -ForegroundColor Green
        }
    } else {
        Write-Warning "GRPC_PORT environment variable not set or empty. Skipping gRPC port check."
    }
    # --- End of gRPC port check --- 

    # --- Stop process on HTTP port (existing logic) ---
    $httpPort = 8080
    Write-Host "Checking for process on HTTP port $httpPort..." -ForegroundColor Cyan
    $httpConnection = Get-NetTCPConnection -ErrorAction SilentlyContinue | Where-Object {$_.LocalPort -eq $httpPort -and $_.State -eq 'Listen'}

    if ($httpConnection) {
        $httpProcess = Get-Process -Id $httpConnection.OwningProcess -ErrorAction SilentlyContinue
        if ($httpProcess) {
            # Check if it's the same process we already stopped
            if ($null -ne $grpcProcess -and $grpcProcess.Id -eq $httpProcess.Id) {
                Write-Host "Process (PID: $($httpProcess.Id)) on HTTP port $httpPort was already stopped (was also using gRPC port)." -ForegroundColor Green
            } else {
                Write-Host "Found process $($httpProcess.ProcessName) (PID: $($httpProcess.Id)) listening on HTTP port $httpPort."
                try {
                    Stop-Process -Id $httpProcess.Id -Force
                    Write-Host "Stopped process on HTTP port $httpPort successfully." -ForegroundColor Green
                } catch {
                    Write-Warning "Error stopping process on HTTP port $httpPort $($_)"
                }
            }
        } else {
            Write-Warning "Could not get process details for connection on HTTP port $httpPort."
        }
    } else {
        Write-Host "No process found listening on HTTP port $httpPort." -ForegroundColor Green
    }
    # --- End of HTTP port check ---

    # Restore the original location
    Set-Location $originalLocation
    
    # Return to the menu instead of exiting
    return
}

function Test-ApiServerRunning {
    $existingProc = Get-ExistingApiProcess
    if ($existingProc) {
        Write-Host "API server is running (PID: $($existingProc.Id))" -ForegroundColor Green
        return $true
    }
    
    # Also try to connect to the health endpoint
    try {
        $response = Invoke-RestMethod -Uri "$API_BASE_URL/health" -Method Get -TimeoutSec 2
        if ($response.status -eq "ok") {
            Write-Host "API server is responding to health checks" -ForegroundColor Green
            return $true
        }
    } catch {
        # Server is not responding
    }
    
    return $false
}

function Get-ExistingApiProcess {
    # Check by port
    $existingProcByPort = Get-NetTCPConnection -LocalPort 8080 -ErrorAction SilentlyContinue | 
                          ForEach-Object { Get-Process -Id $_.OwningProcess -ErrorAction SilentlyContinue }
    
    # If found by port, return that
    if ($existingProcByPort) {
        return $existingProcByPort
    }
    return $null
}

function Test-Prerequisites {
    # Check if Go is installed
    if (-not (Get-Command go -ErrorAction SilentlyContinue)) {
        Write-Warning "Go is required. Download from: https://golang.org/dl/"
        exit 1
    }

    # Check environment variables
    if (-not [Environment]::GetEnvironmentVariable("PROMZ_SUPABASE_URL") -or 
        -not [Environment]::GetEnvironmentVariable("PROMZ_SUPABASE_KEY")) {
        Write-Warning "Missing PROMZ_SUPABASE_URL or PROMZ_SUPABASE_KEY. Set them in System Environment Variables."
        exit 1
    }

    # Create log directory if it doesn't exist
    $logDir = Split-Path $LOG_FILE
    if (-not (Test-Path $logDir)) {
        New-Item -ItemType Directory -Path $logDir -Force | Out-Null
    }
}
