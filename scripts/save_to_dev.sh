#!/bin/bash

set -e  # Exit on error

# Function to ensure we're inside a Git repository
verify_git_repo() {
    if [ ! -d ".git" ]; then
        echo "❌ Not inside a Git repository. Please run this script from a Git repo."
        exit 1
    fi
}

# Function to check for uncommitted changes and exit if any are found
check_uncommitted_changes() {
    if [ -n "$(git status --porcelain)" ]; then
        echo "❌ There are uncommitted changes in your repository."
        echo "Please commit or stash your changes before running this script."
        exit 1
    fi
}

# Function to check for remote and main branch
check_unpushed_changes() {
# Check for local commits that are not pushed
    if [ -n "$(git log --oneline origin/main..HEAD)" ]; then
        echo "❌ Your branch is ahead of the remote. You have local commits that have not been pushed."
        echo "Please push your changes or create a backup branch before proceeding."
        exit 1
    fi
}

verify_git_repo
check_uncommitted_changes
check_unpushed_changes

clear
echo "=================================================================="
echo "     Git Sync Helper                                              "
echo "=================================================================="
echo "[0] Exit"
echo "------------------------------------------------------------------"
echo "[1] Go from main to dev (Update dev to match main)"
echo "[2] Go from dev to main (Squash merge dev changes into main)"
echo "[3] Reset main to latest from remote (origin/main)"
echo "=================================================================="

read -p "Choose an option (0-3): " choice

if [ "$choice" = "0" ]; then
    echo "👋 Exiting script. Have a great day!"
    exit 0

elif [ "$choice" = "1" ]; then
    # Ensure we are on main before updating dev
    CURRENT_BRANCH=$(git rev-parse --abbrev-ref HEAD)
    if [ "$CURRENT_BRANCH" != "main" ]; then
        echo "❌ You must be on 'main' to update dev. Currently on '$CURRENT_BRANCH'."
        exit 1
    fi

    echo "🔄 Fetching latest changes from remote..."
    git fetch origin

    echo "🔀 Checking out 'dev' branch..."
    # Switch to dev or create it if it doesn't exist
    git checkout dev || git checkout -b dev

    echo "🔄 Updating dev to match main..."
    # Force reset dev to be identical to current main
    git reset --hard main

    # Optionally, set dev to track origin/main if desired:
    git branch --set-upstream-to=origin/main dev

    echo "✅ 'dev' is now updated to match 'main'."

elif [ "$choice" = "2" ]; then
    # Squash merge dev changes into main
    echo "🔀 Switching to 'main' branch..."
    git checkout main

    echo "🔄 Squash merging 'dev' into 'main'..."
    git merge --squash dev

    echo "✅ All changes from 'dev' are now staged in 'main' as a single squash commit."
    echo "Review the changes with 'git status' and commit manually when ready."

elif [ "$choice" = "3" ]; then
    echo "🔀 Switching to 'main' branch..."
    git checkout main

    echo "🔄 Fetching latest changes from remote..."
    git fetch origin

    echo "⚠️  Force resetting 'main' to match 'origin/main'..."
    git reset --hard origin/main

    echo "✅ 'main' is now reset to the latest commit from 'origin/main'."

else
    echo "❌ Invalid option. Please select a number from 0 to 3."
    exit 1
fi
