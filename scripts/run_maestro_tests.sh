#!/bin/bash
# Maestro UI Testing Script for Promz Android App
# Provides interactive menu system for running comprehensive UI tests

# --- Global Variables ---
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT_DIR="$(cd "$SCRIPT_DIR/../.." && pwd)"
TESTING_DIR="$PROJECT_ROOT_DIR/testing"
MAESTRO_DIR="$TESTING_DIR/maestro"
CLIENT_DIR="$PROJECT_ROOT_DIR/client"
ORIGINAL_DIR="$(pwd)"
APP_ID="ai.promz"
SCREENSHOTS_DIR="$TESTING_DIR/screenshots"

# Test categories
ANDROID_AUTH_DIR="$MAESTRO_DIR/android/authentication"
TEST_SUITES_DIR="$MAESTRO_DIR/test_suites"

# --- Color Functions ---
info() { echo -e "\033[1;32m[INFO]\033[0m $1"; }
warn() { echo -e "\033[1;33m[WARN]\033[0m $1"; }
error() { echo -e "\033[1;31m[ERROR]\033[0m $1"; }
debug() { echo -e "\033[1;34m[DEBUG]\033[0m $1"; }
success() { echo -e "\033[1;32m[SUCCESS]\033[0m $1"; }

# --- Cleanup Function ---
cleanup_and_exit() {
  info "Cleaning up and exiting..."
  cd "$ORIGINAL_DIR"
  exit 0
}

# --- Utility Functions ---
create_screenshots_dir() {
  mkdir -p "$SCREENSHOTS_DIR"
  info "Screenshots will be saved to: $SCREENSHOTS_DIR"
}

check_maestro_installation() {
  info "Checking Maestro installation..."
  if ! command -v maestro &> /dev/null; then
    error "Maestro CLI not found. Please install Maestro first."
    echo "Installation command: curl -fsSL \"https://get.maestro.mobile.dev\" | bash"
    return 1
  fi
  
  local maestro_version
  maestro_version=$(maestro --version 2>/dev/null || echo "unknown")
  info "Maestro version: $maestro_version"
  return 0
}

check_device_connectivity() {
  info "Checking device connectivity..."
  if ! command -v adb &> /dev/null; then
    error "ADB not found. Please install Android SDK and add adb to your PATH."
    return 1
  fi
  
  local devices
  devices=$(adb devices | grep -v "List of devices" | grep -E "(device|emulator)" | wc -l)
  
  if [ "$devices" -eq 0 ]; then
    error "No Android devices or emulators found."
    echo "Please connect a device or start an emulator:"
    echo "  - Physical device: Enable USB debugging and connect via USB"
    echo "  - Emulator: Start an Android emulator"
    echo "  - Check with: adb devices"
    return 1
  fi
  
  info "Found $devices connected device(s):"
  adb devices | grep -E "(device|emulator)" | while read -r line; do
    echo "  - $line"
  done
  return 0
}

check_app_installation() {
  info "Checking Promz app installation..."
  if ! adb shell pm list packages | grep -q "$APP_ID"; then
    error "Promz app ($APP_ID) not found on device."
    echo "Please install the app first:"
    echo "  1. Build the app: cd $CLIENT_DIR && flutter build apk"
    echo "  2. Install: adb install build/app/outputs/flutter-apk/app-debug.apk"
    echo "  3. Or use the Android build script: $PROJECT_ROOT_DIR/scripts/run_android.sh"
    return 1
  fi
  
  info "Promz app is installed on device."
  return 0
}

check_prerequisites() {
  info "Checking prerequisites for Maestro testing..."
  local prerequisites_ok=true
  
  check_maestro_installation || prerequisites_ok=false
  check_device_connectivity || prerequisites_ok=false
  check_app_installation || prerequisites_ok=false
  
  if [ "$prerequisites_ok" = false ]; then
    error "Prerequisites check failed. Please resolve the issues above."
    return 1
  fi
  
  success "All prerequisites satisfied!"
  return 0
}

# --- Test Execution Functions ---
run_individual_test() {
  local test_file="$1"
  local test_name="$(basename "$test_file" .yaml)"
  
  info "Running individual test: $test_name"
  info "Test file: $test_file"
  
  if [ ! -f "$test_file" ]; then
    error "Test file not found: $test_file"
    return 1
  fi
  
  create_screenshots_dir
  cd "$TESTING_DIR" || return 1
  
  info "Starting test execution..."
  if maestro test "$test_file"; then
    success "Test completed successfully: $test_name"
  else
    error "Test failed: $test_name"
    return 1
  fi
  
  cd "$ORIGINAL_DIR"
}

run_test_suite() {
  local suite_file="$1"
  local suite_name="$(basename "$suite_file" .yaml)"
  
  info "Running test suite: $suite_name"
  info "Suite file: $suite_file"
  
  if [ ! -f "$suite_file" ]; then
    error "Test suite file not found: $suite_file"
    return 1
  fi
  
  create_screenshots_dir
  cd "$TESTING_DIR" || return 1
  
  info "Starting test suite execution..."
  info "Note: Authentication tests require manual OAuth interaction"
  
  if maestro test "$suite_file"; then
    success "Test suite completed successfully: $suite_name"
  else
    error "Test suite failed: $suite_name"
    return 1
  fi
  
  cd "$ORIGINAL_DIR"
}

run_debug_test() {
  local test_file="$1"
  local test_name="$(basename "$test_file" .yaml)"
  
  info "Running test in debug mode: $test_name"
  warn "Debug mode provides verbose output and detailed logging"
  
  if [ ! -f "$test_file" ]; then
    error "Test file not found: $test_file"
    return 1
  fi
  
  create_screenshots_dir
  cd "$TESTING_DIR" || return 1
  
  info "Starting debug test execution..."
  if maestro test --debug "$test_file"; then
    success "Debug test completed: $test_name"
  else
    error "Debug test failed: $test_name"
    return 1
  fi
  
  cd "$ORIGINAL_DIR"
}

# --- Menu Functions ---
show_individual_tests_menu() {
  echo -e "\n\033[1;36mIndividual Authentication Tests:\033[0m"
  echo "  1. Pre-Login State Verification"
  echo "  2. Login Flow (requires manual OAuth)"
  echo "  3. Post-Login State Verification"
  echo "  4. Navigation Access Verification"
  echo "  5. Logout Flow Verification"
  echo "  6. Back to Main Menu"
  
  local choice
  echo -n "Select test (1-6): "
  read choice
  
  case $choice in
    1) run_individual_test "$ANDROID_AUTH_DIR/01_pre_login_state.yaml" ;;
    2) 
      warn "This test requires manual Google OAuth interaction"
      echo -n "Continue? (y/N): "
      read confirm
      if [[ "$confirm" =~ ^[Yy]$ ]]; then
        run_individual_test "$ANDROID_AUTH_DIR/02_login_flow.yaml"
      fi
      ;;
    3) run_individual_test "$ANDROID_AUTH_DIR/03_post_login_state.yaml" ;;
    4) run_individual_test "$ANDROID_AUTH_DIR/04_navigation_access.yaml" ;;
    5) run_individual_test "$ANDROID_AUTH_DIR/05_logout_flow.yaml" ;;
    6) return ;;
    *) warn "Invalid choice. Please try again." ;;
  esac
}

show_test_suites_menu() {
  echo -e "\n\033[1;36mTest Suites:\033[0m"
  echo "  1. Complete Authentication Suite (all auth tests)"
  echo "  2. Back to Main Menu"
  
  local choice
  echo -n "Select suite (1-2): "
  read choice
  
  case $choice in
    1)
      warn "This suite includes manual OAuth interaction"
      echo -n "Continue? (y/N): "
      read confirm
      if [[ "$confirm" =~ ^[Yy]$ ]]; then
        run_test_suite "$TEST_SUITES_DIR/authentication_suite.yaml"
      fi
      ;;
    2) return ;;
    *) warn "Invalid choice. Please try again." ;;
  esac
}

show_debug_menu() {
  echo -e "\n\033[1;36mDebug Mode Tests:\033[0m"
  echo "  1. Debug Pre-Login State"
  echo "  2. Debug Login Flow"
  echo "  3. Debug Post-Login State"
  echo "  4. Debug Navigation Access"
  echo "  5. Debug Logout Flow"
  echo "  6. Back to Main Menu"

  local choice
  echo -n "Select debug test (1-6): "
  read choice

  case $choice in
    1) run_debug_test "$ANDROID_AUTH_DIR/01_pre_login_state.yaml" ;;
    2) run_debug_test "$ANDROID_AUTH_DIR/02_login_flow.yaml" ;;
    3) run_debug_test "$ANDROID_AUTH_DIR/03_post_login_state.yaml" ;;
    4) run_debug_test "$ANDROID_AUTH_DIR/04_navigation_access.yaml" ;;
    5) run_debug_test "$ANDROID_AUTH_DIR/05_logout_flow.yaml" ;;
    6) return ;;
    *) warn "Invalid choice. Please try again." ;;
  esac
}

show_utilities_menu() {
  echo -e "\n\033[1;36mUtilities:\033[0m"
  echo "  1. Check Prerequisites"
  echo "  2. View Device Information"
  echo "  3. Launch Maestro Studio (Interactive Testing)"
  echo "  4. View Test Screenshots"
  echo "  5. Clean Screenshots Directory"
  echo "  6. Install/Update Maestro"
  echo "  7. Back to Main Menu"

  local choice
  echo -n "Select utility (1-7): "
  read choice

  case $choice in
    1) check_prerequisites ;;
    2) show_device_info ;;
    3) launch_maestro_studio ;;
    4) view_screenshots ;;
    5) clean_screenshots ;;
    6) install_maestro ;;
    7) return ;;
    *) warn "Invalid choice. Please try again." ;;
  esac
}

# --- Utility Functions ---
show_device_info() {
  info "Device Information:"
  echo "Connected devices:"
  adb devices
  echo ""

  local device_count
  device_count=$(adb devices | grep -E "(device|emulator)" | wc -l)

  if [ "$device_count" -gt 0 ]; then
    info "App installation status:"
    if adb shell pm list packages | grep -q "$APP_ID"; then
      success "Promz app ($APP_ID) is installed"

      # Get app version if possible
      local version_info
      version_info=$(adb shell dumpsys package "$APP_ID" | grep "versionName" | head -1 || echo "Version info not available")
      echo "  $version_info"
    else
      warn "Promz app ($APP_ID) is not installed"
    fi

    echo ""
    info "Device properties:"
    echo "  Model: $(adb shell getprop ro.product.model)"
    echo "  Android version: $(adb shell getprop ro.build.version.release)"
    echo "  API level: $(adb shell getprop ro.build.version.sdk)"
  fi
}

launch_maestro_studio() {
  info "Launching Maestro Studio for interactive testing..."
  warn "Maestro Studio will open in your browser for interactive test creation"

  if command -v maestro &> /dev/null; then
    maestro studio
  else
    error "Maestro not found. Please install Maestro first."
  fi
}

view_screenshots() {
  if [ ! -d "$SCREENSHOTS_DIR" ]; then
    warn "Screenshots directory not found: $SCREENSHOTS_DIR"
    return
  fi

  local screenshot_count
  screenshot_count=$(find "$SCREENSHOTS_DIR" -name "*.png" 2>/dev/null | wc -l)

  if [ "$screenshot_count" -eq 0 ]; then
    warn "No screenshots found in $SCREENSHOTS_DIR"
    return
  fi

  info "Found $screenshot_count screenshot(s) in $SCREENSHOTS_DIR:"
  find "$SCREENSHOTS_DIR" -name "*.png" -exec basename {} \; | sort

  echo ""
  echo -n "Open screenshots directory? (y/N): "
  read open_dir
  if [[ "$open_dir" =~ ^[Yy]$ ]]; then
    if command -v open &> /dev/null; then
      open "$SCREENSHOTS_DIR"
    elif command -v xdg-open &> /dev/null; then
      xdg-open "$SCREENSHOTS_DIR"
    else
      info "Screenshots directory: $SCREENSHOTS_DIR"
    fi
  fi
}

clean_screenshots() {
  if [ ! -d "$SCREENSHOTS_DIR" ]; then
    warn "Screenshots directory not found: $SCREENSHOTS_DIR"
    return
  fi

  local screenshot_count
  screenshot_count=$(find "$SCREENSHOTS_DIR" -name "*.png" 2>/dev/null | wc -l)

  if [ "$screenshot_count" -eq 0 ]; then
    info "Screenshots directory is already clean"
    return
  fi

  echo -n "Delete $screenshot_count screenshot(s)? (y/N): "
  read confirm
  if [[ "$confirm" =~ ^[Yy]$ ]]; then
    rm -f "$SCREENSHOTS_DIR"/*.png
    success "Screenshots directory cleaned"
  else
    info "Screenshots preserved"
  fi
}

install_maestro() {
  info "Installing/updating Maestro..."
  warn "This will download and install the latest version of Maestro"

  echo -n "Continue? (y/N): "
  read confirm
  if [[ "$confirm" =~ ^[Yy]$ ]]; then
    curl -fsSL "https://get.maestro.mobile.dev" | bash
    if [ $? -eq 0 ]; then
      success "Maestro installation completed"
      info "Please restart your terminal or run: source ~/.bashrc"
    else
      error "Maestro installation failed"
    fi
  fi
}

show_help() {
  echo -e "\n\033[1;32mMaestro UI Testing for Promz Android App\033[0m"
  echo "========================================"
  echo ""
  echo "This script provides comprehensive UI testing capabilities for the Promz Android app"
  echo "using the Maestro testing framework."
  echo ""
  echo "Prerequisites:"
  echo "  - Maestro CLI installed (curl -fsSL \"https://get.maestro.mobile.dev\" | bash)"
  echo "  - Android device or emulator connected"
  echo "  - Promz Android app installed on device"
  echo "  - Internet connectivity for OAuth tests"
  echo ""
  echo "Test Categories:"
  echo "  - Individual Tests: Run specific test scenarios"
  echo "  - Test Suites: Run complete test sequences"
  echo "  - Debug Mode: Run tests with verbose output"
  echo "  - Utilities: Helper tools and information"
  echo ""
  echo "Authentication Tests:"
  echo "  1. Pre-Login State: Verify unauthenticated UI"
  echo "  2. Login Flow: Test Google OAuth authentication (manual interaction required)"
  echo "  3. Post-Login State: Verify authenticated UI"
  echo "  4. Navigation Access: Test authenticated navigation"
  echo "  5. Logout Flow: Test logout and return to unauthenticated state"
  echo ""
  echo "Screenshots:"
  echo "  - Automatically saved to: $SCREENSHOTS_DIR"
  echo "  - Captured at key test points for debugging"
  echo "  - Can be viewed and cleaned via utilities menu"
  echo ""
  echo "For more information, see: $TESTING_DIR/maestro/README.md"
}

# --- Main Menu ---
show_main_menu() {
  while true; do
    echo -e "\n\033[1;32mMaestro UI Testing for Promz Android App\033[0m"
    echo "Project Root: $PROJECT_ROOT_DIR"
    echo "Testing Dir:  $TESTING_DIR"
    echo "App ID:       $APP_ID"
    if [ -d "$SCREENSHOTS_DIR" ]; then
      local screenshot_count
      screenshot_count=$(find "$SCREENSHOTS_DIR" -name "*.png" 2>/dev/null | wc -l)
      echo "Screenshots:  $screenshot_count files in $SCREENSHOTS_DIR"
    fi
    echo "-------------------------------------------"
    echo "Test Execution:"
    echo "  1. Run Individual Tests"
    echo "  2. Run Test Suites"
    echo "  3. Run Tests in Debug Mode"
    echo "Setup & Utilities:"
    echo "  4. Check Prerequisites"
    echo "  5. Utilities Menu"
    echo "Information:"
    echo "  6. Show Help"
    echo "  7. View Test Documentation"
    echo "-------------------------------------------"
    echo "  0. Exit"
    echo "-------------------------------------------"

    local choice
    echo -n "Enter your choice (0-7): "
    read choice

    case $choice in
      1) show_individual_tests_menu ;;
      2) show_test_suites_menu ;;
      3) show_debug_menu ;;
      4) check_prerequisites ;;
      5) show_utilities_menu ;;
      6) show_help ;;
      7) view_documentation ;;
      0) cleanup_and_exit ;;
      *) warn "Invalid choice. Please try again." ;;
    esac

    if [ "$choice" != "0" ]; then
      echo ""
      echo -n "Press any key to continue..."
      read -n 1 -s
      echo
    fi
  done
}

view_documentation() {
  local readme_file="$MAESTRO_DIR/README.md"

  if [ -f "$readme_file" ]; then
    info "Opening Maestro testing documentation..."
    if command -v less &> /dev/null; then
      less "$readme_file"
    elif command -v more &> /dev/null; then
      more "$readme_file"
    else
      cat "$readme_file"
    fi
  else
    warn "Documentation file not found: $readme_file"
  fi
}

# --- Script Entry Point ---
main() {
  info "Maestro UI Testing Script for Promz Android App"
  info "Script initialized. Original directory: $ORIGINAL_DIR"

  # Create screenshots directory
  create_screenshots_dir

  # Show main menu
  show_main_menu
}

# Handle script arguments
case "${1:-}" in
  --help|-h)
    show_help
    exit 0
    ;;
  --check-prereq)
    check_prerequisites
    exit $?
    ;;
  --individual)
    check_prerequisites && show_individual_tests_menu
    exit $?
    ;;
  --suite)
    check_prerequisites && show_test_suites_menu
    exit $?
    ;;
  --debug)
    check_prerequisites && show_debug_menu
    exit $?
    ;;
  "")
    # No arguments, show main menu
    main
    ;;
  *)
    error "Unknown argument: $1"
    echo "Usage: $0 [--help|--check-prereq|--individual|--suite|--debug]"
    exit 1
    ;;
esac
