#!/bin/bash

# Maestro Test Refactoring Script
# This script helps migrate existing test files to use the new modular components

set -e

echo "🔧 Maestro Test Refactoring Tool"
echo "================================"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if we're in the right directory
if [ ! -d "testing/maestro/android" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

# Function to backup a file
backup_file() {
    local file="$1"
    local backup="${file}.backup.$(date +%Y%m%d_%H%M%S)"
    cp "$file" "$backup"
    print_info "Backed up $file to $backup"
}

# Function to refactor navigation assertions
refactor_navigation_assertions() {
    local file="$1"
    print_info "Refactoring navigation assertions in $file"
    
    # Create backup
    backup_file "$file"
    
    # Replace navigation assertion blocks with modular component calls
    # This is a simplified version - manual review is still recommended
    
    # Replace the common navigation assertion pattern
    sed -i '' '/# Step.*navigation.*accessible/,/assertVisible.*Tab.*of 5/ {
        /assertVisible.*Tab.*of 5/a\
# Use modular navigation assertions\
- runFlow: ..\/common\/navigation_assertions.yaml
        /assertVisible.*Tab.*of 5/d
    }' "$file"
    
    print_status "Updated navigation assertions in $file"
}

# Function to refactor authentication state checks
refactor_auth_assertions() {
    local file="$1"
    print_info "Refactoring authentication assertions in $file"
    
    # Replace common auth patterns
    sed -i '' 's/assertVisible: "Sign in"/runFlow: ..\/common\/auth_state_assertions.yaml  # Verify unauthenticated state/g' "$file"
    sed -i '' 's/assertNotVisible: "Sign in"/runFlow: ..\/common\/auth_state_assertions.yaml  # Verify authenticated state/g' "$file"
    
    print_status "Updated authentication assertions in $file"
}

# Function to show refactoring suggestions
show_suggestions() {
    local file="$1"
    echo ""
    print_info "Manual refactoring suggestions for $file:"
    echo ""
    
    # Check for navigation patterns
    if grep -q "assertVisible.*Tab.*of 5" "$file"; then
        echo "  📝 Replace navigation assertions with:"
        echo "     - runFlow: ../common/navigation_assertions.yaml"
        echo ""
    fi
    
    # Check for auth patterns
    if grep -q "assertVisible.*Sign in" "$file"; then
        echo "  📝 Replace authentication checks with:"
        echo "     - runFlow: ../common/auth_state_assertions.yaml"
        echo ""
    fi
    
    # Check for common UI patterns
    if grep -q "waitForAnimationToEnd:" "$file"; then
        echo "  📝 Consider using standardized waits from:"
        echo "     - runFlow: ../common/ui_actions.yaml"
        echo ""
    fi
}

# Main refactoring function
refactor_file() {
    local file="$1"
    local mode="$2"
    
    echo ""
    print_info "Processing: $file"
    
    if [ ! -f "$file" ]; then
        print_error "File not found: $file"
        return 1
    fi
    
    case "$mode" in
        "auto")
            refactor_navigation_assertions "$file"
            refactor_auth_assertions "$file"
            print_status "Automatic refactoring completed for $file"
            ;;
        "suggest")
            show_suggestions "$file"
            ;;
        *)
            print_error "Invalid mode: $mode"
            return 1
            ;;
    esac
}

# Function to validate refactored files
validate_file() {
    local file="$1"
    print_info "Validating $file"
    
    # Check for syntax issues (basic validation)
    if ! grep -q "appId:" "$file"; then
        print_warning "$file may be missing appId declaration"
    fi
    
    # Check for proper flow references
    if grep -q "runFlow:.*\.yaml" "$file"; then
        print_status "$file uses modular components"
    fi
    
    print_status "Validation completed for $file"
}

# Show usage
show_usage() {
    echo "Usage: $0 [command] [options]"
    echo ""
    echo "Commands:"
    echo "  suggest [file]     - Show refactoring suggestions for a file"
    echo "  auto [file]        - Automatically refactor a file (with backup)"
    echo "  validate [file]    - Validate a refactored file"
    echo "  list              - List files that need refactoring"
    echo "  status            - Show refactoring status"
    echo ""
    echo "Examples:"
    echo "  $0 suggest testing/maestro/android/authentication/01_pre_login_state.yaml"
    echo "  $0 auto testing/maestro/android/authentication/01_pre_login_state.yaml"
    echo "  $0 list"
    echo "  $0 status"
}

# List files that need refactoring
list_files() {
    echo ""
    print_info "Files that may need refactoring:"
    echo ""
    
    # Find files with navigation assertions
    echo "📁 Files with navigation assertions:"
    find testing/maestro/android -name "*.yaml" -exec grep -l "assertVisible.*Tab.*of 5" {} \; | while read file; do
        echo "  - $file"
    done
    
    echo ""
    echo "📁 Files with authentication assertions:"
    find testing/maestro/android -name "*.yaml" -exec grep -l "assertVisible.*Sign in" {} \; | while read file; do
        echo "  - $file"
    done
    
    echo ""
    echo "📁 Files using app_launch flow:"
    find testing/maestro/android -name "*.yaml" -exec grep -l "runFlow.*app_launch" {} \; | while read file; do
        echo "  - $file"
    done
}

# Show refactoring status
show_status() {
    echo ""
    print_info "Refactoring Status:"
    echo ""
    
    # Count files with old patterns
    nav_files=$(find testing/maestro/android -name "*.yaml" -exec grep -l "assertVisible.*Tab.*of 5" {} \; | wc -l)
    auth_files=$(find testing/maestro/android -name "*.yaml" -exec grep -l "assertVisible.*Sign in" {} \; | wc -l)
    modular_files=$(find testing/maestro/android -name "*.yaml" -exec grep -l "runFlow:.*common/" {} \; | wc -l)
    
    echo "📊 Files with old navigation patterns: $nav_files"
    echo "📊 Files with old auth patterns: $auth_files"
    echo "📊 Files using modular components: $modular_files"
    echo ""
    
    if [ "$nav_files" -eq 0 ] && [ "$auth_files" -eq 0 ]; then
        print_status "All files have been refactored! 🎉"
    else
        print_warning "Refactoring in progress..."
    fi
}

# Main script logic
case "$1" in
    "suggest")
        if [ -z "$2" ]; then
            print_error "Please specify a file to analyze"
            show_usage
            exit 1
        fi
        refactor_file "$2" "suggest"
        ;;
    "auto")
        if [ -z "$2" ]; then
            print_error "Please specify a file to refactor"
            show_usage
            exit 1
        fi
        refactor_file "$2" "auto"
        ;;
    "validate")
        if [ -z "$2" ]; then
            print_error "Please specify a file to validate"
            show_usage
            exit 1
        fi
        validate_file "$2"
        ;;
    "list")
        list_files
        ;;
    "status")
        show_status
        ;;
    *)
        show_usage
        ;;
esac
