# Promz Admin Development Helper Script for Windows
Set-StrictMode -Version Latest
$ErrorActionPreference = "Stop"

#region Colors for better readability
$GREEN = "`e[0;32m"
$YELLOW = "`e[0;33m"
$RED = "`e[0;31m"
$BLUE = "`e[0;34m"
$NC = "`e[0m" # No Color
#endregion

# Store the root directory for reference
$rootDir = Split-Path $PSScriptRoot
$adminDir = Join-Path $rootDir "admin"

#region Flutter Helper Functions
function Test-Flutter {
    if (!(Get-Command flutter -ErrorAction SilentlyContinue)) {
        Write-Host "${RED}❌ Flutter is not installed or not in PATH!${NC}"
        Write-Host "Please install Flutter from https://flutter.dev/docs/get-started/install"
        return $false
    }
    Write-Host "${GREEN}✅ Flutter is installed${NC}"
    flutter --version
    return $true
}

function Test-Go {
    if (!(Get-Command go -ErrorAction SilentlyContinue)) {
        Write-Host "${YELLOW}⚠️ Go is not installed or not in PATH!${NC}"
        Write-Host "The API server requires Go. Install from https://golang.org/doc/install"
        return $false
    }
    Write-Host "${GREEN}✅ Go is installed${NC}"
    go version
    return $true
}

function Needs-CodeGeneration {
    # Check for presence of *.g.dart or @JsonSerializable in the codebase
    $gFiles = @(Get-ChildItem -Path $adminDir\lib -Recurse -Include *.g.dart -ErrorAction SilentlyContinue)
    if ($gFiles.Count -gt 0) { return $true }
    $jsonSerializable = Get-ChildItem -Path $adminDir\lib -Recurse -Include *.dart | Select-String -Pattern '@JsonSerializable' -SimpleMatch
    if ($jsonSerializable) { return $true }
    return $false
}

function Invoke-GenerateCode {
    if (-not (Needs-CodeGeneration)) {
        Write-Host "${YELLOW}⚠️ No code generation needed (no .g.dart files or @JsonSerializable found). Skipping build_runner.${NC}"
        return $true
    }
    Write-Host "${BLUE}🔄 Running build_runner to generate model code and JSON serialization...${NC}"
    $originalLocation = Get-Location
    Set-Location $adminDir
    dart run build_runner build --delete-conflicting-outputs
    $result = $LASTEXITCODE -eq 0
    Set-Location $originalLocation
    if ($result) {
        Write-Host "${GREEN}✅ Code generation completed successfully!${NC}"
        return $true
    } else {
        Write-Host "${RED}❌ Code generation failed!${NC}"
        return $false
    }
}

function Invoke-CleanAndGet {
    Write-Host "${BLUE}🧹 Cleaning Flutter project...${NC}"
    
    # Save the current location to restore it later
    $originalLocation = Get-Location
    
    # Change to the project root directory where Flutter project is located
    Set-Location $adminDir
    
    flutter clean
    Write-Host "${BLUE}📦 Getting dependencies...${NC}"
    flutter pub get
    $result = $LASTEXITCODE -eq 0
    
    # Restore the original location
    Set-Location $originalLocation
    
    if ($result) {
        Write-Host "${GREEN}✅ Clean and get completed successfully!${NC}"
        return $true
    } else {
        Write-Host "${RED}❌ Clean and get failed!${NC}"
        return $false
    }
}

function Invoke-Tests {
    Write-Host "${BLUE}🧪 Running Flutter tests...${NC}"
    
    # Save the current location to restore it later
    $originalLocation = Get-Location
    
    # Change to the project root directory where Flutter project is located
    Set-Location $adminDir
    
    flutter test
    $result = $LASTEXITCODE -eq 0
    
    # Restore the original location
    Set-Location $originalLocation
    
    if ($result) {
        Write-Host "${GREEN}✅ All tests passed!${NC}"
        return $true
    } else {
        Write-Host "${RED}❌ Some tests failed!${NC}"
        return $false
    }
}

function Invoke-WebApp {
    Write-Host "${BLUE}🚀 Starting local development environment...${NC}"
    
    # Save the current location to restore it later
    $originalLocation = Get-Location
    
    # Change to the project root directory where Flutter project is located
    Set-Location $adminDir
    
    # First run tests to ensure everything is working
    Write-Host "${BLUE}🧪 Running tests before starting app...${NC}"
    Invoke-Tests
    
    # Generate code if needed
    Write-Host "${BLUE}⚙️ Ensuring code generation is up-to-date...${NC}"
    Invoke-GenerateCode
    
    # Start the web app
    Write-Host "${BLUE}🚀 Starting Flutter web application in Chrome...${NC}"
    $env:FLUTTER_BASE_HREF="/"
    flutter run -d chrome
    
    # Restore the original location
    Set-Location $originalLocation
}

function Invoke-BuildWeb {
    Write-Host "${BLUE}🏗️ Building Flutter web application for production...${NC}"
    
    # Save the current location to restore it later
    $originalLocation = Get-Location
    
    # Change to the project root directory where Flutter project is located
    Set-Location $adminDir
    
    if (!(Invoke-GenerateCode)) {
        Write-Host "${RED}❌ Code generation failed, cannot build web!${NC}"
        
        # Restore the original location
        Set-Location $originalLocation
        return $false
    }

    $treeShakeFlag = ""
    $disableTreeShake = Read-Host "Disable tree shaking for icons? (y/n)"
    if ($disableTreeShake -eq "y") {
        $treeShakeFlag = "--no-tree-shake-icons"
    }

    flutter build web --release $treeShakeFlag
    $result = $LASTEXITCODE -eq 0
    
    # Restore the original location
    Set-Location $originalLocation
    
    if ($result) {
        Write-Host "${GREEN}✅ Web application built successfully!${NC}"
        Write-Host "${GREEN}Output is available in build/web directory${NC}"
        return $true
    } else {
        Write-Host "${RED}❌ Web application build failed!${NC}"
        return $false
    }
}

function Invoke-AnalyzeCode {
    Write-Host "${BLUE}🔍 Analyzing Flutter code...${NC}"
    
    # Save the current location to restore it later
    $originalLocation = Get-Location
    
    # Change to the project root directory where Flutter project is located
    Set-Location $adminDir
    
    flutter analyze
    $result = $LASTEXITCODE -eq 0
    
    # Restore the original location
    Set-Location $originalLocation
    
    if ($result) {
        Write-Host "${GREEN}✅ No issues found!${NC}"
        return $true
    } else {
        Write-Host "${YELLOW}⚠️ Some issues were found!${NC}"
        return $false
    }
}

function Test-Environment {
    Write-Host "${BLUE}🔍 Checking development environment...${NC}"
    
    # Save the current location to restore it later
    $originalLocation = Get-Location
    
    # Change to the project root directory where Flutter project is located
    Set-Location $adminDir
    
    if (!(Test-Flutter)) {
        # Restore original location before returning
        Set-Location $originalLocation
        return
    }
    if (!(Test-Go)) {
        # Restore original location before returning
        Set-Location $originalLocation
        return
    }
    
    Write-Host "${BLUE}📦 Checking Flutter packages...${NC}"
    flutter pub get
    
    Write-Host "`n${GREEN}✅ Environment check completed!${NC}"
    
    # Restore the original location
    Set-Location $originalLocation
    
    Read-Host "Press Enter to continue..."
}
#endregion

function Show-AdminMenu {
    while ($true) {
        Clear-Host
        Write-Host "==============================="
        Write-Host "  🚀 Promz Admin Dev Helper 🚀"
        Write-Host "==============================="
        Write-Host "[1] Clean project and get dependencies"
        Write-Host "[2] Run local development environment (with tests)"
        Write-Host "[3] Build web for production"
        Write-Host "[4] Analyze code"
        Write-Host "[5] Check environment"
        Write-Host "[0] Exit"
        Write-Host "==============================="
        
        $choice = Read-Host "Choose an option (0-5)"

        switch ($choice) {
            '1' { Invoke-CleanAndGet; Read-Host "Press Enter to continue..." }
            '2' { Invoke-WebApp }
            '3' { Invoke-BuildWeb; Read-Host "Press Enter to continue..." }
            '4' { Invoke-AnalyzeCode; Read-Host "Press Enter to continue..." }
            '5' { Test-Environment }
            '0' { Write-Host "${GREEN}👋 Goodbye!${NC}"; exit }
            default { Write-Host "${RED}❌ Invalid choice${NC}"; Read-Host "Press Enter to continue..." }
        }
    }
}

# Main execution
Show-AdminMenu
