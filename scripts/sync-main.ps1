# Import shared git helper functions
Import-Module "$PSScriptRoot\Git-SyncHelpers.psm1"

# Navigate to the repository root
Set-Location "C:\repo\litra"

# Initialize submodules if not already initialized
git submodule update --init --recursive

# Check for pending changes and pushes in main repo
Write-Host "Checking for pending changes and pushes..."
if (-not (Test-UnpushedCommits)) {
    exit 1
}

# Get the list of submodules
$submodules = git config --file .gitmodules --get-regexp path | ForEach-Object { $_ -split " " | Select-Object -Last 1 }

foreach ($submodule in $submodules) {
    Write-Host "`nProcessing submodule: $submodule"
    
    # Navigate into the submodule
    Set-Location $submodule

    # Fetch all updates
    git fetch --all

    # Check for unpushed commits in submodule
    if (-not (Test-UnpushedCommits -RepoName $submodule)) {
        exit 1
    }

    # Switch to main branch and pull latest changes
    git checkout main
    git pull origin main

    # Navigate back to the root directory
    Set-Location ..
}

Write-Host "All submodules synced to latest main branch."
