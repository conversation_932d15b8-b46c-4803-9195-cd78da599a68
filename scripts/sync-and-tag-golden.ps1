# Import shared git helper functions
Import-Module "$PSScriptRoot\Git-SyncHelpers.psm1"

# Navigate to the repository root
Set-Location "C:\projects\promz"

# Initialize submodules if not already initialized
git submodule update --init --recursive

# Check for pending changes and pushes in main repo
Write-Host "Checking for pending changes and pushes..."
if (-not (Test-UnpushedCommits)) {
    exit 1
}

$gitStatus = git status
$hasUnstagedChanges = $gitStatus -match "Changes not staged for commit:"
$hasUntrackedFiles = $gitStatus -match "Untracked files:"
$hasSubmoduleChanges = $gitStatus -match "modified:.*(new commits)"

if ($hasUnstagedChanges -or $hasUntrackedFiles -or $hasSubmoduleChanges) {
    Write-Host "`nPending changes detected:"
    if ($hasSubmoduleChanges) {
        Write-Host "- Submodule changes detected"
        $confirmation = Read-Host "Do you want to commit submodule changes? (y/n)"
        if ($confirmation -eq 'y') {
            git add -A
            git commit -m "Update submodule references"
            git push
            if ($LASTEXITCODE -ne 0) {
                Write-Host "Failed to push submodule changes. Please resolve and try again."
                exit 1
            }
        } else {
            Write-Host "Submodule changes not committed. Aborting script."
            exit 1
        }
    }
    if ($hasUntrackedFiles) {
        Write-Host "- Untracked files detected"
        Write-Host "Please add and commit untracked files before proceeding"
        exit 1
    }
    if ($hasUnstagedChanges -and -not $hasSubmoduleChanges) {
        Write-Host "- Unstaged changes detected"
        Write-Host "Please commit or stash changes before proceeding"
        exit 1
    }
}

# Confirm with user before proceeding with tests and tagging
$proceedConfirmation = Read-Host "`nReady to run tests and create/update golden-build tags. Proceed? (y/n)"
if ($proceedConfirmation -ne 'y') {
    Write-Host "Operation cancelled by user."
    exit 0
}

# Run Flutter tests in the client subdirectory
Set-Location "C:\projects\promz\client"
Write-Host "`nRunning Flutter tests..."
flutter test
if ($LASTEXITCODE -ne 0) {
    Write-Host "Flutter tests failed. Aborting script."
    exit 1
}

# Run Go tests in the api subdirectory
Set-Location "C:\projects\promz\api"
Write-Host "`nRunning Go tests..."
go test ./...
if ($LASTEXITCODE -ne 0) {
    Write-Host "Go tests failed. Aborting script."
    exit 1
}

# Navigate back to the repository root
Set-Location "C:\projects\promz"

# Get the list of submodules
$submodules = git config --file .gitmodules --get-regexp path | ForEach-Object { $_ -split " " | Select-Object -Last 1 }

foreach ($submodule in $submodules) {
    Write-Host "`nProcessing submodule: $submodule"

    # Navigate into the submodule
    Set-Location $submodule

    # Fetch all updates
    git fetch --all

    # Check for unpushed commits in submodule
    if (-not (Test-UnpushedCommits -RepoName $submodule)) {
        exit 1
    }

    # Switch to main branch and pull latest changes
    Write-Host "Updating $submodule to latest main..."
    git checkout main
    git pull origin main
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Failed to update $submodule to latest main. Aborting for safety."
        exit 1
    }

    # Double check submodule state
    $subStatus = git status
    if ($subStatus -match "Changes not staged for commit:" -or $subStatus -match "Untracked files:") {
        Write-Host "Warning: Unexpected changes found in $submodule after update."
        Write-Host "Please check the state of $submodule manually."
        exit 1
    }

    # Check if golden-build tag exists
    $tagExists = git tag -l "golden-build"
    if ($tagExists) {
        $confirmation = Read-Host "golden-build tag already exists in $submodule. Do you want to replace it? (y/n)"
        if ($confirmation -eq 'y') {
            # Show diff between current main and existing golden-build
            Write-Host "Showing diff between current main and existing golden-build:"
            git diff golden-build..main --stat
            $finalConfirm = Read-Host "Confirm replacing golden-build tag with current main? (y/n)"
            if ($finalConfirm -eq 'y') {
                git tag -d golden-build
                git push origin :refs/tags/golden-build
            } else {
                Write-Host "Skipping $submodule tag update"
                Set-Location ..
                continue
            }
        } else {
            Write-Host "Skipping $submodule tag update"
            Set-Location ..
            continue
        }
    }

    # Tag the current commit as golden-build
    Write-Host "Creating golden-build tag for $submodule..."
    git tag -a golden-build -m "Marking as golden-build"
    git push origin golden-build
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Failed to push golden-build tag for $submodule. Please resolve and try again."
        exit 1
    }

    # Navigate back to the root directory
    Set-Location ..
}

# Check if root repo golden-build tag exists
$rootTagExists = git tag -l "golden-build"
if ($rootTagExists) {
    Write-Host "`nShowing diff between current main and existing golden-build for root repository:"
    git diff golden-build..main --stat
    $confirmation = Read-Host "golden-build tag already exists in root repository. Do you want to replace it? (y/n)"
    if ($confirmation -eq 'y') {
        $finalConfirm = Read-Host "Confirm replacing root repository golden-build tag with current main? (y/n)"
        if ($finalConfirm -eq 'y') {
            git tag -d golden-build
            git push origin :refs/tags/golden-build
            
            # Tag the main repository
            Write-Host "Tagging the main repository as golden-build"
            git tag -a golden-build -m "Marking as golden-build"
            git push origin golden-build
            if ($LASTEXITCODE -ne 0) {
                Write-Host "Failed to push golden-build tag for root repository. Please resolve manually."
                exit 1
            }
        } else {
            Write-Host "Skipping root repository tag update"
        }
    } else {
        Write-Host "Skipping root repository tag update"
    }
} else {
    # Tag the main repository
    Write-Host "`nTagging the main repository as golden-build"
    git tag -a golden-build -m "Marking as golden-build"
    git push origin golden-build
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Failed to push golden-build tag for root repository. Please resolve manually."
        exit 1
    }
}

Write-Host "`nAll submodules and main repository processing complete."
Write-Host "Please verify the state of all repositories and tags manually before proceeding with your work."

