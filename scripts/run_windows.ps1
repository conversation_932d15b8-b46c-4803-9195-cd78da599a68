# Windows Client Script for Promz Windows Build
Set-StrictMode -Version Latest
$ErrorActionPreference = "Stop"

# Import module that may be needed for execution policy
Import-Module Microsoft.PowerShell.Security

# Get the script path and navigate to the client directory
$clientDir = Join-Path (Split-Path $PSScriptRoot -Parent) "client"
Push-Location $clientDir

try {
    # Function to ensure proper execution policy
    function Set-ScriptExecutionPolicy {
        try {
            $currentPolicy = Get-ExecutionPolicy
            if ($currentPolicy -eq 'Restricted') {
                Write-Host "Current execution policy is Restricted. Setting temporary policy for this session..."
                Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process -Force
                Write-Host "Successfully set execution policy to Bypass for this session."
            } else {
                Write-Host "Current execution policy is $currentPolicy. No changes needed."
            }
        } catch {
            Write-Host "Warning: Unable to modify execution policy. Error: $_"
            Write-Host "You may need to run 'Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser' manually."
            exit 1
        }
    }

    # Set execution policy before proceeding
    Set-ScriptExecutionPolicy

    # Function to ensure SQLite DLLs are present
    function Install-SqliteDlls {
        $sqliteDllPath = Join-Path $clientDir "sqlite3.dll"

        if (Test-Path $sqliteDllPath) {
            Write-Host "SQLite DLLs already present."
            return $true
        }

        Write-Host "Downloading SQLite DLLs..."
        $tempFile = Join-Path $env:TEMP "sqlite.zip"

        try {
            # Download the ZIP file - Use a consistent, recent version
            # Note: Update this URL if a newer stable version is needed
            $sqliteUrl = "https://www.sqlite.org/2025/sqlite-dll-win-x64-3490000.zip" # Ensure this URL is valid or update it
            Invoke-WebRequest -Uri $sqliteUrl -OutFile $tempFile

            # Extract the DLL
            Add-Type -AssemblyName System.IO.Compression.FileSystem
            $zip = [System.IO.Compression.ZipFile]::OpenRead($tempFile)
            $dllEntry = $zip.Entries | Where-Object { $_.Name -eq "sqlite3.dll" }

            if ($null -eq $dllEntry) {
                Write-Host "Error: sqlite3.dll not found in the downloaded archive."
                return $false
            }

            # Extract the DLL to the client directory
            [System.IO.Compression.ZipFileExtensions]::ExtractToFile($dllEntry, $sqliteDllPath, $true)
            $zip.Dispose()

            Write-Host "SQLite DLLs installed successfully."
            return $true
        }
        catch {
            Write-Host "Error installing SQLite DLLs: $_"
            return $false
        }
        finally {
            # Cleanup
            if (Test-Path $tempFile) {
                Remove-Item $tempFile -Force
            }
        }
    }

    # Function to reset and rebuild the project
    function Reset-Build {
        Write-Host "Cleaning up previous build..."
        flutter clean 2>&1
        flutter pub get 2>&1

        # Optional: Attempt to uninstall previous Windows installation if needed
        # This is complex and might require admin rights or specific app registration knowledge.
        # Write-Host "Note: Manual uninstallation of the previous Windows app version might be required."

        Write-Host "Rebuilding project dependencies..."
        dart run build_runner build --delete-conflicting-outputs 2>&1
    }

    function Stop-FlutterTester {
        Get-Process | Where-Object { $_.Name -eq "flutter_tester" } | Stop-Process -Force -ErrorAction SilentlyContinue
    }

    # Function to rebuild the database schema (runs build_runner)
    function Update-Database {
        Write-Host "Updating generated code (including database schema)..."
        dart run build_runner build --delete-conflicting-outputs 2>&1
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Build runner failed."
            exit 1
        }
        Write-Host "Generated code updated successfully."
    }

    # Function to run tests
    function Test-Tests {
        Stop-FlutterTester
        Write-Host "Running tests..."
        flutter test 2>&1
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Tests failed. Aborting." -ForegroundColor Red
            exit 1
        }
        Write-Host "Tests passed." -ForegroundColor Green
        Stop-FlutterTester
    }

    # Function to run benchmarks
    function Test-Benchmarks {
        Write-Host "Running benchmarks..."
        # Update the path if your benchmark tests are located elsewhere
        flutter test --tags benchmark test/benchmark/keyword_extraction_benchmark.dart 2>&1
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Benchmarks failed." -ForegroundColor Red
            return $false
        }
        Write-Host "Benchmarks completed successfully." -ForegroundColor Green
        return $true
    }

    # Function to build the release Windows app
    function Build-Release {
        Write-Host "Building release Windows executable..."
        flutter build windows --release 2>&1
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Release build failed." -ForegroundColor Red
            exit 1
        }
        Write-Host "Release Windows executable built successfully." -ForegroundColor Green
        Write-Host "Output located in: build\windows\runner\Release"
    }

    # Function to build the debug Windows app
    function Build-Debug {
        Write-Host "Building debug Windows executable..."
        flutter build windows --debug 2>&1
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Debug build failed." -ForegroundColor Red
            exit 1
        }
        Write-Host "Debug Windows executable built successfully." -ForegroundColor Green
        Write-Host "Output located in: build\windows\runner\Debug"
    }

    # Function to run the built debug executable
    function Run-Debug {
        $exePath = Join-Path $clientDir "build\windows\x64\runner\Debug\promz.exe"
        if (-not (Test-Path $exePath)) {
            Write-Host "Debug executable not found at $exePath. Please build it first (Option 5 or 6)." -ForegroundColor Yellow
            return
        }
        Write-Host "Running debug build: $exePath"
        Start-Process -FilePath $exePath
    }

    # Function to run the built release executable
    function Run-Release {
        $exePath = Join-Path $clientDir "build\windows\x64\runner\Release\promz.exe"
        if (-not (Test-Path $exePath)) {
            Write-Host "Release executable not found at $exePath. Please build it first (Option 7 or 8)." -ForegroundColor Yellow
            return
        }
        Write-Host "Running release build: $exePath"
        Start-Process -FilePath $exePath
    }


    # Function to show the menu and handle user choices
    function Show-Menu {
        while ($true) {
            Write-Host "`nFlutter Windows Build and Run Script" -ForegroundColor Cyan
            Write-Host "0. Exit"
            Write-Host "1. Update Generated Code (Database Schema, etc.)"
            Write-Host "2. Run Benchmarks"
            Write-Host "3. Run Tests"
            Write-Host "4. Clean and Rebuild Dependencies"
            Write-Host "--- Build ---"
            Write-Host "5. [+ Tests] Build Debug Executable"
            Write-Host "6. [- Tests] Build Debug Executable"
            Write-Host "7. [+ Tests + Benchmarks] Build Release Executable"
            Write-Host "8. [- Tests - Benchmarks] Build Release Executable"
            Write-Host "--- Run ---"
            Write-Host "9. Run Debug Build (if exists)"
            Write-Host "10. Run Release Build (if exists)"

            $choice = Read-Host "Enter your choice (0-10)"

            switch ($choice) {
                '0' {
                    Write-Host "Exiting script."
                    exit
                }
                '1' {
                    Update-Database
                    break
                }
                '2' {
                    Test-Benchmarks
                    break
                }
                '3' {
                    Test-Tests
                    break
                }
                 '4' {
                    Reset-Build
                    break
                }
                '5' {
                    Test-Tests
                    Build-Debug
                    break
                }
                '6' {
                    Build-Debug
                    break
                }
                '7' {
                    Test-Tests
                    if (Test-Benchmarks) {
                        Build-Release
                    } else {
                        Write-Host "Benchmarks failed. Aborting release build." -ForegroundColor Yellow
                    }
                    break
                }
                '8' {
                    Build-Release
                    break
                }
                '9' {
                    Run-Debug
                    break
                }
                '10' {
                    Run-Release
                    break
                }
                default {
                    Write-Warning "Invalid choice. Please try again."
                }
            }
        }
    }

    # Main script execution
    if (-not (Install-SqliteDlls)) {
        Write-Host "Failed to install SQLite DLLs. Build or runtime might fail." -ForegroundColor Yellow
    }

    # Show the menu
    Show-Menu
}
finally {
    # Restore original location
    Pop-Location
}
