# Promz LLM Pricing Tool Menu Script

# Set strict mode and error handling
Set-StrictMode -Version Latest
$ErrorActionPreference = "Stop"

# Get script location and resolve project root
$ScriptPath = $MyInvocation.MyCommand.Path
$ScriptDir = Split-Path $ScriptPath -Parent
$ProjectRoot = Split-Path $ScriptDir -Parent

# Paths
$UtilsScriptPath = Join-Path $ProjectRoot "scripts\script_utils.ps1"
$PricingToolDir = Join-Path $ProjectRoot "tools\llm\pricing"
$OutputJson = Join-Path $PricingToolDir "output\current_llm_pricing.json"

# Source Supabase credentials
if (-not (Test-Path $UtilsScriptPath -PathType Leaf)) {
    Write-Error "Could not find script_utils.ps1 at '$UtilsScriptPath'. Please verify the path."
    exit 1
}
try {
    Write-Host "Sourcing utilities from '$UtilsScriptPath'..."
    . $UtilsScriptPath
} catch {
    Write-Error "Failed to source '$UtilsScriptPath': $_"
    exit 1
}

# Check for required env vars
if (-not ($env:PROMZ_SUPABASE_URL)) {
    Write-Error "PROMZ_SUPABASE_URL environment variable not set after sourcing script_utils.ps1."
    exit 1
}
if (-not ($env:PROMZ_SUPABASE_KEY)) {
    Write-Error "PROMZ_SUPABASE_KEY environment variable not set after sourcing script_utils.ps1."
    exit 1
}
$env:SUPABASE_URL = $env:PROMZ_SUPABASE_URL
$env:SUPABASE_SERVICE_KEY = $env:PROMZ_SUPABASE_KEY
Write-Host "Supabase credentials loaded."

function Build-PricingTool {
    Push-Location $PricingToolDir
    Write-Host "Running npm run build..."
    npm run build
    if ($LASTEXITCODE -ne 0) {
        Write-Host "npm run build failed."
        Pop-Location
        return $false
    }
    Pop-Location
    return $true
}

function Invoke-Seeder {
    param([switch]$WithBuild)
    Push-Location $PricingToolDir
    if ($WithBuild) {
        if (-not (Build-PricingTool)) { Pop-Location; return }
    }
    Write-Host "Running npm run seed..."
    npm run seed
    if ($LASTEXITCODE -ne 0) {
        Write-Host "npm run seed failed."
        Pop-Location
        return
    }
    Write-Host "Seeder script completed successfully."
    Pop-Location
}

function Open-OutputFile {
    if (Test-Path $OutputJson) {
        Write-Host "Opening output/current_llm_pricing.json..."
        Invoke-Item $OutputJson
    } else {
        Write-Host "Output file not found: $OutputJson"
    }
}

function Show-Menu {
    while ($true) {
        Write-Host "`n--- LLM Pricing Tool Menu ---" -ForegroundColor Cyan
        Write-Host "0. Exit"
        Write-Host "1. Build pricing tool"
        Write-Host "2. Run seeder (no build)"
        Write-Host "3. Run seeder (with build)"
        Write-Host "4. Open output/current_llm_pricing.json"
        $choice = Read-Host "Enter your choice (0-4)"
        switch ($choice) {
            '0' { Write-Host "Exiting script."; exit }
            '1' { Build-PricingTool }
            '2' { Invoke-Seeder }
            '3' { Invoke-Seeder -WithBuild }
            '4' { Open-OutputFile }
            default { Write-Warning "Invalid choice. Please try again." }
        }
    }
}

Show-Menu
