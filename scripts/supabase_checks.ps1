# Source the environment variables
. "$PSScriptRoot\script_utils.ps1"

# Supabase credentials
$SupabaseUrl = $env:PROMZ_SUPABASE_URL
$SupabaseKey = $env:PROMZ_SUPABASE_KEY

if (-not $SupabaseUrl -or -not $SupabaseKey) {
    Write-Host "❌ ERROR: PROMZ_SUPABASE_URL and PROMZ_SUPABASE_KEY environment variables must be set!" -ForegroundColor Red
    exit
}

# Function to extract database connection details
function Get-DatabaseConnectionDetails {
    # Extract database connection details from DB_CONNECT env variable
    $DbConnect = $env:PROMZ_DB_CONNECTION

    if (-not $DbConnect) {
        Write-Host "❌ ERROR: PROMZ_DB_CONNECTION environment variable is not set!" -ForegroundColor Red
        return $null
    }

    # Parse the connection string
    if ($DbConnect -match "postgresql://(?<User>.+):(?<Password>.+)@(?<Host>.+):(?<Port>\d+)/(?<Database>.+)") {
        $User = $matches.User
        $Password = $matches.Password
        $DbHost = $matches.Host
        $Port = [int]$matches.Port
        $Database = $matches.Database
    } else {
        Write-Host "❌ ERROR: Invalid PROMZ_DB_CONNECTION format!" -ForegroundColor Red
        return $null
    }

    return @{
        User     = $User
        Password = $Password
        Host     = $DbHost
        Port     = $Port
        Database = $Database
    }
}

# Extract DB connection details
$dbDetails = Get-DatabaseConnectionDetails
if (-not $dbDetails) {
    exit
}

$User = $dbDetails.User
$Password = $dbDetails.Password
$DbHost = $dbDetails.Host
$Port = $dbDetails.Port
$Database = $dbDetails.Database

Write-Host "🔍 Extracted DB Connection Details:"
Write-Host "   📌 Host: $DbHost"
Write-Host "   📌 Port: $Port"
Write-Host "   📌 Database: $Database"
Write-Host "   📌 User: $User"
Write-Host "✅ Database Connection Details Check Passed!" -ForegroundColor Green

# Function to resolve IPv4 Address
function Resolve-IPv4Address {
    param (
        [string]$DbHost
    )
    Write-Host "🔍 Resolving IPv4 address for $DbHost..."
    try {
        $ipList = (Resolve-DnsName $DbHost -Type A -ErrorAction Stop).IPAddress -join ", "
        Write-Host "✅ Supabase IPv4 Address: $ipList"
    } catch {
        Write-Host "❌ ERROR: Failed to resolve IPv4 address for $DbHost. Check your DNS settings." -ForegroundColor Red
        return $false
    }
    return $true
}

# Function to test Network Connectivity
function Test-NetworkConnectivity {
    param (
        [string]$DbHost,
        [int]$Port
    )
    Write-Host "🔍 Testing network connectivity..."
    $test = Test-NetConnection -ComputerName $DbHost -Port $Port
    if ($test.TcpTestSucceeded) {
        Write-Host "✅ PostgreSQL port $Port is reachable!" -ForegroundColor Green
    } else {
        Write-Host "❌ PostgreSQL port $Port is blocked!" -ForegroundColor Red
        return $false
    }
    return $true
}

# Function to attempt PostgreSQL Connection
function Test-PostgreSQLConnection {
    param (
        [string]$DbHost,
        [int]$Port,
        [string]$User,
        [SecureString]$Password,
        [string]$Database
    )
    Write-Host "🔍 Attempting PostgreSQL connection..."
    
    # Convert SecureString to plain text (use with caution)
    $PasswordBSTR = [System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($Password)
    $PlainTextPassword = [System.Runtime.InteropServices.Marshal]::PtrToStringAuto($PasswordBSTR)
    
    try {
        $env:PGPASSWORD = $PlainTextPassword
        $psqlCommand = "psql -h $DbHost -p $Port -U $User -d $Database"
        Invoke-Expression $psqlCommand
    }
    finally {
        # Securely clear the memory used by the plain text password
        [System.Runtime.InteropServices.Marshal]::ZeroFreeBSTR($PasswordBSTR)
        Remove-Variable -Name PlainTextPassword
    }
}

# Function to fetch tables from Supabase database
function Get-SupabaseTables {
    # Construct the Supabase client
    try {
        Add-Type -AssemblyName System.Net.Http

        $supabase = New-Object -TypeName System.Net.Http.HttpClient
        $supabase.BaseAddress = New-Object Uri($SupabaseUrl)
        $supabase.DefaultRequestHeaders.Add("apikey", $SupabaseKey)
        $supabase.DefaultRequestHeaders.Add("Authorization", "Bearer $SupabaseKey")

        # Construct a request to fetch the list of tables from the Supabase database
        try {
            # Construct the request
            $url = "/rest/v1/"
            $request = New-Object -TypeName System.Net.Http.HttpRequestMessage
            $request.Method = [System.Net.Http.HttpMethod]::Get
            $request.RequestUri = New-Object Uri($url, [UriKind]::Relative)
            $request.Headers.Accept.Add([System.Net.Http.Headers.MediaTypeWithQualityHeaderValue]::Parse("application/json"))

            # Send the request
            $response = $supabase.SendAsync($request).GetAwaiter().GetResult()
            $response.EnsureSuccessStatusCode()

            # Parse the response
            $content = $response.Content.ReadAsStringAsync().GetAwaiter().GetResult()
            $apiSpec = ConvertFrom-Json $content

            Write-Host "✅ Successfully fetched tables from Supabase database:" -ForegroundColor Green

            # Extract table names from paths
            $tableNames = @()
            foreach ($path in $apiSpec.paths.PSObject.Properties) {
                if ($path.Name -notlike "/" -and $path.Name -notlike "/rpc/*") {
                    $tableNames += $path.Name.TrimStart("/")
                }
            }

            foreach ($tableName in $tableNames) {
                Write-Host "   📋 $tableName" -ForegroundColor Green
            }
        } catch {
            Write-Host "❌ ERROR: Failed to fetch tables from Supabase database: $_" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ ERROR: Failed to create Supabase client: $_" -ForegroundColor Red
        return $false
    }
    return $true
}

# Function to test a Supabase Vault API key
function Test-SupabaseVaultApiKey {
    param (
        [string]$apiKey
    )

    Write-Host "`n🔍 Testing API key: $('*' * [Math]::Min(5, $apiKey.Length))..." -ForegroundColor Cyan
    
    # Construct the request URL
    $url = "$SupabaseUrl/rest/v1/rpc/verify_api_key"
    
    # Prepare the request body
    $body = @{
        p_api_key = $apiKey
    } | ConvertTo-Json
    
    # Prepare headers
    $headers = @{
        "apikey" = $SupabaseKey
        "Authorization" = "Bearer $SupabaseKey"
        "Content-Type" = "application/json"
        "Prefer" = "return=representation"
    }
    
    Write-Host "Request URL: $url" -ForegroundColor DarkGray
    Write-Host "Headers:" -ForegroundColor DarkGray
    $headers.Keys | ForEach-Object {
        Write-Host "  $_ = $($_ -eq 'Authorization' -or $_ -eq 'apikey' ? '****' : $headers[$_])" -ForegroundColor DarkGray
    }
    
    try {
        $response = Invoke-RestMethod -Uri $url -Method Post -Headers $headers -Body $body -ContentType "application/json"
        Write-Host "✅ Request successful!" -ForegroundColor Green
        Write-Host "Response:" -ForegroundColor DarkGray
        $response | ConvertTo-Json | Write-Host -ForegroundColor DarkGray
        
        if ($response.valid -eq $true) {
            Write-Host "✅ API key is valid!" -ForegroundColor Green
            Write-Host "User ID: $($response.user_id)" -ForegroundColor Green
            Write-Host "License Type: $($response.license_type)" -ForegroundColor Green
            Write-Host "Expiry Date: $($response.expiry_date)" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ API key is invalid!" -ForegroundColor Red
            Write-Host "Reason: $($response.reason)" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ Request failed!" -ForegroundColor Red
        Write-Host "Error Details:" -ForegroundColor Red
        Write-Host "  Message:`n$($_.Exception.Message)" -ForegroundColor Red
        
        # Check if the function doesn't exist
        if ($_.Exception.Message -match "Could not find the function") {
            Write-Host "❌ The 'verify_api_key' function does not exist or you don't have permission to access it." -ForegroundColor Red
            Write-Host "Please ensure the function is created in the Supabase database." -ForegroundColor Red
        }
        
        return $false
    }
}

# Function to test the API server endpoint for API key verification
function Test-ApiServerVerification {
    param (
        [string]$apiKey,
        [string]$apiServerUrl = "http://localhost:8080"
    )

    Write-Host "`n🔍 Testing API key verification via API server..." -ForegroundColor Cyan
    
    # Construct the request URL
    $url = "$apiServerUrl/license/verify"
    
    # Prepare the request body
    $body = @{
        api_key = $apiKey
    } | ConvertTo-Json
    
    # Prepare headers - no auth required for this test
    $headers = @{
        "Content-Type" = "application/json"
    }
    
    Write-Host "Request URL: $url" -ForegroundColor DarkGray
    
    try {
        $response = Invoke-RestMethod -Uri $url -Method Post -Headers $headers -Body $body
        Write-Host "✅ Request successful!" -ForegroundColor Green
        Write-Host "Response:" -ForegroundColor DarkGray
        $response | ConvertTo-Json | Write-Host -ForegroundColor DarkGray
        
        if ($response.valid -eq $true) {
            Write-Host "✅ API key is valid!" -ForegroundColor Green
            if ($response.user_id) {
                Write-Host "User ID: $($response.user_id)" -ForegroundColor Green
            }
            if ($response.license_type) {
                Write-Host "License Type: $($response.license_type)" -ForegroundColor Green
            }
            if ($response.expiry_date) {
                Write-Host "Expiry Date: $($response.expiry_date)" -ForegroundColor Green
            }
            return $true
        } else {
            Write-Host "❌ API key is invalid!" -ForegroundColor Red
            Write-Host "Reason: $($response.reason)" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ Request failed!" -ForegroundColor Red
        Write-Host "Error Details:" -ForegroundColor Red
        Write-Host "  Message:`n$($_.Exception.Message)" -ForegroundColor Red
        
        Write-Host "❌ The API server might not be running or the endpoint is not available." -ForegroundColor Red
        Write-Host "Please ensure the API server is running at $apiServerUrl" -ForegroundColor Red
        
        return $false
    }
}

# Function to test the get_or_create_license RPC function
function Test-GetOrCreateLicense {
    param (
        [string]$userId = "006f85f7-ce10-4f1a-8f84-4546a890cfd1", # Valid UUID format
        [string]$licenseType = "free",
        [int]$daysValid = 36500
    )

    Write-Host "`n🔍 Testing get_or_create_license RPC function..." -ForegroundColor Cyan
    
    # Construct the request URL
    $url = "$SupabaseUrl/rest/v1/rpc/get_or_create_license"
    
    # Prepare the request body
    $body = @{
        p_user_id = $userId
        p_license_type = $licenseType
        p_days_valid = $daysValid
    } | ConvertTo-Json
    
    # Prepare headers
    $headers = @{
        "apikey" = $SupabaseKey
        "Authorization" = "Bearer $SupabaseKey"
        "Content-Type" = "application/json"
        "Prefer" = "return=representation"
    }
    
    Write-Host "Request URL: $url" -ForegroundColor DarkGray
    Write-Host "Request Body: $body" -ForegroundColor DarkGray
    Write-Host "Headers:" -ForegroundColor DarkGray
    $headers.Keys | ForEach-Object {
        $key = $_
        $value = if ($key -eq "apikey" -or $key -eq "Authorization") { "****" } else { $headers[$key] }
        Write-Host "  ${key}: $value" -ForegroundColor DarkGray
    }
    
    try {
        # Make the request
        $response = Invoke-RestMethod -Uri $url -Method Post -Headers $headers -Body $body -ErrorVariable restError
        
        # Display the response
        Write-Host "`n✅ Request successful!" -ForegroundColor Green
        Write-Host "Response:" -ForegroundColor DarkGray
        $response | ConvertTo-Json -Depth 5 | Write-Host -ForegroundColor DarkGray
        
        return $true
    }
    catch {
        Write-Host "`n❌ Request failed!" -ForegroundColor Red
        
        # Try to get more detailed error information
        if ($restError) {
            Write-Host "Error Details:" -ForegroundColor Red
            if ($restError.Message) {
                Write-Host "  Message: $($restError.Message)" -ForegroundColor Red
            }
            
            # Try to parse the error response
            try {
                $errorContent = $restError.ErrorRecord.Exception.Response.GetResponseStream()
                $reader = New-Object System.IO.StreamReader($errorContent)
                $errorResponse = $reader.ReadToEnd() | ConvertFrom-Json
                
                Write-Host "  Status Code: $($restError.ErrorRecord.Exception.Response.StatusCode)" -ForegroundColor Red
                Write-Host "  Error Response: $($errorResponse | ConvertTo-Json)" -ForegroundColor Red
            }
            catch {
                Write-Host "  Could not parse error response: $_" -ForegroundColor Red
            }
        }
        else {
            Write-Host "  $_" -ForegroundColor Red
        }
        
        # Check if the error is related to the function not existing
        if ($restError.ErrorRecord.Exception.Response.StatusCode -eq 404) {
            Write-Host "`n🔍 The RPC function 'get_or_create_license' might not exist or you don't have permission to access it." -ForegroundColor Yellow
            Write-Host "Checking available RPC functions..." -ForegroundColor Yellow
            
            # Try to list available RPC functions
            $functionsUrl = "$SupabaseUrl/rest/v1/rpc"
            try {
                $functionsResponse = Invoke-RestMethod -Uri $functionsUrl -Method Get -Headers $headers
                Write-Host "Available RPC functions:" -ForegroundColor Green
                $functionsResponse | ForEach-Object {
                    Write-Host "  $_" -ForegroundColor Green
                }
            }
            catch {
                Write-Host "Could not retrieve available RPC functions: $_" -ForegroundColor Red
            }
        }
        
        return $false
    }
}

# Function to test the complete license workflow
function Test-LicenseWorkflow {
    param (
        [string]$userId = "006f85f7-ce10-4f1a-8f84-4546a890cfd1", # Valid UUID format
        [string]$licenseType = "free"
    )

    Write-Host "`n🔍 Testing complete license workflow..." -ForegroundColor Cyan
    
    Write-Host "`nStep 1: Creating a license for user..." -ForegroundColor Cyan
    
    # Create a license using get_or_create_license
    $url = "$SupabaseUrl/rest/v1/rpc/get_or_create_license"
    
    # Prepare the request body
    $body = @{
        p_user_id = $userId
        p_license_type = $licenseType
        p_days_valid = 36500
    } | ConvertTo-Json
    
    # Prepare headers
    $headers = @{
        "apikey" = $SupabaseKey
        "Authorization" = "Bearer $SupabaseKey"
        "Content-Type" = "application/json"
        "Prefer" = "return=representation"
    }
    
    try {
        # Make the request
        $licenseResponse = Invoke-RestMethod -Uri $url -Method Post -Headers $headers -Body $body -ErrorVariable restError
        
        Write-Host "✅ License created successfully!" -ForegroundColor Green
        Write-Host "License details:" -ForegroundColor DarkGray
        $licenseResponse | ConvertTo-Json -Depth 5 | Write-Host -ForegroundColor DarkGray
        
        # Check if the license response contains an API key
        if ($licenseResponse -and $licenseResponse.api_key) {
            $apiKey = $licenseResponse.api_key
            Write-Host "`nStep 2: API key found in license response: $('*' * [Math]::Min(5, $apiKey.Length))..." -ForegroundColor Green
            
            Write-Host "`nStep 3: Verifying the API key..." -ForegroundColor Cyan
            
            # Test the API key
            $keyValid = Test-SupabaseVaultApiKey -apiKey $apiKey
            
            if ($keyValid) {
                Write-Host "`n✅ Complete license workflow test passed!" -ForegroundColor Green
                return $true
            } else {
                Write-Host "`n❌ API key verification failed." -ForegroundColor Red
                return $false
            }
        } else {
            Write-Host "`n❌ No API key found in the license response." -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "❌ Failed to create license: $_" -ForegroundColor Red
        return $false
    }
}

# Function to test the new get_popular_prompts_with_data database function
function Test-GetPopularPrompts {
    param (
        [int]$limit = 5
    )
    
    Write-Host "🔍 Testing get_popular_prompts_with_data function..." -ForegroundColor Cyan
    
    # Construct the Supabase client
    try {
        $headers = @{
            "apikey" = $SupabaseKey
            "Authorization" = "Bearer $SupabaseKey"
            "Content-Type" = "application/json"
            "Prefer" = "return=representation"
        }
        
        # Call the RPC function
        $url = "$SupabaseUrl/rest/v1/rpc/get_popular_prompts_with_data"
        $body = @{
            "p_limit" = $limit
        } | ConvertTo-Json
        
        $response = Invoke-RestMethod -Uri $url -Method Post -Headers $headers -Body $body
        
        if ($response) {
            Write-Host "✅ Successfully retrieved popular prompts with data:" -ForegroundColor Green
            $response | Format-Table -AutoSize
            return $response
        } else {
            Write-Host "⚠️ No popular prompts found." -ForegroundColor Yellow
            return $null
        }
    } catch {
        Write-Host "❌ Error testing get_popular_prompts_with_data: $_" -ForegroundColor Red
        return $null
    }
}

# Function to run SQL and handle errors
function Invoke-SQL {
    param (
        [string]$Query,
        [string]$ErrorMessage
    )
    try {
        $result = psql $DB_CONNECTION -t -A -c $Query
        return $result
    } catch {
        Write-Error "❌ $ErrorMessage`: $_"
        return $null
    }
}

# Function to test topic management functions
function Test-TopicManagement {
    Write-Host "`n🔍 Testing topic management functions..." -ForegroundColor Cyan

    # Common headers for all requests
    $headers = @{
        "apikey" = $SupabaseKey
        "Authorization" = "Bearer $SupabaseKey"
        "Content-Type" = "application/json"
        "Prefer" = "return=representation"
    }

    # Test user ID (using the known working UUID)
    $testUserId = "006f85f7-ce10-4f1a-8f84-4546a890cfd1"
    $testTopicName = "Test Topic $([System.Random]::New().Next())"

    # Step 1: Create a topic
    Write-Host "`nStep 1: Creating test topic..." -ForegroundColor Cyan
    $createUrl = "$SupabaseUrl/rest/v1/rpc/create_topic"
    $createBody = @{
        p_user_id = $testUserId
        p_name = $testTopicName
        p_description = "Test Description"
        p_storage_type = "local"
    } | ConvertTo-Json

    try {
        $topic = Invoke-RestMethod -Uri $createUrl -Method Post -Headers $headers -Body $createBody
        Write-Host "✅ Topic created successfully:" -ForegroundColor Green
        $topic | ConvertTo-Json | Write-Host -ForegroundColor DarkGray
        $topicId = $topic.id

        # Step 2: Get topics
        Write-Host "`nStep 2: Getting topics list..." -ForegroundColor Cyan
        $getUrl = "$SupabaseUrl/rest/v1/rpc/get_topics"
        $getBody = @{
            p_user_id = $testUserId
        } | ConvertTo-Json

        $topics = Invoke-RestMethod -Uri $getUrl -Method Post -Headers $headers -Body $getBody
        Write-Host "✅ Topics retrieved successfully:" -ForegroundColor Green
        $topics | ConvertTo-Json | Write-Host -ForegroundColor DarkGray

        # Step 3: Check if topic exists
        Write-Host "`nStep 3: Checking if topic exists..." -ForegroundColor Cyan
        $existsUrl = "$SupabaseUrl/rest/v1/rpc/topic_exists"
        $existsBody = @{
            p_user_id = $testUserId
            p_name = $testTopicName
        } | ConvertTo-Json

        $exists = Invoke-RestMethod -Uri $existsUrl -Method Post -Headers $headers -Body $existsBody
        Write-Host "✅ Topic existence check successful: $exists" -ForegroundColor Green

        # Step 4: Delete topic
        Write-Host "`nStep 4: Deleting test topic..." -ForegroundColor Cyan
        $deleteUrl = "$SupabaseUrl/rest/v1/rpc/delete_topic"
        $deleteBody = @{
            p_user_id = $testUserId
            p_topic_id = $topicId
        } | ConvertTo-Json

        $deleted = Invoke-RestMethod -Uri $deleteUrl -Method Post -Headers $headers -Body $deleteBody
        Write-Host "✅ Topic deleted successfully: $deleted" -ForegroundColor Green

        Write-Host "`n✅ Topic management test completed successfully!" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "`n❌ Topic management test failed!" -ForegroundColor Red
        Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
        
        if ($_.ErrorDetails) {
            Write-Host "Response: $($_.ErrorDetails.Message)" -ForegroundColor Red
        }
        elseif ($_.Exception.Response) {
            try {
                $errorResponse = $_.Exception.Response.Content.ReadAsStringAsync().Result
                Write-Host "Response: $errorResponse" -ForegroundColor Red
            }
            catch {
                Write-Host "Could not read error response: $_" -ForegroundColor Red
            }
        }
        
        return $false
    }
}

# Main Menu
while ($true) {
    Write-Host "`nSupabase Checks Menu" -ForegroundColor Cyan
    Write-Host "1. Resolve IPv4 Address"
    Write-Host "2. Test Network Connectivity"
    Write-Host "3. Test PostgreSQL Connection"
    Write-Host "4. List Supabase Tables"
    Write-Host "5. Test Supabase Vault API Key"
    Write-Host "6. Test Get or Create License RPC Function"
    Write-Host "7. Test Complete License Workflow"
    Write-Host "8. Test API Server Verification"
    Write-Host "9. Test Get Popular Prompts With Data"
    Write-Host "10. Test Topic Management"
    Write-Host "11. Run All Checks"
    Write-Host "12. Exit"

    $choice = Read-Host "Enter your choice (1-12)"

    switch ($choice) {
        "1" {
            Resolve-IPv4Address -DbHost $DbHost
        }
        "2" {
            Test-NetworkConnectivity -DbHost $DbHost -Port $Port
        }
        "3" {
            $securePassword = Read-Host "Enter the password" -AsSecureString
            Test-PostgreSQLConnection -DbHost $DbHost -Port $Port -User $User -Password $securePassword -Database $Database
        }
        "4" {
            Get-SupabaseTables
        }
        "5" {
            $apiKey = Read-Host "Enter the API key to test"
            Test-SupabaseVaultApiKey -apiKey $apiKey
        }
        "6" {
            Test-GetOrCreateLicense
        }
        "7" {
            Test-LicenseWorkflow
        }
        "8" {
            $apiKey = Read-Host "Enter the API key to test"
            Test-ApiServerVerification -apiKey $apiKey
        }
        "9" {
            Test-GetPopularPrompts
        }
        "10" {
            Test-TopicManagement
        }
        "11" {
            Resolve-IPv4Address -DbHost $DbHost
            Test-NetworkConnectivity -DbHost $DbHost -Port $Port
            $securePassword = Read-Host "Enter the password" -AsSecureString
            Test-PostgreSQLConnection -DbHost $DbHost -Port $Port -User $User -Password $securePassword -Database $Database
            Get-SupabaseTables
            $apiKey = Read-Host "Enter the API key to test"
            Test-SupabaseVaultApiKey -apiKey $apiKey
            Test-GetOrCreateLicense
            Test-LicenseWorkflow
            Test-ApiServerVerification -apiKey $apiKey
            Test-GetPopularPrompts
            Test-TopicManagement
        }
        "12" {
            exit
        }
        default {
            Write-Host "Invalid choice. Please try again." -ForegroundColor Red
        }
    }
}

if ($args.Count -eq 0 -or $args[0] -eq "popular_prompts") {
    Test-GetPopularPrompts
}
