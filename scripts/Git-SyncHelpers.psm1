# Common git-related functions for sync scripts

function Test-UnpushedCommits {
    param (
        [string]$RepoName = ""
    )
    
    $gitStatus = git status
    Write-Host $gitStatus

    if ($gitStatus -match "Your branch is ahead of '.*' by \d+ commit") {
        $aheadCount = [regex]::Match($gitStatus, "ahead of '.*' by (\d+) commit").Groups[1].Value
        $repoDisplay = if ($RepoName) { " in $RepoName" } else { "" }
        Write-Host "`nWarning: Your branch is ahead by $aheadCount commits$repoDisplay."
        $pushConfirmation = Read-Host "Do you want to push these commits before proceeding? (y/n)"
        if ($pushConfirmation -eq 'y') {
            git push
            if ($LASTEXITCODE -ne 0) {
                Write-Host "Failed to push commits$repoDisplay. Please resolve and try again."
                return $false
            }
            return $true
        } else {
            Write-Host "Unpushed commits remain$repoDisplay. Aborting for safety."
            return $false
        }
    }
    return $true
}

Export-ModuleMember -Function Test-UnpushedCommits