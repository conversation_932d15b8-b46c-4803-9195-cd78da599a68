#!/bin/bash

# <PERSON>ript to update iOS app icons using the Android icons as source
# This script requires ImageMagick to be installed

# Check if ImageMagick is installed
if ! command -v convert &> /dev/null; then
    echo "Error: ImageMagick is not installed. Please install it first."
    echo "You can install it with: brew install imagemagick"
    exit 1
fi

# Source directory with Android icons
SRC_DIR="client/android/app/src/main/res"

# Destination directory for iOS icons
DEST_DIR="client/ios/Runner/Assets.xcassets/AppIcon.appiconset"

# Create a temporary directory
TMP_DIR=$(mktemp -d)

# Source icon (highest resolution)
SRC_ICON="${SRC_DIR}/mipmap-xxxhdpi/promz_icon.png"

# Check if source icon exists
if [ ! -f "$SRC_ICON" ]; then
    echo "Error: Source icon not found at $SRC_ICON"
    exit 1
fi

# iOS icon sizes and names
declare -A IOS_ICONS=(
    ["<EMAIL>"]="20x20"
    ["<EMAIL>"]="40x40"
    ["<EMAIL>"]="60x60"
    ["<EMAIL>"]="29x29"
    ["<EMAIL>"]="58x58"
    ["<EMAIL>"]="87x87"
    ["<EMAIL>"]="40x40"
    ["<EMAIL>"]="80x80"
    ["<EMAIL>"]="120x120"
    ["<EMAIL>"]="120x120"
    ["<EMAIL>"]="180x180"
    ["<EMAIL>"]="76x76"
    ["<EMAIL>"]="152x152"
    ["<EMAIL>"]="167x167"
    ["<EMAIL>"]="1024x1024"
)

echo "Generating iOS icons from $SRC_ICON..."

# Generate each icon size
for icon_name in "${!IOS_ICONS[@]}"; do
    size="${IOS_ICONS[$icon_name]}"
    echo "Generating $icon_name ($size)..."
    convert "$SRC_ICON" -resize "$size" "$TMP_DIR/$icon_name"
done

# Copy generated icons to the destination directory
echo "Copying icons to $DEST_DIR..."
cp "$TMP_DIR"/*.png "$DEST_DIR/"

# Clean up
rm -rf "$TMP_DIR"

echo "iOS icons updated successfully!"
