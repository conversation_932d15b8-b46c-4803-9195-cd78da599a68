# Windows Client Script for Promz Provider API
Set-StrictMode -Version Latest
$ErrorActionPreference = "Stop"

# Import module that may be needed for execution policy
Import-Module Microsoft.PowerShell.Security

# Get the script path and navigate to the client directory
$clientDir = Join-Path (Split-Path $PSScriptRoot -Parent) "client"
Push-Location $clientDir

try {
    # Function to ensure proper execution policy
    function Set-ScriptExecutionPolicy {
        try {
            $currentPolicy = Get-ExecutionPolicy
            if ($currentPolicy -eq 'Restricted') {
                Write-Host "Current execution policy is Restricted. Setting temporary policy for this session..."
                Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process -Force
                Write-Host "Successfully set execution policy to Bypass for this session."
            } else {
                Write-Host "Current execution policy is $currentPolicy. No changes needed."
            }
        } catch {
            Write-Host "Warning: Unable to modify execution policy. Error: $_"
            Write-Host "You may need to run 'Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser' manually."
            exit 1
        }
    }

    # Set execution policy before proceeding
    Set-ScriptExecutionPolicy

    # Function to ensure SQLite DLLs are present
    function Install-SqliteDlls {
        $sqliteDllPath = Join-Path $clientDir "sqlite3.dll"

        if (Test-Path $sqliteDllPath) {
            Write-Host "SQLite DLLs already present."
            return $true
        }

        Write-Host "Downloading SQLite DLLs..."
        $tempFile = Join-Path $env:TEMP "sqlite.zip"
        
        try {
            # Download the ZIP file
            $sqliteUrl = "https://www.sqlite.org/2025/sqlite-dll-win-x64-3490000.zip"
            Invoke-WebRequest -Uri $sqliteUrl -OutFile $tempFile
            
            # Extract the DLL
            Add-Type -AssemblyName System.IO.Compression.FileSystem
            $zip = [System.IO.Compression.ZipFile]::OpenRead($tempFile)
            $dllEntry = $zip.Entries | Where-Object { $_.Name -eq "sqlite3.dll" }
            
            if ($null -eq $dllEntry) {
                Write-Host "Error: sqlite3.dll not found in the downloaded archive."
                return $false
            }
            
            # Extract the DLL to the client directory
            [System.IO.Compression.ZipFileExtensions]::ExtractToFile($dllEntry, $sqliteDllPath, $true)
            $zip.Dispose()
            
            Write-Host "SQLite DLLs installed successfully."
            return $true
        }
        catch {
            Write-Host "Error installing SQLite DLLs: $_"
            return $false
        }
        finally {
            # Cleanup
            if (Test-Path $tempFile) {
                Remove-Item $tempFile -Force
            }
        }
    }

    # Function to verify Android SDK environment
    function Test-AndroidSDK {
        if (-not $env:ANDROID_HOME) {
            Write-Host "Error: ANDROID_HOME environment variable is not set. Please set it to your Android SDK location."
            return $false
        }

        $emulatorPath = Join-Path $env:ANDROID_HOME "emulator\emulator.exe"
        if (-not (Test-Path $emulatorPath)) {
            Write-Host "Error: Android emulator not found at: $emulatorPath"
            Write-Host "Please ensure Android SDK is properly installed with the emulator component."
            return $false
        }

        return $true
    }

    # Function to create Pixel 8 Pro emulator if missing
    function New-Pixel8ProEmulator {
        if (-not (Test-AndroidSDK)) {
            return $false
        }

        $emulatorName = "Pixel_8_Pro_API_35"
        $avdPath = "$env:USERPROFILE\.android\avd\$emulatorName.avd"
        $configSourcePath = "$PSScriptRoot\pixel_8_pro_config.ini"
        $emulatorPath = Join-Path $env:ANDROID_HOME "emulator\emulator.exe"
        
        # Check if emulator already exists and remove it
        $existingAvds = & $emulatorPath -list-avds
        if ($existingAvds -contains $emulatorName) {
            Write-Host "Removing existing Pixel 8 Pro emulator..."
            Remove-Item -Path $avdPath -Recurse -Force -ErrorAction SilentlyContinue
            Remove-Item -Path "$env:USERPROFILE\.android\avd\$emulatorName.ini" -Force -ErrorAction SilentlyContinue
        }

        # Check if config file exists
        if (-not (Test-Path $configSourcePath)) {
            Write-Host "Error: Could not find pixel_8_pro_config.ini in the scripts directory."
            return $false
        }

        Write-Host "Creating Pixel 8 Pro emulator..."
        
        # Create AVD directory
        New-Item -ItemType Directory -Force -Path $avdPath | Out-Null
        
        # Copy config.ini from source
        Copy-Item -Path $configSourcePath -Destination "$avdPath\config.ini"
        
        # Create AVD ini file
        $avdIniContent = @"
avd.ini.encoding=UTF-8
path=$($avdPath -replace '\\', '\\')
path.rel=avd\\$emulatorName.avd
target=android-35
"@
        Set-Content -Path "$env:USERPROFILE\.android\avd\$emulatorName.ini" -Value $avdIniContent
        
        # Verify creation
        $existingAvds = & $emulatorPath -list-avds
        if ($existingAvds -contains $emulatorName) {
            Write-Host "Successfully created Pixel 8 Pro emulator."
            return $true
        } else {
            Write-Host "Failed to create Pixel 8 Pro emulator."
            return $false
        }
    }

    # Function to test if emulator is running or start one if necessary
    function Test-Emulator {
        if (-not (Test-AndroidSDK)) {
            return $false
        }

        $adbDevices = adb devices 2>&1
        if ($adbDevices -match "emulator") {
            $logPath = Join-Path $env:USERPROFILE "promz_emulator.log"
            $errorLogPath = Join-Path $env:USERPROFILE "promz_emulator_error.log"
            
            Write-Host "Emulator is running."
            Write-Host "Emulator logs can be found at:"
            Write-Host "- Log: $logPath"
            Write-Host "- Error Log: $errorLogPath"
            return $true
        } else {
            Write-Host "No emulator found. Starting a new one..."
            return Start-Emulator
        }
    }

    # Function to start emulator
    function Start-Emulator {
        if (-not (Test-AndroidSDK)) {
            return $false
        }

        $emulatorName = "Pixel_8_Pro_API_35"
        $emulatorPath = Join-Path $env:ANDROID_HOME "emulator\emulator.exe"
        $logPath = Join-Path $env:USERPROFILE "promz_emulator.log"
        $errorLogPath = Join-Path $env:USERPROFILE "promz_emulator_error.log"
        
        Write-Host "`nEmulator output will be redirected to:`n- Log: $logPath`n- Error Log: $errorLogPath`n"
        Write-Host "Press any key to continue or Ctrl+C to cancel..." -NoNewline
        $null = $Host.UI.RawUI.ReadKey('NoEcho,IncludeKeyDown')
        Write-Host "`n"
        
        # Check if emulator exists, create if missing
        $existingAvds = & $emulatorPath -list-avds
        if ($existingAvds -notcontains $emulatorName) {
            if (-not (New-Pixel8ProEmulator)) {
                Write-Host "Failed to create emulator. Please check Android SDK installation and system images."
                return $false
            }
        }
        
        try {
            # Create emulator temp directories to prevent filesystem errors
            $emulatorTempPath = Join-Path $env:LOCALAPPDATA "Temp\AndroidEmulator"
            $crashDbPath = Join-Path $emulatorTempPath "emu-crash-35.3.11.db\attachments"
            New-Item -ItemType Directory -Force -Path $crashDbPath | Out-Null
            
            # Kill any existing emulator processes
            Get-Process | Where-Object { $_.Name -eq "qemu-system-x86_64" } | Stop-Process -Force
            
            Write-Host "Starting Android emulator..."
            
            # Start emulator with additional parameters to reduce noise and optimize for x64
            $emulatorArgs = @(
                "-avd", $emulatorName,
                "-netdelay", "none",
                "-netspeed", "full",
                "-no-snapshot",
                "-no-boot-anim",
                "-gpu", "host",
                "-accel", "on",
                "-feature", "WindowsHypervisorPlatform"
            )
            
            # Start the emulator process and redirect output to files
            $process = Start-Process -FilePath $emulatorPath `
                -ArgumentList $emulatorArgs `
                -RedirectStandardOutput $logPath `
                -RedirectStandardError $errorLogPath `
                -PassThru `
                -NoNewWindow
            
            if (-not $process) {
                Write-Host "Failed to start emulator process."
                return $false
            }
            
            Write-Host "Emulator process started (PID: $($process.Id)). Waiting for device to become available..."
            Write-Host "Check logs at: $logPath"
            
            # Wait for emulator to start
            $timeout = 60  # seconds
            $startTime = Get-Date
            
            do {
                Start-Sleep -Seconds 2
                
                # Check if the process is still running
                if ($process.HasExited) {
                    Write-Host "Emulator process exited unexpectedly with code: $($process.ExitCode)"
                    Write-Host "Please check the error log at: $errorLogPath"
                    return $false
                }
                
                # Check if device is available
                $adbDevices = adb devices 2>&1
                if ($adbDevices -match "emulator") {
                    Write-Host "`nEmulator is now running and device is available."
                    Write-Host "Output is being logged to: $logPath"
                    Write-Host "Errors are being logged to: $errorLogPath"
                    return $true
                }
                
                if (((Get-Date) - $startTime).TotalSeconds -gt $timeout) {
                    Write-Host "`nTimeout waiting for emulator to start."
                    Write-Host "Please check the error log at: $errorLogPath"
                    # Try to kill the process if it's still running
                    if (-not $process.HasExited) {
                        $process.Kill()
                    }
                    return $false
                }
                
                Write-Host "." -NoNewline
                
            } while ($true)
        } catch {
            Write-Host "Failed to start emulator: $_"
            Write-Host "Please check the error log at: $errorLogPath"
            return $false
        }
    }

    # Function to reset and rebuild the project
    function Reset-Build {
        Write-Host "Cleaning up previous build..."
        flutter clean 2>&1
        flutter pub get 2>&1
        
        Write-Host "Uninstalling previous app version..."
        adb uninstall ai.promz 2>&1
        
        Write-Host "Restarting emulator..."
        Stop-Emulator
        if (-not (Start-Emulator)) {
            Write-Host "Failed to restart emulator. Aborting build."
            exit 1
        }
        
        Write-Host "Rebuilding project..."
        dart run build_runner build --delete-conflicting-outputs 2>&1
    }

    function Stop-FlutterTester {
        Get-Process | Where-Object { $_.Name -eq "flutter_tester" } | Stop-Process -Force
    }

    # Function to rebuild the database
    function Update-Database {
        dart run build_runner build --delete-conflicting-outputs 2>&1
    }

    # Function to stop the emulator
    function Stop-Emulator {
        Write-Host "Stopping emulator..."
        adb emu kill 2>&1
        Start-Sleep -Seconds 5
        
        # Clean up old log files if they exist
        $logPath = Join-Path $env:USERPROFILE "promz_emulator.log"
        $errorLogPath = Join-Path $env:USERPROFILE "promz_emulator_error.log"
        
        if (Test-Path $logPath) {
            Move-Item -Path $logPath -Destination "$logPath.old" -Force -ErrorAction SilentlyContinue
        }
        if (Test-Path $errorLogPath) {
            Move-Item -Path $errorLogPath -Destination "$errorLogPath.old" -Force -ErrorAction SilentlyContinue
        }
    }

    # Function to run tests
    function Test-Tests {
        Write-Host "Running client tests..." -ForegroundColor Cyan
        Stop-FlutterTester
        flutter test 2>&1
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Client tests failed. Aborting installation." -ForegroundColor Red
            exit 1
        }
        Stop-FlutterTester
        
        # Run promz_common tests
        Write-Host "Running promz_common tests..." -ForegroundColor Cyan
        $rootDir = Split-Path $clientDir -Parent
        $commonDir = Join-Path $rootDir "packages\promz_common"
        
        # Save current location
        Push-Location $commonDir
        
        try {
            Stop-FlutterTester
            flutter test 2>&1
            if ($LASTEXITCODE -ne 0) {
                Write-Host "promz_common tests failed. Aborting installation." -ForegroundColor Red
                exit 1
            }
            Stop-FlutterTester
        }
        finally {
            # Return to previous location
            Pop-Location
        }
        
        Write-Host "All tests passed successfully!" -ForegroundColor Green
    }

    # Function to run benchmarks
    function Test-Benchmarks {
        Write-Host "Running benchmarks..."
        flutter test --tags benchmark test/benchmark/keyword_extraction_benchmark.dart 2>&1
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Benchmarks failed."
            return $false
        }
        Write-Host "Benchmarks completed successfully."
        return $true
    }

    # Function to build and install the release app
    function Build-Release {
        Write-Host "Building release APK..."
        flutter build apk --release 2>&1
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Release build failed."
            exit 1
        }
        Write-Host "Release APK built successfully."
    }

    # Function to build a release app bundle for Play Console with debug symbols
    function Build-AppBundle {
        Write-Host "Building release App Bundle for Play Console with debug symbols..." -ForegroundColor Cyan

        Write-Host "Step 1: Building app bundle with native debug symbols enabled..."
        flutter build appbundle --release --split-debug-info=build/app/symbols 2>&1
        if ($LASTEXITCODE -ne 0) {
            Write-Host "App Bundle build failed." -ForegroundColor Red
            exit 1
        }

        # Use absolute paths from the current directory to avoid path resolution issues
        $currentDir = Get-Location
        $bundlePath = Join-Path $currentDir "build\app\outputs\bundle\release\app-release.aab"
        $symbolsSourcePath = Join-Path $currentDir "build\app\symbols"
        $symbolsOutputDir = Join-Path $currentDir "build\app\outputs\bundle\release\symbols"

        if (Test-Path $bundlePath) {
            Write-Host "`nApp Bundle built successfully:" -ForegroundColor Green
            Write-Host "- Bundle: $bundlePath" -ForegroundColor Green
            Write-Host "- Raw Debug symbols generated at: $symbolsSourcePath" -ForegroundColor Green

            Write-Host "`nStep 2: Packaging debug symbols into ZIP files for Play Console upload..." -ForegroundColor Cyan

            # Create output directory for symbol packages
            if (-not (Test-Path $symbolsOutputDir)) {
                New-Item -ItemType Directory -Force -Path $symbolsOutputDir | Out-Null
            } else {
                # Clean output directory
                Get-ChildItem -Path $symbolsOutputDir -File | Remove-Item -Force
            }

            # Add required types to work with ZIP files
            Add-Type -AssemblyName System.IO.Compression
            Add-Type -AssemblyName System.IO.Compression.FileSystem

            # Process each architecture's symbols separately as required by Play Console
            $architectures = @("android-arm", "android-arm64", "android-x64")
            $symbolZipFiles = @{}

            foreach ($arch in $architectures) {
                $archSymbolFile = Join-Path $symbolsSourcePath "app.$arch.symbols"
                if (Test-Path $archSymbolFile) {
                    $outputZip = Join-Path $symbolsOutputDir "$arch.symbols.zip"
                    $symbolZipFiles[$arch] = $outputZip

                    try {
                        Write-Host "  - Creating symbol ZIP package for $arch..." -NoNewline

                        # Create a new ZIP archive
                        $zipArchive = [System.IO.Compression.ZipFile]::Open($outputZip, [System.IO.Compression.ZipArchiveMode]::Create)
                        # Get just the filename (e.g., "app.android-arm64.symbols")
                        $entryName = [System.IO.Path]::GetFileName($archSymbolFile)

                        # Add the symbol file to the root of the archive using CreateEntry and stream copying for compatibility
                        $entry = $zipArchive.CreateEntry($entryName)
                        $entryStream = $entry.Open()
                        $sourceStream = [System.IO.File]::OpenRead($archSymbolFile)
                        $sourceStream.CopyTo($entryStream)

                        # Close streams
                        $sourceStream.Close()
                        $sourceStream.Dispose()
                        $entryStream.Close()
                        $entryStream.Dispose()

                        # Dispose the archive to save changes
                        $zipArchive.Dispose()

                        Write-Host "Done! ($outputZip)" -ForegroundColor Green
                    } catch {
                        Write-Host "Failed!" -ForegroundColor Red
                        Write-Host "  Error creating symbol package for $arch`: $_" -ForegroundColor Red
                    }
                } else {
                    Write-Host "  - No symbols found for $arch architecture in $symbolsSourcePath." -ForegroundColor Yellow
                }
            }

            # Report results
            if ($symbolZipFiles.Count -gt 0) {
                Write-Host "`nDebug symbols packaged successfully into ZIP files:" -ForegroundColor Green
                foreach ($arch in $symbolZipFiles.Keys) {
                    Write-Host "  - $arch`: $($symbolZipFiles[$arch])" -ForegroundColor Green
                }

                Write-Host "`nUpload Instructions:" -ForegroundColor Cyan
                Write-Host "1. Upload $bundlePath to Google Play Console"
                Write-Host "2. When prompted for native debug symbols during the upload process:"
                Write-Host "   - For arm64-v8a architecture, upload the file: $($symbolZipFiles['android-arm64'])"
                Write-Host "   - For armeabi-v7a architecture, upload the file: $($symbolZipFiles['android-arm'])"
                Write-Host "   - For x86_64 architecture, upload the file: $($symbolZipFiles['android-x64'])"
                Write-Host "`nNote: Upload the generated .zip files for each architecture." -ForegroundColor Yellow
            } else {
                Write-Host "`nNo architecture symbols were found to package. The app may not have native components requiring debug symbols." -ForegroundColor Yellow
            }
        } else {
            Write-Host "App Bundle was created but not found at the expected location: $bundlePath" -ForegroundColor Yellow
        }
    }

    # Function to build and install the debug app
    function Build-Debug {
        Write-Host "Building and installing debug APK..."
        flutter build apk --debug 2>&1
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Debug build failed."
            exit 1
        }
        
        Write-Host "Debug APK built successfully."
    }

    # Function to install the APK
    function Install-Debug-Emulator {
        Write-Host "Installing APK..."
        adb -e install build/app/outputs/flutter-apk/app-debug.apk 2>&1
        if ($LASTEXITCODE -ne 0) {
            Write-Host "APK installation failed."
            exit 1
        }
        
        Write-Host "APK installed successfully."
    }

    # Function to install the APK to Physical Device
    function Install-Debug-Device {
        Write-Host "Installing APK..."
        adb -d install build/app/outputs/flutter-apk/app-debug.apk 2>&1
        if ($LASTEXITCODE -ne 0) {
            Write-Host "APK installation failed."
            exit 1
        }
        
        Write-Host "APK installed successfully."
    }

    # Function to install the release APK
    function Install-Release-Emulator {
        Write-Host "Installing release APK..."
        adb -e install build/app/outputs/flutter-apk/app-release.apk 2>&1
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Release APK installation failed."
            exit 1
        }
        
        Write-Host "Release APK installed successfully."
    }

    # Function to install the release APK to Physical Device
    function Install-Release-Device {
        Write-Host "Installing release APK..."
        adb -d install build/app/outputs/flutter-apk/app-release.apk 2>&1
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Release APK installation failed."
            exit 1
        }
        
        Write-Host "Release APK installed successfully."
    }

    # Function to test if a device is connected
    function Test-Device {
        Write-Host "Testing device connection..."
        adb devices -l | findstr /v emulator 2>&1
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Device connection test failed."
            return $false
        }
        Write-Host "Device connection test passed."
        return $true
    }

    # Function to show the menu and handle user choices
    function Show-Menu {
        while ($true) {
            Write-Host "`nFlutter Build and Run Script" -ForegroundColor Cyan
            Write-Host "0. Exit"
            Write-Host "1. Start/Verify Emulator"
            Write-Host "2. Update Database Schema"
            Write-Host "3. Run Benchmarks"
            Write-Host "4. [+ Tests] Build, Push Debug APK"
            Write-Host "5. [- Tests] Build, Push Debug APK"
            Write-Host "6. [+ Tests] Build Release APK"
            Write-Host "7. [- Tests] Build Release APK"
            Write-Host "8. [Emulator] Push Debug APK"
            Write-Host "9. [Emulator] Push Release APK"
            Write-Host "10. [Device] Push Debug APK"
            Write-Host "11. [Device] Push Release APK"
            Write-Host "12. Build Release App Bundle for Play Console"

            $choice = Read-Host "Enter your choice (0-12)"

            switch ($choice) {
                '0' {
                    Write-Host "Exiting script."
                    exit
                }
                '1' {
                    Test-Emulator
                    break
                }
                '2' {
                    Update-Database
                    break
                }
                '3' {
                    Test-Benchmarks
                    break
                }
                '4' {
                    if (-not (Test-Emulator)) { break }
                    Test-Tests
                    Build-Debug
                    Install-Debug-Emulator
                    break
                }
                '5' {
                    if (-not (Test-Emulator)) { break }
                    Build-Debug
                    Install-Debug-Emulator
                    break
                }
                '6' {
                    Test-Tests
                    if (Test-Benchmarks) {
                        Build-Release
                    } else {
                        Write-Host "Benchmarks failed. Aborting release build."
                    }
                    break
                }
                '7' {
                    Build-Release
                    break
                }
                '8' {
                    if (-not (Test-Emulator)) { break }
                    Install-Debug-Emulator
                    break
                }
                '9' {
                    if (-not (Test-Emulator)) { break }
                    Install-Release-Emulator
                    break
                }
                '10' {
                    if (-not (Test-Device)) { break }
                    Install-Debug-Device
                    break
                }
                '11' {
                    if (-not (Test-Device)) { break }
                    Install-Release-Device
                    break
                }
                '12' {
                    Build-AppBundle
                    break
                }
                default {
                    Write-Warning "Invalid choice. Please try again."
                }
            }
        }
    }

    # Main script execution
    if (-not (Install-SqliteDlls)) {
        Write-Host "Failed to install SQLite DLLs. Tests may fail."
    }

    # Show the menu
    Show-Menu
}
finally {
    # Restore original location
    Pop-Location
}
