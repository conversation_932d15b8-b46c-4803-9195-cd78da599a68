package contextkeys

import "context"

// Define a custom type for context keys to avoid collisions
type contextKey string

// UserIDKey is the key used to store the user ID in the context.
const UserIDKey contextKey = "user_id"

// LicenseTypeKey is the key used to store the license type in the context.
const LicenseTypeKey contextKey = "license_type"

// APIKeyKey is the key used to store the API key in the context.
const APIKeyKey contextKey = "api_key"

// GetUserID extracts the user ID from the context.
// Returns the user ID string and true if found and valid, otherwise empty string and false.
func GetUserID(ctx context.Context) (string, bool) {
	userIDValue := ctx.Value(UserIDKey)
	userID, ok := userIDValue.(string)
	return userID, ok && userID != ""
}

// GetLicenseType extracts the license type from the context.
// Returns the license type string and true if found, otherwise empty string and false.
func GetLicenseType(ctx context.Context) (string, bool) {
	licenseTypeValue := ctx.Value(LicenseTypeKey)
	licenseType, ok := licenseTypeValue.(string)
	return licenseType, ok
}

// GetAP<PERSON><PERSON>ey extracts the API key from the context.
// Returns the API key string and true if found, otherwise empty string and false.
func GetAPIKey(ctx context.Context) (string, bool) {
	apiKeyValue := ctx.Value(APIKeyKey)
	apiKey, ok := apiKeyValue.(string)
	return apiKey, ok
}
