package models

// LLMRequest represents the client-facing request for LLM execution
// This is what clients send to the API
type LLMRequest struct {
	// Identification fields - which prompt/category to use
	PromptID   string `json:"prompt_id,omitempty"`
	CategoryID string `json:"category_id,omitempty"`

	// Content fields - what the user is asking
	PromptContent string          `json:"prompt_content,omitempty"` // User's input text
	SourceContent []SourceContent `json:"source_content,omitempty"` // Additional context

	// Variables for template substitution
	Variables map[string]interface{} `json:"variables,omitempty"` // Variables for template substitution

	// Model parameters - how to generate the response
	MaxTokens   int     `json:"max_tokens,omitempty"`  // Controls response length
	Temperature float32 `json:"temperature,omitempty"` // Controls randomness/creativity

	// Provider selection
	Provider string `json:"provider,omitempty"` // Specific LLM provider to use (e.g., "gemini", "openai")

	// Additional options
	Options map[string]string `json:"options,omitempty"`
}

// SourceContent represents content from a file or text input
type SourceContent struct {
	Type        string `json:"type"` // "file", "text", etc.
	Content     string `json:"content"`
	FileName    string `json:"file_name,omitempty"`
	ContentType string `json:"content_type,omitempty"`
}
