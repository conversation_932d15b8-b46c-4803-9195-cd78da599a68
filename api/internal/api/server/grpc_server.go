package server

import (
	"context"
	"log"
	"net"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/improbable-eng/grpc-web/go/grpcweb"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/reflection"
	"google.golang.org/grpc/status"
	"promz.ai/api/internal/api/auth"
	"promz.ai/api/internal/api/handler"
	"promz.ai/api/internal/repository/db"
	"promz.ai/api/proto/gen"
	"promz.ai/api/services/processing"
	"promz.ai/api/services/websocket"
	"promz.ai/api/utils/config"
)

// GrpcServer manages the gRPC server
type GrpcServer struct {
	server           *grpc.Server
	webSocketHandler *websocket.WebSocketHandler
	config           *config.Config
	uploadHandler    *processing.UploadHandler
	storageService   processing.StorageService
	contentProcessor processing.ContentProcessor
	jobStore         processing.JobStore
	dbClient         db.SupabaseClientInterface
	authService      *auth.AuthService
}

// NewGrpcServer creates a new gRPC server
func NewGrpcServer(
	config *config.Config,
	uploadHandler *processing.UploadHandler,
	webSocketHandler *websocket.WebSocketHandler,
	storageService processing.StorageService,
	contentProcessor processing.ContentProcessor,
	jobStore processing.JobStore,
	dbClient db.SupabaseClientInterface,
) *GrpcServer {
	// Create shared auth service
	authService := auth.NewAuthService(dbClient)

	// Create server with auth interceptors
	server := newGrpcServerWithAuth(config, authService)

	// Enable reflection for development
	reflection.Register(server)

	return &GrpcServer{
		server:           server,
		webSocketHandler: webSocketHandler,
		config:           config,
		uploadHandler:    uploadHandler,
		storageService:   storageService,
		contentProcessor: contentProcessor,
		jobStore:         jobStore,
		dbClient:         dbClient,
		authService:      authService,
	}
}

// newGrpcServerWithAuth creates a new gRPC server with authentication
func newGrpcServerWithAuth(config *config.Config, authService *auth.AuthService) *grpc.Server {
	// Create server options
	var opts []grpc.ServerOption

	// Create auth interceptors with the shared auth service
	unaryInterceptor := createAuthInterceptor(authService)
	streamInterceptor := createStreamAuthInterceptor(authService)

	// Add authentication interceptors
	opts = append(opts, grpc.UnaryInterceptor(unaryInterceptor))
	opts = append(opts, grpc.StreamInterceptor(streamInterceptor))

	// Add TLS if configured
	if config.UseTLS {
		creds, err := credentials.NewServerTLSFromFile(config.TLSCertPath, config.TLSKeyPath)
		if err != nil {
			log.Fatalf("Failed to generate credentials: %v", err)
		}
		opts = append(opts, grpc.Creds(creds))
	}

	// Create gRPC server
	return grpc.NewServer(opts...)
}

// RegisterServices registers all gRPC services
func (s *GrpcServer) RegisterServices() {
	// TODO: Register other existing services if needed (e.g., LicenseService)

	// --- Register ContentProcessingService ---
	// Instantiate the handler using dependencies stored in GrpcServer
	grpcProcessingHandler := handler.NewGrpcProcessingHandler(s.storageService, s.contentProcessor, s.jobStore)

	// Register the ContentUploadService server
	gen.RegisterContentUploadServiceServer(s.server, grpcProcessingHandler)

	log.Println("Registered gRPC services")
}

// Serve starts the gRPC server
func (s *GrpcServer) Serve(listener net.Listener) error {
	log.Printf("Starting gRPC server on %s", listener.Addr().String())
	return s.server.Serve(listener)
}

// GracefulStop stops the gRPC server gracefully
func (s *GrpcServer) GracefulStop() {
	s.server.GracefulStop()
	log.Println("gRPC server stopped gracefully")
}

// CreateGrpcWebHandler creates a handler for gRPC-Web requests
func (s *GrpcServer) CreateGrpcWebHandler() http.Handler {
	// Create gRPC-Web wrapper around the gRPC server
	wrappedGrpc := grpcweb.WrapServer(s.server,
		grpcweb.WithOriginFunc(func(origin string) bool {
			// Allow all origins in development, or implement stricter rules for production
			return true
		}),
		grpcweb.WithWebsockets(true),
		grpcweb.WithWebsocketOriginFunc(func(req *http.Request) bool {
			// Allow all origins for WebSockets in development
			return true
		}),
	)

	// Create an HTTP handler that checks if a request is gRPC-Web and forwards it to the wrapped server
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Add CORS headers for browser clients
		w.Header().Set("Access-Control-Allow-Origin", "*")
		w.Header().Set("Access-Control-Allow-Methods", "POST, GET, OPTIONS, PUT, DELETE")
		w.Header().Set("Access-Control-Allow-Headers", "Accept, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, X-API-Key")

		// Handle preflight OPTIONS request
		if r.Method == "OPTIONS" {
			w.WriteHeader(http.StatusOK)
			return
		}

		// Handle the request based on whether it's gRPC-Web
		if wrappedGrpc.IsGrpcWebRequest(r) || wrappedGrpc.IsAcceptableGrpcCorsRequest(r) ||
			r.ProtoMajor == 2 || strings.Contains(r.Header.Get("Content-Type"), "application/grpc") {
			log.Printf("Handling gRPC-Web request: %s %s", r.Method, r.URL.Path)
			wrappedGrpc.ServeHTTP(w, r)
			return
		}

		// If not a gRPC-Web request, return 404
		http.NotFound(w, r)
	})
}

// RegisterWithGin registers the gRPC-Web handler with a Gin router
func (s *GrpcServer) RegisterWithGin(router *gin.Engine) {
	// Create gRPC-Web handler
	grpcWebHandler := s.CreateGrpcWebHandler()

	// Register handler for gRPC-Web requests
	router.Any("/api/v1/grpc/*any", func(c *gin.Context) {
		// Modify the path to remove the prefix
		path := c.Request.URL.Path
		c.Request.URL.Path = strings.TrimPrefix(path, "/api/v1/grpc")

		// Serve the request
		grpcWebHandler.ServeHTTP(c.Writer, c.Request)
	})

	log.Println("Registered gRPC-Web handler with Gin")
}

// createAuthInterceptor creates a unary interceptor for authentication using shared AuthService
func createAuthInterceptor(authService *auth.AuthService) grpc.UnaryServerInterceptor {
	return func(
		ctx context.Context,
		req interface{},
		info *grpc.UnaryServerInfo,
		handler grpc.UnaryHandler,
	) (interface{}, error) {
		log.Printf("gRPC unary auth interceptor: %s", info.FullMethod)

		// Extract authentication information from metadata
		md, ok := metadata.FromIncomingContext(ctx)
		if !ok {
			log.Printf("Authentication failed: missing metadata")
			return nil, status.Error(codes.Unauthenticated, "missing metadata")
		}

		// Extract API key
		apiKeys := md.Get("x-api-key")
		if len(apiKeys) == 0 {
			log.Printf("Authentication failed: missing API key")
			return nil, status.Error(codes.Unauthenticated, "missing API key")
		}
		apiKey := apiKeys[0]

		// Verify API key using shared auth service
		authResult, err := authService.VerifyAPIKey(ctx, apiKey)
		if err != nil {
			log.Printf("Authentication failed: %v", err)
			return nil, status.Error(codes.Unauthenticated, "invalid API key")
		}

		// Create new context with authentication info
		newCtx := authService.CreateAuthContext(ctx, authResult)

		log.Printf("API key authenticated successfully for user %s with license %s",
			authResult.UserID, authResult.LicenseType)

		// Call the handler with the new context
		return handler(newCtx, req)
	}
}

// createStreamAuthInterceptor creates a stream interceptor for authentication using shared AuthService
func createStreamAuthInterceptor(authService *auth.AuthService) grpc.StreamServerInterceptor {
	return func(
		srv interface{},
		ss grpc.ServerStream,
		info *grpc.StreamServerInfo,
		handler grpc.StreamHandler,
	) error {
		log.Printf("gRPC stream auth interceptor: %s", info.FullMethod)

		// Extract authentication information from metadata
		md, ok := metadata.FromIncomingContext(ss.Context())
		if !ok {
			log.Printf("Authentication failed: missing metadata")
			return status.Error(codes.Unauthenticated, "missing metadata")
		}

		// Extract API key
		apiKeys := md.Get("x-api-key")
		if len(apiKeys) == 0 {
			log.Printf("Authentication failed: missing API key")
			return status.Error(codes.Unauthenticated, "missing API key")
		}
		apiKey := apiKeys[0]

		// Verify API key using shared auth service
		authResult, err := authService.VerifyAPIKey(ss.Context(), apiKey)
		if err != nil {
			log.Printf("Authentication failed: %v", err)
			return status.Error(codes.Unauthenticated, "invalid API key")
		}

		// Create new context with authentication info
		newCtx := authService.CreateAuthContext(ss.Context(), authResult)

		log.Printf("API key authenticated successfully for user %s with license %s",
			authResult.UserID, authResult.LicenseType)

		// Create wrapped stream with new context
		wrappedStream := &wrappedServerStream{
			ServerStream: ss,
			ctx:          newCtx,
		}

		// Call the handler with the wrapped stream
		return handler(srv, wrappedStream)
	}
}

// wrappedServerStream wraps a grpc.ServerStream with a new context
type wrappedServerStream struct {
	grpc.ServerStream
	ctx context.Context
}

// Context returns the wrapped context
func (w *wrappedServerStream) Context() context.Context {
	return w.ctx
}
