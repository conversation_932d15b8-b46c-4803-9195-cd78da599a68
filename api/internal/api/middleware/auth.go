package middleware

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"promz.ai/api/internal/api/auth"
	"promz.ai/api/internal/repository/db"
)

// APIKeyAuth middleware to verify API key and set user ID in context
func APIKeyAuth(dbClient db.SupabaseClientInterface) gin.HandlerFunc {
	// Create the shared auth service
	authService := auth.NewAuthService(dbClient)

	return func(c *gin.Context) {
		// Get API key from header
		apiKey := c.GetHeader("X-API-Key")
		if apiKey == "" {
			// Try to get from query param
			apiKey = c.Query("api_key")
		}

		// Verify API key using the shared auth service
		authResult, err := authService.VerifyAPIKey(c.Request.Context(), apiKey)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": err.Error()})
			c.Abort()
			return
		}

		// Set API key and user ID in Gin context for later use
		c.Set("api_key", authResult.APIKey)
		c.Set("user_id", authResult.UserID)
		c.Set("license_type", authResult.LicenseType)
		c.Next()
	}
}

// CORSMiddleware handles CORS
func CORSMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Writer.Header().Set("Access-Control-Allow-Origin", "*")
		c.Writer.Header().Set("Access-Control-Allow-Credentials", "true")
		c.Writer.Header().Set("Access-Control-Allow-Headers", "Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, accept, origin, Cache-Control, X-Requested-With, X-API-Key")
		c.Writer.Header().Set("Access-Control-Allow-Methods", "POST, OPTIONS, GET, PUT, PATCH, DELETE")
		c.Writer.Header().Set("Access-Control-Expose-Headers", "Content-Length, Content-Type, X-Content-Type-Options")

		// Handle preflight requests
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}
