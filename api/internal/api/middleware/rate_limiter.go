package middleware

import (
	"net"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
)

// RateLimiter creates a middleware that limits the number of requests
// from a single IP address within a specified time window.
// limit: maximum number of requests allowed
// window: time window in seconds for the limit
func RateLimiter(limit int, window int) gin.HandlerFunc {
	// Track IP addresses and their request counts
	type client struct {
		count      int
		lastAccess time.Time
	}

	// Use a mutex to protect the clients map
	var (
		clients = make(map[string]*client)
		mu      sync.Mutex
	)

	// Start a goroutine to clean up old entries
	go func() {
		for {
			time.Sleep(time.Minute) // Clean up every minute

			mu.Lock()
			now := time.Now()
			for ip, c := range clients {
				// Remove entries older than the window
				if now.Sub(c.lastAccess) > time.Duration(window)*time.Second {
					delete(clients, ip)
				}
			}
			mu.Unlock()
		}
	}()

	return func(c *gin.Context) {
		// Get the client IP address
		ip, _, err := net.SplitHostPort(c.Request.RemoteAddr)
		if err != nil {
			ip = c.Request.RemoteAddr
		}

		// Use X-Forwarded-For or X-Real-IP if behind a proxy
		if forwardedFor := c.Request.Header.Get("X-Forwarded-For"); forwardedFor != "" {
			ip = forwardedFor
		} else if realIP := c.Request.Header.Get("X-Real-IP"); realIP != "" {
			ip = realIP
		}

		mu.Lock()

		// Get or create client record
		cl, exists := clients[ip]
		if !exists {
			cl = &client{count: 0, lastAccess: time.Now()}
			clients[ip] = cl
		}

		// Check if client has reached limit
		if cl.count >= limit && time.Since(cl.lastAccess) < time.Duration(window)*time.Second {
			mu.Unlock()
			c.AbortWithStatusJSON(429, gin.H{
				"error": "Too many requests, please try again later",
			})
			return
		}

		// Reset count if window has passed
		if time.Since(cl.lastAccess) >= time.Duration(window)*time.Second {
			cl.count = 0
		}

		// Update client record
		cl.count++
		cl.lastAccess = time.Now()

		mu.Unlock()

		c.Next()
	}
}
