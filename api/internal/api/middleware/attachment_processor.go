package middleware

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"promz.ai/api/services/processing"
)

// AttachmentVariableProcessor processes attachment variables in the request
func AttachmentVariableProcessor(processingService *processing.ContentProcessingService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Read the request body
		bodyBytes, err := io.ReadAll(c.Request.Body)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to read request body"})
			c.Abort()
			return
		}

		// Restore the request body for later use
		c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

		// Parse the request body
		var requestBody map[string]interface{}
		if err := json.Unmarshal(bodyBytes, &requestBody); err != nil {
			// Continue without processing if we can't parse the body
			return
		}

		// Process variables if they exist
		variables, ok := requestBody["variables"].(map[string]interface{})
		if ok && processingService != nil {
			// Process attachment variables
			processAttachmentVariables(variables, processingService)

			// Update the request body with the processed variables
			requestBody["variables"] = variables
			
			// Marshal the updated request body
			updatedBody, err := json.Marshal(requestBody)
			if err != nil {
				return
			}

			// Replace the request body with the updated one
			c.Request.Body = io.NopCloser(bytes.NewBuffer(updatedBody))
		}

		c.Next()
	}
}

// processAttachmentVariables processes attachment variables in the variables map
func processAttachmentVariables(variables map[string]interface{}, processingService *processing.ContentProcessingService) {
	// Create a background context for processing
	ctx := context.Background()

	for key, value := range variables {
		// Check if this is an attachment variable
		if strings.Contains(key, "ATTACHMENT:") {
			strValue, ok := value.(string)
			if !ok {
				continue
			}

			// Extract the upload ID from the variable value
			// Format could be either "ATTACHMENT:UPLOAD_ID" or a prefixed version like "{$1}ATTACHMENT:UPLOAD_ID"
			uploadID := ""
			
			// Check if it's a prefixed attachment variable
			if strings.Contains(strValue, "ATTACHMENT:") {
				parts := strings.Split(strValue, "ATTACHMENT:")
				if len(parts) == 2 {
					uploadID = parts[1]
				}
			}

			// If we found an upload ID, fetch the content
			if uploadID != "" {
				// Get the upload handler from the processing service
				uploadHandler := processingService.GetUploadHandler()
				if uploadHandler == nil {
					continue
				}

				// Get the processed content for this upload ID
				content, err := uploadHandler.GetProcessedContent(ctx, uploadID)
				if err != nil || content == "" {
					continue
				}

				// Replace the upload ID with the content in the variable value
				newValue := strings.Replace(strValue, "ATTACHMENT:"+uploadID, "ATTACHMENT:CONTENTS", 1)
				variables[key] = newValue
				
				// Also add a direct content variable for easier access
				contentKey := strings.Replace(key, "UPLOAD_ID", "CONTENTS", 1)
				variables[contentKey] = content
			}
		}
	}
}
