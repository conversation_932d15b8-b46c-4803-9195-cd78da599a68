package auth

import (
	"context"
	"errors"
	"log"

	"promz.ai/api/internal/contextkeys"
	"promz.ai/api/internal/repository/db"
)

// Common errors
var (
	ErrMissingAPIKey = errors.New("API key is required")
	ErrInvalidAPIKey = errors.New("Invalid API key")
)

// AuthResult contains authentication result data
type AuthResult struct {
	Valid       bool
	UserID      string
	LicenseType string
	APIKey      string
}

// AuthService provides authentication functionality for both REST and gRPC
type AuthService struct {
	dbClient db.SupabaseClientInterface
}

// NewAuthService creates a new authentication service
func NewAuthService(dbClient db.SupabaseClientInterface) *AuthService {
	return &AuthService{
		dbClient: dbClient,
	}
}

// VerifyAPIKey validates an API key and returns user information
func (s *AuthService) VerifyAPIKey(ctx context.Context, apiKey string) (*AuthResult, error) {
	// Check if API key is provided
	if apiKey == "" {
		log.Printf("API key is required")
		return nil, ErrMissingAPIKey
	}

	// Verify API key and get user information
	result, err := s.dbClient.VerifyAPIKeyEnhanced(ctx, apiKey)
	if err != nil {
		log.Printf("Error verifying API key: %v", err)
		return nil, ErrInvalidAPIKey
	}

	// Check if the API key is valid
	valid, ok := result["valid"].(bool)
	if !ok || !valid {
		log.Printf("Invalid API key")
		return nil, ErrInvalidAPIKey
	}

	// Extract user ID and license information
	userID, _ := result["user_id"].(string)
	licenseType, _ := result["license_type"].(string)

	if userID == "" {
		log.Printf("Missing user ID in API key verification result")
		return nil, ErrInvalidAPIKey
	}

	// Return auth result
	return &AuthResult{
		Valid:       true,
		UserID:      userID,
		LicenseType: licenseType,
		APIKey:      apiKey,
	}, nil
}

// CreateAuthContext creates a new context with authentication information
func (s *AuthService) CreateAuthContext(ctx context.Context, authResult *AuthResult) context.Context {
	// Create new context with user ID and license type
	newCtx := context.WithValue(ctx, contextkeys.UserIDKey, authResult.UserID)
	newCtx = context.WithValue(newCtx, contextkeys.LicenseTypeKey, authResult.LicenseType)
	newCtx = context.WithValue(newCtx, contextkeys.APIKeyKey, authResult.APIKey)
	return newCtx
}
