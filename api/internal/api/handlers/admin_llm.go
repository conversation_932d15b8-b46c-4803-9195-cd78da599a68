package handlers

import (
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"strings"
	"time"

	"promz.ai/api/utils/config"

	"promz.ai/api/llm"
)

type AdminLlmEditMarkdownRequest struct {
	Model       string `json:"model"`
	Content     string `json:"content"`
	Instruction string `json:"instruction"`
}

type DiffLine struct {
	Line   int    `json:"line"`
	Before string `json:"before"`
	After  string `json:"after"`
}

type AdminLlmEditMarkdownResponse struct {
	EditedContent string     `json:"edited_content"`
	Diff          []DiffLine `json:"diff"`
	Model         string     `json:"model"`
	Provider      string     `json:"provider"`
	ElapsedMs     int64      `json:"elapsed_ms"`
}

// HandleAdminLlmEditMarkdown handles the LLM markdown edit request for admin
// Assume cfg is available globally or injected; adjust as needed for your app
var cfg = config.New()

func HandleAdminLlmEditMarkdown(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	var req AdminLlmEditMarkdownRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		w.WriteHeader(http.StatusBadRequest)
		w.Write([]byte(`{"error": "invalid request"}`))
		return
	}

	provider := ""
	var apiKey string
	switch req.Model {
	case "Gemini":
		provider = "Google"
		apiKey = cfg.GoogleAPIKey
	case "ChatGPT":
		provider = "OpenAI"
		apiKey = cfg.OpenAIAPIKey
	default:
		w.WriteHeader(http.StatusBadRequest)
		w.Write([]byte(`{"error": "unsupported model"}`))
		return
	}

	// Call LLM provider (pseudo-code, replace with actual API integration)
	editedMarkdown, err := callLlmApi(provider, apiKey, req.Content, req.Instruction)
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		w.Write([]byte(`{"error": "LLM error: ` + err.Error() + `"}`))
		return
	}

	// Compute line-based diff, ignoring whitespace-only changes
	diffLines := computeLineDiff(req.Content, editedMarkdown)

	resp := AdminLlmEditMarkdownResponse{
		EditedContent: editedMarkdown,
		Diff:          diffLines,
		Model:         req.Model,
		Provider:      provider,
		ElapsedMs:     time.Since(start).Milliseconds(),
	}
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(resp)
}

// callLlmApi calls the appropriate LLM provider (Google or OpenAI) with the given content and instruction.
func callLlmApi(providerName, apiKey, content, instruction string) (string, error) {
	ctx := context.Background()
	prompt := content + "\n\nInstruction: " + instruction
	var provider llm.Provider
	var err error
	switch providerName {
	case "Gemini":
		provider, err = llm.NewGoogleAIProvider(llm.Config{APIKey: apiKey})
	case "ChatGPT":
		provider, err = llm.NewOpenAIProvider(llm.Config{APIKey: apiKey})
	default:
		return "", errors.New("unsupported provider: " + providerName)
	}
	if err != nil {
		return "", err
	}

	req := &llm.ExecuteRequest{
		PromptContent: prompt,
	}
	resp, err := provider.Execute(ctx, req)
	if err != nil {
		return "", err
	}
	return resp.Text, nil
}

// computeLineDiff returns a slice of DiffLine for changed lines, ignoring whitespace-only changes
func computeLineDiff(before, after string) []DiffLine {
	beforeLines := strings.Split(before, "\n")
	afterLines := strings.Split(after, "\n")
	maxLen := len(beforeLines)
	if len(afterLines) > maxLen {
		maxLen = len(afterLines)
	}
	var diffs []DiffLine
	for i := 0; i < maxLen; i++ {
		var b, a string
		if i < len(beforeLines) {
			b = strings.TrimSpace(beforeLines[i])
		}
		if i < len(afterLines) {
			a = strings.TrimSpace(afterLines[i])
		}
		if b != a {
			diffs = append(diffs, DiffLine{Line: i + 1, Before: b, After: a})
		}
	}
	return diffs
}
