package handler

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"promz.ai/api/services"
)

// YouTubeHandler handles YouTube-related API requests
type YouTube<PERSON>andler struct {
	transcriptService services.TranscriptServiceInterface
}

// YouTubeRoutesHandler creates a new YouTube handler
func YouTubeRoutesHandler(transcriptService services.TranscriptServiceInterface) *YouTubeHandler {
	return &YouTubeHandler{
		transcriptService: transcriptService,
	}
}

// GetTranscript handles requests for YouTube transcripts
func (h *YouTubeHandler) GetTranscript(c *gin.Context) {
	videoURL := c.Query("url")
	if videoURL == "" {
		c.J<PERSON>(http.StatusBadRequest, gin.H{
			"error": "Missing video URL",
		})
		return
	}

	lang := c.Default<PERSON>y("lang", "en")
	country := c.<PERSON>("country", "US")

	transcript, title, err := h.transcriptService.GetTranscript(videoURL, lang, country)
	if err != nil {
		c.<PERSON>(http.StatusInternalServerError, gin.H{
			"error": err.Error(),
		})
		return
	}

	c.<PERSON>(http.StatusOK, gin.H{
		"title":      title,
		"transcript": transcript,
	})
}

// GetTranscriptText handles requests for YouTube transcript as plain text
func (h *YouTubeHandler) GetTranscriptText(c *gin.Context) {
	videoURL := c.Query("url")
	if videoURL == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Missing video URL",
		})
		return
	}

	lang := c.DefaultQuery("lang", "en")
	country := c.DefaultQuery("country", "US")

	text, err := h.transcriptService.GetFullTranscriptText(videoURL, lang, country)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"text": text,
	})
}
