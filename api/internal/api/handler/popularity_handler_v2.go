package handler

import (
	"fmt"
	"log"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"promz.ai/api/internal/repository/db"
	"promz.ai/api/services"
)

// WebSocketBroadcaster defines the interface for broadcasting messages via WebSocket
type WebSocketBroadcaster interface {
	BroadcastToTopic(topic string, payload interface{})
}

// PopularityHandlerV2 handles routes related to prompt popularity with WebSocket support
type PopularityHandlerV2 struct {
	popularityService *services.PopularityServiceV2
}

// PopularityRoutesHandlerV2 creates a new handler for popularity routes with WebSocket support
func PopularityRoutesHandlerV2(database db.SupabaseClientInterface, broadcaster WebSocketBroadcaster) *PopularityHandlerV2 {
	return &PopularityHandlerV2{
		popularityService: services.NewPopularityServiceV2(database, broadcaster),
	}
}

// RecordPromptUsage handles requests to record prompt usage events
func (h *PopularityHandlerV2) RecordPromptUsage(c *gin.Context) {
	var events []services.PromptUsageEvent
	if err := c.Should<PERSON>(&events); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("Invalid request: %v", err)})
		return
	}

	// Validate events
	for i, event := range events {
		if event.PromptID == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("Event at index %d is missing prompt_id", i)})
			return
		}

		if event.EventType != "selected" && event.EventType != "executed" {
			c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("Event at index %d has invalid event_type (must be 'selected' or 'executed')", i)})
			return
		}

		// Set timestamp to current time if not provided
		if event.Timestamp.IsZero() {
			events[i].Timestamp = time.Now()
		}
	}

	// Record the usage events
	if err := h.popularityService.RecordUsageEvents(c, events); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to record usage events: %v", err)})
		return
	}

	c.Status(http.StatusNoContent)
}

// GetPopularPrompts handles requests to get popular prompts with complete data
func (h *PopularityHandlerV2) GetPopularPrompts(c *gin.Context) {
	// Parse limit parameter
	limitStr := c.DefaultQuery("limit", "5")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit < 1 {
		limit = 5 // Default limit
	}

	// Get popular prompts with data
	prompts, err := h.popularityService.GetPopularPrompts(c.Request.Context(), limit)
	if err != nil {
		log.Printf("Error getting popular prompts with data: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get popular prompts"})
		return
	}

	if len(prompts) == 0 {
		c.JSON(http.StatusOK, gin.H{"popular_prompts": []map[string]interface{}{}})
		return
	}

	// Log the first prompt to help with debugging
	log.Printf("GetPopularPrompts: Returning (%d); First prompt: %v", len(prompts), prompts[0])
	c.JSON(http.StatusOK, gin.H{"popular_prompts": prompts})
}

// UpdatePopularPromptsCache manually triggers an update of the popular prompts cache
func (h *PopularityHandlerV2) UpdatePopularPromptsCache(c *gin.Context) {
	// Parse days parameter
	daysStr := c.DefaultQuery("days", "30")
	days, err := strconv.Atoi(daysStr)
	if err != nil || days < 1 {
		days = 30 // Default to 30 days
	}

	// Update the cache in a goroutine to avoid blocking the request
	go func() {
		if err := h.popularityService.UpdatePopularPromptsCache(c.Request.Context(), days); err != nil {
			log.Printf("Error updating popular prompts cache: %v", err)
		}
	}()

	c.JSON(http.StatusOK, gin.H{"message": "Popular prompts cache update triggered"})
}
