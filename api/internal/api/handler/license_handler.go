package handler

import (
	"context"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"promz.ai/api/internal/repository/db"
)

// LicenseResponse represents the standardized license response format
type LicenseResponse struct {
	Valid         bool      `json:"valid"`
	LicenseID     string    `json:"license_id,omitempty"`
	UserID        string    `json:"user_id,omitempty"`
	LicenseType   string    `json:"license_type,omitempty"`
	IsActive      bool      `json:"is_active,omitempty"`
	ExpiryDate    time.Time `json:"expiry_date,omitempty"`
	IsFree        bool      `json:"is_free,omitempty"`
	Reason        string    `json:"reason,omitempty"`
	ErrorCode     string    `json:"error_code,omitempty"`
	DaysRemaining int       `json:"days_remaining,omitempty"`
}

// LicenseHandler handles license-related API endpoints with improved functionality
type LicenseHandler struct {
	supabase db.SupabaseClientInterface
}

// LicenseRoutesHandler creates a new license handler
func LicenseRoutesHandler(supabase db.SupabaseClientInterface) *LicenseHandler {
	return &LicenseHandler{
		supabase: supabase,
	}
}

// VerifyAPIKey verifies if an API key is valid with enhanced response data
func (h *LicenseHandler) VerifyAPIKey(c *gin.Context) {
	// Get the API key from query parameters
	apiKey := c.Query("api_key")
	if apiKey == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"valid":      false,
			"reason":     "invalid_request",
			"error_code": "INVALID_REQUEST",
			"error":      "api_key query parameter is required",
		})
		return
	}

	// Set a timeout context for the verification
	ctx, cancel := context.WithTimeout(c.Request.Context(), 5*time.Second)
	defer cancel()

	// Use the Supabase client to verify the API key with enhanced data
	result, err := h.supabase.VerifyAPIKeyEnhanced(ctx, apiKey)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"valid":      false,
			"reason":     "server_error",
			"error_code": "SERVER_ERROR",
			"error":      err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, result)
}

// CheckLicenseStatus checks a user's license status without requiring the API key
func (h *LicenseHandler) CheckLicenseStatus(c *gin.Context) {
	// Get the user ID from query parameters
	userID := c.Query("user_id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"has_license": false,
			"reason":      "invalid_request",
			"error_code":  "INVALID_REQUEST",
			"error":       "user_id query parameter is required",
		})
		return
	}

	// Set a timeout context for the verification
	ctx, cancel := context.WithTimeout(c.Request.Context(), 5*time.Second)
	defer cancel()

	// Use the Supabase client to check license status
	result, err := h.supabase.CheckLicenseStatus(ctx, userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"has_license": false,
			"reason":      "server_error",
			"error_code":  "SERVER_ERROR",
			"error":       err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, result)
}

// GetOrCreateLicense gets an existing license or creates a new license
func (h *LicenseHandler) GetOrCreateLicense(c *gin.Context, licenseType string) {
	// Get the user ID from query parameters
	userID := c.Query("user_id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":      "Invalid request",
			"error_code": "INVALID_REQUEST",
			"details":    "user_id query parameter is required",
		})
		return
	}

	// Set a timeout context for the operation
	ctx, cancel := context.WithTimeout(c.Request.Context(), 5*time.Second)
	defer cancel()

	// Use the Supabase client to get or create a license
	daysValid := 365 * 80 // valid for ~80 years
	if licenseType == "trial" {
		daysValid = 31 // valid for 31 days
	}
	result, err := h.supabase.GetOrCreateLicenseEnhanced(ctx, userID, licenseType, daysValid)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":      "Server error",
			"error_code": "SERVER_ERROR",
			"details":    err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, result)
}
