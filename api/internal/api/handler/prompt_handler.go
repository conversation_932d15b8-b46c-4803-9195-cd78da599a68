package handler

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"promz.ai/api/internal/repository/db"
	"promz.ai/api/llm"
)

// PromptHandler handles prompt related requests
type PromptHandler struct {
	database db.SupabaseClientInterface
	executor *llm.Executor
}

// PromptRoutesHandler creates a new prompt handler
func PromptRoutesHandler(database db.SupabaseClientInterface, executor *llm.Executor) *PromptHandler {
	return &PromptHandler{
		database: database,
		executor: executor,
	}
}

// List lists all prompts with pagination support
func (h *PromptHandler) List(c *gin.Context) {
	// Parse pagination parameters
	page := 1
	limit := 50
	
	// Parse page parameter if provided
	pageParam := c.<PERSON>ult<PERSON>("page", "1")
	if pageInt, err := strconv.Atoi(pageParam); err == nil && pageInt > 0 {
		page = pageInt
	}
	
	// Parse limit parameter if provided
	limitParam := c.DefaultQuery("limit", "50")
	if limitInt, err := strconv.Atoi(limitParam); err == nil && limitInt > 0 && limitInt <= 100 {
		limit = limitInt
	}
	
	// Calculate offset
	offset := (page - 1) * limit
	
	// Get prompts from repository with pagination
	prompts, err := h.database.GetPrompts(c.Request.Context(), limit, offset)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve prompts"})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"prompts": prompts,
	})
}

// Create creates a new prompt
func (h *PromptHandler) Create(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"message": "Create a new prompt",
	})
}

// Get gets a prompt by ID
func (h *PromptHandler) Get(c *gin.Context) {
	id := c.Param("id")
	c.JSON(http.StatusOK, gin.H{
		"message": "Get prompt by ID: " + id,
	})
}

// Update updates a prompt by ID
func (h *PromptHandler) Update(c *gin.Context) {
	id := c.Param("id")
	c.JSON(http.StatusOK, gin.H{
		"message": "Update prompt by ID: " + id,
	})
}

// Delete deletes a prompt by ID
func (h *PromptHandler) Delete(c *gin.Context) {
	id := c.Param("id")
	c.JSON(http.StatusOK, gin.H{
		"message": "Delete prompt by ID: " + id,
	})
}

// GetPromptInstructions gets the instructions for a prompt
func (h *PromptHandler) GetPromptInstructions(c *gin.Context) {
	id := c.Param("id")
	c.JSON(http.StatusOK, gin.H{
		"message": "Get instructions for prompt ID: " + id,
	})
}

// UpdatePromptInstructions updates the instructions for a prompt
func (h *PromptHandler) UpdatePromptInstructions(c *gin.Context) {
	id := c.Param("id")
	c.JSON(http.StatusOK, gin.H{
		"message": "Update instructions for prompt ID: " + id,
	})
}
