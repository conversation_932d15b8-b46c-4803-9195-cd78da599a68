package handler

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"promz.ai/api/internal/api/models"
	"promz.ai/api/internal/repository/db"
	"promz.ai/api/llm"
)

// LLMHandler handles LLM requests
type <PERSON><PERSON><PERSON><PERSON><PERSON> struct {
	database db.SupabaseClientInterface
	executor *llm.Executor
}

// LLMRoutesHandler creates a new LLM handler
func LLMRoutesHandler(database db.SupabaseClientInterface, executor *llm.Executor) *LLMHandler {
	return &LLMHandler{
		database: database,
		executor: executor,
	}
}

// Execute executes an LLM request
func (h *LLMHandler) Execute(c *gin.Context) {
	var clientReq models.LLMRequest
	if err := c.ShouldBindJSON(&clientReq); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Map client request to internal execute request
	// Note: We do NOT pass any client-provided API key to the LLM providers
	internalReq := &llm.ExecuteRequest{
		PromptID:      clientReq.PromptID,
		CategoryID:    clientReq.CategoryID,
		PromptContent: clientReq.PromptContent,
		MaxTokens:     clientReq.MaxTokens,
		Temperature:   clientReq.Temperature,
		Provider:      clientReq.Provider,
		Options:       clientReq.Options,
		Variables:     clientReq.Variables, // Pass variables from client request
	}

	// Map source content if provided
	if len(clientReq.SourceContent) > 0 {
		internalReq.SourceContent = make([]llm.SourceContent, 0, len(clientReq.SourceContent))
		for _, src := range clientReq.SourceContent {
			internalReq.SourceContent = append(internalReq.SourceContent, llm.SourceContent{
				Type:        src.Type,
				Content:     src.Content,
				FileName:    src.FileName,
				ContentType: src.ContentType,
			})
		}
	}

	resp, err := h.executor.ExecuteWithFallback(c.Request.Context(), internalReq)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, resp)
}
