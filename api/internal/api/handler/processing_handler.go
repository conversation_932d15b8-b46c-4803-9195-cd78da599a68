package handler

import (
	"context"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"time"

	"github.com/google/uuid"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"promz.ai/api/internal/contextkeys"
	pb "promz.ai/api/proto/gen"
	"promz.ai/api/services/processing"
)

// --- gRPC Service Implementation ---

// GrpcProcessingHandler implements the pb.ContentUploadServiceServer interface.
type GrpcProcessingHandler struct {
	pb.UnimplementedContentUploadServiceServer                             // Embed for forward compatibility
	storageService                             processing.StorageService   // Dependency for GetUploadStatus
	compositeProcessor                         processing.ContentProcessor // Correct interface type
	jobStore                                   processing.JobStore         // Add JobStore for live status
}

// NewGrpcProcessingHandler creates a new handler for gRPC processing service.
func NewGrpcProcessingHandler(storageService processing.StorageService, processor processing.ContentProcessor, jobStore processing.JobStore) *GrpcProcessingHandler {
	return &GrpcProcessingHandler{
		storageService:     storageService,
		compositeProcessor: processor,
		jobStore:           jobStore,
	}
}

// UploadFile handles gRPC requests to process a file.
// NOTE: This is currently Unary. Consider streaming for large files.
func (h *GrpcProcessingHandler) UploadFile(ctx context.Context, req *pb.UploadFileRequest) (*pb.ProcessingResult, error) {
	log.Printf("Received UploadFile gRPC request for file: %s", req.FileName)

	// 1. Extract User ID from context value using helper
	userID, ok := contextkeys.GetUserID(ctx)
	if !ok {
		log.Println("ERROR: Missing or invalid user_id in gRPC context value")
		return nil, status.Error(codes.Unauthenticated, "user_id not found in context")
	}
	log.Printf("Extracted UserID from context: %s", userID)

	// 2. Generate Job ID
	jobID := uuid.NewString()
	log.Printf("Generated Job ID: %s", jobID)

	// 3. Handle file content - Save temporarily
	if len(req.FileContent) == 0 {
		return nil, status.Error(codes.InvalidArgument, "FileContent is empty")
	}

	// Create temp file path
	tempDir := os.TempDir()
	// Use Job ID in temp name to avoid collisions, keep original extension if possible
	tempFileName := jobID + "-" + filepath.Base(req.FileName)
	tempFilePath := filepath.Join(tempDir, tempFileName)

	// Write content to temp file
	err := os.WriteFile(tempFilePath, req.FileContent, 0644)
	if err != nil {
		log.Printf("ERROR: Failed to write temp file %s: %v", tempFilePath, err)
		return nil, status.Error(codes.Internal, "failed to save temporary file")
	}
	log.Printf("Successfully saved request content to temp file: %s", tempFilePath)
	defer func() { // Ensure temporary file cleanup
		log.Printf("Attempting to remove temp file: %s", tempFilePath)
		err := os.Remove(tempFilePath)
		if err != nil && !os.IsNotExist(err) {
			log.Printf("WARNING: Failed to remove temp file %s: %v", tempFilePath, err)
		} else if err == nil {
			log.Printf("Successfully removed temp file: %s", tempFilePath)
		}
	}()

	// 4. Upload temp file to storage
	// Define storage object name (e.g., "uploads/{userID}/{jobID}/{fileName}")
	objectName := fmt.Sprintf("uploads/%s/%s/%s", userID, jobID, filepath.Base(req.FileName))

	// TODO: Determine correct expiration policy (e.g., based on license)
	expirationDays := 7 // Using 7 days default for now
	log.Printf("Uploading temp file %s to storage as %s with %d day expiration", tempFilePath, objectName, expirationDays)
	err = h.storageService.UploadFile(ctx, tempFilePath, objectName, expirationDays)
	if err != nil {
		log.Printf("ERROR: Failed to upload file to storage %s: %v", objectName, err)
		return nil, status.Error(codes.Internal, "failed to upload file to storage")
	}
	log.Printf("Successfully uploaded file to storage: %s", objectName)

	// 5. Map Request fields to Job Metadata (required for ProcessingJob)
	jobMetadata := make(map[string]interface{})
	// Add explicit fields from the request to the job metadata
	if req.FileName != "" {
		jobMetadata["fileName"] = req.FileName
	}
	if req.MimeType != "" {
		jobMetadata["mimeType"] = req.MimeType
	}
	if req.FilePath != "" {
		jobMetadata["filePath"] = req.FilePath
	}
	if req.ProcessingType != "" {
		jobMetadata["processingType"] = req.ProcessingType
	}

	// 6. Create ProcessingJob struct
	now := time.Now()
	// TODO: Calculate ExpiresAt based on a policy (e.g., license tier + duration)
	expiresAt := now.Add(time.Duration(expirationDays) * 24 * time.Hour)

	job := &processing.ProcessingJob{
		ID:          jobID,
		UserID:      userID,
		FileName:    req.FileName, // Keep original filename
		MimeType:    req.MimeType,
		FilePath:    objectName, // Path in storage
		FileSize:    int64(len(req.FileContent)),
		LicenseTier: req.LicenseTier,
		Status:      processing.StatusQueued, // Initial status
		CreatedAt:   now,
		UpdatedAt:   now,
		ExpiresAt:   expiresAt,
		Metadata:    jobMetadata,
		// MaxTokens, ContentType, ProcessingType etc., might be set later by processor
	}

	// 6.1. Add the newly created job to the JobStore for live tracking
	if h.jobStore != nil { // Check if jobStore was provided
		h.jobStore.AddJob(job)
		log.Printf("Added Job ID %s to JobStore with initial status: %s", jobID, job.Status)
	} else {
		log.Printf("Warning: JobStore is nil in GrpcProcessingHandler. Cannot track live status for Job ID %s", jobID)
	}

	// 7. Call the composite processor to start/queue the job
	// Assuming ProcessJob handles queuing/async execution and returns initial result
	log.Printf("Calling CompositeProcessor.ProcessJob for Job ID: %s", jobID)
	initialResult, err := h.compositeProcessor.ProcessJob(ctx, job)
	if err != nil {
		log.Printf("ERROR: Failed to process job %s: %v", jobID, err)
		// Consider deleting the uploaded file from storage on initiation failure?
		return nil, status.Error(codes.Internal, "failed to initiate file processing")
	}
	log.Printf("ProcessJob call completed successfully for Job ID: %s. Result ID (if applicable): %s", jobID, initialResult.ID)

	// 8. Map initial local result to protobuf result and return
	// The processor might return minimal info initially (like its own ID)
	// We primarily return the JobID we generated and the QUEUED status.
	protoResult := &pb.ProcessingResult{
		JobId:       jobID, // Use the Job ID we generated for the client to track
		Status:      pb.UploadStatus_UPLOAD_STATUS_QUEUED,
		ContentType: initialResult.ContentType, // Map from initial result if available
		// Other fields like Content, ErrorMessage will be populated on GetUploadStatus
	}

	log.Printf("Returning initial gRPC result for Job ID: %s, Status: %s", protoResult.JobId, protoResult.Status)
	return protoResult, nil
}

// GetUploadStatus handles gRPC requests for job status and results.
func (h *GrpcProcessingHandler) GetUploadStatus(ctx context.Context, req *pb.UploadStatusRequest) (*pb.ProcessingResult, error) {
	log.Printf("gRPC GetUploadStatus called for Job ID: %s", req.JobId)

	if req.JobId == "" {
		return nil, status.Errorf(codes.InvalidArgument, "Job ID cannot be empty")
	}

	// Directly fetch the result from storage service
	protoResult, err := h.storageService.GetResult(ctx, req.JobId)
	if err != nil {
		// TODO: Map storage errors (e.g., not found) to appropriate gRPC codes
		log.Printf("Error getting result for job %s from storage: %v", req.JobId, err)
		// Example: Map specific errors
		if err.Error() == "object not found" { // Crude check, improve this
			return nil, status.Errorf(codes.NotFound, "Job ID %s not found", req.JobId)
		}
		return nil, status.Errorf(codes.Internal, "Failed to retrieve job status: %v", err)
	}

	// Return the protobuf result directly.
	return protoResult, nil
}

// StreamUploadUpdates handles gRPC requests for streaming updates.
func (h *GrpcProcessingHandler) StreamUploadUpdates(req *pb.UploadStatusRequest, stream pb.ContentUploadService_StreamUploadUpdatesServer) error {
	jobID := req.JobId
	if jobID == "" {
		return status.Error(codes.InvalidArgument, "JobId cannot be empty")
	}
	log.Printf("Received StreamUploadUpdates request for Job ID: %s", jobID)

	// Extract User ID for authorization using helper
	userID, ok := contextkeys.GetUserID(stream.Context())
	if !ok {
		log.Printf("[Stream %s] ERROR: Missing or invalid user_id in gRPC context value", jobID)
		return status.Error(codes.Unauthenticated, "user_id not found in context")
	}
	log.Printf("[Stream %s] Authorized for UserID: %s", jobID, userID)

	// Polling Setup
	ticker := time.NewTicker(2 * time.Second) // Poll every 2 seconds
	defer ticker.Stop()

	for {
		select {
		case <-stream.Context().Done():
			log.Printf("[Stream %s] Client disconnected. Context Done: %v", jobID, stream.Context().Err())
			return stream.Context().Err() // Respect client disconnection

		case <-ticker.C:
			// Poll the JobStore for the current job status
			job, found := h.jobStore.GetJob(jobID)
			if !found {
				// Job not found in the live store. It's likely completed/failed and removed,
				// or the job ID was invalid from the start.
				log.Printf("[Stream %s] Job not found in JobStore. Assuming finished or invalid. Closing stream.", jobID)
				// Send one last known status? Or maybe query StorageService for final result?
				// For now, just gracefully close the stream.
				// Consider sending a specific 'NotFound' or 'Completed' message before returning.
				return nil // Clean exit as job is no longer actively tracked.
			}

			// Map processing.ProcessingJob to pb.UploadUpdate
			update := processing.MapProcessingJobToProtoUpdate(job) // Use exported helper func
			if update == nil {                                      // Should not happen if job is not nil, but check anyway
				log.Printf("[Stream %s] Warning: MapProcessingJobToProtoUpdate returned nil for non-nil job", jobID)
				continue // Skip this tick if mapping failed
			}

			// Send the update
			if err := stream.Send(update); err != nil {
				log.Printf("[Stream %s] Error sending stream update: %v", jobID, err)
				// If send fails, the stream is likely broken, so return the error
				return err
			}

			// Check for terminal status
			if job.Status == processing.StatusCompleted || job.Status == processing.StatusFailed {
				log.Printf("[Stream %s] Reached terminal status: %s. Closing stream.", jobID, job.Status)
				return nil // Success, stream finished
			}
		}
	}
}
