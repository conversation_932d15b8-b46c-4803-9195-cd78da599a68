package handler

import (
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"promz.ai/api/services"
)

// MockTranscriptService is a mock implementation of the TranscriptService
type MockTranscriptService struct {
	mock.Mock
}

func (m *MockTranscriptService) GetTranscript(videoURL, lang, country string) ([]services.TranscriptData, string, error) {
	args := m.Called(videoURL, lang, country)
	return args.Get(0).([]services.TranscriptData), args.String(1), args.Error(2)
}

func (m *MockTranscriptService) GetFullTranscriptText(videoURL, lang, country string) (string, error) {
	args := m.Called(videoURL, lang, country)
	return args.String(0), args.Error(1)
}

func (m *MockTranscriptService) CleanCache() {
	m.Called()
}

func setupYouTubeHandler() (*gin.Engine, *MockTranscriptService) {
	gin.SetMode(gin.TestMode)
	r := gin.New()

	mockService := new(MockTranscriptService)
	handler := YouTubeRoutesHandler(mockService)

	r.GET("/yt/xs", handler.GetTranscript)
	r.GET("/yt/xs/text", handler.GetTranscriptText)

	return r, mockService
}

func TestGetTranscript(t *testing.T) {
	r, mockService := setupYouTubeHandler()

	// Test case 1: Missing URL parameter
	req := httptest.NewRequest("GET", "/yt/xs", nil)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusBadRequest, w.Code)
	var response map[string]string
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Contains(t, response, "error")

	// Test case 2: Successful transcript retrieval
	mockTranscript := []services.TranscriptData{
		{
			Text:     "Test transcript",
			Duration: 1000,
			Offset:   0,
		},
	}
	mockService.On("GetTranscript", "https://www.youtube.com/watch?v=dQw4w9WgXcQ", "en", "US").
		Return(mockTranscript, "Test Video", nil)

	req = httptest.NewRequest("GET", "/yt/xs?url=https://www.youtube.com/watch?v=dQw4w9WgXcQ", nil)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	var successResponse map[string]interface{}
	err = json.Unmarshal(w.Body.Bytes(), &successResponse)
	assert.NoError(t, err)
	assert.Equal(t, "Test Video", successResponse["title"])
	assert.NotNil(t, successResponse["transcript"])

	// Test case 3: Error from service
	mockService.On("GetTranscript", "https://www.youtube.com/watch?v=invalid", "en", "US").
		Return([]services.TranscriptData{}, "", errors.New("invalid video ID"))

	req = httptest.NewRequest("GET", "/yt/xs?url=https://www.youtube.com/watch?v=invalid", nil)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusInternalServerError, w.Code)
	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Contains(t, response, "error")
}

func TestGetTranscriptText(t *testing.T) {
	r, mockService := setupYouTubeHandler()

	// Test case 1: Missing URL parameter
	req := httptest.NewRequest("GET", "/yt/xs/text", nil)
	w := httptest.NewRecorder()
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusBadRequest, w.Code)
	var response map[string]string
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Contains(t, response, "error")

	// Test case 2: Successful transcript text retrieval
	mockService.On("GetFullTranscriptText", "https://www.youtube.com/watch?v=dQw4w9WgXcQ", "en", "US").
		Return("This is the full transcript text.", nil)

	req = httptest.NewRequest("GET", "/yt/xs/text?url=https://www.youtube.com/watch?v=dQw4w9WgXcQ", nil)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
	var successResponse map[string]string
	err = json.Unmarshal(w.Body.Bytes(), &successResponse)
	assert.NoError(t, err)
	assert.Equal(t, "This is the full transcript text.", successResponse["text"])

	// Test case 3: Error from service
	mockService.On("GetFullTranscriptText", "https://www.youtube.com/watch?v=invalid", "en", "US").
		Return("", errors.New("invalid video ID"))

	req = httptest.NewRequest("GET", "/yt/xs/text?url=https://www.youtube.com/watch?v=invalid", nil)
	w = httptest.NewRecorder()
	r.ServeHTTP(w, req)

	assert.Equal(t, http.StatusInternalServerError, w.Code)
	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Contains(t, response, "error")
}
