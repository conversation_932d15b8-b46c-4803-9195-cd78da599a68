package handler

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

// HealthHandler provides a simple endpoint to verify server connectivity
type HealthHandler struct{}

// HealthRoutesHandler creates a new health handler
func HealthRoutesHandler() *HealthHandler {
	return &HealthHandler{}
}

// Check handles the health check endpoint
func (h *HealthHandler) Check(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status": "ok",
	})
}
