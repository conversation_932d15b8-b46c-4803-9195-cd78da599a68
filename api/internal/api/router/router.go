package router

import (
	"log"
	"net"
	"net/http"

	"github.com/gin-gonic/gin"

	"promz.ai/api/internal/api/handler"
	"promz.ai/api/internal/api/middleware"
	"promz.ai/api/internal/api/server"
	"promz.ai/api/internal/repository/db"
	"promz.ai/api/llm"
	"promz.ai/api/services"
	"promz.ai/api/services/processing"
	"promz.ai/api/services/websocket"
	"promz.ai/api/utils/config"
)

// ServerSetup contains all server components
type ServerSetup struct {
	Router           *gin.Engine
	GrpcServer       *server.GrpcServer
	WebSocketHandler *websocket.WebSocketHandler
	GrpcListener     net.Listener
}

// Setup configures the router with all routes and middleware
func Setup(database db.SupabaseClientInterface, cfg *config.Config, executor *llm.Executor, processingService *processing.ContentProcessingService, storageService processing.StorageService, contentProcessor processing.ContentProcessor) (*ServerSetup, error) {
	if !cfg.Debug {
		gin.SetMode(gin.ReleaseMode)
	}

	r := gin.Default()

	// Configure trusted proxies
	if len(cfg.TrustedProxies) > 0 {
		// Use explicitly configured trusted proxies if available
		r.SetTrustedProxies(cfg.TrustedProxies)
	} else if cfg.IsProduction() {
		// In production with no explicit configuration, trust no proxies by default (most secure)
		r.SetTrustedProxies([]string{})
	} else {
		// In development with no explicit configuration, trust localhost
		r.SetTrustedProxies([]string{"127.0.0.1", "::1"})
	}

	// Add CORS middleware
	r.Use(middleware.CORSMiddleware())

	// Create popularity service; broadcaster will be set after WebSocketHandler creation
	popularityService := services.NewPopularityServiceV2(database, nil)

	// Create WebSocket handler for popularity service
	webSocketHandler := websocket.NewWebSocketHandler(popularityService)

	// Set WebSocketHandler as the broadcaster for the popularity service
	popularityService.SetBroadcaster(webSocketHandler)

	// Start the WebSocket handler in a goroutine
	go func() {
		log.Println("Starting WebSocket handler")
		webSocketHandler.Run()
	}()

	// Create handlers
	promptHandler := handler.PromptRoutesHandler(database, executor)
	healthHandler := handler.HealthRoutesHandler()
	llmHandler := handler.LLMRoutesHandler(database, executor)
	licenseHandler := handler.LicenseRoutesHandler(database)

	// Create popularity handler with WebSocket support
	popularityHandler := handler.PopularityRoutesHandlerV2(database, webSocketHandler)

	// Initialize transcript service
	transcriptService := services.NewTranscriptService()
	// The TranscriptService implements TranscriptServiceInterface
	youtubeHandler := handler.YouTubeRoutesHandler(transcriptService)

	// Health check
	r.GET("/health", healthHandler.Check)

	// License routes - using GET method for all endpoints for simplicity and consistency
	license := r.Group("/license")
	{
		license.GET("/status", licenseHandler.CheckLicenseStatus)
		license.GET("/verify", licenseHandler.VerifyAPIKey)
		license.GET("/get-or-create-free", func(c *gin.Context) {
			licenseHandler.GetOrCreateLicense(c, "free")
		})
		license.GET("/get-or-create-trial", func(c *gin.Context) {
			licenseHandler.GetOrCreateLicense(c, "trial")
		})
	}

	// WebSocket endpoint (requires authentication)
	r.GET("/ws", middleware.APIKeyAuth(database), webSocketHandler.HandleConnection)

	// Protected routes that require API key
	protected := r.Group("")
	protected.Use(middleware.APIKeyAuth(database))

	// Prompt routes
	prompts := protected.Group("/prompts")
	{
		prompts.GET("", promptHandler.List)
		prompts.POST("", promptHandler.Create)
		prompts.GET("/:id", promptHandler.Get)
		prompts.PUT("/:id", promptHandler.Update)
		prompts.DELETE("/:id", promptHandler.Delete)

		// Prompt instructions routes
		prompts.GET("/:id/instructions", promptHandler.GetPromptInstructions)
		prompts.PATCH("/:id/instructions", promptHandler.UpdatePromptInstructions)
		prompts.PUT("/:id/instructions", promptHandler.UpdatePromptInstructions)
	}

	// Usage routes
	usage := protected.Group("/usage")
	{
		usage.POST("/prompt", popularityHandler.RecordPromptUsage)
	}

	// LLM routes
	llm := protected.Group("/llm")
	{
		// Add attachment variable processor middleware to process attachment variables
		if processingService != nil {
			llm.Use(middleware.AttachmentVariableProcessor(processingService))
		}
		llm.POST("/execute", llmHandler.Execute)
	}

	// YouTube routes
	youtube := protected.Group("/yt")
	{
		youtube.GET("/xs", youtubeHandler.GetTranscript)
		youtube.GET("/xs/text", youtubeHandler.GetTranscriptText)
	}

	// Set up gRPC server
	grpcListener, err := net.Listen("tcp", cfg.GRPCAddress)
	if err != nil {
		log.Printf("Failed to listen for gRPC: %v", err)
		return nil, err
	}

	// Create gRPC server with database client for API key verification
	grpcServer := server.NewGrpcServer(
		cfg,
		processingService.GetUploadHandler(),
		webSocketHandler,
		storageService,
		contentProcessor,
		processingService.GetJobStore(),
		database, // Add the database client for API key verification
	)

	// Register gRPC services
	grpcServer.RegisterServices()

	// Register gRPC-Web handler with Gin
	grpcServer.RegisterWithGin(r)

	// Start gRPC server in a goroutine
	go func() {
		log.Printf("Starting gRPC server on %s", cfg.GRPCAddress)
		if err := grpcServer.Serve(grpcListener); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to serve gRPC: %v", err)
		}
	}()

	// Return server setup
	return &ServerSetup{
		Router:           r,
		GrpcServer:       grpcServer,
		WebSocketHandler: webSocketHandler,
		GrpcListener:     grpcListener,
	}, nil
}
