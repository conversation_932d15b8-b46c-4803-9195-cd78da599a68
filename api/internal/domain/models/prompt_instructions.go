package models

import "time"

// PromptInstructions represents the instructions for a prompt
type PromptInstructions struct {
	ID                  string                 `json:"id"`
	PromptID            string                 `json:"prompt_id"`
	InstructionTemplate string                 `json:"instruction_template,omitempty"`
	DetailedPrompt      string                 `json:"detailed_prompt,omitempty"`
	Variables           map[string]interface{} `json:"variables,omitempty"`
	Parameters          map[string]interface{} `json:"parameters,omitempty"`
	UsageNotes          string                 `json:"usage_notes,omitempty"`
	Version             int                    `json:"version"`
	CreatedAt           time.Time              `json:"created_at,omitempty"`
	UpdatedAt           time.Time              `json:"updated_at,omitempty"`
}

// GetPromptInstructionsResponse represents the response for getting prompt instructions
type GetPromptInstructionsResponse struct {
	ID                  string                 `json:"id,omitempty"`
	PromptID            string                 `json:"prompt_id"`
	InstructionTemplate string                 `json:"instruction_template,omitempty"`
	DetailedPrompt      string                 `json:"detailed_prompt,omitempty"`
	Variables           map[string]interface{} `json:"variables,omitempty"`
	Parameters          map[string]interface{} `json:"parameters,omitempty"`
	UsageNotes          string                 `json:"usage_notes,omitempty"`
	InheritFromCategory bool                   `json:"inherit_from_category"`
	Version             int                    `json:"version,omitempty"`
}

// UpdatePromptInstructionsRequest represents a request to update prompt instructions
type UpdatePromptInstructionsRequest struct {
	InstructionTemplate string                 `json:"instruction_template,omitempty"`
	DetailedPrompt      string                 `json:"detailed_prompt,omitempty"`
	Variables           map[string]interface{} `json:"variables,omitempty"`
	Parameters          map[string]interface{} `json:"parameters,omitempty"`
	UsageNotes          *string                `json:"usage_notes,omitempty"`
	InheritFromCategory *bool                  `json:"inherit_from_category,omitempty"`
	Version             int                    `json:"version,omitempty"`
}
