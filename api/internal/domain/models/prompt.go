package models

import "time"

// Prompt represents a prompt entity
type Prompt struct {
	ID           string                 `json:"id"`
	Title        string                 `json:"title"`
	CategoryID   *string                `json:"category_id,omitempty"`
	Subtitle     string                 `json:"subtitle,omitempty"`
	Source       string                 `json:"source"`
	Keywords     []string               `json:"keywords,omitempty"`
	Variables    []string               `json:"variables,omitempty"`
	Metadata     map[string]interface{} `json:"metadata,omitempty"`
	Version      int                    `json:"version"`
	IsSynced     bool                   `json:"is_synced"`
	CreatedAt    time.Time              `json:"created_at,omitempty"`
	UpdatedAt    time.Time              `json:"updated_at,omitempty"`
	LastUsedAt   *time.Time             `json:"last_used_at,omitempty"`
	ServerSyncAt *time.Time             `json:"server_sync_at,omitempty"`
}
