package db

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"time"

	"github.com/supabase-community/supabase-go"
	prompt "promz.ai/api/internal/domain/models"
)

var ErrPromptNotFound = errors.New("prompt not found")

// PromptUsageEvent represents a prompt usage event
type PromptUsageEvent struct {
	PromptID    string
	EventType   string
	UsedAt      time.Time
	CountryCode string
}

// SupabaseClientInterface defines the interface for Supabase client operations
type SupabaseClientInterface interface {
	VerifyAPIKey(ctx context.Context, apiKey string) bool
	VerifyAPIKeyEnhanced(ctx context.Context, apiKey string) (map[string]interface{}, error)
	CheckLicenseStatus(ctx context.Context, userID string) (map[string]interface{}, error)
	GetOrCreateLicenseEnhanced(ctx context.Context, userID string, licenseType string, daysValid int) (map[string]interface{}, error)
	CreatePrompt(ctx context.Context, p *prompt.Prompt) (string, error)
	GetPrompt(ctx context.Context, id string) (*prompt.Prompt, error)
	ListPrompts(ctx context.Context) ([]*prompt.Prompt, error)
	GetPrompts(ctx context.Context, limit, offset int) ([]prompt.Prompt, error)
	UpdatePrompt(ctx context.Context, id string, p *prompt.Prompt) error
	DeletePrompt(ctx context.Context, id string) error
	GetPromptInstructions(ctx context.Context, promptID string) (*prompt.PromptInstructions, error)
	UpdatePromptInstructions(ctx context.Context, promptID string, instructions *prompt.UpdatePromptInstructionsRequest) error
	RecordPromptUsageEvents(ctx context.Context, events []interface{}) error
	GetPopularPrompts(ctx context.Context, limit int) ([]interface{}, error)
	UpdatePopularPromptsCache(ctx context.Context, prompts []interface{}) error
	GetPromptUsageEvents(ctx context.Context, days int) ([]PromptUsageEvent, error)

	// LLM Model Selection methods
	GetEligibleModelsForTier(ctx context.Context, tierID string, preferredModel string) ([]interface{}, error)
	GetBestModelForProvider(ctx context.Context, tierID string, providerID string, preferredModel string) (map[string]interface{}, error)
}

// SupabaseClient wraps the Supabase client to provide database functionality
type SupabaseClient struct {
	client *supabase.Client
}

// NewSupabaseClient creates a new Supabase client
func NewSupabaseClient(url, key string) (*SupabaseClient, error) {
	client, err := supabase.NewClient(url, key, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create Supabase client: %w", err)
	}

	return &SupabaseClient{client: client}, nil
}

// VerifyAPIKey checks if the provided API key exists in Supabase vault
func (s *SupabaseClient) VerifyAPIKey(ctx context.Context, apiKey string) bool {
	log.Printf("Attempting to verify API key...")

	// Call the verify_api_key function with the updated parameter name
	response := s.client.Rpc("verify_api_key", "POST", map[string]interface{}{
		"p_api_key": apiKey,
	})

	// Parse the response
	var result map[string]interface{}
	if err := json.Unmarshal([]byte(response), &result); err != nil {
		log.Printf("Error parsing response: %v", err)
		return false
	}

	// Check if the API key is valid
	valid, ok := result["valid"].(bool)
	if !ok {
		log.Printf("Invalid response format, 'valid' field not found or not a boolean")
		return false
	}

	if !valid {
		reason, _ := result["reason"].(string)
		log.Printf("API key is invalid. Reason: %s", reason)
		return false
	}

	log.Printf("API key verified successfully")
	return true
}

// VerifyAPIKeyEnhanced checks if the provided API key is valid with enhanced response data
func (s *SupabaseClient) VerifyAPIKeyEnhanced(ctx context.Context, apiKey string) (map[string]interface{}, error) {
	log.Printf("Attempting to verify API key with enhanced data...")

	// Call the enhanced verify_api_key function
	response := s.client.Rpc("verify_api_key_enhanced", "POST", map[string]interface{}{
		"p_api_key": apiKey,
	})

	// Parse the response
	var result map[string]interface{}
	if err := json.Unmarshal([]byte(response), &result); err != nil {
		log.Printf("Error parsing response: %v", err)
		return nil, fmt.Errorf("failed to parse API key verification response: %w", err)
	}

	// Check for error in the response
	if _, hasError := result["error"]; hasError {
		log.Printf("Error in API key verification: %v", result["error"])
		return result, fmt.Errorf("API key verification error: %v", result["error"])
	}

	log.Printf("API key verification completed with enhanced data")
	return result, nil
}

// CheckLicenseStatus checks a user's license status without requiring the API key
func (s *SupabaseClient) CheckLicenseStatus(ctx context.Context, userID string) (map[string]interface{}, error) {
	// Call the check_license_status function
	response := s.client.Rpc("check_license_status", "POST", map[string]interface{}{
		"p_user_id": userID,
	})

	// Parse the response
	var result map[string]interface{}
	if err := json.Unmarshal([]byte(response), &result); err != nil {
		log.Printf("Error parsing response: %v", err)
		return nil, fmt.Errorf("failed to parse license status response: %w", err)
	}

	// Check for error in the response
	if _, hasError := result["error"]; hasError {
		log.Printf("Error checking license status: %v", result["error"])
		return result, fmt.Errorf("license status check error: %v", result["error"])
	}

	return result, nil
}

// GetOrCreateLicenseEnhanced gets an existing license or creates a new one with enhanced functionality
func (s *SupabaseClient) GetOrCreateLicenseEnhanced(ctx context.Context, userID string, licenseType string, daysValid int) (map[string]interface{}, error) {
	log.Printf("Getting or creating license for user %s, type %s...", userID, licenseType)

	// Call the get_or_create_license_enhanced function
	response := s.client.Rpc("get_or_create_license_enhanced", "POST", map[string]interface{}{
		"p_user_id":      userID,
		"p_license_type": licenseType,
		"p_days_valid":   daysValid,
	})

	// Parse the response
	var result map[string]interface{}
	if err := json.Unmarshal([]byte(response), &result); err != nil {
		log.Printf("Error parsing response: %v", err)
		return nil, fmt.Errorf("failed to parse license creation response: %w", err)
	}

	// Check for error in the response
	if _, hasError := result["error"]; hasError {
		log.Printf("Error creating/getting license: %v", result["error"])
		return result, fmt.Errorf("license creation error: %v", result["error"])
	}

	log.Printf("License get/create completed for user %s", userID)
	return result, nil
}

// CreatePrompt creates a new prompt in the database
func (s *SupabaseClient) CreatePrompt(ctx context.Context, p *prompt.Prompt) (string, error) {
	var result []map[string]interface{}

	// Create the data to insert
	data := map[string]interface{}{
		"title":    p.Title,
		"subtitle": p.Subtitle,
	}

	// Get the query builder but don't execute yet
	query := s.client.From("prompts").Insert(data, false, "", "", "")

	// Execute the query
	body, _, err := query.Execute()
	if err != nil {
		return "", fmt.Errorf("failed to create prompt: %w", err)
	}

	// Parse the response body
	if err := json.Unmarshal(body, &result); err != nil {
		return "", fmt.Errorf("failed to parse response: %w", err)
	}

	if len(result) == 0 {
		return "", fmt.Errorf("failed to create prompt: no data returned")
	}

	// Assuming the ID is returned in the first row, in the "id" column
	id, ok := result[0]["id"].(string)
	if !ok {
		return "", fmt.Errorf("failed to create prompt: id is not a string")
	}

	return id, nil
}

// GetPrompt retrieves a prompt by ID
func (s *SupabaseClient) GetPrompt(ctx context.Context, id string) (*prompt.Prompt, error) {
	var prompts []*prompt.Prompt

	// Build the query but don't execute yet
	query := s.client.From("prompts").Select("*", "", false).Eq("id", id).Limit(1, "")

	// Execute the query
	body, _, err := query.Execute()
	if err != nil {
		return nil, fmt.Errorf("failed to get prompt: %w", err)
	}

	// Parse the response body
	if err := json.Unmarshal(body, &prompts); err != nil {
		return nil, fmt.Errorf("failed to parse response: %w", err)
	}

	if len(prompts) == 0 {
		return nil, ErrPromptNotFound
	}

	return prompts[0], nil
}

// ListPrompts retrieves all prompts
func (s *SupabaseClient) ListPrompts(ctx context.Context) ([]*prompt.Prompt, error) {
	log.Printf("Listing all prompts...")

	// Query the prompts table
	var prompts []*prompt.Prompt

	// Build the query but don't execute yet
	query := s.client.From("prompts").Select("*", "", false)

	// Execute the query
	body, _, err := query.Execute()
	if err != nil {
		log.Printf("Error listing prompts: %v", err)
		return nil, fmt.Errorf("failed to list prompts: %w", err)
	}

	// Parse the response body
	if err := json.Unmarshal(body, &prompts); err != nil {
		log.Printf("Error parsing prompts: %v", err)
		return nil, fmt.Errorf("failed to parse prompts: %w", err)
	}

	log.Printf("Listed %d prompts", len(prompts))
	return prompts, nil
}

// GetPrompts retrieves a paginated list of prompts
func (s *SupabaseClient) GetPrompts(ctx context.Context, limit, offset int) ([]prompt.Prompt, error) {
	log.Printf("Getting prompts with pagination (limit: %d, offset: %d)...", limit, offset)

	// Query the prompts table with pagination
	var prompts []prompt.Prompt

	// Use RPC to call a stored procedure for pagination
	response := s.client.Rpc("get_prompts_paginated", "POST", map[string]interface{}{
		"p_limit":  limit,
		"p_offset": offset,
	})

	// Parse the response
	if err := json.Unmarshal([]byte(response), &prompts); err != nil {
		log.Printf("Error parsing prompts: %v", err)
		return nil, fmt.Errorf("failed to parse prompts: %w", err)
	}

	log.Printf("Retrieved %d prompts", len(prompts))
	return prompts, nil
}

// UpdatePrompt updates an existing prompt
func (s *SupabaseClient) UpdatePrompt(ctx context.Context, id string, p *prompt.Prompt) error {
	// Build the update data
	data := map[string]interface{}{
		"title":    p.Title,
		"subtitle": p.Subtitle,
	}

	// Build the query but don't execute yet
	query := s.client.From("prompts").Update(data, "", "").Eq("id", id)

	// Execute the query
	_, _, err := query.Execute()
	if err != nil {
		return fmt.Errorf("failed to update prompt: %w", err)
	}

	return nil
}

// DeletePrompt deletes a prompt
func (s *SupabaseClient) DeletePrompt(ctx context.Context, id string) error {
	// Build the query but don't execute yet
	query := s.client.From("prompts").Delete("", "").Eq("id", id)

	// Execute the query
	_, _, err := query.Execute()
	if err != nil {
		return fmt.Errorf("failed to delete prompt: %w", err)
	}

	return nil
}

// GetPromptInstructions retrieves instructions for a prompt by prompt ID
func (s *SupabaseClient) GetPromptInstructions(ctx context.Context, promptID string) (*prompt.PromptInstructions, error) {
	// Build the query but don't execute yet
	query := s.client.From("prompt_instructions").Select("*", "", false).Eq("prompt_id", promptID).Limit(1, "")

	// Execute the query
	body, _, err := query.Execute()

	if err != nil {
		log.Printf("Error executing query for prompt instructions: %v", err)
		return nil, fmt.Errorf("failed to get prompt instructions: %w", err)
	}

	// Handle empty response
	if len(body) == 0 || string(body) == "[]" {
		log.Printf("Empty response body for prompt ID: %s", promptID)
		return nil, nil
	}

	// Try to parse as array first
	var instructionsArray []map[string]interface{}
	if err := json.Unmarshal(body, &instructionsArray); err != nil {
		log.Printf("Error unmarshaling as array: %v", err)

		// Try to parse as single object
		var instructionObj map[string]interface{}
		if err := json.Unmarshal(body, &instructionObj); err != nil {
			log.Printf("Error unmarshaling as object: %v", err)
			return nil, fmt.Errorf("failed to parse response: %w", err)
		}

		// Convert single object to instruction
		if len(instructionObj) == 0 {
			return nil, nil
		}

		// Create instruction from map
		instruction := &prompt.PromptInstructions{}

		// Extract fields
		if id, ok := instructionObj["id"].(string); ok {
			instruction.ID = id
		}

		if promptID, ok := instructionObj["prompt_id"].(string); ok {
			instruction.PromptID = promptID
		}

		if template, ok := instructionObj["instruction_template"].(string); ok {
			instruction.InstructionTemplate = template
		}

		if detailed, ok := instructionObj["detailed_prompt"].(string); ok {
			instruction.DetailedPrompt = detailed
		}

		if variables, ok := instructionObj["variables"].(map[string]interface{}); ok {
			instruction.Variables = variables
		}

		if parameters, ok := instructionObj["parameters"].(map[string]interface{}); ok {
			instruction.Parameters = parameters
		}

		if notes, ok := instructionObj["usage_notes"].(string); ok {
			instruction.UsageNotes = notes
		}

		if version, ok := instructionObj["version"].(float64); ok {
			instruction.Version = int(version)
		}

		log.Printf("Successfully parsed instruction from object for prompt ID: %s", promptID)
		return instruction, nil
	}

	// Handle empty array
	if len(instructionsArray) == 0 {
		log.Printf("No instructions found for prompt ID: %s", promptID)
		return nil, nil
	}

	// Convert first array item to instruction
	instructionObj := instructionsArray[0]
	instruction := &prompt.PromptInstructions{}

	// Extract fields
	if id, ok := instructionObj["id"].(string); ok {
		instruction.ID = id
	}

	if promptID, ok := instructionObj["prompt_id"].(string); ok {
		instruction.PromptID = promptID
	}

	if template, ok := instructionObj["instruction_template"].(string); ok {
		instruction.InstructionTemplate = template
	}

	if detailed, ok := instructionObj["detailed_prompt"].(string); ok {
		instruction.DetailedPrompt = detailed
	}

	if variables, ok := instructionObj["variables"].(map[string]interface{}); ok {
		instruction.Variables = variables
	}

	if parameters, ok := instructionObj["parameters"].(map[string]interface{}); ok {
		instruction.Parameters = parameters
	}

	if notes, ok := instructionObj["usage_notes"].(string); ok {
		instruction.UsageNotes = notes
	}

	if version, ok := instructionObj["version"].(float64); ok {
		instruction.Version = int(version)
	}

	return instruction, nil
}

// UpdatePromptInstructions updates instructions for a prompt
func (s *SupabaseClient) UpdatePromptInstructions(ctx context.Context, promptID string, req *prompt.UpdatePromptInstructionsRequest) error {
	log.Printf("Updating prompt instructions for prompt ID: %s", promptID)

	// Check if instructions already exist for this prompt
	existing, err := s.GetPromptInstructions(ctx, promptID)
	if err != nil && err != ErrPromptNotFound {
		return fmt.Errorf("failed to check existing instructions: %w", err)
	}

	// Prepare data for update
	data := make(map[string]interface{})

	// Only set fields that are provided in the request
	if req.InstructionTemplate != "" {
		data["instruction_template"] = req.InstructionTemplate
		log.Printf("Setting instruction_template to: %s", req.InstructionTemplate)
	}

	if req.DetailedPrompt != "" {
		data["detailed_prompt"] = req.DetailedPrompt
		log.Printf("Setting detailed_prompt to: %s", req.DetailedPrompt)
	}

	if req.Variables != nil {
		data["variables"] = req.Variables
		log.Printf("Setting variables")
	}

	if req.Parameters != nil {
		data["parameters"] = req.Parameters
		log.Printf("Setting parameters")
	}

	if req.UsageNotes != nil {
		data["usage_notes"] = *req.UsageNotes
		log.Printf("Setting usage_notes to: %s", *req.UsageNotes)
	}

	if req.InheritFromCategory != nil {
		data["inherit_from_category"] = *req.InheritFromCategory
		log.Printf("Setting inherit_from_category to: %t", *req.InheritFromCategory)
	}

	// Always set the version field
	data["version"] = req.Version
	log.Printf("Setting version to: %d", req.Version)

	// Execute the query
	var execErr error

	if existing == nil {
		// For insert - make sure to include prompt_id for new instructions
		log.Printf("No existing instructions found, creating new ones")
		data["prompt_id"] = promptID
		insertResult := s.client.From("prompt_instructions").Insert(data, false, "", "", "")
		_, _, execErr = insertResult.Execute()
	} else {
		// For update - use direct method calls instead of type assertions
		log.Printf("Existing instructions found, updating them")
		updateResult := s.client.From("prompt_instructions").Update(data, "", "").Eq("prompt_id", promptID)
		_, _, execErr = updateResult.Execute()
	}

	if execErr != nil {
		log.Printf("Error executing query: %v", execErr)
		return fmt.Errorf("failed to update prompt instructions: %w", execErr)
	}

	log.Printf("Successfully updated instructions for prompt ID: %s", promptID)
	return nil
}

// RecordPromptUsageEvents records prompt usage events
func (s *SupabaseClient) RecordPromptUsageEvents(ctx context.Context, events []interface{}) error {
	// Call the record_prompt_usage_events function
	response := s.client.Rpc("record_prompt_usage_events", "POST", map[string]interface{}{
		"p_events": events,
	})

	// Parse the response
	var result map[string]interface{}
	if err := json.Unmarshal([]byte(response), &result); err != nil {
		log.Printf("Error parsing response: %v", err)
		return fmt.Errorf("failed to record prompt usage events: %w", err)
	}

	// Check for error in the response
	if _, hasError := result["error"]; hasError {
		log.Printf("Error recording prompt usage events: %v", result["error"])
		return fmt.Errorf("prompt usage events recording error: %v", result["error"])
	}

	log.Printf("Prompt usage events recorded successfully")
	return nil
}

// GetPopularPrompts retrieves popular prompts
func (s *SupabaseClient) GetPopularPrompts(ctx context.Context, limit int) ([]interface{}, error) {
	// Call the get_cached_popular_prompts function to retrieve from cache
	response := s.client.Rpc("get_cached_popular_prompts", "POST", map[string]interface{}{
		"p_limit": limit,
	})

	// Parse the response
	var result []interface{}
	if err := json.Unmarshal([]byte(response), &result); err != nil {
		log.Printf("Error parsing response: %v", err)
		return nil, fmt.Errorf("failed to get popular prompts: %w", err)
	}

	// Check for error in the response
	if len(result) == 0 {
		return nil, nil
	}

	return result, nil
}

// UpdatePopularPromptsCache updates the popular prompts cache in the database
func (s *SupabaseClient) UpdatePopularPromptsCache(ctx context.Context, prompts []interface{}) error {
	log.Printf("Updating popular prompts cache...")

	// Convert prompts to JSONB format expected by the SQL function
	promptsJSON, err := json.Marshal(prompts)
	if err != nil {
		log.Printf("Error marshaling prompts: %v", err)
		return fmt.Errorf("failed to marshal prompts: %w", err)
	}

	// Call the update_popular_prompts_cache function
	response := s.client.Rpc("update_popular_prompts_cache", "POST", map[string]interface{}{
		"p_prompts": json.RawMessage(promptsJSON),
	})

	// Parse the response
	var result map[string]interface{}
	if err := json.Unmarshal([]byte(response), &result); err != nil {
		log.Printf("Error parsing response: %v", err)
		return fmt.Errorf("failed to update popular prompts cache: %w", err)
	}

	// Check for error in the response
	if _, hasError := result["error"]; hasError {
		log.Printf("Error updating popular prompts cache: %v", result["error"])
		return fmt.Errorf("cache update error: %v", result["error"])
	}

	log.Printf("Popular prompts cache updated successfully")
	return nil
}

// GetPromptUsageEvents retrieves prompt usage events from the last N days
func (s *SupabaseClient) GetPromptUsageEvents(ctx context.Context, days int) ([]PromptUsageEvent, error) {
	log.Printf("Getting prompt usage events for the last %d days", days)

	// Use the Supabase RPC method to call a custom SQL query
	response := s.client.Rpc("get_prompt_usage_events", "POST", map[string]interface{}{
		"p_days": days,
	})

	// Check if response is empty
	if response == "" {
		log.Printf("Empty response from Supabase")
		return []PromptUsageEvent{}, nil
	}

	// Parse the response
	var rawEvents []map[string]interface{}
	if err := json.Unmarshal([]byte(response), &rawEvents); err != nil {
		log.Printf("Error parsing response: %v", err)
		return nil, fmt.Errorf("failed to parse prompt usage events: %w", err)
	}

	// Convert to our struct format
	events := make([]PromptUsageEvent, 0, len(rawEvents))
	for _, raw := range rawEvents {
		// Extract and convert fields
		promptID, _ := raw["prompt_id"].(string)
		eventType, _ := raw["event_type"].(string)

		// Parse usedAt
		var usedAt time.Time
		if ts, ok := raw["used_at"].(string); ok {
			var err error
			usedAt, err = time.Parse(time.RFC3339, ts)
			if err != nil {
				log.Printf("Warning: could not parse used_at %s: %v", ts, err)
				// Use current time as fallback
				usedAt = time.Now()
			}
		}

		countryCode, _ := raw["country_code"].(string)

		events = append(events, PromptUsageEvent{
			PromptID:    promptID,
			EventType:   eventType,
			UsedAt:      usedAt,
			CountryCode: countryCode,
		})
	}

	log.Printf("Retrieved %d prompt usage events for the last %d days", len(events), days)
	return events, nil
}

// GetEligibleModelsForTier retrieves all models available for a specific user tier
func (s *SupabaseClient) GetEligibleModelsForTier(ctx context.Context, tierID string, preferredModel string) ([]interface{}, error) {
	log.Printf("Getting eligible models for tier: %s with preferred model: %s", tierID, preferredModel)

	// Call the get_eligible_models_for_tier function
	response := s.client.Rpc("get_eligible_models_for_tier", "POST", map[string]interface{}{
		"p_tier_id":         tierID,
		"p_preferred_model": preferredModel,
	})

	// Check if response is empty
	if response == "" {
		log.Printf("Empty response from Supabase")
		return []interface{}{}, nil
	}

	// Parse the response
	var models []interface{}
	if err := json.Unmarshal([]byte(response), &models); err != nil {
		log.Printf("Error parsing response: %v", err)
		return nil, fmt.Errorf("failed to parse eligible models: %w", err)
	}

	log.Printf("Retrieved %d eligible models for tier %s", len(models), tierID)
	return models, nil
}

// GetBestModelForProvider retrieves the best model for a specific provider and tier
func (s *SupabaseClient) GetBestModelForProvider(ctx context.Context, tierID string, providerID string, preferredModel string) (map[string]interface{}, error) {
	log.Printf("Getting best model for provider %s and tier %s (preferred model: %s)", providerID, tierID, preferredModel)

	// Call the get_best_model_for_provider function
	response := s.client.Rpc("get_best_model_for_provider", "POST", map[string]interface{}{
		"p_tier_id":         tierID,
		"p_provider_id":     providerID,
		"p_preferred_model": preferredModel,
	})

	// Check if response is empty
	if response == "" || response == "[]" {
		log.Printf("No eligible models found")
		return nil, fmt.Errorf("no eligible %s models available for %s tier", providerID, tierID)
	}

	// Parse the response
	var models []map[string]interface{}
	if err := json.Unmarshal([]byte(response), &models); err != nil {
		// Try parsing as a single object
		var model map[string]interface{}
		if err := json.Unmarshal([]byte(response), &model); err != nil {
			log.Printf("Error parsing response: %v", err)
			return nil, fmt.Errorf("failed to parse model data: %w", err)
		}

		if len(model) == 0 {
			return nil, fmt.Errorf("no eligible %s models available for %s tier", providerID, tierID)
		}

		log.Printf("Retrieved best model for provider %s and tier %s: %v", providerID, tierID, model)
		return model, nil
	}

	if len(models) == 0 {
		return nil, fmt.Errorf("no eligible %s models available for %s tier", providerID, tierID)
	}

	log.Printf("Retrieved best model for provider %s and tier %s: %v", providerID, tierID, models[0])
	return models[0], nil
}
