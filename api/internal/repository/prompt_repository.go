package repository

import (
	"database/sql"

	prompt "promz.ai/api/internal/domain/models"
)

// PromptRepository defines the interface for prompt repository operations
type PromptRepository interface {
	GetPrompts(limit, offset int) ([]prompt.Prompt, error)
	GetPromptByID(id string) (*prompt.Prompt, error)
	CreatePrompt(prompt *prompt.Prompt) error
	UpdatePrompt(prompt *prompt.Prompt) error
	DeletePrompt(id string) error
}

// PostgresPromptRepository implements PromptRepository for PostgreSQL
type PostgresPromptRepository struct {
	DB *sql.DB
}
