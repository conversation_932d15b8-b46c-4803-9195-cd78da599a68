package main

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"

	_ "github.com/lib/pq"
	"promz.ai/api/internal/api/router"
	"promz.ai/api/internal/repository/db"
	"promz.ai/api/llm"
	"promz.ai/api/services"
	"promz.ai/api/services/processing"
	"promz.ai/api/utils/config"
)

// MockProvider is a simple mock LLM provider for development/testing
type MockProvider struct{}

func (p *MockProvider) Name() string {
	return "MockProvider"
}

func (p *MockProvider) IsAvailable(ctx context.Context) bool {
	return true
}

func (p *MockProvider) Execute(ctx context.Context, req *llm.ExecuteRequest) (*llm.ExecuteResponse, error) {
	return &llm.ExecuteResponse{
		Text:         "This is a mock response. Please configure a real LLM provider by setting PROMZ_OPENAI_API_KEY or PROMZ_GOOGLE_API_KEY.",
		Provider:     "MockProvider",
		Metadata:     map[string]string{"type": "mock"},
		Tokens:       0,
		FinishReason: "mock",
	}, nil
}

// DetailsMockProvider is a mock provider that displays request details instead of running through an LLM
type DetailsMockProvider struct{}

func (p *DetailsMockProvider) Name() string {
	return "DetailsMockProvider"
}

func (p *DetailsMockProvider) IsAvailable(ctx context.Context) bool {
	return true
}

func (p *DetailsMockProvider) Execute(ctx context.Context, req *llm.ExecuteRequest) (*llm.ExecuteResponse, error) {
	// Format the request details as a readable text
	details := fmt.Sprintf(`
REQUEST DETAILS:
---------------
PromptID: %s
CategoryID: %s
Provider: %s
MaxTokens: %d
Temperature: %f

Prompt Content:
--------------
%s
`, req.PromptID, req.CategoryID, req.Provider, req.MaxTokens, req.Temperature, req.PromptContent)

	// Add source content details if available
	if len(req.SourceContent) > 0 {
		details += "\nSource Content:\n--------------\n"
		for i, src := range req.SourceContent {
			details += fmt.Sprintf("Source #%d:\n  Type: %s\n  FileName: %s\n  ContentType: %s\n  Content: %s\n\n",
				i+1, src.Type, src.FileName, src.ContentType, src.Content)
		}
	}

	// Add options if available
	if len(req.Options) > 0 {
		details += "\nOptions:\n--------\n"
		for k, v := range req.Options {
			details += fmt.Sprintf("%s: %s\n", k, v)
		}
	}

	return &llm.ExecuteResponse{
		Text:         details,
		Provider:     "DetailsMockProvider",
		Metadata:     map[string]string{"type": "details_mock"},
		Tokens:       0,
		FinishReason: "mock",
	}, nil
}

// SupabaseInstructionsService is an adapter that implements the llm.InstructionsService interface
// using the SupabaseClient instead of direct SQL queries
type SupabaseInstructionsService struct {
	client db.SupabaseClientInterface
}

// NewSupabaseInstructionsService creates a new SupabaseInstructionsService
func NewSupabaseInstructionsService(client db.SupabaseClientInterface) *SupabaseInstructionsService {
	return &SupabaseInstructionsService{
		client: client,
	}
}

// GetInstructions fetches instructions for a prompt or category
func (s *SupabaseInstructionsService) GetInstructions(ctx context.Context, promptID string) (string, error) {
	// Maintain backward compatibility with the original interface
	instructionSet, err := s.GetInstructionSet(ctx, promptID)
	if err != nil {
		return "", err
	}

	return instructionSet.InstructionText, nil
}

// GetInstructionSet retrieves an instruction set for a given prompt ID
func (s *SupabaseInstructionsService) GetInstructionSet(ctx context.Context, promptID string) (*services.InstructionSet, error) {
	if s.client == nil {
		return nil, fmt.Errorf("supabase client is nil")
	}

	if promptID == "" {
		return &services.InstructionSet{}, nil
	}

	instructions, err := s.client.GetPromptInstructions(ctx, promptID)
	if err != nil {
		return nil, err
	}

	// Check if result is nil, which can happen if no record is found
	if instructions == nil {
		return nil, fmt.Errorf("no instruction set found for prompt ID: %s", promptID)
	}

	// Convert the parameters to the expected format
	result := &services.InstructionSet{
		InstructionText: instructions.InstructionTemplate,
		PromptID:        promptID,
	}

	// Extract parameters if they exist
	if instructions.Parameters != nil {
		// Try to extract known parameters
		if maxTokens, ok := instructions.Parameters["max_tokens"].(float64); ok {
			result.Params.MaxTokens = int(maxTokens)
		}
		if temperature, ok := instructions.Parameters["temperature"].(float64); ok {
			result.Params.Temperature = float32(temperature)
		}
		// Copy all parameters to Options
		result.Params.Options = instructions.Parameters
	}

	return result, nil
}

func getFinancialDataDB() *sql.DB {
	// Get configuration
	cfg := config.New()

	// Validate database connection
	if err := cfg.ValidateDBConnection(); err != nil {
		log.Fatalf("Database configuration error: %v", err)
	}

	// Open a standard database/sql connection
	db, err := sql.Open("postgres", cfg.DBConnection)
	if err != nil {
		log.Fatalf("Failed to create financial data DB adapter: %v", err)
	}

	// Configure the connection pool
	db.SetMaxOpenConns(5)
	db.SetMaxIdleConns(2)

	// Test the connection
	if err := db.Ping(); err != nil {
		log.Fatalf("Failed to ping financial data database: %v", err)
	}

	log.Println("Financial data database connected successfully")
	return db
}

func main() {
	// Load .env file if it exists
	_ = godotenv.Load()

	// Initialize configuration
	cfg := config.New()

	// Validate required configuration
	if err := cfg.Validate(); err != nil {
		log.Fatalf("Configuration error: %v", err)
	}

	// Set Gin mode based on environment
	if cfg.IsProduction() {
		gin.SetMode(gin.ReleaseMode)
	}

	// Create Supabase client
	supaClient, err := db.NewSupabaseClient(cfg.SupabaseURL, cfg.SupabaseKey)
	if err != nil {
		log.Fatalf("Failed to create Supabase client: %v", err)
	}

	// Initialize services using Supabase client
	instructionSvc := NewSupabaseInstructionsService(supaClient)

	// Get SQL DB connection for financial data service
	sqlDB := getFinancialDataDB()
	financialDataService := services.NewFinancialDataService(sqlDB)

	// Initialize providers based on configuration
	var providers []llm.Provider

	if cfg.UseRealExecution {
		log.Printf("INFO: Using real LLM execution mode")
		providersAvailable := false

		// Initialize Google AI (Gemini) first as the default provider
		if cfg.HasGoogleAI() {
			googleAIConfig := llm.Config{
				APIKey: cfg.GoogleAPIKey,
			}
			googleAISvc, err := llm.NewGoogleAIProvider(googleAIConfig)
			if err != nil {
				log.Printf("WARNING: Could not create Gemini provider: %v", err)
			} else {
				providers = append(providers, googleAISvc)
				providersAvailable = true
				log.Printf("INFO: Gemini provider initialized successfully as default provider")
			}
		} else {
			log.Printf("WARNING: PROMZ_GOOGLE_API_KEY not set, Gemini provider will be unavailable")
		}

		if cfg.HasOpenAI() {
			openAIConfig := llm.Config{
				APIKey: cfg.OpenAIAPIKey,
			}
			openAISvc, err := llm.NewOpenAIProvider(openAIConfig)
			if err != nil {
				log.Printf("WARNING: Could not create OpenAI provider: %v", err)
			} else {
				providers = append(providers, openAISvc)
				providersAvailable = true
				log.Printf("INFO: OpenAI provider initialized successfully")
			}
		} else {
			log.Printf("WARNING: PROMZ_OPENAI_API_KEY not set, OpenAI provider will be unavailable")
		}

		// Add mock provider if no real providers are available
		if !providersAvailable {
			log.Printf("WARNING: No LLM providers available, adding mock provider for development")
			mockProvider := &MockProvider{}
			providers = append(providers, mockProvider)
		}
	} else {
		// Use the details mock provider by default in debug mode
		log.Printf("INFO: Using mock mode - API will display request details instead of executing LLM calls")
		detailsMockProvider := &DetailsMockProvider{}
		providers = append(providers, detailsMockProvider)
	}

	// Initialize transcript service
	transcriptService := services.NewTranscriptService()

	// Initialize template processor with both services
	templateProcessor := services.NewTemplateProcessorWithServices(financialDataService, transcriptService)

	// Initialize model selection service
	modelSelectionService := llm.NewModelSelectionService(supaClient)

	// Initialize file name generator
	fileNameGen := services.NewFileNameGenerator()

	// Initialize LLM executor with available providers
	executor := llm.NewExecutor(instructionSvc, templateProcessor, modelSelectionService, fileNameGen, cfg.UseRealExecution, providers...)

	// Initialize content processing service
	log.Printf("INFO: Initializing content processing service with bucket: %s", cfg.StorageBucket)
	processingService, err := processing.NewContentProcessingService(cfg.StorageBucket, cfg)
	if err != nil {
		log.Printf("WARNING: Could not initialize content processing service: %v", err)
		log.Printf("Content processing features will be unavailable")
		processingService = nil
	} else {
		// Start cleanup task for expired processing jobs
		processingService.StartCleanupTask(context.Background())
		// Ensure the storage service is closed when the server shuts down
		defer func() {
			if err := processingService.Close(); err != nil {
				log.Printf("ERROR: Failed to close content processing service: %v", err)
			}
		}()
		log.Printf("INFO: Content processing service initialized successfully with bucket: %s", cfg.StorageBucket)
	}

	// Setup router
	// Dereference the pointer to get the struct value, as expected by router.Setup
	serverSetup, err := router.Setup(supaClient, cfg, executor, processingService, *processingService.GetStorageService(), processingService.GetProcessor())
	if err != nil {
		log.Fatalf("Failed to set up server: %v", err)
	}

	// Configure CORS if needed
	if !cfg.IsProduction() {
		serverSetup.Router.Use(cors.New(cors.Config{
			AllowOrigins:     []string{"*"},
			AllowMethods:     []string{"GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"},
			AllowHeaders:     []string{"Origin", "Content-Length", "Content-Type", "Authorization"},
			ExposeHeaders:    []string{"Content-Length"},
			AllowCredentials: true,
			MaxAge:           12 * time.Hour,
		}))
	}

	// Start server
	log.Printf("INFO: Starting server on port %s", cfg.Port)
	if err := serverSetup.Router.Run(":" + cfg.Port); err != nil {
		log.Fatalf("FATAL: Could not start server: %v", err)
	}
}
