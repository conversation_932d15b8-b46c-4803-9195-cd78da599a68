package services

import (
	"fmt"
	"log"
	"regexp"
	"strings"
)

// Regex patterns used throughout the template processor
const (
	// TripleBracePattern matches triple brace variables: {{{$1}FINANCE:TICKER}}
	TripleBracePattern = `\{\{\{\s*([#$][a-zA-Z0-9]+)\}\s*([A-Z_]+(?::[A-Z0-9_]+(?:_[A-Z0-9_]+)*)?|\w+)\}\}`

	// DoubleBracePattern matches double brace variables: {{FINANCE:TICKER}}
	DoubleBracePattern = `\{\{([A-Z_]+(?::[A-Z0-9_]+(?:_[A-Z0-9_]+)*)?|\w+)\}\}`

	// DoubleBraceWithDotPattern matches double brace variables with dot notation: {{user.name}}
	DoubleBraceWithDotPattern = `\{\{([A-Z_]+(?::[A-Z0-9_]+(?:_[A-Z0-9_]+)*)?|\w+(?:\.\w+)*)\}\}`
)

// Pre-compiled regex patterns for better performance
var (
	tripleRegex        = regexp.MustCompile(TripleBracePattern)
	doubleRegex        = regexp.MustCompile(DoubleBracePattern)
	doubleWithDotRegex = regexp.MustCompile(DoubleBraceWithDotPattern)
)

// ExtractPrefix extracts the prefix from a variable name
// For example: "{$1}FINANCE:TICKER" -> "{$1}", "FINANCE:TICKER" -> ""
func ExtractPrefix(variableName string) string {
	// Check if the variable name starts with a prefix pattern
	if strings.HasPrefix(variableName, "{") {
		// Find the closing brace
		closingBraceIndex := strings.Index(variableName, "}")
		if closingBraceIndex > 0 {
			// Check if the prefix matches the expected format (#n or $n)
			prefix := variableName[:closingBraceIndex+1]
			prefixContent := prefix[1:closingBraceIndex] // Remove braces
			if strings.HasPrefix(prefixContent, "#") || strings.HasPrefix(prefixContent, "$") {
				// Valid prefix format
				return prefix
			}
		}
	}
	// No valid prefix found
	return ""
}

// ExtractBaseName extracts the base part of a variable name
// For example: "FINANCE:TICKER" -> "FINANCE:TICKER", "{#1}FINANCE:TICKER" -> "FINANCE:TICKER"
func ExtractBaseName(variableName string) string {
	// Extract the prefix
	prefix := ExtractPrefix(variableName)

	// If there's a prefix, remove it
	if prefix != "" {
		return variableName[len(prefix):]
	}

	// No prefix, the variable name is the base name
	return variableName
}

// AddPrefixToVariable adds a prefix to a variable name
func AddPrefixToVariable(prefix string, variableName string) string {
	// If the prefix is empty, just return the variable name
	if prefix == "" {
		return variableName
	}

	// Add the prefix to the variable name
	return prefix + variableName
}

// IsFinanceVariable checks if the variable is a financial variable (starts with FINANCE:)
func IsFinanceVariable(variableName string) bool {
	// Get the base name without any prefix
	baseName := ExtractBaseName(variableName)
	return strings.HasPrefix(strings.ToUpper(baseName), "FINANCE:")
}

// SanitizeTemplateVariableName sanitizes a variable name for use in a template
func SanitizeTemplateVariableName(name string) string {
	// Replace any characters that might cause issues in Go templates
	sanitized := strings.ReplaceAll(name, "#", "num")
	sanitized = strings.ReplaceAll(sanitized, "$", "var")
	sanitized = strings.ReplaceAll(sanitized, "{", "")
	sanitized = strings.ReplaceAll(sanitized, "}", "")
	return sanitized
}

// DebugTemplateText logs information about the template text for debugging
func DebugTemplateText(templateText string) {
	log.Printf("DEBUG: DebugTemplateText: Template length: %d", len(templateText))

	// Check for YouTube transcript variable to help diagnose YouTube-related issues
	if strings.Contains(templateText, "{{MEDIA:YOUTUBE_TRANSCRIPT}}") {
		log.Printf("DEBUG: DebugTemplateText: Found exact match for {{MEDIA:YOUTUBE_TRANSCRIPT}}")
		log.Printf("DEBUG: DebugTemplateText: Found YOUTUBE_TRANSCRIPT in template")
	} else {
		log.Printf("DEBUG: DebugTemplateText: No exact match for {{MEDIA:YOUTUBE_TRANSCRIPT}}")
	}

	// Log a preview of the template text
	if len(templateText) > 0 {
		preview := templateText
		if len(preview) > 200 {
			preview = preview[:200] + "..."
		}
		log.Printf("DEBUG: DebugTemplateText: Template preview:\n%s", preview)
	}
}

// DebugVariableProcessing logs information about the variables for debugging
func DebugVariableProcessing(variables map[string]interface{}) {
	log.Printf("DEBUG: DebugVariableProcessing: Starting with %d variables", len(variables))

	// Log each variable for debugging
	if len(variables) > 0 {
		log.Printf("DEBUG: DebugVariableProcessing: Variables:")
		for key, value := range variables {
			valueStr := fmt.Sprintf("%v", value)
			if len(valueStr) > 50 {
				valueStr = valueStr[:50] + "..."
			}
			log.Printf("DEBUG: Variable: %s = %s", key, valueStr)
		}
	}

	// Check for YouTube-related variables
	hasTranscriptVar := false
	hasYouTubeURLVar := false
	youtubeURL := ""

	for key, value := range variables {
		// Check for transcript variable
		if strings.EqualFold(key, "MEDIA:YOUTUBE_TRANSCRIPT") || strings.EqualFold(key, "YOUTUBE_TRANSCRIPT") {
			hasTranscriptVar = true
			log.Printf("DEBUG: DebugVariableProcessing: Found MEDIA:YOUTUBE_TRANSCRIPT variable")
		}

		// Check for URL variable
		if strings.EqualFold(key, "MEDIA:YOUTUBE_URL") || strings.EqualFold(key, "YOUTUBE_URL") {
			hasYouTubeURLVar = true
			if str, ok := value.(string); ok {
				youtubeURL = str
				log.Printf("DEBUG: DebugVariableProcessing: Found MEDIA:YOUTUBE_URL variable: %s", youtubeURL)
			} else {
				log.Printf("DEBUG: DebugVariableProcessing: Found MEDIA:YOUTUBE_URL variable but it's not a string")
			}
		}
	}

	// Log the results
	if !hasTranscriptVar {
		log.Printf("DEBUG: DebugVariableProcessing: MEDIA:YOUTUBE_TRANSCRIPT variable NOT found")
	}

	if !hasYouTubeURLVar {
		log.Printf("DEBUG: DebugVariableProcessing: MEDIA:YOUTUBE_URL variable NOT found")
	}

	// Log if we found YouTube-related variables
	if hasTranscriptVar || hasYouTubeURLVar {
		if hasYouTubeURLVar {
			log.Printf("DEBUG: DebugVariableProcessing: Found YouTube-related variable: MEDIA:YOUTUBE_URL")
		}
		if hasTranscriptVar {
			log.Printf("DEBUG: DebugVariableProcessing: Found YouTube-related variable: MEDIA:YOUTUBE_TRANSCRIPT")
		}
	}
}

// FindVariables finds all variables in a template and returns them as a map
func FindVariables(templateText string) (tripleMatches [][]string, doubleMatches [][]string) {
	// Use pre-compiled regex for better performance
	tripleMatches = tripleRegex.FindAllStringSubmatch(templateText, -1)
	doubleMatches = doubleWithDotRegex.FindAllStringSubmatch(templateText, -1)
	return tripleMatches, doubleMatches
}
