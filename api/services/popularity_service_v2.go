package services

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"math"
	"sort"
	"strconv"
	"time"

	"promz.ai/api/internal/repository/db"
)

// PromptUsageEvent represents a single usage event for a prompt
type PromptUsageEvent struct {
	PromptID    string    `json:"prompt_id"`
	EventType   string    `json:"event_type"`
	Timestamp   time.Time `json:"timestamp"`
	CountryCode string    `json:"country_code,omitempty"`
}

// PopularPrompt represents a popular prompt with its score
type PopularPrompt struct {
	PromptID        string `json:"prompt_id"`
	PopularityScore int    `json:"popularity_score"`
}

// PopularityServiceV2 handles prompt popularity tracking and calculations with WebSocket support
type PopularityServiceV2 struct {
	db                   db.SupabaseClientInterface
	webSocketBroadcaster WebSocketBroadcaster
}

// WebSocketBroadcaster defines the interface for broadcasting messages via WebSocket
type WebSocketBroadcaster interface {
	BroadcastToTopic(topic string, payload interface{})
}

// NewPopularityServiceV2 creates a new PopularityServiceV2 with WebSocket support
func NewPopularityServiceV2(db db.SupabaseClientInterface, broadcaster WebSocketBroadcaster) *PopularityServiceV2 {
	return &PopularityServiceV2{
		db:                   db,
		webSocketBroadcaster: broadcaster,
	}
}

// SetBroadcaster sets the WebSocket broadcaster for the service
func (s *PopularityServiceV2) SetBroadcaster(broadcaster WebSocketBroadcaster) {
	s.webSocketBroadcaster = broadcaster
}

// RecordUsageEvents records multiple prompt usage events
func (s *PopularityServiceV2) RecordUsageEvents(ctx context.Context, events []PromptUsageEvent) error {
	if len(events) == 0 {
		return nil
	}

	// Convert our specific events to the generic interface{} type expected by the client
	interfaceEvents := make([]interface{}, len(events))
	for i, event := range events {
		interfaceEvents[i] = map[string]interface{}{
			"prompt_id":    event.PromptID,
			"event_type":   event.EventType,
			"timestamp":    event.Timestamp,
			"country_code": event.CountryCode,
		}
	}

	// Use the new interface method to record events
	err := s.db.RecordPromptUsageEvents(ctx, interfaceEvents)
	if err != nil {
		return err
	}

	// After recording usage events, check if we should update the popular prompts cache
	// This is a simple heuristic - in a production system, you might want to be more sophisticated
	// about when to update the cache (e.g., based on time since last update, number of new events, etc.)
	if len(events) >= 10 {
		go func() {
			// Use a background context since this is running in a goroutine
			bgCtx := context.Background()
			if err := s.UpdatePopularPromptsCache(bgCtx, 30); err != nil {
				log.Printf("Error updating popular prompts cache after recording usage events: %v", err)
			}
		}()
	}

	return nil
}

// UpdatePopularPromptsCache updates the cached popular prompts in the database
func (s *PopularityServiceV2) UpdatePopularPromptsCache(ctx context.Context, days int) error {
	if days <= 0 {
		days = 30 // Default to 30 days if not specified
	}

	// 1. Fetch raw usage events from the database
	usageEvents, err := s.db.GetPromptUsageEvents(ctx, days)
	if err != nil {
		return fmt.Errorf("failed to get usage events: %w", err)
	}

	log.Printf("Retrieved %d usage events for scoring", len(usageEvents))

	// 2. Calculate scores using our custom algorithm
	promptScores := s.calculatePromptScores(usageEvents, days)

	log.Printf("Calculated scores for %d prompts", len(promptScores))

	// 3. Convert to the format expected by the database
	interfacePrompts := make([]interface{}, len(promptScores))
	for i, ps := range promptScores {
		interfacePrompts[i] = map[string]interface{}{
			"prompt_id":        ps.PromptID,
			"popularity_score": ps.PopularityScore,
		}
	}

	// 4. Update the cache in the database
	if err := s.db.UpdatePopularPromptsCache(ctx, interfacePrompts); err != nil {
		return fmt.Errorf("failed to update popular prompts cache: %w", err)
	}

	log.Printf("Successfully updated popular prompts cache with %d prompts", len(promptScores))

	// 5. Broadcast the updated popular prompts to all connected clients
	go s.broadcastPopularPrompts(ctx)

	return nil
}

// broadcastPopularPrompts fetches the latest popular prompts and broadcasts them via WebSocket
func (s *PopularityServiceV2) broadcastPopularPrompts(ctx context.Context) {
	// Fetch the latest popular prompts with complete data
	prompts, err := s.GetPopularPrompts(ctx, 10) // Limit to top 10 for broadcasts
	if err != nil {
		log.Printf("Error fetching popular prompts for broadcast: %v", err)
		return
	}

	// Broadcast to all clients subscribed to the "popular_prompts" topic
	if s.webSocketBroadcaster != nil {
		s.webSocketBroadcaster.BroadcastToTopic("popular_prompts", map[string]interface{}{
			"popular_prompts": prompts,
			"updated_at":      time.Now(),
		})
		log.Printf("Broadcasted %d popular prompts to WebSocket clients", len(prompts))
	}
}

// calculatePromptScores applies our custom scoring algorithm
func (s *PopularityServiceV2) calculatePromptScores(events []db.PromptUsageEvent, days int) []PopularPrompt {
	// Map to store scores by prompt ID
	scoresByPrompt := make(map[string]float64)

	// Current time for time decay calculation
	now := time.Now()

	// Process each event
	for _, event := range events {
		// Base score based on event type
		baseScore := 1.0
		if event.EventType == "executed" {
			baseScore = 3.0 // Executions count more than selections
		}

		// Apply time decay factor
		ageInDays := now.Sub(event.UsedAt).Hours() / 24
		decayFactor := 1.0
		if days > 0 {
			// Linear decay: newer events count more
			decayFactor = 1.0 - (ageInDays / float64(days))
			if decayFactor < 0.1 {
				decayFactor = 0.1 // Minimum decay factor
			}
		}

		// Calculate final score for this event
		eventScore := baseScore * decayFactor

		// Add to the prompt's total score
		scoresByPrompt[event.PromptID] += eventScore
	}

	// Convert map to slice for sorting
	var popularPrompts []PopularPrompt
	for promptID, score := range scoresByPrompt {
		popularPrompts = append(popularPrompts, PopularPrompt{
			PromptID:        promptID,
			PopularityScore: int(math.Round(score)), // Round to nearest integer
		})
	}

	// Sort by score (descending)
	sort.Slice(popularPrompts, func(i, j int) bool {
		return popularPrompts[i].PopularityScore > popularPrompts[j].PopularityScore
	})

	// Limit to top 50 prompts
	if len(popularPrompts) > 50 {
		popularPrompts = popularPrompts[:50]
	}

	return popularPrompts
}

// GetPopularPrompts retrieves popular prompts with complete prompt data in a single query
func (s *PopularityServiceV2) GetPopularPrompts(ctx context.Context, limit int) ([]map[string]interface{}, error) {
	// We'll use the existing Supabase client interface to execute a custom RPC function
	// First, let's get the popular prompts to get their IDs
	popularPrompts, err := s.db.GetPopularPrompts(ctx, limit)
	if err != nil {
		log.Printf("Error getting popular prompts: %v", err)
		return nil, fmt.Errorf("error getting popular prompts: %w", err)
	}

	if len(popularPrompts) == 0 {
		return []map[string]interface{}{}, nil
	}

	// Extract prompt IDs from the popular prompts
	promptIDs := make([]string, 0, len(popularPrompts))
	popularityScores := make(map[string]int)

	for _, p := range popularPrompts {
		promptData, ok := p.(map[string]interface{})
		if !ok {
			log.Printf("Error: popular prompt data is not a map")
			continue
		}

		promptID, ok := promptData["prompt_id"].(string)
		if !ok {
			log.Printf("Error: prompt_id is not a string")
			continue
		}

		// Handle different types of score values
		var score float64
		scoreValue := promptData["popularity_score"]
		switch v := scoreValue.(type) {
		case float64:
			score = v
		case int:
			score = float64(v)
		case int64:
			score = float64(v)
		case string:
			var err error
			score, err = strconv.ParseFloat(v, 64)
			if err != nil {
				log.Printf("Error converting score string to number: %v", err)
				continue
			}
		case nil:
			log.Printf("Score is nil for prompt %s, using default 0", promptID)
			score = 0
		default:
			log.Printf("Error: score has unexpected type %T for prompt %s", scoreValue, promptID)
			continue
		}

		promptIDs = append(promptIDs, promptID)
		popularityScores[promptID] = int(score)
	}

	// For each prompt ID, get the full prompt data
	var results []map[string]interface{}
	for _, promptID := range promptIDs {
		// Get the prompt data
		promptObj, err := s.db.GetPrompt(ctx, promptID)
		if err != nil {
			log.Printf("Error getting prompt %s: %v", promptID, err)
			continue
		}

		// Convert to map
		promptBytes, err := json.Marshal(promptObj)
		if err != nil {
			log.Printf("Error marshaling prompt: %v", err)
			continue
		}

		var promptMap map[string]interface{}
		if err := json.Unmarshal(promptBytes, &promptMap); err != nil {
			log.Printf("Error unmarshaling prompt: %v", err)
			continue
		}

		// Add popularity score
		promptMap["popularity_score"] = popularityScores[promptID]
		results = append(results, promptMap)
	}

	return results, nil
}
