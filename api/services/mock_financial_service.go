package services

import (
	"fmt"
)

// MockFinancialDataService is a testable version of FinancialDataService
type MockFinancialDataService struct {
	GetTickerInfoFunc          func(symbol string) (*TickerInfo, error)
	EnrichFinancialVariablesFunc func(variables map[string]interface{}) (map[string]interface{}, error)
}

// NewMockFinancialDataService creates a new mock service with default implementations
func NewMockFinancialDataService() *MockFinancialDataService {
	return &MockFinancialDataService{
		// Default implementation for GetTickerInfo
		GetTickerInfoFunc: func(symbol string) (*TickerInfo, error) {
			return nil, fmt.Errorf("GetTickerInfo not implemented")
		},
		// Default implementation for EnrichFinancialVariables
		EnrichFinancialVariablesFunc: func(variables map[string]interface{}) (map[string]interface{}, error) {
			return variables, nil
		},
	}
}

// GetTickerInfo implements the FinancialDataService interface
func (s *MockFinancialDataService) GetTickerInfo(symbol string) (*TickerInfo, error) {
	return s.GetTickerInfoFunc(symbol)
}

// EnrichFinancialVariables implements the FinancialDataService interface
func (s *MockFinancialDataService) EnrichFinancialVariables(variables map[string]interface{}) (map[string]interface{}, error) {
	if s.EnrichFinancialVariablesFunc != nil {
		return s.EnrichFinancialVariablesFunc(variables)
	}
	
	// Default implementation if no custom function is provided
	// If there's no FINANCE:TICKER, we can't enrich anything
	tickerValue, hasTicker := variables["FINANCE:TICKER"]
	if !hasTicker {
		return variables, nil
	}

	// Convert the ticker value to string
	tickerSymbol, ok := tickerValue.(string)
	if !ok || tickerSymbol == "" {
		return variables, nil
	}

	// Get ticker info
	tickerInfo, err := s.GetTickerInfo(tickerSymbol)
	if err != nil {
		return variables, err
	}

	// Make a copy of the input variables
	result := make(map[string]interface{})
	for k, v := range variables {
		result[k] = v
	}

	// If ticker info is found, enrich variables
	if tickerInfo != nil {
		result["FINANCE:COMPANY_NAME"] = tickerInfo.CompanyName
		result["FINANCE:SECTOR"] = tickerInfo.Sector
		result["FINANCE:INDUSTRY"] = tickerInfo.Industry
	} else {
		// For unknown tickers, add empty values for template processor tests
		result["FINANCE:COMPANY_NAME"] = ""
		result["FINANCE:SECTOR"] = ""
		result["FINANCE:INDUSTRY"] = ""
	}

	return result, nil
}

// WithAppleTickerInfo configures the mock to return Apple stock data
func (s *MockFinancialDataService) WithAppleTickerInfo() *MockFinancialDataService {
	s.GetTickerInfoFunc = func(symbol string) (*TickerInfo, error) {
		if symbol == "AAPL" {
			return &TickerInfo{
				Symbol:      "AAPL",
				CompanyName: "Apple Inc.",
				Sector:      "Information Technology",
				Industry:    "Technology Hardware, Storage & Peripherals",
			}, nil
		}
		// For unknown tickers, return nil without an error
		return nil, nil
	}
	
	// Also set up the EnrichFinancialVariables function to handle unknown tickers correctly
	s.EnrichFinancialVariablesFunc = func(variables map[string]interface{}) (map[string]interface{}, error) {
		// Make a copy of the input variables
		result := make(map[string]interface{})
		for k, v := range variables {
			result[k] = v
		}
		
		// Check if there's a ticker variable
		tickerValue, hasTicker := variables["FINANCE:TICKER"]
		if !hasTicker {
			return result, nil
		}
		
		// Convert the ticker value to string
		tickerSymbol, ok := tickerValue.(string)
		if !ok || tickerSymbol == "" {
			return result, nil
		}
		
		// For Apple ticker, add the financial data
		if tickerSymbol == "AAPL" {
			result["FINANCE:COMPANY_NAME"] = "Apple Inc."
			result["FINANCE:SECTOR"] = "Information Technology"
			result["FINANCE:INDUSTRY"] = "Technology Hardware, Storage & Peripherals"
		} else {
			// For unknown tickers, add empty values for template processor tests
			result["FINANCE:COMPANY_NAME"] = ""
			result["FINANCE:SECTOR"] = ""
			result["FINANCE:INDUSTRY"] = ""
		}
		
		return result, nil
	}
	
	return s
}

// DefaultAppleMockService returns a mock service pre-configured with Apple stock data
func DefaultAppleMockService() *MockFinancialDataService {
	return NewMockFinancialDataService().WithAppleTickerInfo()
}
