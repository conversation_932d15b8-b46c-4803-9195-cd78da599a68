package services

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestTemplateProcessor_ProcessTemplate(t *testing.T) {
	processor := NewTemplateProcessor()

	tests := []struct {
		name         string
		templateText string
		variables    map[string]interface{}
		expected     string
		expectError  bool
	}{
		{
			name:         "Empty template",
			templateText: "",
			variables:    nil,
			expected:     "",
			expectError:  false,
		},
		{
			name:         "No variables",
			templateText: "This is a template without variables",
			variables:    nil,
			expected:     "This is a template without variables",
			expectError:  false,
		},
		{
			name:         "Simple variable",
			templateText: "Hello, {{name}}!",
			variables:    map[string]interface{}{"name": "World"},
			expected:     "Hello, World!",
			expectError:  false,
		},
		{
			name:         "Finance ticker variable",
			templateText: "The stock symbol is {{FINANCE:TICKER}}",
			variables:    map[string]interface{}{"FINANCE:TICKER": "AAPL"},
			expected:     "The stock symbol is AAPL",
			expectError:  false,
		},
		{
			name:         "Multiple finance variables",
			templateText: "{{FINANCE:TICKER}} ({{FINANCE:COMPANY_NAME}}) is in the {{FINANCE:INDUSTRY}} industry",
			variables: map[string]interface{}{
				"FINANCE:TICKER":       "AAPL",
				"FINANCE:COMPANY_NAME": "Apple Inc.",
				"FINANCE:INDUSTRY":     "Technology",
			},
			expected:    "AAPL (Apple Inc.) is in the Technology industry",
			expectError: false,
		},
		{
			name:         "Location variables",
			templateText: "The location is {{LOCATION:CITY}}, {{LOCATION:COUNTRY}}",
			variables: map[string]interface{}{
				"LOCATION:CITY":    "San Francisco",
				"LOCATION:COUNTRY": "USA",
			},
			expected:    "The location is San Francisco, USA",
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := processor.ProcessTemplate(tt.templateText, tt.variables)
			
			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			}
		})
	}
}

// TestTemplateProcessorWithFinancialService tests the template processor with financial data service
func TestTemplateProcessorWithFinancialService(t *testing.T) {
	// Create a mock financial service with Apple stock data
	mockService := DefaultAppleMockService()
	
	// Create a processor with our mock service
	processor := &TemplateProcessor{
		financialService: mockService,
	}
	
	// Test cases
	tests := []struct {
		name         string
		templateText string
		variables    map[string]interface{}
		expected     string
	}{
		{
			name:         "Enrich ticker with sector and industry",
			templateText: "{{FINANCE:TICKER}} is in the {{FINANCE:SECTOR}} sector and {{FINANCE:INDUSTRY}} industry",
			variables:    map[string]interface{}{"FINANCE:TICKER": "AAPL"},
			expected:     "AAPL is in the Information Technology sector and Technology Hardware, Storage & Peripherals industry",
		},
		{
			name:         "Enrich ticker with company name",
			templateText: "{{FINANCE:TICKER}} ({{FINANCE:COMPANY_NAME}})",
			variables:    map[string]interface{}{"FINANCE:TICKER": "AAPL"},
			expected:     "AAPL (Apple Inc.)",
		},
		{
			name:         "No enrichment for unknown ticker",
			templateText: "{{FINANCE:TICKER}} is in the {{FINANCE:SECTOR}} sector",
			variables:    map[string]interface{}{"FINANCE:TICKER": "UNKNOWN"},
			expected:     "UNKNOWN is in the  sector",
		},
	}
	
	// Run the tests
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := processor.ProcessTemplate(tt.templateText, tt.variables)
			assert.NoError(t, err)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestTemplateProcessor_ExtractVariables(t *testing.T) {
	processor := NewTemplateProcessor()

	tests := []struct {
		name         string
		templateText string
		expected     []string
	}{
		{
			name:         "Empty template",
			templateText: "",
			expected:     []string{},
		},
		{
			name:         "No variables",
			templateText: "This is a template without variables",
			expected:     []string{},
		},
		{
			name:         "Single variable",
			templateText: "Hello, {{name}}!",
			expected:     []string{"name"},
		},
		{
			name:         "Multiple variables",
			templateText: "{{FINANCE:TICKER}} ({{FINANCE:COMPANY_NAME}}) is in the {{FINANCE:INDUSTRY}} industry",
			expected:     []string{"FINANCE:TICKER", "FINANCE:COMPANY_NAME", "FINANCE:INDUSTRY"},
		},
		{
			name:         "Duplicate variables",
			templateText: "{{name}} is {{name}}",
			expected:     []string{"name"},
		},
		{
			name:         "Multiple variables with canonical names",
			templateText: "{{user.name}} is from {{user.location}}",
			expected:     []string{"user.name", "user.location"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := processor.ExtractVariables(tt.templateText)
			if len(tt.expected) == 0 {
				assert.Empty(t, result)
			} else {
				assert.ElementsMatch(t, tt.expected, result)
			}
		})
	}
}
