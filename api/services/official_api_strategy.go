package services

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"net/http/httputil"
	"regexp" // Still needed for the loggingTransport
	"time"

	"google.golang.org/api/option"
	"google.golang.org/api/youtube/v3"
)

// loggingTransport is a custom http.RoundTripper that logs requests and responses
type loggingTransport struct {
	underlying http.RoundTripper
	apiKey     string
}

func (t *loggingTransport) RoundTrip(req *http.Request) (*http.Response, error) {
	// Add API key to query parameters for all requests if not already present
	query := req.URL.Query()
	if query.Get("key") == "" && t.apiKey != "" {
		query.Set("key", t.apiKey)
		req.URL.RawQuery = query.Encode()
	}

	// Log the request (but redact the api key)
	dump, _ := httputil.DumpRequestOut(req, false)
	requestStr := string(dump)
	requestStr = regexp.MustCompile(`key=[^&]*`).ReplaceAllString(requestStr, "key=REDACTED")
	log.Printf("DEBUG: YouTube API request: %s", requestStr)

	// Perform the request
	resp, err := t.underlying.RoundTrip(req)

	// Log the response
	if err != nil {
		log.Printf("ERROR: YouTube API request failed: %v", err)
	} else {
		log.Printf("DEBUG: YouTube API response status: %s", resp.Status)
	}

	return resp, err
}

// OfficialAPIStrategy implements transcript retrieval using the official YouTube Data API
type OfficialAPIStrategy struct {
	service    *youtube.Service
	apiKey     string
	httpClient *http.Client
	userAgent  string
	name       string
}

// maskAPIKey returns a partially masked API key for safe logging
func maskAPIKey(key string) string {
	if len(key) < 8 {
		return "****"
	}
	return key[:4] + "..." + key[len(key)-4:]
}

// NewOfficialAPIStrategy creates a new strategy using the official YouTube API
func NewOfficialAPIStrategy(apiKey string) (*OfficialAPIStrategy, error) {
	if apiKey == "" {
		return nil, fmt.Errorf("YouTube API key is required")
	}

	maskedKey := maskAPIKey(apiKey)
	log.Printf("DEBUG: Creating OfficialAPIStrategy with API key: %s (length: %d)", maskedKey, len(apiKey))

	ctx := context.Background()
	client := &http.Client{
		Timeout: 15 * time.Second,
		Transport: &loggingTransport{
			underlying: http.DefaultTransport,
			apiKey:     apiKey,
		},
	}

	log.Printf("DEBUG: Initializing YouTube service with API key")
	// First try with default option
	service, err := youtube.NewService(ctx,
		option.WithAPIKey(apiKey),
		option.WithHTTPClient(client))
	if err != nil {
		log.Printf("ERROR: Failed to create YouTube service: %v", err)
		return nil, fmt.Errorf("failed to create YouTube service: %w", err)
	}

	log.Printf("DEBUG: YouTube service created successfully")

	return &OfficialAPIStrategy{
		service:    service,
		apiKey:     apiKey,
		httpClient: client,
		userAgent:  "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
		name:       "OfficialAPI",
	}, nil
}

// GetName returns the name of this strategy
func (s *OfficialAPIStrategy) GetName() string {
	return s.name
}

// GetTranscript fetches transcript using the official YouTube Data API
func (s *OfficialAPIStrategy) GetTranscript(videoID, lang, country string) ([]TranscriptSegment, string, error) {
	log.Printf("DEBUG: OfficialAPIStrategy - Getting transcript for video ID: %s with API key: %s...", videoID, maskAPIKey(s.apiKey))

	// First, try to get video details to fetch the title
	listCall := s.service.Videos.List([]string{"snippet", "contentDetails"}).Id(videoID)

	// API key will be injected by our custom transport

	// Debug the HTTP request
	log.Printf("DEBUG: Making YouTube API request with API key length: %d", len(s.apiKey))

	videoResp, err := listCall.Do()
	if err != nil {
		log.Printf("ERROR: YouTube API call failed: %v", err)
		log.Printf("DEBUG: Double-checking if API key is correctly set in the service client")
		return nil, "", fmt.Errorf("failed to fetch video details: %w", err)
	}

	if len(videoResp.Items) == 0 {
		return nil, "", fmt.Errorf("video not found: %s", videoID)
	}

	title := videoResp.Items[0].Snippet.Title

	// Since we have the title from the API, we'll return an error to intentionally fall back to the scraping strategy
	// This way the fallback mechanism will try the scraping strategy which works well
	log.Printf("INFO: Official API used for metadata only (video title: %s). Triggering fallback to scraping strategy for transcript (OAuth2 required for captions)", title)
	return nil, title, fmt.Errorf("official API used for metadata only, fallback to scraping for transcripts")

	/*
		// NOTE: This code is preserved but disabled until OAuth2 implementation is ready
		// It would retrieve the caption using the official API with OAuth2 authentication

		// We've successfully retrieved the video metadata using the official API
		// For captions, since the API requires OAuth2 for downloading them, let's use the scraping strategy instead
		log.Printf("INFO: Using scraping strategy for transcript as OAuth2 is required for official captions download")

		// Create a scraping strategy and use it to get the transcript
		scrapingStrategy := NewScrapingStrategy()
		segments, _, err := scrapingStrategy.GetTranscript(videoID, lang, country)
		if err != nil {
			return nil, title, fmt.Errorf("failed to fetch transcript using scraping after metadata retrieval: %w", err)
		}

		// Check if we got any segments
		if len(segments) == 0 {
			return nil, title, fmt.Errorf("no transcript segments found using scraping strategy")
		}

		log.Printf("Successfully extracted %d transcript segments using hybrid approach (Official API for metadata, Scraping for transcripts)", len(segments))
		return segments, title, nil
	*/
}
