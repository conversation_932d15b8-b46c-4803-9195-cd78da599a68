package services

import (
	"database/sql"
	"fmt"
)

// FinancialDataService provides access to financial data such as ticker information
type FinancialDataService struct {
	db *sql.DB
}

// NewFinancialDataService creates a new financial data service
func NewFinancialDataService(db *sql.DB) *FinancialDataService {
	return &FinancialDataService{
		db: db,
	}
}

// TickerInfo represents information about a stock ticker
type TickerInfo struct {
	Symbol      string
	CompanyName string
	Sector      string
	Industry    string
}

// GetTickerInfo retrieves information about a ticker from the database
func (s *FinancialDataService) GetTickerInfo(symbol string) (*TickerInfo, error) {
	if s.db == nil {
		return nil, fmt.Errorf("database connection not initialized")
	}

	query := `
		SELECT symbol, company_name, sector, industry
		FROM sp500_tickers
		WHERE symbol = $1
		LIMIT 1
	`

	var info TickerInfo
	err := s.db.QueryRow(query, symbol).Scan(
		&info.Symbol,
		&info.CompanyName,
		&info.Sector,
		&info.Industry,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("ticker not found: %s", symbol)
		}
		return nil, fmt.Errorf("error querying ticker info: %w", err)
	}

	return &info, nil
}

// EnrichFinancialVariables adds additional financial information to variables based on ticker symbol
func (s *FinancialDataService) EnrichFinancialVariables(variables map[string]interface{}) (map[string]interface{}, error) {
	// Create a copy of the variables map to avoid modifying the original
	enriched := make(map[string]interface{})
	for k, v := range variables {
		enriched[k] = v
	}

	// Find all keys that end with FINANCE:TICKER and process them
	for key, value := range variables {
		// Skip if not a finance variable
		if !IsFinanceVariable(key) {
			continue
		}

		// Only process ticker variables
		baseName := ExtractBaseName(key)
		if baseName != "FINANCE:TICKER" {
			continue
		}

		// Extract prefix
		prefix := ExtractPrefix(key)

		// Get the ticker symbol value
		tickerSymbol, ok := value.(string)
		if !ok || tickerSymbol == "" {
			continue // Skip if value is not a string or is empty
		}

		fmt.Printf("Processing financial variable: %s with symbol: %s and prefix: %q\n", key, tickerSymbol, prefix)

		// Get ticker info from the database
		info, err := s.GetTickerInfo(tickerSymbol)
		if err != nil {
			fmt.Printf("Error retrieving ticker info: %v\n", err)
			continue // Continue with next variable
		}

		// Add financial information with the same prefix
		if info.CompanyName != "" {
			companyNameVar := AddPrefixToVariable(prefix, "FINANCE:COMPANY_NAME")
			enriched[companyNameVar] = info.CompanyName
			fmt.Printf("Added %s = %s\n", companyNameVar, info.CompanyName)
		}

		if info.Sector != "" {
			sectorVar := AddPrefixToVariable(prefix, "FINANCE:SECTOR")
			enriched[sectorVar] = info.Sector
			fmt.Printf("Added %s = %s\n", sectorVar, info.Sector)
		}

		if info.Industry != "" {
			industryVar := AddPrefixToVariable(prefix, "FINANCE:INDUSTRY")
			enriched[industryVar] = info.Industry
			fmt.Printf("Added %s = %s\n", industryVar, info.Industry)
		}
	}

	return enriched, nil
}
