package processing

import (
	"context"
	"io"
)

// FileProcessor is the interface for file processors
// This extends the base ContentProcessor interface from types.go
type FileProcessor interface {
	ContentProcessor

	// UploadFile processes a file and returns a result ID
	UploadFile(ctx context.Context, job *ProcessingJob, metadata map[string]interface{}) (string, error)

	// DetectContentType detects the content type of a file
	DetectContentType(ctx context.Context, reader io.Reader, filename string) (ContentType, error)
}
