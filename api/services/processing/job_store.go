package processing

import (
	"sync"
	"time"
)

// JobStore defines the interface for managing active processing jobs in memory.
// This allows decoupling the handler (reading status) from the processor (writing status).
type JobStore interface {
	// AddJob adds a new job to the store or replaces an existing one with the same ID.
	AddJob(job *ProcessingJob)
	// GetJob retrieves a copy of a job by its ID. Returns the job and true if found, otherwise nil and false.
	GetJob(jobID string) (*ProcessingJob, bool)
	// UpdateJob updates an existing job in the store based on its ID.
	// It is recommended to fetch the job, modify it, then pass the modified job back to UpdateJob.
	// If the job ID doesn't exist, it will be added (behaves like AddJob).
	UpdateJob(job *ProcessingJob)
	// RemoveJob removes a job from the store by its ID.
	RemoveJob(jobID string)
	// TODO: Consider adding methods for listing jobs or cleanup of stale jobs.
}

// InMemoryJobStore implements the JobStore interface using an in-memory map.
type InMemoryJobStore struct {
	mu   sync.RWMutex
	jobs map[string]*ProcessingJob
}

// NewInMemoryJobStore creates a new InMemoryJobStore.
func NewInMemoryJobStore() JobStore {
	return &InMemoryJobStore{
		jobs: make(map[string]*ProcessingJob),
	}
}

// AddJob adds a new job to the store or replaces an existing one.
func (s *InMemoryJobStore) AddJob(job *ProcessingJob) {
	if job == nil {
		return // Should perhaps log an error
	}
	s.mu.Lock()
	defer s.mu.Unlock()
	// Store a copy to prevent external modifications affecting the store directly
	jobCopy := *job
	s.jobs[job.ID] = &jobCopy
}

// GetJob retrieves a copy of a job by its ID.
func (s *InMemoryJobStore) GetJob(jobID string) (*ProcessingJob, bool) {
	s.mu.RLock()
	defer s.mu.RUnlock()
	job, found := s.jobs[jobID]
	if !found {
		return nil, false
	}
	// Return a copy to prevent race conditions on the caller's side
	jobCopy := *job
	return &jobCopy, true
}

// UpdateJob updates an existing job in the store or adds it if not present.
func (s *InMemoryJobStore) UpdateJob(job *ProcessingJob) {
	if job == nil {
		return // Should perhaps log an error
	}
	s.mu.Lock()
	defer s.mu.Unlock()

	// Store (or update) a copy
	jobCopy := *job
	// Ensure UpdatedAt reflects the update time
	// Note: If the caller already set UpdatedAt, this will overwrite it.
	// Consider if caller should manage UpdatedAt exclusively.
	jobCopy.UpdatedAt = time.Now()
	s.jobs[job.ID] = &jobCopy
}

// RemoveJob removes a job from the store by its ID.
func (s *InMemoryJobStore) RemoveJob(jobID string) {
	s.mu.Lock()
	defer s.mu.Unlock()
	delete(s.jobs, jobID)
}
