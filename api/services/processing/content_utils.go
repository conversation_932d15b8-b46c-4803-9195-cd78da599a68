package processing

import (
	"fmt"
	"reflect"
	"strings"
)

// estimateTokens provides a simple estimation of token count from content
// This is a simplified implementation - in production, you would use a proper tokenizer
func estimateTokens(content interface{}) int64 {
	// Handle nil content
	if content == nil {
		return 0
	}

	// Convert content to string for token estimation
	var text string
	
	switch v := content.(type) {
	case string:
		text = v
	case []byte:
		text = string(v)
	case map[string]interface{}:
		// For maps, estimate tokens in all string values
		var tokens int64
		for _, val := range v {
			if str, ok := val.(string); ok {
				tokens += estimateTokens(str)
			} else {
				// Recursively estimate tokens for non-string values
				tokens += estimateTokens(val)
			}
		}
		return tokens
	case []interface{}:
		// For arrays, estimate tokens in all elements
		var tokens int64
		for _, item := range v {
			tokens += estimateTokens(item)
		}
		return tokens
	default:
		// For other types, convert to string if possible
		text = fmt.Sprintf("%v", v)
	}

	// Simple token estimation: split by whitespace and count
	// This is a very rough approximation - production systems should use a proper tokenizer
	words := strings.Fields(text)
	
	// Estimate tokens: roughly 4 characters per token on average
	charCount := len(text)
	estimatedTokens := int64(charCount) / 4
	
	// Ensure we return at least the word count
	if int64(len(words)) > estimatedTokens {
		return int64(len(words))
	}
	
	return estimatedTokens
}

// mergeContent combines content from multiple chunks
// The implementation depends on the content type
func mergeContent(existing, new interface{}) interface{} {
	// Handle nil cases
	if existing == nil {
		return new
	}
	if new == nil {
		return existing
	}

	// Type-specific merging
	switch existingVal := existing.(type) {
	case string:
		// For strings, concatenate
		if newVal, ok := new.(string); ok {
			return existingVal + newVal
		}
		return fmt.Sprintf("%v%v", existingVal, new)
		
	case []interface{}:
		// For arrays, append elements
		if newVal, ok := new.([]interface{}); ok {
			return append(existingVal, newVal...)
		}
		return append(existingVal, new)
		
	case map[string]interface{}:
		// For maps, merge keys
		if newVal, ok := new.(map[string]interface{}); ok {
			result := make(map[string]interface{})
			
			// Copy existing values
			for k, v := range existingVal {
				result[k] = v
			}
			
			// Merge or overwrite with new values
			for k, v := range newVal {
				// If both values are of the same complex type, merge recursively
				if existingSubVal, exists := existingVal[k]; exists {
					if reflect.TypeOf(existingSubVal) == reflect.TypeOf(v) {
						result[k] = mergeContent(existingSubVal, v)
						continue
					}
				}
				
				// Otherwise just overwrite
				result[k] = v
			}
			
			return result
		}
		
		// If types don't match, just add a new entry
		existingVal["_merged"] = new
		return existingVal
		
	default:
		// For other types, create an array
		return []interface{}{existing, new}
	}
}
