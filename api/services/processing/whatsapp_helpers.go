package processing

import (
	"log"
	"os"
	"regexp"
	"strings"
	"time"
)

// List of common WhatsApp timestamp layouts to try parsing
// Based on common locale settings. Order might matter if formats are ambiguous.
var whatsappDateTimeLayouts = []string{
	"1/2/06, 3:04 PM",   // M/D/YY, H:MM PM (US)
	"1/2/06, 15:04",     // M/D/YY, HH:MM (US 24h)
	"01/02/06, 15:04",   // DD/MM/YY, HH:MM (UK/EU)
	"02.01.06, 15:04",   // DD.MM.YY, HH:MM (German/EU)
	"2006-01-02, 15:04", // YYYY-MM-DD, HH:MM (ISO-like)
	// Add more layouts as needed based on observed formats
}

// parseWhatsAppDateTime tries to parse a string using known WhatsApp layouts
func parseWhatsAppDateTime(dateTimeStr string) (time.Time, bool) {
	isDebug := os.Getenv("PROMZ_DEBUG") == "true"
	trimmedStr := strings.TrimSpace(dateTimeStr)

	// Replace Narrow No-Break Space (U+202F) with a regular space
	replacedStr := strings.ReplaceAll(trimmedStr, "\u202F", " ")

	// Parse for datetime only if string is not empty
	if replacedStr != "" {
		for _, layout := range whatsappDateTimeLayouts {
			t, err := time.Parse(layout, replacedStr)
			if err == nil {
				return t, true // Successfully parsed
			}
		}
		if isDebug {
			log.Printf("[WhatsAppHelpers DEBUG] -> Failed to parse with any known layout")
		}
	}

	return time.Time{}, false // No layout matched
}

// Helper functions for WhatsApp content processing

// extractParticipants extracts participant names from WhatsApp chat content
func extractParticipants(content string) []string {
	isDebug := os.Getenv("PROMZ_DEBUG") == "true"
	if isDebug {
		log.Printf("[WhatsAppHelpers DEBUG] extractParticipants called")
	}
	participantMap := make(map[string]bool)

	// Liberal regex to capture potential datetime string and the rest of the line
	// Looks for: start_of_line, (anything), space, dash, space, (non-colon chars), colon, space
	participantRegex := regexp.MustCompile(`^(.+?) - ([^:]+): `)

	// Split content into lines and check each line
	lines := strings.Split(content, "\n")
	matchesCount := 0
	parsedCount := 0
	for _, line := range lines {
		match := participantRegex.FindStringSubmatch(line)
		if len(match) >= 3 { // Expecting 3 groups: full match, potential datetime, name
			matchesCount++
			potentialDateTime := match[1]
			participantName := strings.TrimSpace(match[2])

			// Try parsing the captured datetime string
			if _, ok := parseWhatsAppDateTime(potentialDateTime); ok {
				parsedCount++
				// Exclude system messages that might coincidentally match the pattern
				if !strings.Contains(participantName, "Messages and calls are end-to-end encrypted") {
					participantMap[participantName] = true
				}
			}
		}
	}
	if isDebug {
		log.Printf("[WhatsAppHelpers DEBUG] -> participantRegex checked %d lines, found %d potential lines, successfully parsed %d datetimes", len(lines), matchesCount, parsedCount)
	}

	// Convert map to slice
	participants := make([]string, 0, len(participantMap))
	for participant := range participantMap {
		participants = append(participants, participant)
	}
	if isDebug {
		log.Printf("[WhatsAppHelpers DEBUG] -> Extracted %d unique participants", len(participants))
	}

	return participants
}

// countMessages counts the number of messages in WhatsApp chat content
func countMessages(content string) int {
	isDebug := os.Getenv("PROMZ_DEBUG") == "true"
	if isDebug {
		log.Printf("[WhatsAppHelpers DEBUG] countMessages called")
	}
	// Liberal regex to capture potential datetime string and participant part
	// Only count lines that look like a participant message (have a colon after the dash)
	messageRegex := regexp.MustCompile(`^(.+?) - ([^:]+): `)

	// Split content into lines and count matches where datetime parsing succeeds
	lines := strings.Split(content, "\n")
	count := 0
	potentialCount := 0
	for _, line := range lines {
		match := messageRegex.FindStringSubmatch(line)
		if len(match) >= 3 { // Now expects 3 groups: full, datetime, name
			potentialCount++
			potentialDateTime := match[1]
			if _, ok := parseWhatsAppDateTime(potentialDateTime); ok {
				count++
			}
		}
	}
	if isDebug {
		log.Printf("[WhatsAppHelpers DEBUG] -> messageRegex checked %d lines, found %d potential participant lines, successfully parsed %d datetimes", len(lines), potentialCount, count)
	}
	return count
}

// extractGroupNameFromContent extracts the group name from WhatsApp chat content
func extractGroupNameFromContent(content string) string {
	isDebug := os.Getenv("PROMZ_DEBUG") == "true"
	if isDebug {
		log.Printf("[WhatsAppHelpers DEBUG] extractGroupNameFromContent called")
	}

	// Split content into lines and check the first few lines for potential headers
	lines := strings.Split(content, "\n")
	linesToCheck := 5 // Check the first 5 lines
	if len(lines) < linesToCheck {
		linesToCheck = len(lines)
	}

	// Regex patterns for potential headers (case-insensitive)
	// Note: The sample doesn't show a header, these are based on common patterns
	groupNameRegex := regexp.MustCompile(`(?i)WhatsApp Group Chat with (.+)`)
	altRegex := regexp.MustCompile(`(?i)WhatsApp Chat with (.+)`)

	for i := 0; i < linesToCheck; i++ {
		line := strings.TrimSpace(lines[i])

		match := groupNameRegex.FindStringSubmatch(line)
		if len(match) >= 2 {
			groupName := strings.TrimSpace(match[1])
			if isDebug {
				log.Printf("[WhatsAppHelpers DEBUG] -> groupNameRegex found match: '%s' on line %d", groupName, i+1)
			}
			return groupName
		}

		match = altRegex.FindStringSubmatch(line)
		if len(match) >= 2 {
			groupName := strings.TrimSpace(match[1])
			if isDebug {
				log.Printf("[WhatsAppHelpers DEBUG] -> altRegex found match: '%s' on line %d", groupName, i+1)
			}
			return groupName
		}
	}

	if isDebug {
		log.Printf("[WhatsAppHelpers DEBUG] -> No group name regex matched in the first %d lines", linesToCheck)
	}
	return ""
}

// Helper functions for type conversion

// getStringValue safely gets a string value from a map
func getStringValue(m map[string]interface{}, key string) string {
	if val, ok := m[key].(string); ok {
		return val
	}
	return ""
}

// getIntValue safely gets an int value from a map
func getIntValue(m map[string]interface{}, key string) int {
	switch v := m[key].(type) {
	case int:
		return v
	case int32:
		return int(v)
	case int64:
		return int(v)
	case float32:
		return int(v)
	case float64:
		return int(v)
	}
	return 0
}

// getInt32Value safely gets an int32 value from a map
func getInt32Value(m map[string]interface{}, key string) int32 {
	return int32(getIntValue(m, key))
}

// getBoolValue safely gets a bool value from a map
func getBoolValue(m map[string]interface{}, key string) bool {
	if val, ok := m[key].(bool); ok {
		return val
	}
	return false
}
