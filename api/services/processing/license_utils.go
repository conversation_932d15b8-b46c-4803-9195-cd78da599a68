package processing

import (
	"context"
	"log"
	"strings"
	"time"

	"promz.ai/api/internal/repository/db"
)

// GetUserLicenseTier retrieves the user's license tier from the database
// This is more secure than trusting client-provided tier information
func (h *UploadHandler) GetUserLicenseTier(userID string) (string, error) {
	// Create a context with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// Create a new Supabase client using the configuration
	supabaseClient, err := db.NewSupabaseClient(h.Config.SupabaseURL, h.Config.SupabaseKey)
	if err != nil {
		log.Printf("Error creating Supabase client: %v", err)
		return "free", nil // Default to free tier on error
	}

	// Check the user's license status
	result, err := supabaseClient.CheckLicenseStatus(ctx, userID)
	if err != nil {
		log.Printf("Error checking license status: %v", err)
		return "free", nil // Default to free tier on error
	}

	// Extract license type from the result
	licenseType, ok := result["license_type"].(string)
	if !ok || licenseType == "" {
		log.Printf("License type not found or invalid in response, defaulting to free tier")
		return "free", nil
	}

	// Convert license type to lowercase for consistency
	return strings.ToLower(licenseType), nil
}
