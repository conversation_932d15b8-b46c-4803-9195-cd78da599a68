package processing

import (
	"context"
	"fmt"
)

// ProcessJob processes a job with the given ID
func (h *UploadHandler) ProcessJob(jobID string) (ProcessingResult, error) {
	h.jobsMutex.RLock()
	job, exists := h.jobs[jobID]
	h.jobsMutex.RUnlock()

	if !exists {
		return ProcessingResult{}, fmt.Errorf("job not found: %s", jobID)
	}

	// Update job status
	h.updateJobStatus(jobID, StatusProcessing, 0.2, "")

	// Process based on processing type
	var result ProcessingResult
	var err error

	switch job.ProcessingType {
	case "whatsapp_extraction":
		result, err = h.processWhatsAppJob(job)
	case "content_detection":
		// For content detection, first detect the content type, then process accordingly
		contentType, err := h.detectContentType(job)
		if err != nil {
			h.updateJobStatus(jobID, StatusFailed, 0.0, fmt.Sprintf("Content detection failed: %v", err))
			return ProcessingResult{}, err
		}

		// Update the job's content type based on detection
		h.jobsMutex.Lock()
		job.ContentType = contentType
		h.jobs[jobID] = job
		h.jobsMutex.Unlock()

		// Process based on detected content type
		switch contentType {
		case string(ContentTypeWhatsApp):
			result, _ = h.processWhatsAppJob(job)
		default:
			result, _ = h.processGenericJob(job)
		}
	default:
		result, _ = h.processGenericJob(job)
	}

	if err != nil {
		h.updateJobStatus(jobID, StatusFailed, 0.0, err.Error())
		return ProcessingResult{}, err
	}

	// Update job status
	h.updateJobStatus(jobID, StatusCompleted, 1.0, "")

	// Store result ID
	h.jobsMutex.Lock()
	job.ResultID = result.ID
	h.jobs[jobID] = job
	h.jobsMutex.Unlock()

	return result, nil
}

// processWhatsAppJob processes a WhatsApp job
func (h *UploadHandler) processWhatsAppJob(job *ProcessingJob) (ProcessingResult, error) {
	// Use the content processor to process the job
	ctx := context.Background()
	return h.Processor.ProcessJob(ctx, job)
}

// processGenericJob processes a generic job
func (h *UploadHandler) processGenericJob(job *ProcessingJob) (ProcessingResult, error) {
	// Use the content processor to process the job
	ctx := context.Background()
	return h.Processor.ProcessJob(ctx, job)
}
