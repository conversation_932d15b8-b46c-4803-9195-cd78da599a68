package processing

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"promz.ai/api/utils/config"
)

// UploadHandler handles file uploads and processing
type UploadHandler struct {
	StorageService *StorageService
	Processor      ContentProcessor
	Config         *config.Config
	jobs           map[string]*ProcessingJob
	jobsMutex      sync.RWMutex
}

// NewUploadHandler creates a new upload handler
func NewUploadHandler(storageService *StorageService, processor ContentProcessor, cfg *config.Config) *UploadHandler {
	return &UploadHandler{
		StorageService: storageService,
		Processor:      processor,
		Config:         cfg,
		jobs:           make(map[string]*ProcessingJob),
		jobsMutex:      sync.RWMutex{},
	}
}

// HandleUpload handles file upload requests
func (h *UploadHandler) HandleUpload(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		log.Printf("Unauthorized: missing user ID")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized: missing user ID"})
		return
	}

	userIDStr, ok := userID.(string)
	if !ok || userIDStr == "" {
		log.Printf("Unauthorized: invalid user ID")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized: invalid user ID"})
		return
	}

	// Get license type from context or metadata
	licenseTier := "free" // Default to free tier
	if tier, exists := c.Get("license_type"); exists {
		if tierStr, ok := tier.(string); ok && tierStr != "" {
			licenseTier = tierStr
		}
	}

	// Get file from form
	file, header, err := c.Request.FormFile("file")
	if err != nil {
		log.Printf("Error getting file: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Error getting file: " + err.Error()})
		return
	}
	defer file.Close()

	// Check file size against tier limits
	tierConfig := h.Config.GetTierConfig(licenseTier)
	if header.Size > tierConfig.MaxFileSizeBytes {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": fmt.Sprintf("File size exceeds limit for %s tier: %d bytes",
				licenseTier, tierConfig.MaxFileSizeBytes),
		})
		return
	}

	// Create a unique ID for this job
	id := uuid.New().String()

	// Get metadata
	var metadata map[string]interface{}
	metadataStr := c.PostForm("metadata")
	if metadataStr != "" {
		if err := json.Unmarshal([]byte(metadataStr), &metadata); err != nil {
			log.Printf("Error parsing metadata: %v", err)
			c.JSON(http.StatusBadRequest, gin.H{"error": "Error parsing metadata: " + err.Error()})
			return
		}
	}
	if metadata == nil {
		metadata = make(map[string]interface{})
	}

	// Add user ID and license tier to metadata
	metadata["userId"] = userIDStr
	metadata["licenseTier"] = licenseTier

	// Add TLS configuration information to metadata to ensure client and server match
	metadata["useTLS"] = h.Config.UseTLS
	if h.Config.UseTLS {
		// Only include these if TLS is enabled
		metadata["tlsCertAvailable"] = h.Config.TLSCertPath != ""
		metadata["tlsKeyAvailable"] = h.Config.TLSKeyPath != ""
	}

	// Store file in temp location
	tempFilePath := filepath.Join(os.TempDir(), id+filepath.Ext(header.Filename))
	tempFile, err := os.Create(tempFilePath)
	if err != nil {
		log.Printf("Error creating temp file: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Error creating temp file: " + err.Error()})
		return
	}

	_, err = io.Copy(tempFile, file)
	tempFile.Close()
	if err != nil {
		log.Printf("Error saving temp file: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Error saving temp file: " + err.Error()})
		return
	}

	// Upload to GCS
	objectName := fmt.Sprintf("uploads/%s/%s%s", userIDStr, id, filepath.Ext(header.Filename))
	if err := h.StorageService.UploadFile(c.Request.Context(), tempFilePath, objectName, tierConfig.MaxStorageDays); err != nil {
		log.Printf("Error uploading to storage: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Error uploading to storage: " + err.Error()})
		return
	}

	// Create job with token limits
	job := &ProcessingJob{
		ID:          id,
		FileName:    header.Filename,
		MimeType:    header.Header.Get("Content-Type"),
		Status:      StatusQueued,
		Progress:    0.0,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		ExpiresAt:   time.Now().Add(time.Duration(tierConfig.MaxStorageDays) * 24 * time.Hour),
		FilePath:    objectName,
		UserID:      userIDStr,
		LicenseTier: licenseTier,
		TokensLimit: tierConfig.MaxTokensPerFile,
	}

	// Store job in memory
	h.jobsMutex.Lock()
	h.jobs[id] = job
	h.jobsMutex.Unlock()

	// Start processing in background
	go func() {
		// Update status to processing
		h.updateJobStatus(id, StatusProcessing, 0.1, "")

		// Define the large file threshold (50MB)
		const largeFileThreshold = 50 * 1024 * 1024
		// Define default chunk size (5MB)
		const defaultChunkSize = 5 * 1024 * 1024

		// Process file using appropriate method based on size
		var result ProcessingResult
		var err error

		if header.Size > largeFileThreshold {
			// Use chunked processing for large files
			result, err = h.Processor.ProcessLargeJob(context.Background(), job, defaultChunkSize)
		} else {
			// Use standard processing for smaller files
			result, err = h.Processor.ProcessJob(context.Background(), job)
		}

		if err != nil {
			// Check if this is a token limit exceeded error
			if job.TokensExceeded {
				// Mark as completed but with token limit warning
				h.updateJobStatus(id, StatusCompleted, 1.0, fmt.Sprintf("Token limit of %d exceeded. Processing was partial.", job.TokensLimit))
			} else {
				log.Printf("Error processing file: %v", err)
				h.updateJobStatus(id, StatusFailed, 0.0, err.Error())
				return
			}
		}

		// Get the result ID
		resultID := result.ID

		// Update job with result ID and token count
		h.jobsMutex.Lock()
		if job, exists := h.jobs[id]; exists {
			job.ResultID = resultID
			h.jobs[id] = job
		}
		h.jobsMutex.Unlock()

		// If not already marked as completed with token limit warning
		if !job.TokensExceeded {
			h.updateJobStatus(id, StatusCompleted, 1.0, "")
		}
	}()

	// Clean up temp file
	os.Remove(tempFilePath)

	// Return response with token limit information
	c.JSON(http.StatusOK, UploadResponse{
		ID:                   id,
		Status:               string(job.Status),
		EstimatedTimeSeconds: 30, // Default estimate
		MaxTokens:            tierConfig.MaxTokensPerFile,
		FileSize:             header.Size,
		LicenseTier:          licenseTier,
	})
}

// HandleStatus handles status check requests
func (h *UploadHandler) HandleStatus(c *gin.Context) {
	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		log.Printf("Unauthorized: missing user ID")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized: missing user ID"})
		return
	}

	userIDStr, ok := userID.(string)
	if !ok || userIDStr == "" {
		log.Printf("Unauthorized: invalid user ID")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized: invalid user ID"})
		return
	}

	// Get job ID from path
	id := c.Param("id")

	// Get job
	h.jobsMutex.RLock()
	job, exists := h.jobs[id]
	h.jobsMutex.RUnlock()

	if !exists {
		c.JSON(http.StatusNotFound, gin.H{"error": "Job not found"})
		return
	}

	// Check if job belongs to user
	if job.UserID != userIDStr {
		log.Printf("Unauthorized: job does not belong to user")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized: job does not belong to user"})
		return
	}

	response := StatusResponse{
		ID:              job.ID,
		Status:          string(job.Status),
		Progress:        job.Progress,
		TokensProcessed: job.TokensProcessed,
		TokensLimit:     job.TokensLimit,
		TokensExceeded:  job.TokensExceeded,
	}

	if job.Error != "" {
		response.Error = job.Error
	}

	if job.ResultID != "" {
		response.ResultID = job.ResultID
	}

	c.JSON(http.StatusOK, response)
}

// HandleResults handles result retrieval requests
func (h *UploadHandler) HandleResults(c *gin.Context) {
	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		log.Printf("Unauthorized: missing user ID")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized: missing user ID"})
		return
	}

	userIDStr, ok := userID.(string)
	if !ok || userIDStr == "" {
		log.Printf("Unauthorized: invalid user ID")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized: invalid user ID"})
		return
	}

	// Get job ID from path
	id := c.Param("id")

	// Get job
	h.jobsMutex.RLock()
	job, exists := h.jobs[id]
	h.jobsMutex.RUnlock()

	if !exists {
		c.JSON(http.StatusNotFound, gin.H{"error": "Job not found"})
		return
	}

	// Check if job belongs to user
	if job.UserID != userIDStr {
		log.Printf("Unauthorized: job does not belong to user")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized: job does not belong to user"})
		return
	}

	if job.Status != StatusCompleted {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Processing not complete"})
		return
	}

	// Retrieve results from storage
	result, err := h.Processor.GetResults(c.Request.Context(), job)
	if err != nil {
		log.Printf("Error retrieving results: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Error retrieving results: " + err.Error()})
		return
	}

	// Ensure contentType is always provided
	if result.ContentType == "" {
		result.ContentType = "unknown"
	}

	// Always provide a contentUrl and set content to nil
	if result.ContentUrl == "" && result.Content != nil {
		// If we have content but no URL, generate a temporary URL
		contentID := uuid.New().String()
		contentPath := fmt.Sprintf("results/%s/%s", job.UserID, contentID)

		// Store the content for URL access
		if err := h.StorageService.StoreObject(c.Request.Context(), contentPath, result.Content, 24*time.Hour); err != nil {
			log.Printf("Warning: Failed to store content for URL access: %v", err)
		} else {
			// Set the content URL
			result.ContentUrl = fmt.Sprintf("%s/storage/%s", h.Config.APIBaseURL, contentPath)

			// Update the job with the result ID
			h.jobsMutex.Lock()
			job.ResultID = contentID
			h.jobsMutex.Unlock()
		}
	}

	// Always set content to nil to avoid sending it directly
	result.Content = nil

	c.JSON(http.StatusOK, result)
}

// isJSON checks if a byte slice contains valid JSON
func isJSON(data []byte) bool {
	var js interface{}
	return json.Unmarshal(data, &js) == nil
}

// HandleGetContent serves content via URL
func (h *UploadHandler) HandleGetContent(c *gin.Context) {
	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		log.Printf("Unauthorized: missing user ID")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized: missing user ID"})
		return
	}

	userIDStr, ok := userID.(string)
	if !ok || userIDStr == "" {
		log.Printf("Unauthorized: invalid user ID")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized: invalid user ID"})
		return
	}

	// Get path from URL
	path := c.Param("path")
	if path == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Missing path"})
		return
	}

	// Ensure the path starts with the user's ID to prevent unauthorized access
	expectedPrefix := fmt.Sprintf("results/%s/", userIDStr)
	if !strings.HasPrefix(path, expectedPrefix) {
		log.Printf("Unauthorized: attempt to access content outside user's directory")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized access"})
		return
	}

	// Retrieve content from storage
	contentReader, err := h.StorageService.GetObject(c.Request.Context(), path)
	if err != nil {
		log.Printf("Error retrieving content: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Error retrieving content: " + err.Error()})
		return
	}

	if contentReader == nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Content not found"})
		return
	}

	defer contentReader.Close()

	// Read the content
	contentBytes, err := io.ReadAll(contentReader)
	if err != nil {
		log.Printf("Error reading content: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Error reading content: " + err.Error()})
		return
	}

	// Try to determine content type from the content
	contentType := http.DetectContentType(contentBytes)

	// Check if it's JSON
	if isJSON(contentBytes) {
		contentType = "application/json"
	}

	// Set content type header
	c.Header("Content-Type", contentType)

	// Serve the content
	c.Data(http.StatusOK, contentType, contentBytes)
}

// HandleCancel handles cancellation requests
func (h *UploadHandler) HandleCancel(c *gin.Context) {
	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		log.Printf("Unauthorized: missing user ID")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized: missing user ID"})
		return
	}

	userIDStr, ok := userID.(string)
	if !ok || userIDStr == "" {
		log.Printf("Unauthorized: invalid user ID")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized: invalid user ID"})
		return
	}

	// Get job ID from path
	id := c.Param("id")

	// Get job
	h.jobsMutex.RLock()
	job, exists := h.jobs[id]
	h.jobsMutex.RUnlock()

	if !exists {
		c.JSON(http.StatusNotFound, gin.H{"error": "Job not found"})
		return
	}

	// Check if job belongs to user
	if job.UserID != userIDStr {
		log.Printf("Unauthorized: job does not belong to user")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized: job does not belong to user"})
		return
	}

	// Only cancel if not completed
	if job.Status == StatusCompleted || job.Status == StatusFailed {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Cannot cancel completed or failed job"})
		return
	}

	// Update status to failed with cancellation message
	h.updateJobStatus(id, StatusFailed, 0.0, "Cancelled by user")

	// Return success
	c.JSON(http.StatusOK, gin.H{"success": true})
}

// Helper method to update job status
func (h *UploadHandler) updateJobStatus(id string, status ProcessingStatus, progress float64, errorMsg string) {
	h.jobsMutex.Lock()
	defer h.jobsMutex.Unlock()

	job, exists := h.jobs[id]
	if !exists {
		return
	}

	job.Status = status
	job.Progress = progress
	job.Error = errorMsg
	job.UpdatedAt = time.Now()
}

// CleanupExpiredJobs removes expired jobs and their resources
func (h *UploadHandler) CleanupExpiredJobs(ctx context.Context) {
	h.jobsMutex.Lock()
	defer h.jobsMutex.Unlock()

	now := time.Now()
	for id, job := range h.jobs {
		if now.After(job.ExpiresAt) {
			// Delete from storage if needed
			if job.FilePath != "" {
				h.StorageService.DeleteObject(ctx, job.FilePath)
			}

			// Delete result if exists
			if job.ResultID != "" {
				h.StorageService.DeleteObject(ctx, fmt.Sprintf("results/%s", job.ResultID))
			}

			// Remove from jobs map
			delete(h.jobs, id)
		}
	}
}
