package processing

import (
	"context"
	"log"
	"time"

	"promz.ai/api/utils/config"
)

// ContentProcessingService is the main service for content processing
type ContentProcessingService struct {
	storageService *StorageService
	processor      *CompositeContentProcessor
	uploadHandler  *UploadHandler
	bucketName     string
	jobStore       JobStore
}

// NewContentProcessingService creates a new content processing service
func NewContentProcessingService(bucketName string, cfg *config.Config) (*ContentProcessingService, error) {
	ctx := context.Background()

	// Create storage service
	storageService, err := NewStorageService(ctx, bucketName)
	if err != nil {
		return nil, err
	}

	// Create WhatsApp processor
	whatsAppProcessor := NewWhatsAppProcessor()

	// Create WhatsApp service, injecting the processor
	whatsAppService := NewWhatsAppService(whatsAppProcessor)

	// Create JobStore
	jobStore := NewInMemoryJobStore()

	// Create content processor
	processor := NewCompositeContentProcessor(storageService, whatsAppService, jobStore)

	// Create upload handler
	uploadHandler := NewUploadHandler(storageService, processor, cfg)

	return &ContentProcessingService{
		storageService: storageService,
		processor:      processor,
		uploadHandler:  uploadHandler,
		bucketName:     bucketName,
		jobStore:       jobStore,
	}, nil
}

// StartCleanupTask starts a background task to clean up expired jobs
func (s *ContentProcessingService) StartCleanupTask(ctx context.Context) {
	go func() {
		ticker := time.NewTicker(1 * time.Hour)
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				log.Println("Running cleanup of expired content processing jobs")
				s.uploadHandler.CleanupExpiredJobs(context.Background())
			case <-ctx.Done():
				return
			}
		}
	}()
}

// Close closes the service and releases resources
func (s *ContentProcessingService) Close() error {
	return s.storageService.Close()
}

// GetProcessor returns the content processor
func (s *ContentProcessingService) GetProcessor() *CompositeContentProcessor {
	return s.processor
}

// GetUploadHandler returns the upload handler
func (s *ContentProcessingService) GetUploadHandler() *UploadHandler {
	return s.uploadHandler
}

// GetStorageService returns the storage service
func (s *ContentProcessingService) GetStorageService() *StorageService {
	return s.storageService
}

// GetJobStore returns the JobStore instance.
func (s *ContentProcessingService) GetJobStore() JobStore {
	return s.jobStore
}
