package processing

import (
	"archive/zip"
	"fmt"
	"io"
	"path/filepath"
	"strings"
)

// detectContentType detects the content type of a job
func (h *UploadHandler) detectContentType(job *ProcessingJob) (string, error) {
	// If content type is already specified and not generic, use it
	if job.ContentType != "" && job.ContentType != "zip" && job.ContentType != "generic" {
		return job.ContentType, nil
	}

	// Get file path from job
	filePath := job.FilePath
	if filePath == "" {
		return "", fmt.Errorf("no file path in job")
	}

	// Check file extension
	ext := strings.ToLower(filepath.Ext(filePath))
	if ext != ".zip" {
		// For non-zip files, return the content type based on extension
		return h.getContentTypeFromExtension(ext), nil
	}

	// For zip files, we need to analyze the contents
	return h.detectZipContentType(filePath)
}

// detectZipContentType analyzes a ZIP file to determine its content type
func (h *UploadHandler) detectZipContentType(filePath string) (string, error) {
	// Open the zip file
	reader, err := zip.OpenReader(filePath)
	if err != nil {
		return string(ContentTypeGeneric), fmt.Errorf("failed to open zip file: %v", err)
	}
	defer reader.Close()

	// Check for WhatsApp content
	if h.containsWhatsAppContent(reader) {
		return string(ContentTypeWhatsApp), nil
	}

	// Default to generic content
	return string(ContentTypeGeneric), nil
}

// containsWhatsAppContent checks if a zip file contains WhatsApp chat exports
func (h *UploadHandler) containsWhatsAppContent(reader *zip.ReadCloser) bool {
	// Look for common WhatsApp export patterns
	for _, file := range reader.File {
		fileName := strings.ToLower(file.Name)
		
		// Check for WhatsApp chat files (common pattern is chat files ending with .txt)
		if strings.Contains(fileName, "chat") && strings.HasSuffix(fileName, ".txt") {
			// Open the file to check content
			if h.isWhatsAppChatContent(file) {
				return true
			}
		}
	}
	
	return false
}

// isWhatsAppChatContent checks if a file contains WhatsApp chat content
func (h *UploadHandler) isWhatsAppChatContent(file *zip.File) bool {
	// Open the file
	rc, err := file.Open()
	if err != nil {
		return false
	}
	defer rc.Close()

	// Read the first 1KB of the file to check for WhatsApp patterns
	buffer := make([]byte, 1024)
	n, err := rc.Read(buffer)
	if err != nil && err != io.EOF {
		return false
	}

	// Convert to string for pattern matching
	content := string(buffer[:n])
	
	// Check for common WhatsApp chat patterns
	// WhatsApp chats typically start with dates in formats like:
	// [DD/MM/YY, HH:MM:SS] or [MM/DD/YY, HH:MM:SS]
	return strings.Contains(content, "[") && 
		   (strings.Contains(content, "/") || strings.Contains(content, "-")) && 
		   strings.Contains(content, ":")
}

// getContentTypeFromExtension returns a content type based on file extension
func (h *UploadHandler) getContentTypeFromExtension(ext string) string {
	switch ext {
	case ".txt":
		return "text"
	case ".pdf":
		return "pdf"
	case ".doc", ".docx":
		return "document"
	case ".jpg", ".jpeg", ".png", ".gif":
		return "image"
	default:
		return "generic"
	}
}
