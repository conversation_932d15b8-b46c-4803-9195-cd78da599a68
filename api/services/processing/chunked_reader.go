package processing

import (
	"errors"
	"io"
)

// ChunkedReader provides functionality to read a file in chunks
type ChunkedReader struct {
	reader       io.Reader
	chunkSize    int
	totalBytes   int64
	processedBytes int64
	buffer       []byte
	eof          bool
}

// NewChunkedReader creates a new chunked reader
func NewChunkedReader(reader io.Reader, chunkSize int) *ChunkedReader {
	return &ChunkedReader{
		reader:    reader,
		chunkSize: chunkSize,
		buffer:    make([]byte, chunkSize),
	}
}

// SetTotalBytes sets the total number of bytes in the source
func (c *ChunkedReader) SetTotalBytes(totalBytes int64) {
	c.totalBytes = totalBytes
}

// TotalBytes returns the total number of bytes in the source
func (c *ChunkedReader) TotalBytes() int64 {
	return c.totalBytes
}

// ProcessedBytes returns the number of bytes processed so far
func (c *ChunkedReader) ProcessedBytes() int64 {
	return c.processedBytes
}

// Next checks if there are more chunks to read
func (c *ChunkedReader) Next() bool {
	return !c.eof
}

// Read reads the next chunk
func (c *ChunkedReader) Read() ([]byte, error) {
	if c.eof {
		return nil, errors.New("end of file reached")
	}

	n, err := io.ReadFull(c.reader, c.buffer)
	c.processedBytes += int64(n)

	if err == io.EOF || err == io.ErrUnexpectedEOF {
		c.eof = true
		return c.buffer[:n], nil
	}

	if err != nil {
		return nil, err
	}

	return c.buffer, nil
}

// Progress returns the progress as a float between 0 and 1
func (c *ChunkedReader) Progress() float64 {
	if c.totalBytes <= 0 {
		return 0
	}
	return float64(c.processedBytes) / float64(c.totalBytes)
}
