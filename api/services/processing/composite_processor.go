package processing

import (
	"context"
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"strings"
	"time"

	pb "promz.ai/api/proto/gen"
)

// CompositeContentProcessor combines multiple type-specific processors
type CompositeContentProcessor struct {
	storageService   *StorageService
	processors       map[ContentType]FileProcessor
	defaultProcessor FileProcessor
	jobStore         JobStore // Service for tracking live job status
}

// Ensure CompositeContentProcessor implements ContentProcessor interface
var _ ContentProcessor = (*CompositeContentProcessor)(nil)

// NewCompositeContentProcessor creates a new composite processor
func NewCompositeContentProcessor(storageService *StorageService, whatsAppService *WhatsAppService, jobStore JobStore) *CompositeContentProcessor {
	cp := &CompositeContentProcessor{
		storageService: storageService,
		processors:     make(map[ContentType]FileProcessor),
		jobStore:       jobStore,
	}

	if jobStore == nil {
		log.Println("Warning: NewCompositeContentProcessor created with nil JobStore")
	}

	// Register processors
	zipProcessor := NewZipProcessor(storageService, whatsAppService)
	cp.processors[ContentTypeZip] = zipProcessor

	// We don't need a separate WhatsApp processor here
	// The ZipProcessor already has a WhatsAppProcessor that handles WhatsApp content
	// We'll detect WhatsApp content in the DetectContentType method and route accordingly

	// Set the ZIP processor as the default for now
	// Later we can implement more specialized processors
	cp.defaultProcessor = zipProcessor

	return cp
}

// UploadFile processes a file by detecting its type and using the appropriate processor
func (cp *CompositeContentProcessor) UploadFile(ctx context.Context, job *ProcessingJob, metadata map[string]interface{}) (string, error) {
	// Download file from storage
	tempFilePath := filepath.Join(os.TempDir(), job.ID+filepath.Ext(job.FileName))
	defer os.Remove(tempFilePath)

	if err := cp.storageService.DownloadFile(ctx, job.FilePath, tempFilePath); err != nil {
		return "", fmt.Errorf("failed to download file: %v", err)
	}

	// Open file for content type detection
	file, err := os.Open(tempFilePath)
	if err != nil {
		return "", fmt.Errorf("failed to open file: %v", err)
	}
	defer file.Close()

	// Detect content type
	contentType, err := cp.DetectContentType(ctx, file, job.FileName)
	if err != nil {
		return "", fmt.Errorf("failed to detect content type: %v", err)
	}

	// Update job content type
	job.ContentType = string(contentType)

	// Reset file position
	if _, err := file.Seek(0, io.SeekStart); err != nil {
		return "", fmt.Errorf("failed to reset file position: %v", err)
	}

	// Special handling for WhatsApp content
	if contentType == ContentTypeWhatsApp {
		// Use the ZipProcessor's WhatsAppProcessor to process WhatsApp content
		if zipProcessor, ok := cp.processors[ContentTypeZip].(*ZipProcessor); ok && zipProcessor.whatsAppProcessor != nil {
			// Read the file content
			content, err := os.ReadFile(tempFilePath)
			if err != nil {
				return "", fmt.Errorf("failed to read file: %v", err)
			}

			// Process the WhatsApp content
			result := zipProcessor.whatsAppProcessor.ProcessContent(string(content), job.FileName)

			// Create a processing result
			processingResult := ProcessingResult{
				ID:          job.ID + "_result",
				ContentType: string(ContentTypeWhatsApp),
				Content:     result.Content,
				Metadata:    result.Metadata,
				ExpiresAt:   job.ExpiresAt,
			}

			// Convert to proto
			protoResult := mapLocalResultToProto(processingResult)
			
			// Use WhatsApp metadata directly from the result
			if result.WhatsAppMetadata != nil {
				// Set the WhatsApp metadata in the oneof field
				protoResult.ContentMetadata = &pb.ProcessingResult_WhatsappMetadata{
					WhatsappMetadata: result.WhatsAppMetadata,
				}
				
				// Set additional fields to help client-side rendering
				protoResult.SourceType = "conversation"
				protoResult.Source = "WhatsApp"
				protoResult.AppName = "WhatsApp"
				
				// Set title based on available metadata
				if result.WhatsAppMetadata.GroupName != "" {
					protoResult.Title = result.WhatsAppMetadata.GroupName
				} else if result.WhatsAppMetadata.ChatName != "" {
					protoResult.Title = result.WhatsAppMetadata.ChatName
				} else if len(result.WhatsAppMetadata.Participants) > 0 {
					// If no group or chat name, use the first few participants
					if len(result.WhatsAppMetadata.Participants) == 1 {
						protoResult.Title = "Chat with " + result.WhatsAppMetadata.Participants[0]
					} else if len(result.WhatsAppMetadata.Participants) == 2 {
						protoResult.Title = "Chat with " + result.WhatsAppMetadata.Participants[0] + " and " + result.WhatsAppMetadata.Participants[1]
					} else {
						protoResult.Title = "Group Chat with " + result.WhatsAppMetadata.Participants[0] + " and others"
					}
				} else {
					protoResult.Title = "WhatsApp Chat"
				}
				
				log.Printf("[Job %s] Added WhatsApp metadata to ProcessingResult with title: %s", job.ID, protoResult.Title)
			}

			// Store the result
			resultID, err := cp.storageService.StoreResult(ctx, protoResult, job.ExpiresAt)
			if err != nil {
				return "", fmt.Errorf("failed to store result: %v", err)
			}

			// Return the result ID
			return resultID, nil
		}
	}

	// --- Mark job as Processing before delegating ---
	log.Printf("[Job %s] Setting status to Processing and updating JobStore", job.ID)
	job.Status = StatusProcessing
	job.UpdatedAt = time.Now()
	if cp.jobStore != nil {
		cp.jobStore.UpdateJob(job) // Update store with Processing status
	} else {
		log.Printf("[Job %s] Warning: JobStore is nil, cannot update status to Processing in store", job.ID)
	}
	// -------------------------------------------------

	// Get appropriate processor for non-WhatsApp content
	processor, exists := cp.processors[contentType]
	if !exists {
		// Use default processor for unknown types
		processor = cp.defaultProcessor
		if processor == nil {
			return "", fmt.Errorf("no processor available for content type: %v", contentType)
		}
	}

	// Process file with the appropriate processor
	resultID, err := processor.UploadFile(ctx, job, metadata)

	// --- Update final status in JobStore and remove ---
	finalStatus := StatusCompleted
	if err != nil {
		finalStatus = StatusFailed
		job.Error = err.Error() // Store the error message
		log.Printf("[Job %s] Processor failed: %v. Setting status to Failed.", job.ID, err)
	} else {
		log.Printf("[Job %s] Processor completed successfully. Setting status to Completed.", job.ID)
	}

	job.Status = finalStatus
	job.UpdatedAt = time.Now()
	if cp.jobStore != nil {
		cp.jobStore.UpdateJob(job) // Update store with final status

		// Remove the job from the live store shortly after final update
		// A goroutine with a small delay gives polling streamers a chance
		// to see the final Completed/Failed status before the job disappears.
		go func(idToRemove string) {
			time.Sleep(5 * time.Second) // Delay removal by 5 seconds
			cp.jobStore.RemoveJob(idToRemove)
			log.Printf("[Job %s] Removed job from JobStore after completion/failure and delay.", idToRemove)
		}(job.ID)

	} else {
		log.Printf("[Job %s] Warning: JobStore is nil, cannot update final status/remove job from store", job.ID)
	}
	// -------------------------------------------------

	// Return the resultID and error from the processor
	return resultID, err
}

// ProcessJob implements the ContentProcessor interface
func (cp *CompositeContentProcessor) ProcessJob(ctx context.Context, job *ProcessingJob) (ProcessingResult, error) {
	// Use UploadFile to process the job
	resultID, err := cp.UploadFile(ctx, job, job.Metadata)
	if err != nil {
		return ProcessingResult{}, err
	}

	// Get the result
	protoResult, err := cp.storageService.GetResult(ctx, resultID)
	if err != nil {
		return ProcessingResult{}, err
	}

	// Convert pointer to value
	return mapProtoResultToLocal(protoResult), nil
}

// ProcessLargeJob processes a large file in chunks with token tracking
func (cp *CompositeContentProcessor) ProcessLargeJob(ctx context.Context, job *ProcessingJob, chunkSize int) (ProcessingResult, error) {
	// Download file from storage
	tempFilePath := filepath.Join(os.TempDir(), job.ID+filepath.Ext(job.FileName))
	defer os.Remove(tempFilePath)

	if err := cp.storageService.DownloadFile(ctx, job.FilePath, tempFilePath); err != nil {
		return ProcessingResult{}, fmt.Errorf("failed to download file: %v", err)
	}

	// Open file for processing
	file, err := os.Open(tempFilePath)
	if err != nil {
		return ProcessingResult{}, fmt.Errorf("failed to open file: %v", err)
	}
	defer file.Close()

	// Get file info for size
	fileInfo, err := file.Stat()
	if err != nil {
		return ProcessingResult{}, fmt.Errorf("failed to get file info: %v", err)
	}

	// Detect content type
	contentType, err := cp.DetectContentType(ctx, file, job.FileName)
	if err != nil {
		return ProcessingResult{}, fmt.Errorf("failed to detect content type: %v", err)
	}

	// Reset file position
	if _, err := file.Seek(0, io.SeekStart); err != nil {
		return ProcessingResult{}, fmt.Errorf("failed to reset file position: %v", err)
	}

	// Create chunked reader
	chunks := NewChunkedReader(file, chunkSize)
	chunks.SetTotalBytes(fileInfo.Size())

	// Initialize result data
	var combinedContent interface{} = ""
	var tokensProcessed int64 = 0
	var resultID string

	// Process chunks incrementally
	chunkIndex := 0
	for chunks.Next() {
		// Update progress based on bytes processed
		progress := chunks.Progress()
		job.Progress = progress

		// Read chunk
		chunk, err := chunks.Read()
		if err != nil {
			return ProcessingResult{}, fmt.Errorf("error reading chunk %d: %v", chunkIndex, err)
		}

		// Create a temporary job for this chunk
		chunkJob := &ProcessingJob{
			ID:          fmt.Sprintf("%s_chunk_%d", job.ID, chunkIndex),
			UserID:      job.UserID,
			FileName:    job.FileName,
			MimeType:    job.MimeType,
			LicenseTier: job.LicenseTier,
			Metadata:    job.Metadata,
		}

		// Write chunk to temporary file
		chunkFilePath := filepath.Join(os.TempDir(), chunkJob.ID+filepath.Ext(job.FileName))
		defer os.Remove(chunkFilePath)

		chunkFile, err := os.Create(chunkFilePath)
		if err != nil {
			return ProcessingResult{}, fmt.Errorf("failed to create chunk file: %v", err)
		}

		_, err = chunkFile.Write(chunk)
		chunkFile.Close()
		if err != nil {
			return ProcessingResult{}, fmt.Errorf("failed to write chunk file: %v", err)
		}

		// Upload chunk to storage
		chunkObjectName := fmt.Sprintf("chunks/%s/%s_chunk_%d%s", job.UserID, job.ID, chunkIndex, filepath.Ext(job.FileName))
		if err := cp.storageService.UploadFile(ctx, chunkFilePath, chunkObjectName, 1); err != nil {
			return ProcessingResult{}, fmt.Errorf("failed to upload chunk: %v", err)
		}

		chunkJob.FilePath = chunkObjectName

		// Get appropriate processor
		processor, exists := cp.processors[contentType]
		if !exists {
			processor = cp.defaultProcessor
			if processor == nil {
				return ProcessingResult{}, fmt.Errorf("no processor available for content type: %v", contentType)
			}
		}

		// Process chunk
		chunkResultID, err := processor.UploadFile(ctx, chunkJob, job.Metadata)
		if err != nil {
			return ProcessingResult{}, fmt.Errorf("error processing chunk %d: %v", chunkIndex, err)
		}

		// Get chunk result
		protoChunkResult, err := cp.storageService.GetResult(ctx, chunkResultID)
		if err != nil {
			return ProcessingResult{}, fmt.Errorf("error getting chunk %d result: %v", chunkIndex, err)
		}

		// Estimate tokens used in this chunk (simplified estimation)
		chunkTokens := estimateTokens(protoChunkResult.Content)
		tokensProcessed += chunkTokens

		// Check if we've exceeded token limit
		if job.TokensLimit > 0 && tokensProcessed > job.TokensLimit {
			// Mark job as token limit exceeded
			job.TokensExceeded = true
			job.TokensProcessed = tokensProcessed

			// Create final result with what we've processed so far
			finalResult := ProcessingResult{
				ID:          job.ID,
				ContentType: string(contentType),
				Content:     combinedContent,
				Metadata: map[string]interface{}{
					"tokensProcessed":   tokensProcessed,
					"tokensLimit":       job.TokensLimit,
					"tokensExceeded":    true,
					"partialProcessing": true,
				},
				ExpiresAt: job.ExpiresAt,
			}

			// Store the final result
			protoFinalResult := mapLocalResultToProto(finalResult)
			_, err = cp.storageService.StoreResult(ctx, protoFinalResult, job.ExpiresAt)
			if err != nil {
				return ProcessingResult{}, fmt.Errorf("failed to store partial result: %v", err)
			}

			// Return the partial result with token limit exceeded
			return finalResult, fmt.Errorf("token limit exceeded: processed %d of %d tokens", tokensProcessed, job.TokensLimit)
		}

		// Merge chunk result with combined result
		combinedContent = mergeContent(combinedContent, protoChunkResult.Content)

		// Store the latest combined result for each chunk
		// This ensures we have partial results if processing is interrupted
		tempResult := ProcessingResult{
			ID:          job.ID,
			ContentType: string(contentType),
			Content:     combinedContent,
			Metadata: map[string]interface{}{
				"tokensProcessed": tokensProcessed,
				"chunksProcessed": chunkIndex + 1,
			},
			ExpiresAt: job.ExpiresAt,
		}

		protoTempResult := mapLocalResultToProto(tempResult)
		resultID, err = cp.storageService.StoreResult(ctx, protoTempResult, job.ExpiresAt)
		if err != nil {
			return ProcessingResult{}, fmt.Errorf("failed to store intermediate result: %v", err)
		}

		chunkIndex++
	}

	// Update job with token count
	job.TokensProcessed = tokensProcessed

	// Get the final result
	protoResult, err := cp.storageService.GetResult(ctx, resultID)
	if err != nil {
		return ProcessingResult{}, err
	}

	// Convert pointer to value
	return mapProtoResultToLocal(protoResult), nil
}

// GetResults retrieves processing results
func (cp *CompositeContentProcessor) GetResults(ctx context.Context, job *ProcessingJob) (ProcessingResult, error) {
	// If we have a result ID, use it to get the specific processor
	if job.ResultID != "" {
		// For now, we'll just use the storage service directly
		// In the future, we could route to specific processors based on content type
		protoResult, err := cp.storageService.GetResult(ctx, job.ResultID)
		if err != nil {
			return ProcessingResult{}, err
		}

		// Convert the protobuf result back to the local struct required by the interface
		return mapProtoResultToLocal(protoResult), nil
	}

	return ProcessingResult{}, fmt.Errorf("no result ID available")
}

// DetectContentType detects the content type of a file
func (cp *CompositeContentProcessor) DetectContentType(ctx context.Context, reader io.Reader, filename string) (ContentType, error) {
	// Simple detection based on file extension
	ext := strings.ToLower(filepath.Ext(filename))

	// Check for ZIP files
	if ext == ".zip" {
		return ContentTypeZip, nil
	}

	// Check for WhatsApp chat exports (often .txt files with specific content)
	if ext == ".txt" {
		// First check filename patterns
		if strings.Contains(strings.ToLower(filename), "whatsapp") {
			return ContentTypeWhatsApp, nil
		}

		// Get a small sample of the content to check
		buffer := make([]byte, 4096) // Read first 4KB
		n, err := reader.Read(buffer)
		if err != nil && err != io.EOF {
			return ContentTypeUnknown, fmt.Errorf("failed to read file: %v", err)
		}

		// Reset position if possible
		if seeker, ok := reader.(io.Seeker); ok {
			_, _ = seeker.Seek(0, io.SeekStart)
		}

		// Use the ZipProcessor's WhatsAppProcessor to detect WhatsApp content
		if zipProcessor, ok := cp.processors[ContentTypeZip].(*ZipProcessor); ok && zipProcessor.whatsAppProcessor != nil {
			isWhatsApp, confidence := zipProcessor.whatsAppProcessor.IsWhatsAppChat(string(buffer[:n]), filename)
			if isWhatsApp && confidence >= 0.5 {
				return ContentTypeWhatsApp, nil
			}
		}
	}

	// Check for PDF files
	if ext == ".pdf" {
		return ContentTypePDF, nil
	}

	// Default to unknown
	return ContentTypeUnknown, nil
}

// RegisterProcessor registers a processor for a specific content type
func (cp *CompositeContentProcessor) RegisterProcessor(contentType ContentType, processor FileProcessor) {
	cp.processors[contentType] = processor
}

// mapLocalResultToProto converts a local ProcessingResult to its protobuf equivalent.
// This function handles common metadata fields and prepares the structure for specific metadata types.
func mapLocalResultToProto(localResult ProcessingResult) *pb.ProcessingResult {
    // Convert content to string
    contentStr := ""
    if localResult.Content != nil {
        switch v := localResult.Content.(type) {
        case string:
            contentStr = v
        default:
            // Try to convert to string or use empty string
            contentStr = fmt.Sprintf("%v", localResult.Content)
            log.Printf("Warning: Content was not a string, converted from %T", localResult.Content)
        }
    }
    
    protoResult := &pb.ProcessingResult{
        JobId:       localResult.ID,
        ContentType: localResult.ContentType,
        Content:     contentStr,
        ExpiresAt:   localResult.ExpiresAt.Format(time.RFC3339Nano),
    }

    if localResult.ExpiresAt.IsZero() {
        protoResult.ExpiresAt = ""
    }

    // Extract common metadata fields
    if localResult.Metadata != nil {
        // Try to extract standard fields
        if url, ok := localResult.Metadata["url"].(string); ok {
            protoResult.SourceUrl = url
        }
        if author, ok := localResult.Metadata["author"].(string); ok {
            protoResult.Author = author
        }
        if title, ok := localResult.Metadata["title"].(string); ok {
            protoResult.Title = title
        }
        if source, ok := localResult.Metadata["source"].(string); ok {
            protoResult.Source = source
        }
        if appName, ok := localResult.Metadata["appName"].(string); ok {
            protoResult.AppName = appName
        }
        if sourceType, ok := localResult.Metadata["sourceType"].(string); ok {
            protoResult.SourceType = sourceType
        }
        if isZipContent, ok := localResult.Metadata["isZipContent"].(bool); ok {
            protoResult.IsZipContent = isZipContent
        }
        if fileName, ok := localResult.Metadata["fileName"].(string); ok {
            protoResult.FileName = fileName
        }
        if mimeType, ok := localResult.Metadata["mimeType"].(string); ok {
            protoResult.MimeType = mimeType
        }
        if displayName, ok := localResult.Metadata["displayName"].(string); ok {
            protoResult.DisplayName = displayName
        }
        if filePath, ok := localResult.Metadata["filePath"].(string); ok {
            protoResult.FilePath = filePath
        }
        
        // Handle specific content type metadata
        switch localResult.ContentType {
        case string(ContentTypeWhatsApp):
            // WhatsApp metadata is handled separately by ConvertToWhatsAppMetadataProto
            // and UpdateProcessingResultWithWhatsAppMetadata functions
            if isWhatsApp, ok := localResult.Metadata["isWhatsAppChat"].(bool); ok && isWhatsApp {
                // Set basic WhatsApp indicators even if detailed metadata is added later
                protoResult.Source = "WhatsApp"
                protoResult.SourceType = "conversation"
            }
        }
    }

	return protoResult
}

// mapProtoResultToLocal converts a protobuf ProcessingResult to its local equivalent.
// Note: This is a basic conversion and might need expansion for metadata.
func mapProtoResultToLocal(protoResult *pb.ProcessingResult) ProcessingResult {
	if protoResult == nil {
		return ProcessingResult{}
	}

	localResult := ProcessingResult{
		ID:          protoResult.JobId,
		ContentType: protoResult.ContentType, // Already string
		Content:     protoResult.Content,     // Assign proto string to local interface{}
		// Status and ErrorMessage are not part of the local ProcessingResult struct
		Metadata: make(map[string]interface{}), // Initialize, TODO: Map metadata if needed
	}

	// Convert ExpiresAt (assuming it's stored as RFC3339 string in proto)
	if protoResult.ExpiresAt != "" {
		if t, err := time.Parse(time.RFC3339Nano, protoResult.ExpiresAt); err == nil {
			localResult.ExpiresAt = t
		} else {
			log.Printf("Warning: Could not parse ExpiresAt '%s' during proto-to-local conversion: %v", protoResult.ExpiresAt, err)
		}
	}

	// Map explicit fields to local metadata
	// File metadata
	if protoResult.FileName != "" {
		localResult.Metadata["fileName"] = protoResult.FileName
	}
	if protoResult.MimeType != "" {
		localResult.Metadata["mimeType"] = protoResult.MimeType
	}
	if protoResult.DisplayName != "" {
		localResult.Metadata["displayName"] = protoResult.DisplayName
	}
	if protoResult.FilePath != "" {
		localResult.Metadata["filePath"] = protoResult.FilePath
	}

	// Other metadata
	if protoResult.Timestamp != 0 {
		localResult.Metadata["timestamp"] = protoResult.Timestamp
	}
	if protoResult.IsServerProcessed {
		localResult.Metadata["isServerProcessed"] = protoResult.IsServerProcessed
	}
	if protoResult.ProcessingType != "" {
		localResult.Metadata["processingType"] = protoResult.ProcessingType
	}
	if protoResult.SourceUrl != "" {
		localResult.Metadata["url"] = protoResult.SourceUrl
	}
	if protoResult.Author != "" {
		localResult.Metadata["author"] = protoResult.Author
	}
	if protoResult.Title != "" {
		localResult.Metadata["title"] = protoResult.Title
	}

	// Map ContentMetadata (oneof)
	switch meta := protoResult.ContentMetadata.(type) {
	case *pb.ProcessingResult_WhatsappMetadata:
		if meta.WhatsappMetadata != nil {
			// Store the entire proto message, or specific fields?
			// Storing the proto message might be easiest for now.
			localResult.Metadata["whatsappMetadata"] = meta.WhatsappMetadata
		}
		// Add cases for other specific metadata types here if needed
		// case *pb.ProcessingResult_OtherMetadataType:
		// 	 if meta.OtherMetadataType != nil {
		// 		 localResult.Metadata["otherMetadata"] = meta.OtherMetadataType
		// 	 }
	}

	return localResult
}

// --- New Mapping Functions (Job <-> Proto) ---

// MapLocalStatusToProto converts the internal UploadStatus enum to the Protobuf enum.
func MapLocalStatusToProto(localStatus ProcessingStatus) pb.UploadStatus {
	switch localStatus {
	case StatusQueued:
		return pb.UploadStatus_UPLOAD_STATUS_QUEUED
	case StatusProcessing:
		return pb.UploadStatus_UPLOAD_STATUS_PROCESSING
	case StatusCompleted:
		return pb.UploadStatus_UPLOAD_STATUS_COMPLETED
	case StatusFailed:
		return pb.UploadStatus_UPLOAD_STATUS_FAILED
	default:
		log.Printf("Warning: Unknown local UploadStatus '%s', mapping to UNSPECIFIED", localStatus)
		return pb.UploadStatus_UPLOAD_STATUS_UNSPECIFIED
	}
}

// MapProcessingJobToProtoUpdate maps a ProcessingJob to a UploadUpdate proto message.
// Used for sending live status updates via streams.
func MapProcessingJobToProtoUpdate(job *ProcessingJob) *pb.UploadUpdate {
	if job == nil {
		return nil
	}
	update := &pb.UploadUpdate{
		JobId:              job.ID,
		Status:             MapLocalStatusToProto(job.Status),
		ProgressPercentage: float32(job.Progress), // Direct conversion, assuming progress is 0.0-1.0
		CurrentStage:       job.Message,           // Use job message as current stage for now
		Message:            job.Message,           // Use job message as general message
	}
	// Potentially add specific error message if available
	if job.Error != "" {
		update.Message = job.Error // Overwrite message with specific error if present
	}
	return update
}

// MapProcessingJobToProtoResult maps a ProcessingJob to a ProcessingResult proto message.
// Used for saving the final result. Note that Content and specific metadata are not typically
// part of the Job struct itself and must be handled separately.
func MapProcessingJobToProtoResult(job *ProcessingJob) *pb.ProcessingResult {
	if job == nil {
		return nil
	}
	result := &pb.ProcessingResult{
		JobId:       job.ID,
		ContentType: job.ContentType,
		// Content is usually generated/retrieved separately, not directly from job struct
		// Content:      "",
		ExpiresAt:    job.ExpiresAt.Format(time.RFC3339Nano),
		Status:       MapLocalStatusToProto(job.Status),
		ErrorMessage: job.Error,
		// TODO: Map GeneralMetadata if needed from job.Metadata?
		// TODO: Map specific ContentMetadata if needed?
	}
	if job.ExpiresAt.IsZero() {
		result.ExpiresAt = ""
	}
	return result
}
