package processing

import (
	"context"
	"time"
)

// ProcessingStatus represents the status of a processing job
type ProcessingStatus string

const (
	StatusQueued     ProcessingStatus = "queued"
	StatusProcessing ProcessingStatus = "processing"
	StatusCompleted  ProcessingStatus = "completed"
	StatusFailed     ProcessingStatus = "failed"
)

// ProcessingJob represents a file processing job
type ProcessingJob struct {
	ID             string                 `json:"id"`
	UserID         string                 `json:"user_id"`
	FileName       string                 `json:"file_name"`
	MimeType       string                 `json:"mime_type"`
	FilePath       string                 `json:"file_path"`
	FileSize       int64                  `json:"file_size,omitempty"`
	Status         ProcessingStatus       `json:"status"`
	Progress       float64                `json:"progress"`
	Error          string                 `json:"error,omitempty"`
	Message        string                 `json:"message,omitempty"`
	CreatedAt      time.Time              `json:"created_at"`
	UpdatedAt      time.Time              `json:"updated_at"`
	ExpiresAt      time.Time              `json:"expires_at"`
	ResultID       string                 `json:"result_id,omitempty"`
	LicenseTier    string                 `json:"license_tier"`
	ContentType    string                 `json:"content_type,omitempty"`
	ProcessingType string                 `json:"processing_type,omitempty"`
	Metadata       map[string]interface{} `json:"metadata,omitempty"`
	MaxTokens      int                    `json:"max_tokens,omitempty"`
	TokensProcessed int64                 `json:"tokens_processed,omitempty"`
	TokensLimit     int64                 `json:"tokens_limit,omitempty"`
	TokensExceeded  bool                  `json:"tokens_exceeded,omitempty"`
}

// ContentType represents the type of content being processed
type ContentType string

const (
	ContentTypeUnknown     ContentType = "unknown"
	ContentTypeGeneric     ContentType = "generic"
	ContentTypeText        ContentType = "text"
	ContentTypeZip         ContentType = "zip"
	ContentTypeWhatsApp    ContentType = "whatsapp"
	ContentTypePDF         ContentType = "pdf"
	ContentTypeNewsArticle ContentType = "news"
)

// ProcessingResult represents the result of processing a job
type ProcessingResult struct {
	ID          string                 `json:"id"`
	ContentType string                 `json:"content_type"`
	Content     interface{}            `json:"content"`
	ContentUrl  string                 `json:"contentUrl,omitempty"`
	HasFullContent bool                 `json:"hasFullContent,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	ExpiresAt   time.Time              `json:"expires_at,omitempty"`
}

// UploadResponse represents the response to an upload request
type UploadResponse struct {
	ID                  string `json:"id"`
	Status              string `json:"status"`
	EstimatedTimeSeconds int    `json:"estimated_time_seconds"`
	MaxTokens           int64  `json:"max_tokens,omitempty"`
	FileSize            int64  `json:"file_size,omitempty"`
	LicenseTier         string `json:"license_tier,omitempty"`
}

// StatusResponse represents the response to a status request
type StatusResponse struct {
	ID              string  `json:"id"`
	Status          string  `json:"status"`
	Progress        float64 `json:"progress"`
	Error           string  `json:"error,omitempty"`
	Message         string  `json:"message,omitempty"`
	ResultID        string  `json:"result_id,omitempty"`
	TokensProcessed int64   `json:"tokens_processed,omitempty"`
	TokensLimit     int64   `json:"tokens_limit,omitempty"`
	TokensExceeded  bool    `json:"tokens_exceeded,omitempty"`
}

// ContentProcessor defines the interface for processing content
type ContentProcessor interface {
	// ProcessJob processes a job in a single operation
	ProcessJob(ctx context.Context, job *ProcessingJob) (ProcessingResult, error)
	
	// ProcessLargeJob processes a large file in chunks with token tracking
	ProcessLargeJob(ctx context.Context, job *ProcessingJob, chunkSize int) (ProcessingResult, error)
	
	// GetResults retrieves the results of a processed job
	GetResults(ctx context.Context, job *ProcessingJob) (ProcessingResult, error)
}
