package processing

import (
	"fmt"
	"regexp"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
)

// Sample WhatsApp chat content based on user provided lines
const sampleWhatsAppContent = `7/23/24, 7:29 PM - Messages and calls are end-to-end encrypted. Only people in this chat can read, listen to, or share them. Learn more.
1/20/25, 4:13 PM - <PERSON><PERSON><PERSON>: https://www.deccanchronicle.com/southern-states/telangana/hyderabad-vijayawada-nh-65-expansion-works-to-commence-in-3-months-1855407

2 hrs may be stretch..still.

This will make flights between hyd and vij less useful..
1/20/25, 4:23 PM - <PERSON><PERSON><PERSON> JNTU: https://x.com/vijeshetty/status/1881152621177942240?t=ZVv0ZiKKetqZAibXhZDNlQ&s=08
1/20/25, 4:25 PM - <PERSON><PERSON><PERSON> JNTU: This is the kind of Happy news i want to  see in the morning!! The drivers made to listen to the illegal/banned air horns. I almost got heart attacks multiple times because of these horns <This message was edited>
1/20/25, 4:28 PM - <PERSON><PERSON><PERSON>: 2 hrs..  ticket to heaven!
1/20/25, 5:13 PM - +****************: Go <PERSON><PERSON>s
1/20/25, 5:17 PM - +****************: It is so annoying to watch <PERSON> Dame quarterback take off with the ball pretty much every other play.  I know Defense is weak in College Football, but there’s no grace in the way <PERSON> Dame is scoring.  Ohio State on the contrary is a joy to watch.  <PERSON> they win.
1/20/25, 5:25 PM - <PERSON> Kondaka: Definitely ugly number from Biden. Looks like <PERSON> is going to compete with him on this. Pardoned 1500+ first day
1/20/25, 6:07 PM - +91 96187 22225: If these pardons are granted to crime convicts facing death sentences, or to people who served 10+ years jail sentences already, I would wholeheartedly welcome them.
1/20/25, 6:20 PM - Srinivas Chittaluru: This maybe the real reason 😜

https://youtube.com/shorts/KTA_JTbPh2U?si=Qsoyj54dM7h8SBN0
`

const sampleWhatsAppContentWithHeader = `1/1/25, 10:00 AM - WhatsApp Chat with Test Group
1/1/25, 10:01 AM - Alice: Hello!
1/1/25, 10:02 AM - Bob: Hi there.
`

// Pre-compile the regex used in the test to avoid SA6000 warning
// This regex matches lines that look like participant messages
var testMessageRegex = regexp.MustCompile(`^(.+?) - ([^:]+): `)

func TestWhatsAppProcessor_ProcessContent(t *testing.T) {
	processor := NewWhatsAppProcessor()

	// Enable debug logs for testing if needed
	// os.Setenv("PROMZ_DEBUG", "true")
	// defer os.Unsetenv("PROMZ_DEBUG")

	t.Run("Sample Content Processing", func(t *testing.T) {
		result := processor.ProcessContent(sampleWhatsAppContent, "WhatsApp Chat with JNTU Social.txt")

		assert.Equal(t, sampleWhatsAppContent, result.Content)
		assert.Equal(t, "WhatsApp Chat with JNTU Social.txt", result.FileName)

		// Check Metadata
		assert.NotNil(t, result.Metadata)
		assert.Equal(t, true, result.Metadata["isWhatsAppChat"])
		assert.Equal(t, "conversation", result.Metadata["sourceType"])

		participants, ok := result.Metadata["participants"].([]string)
		assert.True(t, ok)
		assert.ElementsMatch(t, []string{
			"Sudhir Kolli",
			"Ratnakumar JNTU",
			"Srinivas Chittaluru",
			"+****************",
			"+****************",
			"Krishna Kondaka",
			"+91 96187 22225",
		}, participants)
		assert.Equal(t, len(participants), result.Metadata["participantCount"])
		assert.True(t, result.Metadata["isGroupChat"].(bool)) // More than 1 participant

		// Message count should match the number of lines starting with a valid timestamp
		// AND looking like a participant message (containing ':')
		expectedMessageCount := 0
		lines := strings.Split(sampleWhatsAppContent, "\n")
		for _, line := range lines {
			match := testMessageRegex.FindStringSubmatch(line)
			if len(match) >= 3 { // Check if it matches the participant line structure
				potentialDateTime := match[1]
				if _, ok := parseWhatsAppDateTime(potentialDateTime); ok { // Check if the first part is a valid date
					expectedMessageCount++
				}
			}
		}
		assert.Equal(t, expectedMessageCount, result.Metadata["messageCount"])

		// Group name should be empty as it's not in the header
		assert.Equal(t, "", getStringValue(result.Metadata, "groupName"))

		// Check Variables
		assert.NotNil(t, result.Variables)
		assert.Equal(t, sampleWhatsAppContent, result.Variables["CONVERSATION:CONTENTS"])
		assert.Equal(t, fmt.Sprintf("%d", expectedMessageCount), result.Variables["CONVERSATION:MESSAGE_COUNT"])
		assert.Equal(t, fmt.Sprintf("%d", len(participants)), result.Variables["CONVERSATION:PARTICIPANT_COUNT"])
		assert.Contains(t, result.Variables["CONVERSATION:PARTICIPANTS"], "Sudhir Kolli")
		assert.Contains(t, result.Variables["CONVERSATION:PARTICIPANTS"], "Ratnakumar JNTU")
		assert.NotContains(t, result.Variables, "CONVERSATION:GROUP_NAME") // No group name found
	})

	t.Run("Sample Content With Header Processing", func(t *testing.T) {
		result := processor.ProcessContent(sampleWhatsAppContentWithHeader, "WhatsApp Chat with Test Group.txt")

		assert.Equal(t, "Test Group", getStringValue(result.Metadata, "groupName"))
		assert.Equal(t, "Test Group", result.Variables["CONVERSATION:GROUP_NAME"])
		assert.ElementsMatch(t, []string{"Alice", "Bob"}, result.Metadata["participants"].([]string))
		assert.Equal(t, 2, result.Metadata["messageCount"])
		assert.True(t, result.Metadata["isGroupChat"].(bool))
	})

	t.Run("Empty Content Processing", func(t *testing.T) {
		result := processor.ProcessContent("", "Empty WhatsApp Chat.txt")
		assert.Equal(t, "", result.Content)
		assert.Equal(t, 0, result.Metadata["participantCount"])
		assert.Equal(t, 0, result.Metadata["messageCount"])
		assert.False(t, result.Metadata["isGroupChat"].(bool))
		assert.Empty(t, result.Variables)
	})
}
