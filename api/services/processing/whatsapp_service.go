package processing

import (
	"context"
	"fmt"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	pb "promz.ai/api/proto/gen"
)

// WhatsAppService implements the WhatsAppProcessingService gRPC interface
type WhatsAppService struct {
	pb.UnimplementedWhatsAppProcessingServiceServer
	processor *WhatsAppProcessor // Inject the processor
}

// NewWhatsAppService creates a new WhatsApp service
func NewWhatsAppService(processor *WhatsAppProcessor) *WhatsAppService {
	return &WhatsAppService{
		processor: processor, // Store the injected processor
	}
}

// DetectWhatsAppContent detects if the provided content is a WhatsApp chat
func (s *WhatsAppService) DetectWhatsAppContent(ctx context.Context, req *pb.DetectWhatsAppRequest) (*pb.DetectWhatsAppResponse, error) {
	// Extract content from request
	var content string
	var fileName string = req.FileName

	if contentSource := req.GetContentSource(); contentSource != nil {
		switch v := contentSource.(type) {
		case *pb.DetectWhatsAppRequest_Content:
			content = v.Content
		case *pb.DetectWhatsAppRequest_ContentId:
			contentID := v.ContentId
			var err error
			content, err = retrieveContentByID(contentID) // Keep using the helper for now
			if err != nil {
				return nil, status.Errorf(codes.NotFound, "content with ID %s not found: %v", contentID, err)
			}
		default:
			return nil, status.Error(codes.InvalidArgument, "content source not specified")
		}
	} else {
		return nil, status.Error(codes.InvalidArgument, "content source not specified")
	}

	// Use the processor to detect
	isWhatsApp, confidence := s.processor.IsWhatsAppChat(content, fileName)

	// If it's not a WhatsApp chat, return early
	if !isWhatsApp {
		return &pb.DetectWhatsAppResponse{
			IsWhatsappChat:  false,
			ConfidenceScore: confidence,
		}, nil
	}

	// If it is WhatsApp, process it to get metadata
	processedResult := s.processor.ProcessContent(content, fileName)

	// Convert the processor's metadata map to the proto format
	metadataProto := &pb.WhatsAppMetadata{
		GroupName:        getStringValue(processedResult.Metadata, "groupName"),
		ParticipantCount: getInt32Value(processedResult.Metadata, "participantCount"),
		MessageCount:     getInt32Value(processedResult.Metadata, "messageCount"),
		IsGroupChat:      getBoolValue(processedResult.Metadata, "isGroupChat"),
	}

	// Add participants if available
	if participants, ok := processedResult.Metadata["participants"].([]string); ok {
		metadataProto.Participants = participants
	}

	// Add timestamps if available (using the helper functions for safe access)
	metadataProto.FirstMessageTimestamp = getInt64Value(processedResult.Metadata, "firstMessageTimestamp")
	metadataProto.LastMessageTimestamp = getInt64Value(processedResult.Metadata, "lastMessageTimestamp")

	return &pb.DetectWhatsAppResponse{
		IsWhatsappChat:  true,
		ConfidenceScore: confidence,
		Metadata:        metadataProto,
	}, nil
}

// ProcessWhatsAppContent processes WhatsApp chat content
func (s *WhatsAppService) ProcessWhatsAppContent(ctx context.Context, req *pb.ProcessWhatsAppRequest) (*pb.ProcessWhatsAppResponse, error) {
	// Extract content from request
	var content string

	if contentSource := req.GetContentSource(); contentSource != nil {
		switch v := contentSource.(type) {
		case *pb.ProcessWhatsAppRequest_Content:
			content = v.Content
		case *pb.ProcessWhatsAppRequest_ContentId:
			contentID := v.ContentId
			var err error
			content, err = retrieveContentByID(contentID) // Keep using the helper for now
			if err != nil {
				return nil, status.Errorf(codes.NotFound, "content with ID %s not found: %v", contentID, err)
			}
		default:
			return nil, status.Error(codes.InvalidArgument, "content source not specified")
		}
	} else {
		return nil, status.Error(codes.InvalidArgument, "content source not specified")
	}

	// Use the processor to process the content
	// Assuming filename isn't strictly needed for processing if content is provided, passing empty string.
	// If filename context is important for ProcessContent, it should be added to the request proto.
	processedResult := s.processor.ProcessContent(content, "") // Pass empty filename for now

	// Convert the processor's metadata map to the proto format
	metadataProto := &pb.WhatsAppMetadata{
		GroupName:        getStringValue(processedResult.Metadata, "groupName"),
		ParticipantCount: getInt32Value(processedResult.Metadata, "participantCount"),
		MessageCount:     getInt32Value(processedResult.Metadata, "messageCount"),
		IsGroupChat:      getBoolValue(processedResult.Metadata, "isGroupChat"),
	}

	// Add participants if available
	if participants, ok := processedResult.Metadata["participants"].([]string); ok {
		metadataProto.Participants = participants
	}

	// Add timestamps if available (using the helper functions for safe access)
	metadataProto.FirstMessageTimestamp = getInt64Value(processedResult.Metadata, "firstMessageTimestamp")
	metadataProto.LastMessageTimestamp = getInt64Value(processedResult.Metadata, "lastMessageTimestamp")

	// Return the processed content with metadata and variables from the processor result
	return &pb.ProcessWhatsAppResponse{
		ProcessedContent: processedResult.Content, // Use content from the result
		Metadata:         metadataProto,
		Variables:        processedResult.Variables, // Use variables from the result
	}, nil
}

// Helper function to retrieve content by ID (remains unchanged for now)
func retrieveContentByID(_ string) (string, error) {
	// In a real implementation, this would retrieve content from a database or cache
	// For now, we'll return an error to indicate this needs to be implemented
	return "", fmt.Errorf("retrieveContentByID not implemented")
}

// Added helper function to safely get int64 from map (needed for timestamps)
func getInt64Value(m map[string]interface{}, key string) int64 {
	switch v := m[key].(type) {
	case int:
		return int64(v)
	case int32:
		return int64(v)
	case int64:
		return v
	case float32:
		return int64(v)
	case float64:
		return int64(v)
	}
	return 0
}
