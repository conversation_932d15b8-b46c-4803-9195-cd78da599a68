package processing

import (
	"context"

	"google.golang.org/grpc"
)

// This file contains stub interfaces for the gRPC service
// These will be replaced by the generated code from protoc

// FileProcessingServiceServer is the server API for FileProcessingService service.
type FileProcessingServiceServer interface {
	// UploadFile handles streaming file uploads
	UploadFile(FileProcessingService_UploadFileServer) error
	// GetStatus returns the current status of a processing job
	GetStatus(context.Context, *StatusRequest) (*StatusResponse, error)
	// GetResults streams the results of a processing job
	GetResults(*ResultsRequest, FileProcessingService_GetResultsServer) error
	// CancelProcessing cancels a processing job
	CancelProcessing(context.Context, *CancelRequest) (*CancelResponse, error)
}

// FileProcessingService_UploadFileServer is the server API for FileProcessingService service.
type FileProcessingService_UploadFileServer interface {
	SendAndClose(*FileUploadResponse) error
	Recv() (*FileUploadRequest, error)
	grpc.ServerStream
}

// FileProcessingService_GetResultsServer is the server API for FileProcessingService service.
type FileProcessingService_GetResultsServer interface {
	Send(*ResultsResponse) error
	grpc.ServerStream
}

// FileUploadRequest represents a file upload request
type FileUploadRequest struct {
	// Types that are assignable to Request:
	//	*FileUploadRequest_Metadata
	//	*FileUploadRequest_Chunk
	Request isFileUploadRequest_Request
}

// isFileUploadRequest_Request is implemented by FileUploadRequest_Metadata and FileUploadRequest_Chunk
type isFileUploadRequest_Request interface {
	isFileUploadRequest_Request()
}

// FileUploadRequest_Metadata represents the metadata for a file upload
type FileUploadRequest_Metadata struct {
	Metadata *FileUploadMetadata
}

func (*FileUploadRequest_Metadata) isFileUploadRequest_Request() {}

// FileUploadRequest_Chunk represents a chunk of a file upload
type FileUploadRequest_Chunk struct {
	Chunk *FileChunk
}

func (*FileUploadRequest_Chunk) isFileUploadRequest_Request() {}

// FileUploadMetadata represents the metadata for a file upload
type FileUploadMetadata struct {
	FileName       string            `json:"file_name"`
	MimeType       string            `json:"mime_type"`
	FileSize       int64             `json:"file_size"`
	LicenseTier    string            `json:"license_tier"`
	CustomMetadata map[string]string `json:"custom_metadata"`
}

// FileChunk represents a chunk of a file upload
type FileChunk struct {
	Data       []byte `json:"data"`
	ChunkIndex int32  `json:"chunk_index"`
}

// FileUploadResponse represents the response to a file upload
type FileUploadResponse struct {
	Id                   string `json:"id"`
	Status               string `json:"status"`
	EstimatedTimeSeconds int32  `json:"estimated_time_seconds"`
	MaxTokens            int32  `json:"max_tokens"`
	FileSize             int64  `json:"file_size"`
	LicenseTier          string `json:"license_tier"`
}

// StatusRequest represents a request for the status of a processing job
type StatusRequest struct {
	Id string `json:"id"`
}

// ResultsRequest represents a request for the results of a processing job
type ResultsRequest struct {
	Id string `json:"id"`
}

// ResultsResponse represents the response to a results request
type ResultsResponse struct {
	// Types that are assignable to Response:
	//	*ResultsResponse_Metadata_
	//	*ResultsResponse_Content
	Response isResultsResponse_Response
}

// isResultsResponse_Response is implemented by ResultsResponse_Metadata_ and ResultsResponse_Content
type isResultsResponse_Response interface {
	isResultsResponse_Response()
}

// ResultsResponse_Metadata_ represents the metadata for a results response
type ResultsResponse_Metadata_ struct {
	Metadata *ResultsResponse_Metadata
}

func (*ResultsResponse_Metadata_) isResultsResponse_Response() {}

// ResultsResponse_Content represents the content for a results response
type ResultsResponse_Content struct {
	Content *ResultsResponse_ContentChunk
}

func (*ResultsResponse_Content) isResultsResponse_Response() {}

// ResultsResponse_Metadata represents the metadata for a results response
type ResultsResponse_Metadata struct {
	Id              string            `json:"id"`
	ContentType     string            `json:"content_type"`
	Metadata        map[string]string `json:"metadata"`
	HasFullContent  bool              `json:"has_full_content"`
	ContentUrl      string            `json:"content_url"`
	ExpiresAt       int64             `json:"expires_at"`
	TokensProcessed int32             `json:"tokens_processed"`
	TokensLimit     int32             `json:"tokens_limit"`
	TokensExceeded  bool              `json:"tokens_exceeded"`
}

// ResultsResponse_ContentChunk represents a chunk of content for a results response
type ResultsResponse_ContentChunk struct {
	Data       []byte `json:"data"`
	ChunkIndex int32  `json:"chunk_index"`
	IsLast     bool   `json:"is_last"`
}

// CancelRequest represents a request to cancel a processing job
type CancelRequest struct {
	Id string `json:"id"`
}

// CancelResponse represents the response to a cancel request
type CancelResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

// FileUploadUpdate represents a file processing status update for WebSocket
type FileUploadUpdate struct {
	Id              string  `json:"id"`
	Status          string  `json:"status"`
	Progress        float64 `json:"progress"`
	Error           string  `json:"error,omitempty"`
	Message         string  `json:"message,omitempty"`
	TokensProcessed int32   `json:"tokens_processed,omitempty"`
	TokensLimit     int32   `json:"tokens_limit,omitempty"`
	TokensExceeded  bool    `json:"tokens_exceeded,omitempty"`
	Timestamp       int64   `json:"timestamp,omitempty"`
}

// RegisterFileProcessingServiceServer registers the server implementation with the gRPC server
func RegisterFileProcessingServiceServer(s *grpc.Server, srv FileProcessingServiceServer) {
	// This is a stub that will be replaced by the generated code
}
