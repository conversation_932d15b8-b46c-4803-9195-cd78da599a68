package processing

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"time"

	"cloud.google.com/go/storage" // GCS client
	"google.golang.org/protobuf/encoding/protojson" // For marshaling proto messages
	"google.golang.org/protobuf/proto"                 // For proto message interface check
	pb "promz.ai/api/proto/gen"
)

// StorageService handles interactions with Google Cloud Storage
type StorageService struct {
	client     *storage.Client
	bucketName string
}

// NewStorageService creates a new storage service
func NewStorageService(ctx context.Context, bucketName string) (*StorageService, error) {
	// Create a temporary directory for local storage in development mode
	tempDir := filepath.Join(os.TempDir(), "promz-storage")
	if err := os.MkdirAll(tempDir, 0o755); err != nil {
		return nil, fmt.Errorf("failed to create local storage directory: %v", err)
	}

	// Try to create a GCS client, but don't fail if it doesn't work
	client, err := storage.NewClient(ctx)
	if err != nil {
		// Log the error but continue with a nil client for local development
		fmt.Printf("WARNING: Failed to create GCS client, using local storage instead: %v\n", err)
		return &StorageService{
			client:     nil, // Nil client indicates we're using local storage
			bucketName: bucketName,
		}, nil
	}

	return &StorageService{
		client:     client,
		bucketName: bucketName,
	}, nil
}

// UploadFile uploads a file to GCS or local storage
func (s *StorageService) UploadFile(ctx context.Context, filePath string, objectName string, expirationDays int) error {
	f, err := os.Open(filePath)
	if err != nil {
		return fmt.Errorf("failed to open file: %v", err)
	}
	defer f.Close()

	// If client is nil, use local storage
	if s.client == nil {
		// Create the local storage directory structure
		localDir := filepath.Join(os.TempDir(), "promz-storage", s.bucketName, filepath.Dir(objectName))
		if err := os.MkdirAll(localDir, 0o755); err != nil {
			return fmt.Errorf("failed to create local directory: %v", err)
		}

		// Copy the file to local storage
		destPath := filepath.Join(os.TempDir(), "promz-storage", s.bucketName, objectName)
		srcFile, err := os.Open(filePath)
		if err != nil {
			return fmt.Errorf("failed to open source file: %v", err)
		}
		defer srcFile.Close()

		destFile, err := os.Create(destPath)
		if err != nil {
			return fmt.Errorf("failed to create destination file: %v", err)
		}
		defer destFile.Close()

		_, err = io.Copy(destFile, srcFile)
		if err != nil {
			return fmt.Errorf("failed to copy file: %v", err)
		}

		// Create metadata file with expiration
		metadataPath := destPath + ".metadata"
		metadata := map[string]string{
			"expiration": time.Now().Add(time.Duration(expirationDays) * 24 * time.Hour).Format(time.RFC3339),
		}
		metadataBytes, err := json.Marshal(metadata)
		if err != nil {
			return fmt.Errorf("failed to marshal metadata: %v", err)
		}

		if err := os.WriteFile(metadataPath, metadataBytes, 0o644); err != nil {
			return fmt.Errorf("failed to write metadata: %v", err)
		}

		return nil
	}

	// Otherwise use GCS
	bucket := s.client.Bucket(s.bucketName)
	obj := bucket.Object(objectName)

	// Set expiration time based on license tier
	expirationTime := time.Now().Add(time.Duration(expirationDays) * 24 * time.Hour)
	attrs := storage.ObjectAttrsToUpdate{
		Metadata: map[string]string{
			"expiration": expirationTime.Format(time.RFC3339),
		},
	}

	// Update object attributes
	_, err = obj.Update(ctx, attrs)
	if err != nil {
		return fmt.Errorf("failed to set object attributes: %v", err)
	}

	// Upload the file
	w := obj.NewWriter(ctx)
	if _, err := io.Copy(w, f); err != nil {
		return fmt.Errorf("failed to copy file to storage: %v", err)
	}
	if err := w.Close(); err != nil {
		return fmt.Errorf("failed to finalize upload: %v", err)
	}

	return nil
}

// DownloadFile downloads a file from GCS or local storage
func (s *StorageService) DownloadFile(ctx context.Context, objectName string, destPath string) error {
	// If client is nil, use local storage
	if s.client == nil {
		return s.downloadFileLocal(objectName, destPath)
	}

	// Otherwise use GCS
	bucket := s.client.Bucket(s.bucketName)
	obj := bucket.Object(objectName)

	r, err := obj.NewReader(ctx)
	if err != nil {
		return fmt.Errorf("failed to open object: %v", err)
	}
	defer r.Close()

	f, err := os.Create(destPath)
	if err != nil {
		return fmt.Errorf("failed to create destination file: %v", err)
	}
	defer f.Close()

	if _, err := io.Copy(f, r); err != nil {
		return fmt.Errorf("failed to copy object to file: %v", err)
	}

	return nil
}

// downloadFileLocal copies a file from local storage for development
func (s *StorageService) downloadFileLocal(objectName string, destPath string) error {
	// Get the file from local storage
	sourcePath := filepath.Join(os.TempDir(), "promz-storage", s.bucketName, objectName)
	srcFile, err := os.Open(sourcePath)
	if err != nil {
		return fmt.Errorf("failed to open source file: %v", err)
	}
	defer srcFile.Close()

	destFile, err := os.Create(destPath)
	if err != nil {
		return fmt.Errorf("failed to create destination file: %v", err)
	}
	defer destFile.Close()

	_, err = io.Copy(destFile, srcFile)
	if err != nil {
		return fmt.Errorf("failed to copy file: %v", err)
	}

	return nil
}

// GetObject gets an object from GCS or local storage as a reader
func (s *StorageService) GetObject(ctx context.Context, objectName string) (io.ReadCloser, error) {
	// If client is nil, use local storage
	if s.client == nil {
		return s.getObjectLocal(objectName)
	}

	// Otherwise use GCS
	bucket := s.client.Bucket(s.bucketName)
	obj := bucket.Object(objectName)

	r, err := obj.NewReader(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to open object: %v", err)
	}
	return r, nil
}

// getObjectLocal gets an object from local storage as a reader
func (s *StorageService) getObjectLocal(objectName string) (io.ReadCloser, error) {
	// Get the file from local storage
	filePath := filepath.Join(os.TempDir(), "promz-storage", s.bucketName, objectName)
	file, err := os.Open(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open file: %v", err)
	}
	return file, nil
}

// SaveResult saves processing results to GCS or local storage
// Saves the *protobuf* ProcessingResult message, marshaled to JSON using protojson.
func (s *StorageService) SaveResult(ctx context.Context, id string, result *pb.ProcessingResult) error {
	// Results are stored under a 'results/' prefix
	objectName := filepath.Join("results", id)
	expiration := 30 * 24 * time.Hour // Default expiration: 30 days

	// If client is nil, use local storage
	if s.client == nil {
		return s.saveResultLocal(id, result)
	}

	// Otherwise use GCS
	bucket := s.client.Bucket(s.bucketName)
	obj := bucket.Object(objectName)

	// Convert proto result to JSON using protojson
	resultBytes, err := protojson.MarshalOptions{EmitUnpopulated: true}.Marshal(result)
	if err != nil {
		return fmt.Errorf("failed to marshal proto result: %v", err)
	}

	// Set expiration time
	expirationTime := time.Now().Add(expiration)
	attrs := storage.ObjectAttrsToUpdate{
		Metadata: map[string]string{
			"expiration": expirationTime.Format(time.RFC3339),
		},
	}

	// Upload the content
	w := obj.NewWriter(ctx)
	defer w.Close()

	if _, err := w.Write(resultBytes); err != nil {
		return fmt.Errorf("failed to write content to storage: %v", err)
	}

	// Update object attributes
	_, err = obj.Update(ctx, attrs)
	if err != nil {
		return fmt.Errorf("failed to set object attributes: %v", err)
	}

	return nil
}

// StoreResult saves a result and returns the result ID
// NOTE: This function still uses the local ProcessingResult struct. Needs refactoring
// if intended to be used with proto results. Currently appears unused by ZipProcessor.
func (s *StorageService) StoreResult(ctx context.Context, result *pb.ProcessingResult, defaultExpiresAt time.Time) (string, error) {
	// Use the existing ID or generate a new one
	id := result.JobId // Use CamelCase field name from generated struct
	if id == "" {
		id = fmt.Sprintf("result_%d", time.Now().UnixNano())
		// Note: Modifying the input result might be unexpected. Consider if ID should be set elsewhere.
		// For now, we assume the caller handles the ID persistence if needed.
	}

	// Save the result
	if err := s.SaveResult(ctx, id, result); err != nil {
		return "", err
	}

	return id, nil
}

// saveResultLocal saves protobuf processing results to local storage using protojson
func (s *StorageService) saveResultLocal(id string, result *pb.ProcessingResult) error {
	// Create the directory structure
	localDir := filepath.Join(os.TempDir(), "promz-storage", s.bucketName, "results")
	if err := os.MkdirAll(localDir, 0o755); err != nil {
		return fmt.Errorf("failed to create local directory: %v", err)
	}

	// Convert proto result to JSON using protojson
	resultBytes, err := protojson.MarshalOptions{EmitUnpopulated: true}.Marshal(result)
	if err != nil {
		return fmt.Errorf("failed to marshal proto result: %v", err)
	}

	// Write result to file
	filePath := filepath.Join(localDir, id)
	if err := os.WriteFile(filePath, resultBytes, 0o644); err != nil {
		return fmt.Errorf("failed to write result: %v", err)
	}

	return nil
}

// GetResult retrieves processing results from GCS or local storage
// Returns the *protobuf* ProcessingResult message, unmarshaled from JSON using protojson.
func (s *StorageService) GetResult(ctx context.Context, id string) (*pb.ProcessingResult, error) {
	// Results are stored under a 'results/' prefix
	objectName := filepath.Join("results", id)

	// If client is nil, use local storage
	if s.client == nil {
		return s.getResultLocal(id)
	}

	// Otherwise use GCS
	bucket := s.client.Bucket(s.bucketName)
	obj := bucket.Object(objectName)

	r, err := obj.NewReader(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to open result object: %v", err)
	}
	defer r.Close()

	// Read all bytes from the reader
	resultBytes, err := io.ReadAll(r)
	if err != nil {
		return nil, fmt.Errorf("failed to read result data: %v", err)
	}

	var result pb.ProcessingResult
	if err := protojson.Unmarshal(resultBytes, &result); err != nil {
		return nil, fmt.Errorf("failed to unmarshal proto result: %v", err)
	}

	return &result, nil
}

// getResultLocal retrieves protobuf processing results from local storage using protojson
func (s *StorageService) getResultLocal(id string) (*pb.ProcessingResult, error) {
	// Get the result file
	filePath := filepath.Join(os.TempDir(), "promz-storage", s.bucketName, "results", id)
	fileBytes, err := os.ReadFile(filePath)
	if err != nil {
		if os.IsNotExist(err) {
			return nil, fmt.Errorf("result not found: %w", err)
		}
		return nil, fmt.Errorf("failed to read result file: %v", err)
	}

	// Decode the result
	var result pb.ProcessingResult
	if err := protojson.Unmarshal(fileBytes, &result); err != nil {
		return nil, fmt.Errorf("failed to unmarshal proto result: %v", err)
	}

	return &result, nil
}

// StoreObject stores arbitrary content in GCS or local storage with an expiration time
// Accepts an interface{}. If it's a proto message, use protojson, otherwise use encoding/json.
func (s *StorageService) StoreObject(ctx context.Context, objectName string, content interface{}, expiration time.Duration) error {
	// If client is nil, use local storage
	if s.client == nil {
		// Create the directory structure
		localDir := filepath.Join(os.TempDir(), "promz-storage", s.bucketName, filepath.Dir(objectName))
		if err := os.MkdirAll(localDir, 0o755); err != nil {
			return fmt.Errorf("failed to create local directory: %v", err)
		}

		// Convert content to JSON - Use protojson if it's a proto message
		var contentBytes []byte
		var err error
		if protoMsg, ok := content.(proto.Message); ok {
			contentBytes, err = protojson.MarshalOptions{EmitUnpopulated: true}.Marshal(protoMsg)
		} else {
			contentBytes, err = json.Marshal(content)
		}
		if err != nil {
			return fmt.Errorf("failed to marshal content: %v", err)
		}

		// Write content to file
		destPath := filepath.Join(os.TempDir(), "promz-storage", s.bucketName, objectName)
		if err := os.WriteFile(destPath, contentBytes, 0o644); err != nil {
			return fmt.Errorf("failed to write content: %v", err)
		}

		// Create metadata file with expiration
		metadataPath := destPath + ".metadata"
		metadata := map[string]string{
			"expiration": time.Now().Add(expiration).Format(time.RFC3339),
		}
		metadataBytes, err := json.Marshal(metadata)
		if err != nil {
			return fmt.Errorf("failed to marshal metadata: %v", err)
		}

		if err := os.WriteFile(metadataPath, metadataBytes, 0o644); err != nil {
			return fmt.Errorf("failed to write metadata: %v", err)
		}

		return nil
	}

	// Otherwise use GCS
	bucket := s.client.Bucket(s.bucketName)
	obj := bucket.Object(objectName)

	// Convert content to JSON - Use protojson if it's a proto message
	var contentBytes []byte
	var err error
	if protoMsg, ok := content.(proto.Message); ok {
		contentBytes, err = protojson.MarshalOptions{EmitUnpopulated: true}.Marshal(protoMsg)
	} else {
		contentBytes, err = json.Marshal(content)
	}
	if err != nil {
		return fmt.Errorf("failed to marshal content: %v", err)
	}

	// Set expiration time
	expirationTime := time.Now().Add(expiration)
	attrs := storage.ObjectAttrsToUpdate{
		Metadata: map[string]string{
			"expiration": expirationTime.Format(time.RFC3339),
		},
	}

	// Upload the content
	w := obj.NewWriter(ctx)
	defer w.Close()

	if _, err := w.Write(contentBytes); err != nil {
		return fmt.Errorf("failed to write content to storage: %v", err)
	}

	// Update object attributes
	_, err = obj.Update(ctx, attrs)
	if err != nil {
		return fmt.Errorf("failed to set object attributes: %v", err)
	}

	return nil
}

// DeleteObject deletes an object from GCS or local storage
func (s *StorageService) DeleteObject(ctx context.Context, objectName string) error {
	// If client is nil, use local storage
	if s.client == nil {
		return s.deleteObjectLocal(objectName)
	}

	// Otherwise use GCS
	bucket := s.client.Bucket(s.bucketName)
	obj := bucket.Object(objectName)

	if err := obj.Delete(ctx); err != nil {
		return fmt.Errorf("failed to delete object: %v", err)
	}

	return nil
}

// deleteObjectLocal deletes an object from local storage
func (s *StorageService) deleteObjectLocal(objectName string) error {
	// Delete the file
	filePath := filepath.Join(os.TempDir(), "promz-storage", s.bucketName, objectName)
	if err := os.Remove(filePath); err != nil && !os.IsNotExist(err) {
		return fmt.Errorf("failed to delete file: %v", err)
	}

	// Delete the metadata file if it exists
	metadataPath := filePath + ".metadata"
	if err := os.Remove(metadataPath); err != nil && !os.IsNotExist(err) {
		return fmt.Errorf("failed to delete metadata: %v", err)
	}

	return nil
}

// Close closes the storage client
func (s *StorageService) Close() error {
	// If client is nil, nothing to close
	if s.client == nil {
		return nil
	}

	return s.client.Close()
}
