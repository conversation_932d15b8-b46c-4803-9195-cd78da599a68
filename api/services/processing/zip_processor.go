package processing

import (
	"archive/zip"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"strings"
	"time"

	pb "promz.ai/api/proto/gen"
)

// Define max size for text file content reading (e.g., 1MB)
const maxTextFileSize = 1 * 1024 * 1024

// Ensure ZipProcessor implements FileProcessor
var _ FileProcessor = (*ZipProcessor)(nil)

// ZipProcessor processes ZIP files
type ZipProcessor struct {
	storageService    *StorageService
	whatsAppProcessor *WhatsAppProcessor
}

// NewZipProcessor creates a new ZIP processor
func NewZipProcessor(storageService *StorageService, whatsAppService *WhatsAppService) *ZipProcessor {
	return &ZipProcessor{
		storageService:    storageService,
		whatsAppProcessor: whatsAppService.processor,
	}
}

// UploadFile processes a ZIP file job
// Downloads, extracts, analyzes (for WhatsApp), and saves results.
// The metadata parameter is currently unused due to protobuf refactoring but required by the interface.
func (p *ZipProcessor) UploadFile(ctx context.Context, job *ProcessingJob, _ map[string]interface{}) (string, error) {
	// Download the file from storage
	tempFilePath := filepath.Join(os.TempDir(), job.ID+filepath.Ext(job.FileName))
	defer os.Remove(tempFilePath)

	var errMsg string // Store potential error messages
	if err := p.storageService.DownloadFile(ctx, job.FilePath, tempFilePath); err != nil {
		// Update job status to failed before returning
		job.Status = StatusFailed // Use local status constant
		// job.ErrorMessage = fmt.Sprintf("failed to download file: %v", err) // Remove assignment to non-existent field
		errMsg = fmt.Sprintf("failed to download file: %v", err)
		// if updateErr := p.storageService.UpdateJobStatus(ctx, job.ID, job.Status, job.ErrorMessage); updateErr != nil { // Comment out undefined method call
		// 	 log.Printf("Error updating job status to failed after download error: %v", updateErr)
		// }
		log.Printf("Error updating job status to failed after download error (UpdateJobStatus call commented out): %s", errMsg)
		return "", errors.New(errMsg)
	}

	// Update job status to processing
	job.Status = StatusProcessing // Use local status constant
	// if err := p.storageService.UpdateJobStatus(ctx, job.ID, job.Status, ""); err != nil { // Comment out undefined method call
	// 	 log.Printf("Error updating job status to processing: %v", err)
	// 	 // Continue processing, but log the error
	// }
	log.Printf("Job %s status set to processing (UpdateJobStatus call commented out)", job.ID)

	// Process the ZIP file
	extractedFiles, _, err := p.extractZipContents(tempFilePath)
	if err != nil {
		// Update job status to failed
		job.Status = StatusFailed // Use local status constant
		// job.ErrorMessage = fmt.Sprintf("failed to extract ZIP contents: %v", err) // Remove assignment to non-existent field
		errMsg = fmt.Sprintf("failed to extract ZIP contents: %v", err)
		// if updateErr := p.storageService.UpdateJobStatus(ctx, job.ID, job.Status, job.ErrorMessage); updateErr != nil { // Comment out undefined method call
		// 	 log.Printf("Error updating job status to failed after extraction error: %v", updateErr)
		// }
		log.Printf("Error updating job status to failed after extraction error (UpdateJobStatus call commented out): %s", errMsg)
		return "", errors.New(errMsg)
	}

	// Analyze extracted content for specific content types
	// Returns specific metadata proto (WhatsApp or Archive), combined text content, variables map, and error
	contentMetadataProto, combinedContent, variables, err := p.analyzeExtractedContent(extractedFiles)
	if err != nil {
		// Log error but continue, maybe set a warning status?
		log.Printf("Warning: Error analyzing extracted content for job %s: %v", job.ID, err)
		// We might not want to fail the whole job here, depends on requirements
	}

	// Create the protobuf result message
	protoResult := &pb.ProcessingResult{
		JobId:        job.ID,
		Content:      combinedContent,
		ExpiresAt:    job.ExpiresAt.Format(time.RFC3339),
		Status:       pb.UploadStatus_UPLOAD_STATUS_COMPLETED, // Assume success initially
		FileName:     job.FileName,                            // Use explicit field instead of GeneralMetadata
		IsZipContent: true,                                    // Mark as zip content
	}

	// Add zip metadata information
	// Since we can't store variables directly in ZipMetadata (it doesn't have a Variables field),
	// we'll need to store them in the content or use a different approach
	// For now, we'll add basic zip metadata
	zipMetadata := &pb.ZipMetadata{
		FileCount:   int32(len(extractedFiles)),
		ExtractedAt: time.Now().Format(time.RFC3339),
	}

	// If we have variables from analysis, add them to the content
	if len(variables) > 0 {
		// Append variables to the content as JSON
		variablesJSON, err := json.Marshal(variables)
		if err == nil {
			// Append variables to the content
			combinedContent += "\n\n--- EXTRACTED VARIABLES ---\n" + string(variablesJSON)
			// Update the content in the proto result
			protoResult.Content = combinedContent
		}
	}

	// Set the zip_metadata field in the oneof
	protoResult.ContentMetadata = &pb.ProcessingResult_ZipMetadata{
		ZipMetadata: zipMetadata,
	}

	// Populate the content_metadata oneof based on analysis result type
	switch metadataProto := contentMetadataProto.(type) {
	case *pb.WhatsAppMetadata:
		protoResult.ContentType = "conversation"
		protoResult.ContentMetadata = &pb.ProcessingResult_WhatsappMetadata{
			WhatsappMetadata: metadataProto,
		}
	case *pb.ZipMetadata: // Use ZipMetadata instead of ArchiveMetadata
		protoResult.ContentType = "archive"
		protoResult.ContentMetadata = &pb.ProcessingResult_ZipMetadata{ // Use ZipMetadata
			ZipMetadata: metadataProto, // Use ZipMetadata
		}
	default:
		// Default to archive if analysis somehow failed or returned unexpected type
		log.Printf("Warning: Unexpected metadata type from analysis for job %s. Defaulting to ZipMetadata.", job.ID)
		protoResult.ContentType = "archive"
		protoResult.ContentMetadata = &pb.ProcessingResult_ZipMetadata{ // Use ZipMetadata
			ZipMetadata: &pb.ZipMetadata{ // Use ZipMetadata
				FileCount: int32(len(extractedFiles)), // Use extractedFiles count as fallback
			},
		}
	}

	// Add file size and date metadata from original file stat to explicit fields
	fileInfo, statErr := os.Stat(tempFilePath)
	if statErr == nil {
		// Add file size to zip metadata
		if zipMeta, ok := protoResult.ContentMetadata.(*pb.ProcessingResult_ZipMetadata); ok {
			zipMeta.ZipMetadata.TotalSizeBytes = fileInfo.Size()
		}

		// Set timestamp field
		protoResult.Timestamp = fileInfo.ModTime().Unix()
	}

	// Save the result using the proto structure
	if err := p.storageService.SaveResult(ctx, protoResult.JobId, protoResult); err != nil {
		// Update job status to failed
		job.Status = StatusFailed // Use local status constant
		// job.ErrorMessage = fmt.Sprintf("failed to save result: %v", err) // Remove assignment
		errMsg = fmt.Sprintf("failed to save result: %v", err)
		protoResult.Status = pb.UploadStatus_UPLOAD_STATUS_FAILED // Keep proto status updated
		protoResult.ErrorMessage = errMsg                         // Set error message in proto result

		// Attempt to save the failed status back using SaveResult again with updated proto result
		if saveErr := p.storageService.SaveResult(ctx, protoResult.JobId, protoResult); saveErr != nil {
			log.Printf("Error saving failed result for job %s: %v", job.ID, saveErr)
		}

		// Also update the job status in the primary job tracking if separate
		// if updateErr := p.storageService.UpdateJobStatus(ctx, job.ID, job.Status, job.ErrorMessage); updateErr != nil { // Comment out
		// 	 log.Printf("Error updating job status to failed after save error for job %s: %v", job.ID, updateErr)
		// }
		log.Printf("Error updating job status to failed after save error (UpdateJobStatus call commented out): %s", errMsg)
		return "", errors.New(errMsg)
	}

	// Final status update to Completed
	job.Status = StatusCompleted // Use local status constant
	// if err := p.storageService.UpdateJobStatus(ctx, job.ID, job.Status, ""); err != nil { // Comment out
	// 	 log.Printf("Error updating job status to completed for job %s: %v", job.ID, err)
	// 	 // Log error but return success as the result was saved
	// }
	log.Printf("Job %s status set to completed (UpdateJobStatus call commented out)", job.ID)

	return job.ID, nil // Return Job ID on success
}

// ExtractedFile represents a file extracted from a ZIP archive (Restored struct definition)
type ExtractedFile struct {
	Name     string
	Path     string
	Size     int64
	Content  string // Limited size for text files
	IsText   bool
	MimeType string
	ModTime  time.Time
}

// analyzeExtractedContent analyzes extracted content to detect specific content types
// Returns specific metadata proto (WhatsApp or Archive), combined text content, variables map, and error
func (p *ZipProcessor) analyzeExtractedContent(files []ExtractedFile) (interface{}, string, map[string]string, error) {
	isDebug := os.Getenv("PROMZ_DEBUG") == "true"
	if isDebug {
		log.Printf("[ZipProcessor DEBUG] Starting analysis of %d extracted files...", len(files))
	}

	var combinedContent strings.Builder
	var whatsAppFile *ExtractedFile
	var highestConfidence float32 = 0.0
	var totalSize int64 = 0

	for i := range files {
		file := &files[i] // Use pointer to avoid copying large structs
		totalSize += file.Size

		// Combine text content regardless of WhatsApp detection first
		if file.IsText && file.Content != "" {
			combinedContent.WriteString("--- " + file.Path + " ---\n")
			combinedContent.WriteString(file.Content)
			combinedContent.WriteString("\n\n")
		}

		// Now check if it's a potential WhatsApp file
		if file.IsText {
			if isDebug {
				log.Printf("[ZipProcessor DEBUG] Checking file for WhatsApp: %s (Size: %d)", file.Path, file.Size)
			}
			isWhatsApp, confidence := p.whatsAppProcessor.IsWhatsAppChat(file.Content, file.Name)
			if isDebug {
				log.Printf("[ZipProcessor DEBUG] -> IsWhatsAppChat result: isWhatsApp=%t, confidence=%.2f", isWhatsApp, confidence)
			}
			if isWhatsApp && confidence > highestConfidence {
				if isDebug {
					log.Printf("[ZipProcessor DEBUG] -> New highest confidence WhatsApp file found: %s (%.2f)", file.Path, confidence)
				}
				highestConfidence = confidence
				whatsAppFile = file
			}
		}
	}

	// If a WhatsApp chat file was found, process it
	if whatsAppFile != nil {
		if isDebug {
			log.Printf("[ZipProcessor DEBUG] Processing highest confidence WhatsApp file: %s", whatsAppFile.Path)
		}
		result := p.whatsAppProcessor.ProcessContent(whatsAppFile.Content, whatsAppFile.Name)

		if isDebug {
			log.Printf("[ZipProcessor DEBUG] -> ProcessContent result metadata keys: %v", getMapKeys(result.Metadata))
			log.Printf("[ZipProcessor DEBUG] -> ProcessContent result variable keys: %v", getMapKeysString(result.Variables))
		}

		// Create and populate the WhatsAppMetadata protobuf message
		whatsAppMetadata := &pb.WhatsAppMetadata{
			GroupName:        getStringValue(result.Metadata, "groupName"),
			ChatName:         getStringValue(result.Metadata, "chatName"),
			Participants:     getStringSliceValue(result.Metadata, "participants"),
			ParticipantCount: getInt32Value(result.Metadata, "participantCount"),
			MessageCount:     getInt32Value(result.Metadata, "messageCount"),
			IsGroupChat:      getBoolValue(result.Metadata, "isGroupChat"),
			// Use helper to convert timestamps to int64 (assuming epoch milliseconds)
			FirstMessageTimestamp: getInt64TimestampValue(result.Metadata, "firstMessageTimestamp"),
			LastMessageTimestamp:  getInt64TimestampValue(result.Metadata, "lastMessageTimestamp"),
			// SampleMessages would require converting the structure from result.Metadata
			// SampleMessages: convertSampleMessages(result.Metadata["sampleMessages"]),
		}

		if isDebug {
			log.Printf("[ZipProcessor DEBUG] -> Created WhatsAppMetadata proto: %+v", whatsAppMetadata)
		}

		// Return the WhatsApp proto, combined content, and variables
		return whatsAppMetadata, combinedContent.String(), result.Variables, nil

	} else {
		// No WhatsApp chat detected, create ArchiveMetadata
		if isDebug {
			log.Printf("[ZipProcessor DEBUG] No WhatsApp chat file detected with sufficient confidence. Creating ZipMetadata.") // Log ZipMetadata
		}
		archiveMetadata := &pb.ZipMetadata{ // Use ZipMetadata
			FileCount: int32(len(files)),
			// TotalSize: totalSize, // REMOVED - Not a field in ZipMetadata
			// Optionally add more details like a list of FileInfo protos if needed
		}
		// Return the Archive proto, combined content, no variables
		return archiveMetadata, combinedContent.String(), nil, nil
	}
}

// extractZipContents extracts the contents of a ZIP file
// Returns slice of extracted files, total uncompressed size, and error
func (p *ZipProcessor) extractZipContents(zipFilePath string) ([]ExtractedFile, int64, error) {
	// Open the ZIP file
	reader, err := zip.OpenReader(zipFilePath)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to open ZIP file: %v", err)
	}
	defer reader.Close()

	// Initialize slice to hold extracted file info
	extractedFiles := make([]ExtractedFile, 0, len(reader.File))
	var totalSize int64 = 0
	for _, file := range reader.File {
		// Skip directories
		if file.FileInfo().IsDir() {
			continue
		}

		// Open the file
		rc, err := file.Open()
		if err != nil {
			log.Printf("Error opening file %s: %v", file.Name, err)
			continue // Skip this file
		}

		// Read file content (limit size to avoid memory issues)
		fileSize := int64(file.UncompressedSize64)
		var content []byte
		limitReader := io.LimitedReader{R: rc, N: maxTextFileSize + 1} // Read up to 1MB + 1 byte
		content, err = io.ReadAll(&limitReader)
		rc.Close() // Close file reader

		if err != nil && err != io.EOF {
			log.Printf("Error reading file %s: %v", file.Name, err)
			continue // Skip this file
		}

		// Determine if it's a text file and get MIME type
		isText := p.isTextFile(file.Name, content) // Restore call
		mimeType := p.getMimeType(file.Name)       // Restore call

		// Get content as string only if it's text and within size limit
		var contentStr string
		if isText {
			if len(content) > maxTextFileSize {
				log.Printf("Truncating text content for file %s (size %d > %d)", file.Name, len(content), maxTextFileSize)
				contentStr = string(content[:maxTextFileSize]) + "\n... [TRUNCATED]"
			} else {
				contentStr = string(content)
			}
		}

		// Create extracted file info
		extractedFile := ExtractedFile{
			Name:     filepath.Base(file.Name),
			Path:     file.Name,
			Size:     fileSize,
			Content:  contentStr, // Only populated for text files, limited size
			IsText:   isText,
			MimeType: mimeType,
			ModTime:  file.Modified, // Store mod time
		}

		extractedFiles = append(extractedFiles, extractedFile)
		totalSize += extractedFile.Size
	}

	return extractedFiles, totalSize, nil
}

// Helper to convert timestamp interface{} (could be float64, int64, string) to int64
func getInt64TimestampValue(data map[string]interface{}, key string) int64 {
	if val, ok := data[key]; ok {
		switch v := val.(type) {
		case float64:
			return int64(v) // Assuming timestamp is epoch milliseconds/seconds as number
		case int64:
			return v
		case int:
			return int64(v)
		case int32:
			return int64(v)
		case string:
			// Try parsing common formats (RFC3339, or just epoch ms as string)
			if ts, err := time.Parse(time.RFC3339Nano, v); err == nil {
				return ts.UnixMilli() // Use milliseconds for consistency
			}
			if ts, err := time.Parse(time.RFC3339, v); err == nil {
				return ts.UnixMilli()
			}
			// Add parsing for epoch milliseconds string if needed
			// if tsInt, err := strconv.ParseInt(v, 10, 64); err == nil {
			// 	 // Check if it's likely seconds vs milliseconds
			// 	 if tsInt < 1e12 { // Heuristic: likely seconds
			// 	 	return tsInt * 1000
			// 	 } else { // Likely milliseconds
			// 	 	return tsInt
			// 	 }
			// }
			log.Printf("Warning: Could not parse timestamp string '%s' for key '%s' using RFC3339 formats", v, key)

		default:
			log.Printf("Warning: Unexpected type for timestamp key '%s': %T", key, v)

		}
	}
	return 0 // Return zero or handle error appropriately
}

// Helper to get map keys for logging
func getMapKeys[K comparable, V any](m map[K]V) []K {
	keys := make([]K, 0, len(m))
	for k := range m {
		keys = append(keys, k)
	}
	return keys
}

// Helper to get map keys for logging (string values)
func getMapKeysString(m map[string]string) []string {
	keys := make([]string, 0, len(m))
	for k := range m {
		keys = append(keys, k)
	}
	return keys
}

// Helper to get string slice value (Added helper)
func getStringSliceValue(data map[string]interface{}, key string) []string {
	if val, ok := data[key]; ok {
		if slice, ok := val.([]string); ok {
			return slice
		}
		// Handle potential []interface{} conversion
		if interfaceSlice, ok := val.([]interface{}); ok {
			strSlice := make([]string, 0, len(interfaceSlice))
			for _, item := range interfaceSlice {
				if str, ok := item.(string); ok {
					strSlice = append(strSlice, str)
				}
			}
			return strSlice
		}
		log.Printf("Warning: Unexpected type for string slice key '%s': %T", key, val)
	}
	return nil
}

// isTextFile determines if a file is a text file based on name and content
func (p *ZipProcessor) isTextFile(fileName string, content []byte) bool {
	// Check file extension
	ext := strings.ToLower(filepath.Ext(fileName))
	textExtensions := map[string]bool{
		".txt":  true,
		".csv":  true,
		".json": true,
		".xml":  true,
		".html": true,
		".htm":  true,
		".md":   true,
		".log":  true,
		".css":  true,
		".js":   true,
	}

	if textExtensions[ext] {
		return true
	}

	// Check content for binary characters
	if len(content) == 0 {
		return false // Consider empty files non-text for safety?
	}

	// Simple heuristic: check if the content contains too many non-printable characters
	nonPrintable := 0
	sampleSize := min(len(content), 1000) // Use local min helper
	for i := 0; i < sampleSize; i++ {
		c := content[i]
		if (c < 32 || c > 126) && c != 9 && c != 10 && c != 13 { // Allow TAB, LF, CR
			nonPrintable++
		}
	}

	// If more than 10% are non-printable, consider it binary
	return nonPrintable*10 < sampleSize // Equivalent to nonPrintable < sampleSize / 10
}

// getMimeType returns the MIME type for a file based on its extension
func (p *ZipProcessor) getMimeType(fileName string) string {
	ext := strings.ToLower(filepath.Ext(fileName))
	switch ext {
	case ".txt":
		return "text/plain"
	case ".html", ".htm":
		return "text/html"
	case ".css":
		return "text/css"
	case ".js":
		return "application/javascript"
	case ".json":
		return "application/json"
	case ".xml":
		return "application/xml"
	case ".pdf":
		return "application/pdf"
	case ".png":
		return "image/png"
	case ".jpg", ".jpeg":
		return "image/jpeg"
	case ".gif":
		return "image/gif"
	case ".svg":
		return "image/svg+xml"
	case ".mp3":
		return "audio/mpeg"
	case ".mp4":
		return "video/mp4"
	case ".zip":
		return "application/zip"
	default:
		return "application/octet-stream"
	}
}

// DetectContentType detects the content type based on the filename or reader content.
// For ZipProcessor, it primarily relies on the filename extension.
func (p *ZipProcessor) DetectContentType(ctx context.Context, reader io.Reader, filename string) (ContentType, error) {
	// TODO: Implement more robust detection if needed, potentially sniffing bytes from the reader.
	if strings.HasSuffix(strings.ToLower(filename), ".zip") {
		return ContentTypeZip, nil
	}
	// Fallback or more sophisticated detection needed
	return ContentTypeUnknown, nil
}

// ProcessJob is required by the ContentProcessor interface but not the primary method for ZipProcessor.
// It might delegate to UploadFile or could be implemented separately if needed.
func (p *ZipProcessor) ProcessJob(ctx context.Context, job *ProcessingJob) (ProcessingResult, error) {
	// For now, return not implemented. Could potentially call UploadFile and then GetResults,
	// but the return types differ (string vs ProcessingResult).
	return ProcessingResult{}, errors.New("ProcessJob not implemented for ZipProcessor, use UploadFile")
}

// ProcessLargeJob is required by the ContentProcessor interface.
// ZIP file processing typically happens in memory after download, so chunking isn't standard.
func (p *ZipProcessor) ProcessLargeJob(ctx context.Context, job *ProcessingJob, chunkSize int) (ProcessingResult, error) {
	// ZIP files are usually processed whole. Implementing chunked processing would be complex.
	return ProcessingResult{}, errors.New("ProcessLargeJob not implemented for ZipProcessor")
}

// min returns the minimum of two integers (needed by isTextFile)
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// GetResults retrieves the processing results for a given job ID.
// Returns the local ProcessingResult struct to satisfy the FileProcessor interface.
func (p *ZipProcessor) GetResults(ctx context.Context, job *ProcessingJob) (ProcessingResult, error) {
	protoResult, err := p.storageService.GetResult(ctx, job.ID)
	if err != nil {
		log.Printf("Error retrieving results for job %s: %v", job.ID, err)
		// Return local ProcessingResult error representation
		return ProcessingResult{
			ID: job.ID,
		}, fmt.Errorf("failed to get results: %w", err)
	}

	// Storage service GetResult already handles not found and returns error
	if protoResult == nil { // Should ideally not happen if error is nil, but check defensively
		log.Printf("Storage service returned nil result without error for job %s", job.ID)
		// Handle not found case by returning local struct representation
		return ProcessingResult{
			ID: job.ID,
		}, fmt.Errorf("results not found for job %s", job.ID)
	}

	// Convert *pb.ProcessingResult to local ProcessingResult
	localResult := ProcessingResult{
		ID:          protoResult.JobId,
		ContentType: protoResult.ContentType,      // It's already a string
		Content:     protoResult.Content,          // Assign string to interface{}
		Metadata:    make(map[string]interface{}), // Initialize,
	}

	// Convert ExpiresAt (assuming it's stored as RFC3339 string in proto for now)
	if protoResult.ExpiresAt != "" {
		if t, err := time.Parse(time.RFC3339Nano, protoResult.ExpiresAt); err == nil {
			localResult.ExpiresAt = t
		} else {
			log.Printf("Warning: Could not parse ExpiresAt '%s' for job %s: %v", protoResult.ExpiresAt, job.ID, err)
			// Leave ExpiresAt zero or handle error
		}
	}

	return localResult, nil
}
