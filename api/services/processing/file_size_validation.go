package processing

import (
	"context"
	"fmt"
	"log"

	"golang.org/x/text/cases"
	"golang.org/x/text/language"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	pb "promz.ai/api/proto/gen"
)

// ValidateFileSize validates if a file size is within the limits for a given license tier
// This is a separate RPC endpoint that can be called before uploading a file
func (s *FileProcessingService) ValidateFileSize(ctx context.Context, req *pb.FileSizeValidationRequest) (*pb.FileSizeValidationResponse, error) {
	// Extract user ID from context (set by auth interceptor)
	userId, ok := ctx.Value("user_id").(string)
	if !ok || userId == "" {
		return nil, status.Error(codes.Unauthenticated, "Missing or invalid user ID")
	}

	// Get the user's actual license tier from the database based on user ID
	// This is more secure than trusting client-provided tier information
	licenseTier, err := s.uploadHandler.GetUserLicenseTier(userId)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "Error retrieving license tier: %v", err)
	}

	// Log the license tier for debugging
	log.Printf("User %s has license tier: %s", userId, licenseTier)

	// Get tier configuration
	tierConfig := s.uploadHandler.Config.GetTierConfig(licenseTier)

	// Check if file size exceeds the tier limit
	if req.FileSizeBytes > tierConfig.MaxFileSizeBytes {
		// File size exceeds limit, determine next tier that would support it
		nextTier := s.getNextTierForFileSize(req.FileSizeBytes)

		// Create error message
		errorMessage := fmt.Sprintf(
			"File size (%.2f MB) exceeds the %s tier limit of %.2f MB",
			float64(req.FileSizeBytes)/(1024*1024),
			cases.Title(language.English).String(licenseTier),
			float64(tierConfig.MaxFileSizeBytes)/(1024*1024),
		)

		// Add upgrade suggestion if a next tier is available
		if nextTier != "" {
			errorMessage += fmt.Sprintf(". Please upgrade to %s tier", nextTier)
		}

		// Create error details map
		errorDetails := make(map[string]string)
		errorDetails["current_tier"] = licenseTier
		errorDetails["max_size_bytes"] = fmt.Sprintf("%d", tierConfig.MaxFileSizeBytes)
		errorDetails["file_size_bytes"] = fmt.Sprintf("%d", req.FileSizeBytes)

		// Return validation response with error details
		return &pb.FileSizeValidationResponse{
			IsValid:      false,
			ErrorCode:    pb.FileProcessingErrorCode_FILE_SIZE_LIMIT_EXCEEDED,
			ErrorMessage: errorMessage,
			ErrorDetails: errorDetails,
			NextTier:     nextTier,
			MaxSizeBytes: tierConfig.MaxFileSizeBytes,
		}, nil
	}

	// File size is within limits
	return &pb.FileSizeValidationResponse{
		IsValid:      true,
		MaxSizeBytes: tierConfig.MaxFileSizeBytes,
	}, nil
}

// getNextTierForFileSize determines the next tier that would support the given file size
func (s *FileProcessingService) getNextTierForFileSize(fileSize int64) string {
	// Get tier configurations
	freeTier := s.uploadHandler.Config.GetTierConfig("free")
	proTier := s.uploadHandler.Config.GetTierConfig("pro")
	enterpriseTier := s.uploadHandler.Config.GetTierConfig("enterprise")

	// Check which tier would support the file size
	if fileSize <= freeTier.MaxFileSizeBytes {
		return "" // Free tier is sufficient
	} else if fileSize <= proTier.MaxFileSizeBytes {
		return "Pro" // Pro tier would support it
	} else if fileSize <= enterpriseTier.MaxFileSizeBytes {
		return "Enterprise" // Enterprise tier would support it
	}

	// No tier supports this file size
	return ""
}
