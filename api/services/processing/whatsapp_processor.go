package processing

import (
	"fmt"
	"log"
	"os"
	"regexp"
	"strings"
	"sync"

	pb "promz.ai/api/proto/gen"
)

// WhatsAppProcessor processes WhatsApp chat content
type WhatsAppProcessor struct {
	// Add a mutex for thread safety
	mutex sync.RWMutex
	// Add a cache to prevent redundant processing
	contentCache map[string]ProcessedWhatsAppResult
}

// NewWhatsAppProcessor creates a new WhatsApp processor
func NewWhatsAppProcessor() *WhatsAppProcessor {
	return &WhatsAppProcessor{
		contentCache: make(map[string]ProcessedWhatsAppResult),
	}
}

// IsWhatsAppChat determines if the content is a WhatsApp chat and returns a confidence score
func (p *WhatsAppProcessor) IsWhatsAppChat(content string, fileName string) (bool, float32) {
	isDebug := os.Getenv("PROMZ_DEBUG") == "true"
	if isDebug {
		log.Printf("[WhatsAppProcessor DEBUG] IsWhatsAppChat called for file: %s", fileName)
	}
	confidence := float32(0.0)

	// Check filename for WhatsApp indicators
	fileNameLower := strings.ToLower(fileName)
	if strings.Contains(fileNameLower, "whatsapp") {
		confidence += 0.4 // Increased from 0.3
		if isDebug {
			log.Printf("[WhatsAppProcessor DEBUG] -> Filename 'whatsapp' match (+0.4)")
		} // Updated log
	}
	if strings.Contains(fileNameLower, "chat") {
		confidence += 0.2 // Increased from 0.1
		if isDebug {
			log.Printf("[WhatsAppProcessor DEBUG] -> Filename 'chat' match (+0.2)")
		} // Updated log
	}

	// Early exit if filename provides enough confidence
	if confidence >= 0.5 {
		if isDebug {
			log.Printf("[WhatsAppProcessor DEBUG] -> Early confidence from filename: %.2f (enough to determine)",
				confidence)
		}
		return true, confidence
	}

	// Check content for WhatsApp chat patterns
	if len(content) > 0 {
		// Split content into lines for checking header and message patterns
		lines := strings.Split(content, "\n")
		messagePatternFound := false

		// Check first few lines for header (case-insensitive)
		linesToCheck := 5
		if len(lines) < linesToCheck {
			linesToCheck = len(lines)
		}
		headerRegex := regexp.MustCompile(`(?i)whatsapp chat with`)
		for i := 0; i < linesToCheck; i++ {
			if headerRegex.MatchString(lines[i]) {
				confidence += 0.4
				if isDebug {
					log.Printf("[WhatsAppProcessor DEBUG] -> Content header 'whatsapp chat with' match (+0.4)")
				}
				break
			}
		}

		// Check lines for message patterns using parseWhatsAppDateTime
		// More liberal regex to find potential lines, then validate timestamp
		messageLineRegex := regexp.MustCompile(`^(.+?) - `)
		participantLineRegex := regexp.MustCompile(`^(.+?) - ([^:]+): `)
		participantPatternFound := false // Track participant pattern separately

		// Limit the number of lines to check for patterns to improve performance
		maxLinesToCheckForPatterns := 100
		if len(lines) > maxLinesToCheckForPatterns {
			if isDebug {
				log.Printf("[WhatsAppProcessor DEBUG] -> Limiting pattern check to %d of %d lines",
					maxLinesToCheckForPatterns, len(lines))
			}
		}
		linesToExamine := min(len(lines), maxLinesToCheckForPatterns)

		for i := 0; i < linesToExamine; i++ {
			line := lines[i]
			match := messageLineRegex.FindStringSubmatch(line)
			if len(match) >= 2 {
				potentialDateTime := match[1]
				if _, ok := parseWhatsAppDateTime(potentialDateTime); ok {
					// Found a line starting with a valid timestamp
					if !messagePatternFound { // Add confidence only once for message pattern
						confidence += 0.4
						if isDebug {
							log.Printf("[WhatsAppProcessor DEBUG] -> Content message pattern match (+0.4)")
						}
						messagePatternFound = true
					}

					// Check if it also matches the participant pattern
					if participantLineRegex.MatchString(line) && !participantPatternFound {
						confidence += 0.2
						if isDebug {
							log.Printf("[WhatsAppProcessor DEBUG] -> Content participant pattern match (+0.2)")
						}
						participantPatternFound = true
					}

					// Optimization: If both patterns found, no need to check further lines for confidence
					if messagePatternFound && participantPatternFound {
						break
					}
				}
			}
		}

	} else {
		if isDebug {
			log.Printf("[WhatsAppProcessor DEBUG] -> Content is empty, skipping content checks.")
		}
	}

	// Determine if it's a WhatsApp chat based on confidence threshold
	isWhatsApp := confidence >= 0.5 // Keep original threshold

	// Normalize confidence to 0.0-1.0 range
	if confidence > 1.0 {
		confidence = 1.0
	}

	if isDebug {
		log.Printf("[WhatsAppProcessor DEBUG] -> Final confidence: %.2f, IsWhatsApp: %t", confidence, isWhatsApp)
	}

	return isWhatsApp, confidence
}

// ProcessedWhatsAppResult contains the processed WhatsApp content and metadata
type ProcessedWhatsAppResult struct {
	Content          string
	FileName         string
	Metadata         map[string]interface{} // Keep for backward compatibility
	Variables        map[string]string      // For variable substitution
	WhatsAppMetadata *pb.WhatsAppMetadata   // Direct proto metadata
}

// ProcessContent processes WhatsApp chat content and extracts metadata
func (p *WhatsAppProcessor) ProcessContent(content string, fileName string) ProcessedWhatsAppResult {
	isDebug := os.Getenv("PROMZ_DEBUG") == "true"

	// Generate a cache key based on content hash or first few lines
	cacheKey := generateCacheKey(content, fileName)

	// Check cache first
	p.mutex.RLock()
	if result, exists := p.contentCache[cacheKey]; exists {
		p.mutex.RUnlock()
		if isDebug {
			log.Printf("[WhatsAppProcessor DEBUG] Cache hit for file: %s", fileName)
		}
		return result
	}
	p.mutex.RUnlock()

	if isDebug {
		log.Printf("[WhatsAppProcessor DEBUG] ProcessContent called for file: %s", fileName)
		// Log the beginning of the content
		contentSample := content
		if len(contentSample) > 200 {
			contentSample = contentSample[:200] + "..."
		}
		log.Printf("[WhatsAppProcessor DEBUG] -> Content sample: %s", contentSample)
	}

	// Uses helper functions from whatsapp_helpers.go
	metadata := p.extractChatMetadata(content) // Use local helper method

	// Try to extract group name using helper
	groupName := extractGroupNameFromContent(content) // Use helper from whatsapp_helpers.go
	if groupName != "" {
		metadata["groupName"] = groupName
	}

	// Add source type for client rendering
	metadata["sourceType"] = "conversation"
	metadata["isWhatsAppChat"] = true

	// Extract data for proto metadata
	participantsList, _ := metadata["participants"].([]string)
	participantCount := len(participantsList)
	messageCount := getIntValue(metadata, "messageCount")
	isGroupChat := participantCount > 1
	metadata["isGroupChat"] = isGroupChat

	// Create WhatsApp metadata proto
	whatsAppMetadata := &pb.WhatsAppMetadata{
		GroupName:        groupName,
		ChatName:         fileName, // Use filename as chat name if no group name
		Participants:     participantsList,
		ParticipantCount: int32(participantCount),
		MessageCount:     int32(messageCount),
		IsGroupChat:      isGroupChat,
	}

	// Create variables map - only add if data exists
	variables := make(map[string]string)
	if groupName != "" {
		variables["CONVERSATION:GROUP_NAME"] = groupName
	}
	if content != "" {
		// Limit content size for variable to prevent memory issues
		contentForVariable := content
		if len(contentForVariable) > 10000 {
			contentForVariable = contentForVariable[:10000] + "... (content truncated)"
		}
		variables["CONVERSATION:CONTENTS"] = contentForVariable // Keep original content for variable
	}
	if participantCount > 0 {
		// Convert participants slice to comma-separated string for variable
		variables["CONVERSATION:PARTICIPANTS"] = strings.Join(participantsList, ", ")
		variables["CONVERSATION:PARTICIPANT_COUNT"] = fmt.Sprintf("%d", participantCount)
	}
	if startDate, ok := metadata["startDate"].(string); ok && startDate != "" {
		variables["CONVERSATION:START_DATE"] = startDate
	}
	if endDate, ok := metadata["endDate"].(string); ok && endDate != "" {
		variables["CONVERSATION:END_DATE"] = endDate
	}
	if messageCount > 0 {
		variables["CONVERSATION:MESSAGE_COUNT"] = fmt.Sprintf("%d", messageCount)
	}

	if isDebug {
		log.Printf("[WhatsAppProcessor DEBUG] Created WhatsAppMetadata proto with %d participants, %d messages, group chat: %t",
			whatsAppMetadata.ParticipantCount, whatsAppMetadata.MessageCount, whatsAppMetadata.IsGroupChat)
	}

	result := ProcessedWhatsAppResult{
		Content:          content, // Return original content, processing/cleaning can be added later if needed
		FileName:         fileName,
		Metadata:         metadata,         // Keep for backward compatibility
		Variables:        variables,        // Include variables
		WhatsAppMetadata: whatsAppMetadata, // Include proto metadata
	}

	// Store in cache
	p.mutex.Lock()
	p.contentCache[cacheKey] = result
	// Limit cache size to prevent memory issues
	if len(p.contentCache) > 100 {
		// Remove a random entry if cache gets too large
		for k := range p.contentCache {
			delete(p.contentCache, k)
			break
		}
	}
	p.mutex.Unlock()

	return result
}

// extractChatMetadata extracts metadata from WhatsApp chat content
// Kept as a private method within the processor
func (p *WhatsAppProcessor) extractChatMetadata(content string) map[string]interface{} {
	participantsList := extractParticipants(content)
	messageCount := countMessages(content)

	// Simplified metadata extraction, timestamp/duration logic can be refined
	metadata := map[string]interface{}{
		"participants":     participantsList,
		"participantCount": len(participantsList),
		"messageCount":     messageCount,
	}

	return metadata
}

// generateCacheKey creates a unique key for caching processed content
// This is a simple implementation that could be improved for production
func generateCacheKey(content string, fileName string) string {
	// Use first 1000 chars + filename as a simple cache key
	contentSample := content
	if len(contentSample) > 1000 {
		contentSample = contentSample[:1000]
	}
	return fileName + ":" + contentSample
}
