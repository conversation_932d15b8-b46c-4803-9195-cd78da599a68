package processing

import (
	"context"
	"errors"
	"log"
)

// GetJob retrieves a job by ID
func (h *UploadHandler) GetJob(id string) (*ProcessingJob, bool) {
	h.jobsMutex.RLock()
	job, exists := h.jobs[id]
	h.jobsMutex.RUnlock()
	return job, exists
}

// GetProcessedContent retrieves the processed content for a job by ID
func (h *UploadHandler) GetProcessedContent(ctx context.Context, jobID string) (string, error) {
	// Get the job
	job, exists := h.GetJob(jobID)
	if !exists {
		return "", errors.New("job not found")
	}

	// Check if job is completed
	if job.Status != StatusCompleted {
		return "", errors.New("processing not complete")
	}

	// Get the results from the processor
	result, err := h.Processor.GetResults(ctx, job)
	if err != nil {
		log.Printf("Error retrieving results for job %s: %v", jobID, err)
		return "", err
	}

	// Get the content from the result
	switch content := result.Content.(type) {
	case string:
		if content == "" {
			return "", errors.New("empty content")
		}
		return content, nil
	case map[string]interface{}:
		// If content is a map, try to get the 'content' field
		if textContent, ok := content["content"].(string); ok && textContent != "" {
			return textContent, nil
		}
	}

	return "", errors.New("no content available")
}
