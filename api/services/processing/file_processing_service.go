package processing

import (
	"context"
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"sync"
	"time"

	"github.com/google/uuid"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

// FileProcessingService implements the gRPC file processing service
type FileProcessingService struct {
	// Embed the upload handler to reuse existing functionality
	uploadHandler *UploadHandler

	// Map to track active uploads
	activeUploads      map[string]*activeUpload
	activeUploadsMutex sync.RWMutex

	// WebSocket handler for real-time updates
	webSocketHandler WebSocketHandler
}

// activeUpload tracks an in-progress upload
type activeUpload struct {
	metadata     map[string]string
	fileName     string
	mimeType     string
	fileSize     int64
	licenseTier  string
	tempFilePath string
	file         *os.File
	chunkIndex   int32
	userId       string
	createdAt    time.Time
}

// NewFileProcessingService creates a new gRPC file processing service
func NewFileProcessingService(
	uploadHand<PERSON> *UploadHandler,
	webSocketHandler WebSocketHandler,
) *FileProcessingService {
	return &FileProcessingService{
		uploadHandler:      uploadHandler,
		activeUploads:      make(map[string]*activeUpload),
		activeUploadsMutex: sync.RWMutex{},
		webSocketHandler:   webSocketHandler,
	}
}

// UploadFile handles streaming file uploads
func (s *FileProcessingService) UploadFile(stream FileProcessingService_UploadFileServer) error {
	ctx := stream.Context()

	// Extract user ID from context (set by auth interceptor)
	userId, ok := ctx.Value("user_id").(string)
	if !ok || userId == "" {
		return status.Error(codes.Unauthenticated, "Missing or invalid user ID")
	}

	// Generate a unique ID for this upload
	uploadId := uuid.New().String()
	// Define a variable to hold the upload data
	var uploadData *activeUpload

	// Process the stream
	for {
		// Check if context is done
		select {
		case <-ctx.Done():
			return cleanupAndReturnError(s, uploadId, ctx.Err())
		default:
		}

		// Receive the next message
		req, err := stream.Recv()
		if err == io.EOF {
			// End of stream, finalize the upload
			return s.finalizeUpload(ctx, stream, uploadId, userId)
		}
		if err != nil {
			return cleanupAndReturnError(s, uploadId, err)
		}

		// Process the message based on its type
		switch r := req.Request.(type) {
		case *FileUploadRequest_Metadata:
			// Handle metadata message (should be the first message)
			if uploadData != nil {
				return cleanupAndReturnError(s, uploadId, status.Error(codes.InvalidArgument, "Metadata already received"))
			}

			metadata := r.Metadata

			// Check file size against tier limits
			licenseTier := metadata.LicenseTier
			if licenseTier == "" {
				licenseTier = "free" // Default to free tier
			}

			tierConfig := s.uploadHandler.Config.GetTierConfig(licenseTier)
			if metadata.FileSize > tierConfig.MaxFileSizeBytes {
				return status.Errorf(codes.InvalidArgument,
					"File size exceeds limit for %s tier: %d bytes",
					licenseTier, tierConfig.MaxFileSizeBytes)
			}

			// Create temp file for the upload
			tempFilePath := filepath.Join(os.TempDir(), uploadId+filepath.Ext(metadata.FileName))
			file, err := os.Create(tempFilePath)
			if err != nil {
				return cleanupAndReturnError(s, uploadId, status.Errorf(codes.Internal, "Error creating temp file: %v", err))
			}

			// Create active upload record
			customMetadata := make(map[string]string)
			for k, v := range metadata.CustomMetadata {
				customMetadata[k] = v
			}

			// Add user ID and license tier to metadata
			customMetadata["userId"] = userId
			customMetadata["licenseTier"] = licenseTier

			uploadData = &activeUpload{
				metadata:     customMetadata,
				fileName:     metadata.FileName,
				mimeType:     metadata.MimeType,
				fileSize:     metadata.FileSize,
				licenseTier:  licenseTier,
				tempFilePath: tempFilePath,
				file:         file,
				chunkIndex:   0,
				userId:       userId,
				createdAt:    time.Now(),
			}

			// Store the active upload
			s.activeUploadsMutex.Lock()
			s.activeUploads[uploadId] = uploadData
			s.activeUploadsMutex.Unlock()

			// Log the start of the upload
			log.Printf("Started file upload: id=%s, fileName=%s, size=%d, tier=%s",
				uploadId, metadata.FileName, metadata.FileSize, licenseTier)

		case *FileUploadRequest_Chunk:
			// Handle chunk message
			if uploadData == nil {
				return cleanupAndReturnError(s, uploadId, status.Error(codes.InvalidArgument, "Metadata not received"))
			}

			// Verify chunk index
			if r.Chunk.ChunkIndex != uploadData.chunkIndex {
				return cleanupAndReturnError(s, uploadId, status.Errorf(codes.InvalidArgument,
					"Invalid chunk index: expected %d, got %d",
					uploadData.chunkIndex, r.Chunk.ChunkIndex))
			}

			// Write chunk to file
			_, err := uploadData.file.Write(r.Chunk.Data)
			if err != nil {
				return cleanupAndReturnError(s, uploadId, status.Errorf(codes.Internal, "Error writing chunk: %v", err))
			}

			// Increment chunk index
			uploadData.chunkIndex++

		default:
			return cleanupAndReturnError(s, uploadId, status.Error(codes.InvalidArgument, "Invalid request type"))
		}
	}
}

// finalizeUpload completes the upload process and starts processing
func (s *FileProcessingService) finalizeUpload(
	ctx context.Context,
	stream FileProcessingService_UploadFileServer,
	uploadId string,
	userId string,
) error {
	// Get the active upload
	s.activeUploadsMutex.RLock()
	uploadData, exists := s.activeUploads[uploadId]
	s.activeUploadsMutex.RUnlock()

	if !exists || uploadData == nil {
		return status.Error(codes.NotFound, "Upload not found")
	}

	// Close the file
	if err := uploadData.file.Close(); err != nil {
		return cleanupAndReturnError(s, uploadId, status.Errorf(codes.Internal, "Error closing file: %v", err))
	}

	// Upload to storage
	tierConfig := s.uploadHandler.Config.GetTierConfig(uploadData.licenseTier)
	objectName := fmt.Sprintf("uploads/%s/%s", userId, filepath.Base(uploadData.tempFilePath))

	if err := s.uploadHandler.StorageService.UploadFile(ctx, uploadData.tempFilePath, objectName, tierConfig.MaxStorageDays); err != nil {
		return cleanupAndReturnError(s, uploadId, status.Errorf(codes.Internal, "Error uploading to storage: %v", err))
	}

	// Create processing job
	job := &ProcessingJob{
		ID:          uploadId,
		FileName:    uploadData.fileName,
		MimeType:    uploadData.mimeType,
		Status:      StatusQueued,
		Progress:    0.0,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		UserID:      userId,
		FilePath:    objectName,
		FileSize:    uploadData.fileSize,
		LicenseTier: uploadData.licenseTier,
		MaxTokens:   tierConfig.MaxTokens,
		ExpiresAt:   time.Now().AddDate(0, 0, tierConfig.MaxStorageDays),
	}

	// Store the job
	s.uploadHandler.jobsMutex.Lock()
	s.uploadHandler.jobs[uploadId] = job
	s.uploadHandler.jobsMutex.Unlock()

	// Start processing in background
	go s.processInBackground(ctx, job)

	// Send response
	response := &FileUploadResponse{
		Id:                   uploadId,
		Status:               string(StatusQueued),
		EstimatedTimeSeconds: estimateProcessingTime(uploadData.fileSize),
		MaxTokens:            int32(job.MaxTokens),
		FileSize:             uploadData.fileSize,
		LicenseTier:          uploadData.licenseTier,
	}

	if err := stream.SendAndClose(response); err != nil {
		log.Printf("Error sending response: %v", err)
		return status.Errorf(codes.Internal, "Error sending response: %v", err)
	}

	// Cleanup the active upload
	s.activeUploadsMutex.Lock()
	delete(s.activeUploads, uploadId)
	s.activeUploadsMutex.Unlock()

	// Log the completion of the upload
	log.Printf("Completed file upload: id=%s, fileName=%s", uploadId, uploadData.fileName)

	return nil
}

// processInBackground processes the file in the background
func (s *FileProcessingService) processInBackground(ctx context.Context, job *ProcessingJob) {
	// Update job status to processing
	s.uploadHandler.updateJobStatus(job.ID, StatusProcessing, 0.0, "")

	// Send WebSocket notification
	s.sendStatusUpdate(job)

	// Process the file
	result, err := s.uploadHandler.Processor.ProcessJob(ctx, job)

	if err != nil {
		// Update job status to failed
		s.uploadHandler.updateJobStatus(job.ID, StatusFailed, 0.0, err.Error())

		// Send WebSocket notification
		s.sendStatusUpdate(job)

		log.Printf("Error processing file: id=%s, error=%v", job.ID, err)
		return
	}

	// Update job with result info
	job.Status = StatusCompleted
	job.Progress = 1.0
	job.ResultID = result.ID
	// Since ProcessingResult doesn't have these fields, we'll set them directly
	// We would normally get this from the result, but for now we'll use the job's values
	job.UpdatedAt = time.Now()

	// Send WebSocket notification
	s.sendStatusUpdate(job)

	log.Printf("Completed file processing: id=%s", job.ID)
}

// GetStatus handles status check requests
func (s *FileProcessingService) GetStatus(ctx context.Context, req *StatusRequest) (*StatusResponse, error) {
	// Extract user ID from context
	userId, ok := ctx.Value("user_id").(string)
	if !ok || userId == "" {
		return nil, status.Error(codes.Unauthenticated, "Missing or invalid user ID")
	}

	// Get job
	s.uploadHandler.jobsMutex.RLock()
	job, exists := s.uploadHandler.jobs[req.Id]
	s.uploadHandler.jobsMutex.RUnlock()

	if !exists {
		return nil, status.Error(codes.NotFound, "Job not found")
	}

	// Check if job belongs to user
	if job.UserID != userId {
		return nil, status.Error(codes.PermissionDenied, "Job does not belong to user")
	}

	// Create response
	response := &StatusResponse{
		ID:              job.ID,
		Status:          string(job.Status),
		Progress:        job.Progress,
		Error:           job.Error,
		Message:         job.Message,
		TokensProcessed: job.TokensProcessed,
		TokensLimit:     job.TokensLimit,
		TokensExceeded:  job.TokensExceeded,
	}

	return response, nil
}

// GetResults handles result retrieval requests
func (s *FileProcessingService) GetResults(req *ResultsRequest, stream FileProcessingService_GetResultsServer) error {
	ctx := stream.Context()

	// Extract user ID from context
	userId, ok := ctx.Value("user_id").(string)
	if !ok || userId == "" {
		return status.Error(codes.Unauthenticated, "Missing or invalid user ID")
	}

	// Get job
	s.uploadHandler.jobsMutex.RLock()
	job, exists := s.uploadHandler.jobs[req.Id]
	s.uploadHandler.jobsMutex.RUnlock()

	if !exists {
		return status.Error(codes.NotFound, "Job not found")
	}

	// Check if job belongs to user
	if job.UserID != userId {
		return status.Error(codes.PermissionDenied, "Job does not belong to user")
	}

	// Check if job is completed
	if job.Status != StatusCompleted {
		return status.Errorf(codes.FailedPrecondition, "Job is not completed: %s", job.Status)
	}

	// Check if result exists
	if job.ResultID == "" {
		return status.Error(codes.NotFound, "Result not found")
	}

	// Get result path
	resultPath := fmt.Sprintf("results/%s/%s", userId, job.ResultID)

	// Get result metadata
	// For now, we'll create empty metadata since GetMetadata is not implemented yet
	metadata := make(map[string]string)

	// Create content URL
	contentUrl := fmt.Sprintf("/api/v1/processing/storage/%s", resultPath)

	// Send metadata message
	metadataMsg := &ResultsResponse_Metadata{
		Id:              job.ResultID,
		ContentType:     job.MimeType,
		Metadata:        metadata,
		HasFullContent:  true,
		ContentUrl:      contentUrl,
		ExpiresAt:       job.ExpiresAt.Unix(),
		TokensProcessed: int32(job.TokensProcessed),
		TokensLimit:     int32(job.MaxTokens),
		TokensExceeded:  job.TokensExceeded,
	}

	if err := stream.Send(&ResultsResponse{
		Response: &ResultsResponse_Metadata_{
			Metadata: metadataMsg,
		},
	}); err != nil {
		return status.Errorf(codes.Internal, "Error sending metadata: %v", err)
	}

	// Get result content
	contentReader, err := s.uploadHandler.StorageService.GetObject(ctx, resultPath)
	if err != nil {
		return status.Errorf(codes.Internal, "Error getting result content: %v", err)
	}
	defer contentReader.Close()

	// Stream content in chunks
	buffer := make([]byte, 64*1024) // 64KB chunks
	chunkIndex := int32(0)

	for {
		n, err := contentReader.Read(buffer)
		if err == io.EOF {
			// Send last chunk
			if n > 0 {
				if err := stream.Send(&ResultsResponse{
					Response: &ResultsResponse_Content{
						Content: &ResultsResponse_ContentChunk{
							Data:       buffer[:n],
							ChunkIndex: chunkIndex,
							IsLast:     true,
						},
					},
				}); err != nil {
					return status.Errorf(codes.Internal, "Error sending last chunk: %v", err)
				}
			}
			break
		}
		if err != nil {
			return status.Errorf(codes.Internal, "Error reading content: %v", err)
		}

		// Send chunk
		if err := stream.Send(&ResultsResponse{
			Response: &ResultsResponse_Content{
				Content: &ResultsResponse_ContentChunk{
					Data:       buffer[:n],
					ChunkIndex: chunkIndex,
					IsLast:     false,
				},
			},
		}); err != nil {
			return status.Errorf(codes.Internal, "Error sending chunk: %v", err)
		}

		chunkIndex++
	}

	return nil
}

// CancelProcessing handles cancellation requests
func (s *FileProcessingService) CancelProcessing(ctx context.Context, req *CancelRequest) (*CancelResponse, error) {
	// Extract user ID from context
	userId, ok := ctx.Value("user_id").(string)
	if !ok || userId == "" {
		return nil, status.Error(codes.Unauthenticated, "Missing or invalid user ID")
	}

	// Get job
	s.uploadHandler.jobsMutex.RLock()
	job, exists := s.uploadHandler.jobs[req.Id]
	s.uploadHandler.jobsMutex.RUnlock()

	if !exists {
		return nil, status.Error(codes.NotFound, "Job not found")
	}

	// Check if job belongs to user
	if job.UserID != userId {
		return nil, status.Error(codes.PermissionDenied, "Job does not belong to user")
	}

	// Only cancel if not completed
	if job.Status == StatusCompleted || job.Status == StatusFailed {
		return &CancelResponse{
			Success: false,
			Message: fmt.Sprintf("Cannot cancel job with status: %s", job.Status),
		}, nil
	}

	// Update status to failed with cancellation message
	s.uploadHandler.updateJobStatus(req.Id, StatusFailed, 0.0, "Cancelled by user")

	// Send WebSocket notification
	s.uploadHandler.jobsMutex.RLock()
	updatedJob := s.uploadHandler.jobs[req.Id]
	s.uploadHandler.jobsMutex.RUnlock()
	s.sendStatusUpdate(updatedJob)

	// Return success
	return &CancelResponse{
		Success: true,
		Message: "Job cancelled successfully",
	}, nil
}

// sendStatusUpdate sends a status update via WebSocket
func (s *FileProcessingService) sendStatusUpdate(job *ProcessingJob) {
	if s.webSocketHandler == nil {
		return
	}

	// Create status update
	update := &FileUploadUpdate{
		Id:              job.ID,
		Status:          string(job.Status),
		Progress:        job.Progress,
		Error:           job.Error,
		Message:         job.Message,
		TokensProcessed: int32(job.TokensProcessed),
		TokensLimit:     int32(job.MaxTokens),
		TokensExceeded:  job.TokensExceeded,
		Timestamp:       time.Now().Unix(),
	}

	// Send update to specific user
	topic := fmt.Sprintf("file_processing_%s", job.ID)
	s.webSocketHandler.SendToUser(job.UserID, topic, update)
}

// cleanupAndReturnError cleans up resources and returns an error
func cleanupAndReturnError(s *FileProcessingService, uploadId string, err error) error {
	// Remove from active uploads
	s.activeUploadsMutex.Lock()
	delete(s.activeUploads, uploadId)
	s.activeUploadsMutex.Unlock()

	// Get active upload
	s.activeUploadsMutex.RLock()
	uploadData, exists := s.activeUploads[uploadId]
	s.activeUploadsMutex.RUnlock()

	if exists && uploadData != nil {
		// Close and remove file
		if uploadData.file != nil {
			uploadData.file.Close()
		}
		if uploadData.tempFilePath != "" {
			os.Remove(uploadData.tempFilePath)
		}
	}

	// If it's already a gRPC status error, return it directly
	if _, ok := status.FromError(err); ok {
		return err
	}

	// Otherwise, wrap it in a gRPC status error
	return status.Errorf(codes.Internal, "Upload failed: %v", err)
}

// estimateProcessingTime estimates the processing time based on file size
func estimateProcessingTime(fileSize int64) int32 {
	// Simple estimation based on file size
	// 1MB ~ 5 seconds, with minimum of 10 seconds
	estimatedSeconds := int32((fileSize / (1024 * 1024)) * 5)
	if estimatedSeconds < 10 {
		estimatedSeconds = 10
	}
	return estimatedSeconds
}

// WebSocketHandler interface for sending WebSocket messages
type WebSocketHandler interface {
	// SendToUser sends a message to a specific user
	SendToUser(userId string, topic string, message interface{}) error

	// Broadcast sends a message to all connected users
	Broadcast(topic string, message interface{}) error
}
