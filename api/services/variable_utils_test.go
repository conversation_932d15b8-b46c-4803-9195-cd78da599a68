package services

import (
	"regexp"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestExtractPrefix(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "No prefix",
			input:    "FINANCE:TICKER",
			expected: "",
		},
		{
			name:     "Hash prefix",
			input:    "{#1}FINANCE:TICKER",
			expected: "{#1}",
		},
		{
			name:     "Dollar prefix",
			input:    "{$1}FINANCE:TICKER",
			expected: "{$1}",
		},
		{
			name:     "Complex hash prefix",
			input:    "{#abc123}FINANCE:TICKER",
			expected: "{#abc123}",
		},
		{
			name:     "Complex dollar prefix",
			input:    "{$xyz789}FINANCE:TICKER",
			expected: "{$xyz789}",
		},
		{
			name:     "Invalid prefix format",
			input:    "{1}FINANCE:TICKER",
			expected: "",
		},
		{
			name:     "Empty string",
			input:    "",
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ExtractPrefix(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestExtractBaseName(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "No prefix",
			input:    "FINANCE:TICKER",
			expected: "FINANCE:TICKER",
		},
		{
			name:     "Hash prefix",
			input:    "{#1}FINANCE:TICKER",
			expected: "FINANCE:TICKER",
		},
		{
			name:     "Dollar prefix",
			input:    "{$1}FINANCE:TICKER",
			expected: "FINANCE:TICKER",
		},
		{
			name:     "Complex hash prefix",
			input:    "{#abc123}FINANCE:TICKER",
			expected: "FINANCE:TICKER",
		},
		{
			name:     "Complex dollar prefix",
			input:    "{$xyz789}FINANCE:TICKER",
			expected: "FINANCE:TICKER",
		},
		{
			name:     "Invalid prefix format",
			input:    "{1}FINANCE:TICKER",
			expected: "{1}FINANCE:TICKER",
		},
		{
			name:     "Empty string",
			input:    "",
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ExtractBaseName(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestAddPrefixToVariable(t *testing.T) {
	tests := []struct {
		name     string
		prefix   string
		baseName string
		expected string
	}{
		{
			name:     "No prefix",
			prefix:   "",
			baseName: "FINANCE:TICKER",
			expected: "FINANCE:TICKER",
		},
		{
			name:     "Hash prefix",
			prefix:   "{#1}",
			baseName: "FINANCE:TICKER",
			expected: "{#1}FINANCE:TICKER",
		},
		{
			name:     "Dollar prefix",
			prefix:   "{$1}",
			baseName: "FINANCE:TICKER",
			expected: "{$1}FINANCE:TICKER",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := AddPrefixToVariable(tt.prefix, tt.baseName)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestIsFinanceVariable(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected bool
	}{
		{
			name:     "Finance variable",
			input:    "FINANCE:TICKER",
			expected: true,
		},
		{
			name:     "Finance variable with prefix",
			input:    "{#1}FINANCE:TICKER",
			expected: true,
		},
		{
			name:     "Non-finance variable",
			input:    "LOCATION:CITY",
			expected: false,
		},
		{
			name:     "Non-finance variable with prefix",
			input:    "{#1}LOCATION:CITY",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsFinanceVariable(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestSanitizeTemplateVariableName(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "Hash character",
			input:    "{#1}FINANCE:TICKER",
			expected: "num1FINANCE:TICKER",
		},
		{
			name:     "Dollar character",
			input:    "{$1}FINANCE:TICKER",
			expected: "var1FINANCE:TICKER",
		},
		{
			name:     "Braces",
			input:    "{prefix}FINANCE:TICKER",
			expected: "prefixFINANCE:TICKER",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := SanitizeTemplateVariableName(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// This test specifically tests the scenario described in the issue
func TestPrefixedVariableReplacement(t *testing.T) {
	// Create a template processor
	processor := NewTemplateProcessor()
	
	// Test template with prefixed variables
	templateText := `
**Stock 1:**
*   Ticker: **{{{#1}FINANCE:TICKER}}**
*   Company Name: **{{{#1}FINANCE:COMPANY_NAME}}**

**Stock 2:**
*   Ticker: **{{{#2}FINANCE:TICKER}}**
*   Company Name: **{{{#2}FINANCE:COMPANY_NAME}}**
`

	// Variables with prefixed keys
	variables := map[string]interface{}{
		"{#1}FINANCE:TICKER":       "AAPL",
		"{#1}FINANCE:COMPANY_NAME": "Apple Inc.",
		"{#2}FINANCE:TICKER":       "MSFT",
		"{#2}FINANCE:COMPANY_NAME": "Microsoft Corporation",
	}
	
	expected := `
**Stock 1:**
*   Ticker: **AAPL**
*   Company Name: **Apple Inc.**

**Stock 2:**
*   Ticker: **MSFT**
*   Company Name: **Microsoft Corporation**
`
	
	// Debug output
	t.Logf("Template text: %s", templateText)
	
	// Extract variables from template
	extractedVars := processor.ExtractVariables(templateText)
	t.Logf("Extracted variables: %v", extractedVars)
	
	// Transform template for debugging
	transformedTemplate, varMap := transformTemplate(templateText)
	t.Logf("Transformed template: %s", transformedTemplate)
	t.Logf("Variable mapping: %v", varMap)
	
	result, err := processor.ProcessTemplate(templateText, variables)
	if err != nil {
		t.Logf("Error processing template: %v", err)
	}
	t.Logf("Result: %s", result)
	
	assert.NoError(t, err)
	assert.Equal(t, expected, result)
}

// This test specifically tests the triple braces format with prefixed variables
func TestTripleBracePrefixedVariableReplacement(t *testing.T) {
	// Create a template processor
	processor := NewTemplateProcessor()
	
	// Test template with triple braces prefixed variables - note the triple braces
	templateText := `
**Stock 1:**
*   Ticker: **{{{#1}FINANCE:TICKER}}**
*   Company Name: **{{{#1}FINANCE:COMPANY_NAME}}**

**Stock 2:**
*   Ticker: **{{{#2}FINANCE:TICKER}}**
*   Company Name: **{{{#2}FINANCE:COMPANY_NAME}}**
`

	// Variables with prefixed keys - exactly as they would be sent by client
	variables := map[string]interface{}{
		"{#1}FINANCE:TICKER":       "AAPL",
		"{#1}FINANCE:COMPANY_NAME": "Apple Inc.",
		"{#2}FINANCE:TICKER":       "MSFT",
		"{#2}FINANCE:COMPANY_NAME": "Microsoft Corporation",
	}
	
	expected := `
**Stock 1:**
*   Ticker: **AAPL**
*   Company Name: **Apple Inc.**

**Stock 2:**
*   Ticker: **MSFT**
*   Company Name: **Microsoft Corporation**
`
	
	// Debug the template text
	t.Logf("\nTemplate text:\n%s", templateText)
	
	// Debug the regex pattern used in the template processor
	re := regexp.MustCompile(`{{(\{[#$][a-zA-Z0-9]+\})?([A-Z_]+(?::[A-Z0-9_]+)?|\w+)}}`)
	matches := re.FindAllStringSubmatch(templateText, -1)
	t.Logf("\nRegex matches for standard pattern:\n%v", matches)
	
	// Try a modified regex that might handle triple braces
	tripleRe := regexp.MustCompile(`\{\{\{([#$][a-zA-Z0-9]+)\}([A-Z_]+(?::[A-Z0-9_]+)?|\w+)\}\}\}`)
	tripleMatches := tripleRe.FindAllStringSubmatch(templateText, -1)
	t.Logf("\nRegex matches for triple brace pattern:\n%v", tripleMatches)
	
	// Extract variables from template
	extractedVars := processor.ExtractVariables(templateText)
	t.Logf("\nExtracted variables:\n%v", extractedVars)
	
	// Transform template for debugging
	transformedTemplate, varMap := transformTemplate(templateText)
	t.Logf("\nTransformed template:\n%s", transformedTemplate)
	t.Logf("\nVariable mapping:\n%v", varMap)
	
	// Process the template
	result, err := processor.ProcessTemplate(templateText, variables)
	t.Logf("\nTemplate processing result:\n%s", result)
	
	assert.NoError(t, err)
	assert.Equal(t, expected, result)
}
