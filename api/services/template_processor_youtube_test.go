package services

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockTranscriptService is a mock implementation of the TranscriptService
type MockTranscriptServiceForTemplate struct {
	mock.Mock
}

func (m *MockTranscriptServiceForTemplate) GetTranscript(videoURL, lang, country string) ([]TranscriptData, string, error) {
	args := m.Called(videoURL, lang, country)
	return args.Get(0).([]TranscriptData), args.String(1), args.Error(2)
}

func (m *MockTranscriptServiceForTemplate) GetFullTranscriptText(videoURL, lang, country string) (string, error) {
	args := m.Called(videoURL, lang, country)
	return args.String(0), args.Error(1)
}

func (m *MockTranscriptServiceForTemplate) CleanCache() {
	m.Called()
}

func TestProcessYouTubeTranscriptVariables(t *testing.T) {
	// Create a mock transcript service
	mockService := new(MockTranscriptServiceForTemplate)

	// Create a template processor with the mock service
	processor := &TemplateProcessor{
		transcriptService: mockService,
	}

	// Test case 1: Only YouTube URL variable present - now we expect transcript to be auto-added
	variables := map[string]interface{}{
		"MEDIA:YOUTUBE_URL": "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
	}

	// Set up expectations for the mock - now we expect a call to get the transcript
	mockService.On("GetFullTranscriptText", "https://www.youtube.com/watch?v=dQw4w9WgXcQ", "en", "US").
		Return("Auto-generated transcript", nil).Once()

	err := processor.ProcessYouTubeTranscriptVariables(variables)
	assert.NoError(t, err)
	// Now we expect the transcript variable to have been added
	assert.Contains(t, variables, "MEDIA:YOUTUBE_TRANSCRIPT")
	assert.Equal(t, "Auto-generated transcript", variables["MEDIA:YOUTUBE_TRANSCRIPT"])

	// Test case 2: YouTube transcript variable but no URL
	variables = map[string]interface{}{
		"MEDIA:YOUTUBE_TRANSCRIPT": "",
	}

	err = processor.ProcessYouTubeTranscriptVariables(variables)
	assert.NoError(t, err)                                                   // No error is returned now
	assert.Contains(t, variables["MEDIA:YOUTUBE_TRANSCRIPT"], "missing URL") // Placeholder message is set

	// Test case 3: Both variables present, successful transcript fetch
	variables = map[string]interface{}{
		"MEDIA:YOUTUBE_URL":        "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
		"MEDIA:YOUTUBE_TRANSCRIPT": "",
	}

	// Set up expectations on the mock for the second call
	mockService.On("GetFullTranscriptText", "https://www.youtube.com/watch?v=dQw4w9WgXcQ", "en", "US").
		Return("This is a test transcript", nil).Once()

	err = processor.ProcessYouTubeTranscriptVariables(variables)
	assert.NoError(t, err)
	assert.Equal(t, "This is a test transcript", variables["MEDIA:YOUTUBE_TRANSCRIPT"])

	// Verify that the mock was called as expected
	mockService.AssertExpectations(t)
}

func TestProcessTemplateWithYouTubeTranscript(t *testing.T) {
	// Create a mock transcript service
	mockService := new(MockTranscriptServiceForTemplate)

	// Create a template processor with the mock service
	processor := &TemplateProcessor{
		transcriptService: mockService,
	}

	// Set up expectations on the mock - using Once() to ensure each call is tracked properly
	mockService.On("GetFullTranscriptText", "https://www.youtube.com/watch?v=dQw4w9WgXcQ", "en", "US").
		Return("This is a test transcript", nil).Once()

	// Test case 1: Template with both URL and transcript variables, but only URL value provided
	// The processor should automatically fetch the transcript
	templateText := "Video URL: {{MEDIA:YOUTUBE_URL}}\nTranscript: {{MEDIA:YOUTUBE_TRANSCRIPT}}"
	variables := map[string]interface{}{
		"MEDIA:YOUTUBE_URL": "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
		// Note: We're not including MEDIA:YOUTUBE_TRANSCRIPT in variables map to test auto-addition
	}

	result, err := processor.ProcessTemplate(templateText, variables)
	assert.NoError(t, err)
	assert.Contains(t, result, "Video URL: https://www.youtube.com/watch?v=dQw4w9WgXcQ")
	assert.Contains(t, result, "Transcript: This is a test transcript")

	// Test case 2: Just the URL provided in a template with both variables
	mockService.On("GetFullTranscriptText", "https://www.youtube.com/watch?v=dQw4w9WgXcQ", "en", "US").
		Return("Another test transcript", nil).Once()

	templateText2 := "Video with transcript: {{MEDIA:YOUTUBE_URL}} - {{MEDIA:YOUTUBE_TRANSCRIPT}}"
	variables2 := map[string]interface{}{
		"MEDIA:YOUTUBE_URL": "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
	}

	result2, err := processor.ProcessTemplate(templateText2, variables2)
	assert.NoError(t, err)
	assert.Contains(t, result2, "Video with transcript: https://www.youtube.com/watch?v=dQw4w9WgXcQ")
	assert.Contains(t, result2, "Another test transcript")

	// Verify that the mock was called as expected
	mockService.AssertExpectations(t)
}
