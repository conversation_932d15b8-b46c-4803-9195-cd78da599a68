package websocket

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/gorilla/websocket"
	"promz.ai/api/services"
)

// PopularityService defines the interface for popularity-related operations
type PopularityService interface {
	GetPopularPrompts(ctx context.Context, limit int) ([]map[string]interface{}, error)
}

// WebSocketHandler manages WebSocket connections
type WebSocketHandler struct {
	// Map of user ID to connections
	userConnections      map[string]map[string]*WebSocketConnection
	userConnectionsMutex sync.RWMutex

	// Map of connection ID to user ID
	connectionUsers      map[string]string
	connectionUsersMutex sync.RWMutex

	// Map of topic subscriptions
	topicSubscriptions      map[string]map[string]bool
	topicSubscriptionsMutex sync.RWMutex

	// WebSocket upgrader
	upgrader websocket.Upgrader

	// Service for popularity-related operations
	popularityService PopularityService
}

// WebSocketConnection represents a WebSocket connection
type WebSocketConnection struct {
	ID        string
	UserID    string
	Conn      *websocket.Conn
	Send      chan []byte
	Topics    map[string]bool
	CreatedAt time.Time
}

// WebSocketMessage represents a message sent over WebSocket
type WebSocketMessage struct {
	Type      string          `json:"type"`
	Action    string          `json:"action"`
	Topic     string          `json:"topic,omitempty"`
	Payload   json.RawMessage `json:"payload,omitempty"`
	MessageID string          `json:"message_id,omitempty"`
	RequestID string          `json:"request_id,omitempty"`
	Error     *ErrorInfo      `json:"error,omitempty"`
}

// ErrorInfo contains error details
type ErrorInfo struct {
	Code    string            `json:"code"`
	Message string            `json:"message"`
	Details map[string]string `json:"details,omitempty"`
}

// NewWebSocketHandler creates a new WebSocket handler
func NewWebSocketHandler(popularityService PopularityService) *WebSocketHandler {
	return &WebSocketHandler{
		userConnections:    make(map[string]map[string]*WebSocketConnection),
		connectionUsers:    make(map[string]string),
		topicSubscriptions: make(map[string]map[string]bool),
		upgrader: websocket.Upgrader{
			ReadBufferSize:  1024,
			WriteBufferSize: 1024,
			CheckOrigin: func(r *http.Request) bool {
				return true // Allow all origins in development
			},
		},
		popularityService: popularityService, // Add popularity service
	}
}

// HandleConnection handles a new WebSocket connection
func (h *WebSocketHandler) HandleConnection(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		log.Printf("Unauthorized: missing user ID")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized: missing user ID"})
		return
	}

	userIDStr, ok := userID.(string)
	if !ok || userIDStr == "" {
		log.Printf("Unauthorized: invalid user ID")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized: invalid user ID"})
		return
	}

	// Upgrade HTTP connection to WebSocket
	conn, err := h.upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		log.Printf("Error upgrading connection: %v", err)
		return
	}

	// Create connection ID
	connectionID := uuid.New().String()

	// Create WebSocket connection
	wsConn := &WebSocketConnection{
		ID:        connectionID,
		UserID:    userIDStr,
		Conn:      conn,
		Send:      make(chan []byte, 256),
		Topics:    make(map[string]bool),
		CreatedAt: time.Now(),
	}

	// Register connection
	h.registerConnection(wsConn)

	// Start goroutines for reading and writing
	go h.readPump(wsConn)
	go h.writePump(wsConn)

	log.Printf("WebSocket connection established: id=%s, user=%s", connectionID, userIDStr)
}

// registerConnection registers a new WebSocket connection
func (h *WebSocketHandler) registerConnection(conn *WebSocketConnection) {
	// Add to user connections
	h.userConnectionsMutex.Lock()
	if _, exists := h.userConnections[conn.UserID]; !exists {
		h.userConnections[conn.UserID] = make(map[string]*WebSocketConnection)
	}
	h.userConnections[conn.UserID][conn.ID] = conn
	h.userConnectionsMutex.Unlock()

	// Add to connection users
	h.connectionUsersMutex.Lock()
	h.connectionUsers[conn.ID] = conn.UserID
	h.connectionUsersMutex.Unlock()
}

// unregisterConnection unregisters a WebSocket connection
func (h *WebSocketHandler) unregisterConnection(conn *WebSocketConnection) {
	// Remove from topic subscriptions
	h.topicSubscriptionsMutex.Lock()
	for topic := range conn.Topics {
		if subs, exists := h.topicSubscriptions[topic]; exists {
			delete(subs, conn.ID)
			if len(subs) == 0 {
				delete(h.topicSubscriptions, topic)
			}
		}
	}
	h.topicSubscriptionsMutex.Unlock()

	// Remove from user connections
	h.userConnectionsMutex.Lock()
	if conns, exists := h.userConnections[conn.UserID]; exists {
		delete(conns, conn.ID)
		if len(conns) == 0 {
			delete(h.userConnections, conn.UserID)
		}
	}
	h.userConnectionsMutex.Unlock()

	// Remove from connection users
	h.connectionUsersMutex.Lock()
	delete(h.connectionUsers, conn.ID)
	h.connectionUsersMutex.Unlock()

	// Close connection
	conn.Conn.Close()
	close(conn.Send)

	log.Printf("WebSocket connection closed: id=%s, user=%s", conn.ID, conn.UserID)
}

// readPump pumps messages from the WebSocket connection to the hub
func (h *WebSocketHandler) readPump(conn *WebSocketConnection) {
	defer func() {
		h.unregisterConnection(conn)
	}()

	// Set read deadline
	conn.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))
	conn.Conn.SetPongHandler(func(string) error {
		conn.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		return nil
	})

	// Read messages
	for {
		_, message, err := conn.Conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("Error reading message: %v", err)
			}
			break
		}

		// Parse message
		var wsMessage WebSocketMessage
		if err := json.Unmarshal(message, &wsMessage); err != nil {
			log.Printf("Error parsing message: %v", err)
			h.sendErrorMessage(conn, "", "invalid_message", "Invalid message format", nil)
			continue
		}

		// Handle message based on type and action
		h.handleMessage(conn, &wsMessage)
	}
}

// writePump pumps messages from the hub to the WebSocket connection
func (h *WebSocketHandler) writePump(conn *WebSocketConnection) {
	ticker := time.NewTicker(30 * time.Second)
	defer func() {
		ticker.Stop()
		conn.Conn.Close()
	}()

	for {
		select {
		case message, ok := <-conn.Send:
			conn.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if !ok {
				// Channel closed
				conn.Conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			w, err := conn.Conn.NextWriter(websocket.TextMessage)
			if err != nil {
				return
			}
			w.Write(message)

			// Add queued messages
			n := len(conn.Send)
			for i := 0; i < n; i++ {
				w.Write([]byte{'\n'})
				w.Write(<-conn.Send)
			}

			if err := w.Close(); err != nil {
				return
			}
		case <-ticker.C:
			conn.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := conn.Conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// handleMessage handles a WebSocket message
func (h *WebSocketHandler) handleMessage(conn *WebSocketConnection, msg *WebSocketMessage) {
	switch msg.Type {
	case "subscription":
		switch msg.Action {
		case "subscribe":
			h.handleSubscribe(conn, msg)
		case "unsubscribe":
			h.handleUnsubscribe(conn, msg)
		default:
			h.sendErrorMessage(conn, msg.RequestID, "invalid_action", "Invalid subscription action", nil)
		}
	case "ping":
		h.handlePing(conn, msg)
	case "request": // Add handling for request type
		switch msg.Topic {
		case "popular_prompts":
			h.handlePopularPromptsRequest(conn, msg)
		default:
			h.sendErrorMessage(conn, msg.RequestID, "invalid_topic", "Invalid request topic", nil)
		}
	default:
		h.sendErrorMessage(conn, msg.RequestID, "invalid_type", "Invalid message type", nil)
	}
}

// handleSubscribe handles a subscription request
func (h *WebSocketHandler) handleSubscribe(conn *WebSocketConnection, msg *WebSocketMessage) {
	if msg.Topic == "" {
		h.sendErrorMessage(conn, msg.RequestID, "invalid_topic", "Topic is required", nil)
		return
	}

	// Check if user is authorized to subscribe to this topic
	if !h.isAuthorizedForTopic(conn.UserID, msg.Topic) {
		h.sendErrorMessage(conn, msg.RequestID, "unauthorized", "Not authorized for this topic", nil)
		return
	}

	// Add to topic subscriptions
	h.topicSubscriptionsMutex.Lock()
	if _, exists := h.topicSubscriptions[msg.Topic]; !exists {
		h.topicSubscriptions[msg.Topic] = make(map[string]bool)
	}
	h.topicSubscriptions[msg.Topic][conn.ID] = true
	h.topicSubscriptionsMutex.Unlock()

	// Add to connection topics
	conn.Topics[msg.Topic] = true

	// Send success response
	response := WebSocketMessage{
		Type:      "subscription",
		Action:    "subscribed",
		Topic:     msg.Topic,
		RequestID: msg.RequestID,
		MessageID: uuid.New().String(),
	}

	responseJSON, _ := json.Marshal(response)
	conn.Send <- responseJSON

	log.Printf("Subscribed to topic: user=%s, topic=%s", conn.UserID, msg.Topic)
}

// handleUnsubscribe handles an unsubscription request
func (h *WebSocketHandler) handleUnsubscribe(conn *WebSocketConnection, msg *WebSocketMessage) {
	if msg.Topic == "" {
		h.sendErrorMessage(conn, msg.RequestID, "invalid_topic", "Topic is required", nil)
		return
	}

	// Remove from topic subscriptions
	h.topicSubscriptionsMutex.Lock()
	if subs, exists := h.topicSubscriptions[msg.Topic]; exists {
		delete(subs, conn.ID)
		if len(subs) == 0 {
			delete(h.topicSubscriptions, msg.Topic)
		}
	}
	h.topicSubscriptionsMutex.Unlock()

	// Remove from connection topics
	delete(conn.Topics, msg.Topic)

	// Send success response
	response := WebSocketMessage{
		Type:      "subscription",
		Action:    "unsubscribed",
		Topic:     msg.Topic,
		RequestID: msg.RequestID,
		MessageID: uuid.New().String(),
	}

	responseJSON, _ := json.Marshal(response)
	conn.Send <- responseJSON

	log.Printf("Unsubscribed from topic: user=%s, topic=%s", conn.UserID, msg.Topic)
}

// handlePing handles a ping message
func (h *WebSocketHandler) handlePing(conn *WebSocketConnection, msg *WebSocketMessage) {
	// Send pong response
	response := WebSocketMessage{
		Type:      "pong",
		RequestID: msg.RequestID,
		MessageID: uuid.New().String(),
	}

	responseJSON, _ := json.Marshal(response)
	conn.Send <- responseJSON
}

// handlePopularPromptsRequest handles a request for popular prompts
func (h *WebSocketHandler) handlePopularPromptsRequest(conn *WebSocketConnection, msg *WebSocketMessage) {
	// Extract request parameters
	var limit int = 10 // Default limit
	var requestPayload map[string]interface{}

	if len(msg.Payload) > 0 {
		if err := json.Unmarshal(msg.Payload, &requestPayload); err == nil {
			if l, ok := requestPayload["limit"].(float64); ok {
				limit = int(l)
			}
		} else {
			log.Printf("Error unmarshaling popular_prompts payload: %v", err)
		}
	}

	// Get popular prompts from the service
	popularPrompts, err := h.getPopularPrompts(conn.UserID, limit)
	if err != nil {
		log.Printf("Error getting popular prompts: %v", err)
		h.sendErrorMessage(conn, msg.RequestID, "internal_error", "Failed to retrieve popular prompts", nil)
		return
	}

	// Prepare response payload
	responsePayload := map[string]interface{}{
		"popular_prompts": popularPrompts,
	}
	responsePayloadJSON, err := json.Marshal(responsePayload)
	if err != nil {
		log.Printf("Error marshaling popular prompts response payload: %v", err)
		h.sendErrorMessage(conn, msg.RequestID, "internal_error", "Failed to prepare response", nil)
		return
	}

	// Send the response directly to this client
	response := WebSocketMessage{
		Type:      "message",
		Topic:     "popular_prompts",
		Payload:   responsePayloadJSON,
		RequestID: msg.RequestID,
		MessageID: uuid.New().String(),
	}

	responseData, err := json.Marshal(response)
	if err != nil {
		log.Printf("Error marshaling popular prompts response: %v", err)
		return
	}

	select {
	case conn.Send <- responseData:
		log.Printf("Sent popular prompts response to client: userID=%s, connID=%s", conn.UserID, conn.ID)
	default:
		log.Printf("Send buffer full for client %s, connID=%s when sending popular prompts", conn.UserID, conn.ID)
	}
}

// Make sure WebSocketHandler implements WebSocketBroadcaster interface
var _ services.WebSocketBroadcaster = (*WebSocketHandler)(nil)

// BroadcastToTopic broadcasts a message to all clients subscribed to a topic
// This method is added to satisfy the expected WebSocketBroadcaster interface
func (h *WebSocketHandler) BroadcastToTopic(topic string, payload interface{}) {
	if err := h.Broadcast(topic, payload); err != nil {
		log.Printf("Error broadcasting to topic %s: %v", topic, err)
	}
}

// Run starts the WebSocketHandler's main processing loops
// This is required to match the interface expected by router.go
func (h *WebSocketHandler) Run() {
	log.Println("WebSocket handler running")
	// This handler doesn't need an active run loop since the actual handling
	// is done in the readPump and writePump goroutines for each connection

	// Create a channel to block forever to keep the service running
	blockForever := make(chan struct{})
	<-blockForever
}

// getPopularPrompts retrieves popular prompts using the popularity service
func (h *WebSocketHandler) getPopularPrompts(userID string, limit int) ([]map[string]interface{}, error) {
	ctx := context.Background()

	if h.popularityService != nil {
		prompts, err := h.popularityService.GetPopularPrompts(ctx, limit)
		if err != nil {
			return nil, fmt.Errorf("error calling popularity service: %w", err)
		}
		if prompts == nil {
			return []map[string]interface{}{}, nil
		}
		return prompts, nil
	}

	log.Printf("Popularity service not configured, returning empty popular prompts for user %s", userID)
	return []map[string]interface{}{}, nil
}

// sendErrorMessage sends an error message to a connection
func (h *WebSocketHandler) sendErrorMessage(conn *WebSocketConnection, requestID, code, message string, details map[string]string) {
	errorMsg := WebSocketMessage{
		Type:      "error",
		RequestID: requestID,
		MessageID: uuid.New().String(),
		Error: &ErrorInfo{
			Code:    code,
			Message: message,
			Details: details,
		},
	}

	errorJSON, _ := json.Marshal(errorMsg)
	conn.Send <- errorJSON
}

// SendToUser sends a message to a specific user
func (h *WebSocketHandler) SendToUser(userID, topic string, message interface{}) error {
	// Marshal message to JSON
	payload, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("error marshaling message: %v", err)
	}

	// Create WebSocket message
	wsMessage := WebSocketMessage{
		Type:      "message",
		Topic:     topic,
		Payload:   payload,
		MessageID: uuid.New().String(),
	}

	wsMessageJSON, err := json.Marshal(wsMessage)
	if err != nil {
		return fmt.Errorf("error marshaling WebSocket message: %v", err)
	}

	// Get user connections
	h.userConnectionsMutex.RLock()
	conns, exists := h.userConnections[userID]
	h.userConnectionsMutex.RUnlock()

	if !exists || len(conns) == 0 {
		return nil // User has no active connections
	}

	// Send to all user connections subscribed to the topic
	for _, conn := range conns {
		if _, subscribed := conn.Topics[topic]; subscribed {
			select {
			case conn.Send <- wsMessageJSON:
				// Message sent
			default:
				// Buffer full, close connection
				h.unregisterConnection(conn)
			}
		}
	}

	return nil
}

// Broadcast sends a message to all connections subscribed to a topic
func (h *WebSocketHandler) Broadcast(topic string, message interface{}) error {
	// Marshal message to JSON
	payload, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("error marshaling message: %v", err)
	}

	// Create WebSocket message
	wsMessage := WebSocketMessage{
		Type:      "message",
		Topic:     topic,
		Payload:   payload,
		MessageID: uuid.New().String(),
	}

	wsMessageJSON, err := json.Marshal(wsMessage)
	if err != nil {
		return fmt.Errorf("error marshaling WebSocket message: %v", err)
	}

	// Get topic subscriptions
	h.topicSubscriptionsMutex.RLock()
	subs, exists := h.topicSubscriptions[topic]
	h.topicSubscriptionsMutex.RUnlock()

	if !exists || len(subs) == 0 {
		return nil // No subscriptions for this topic
	}

	// Get connection IDs
	connIDs := make([]string, 0, len(subs))
	for connID := range subs {
		connIDs = append(connIDs, connID)
	}

	// Send to all connections
	for _, connID := range connIDs {
		// Get user ID
		h.connectionUsersMutex.RLock()
		userID, exists := h.connectionUsers[connID]
		h.connectionUsersMutex.RUnlock()

		if !exists {
			continue
		}

		// Get connection
		h.userConnectionsMutex.RLock()
		userConns, exists := h.userConnections[userID]
		if !exists {
			h.userConnectionsMutex.RUnlock()
			continue
		}

		conn, exists := userConns[connID]
		h.userConnectionsMutex.RUnlock()

		if !exists {
			continue
		}

		// Send message
		select {
		case conn.Send <- wsMessageJSON:
			// Message sent
		default:
			// Buffer full, close connection
			h.unregisterConnection(conn)
		}
	}

	return nil
}

// isAuthorizedForTopic checks if a user is authorized to subscribe to a topic
func (h *WebSocketHandler) isAuthorizedForTopic(userID, topic string) bool {
	// Check if it's a user-specific topic
	if len(topic) > 0 && topic[0] == '@' {
		// Topic format: @user_id/topic_name
		parts := splitTopic(topic)
		if len(parts) >= 2 && parts[0] == userID {
			return true
		}
		return false
	}

	// For file processing topics, check if the user owns the file
	if len(topic) > 15 && topic[:15] == "file_processing_" {
		// In a real implementation, we would check if the user owns this file using the fileID
		// fileID := topic[15:]
		// For now, we'll just allow it
		return true
	}

	// Public topics that anyone can subscribe to
	publicTopics := map[string]bool{
		"popular_prompts":      true,
		"system_announcements": true,
	}

	return publicTopics[topic]
}

// splitTopic splits a topic into parts
func splitTopic(topic string) []string {
	if len(topic) == 0 || topic[0] != '@' {
		return []string{topic}
	}

	// Remove @ prefix
	topic = topic[1:]

	// Split by /
	parts := make([]string, 0)
	start := 0
	for i := 0; i < len(topic); i++ {
		if topic[i] == '/' {
			if i > start {
				parts = append(parts, topic[start:i])
			}
			start = i + 1
		}
	}

	if start < len(topic) {
		parts = append(parts, topic[start:])
	}

	return parts
}

// CleanupInactiveConnections removes inactive connections
func (h *WebSocketHandler) CleanupInactiveConnections(ctx context.Context) {
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			h.cleanupConnections()
		}
	}
}

// cleanupConnections removes inactive connections
func (h *WebSocketHandler) cleanupConnections() {
	now := time.Now()
	inactiveThreshold := 24 * time.Hour

	// Get all connections
	var connsToRemove []*WebSocketConnection

	h.userConnectionsMutex.RLock()
	for _, userConns := range h.userConnections {
		for _, conn := range userConns {
			if now.Sub(conn.CreatedAt) > inactiveThreshold {
				connsToRemove = append(connsToRemove, conn)
			}
		}
	}
	h.userConnectionsMutex.RUnlock()

	// Remove inactive connections
	for _, conn := range connsToRemove {
		h.unregisterConnection(conn)
	}

	if len(connsToRemove) > 0 {
		log.Printf("Cleaned up %d inactive connections", len(connsToRemove))
	}
}

// RegisterRoutes registers the WebSocket handler routes
func (h *WebSocketHandler) RegisterRoutes(router *gin.RouterGroup) {
	router.GET("/ws", h.HandleConnection)
}
