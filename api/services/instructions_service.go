package services

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
)

// InstructionParams contains the configurable parameters for LLM execution
type InstructionParams struct {
	MaxTokens   int                    `json:"max_tokens,omitempty"`
	Temperature float32                `json:"temperature,omitempty"`
	Options     map[string]interface{} `json:"options,omitempty"`
}

// InstructionSet contains all parameters from instructions
type InstructionSet struct {
	InstructionText string
	Params          InstructionParams
	PromptID        string
	Provider        string // Preferred provider (OpenAI, Google, etc.)
	UsageNotes      string
}

// InstructionsService provides methods to fetch and process instructions
type InstructionsService struct {
	db *sql.DB
}

// NewInstructionsService creates a new instructions service
func NewInstructionsService(db *sql.DB) *InstructionsService {
	return &InstructionsService{
		db: db,
	}
}

// GetInstructions fetches instructions for a prompt
// It returns the instruction text for the given prompt
func (s *InstructionsService) GetInstructions(ctx context.Context, promptID string) (string, error) {
	// Maintain backward compatibility with the original interface
	instructionSet, err := s.GetInstructionSet(ctx, promptID)
	if err != nil {
		return "", err
	}

	return instructionSet.InstructionText, nil
}

// GetInstructionSet fetches comprehensive instruction data including parameters
func (s *InstructionsService) GetInstructionSet(ctx context.Context, promptID string) (*InstructionSet, error) {
	// Check if db is nil
	if s.db == nil {
		return nil, fmt.Errorf("database connection is nil")
	}

	if promptID == "" {
		return &InstructionSet{}, nil // No instructions needed
	}

	var result InstructionSet

	// Initialize prompt identifier in the result
	result.PromptID = promptID

	instructions, params, provider, usageNotes, err := s.getPromptInstructions(ctx, promptID)
	if err != nil {
		return nil, err
	}

	result.InstructionText = instructions
	result.Provider = provider
	result.UsageNotes = usageNotes

	// If we have parameters, parse and add them
	if params != "" {
		var instructionParams InstructionParams
		if err := json.Unmarshal([]byte(params), &instructionParams); err != nil {
			log.Printf("Warning: Failed to parse prompt parameters: %v", err)
		} else {
			result.Params = instructionParams
		}
	}

	return &result, nil
}

// getPromptInstructions fetches instructions specific to a prompt
func (s *InstructionsService) getPromptInstructions(ctx context.Context, promptID string) (
	instructions string, parameters string, provider string, usageNotes string, err error,
) {
	var providerNull, usageNotesNull sql.NullString

	query := `
		SELECT pi.instruction_template, pi.parameters, pi.provider, pi.usage_notes
		FROM prompt_instructions pi
		WHERE pi.prompt_id = $1
	`

	err = s.db.QueryRowContext(ctx, query, promptID).Scan(
		&instructions, &parameters, &providerNull, &usageNotesNull)

	if err == sql.ErrNoRows {
		return "", "", "", "", nil
	}
	if err != nil {
		return "", "", "", "", fmt.Errorf("failed to get prompt instructions: %w", err)
	}

	// Convert nullable strings to regular strings
	if providerNull.Valid {
		provider = providerNull.String
	}

	if usageNotesNull.Valid {
		usageNotes = usageNotesNull.String
	}

	return instructions, parameters, provider, usageNotes, nil
}
