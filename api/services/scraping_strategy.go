package services

import (
	"fmt"
	"io"
	"log"
	"net/http"
	"regexp"
	"strings"
	"time"
)

// ScrapingStrategy implements transcript retrieval using HTML scraping
type ScrapingStrategy struct {
	httpClient *http.Client
	userAgent  string
	name       string
}

// NewScrapingStrategy creates a new strategy using HTML scraping
func NewScrapingStrategy() *ScrapingStrategy {
	return &ScrapingStrategy{
		httpClient: &http.Client{Timeout: 10 * time.Second},
		userAgent:  "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
		name:       "Scraping",
	}
}

// GetName returns the name of this strategy
func (s *ScrapingStrategy) GetName() string {
	return s.name
}

// GetTranscript fetches a transcript using HTML scraping
func (s *ScrapingStrategy) GetTranscript(videoID, lang, country string) ([]TranscriptSegment, string, error) {
	// Construct the video URL (not used directly but useful for logging)
	_ = fmt.Sprintf("https://www.youtube.com/watch?v=%s", videoID)

	// Get the video title first
	title, err := s.getVideoTitle(videoID)
	if err != nil {
		log.Printf("Warning: Failed to get video title: %v", err)
		title = "YouTube Video: " + videoID
	}

	// Fetch the transcript using page parsing
	transcript, err := s.fetchTranscriptFromPage(videoID)
	if err != nil {
		return nil, title, err
	}

	return transcript, title, nil
}

// getVideoTitle tries to get the title of a YouTube video from its page
func (s *ScrapingStrategy) getVideoTitle(videoID string) (string, error) {
	videoURL := fmt.Sprintf("https://www.youtube.com/watch?v=%s", videoID)

	req, err := http.NewRequest("GET", videoURL, nil)
	if err != nil {
		return "", err
	}

	req.Header.Set("User-Agent", s.userAgent)

	resp, err := s.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("page returned status code %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	pageContent := string(body)

	// Extract title using regex
	titleRegex := regexp.MustCompile(`<meta\s+property="og:title"\s+content="([^"]+)"`)
	titleMatch := titleRegex.FindStringSubmatch(pageContent)

	if len(titleMatch) > 1 {
		return titleMatch[1], nil
	}

	// Fallback to another method if the first one fails
	titleRegex = regexp.MustCompile(`<title>([^<]+)</title>`)
	titleMatch = titleRegex.FindStringSubmatch(pageContent)

	if len(titleMatch) > 1 {
		// Remove " - YouTube" from the title if present
		title := titleMatch[1]
		title = strings.TrimSuffix(title, " - YouTube")
		return title, nil
	}

	return "YouTube Video: " + videoID, nil
}

// fetchTranscriptFromPage attempts to fetch transcript by parsing the video page
func (s *ScrapingStrategy) fetchTranscriptFromPage(videoID string) ([]TranscriptSegment, error) {
	// Fetch the video page
	videoURL := fmt.Sprintf("https://www.youtube.com/watch?v=%s", videoID)

	req, err := http.NewRequest("GET", videoURL, nil)
	if err != nil {
		return nil, err
	}

	req.Header.Set("User-Agent", s.userAgent)

	resp, err := s.httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("page returned status code %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	pageContent := string(body)

	// Try to find transcript data in the page
	// Look for captionTracks in the page
	captionRegex := regexp.MustCompile(`"captionTracks":\s*(\[.*?\])`)
	matches := captionRegex.FindStringSubmatch(pageContent)

	if len(matches) < 2 {
		return nil, fmt.Errorf("could not find caption tracks in page")
	}

	captionJSON := matches[1]

	// Extract the URL for transcript JSON
	urlRegex := regexp.MustCompile(`"baseUrl":\s*"(.*?)"`)
	urlMatches := urlRegex.FindStringSubmatch(captionJSON)

	if len(urlMatches) < 2 {
		return nil, fmt.Errorf("could not extract transcript URL")
	}

	// Replace escaped characters in the URL
	transcriptURL := strings.ReplaceAll(urlMatches[1], "\\u0026", "&")

	// Fetch the transcript JSON
	req, err = http.NewRequest("GET", transcriptURL, nil)
	if err != nil {
		return nil, err
	}

	req.Header.Set("User-Agent", s.userAgent)

	resp, err = s.httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("transcript URL returned status code %d", resp.StatusCode)
	}

	transcriptBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	// Parse the transcript XML
	segments := s.extractSegmentsFromXML(string(transcriptBody))
	if len(segments) == 0 {
		return nil, fmt.Errorf("failed to extract segments from transcript XML")
	}

	return segments, nil
}

// extractSegmentsFromXML extracts transcript segments from XML content
func (s *ScrapingStrategy) extractSegmentsFromXML(xmlContent string) []TranscriptSegment {
	var segments []TranscriptSegment

	// Extract text elements
	regex := regexp.MustCompile(`<text start="([\d.]+)" dur="([\d.]+)"[^>]*>(.*?)</text>`)
	matches := regex.FindAllStringSubmatch(xmlContent, -1)

	for _, match := range matches {
		if len(match) < 4 {
			continue
		}

		var startTime, duration float64
		fmt.Sscanf(match[1], "%f", &startTime)
		fmt.Sscanf(match[2], "%f", &duration)
		text := match[3]

		// Handle HTML entities and formatting
		text = strings.ReplaceAll(text, "&#39;", "'")
		text = strings.ReplaceAll(text, "&amp;", "&")
		text = strings.ReplaceAll(text, "&quot;", "\"")
		text = strings.ReplaceAll(text, "&lt;", "<")
		text = strings.ReplaceAll(text, "&gt;", ">")

		// Remove HTML tags
		htmlTagRegex := regexp.MustCompile(`<[^>]*>`)
		text = htmlTagRegex.ReplaceAllString(text, "")

		segment := TranscriptSegment{
			Text:     text,
			Start:    startTime,
			Duration: duration,
		}

		segments = append(segments, segment)
	}

	return segments
}
