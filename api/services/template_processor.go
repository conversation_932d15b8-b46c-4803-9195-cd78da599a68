package services

import (
	"bytes"
	"fmt"
	"log"
	"os"
	"strings"
	"text/template"
)

// FinancialDataServiceInterface defines the methods required for financial data operations
type FinancialDataServiceInterface interface {
	GetTickerInfo(symbol string) (*TickerInfo, error)
	EnrichFinancialVariables(variables map[string]interface{}) (map[string]interface{}, error)
}

// TemplateProcessor handles the processing of templates with variable substitution
type TemplateProcessor struct {
	financialService  FinancialDataServiceInterface
	transcriptService TranscriptServiceInterface
}

// NewTemplateProcessor creates a new template processor
func NewTemplateProcessor() *TemplateProcessor {
	return &TemplateProcessor{}
}

// NewTemplateProcessorWithFinancialService creates a new template processor with financial data service
func NewTemplateProcessorWithFinancialService(financialService FinancialDataServiceInterface) *TemplateProcessor {
	return &TemplateProcessor{
		financialService: financialService,
	}
}

// NewTemplateProcessorWithServices creates a new template processor with all services
func NewTemplateProcessorWithServices(financialService FinancialDataServiceInterface, transcriptService TranscriptServiceInterface) *TemplateProcessor {
	return &TemplateProcessor{
		financialService:  financialService,
		transcriptService: transcriptService,
	}
}

// SetTranscriptService sets the transcript service
func (p *TemplateProcessor) SetTranscriptService(transcriptService TranscriptServiceInterface) {
	p.transcriptService = transcriptService
}

// ProcessYouTubeTranscriptVariables processes YouTube transcript variables
func (p *TemplateProcessor) ProcessYouTubeTranscriptVariables(variables map[string]interface{}) error {
	log.Printf("DEBUG: ProcessYouTubeTranscriptVariables: Starting with %d variables", len(variables))

	// Check for case-insensitive matches for both YouTube transcript and URL variables
	transcriptVarKey := findVariableKey(variables, []string{"MEDIA:YOUTUBE_TRANSCRIPT", "YOUTUBE_TRANSCRIPT"})
	youtubeURLKey := findVariableKey(variables, []string{"MEDIA:YOUTUBE_URL", "YOUTUBE_URL"})

	// If no transcript variable is found, but we have a YouTube URL, check if we need to add the transcript variable
	// This happens when the template contains {{MEDIA:YOUTUBE_TRANSCRIPT}} but the variable wasn't provided
	if transcriptVarKey == "" && youtubeURLKey != "" {
		// First check if the URL is valid and not empty
		youtubeURL, ok := variables[youtubeURLKey].(string)
		if !ok || youtubeURL == "" {
			log.Printf("DEBUG: YouTube URL is present but empty or not a string, not adding transcript variable")
			return nil
		}

		// Add a placeholder for the transcript variable - it will be populated later if needed
		log.Printf("DEBUG: Adding MEDIA:YOUTUBE_TRANSCRIPT variable for URL: %s", youtubeURL)
		variables["MEDIA:YOUTUBE_TRANSCRIPT"] = ""
		transcriptVarKey = "MEDIA:YOUTUBE_TRANSCRIPT"
	}

	// If no transcript variable is found at all (even after our attempt to add one), return early
	if transcriptVarKey == "" {
		log.Printf("DEBUG: No YouTube transcript variable found")
		return nil
	}

	log.Printf("DEBUG: Found transcript variable with key: %s", transcriptVarKey)

	// Look for YouTube URL if not already found
	if youtubeURLKey == "" {
		youtubeURLKey = findVariableKey(variables, []string{"MEDIA:YOUTUBE_URL", "YOUTUBE_URL"})
	}

	// Helper function to set transcript message in variables
	setTranscriptMessage := func(message string) {
		variables[transcriptVarKey] = message
		variables["MEDIA:YOUTUBE_TRANSCRIPT"] = message // Ensure standardized key is set
	}

	// Check if we have a YouTube URL variable
	if youtubeURLKey == "" {
		log.Printf("WARNING: Found MEDIA:YOUTUBE_TRANSCRIPT variable but no MEDIA:YOUTUBE_URL")
		// Set placeholder message
		setTranscriptMessage("[YouTube transcript not available - missing URL]")
		return nil
	}

	// Extract the YouTube URL value
	youtubeURL, ok := variables[youtubeURLKey].(string)
	if !ok || youtubeURL == "" {
		log.Printf("WARNING: MEDIA:YOUTUBE_URL is present but empty or not a string")
		setTranscriptMessage("[YouTube transcript not available - invalid URL]")
		return nil
	}

	log.Printf("DEBUG: Found YouTube URL variable with key: %s, value: %s", youtubeURLKey, youtubeURL)

	// Check if transcript service is available
	if p.transcriptService == nil {
		log.Printf("ERROR: Transcript service is not available")
		setTranscriptMessage("[YouTube transcript service not available]")
		return nil
	}

	// Get the transcript
	log.Printf("DEBUG: Fetching transcript for URL: %v", youtubeURL)
	text, err := p.transcriptService.GetFullTranscriptText(youtubeURL, "en", "US")
	if err != nil {
		log.Printf("ERROR: Failed to get transcript: %v", err)
		setTranscriptMessage("[Error fetching YouTube transcript]")
		return nil
	}

	// Log transcript length for debugging
	log.Printf("DEBUG: Got transcript with length: %d characters", len(text))
	if len(text) > 100 {
		log.Printf("DEBUG: Transcript preview: %s...", text[:100])
	} else {
		log.Printf("DEBUG: Transcript: %s", text)
	}

	// Set the transcript variable using BOTH the original key and the standardized key
	setTranscriptMessage(text)
	log.Printf("DEBUG: Updated variables map with transcript")

	return nil
}

// ProcessTemplate substitutes variables in a template string
func (p *TemplateProcessor) ProcessTemplate(templateText string, variables map[string]interface{}) (string, error) {
	log.Printf("DEBUG: ProcessTemplate: Starting with template length %d and %d variables", len(templateText), len(variables))

	// Use debug utilities to log detailed information
	DebugTemplateText(templateText)
	DebugVariableProcessing(variables)

	if templateText == "" || variables == nil || len(variables) == 0 {
		log.Printf("ProcessTemplate: Empty template or no variables, returning original template")
		return templateText, nil
	}

	// Process any special variables that need preprocessing
	log.Printf("DEBUG: ProcessTemplate: Calling ProcessYouTubeTranscriptVariables")
	if err := p.ProcessYouTubeTranscriptVariables(variables); err != nil {
		log.Printf("Warning: Failed to process YouTube transcript variables: %v", err)
		// Continue with processing, as we want to be resilient to failures
	}

	// Check if the template has variable placeholders
	hasPlaceholders := hasVariablePlaceholders(templateText)
	log.Printf("DEBUG: ProcessTemplate: Template has placeholders: %v", hasPlaceholders)
	if !hasPlaceholders {
		log.Printf("ProcessTemplate: No variable placeholders found in template of length %d, returning original", len(templateText))
		return templateText, nil
	}

	// Enrich financial variables if financial service is available
	enrichedVariables := variables
	if p.financialService != nil {
		var err error
		enrichedVariables, err = p.financialService.EnrichFinancialVariables(variables)
		if err != nil {
			// Log the error but continue with original variables
			log.Printf("Error enriching financial variables: %v", err)
			enrichedVariables = variables
		}
	}

	// First, we need to transform the template to use dot notation instead of colons
	// This is because Go templates don't support colons in variable names
	log.Printf("DEBUG: ProcessTemplate: Transforming template")
	transformedTemplate, varMap := transformTemplate(templateText)

	// Log the variable mapping for debugging
	log.Printf("DEBUG: ProcessTemplate: Variable mapping created with %d entries", len(varMap))
	for originalVar, transformedVar := range varMap {
		log.Printf("DEBUG: Variable mapping: %s -> %s", originalVar, transformedVar)
	}

	// Create a template with custom delimiters
	log.Printf("DEBUG: ProcessTemplate: Parsing transformed template of length %d", len(transformedTemplate))
	tmpl, err := template.New("prompt").Parse(transformedTemplate)
	if err != nil {
		log.Printf("ERROR: Failed to parse template: %v", err)
		return "", fmt.Errorf("failed to parse template: %w", err)
	}

	// Create a map that maps the transformed variable names to their values
	log.Printf("DEBUG: ProcessTemplate: Creating template data map")
	templateData := make(map[string]interface{})
	for originalVar, transformedVar := range varMap {
		// Direct match for the original variable
		if value, exists := enrichedVariables[originalVar]; exists {
			log.Printf("DEBUG: Direct match found for %s -> %s", originalVar, transformedVar)
			templateData[transformedVar] = value
			continue
		}

		// If we couldn't find a direct match, look for variables with the same base name but different prefix
		baseName := ExtractBaseName(originalVar)

		// Only log this in debug mode
		if os.Getenv("PROMZ_DEBUG") == "true" {
			log.Printf("ProcessTemplate: No direct match for variable %s, looking for base name: %s", originalVar, baseName)
		}

		for enrichedKey, enrichedValue := range enrichedVariables {
			enrichedBaseName := ExtractBaseName(enrichedKey)

			if enrichedBaseName == baseName {
				// There might be multiple variables with the same base name but different prefixes
				// We only want to match the one with the correct prefix
				originalPrefix := ExtractPrefix(originalVar)
				enrichedPrefix := ExtractPrefix(enrichedKey)

				// Only log this in debug mode
				if os.Getenv("PROMZ_DEBUG") == "true" {
					log.Printf("ProcessTemplate: Base name match found. Original prefix: %s, Enriched prefix: %s", originalPrefix, enrichedPrefix)
				}

				if originalPrefix == enrichedPrefix {
					templateData[transformedVar] = enrichedValue
					break
				}
			}
		}
	}

	// Log the final template data for debugging
	log.Printf("DEBUG: ProcessTemplate: Final template data map has %d entries", len(templateData))
	for k, v := range templateData {
		valueStr := fmt.Sprintf("%v", v)
		if len(valueStr) > 50 {
			valueStr = valueStr[:50] + "..."
		}
		log.Printf("DEBUG: Template data: %s = %s", k, valueStr)
	}

	// Execute the template with the transformed variables
	log.Printf("DEBUG: ProcessTemplate: Executing template")
	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, templateData); err != nil {
		log.Printf("ERROR: Error executing template: %v", err)
		return "", fmt.Errorf("failed to execute template: %w", err)
	}

	result := buf.String()
	log.Printf("ProcessTemplate: Successfully processed template of length %d, result length: %d", len(templateText), len(result))
	return result, nil
}

// hasVariablePlaceholders checks if the template contains variable placeholders
func hasVariablePlaceholders(text string) bool {
	// Simple check for {{ and }} patterns or triple brace patterns {{{
	hasOpenBrace := strings.Contains(text, "{{") || strings.Contains(text, "{{{")
	hasCloseBrace := strings.Contains(text, "}}")
	log.Printf("DEBUG: hasVariablePlaceholders: text length: %d, has open brace: %v, has close brace: %v", len(text), hasOpenBrace, hasCloseBrace)

	// If we have both braces, do a more detailed check with regex
	if hasOpenBrace && hasCloseBrace {
		// Get all variable matches using shared regex patterns
		tripleMatches, doubleMatches := FindVariables(text)

		// Log results for debugging
		if len(tripleMatches) > 0 {
			log.Printf("DEBUG: hasVariablePlaceholders: Found %d triple brace matches", len(tripleMatches))
			for i, m := range tripleMatches {
				if len(m) > 0 {
					log.Printf("DEBUG: hasVariablePlaceholders: Triple brace match %d: %s", i+1, m[0])
				}
			}
		}

		if len(doubleMatches) > 0 {
			log.Printf("DEBUG: hasVariablePlaceholders: Found %d double brace matches", len(doubleMatches))
			for i, m := range doubleMatches {
				if len(m) > 0 {
					log.Printf("DEBUG: hasVariablePlaceholders: Double brace match %d: %s", i+1, m[0])
				}
			}
		}

		// Return true if we found any matches
		return len(tripleMatches) > 0 || len(doubleMatches) > 0
	}

	return hasOpenBrace && hasCloseBrace
}

// transformTemplate transforms a template with colon-based variable names to use
// dot notation which is compatible with Go's template engine
func transformTemplate(templateText string) (string, map[string]string) {
	log.Printf("DEBUG: transformTemplate: Starting with template of length %d", len(templateText))

	// Map to store original variable names to transformed names
	varMap := make(map[string]string)

	// First, try to match triple brace format {{{PREFIX}VAR}}
	tripleMatches := tripleRegex.FindAllStringSubmatch(templateText, -1)
	log.Printf("DEBUG: transformTemplate: Found %d triple brace variable placeholders", len(tripleMatches))
	for i, m := range tripleMatches {
		if len(m) > 2 {
			log.Printf("DEBUG: transformTemplate: Triple brace match %d: %s, prefix: %s, var: %s", i+1, m[0], m[1], m[2])
		}
	}

	// Then, match double brace format {{VAR}}
	doubleMatches := doubleRegex.FindAllString(templateText, -1)
	log.Printf("DEBUG: transformTemplate: Found %d double brace variable placeholders", len(doubleMatches))
	for i, m := range doubleMatches {
		log.Printf("DEBUG: transformTemplate: Double brace match %d: %s", i+1, m)
	}

	// Process triple brace format first
	transformedTemplate := tripleRegex.ReplaceAllStringFunc(templateText, func(match string) string {
		// Extract the prefix and variable name from the match
		submatches := tripleRegex.FindStringSubmatch(match)
		if len(submatches) < 3 {
			log.Printf("DEBUG: transformTemplate: Failed to extract submatches from triple brace: %s", match)
			return match // Return unchanged if we can't extract the parts
		}

		prefix := submatches[1]  // e.g., "#1"
		varName := submatches[2] // e.g., "FINANCE:TICKER"

		log.Printf("DEBUG: transformTemplate: Processing triple brace variable: prefix=%s, var=%s", prefix, varName)

		// Create the original variable name with prefix in braces
		originalVar := "{" + prefix + "}" + varName

		// Create a transformed name by replacing colons with underscores
		transformedName := "num" + prefix[1:] + "_" + strings.ReplaceAll(varName, ":", "_")
		transformedName = SanitizeTemplateVariableName(transformedName)

		log.Printf("DEBUG: transformTemplate: Triple brace mapping: '%s' -> '%s'", originalVar, transformedName)

		// Store the mapping
		varMap[originalVar] = transformedName

		// Return the transformed placeholder with dot notation
		return "{{." + transformedName + "}}"
	})

	// Log the regex pattern being used
	log.Printf("DEBUG: transformTemplate: Using double brace regex pattern: %s", DoubleBracePattern)

	// Find all double brace matches for logging
	matches := doubleRegex.FindAllString(transformedTemplate, -1)
	log.Printf("DEBUG: transformTemplate: Found %d double brace variable placeholders", len(matches))
	for i, m := range matches {
		log.Printf("DEBUG: transformTemplate: Match %d: %s", i+1, m)
	}

	// Transform the double brace format on the already transformed template
	transformedTemplate = doubleRegex.ReplaceAllStringFunc(transformedTemplate, func(match string) string {
		// Extract the variable name from the match (removing {{ and }})
		varContent := match[2 : len(match)-2]
		log.Printf("DEBUG: transformTemplate: Processing variable: %s", varContent)

		// Get prefix and base name
		prefix := ExtractPrefix(varContent)
		baseName := ExtractBaseName(varContent)
		log.Printf("DEBUG: transformTemplate: Extracted prefix: '%s', base name: '%s'", prefix, baseName)

		// Create a transformed name by replacing colons with underscores
		// Keep the prefix intact to maintain uniqueness between different prefixed variables
		transformedName := prefix + strings.ReplaceAll(baseName, ":", "_")
		log.Printf("DEBUG: transformTemplate: After colon replacement: '%s'", transformedName)

		// Sanitize the template variable name to be compatible with Go templates
		transformedName = SanitizeTemplateVariableName(transformedName)
		log.Printf("DEBUG: transformTemplate: After sanitization: '%s'", transformedName)

		// Store the mapping from original variable to transformed name
		varMap[varContent] = transformedName
		log.Printf("DEBUG: transformTemplate: Added mapping: '%s' -> '%s'", varContent, transformedName)

		// Return the transformed placeholder with dot notation for Go template
		result := "{{." + transformedName + "}}"
		log.Printf("DEBUG: transformTemplate: Transformed placeholder: '%s' -> '%s'", match, result)
		return result
	})

	log.Printf("DEBUG: transformTemplate: Transformation complete, created %d mappings", len(varMap))
	return transformedTemplate, varMap
}

// ExtractVariables extracts variable placeholders from a template
func (p *TemplateProcessor) ExtractVariables(templateText string) []string {
	if templateText == "" {
		return nil
	}

	// Use shared patterns to find all variables
	tripleMatches, doubleMatches := FindVariables(templateText)

	// Log the patterns used and matches found
	log.Printf("DEBUG: ExtractVariables: Found %d triple brace matches", len(tripleMatches))
	for i, m := range tripleMatches {
		if len(m) > 2 {
			log.Printf("DEBUG: ExtractVariables: Triple brace match %d: %s, prefix: %s, var: %s", i+1, m[0], m[1], m[2])
		}
	}

	log.Printf("DEBUG: ExtractVariables: Found %d double brace matches", len(doubleMatches))
	for i, m := range doubleMatches {
		if len(m) > 1 {
			log.Printf("DEBUG: ExtractVariables: Double brace match %d: %s, var: %s", i+1, m[0], m[1])
		}
	}

	// Extract variable names and deduplicate
	varMap := make(map[string]bool)

	// Process triple brace matches
	for _, match := range tripleMatches {
		if len(match) > 2 {
			// Triple brace format: {{{PREFIX}VAR}}
			// match[1] is the prefix (e.g., "#1")
			// match[2] is the variable name (e.g., "FINANCE:TICKER")
			varName := "{" + match[1] + "}" + match[2] // Format as {PREFIX}VAR
			log.Printf("DEBUG: ExtractVariables: Adding triple brace variable: %s", varName)
			varMap[varName] = true
		}
	}

	// Process double brace matches
	for _, match := range doubleMatches {
		if len(match) > 1 {
			// Simple double brace format: {{VAR}}
			// match[1] is the variable name (e.g., "FINANCE:TICKER" or "name")
			varName := match[1]
			log.Printf("DEBUG: ExtractVariables: Adding double brace variable: %s", varName)
			varMap[varName] = true
		}
	}

	// Convert map keys to slice
	vars := make([]string, 0, len(varMap))
	for v := range varMap {
		vars = append(vars, v)
	}

	log.Printf("DEBUG: ExtractVariables: Extracted %d unique variables: %v", len(vars), vars)
	return vars
}

// findVariableKey searches for a matching variable key in variables map using a list of possible names
// It checks both exact match and case-insensitive matches
func findVariableKey(variables map[string]interface{}, possibleNames []string) string {
	// First, try exact matches
	for _, name := range possibleNames {
		if _, exists := variables[name]; exists {
			return name
		}
	}

	// If exact match not found, try case-insensitive match
	for varKey := range variables {
		for _, name := range possibleNames {
			if strings.EqualFold(varKey, name) {
				log.Printf("DEBUG: Found case-insensitive match for variable: %s -> %s", name, varKey)
				return varKey
			}
		}
	}

	// No match found
	return ""
}

// ExtractBaseName extracts the base part of a variable name
// For example: "FINANCE:TICKER" -> "FINANCE:TICKER", "{#1}FINANCE:TICKER" -> "FINANCE:TICKER"
// This is needed to match variable names across different formats
