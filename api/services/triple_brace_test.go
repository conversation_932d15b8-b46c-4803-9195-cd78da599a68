package services

import (
	"fmt"
	"regexp"
	"testing"

	"github.com/stretchr/testify/assert"
)

// This test specifically focuses on the triple brace format issue
func TestTripleBraceFormat(t *testing.T) {
	// Example template with triple braces
	templateText := `
**Stock 1:**
*   Ticker: **{{{$1}FINANCE:TICKER}}**
*   Company Name: **{{{$1}FINANCE:COMPANY_NAME}}**

**Stock 2:**
*   Ticker: **{{{$2}FINANCE:TICKER}}**
*   Company Name: **{{{$2}FINANCE:COMPANY_NAME}}**
`

	// Print the template for reference
	fmt.Printf("Template:\n%s\n", templateText)

	// The current regex pattern used in transformTemplate function
	currentRegex := regexp.MustCompile(`{{(\{[#$][a-zA-Z0-9]+\})?([A-Z_]+(?::[A-Z0-9_]+)?|\w+)}}`)
	currentMatches := currentRegex.FindAllStringSubmatch(templateText, -1)
	fmt.Printf("\nCurrent regex matches: %v\n", currentMatches)

	// A proposed fix - modified regex that handles triple braces
	proposedRegex := regexp.MustCompile(`\{\{\{([#$][a-zA-Z0-9]+)\}([A-Z_]+(?::[A-Z0-9_]+)?|\w+)\}\}\}`)
	proposedMatches := proposedRegex.FindAllStringSubmatch(templateText, -1)
	fmt.Printf("\nProposed regex matches: %v\n", proposedMatches)

	// Test the actual template processing
	// Since we're in the same package, we can directly call the function
	processor := &TemplateProcessor{}
	
	// Variables with prefixed keys
	variables := map[string]interface{}{
		"{$1}FINANCE:TICKER":       "AAPL",
		"{$1}FINANCE:COMPANY_NAME": "Apple Inc.",
		"{$2}FINANCE:TICKER":       "MSFT",
		"{$2}FINANCE:COMPANY_NAME": "Microsoft Corporation",
	}
	
	expected := `
**Stock 1:**
*   Ticker: **AAPL**
*   Company Name: **Apple Inc.**

**Stock 2:**
*   Ticker: **MSFT**
*   Company Name: **Microsoft Corporation**
`
	
	// Process the template
	result, err := processor.ProcessTemplate(templateText, variables)
	fmt.Printf("\nTemplate processing result:\n%s\n", result)
	
	assert.NoError(t, err)
	assert.Equal(t, expected, result)
}
