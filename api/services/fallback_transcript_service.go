package services

import (
	"fmt"
	"log"
	"net/url"
	"strings"
	"sync"
	"time"
)

// TranscriptServiceStrategy represents a strategy for fetching transcripts
type TranscriptServiceStrategy interface {
	GetTranscript(videoID, lang, country string) ([]TranscriptSegment, string, error)
	GetName() string
}

// TranscriptSegment represents a segment returned by any strategy
type TranscriptSegment struct {
	Text     string
	Start    float64
	Duration float64
}

// TranscriptServiceConfig holds configuration for the transcript service
type TranscriptServiceConfig struct {
	APIKey                   string
	CacheTTL                 time.Duration
	EnableScraping           bool
	EnableOfficialAPI        bool
	EnableOfficialAPICaption bool // Flag to enable/disable official API for captions (requires OAuth2)
}

// NewDefaultTranscriptServiceConfig creates a default config
func NewDefaultTranscriptServiceConfig() *TranscriptServiceConfig {
	return &TranscriptServiceConfig{
		APIKey:                   "",
		CacheTTL:                 24 * time.Hour,
		EnableScraping:           true,
		EnableOfficialAPI:        true,
		EnableOfficialAPICaption: false, // Disabled until OAuth2 is implemented
	}
}

// FallbackTranscriptService implements the TranscriptServiceInterface with a strategy pattern
type FallbackTranscriptService struct {
	strategies []TranscriptServiceStrategy
	cache      map[string][]TranscriptData // In-memory cache
	mutex      sync.RWMutex
	cacheTTL   time.Duration // Time-to-live for cache entries
	lastClear  time.Time     // Last time cache was cleared
	config     *TranscriptServiceConfig
}

// NewFallbackTranscriptService creates a new transcript service with multiple strategies
func NewFallbackTranscriptService(config *TranscriptServiceConfig) (*FallbackTranscriptService, error) {
	if config == nil {
		config = NewDefaultTranscriptServiceConfig()
	}

	// Create strategies in order of preference
	var strategies []TranscriptServiceStrategy

	// Try to create official API strategy first (if enabled and key is provided)
	if config.EnableOfficialAPI && config.APIKey != "" {
		officialStrategy, err := NewOfficialAPIStrategy(config.APIKey)
		if err == nil {
			strategies = append(strategies, officialStrategy)
			log.Printf("INFO: Initialized YouTube Official API strategy")
		} else {
			log.Printf("WARNING: Failed to initialize YouTube API strategy: %v", err)
		}
	} else if config.EnableOfficialAPI && config.APIKey == "" {
		log.Printf("WARNING: Official YouTube API enabled but no API key provided")
	}

	// Always add scraping strategy as fallback if enabled
	if config.EnableScraping {
		strategies = append(strategies, NewScrapingStrategy())
		log.Printf("INFO: Initialized YouTube Scraping strategy")
	}

	if len(strategies) == 0 {
		return nil, fmt.Errorf("no transcript strategies available")
	}

	log.Printf("INFO: Created FallbackTranscriptService with %d strategies", len(strategies))

	return &FallbackTranscriptService{
		strategies: strategies,
		cache:      make(map[string][]TranscriptData),
		cacheTTL:   config.CacheTTL,
		lastClear:  time.Now(),
		config:     config,
	}, nil
}

// GetTranscript fetches a transcript for a YouTube video with caching and fallback
func (s *FallbackTranscriptService) GetTranscript(videoURL, lang, country string) ([]TranscriptData, string, error) {
	// Extract video ID
	videoID, err := ExtractVideoID(videoURL)
	if err != nil {
		return nil, "", err
	}

	// Check cache first
	s.mutex.RLock()
	if data, ok := s.cache[videoID]; ok {
		title, _ := s.getVideoTitle(videoID)
		s.mutex.RUnlock()
		log.Printf("INFO: Using cached transcript for video %s", videoID)
		return data, title, nil
	}
	s.mutex.RUnlock()

	// Try each strategy in order until one succeeds
	var lastError error
	for _, strategy := range s.strategies {
		log.Printf("INFO: Trying to fetch transcript using %s strategy", strategy.GetName())
		transcript, title, err := strategy.GetTranscript(videoID, lang, country)
		if err == nil {
			// Convert to our data structure
			var data []TranscriptData
			for _, seg := range transcript {
				data = append(data, TranscriptData{
					Text:     seg.Text,
					Duration: int(seg.Duration * 1000), // Convert to milliseconds
					Offset:   int(seg.Start * 1000),    // Convert to milliseconds
				})
			}

			// Cache the result
			s.mutex.Lock()
			s.cache[videoID] = data
			s.mutex.Unlock()

			log.Printf("INFO: Successfully fetched transcript using %s strategy", strategy.GetName())
			return data, title, nil
		}

		lastError = err
		log.Printf("WARNING: Strategy %s failed: %v", strategy.GetName(), err)
	}

	return nil, "", fmt.Errorf("all transcript strategies failed, last error: %v", lastError)
}

// GetFullTranscriptText returns the full transcript as a single string
func (s *FallbackTranscriptService) GetFullTranscriptText(videoURL, lang, country string) (string, error) {
	transcript, _, err := s.GetTranscript(videoURL, lang, country)
	if err != nil {
		log.Printf("Error getting transcript for %s: %v", videoURL, err)

		// Generate a simple fallback message when GetTranscript fails
		fallbackMsg := fmt.Sprintf(
			"[Transcript unavailable for video: %s. Error: %v]",
			videoURL, err,
		)
		return fallbackMsg, nil // Return message but no error to prevent processing failures
	}

	// Check if we have any transcript segments
	if len(transcript) == 0 {
		log.Printf("Warning: Empty transcript returned for %s", videoURL)
		return fmt.Sprintf("[No transcript content available for video: %s]", videoURL), nil
	}

	// Build the full text from transcript segments
	var fullText strings.Builder
	for _, t := range transcript {
		fullText.WriteString(t.Text)
		fullText.WriteString(" ")
	}

	return fullText.String(), nil
}

// CleanCache removes expired entries from the cache
func (s *FallbackTranscriptService) CleanCache() {
	if time.Since(s.lastClear) < s.cacheTTL {
		return
	}

	s.mutex.Lock()
	defer s.mutex.Unlock()

	s.cache = make(map[string][]TranscriptData)
	s.lastClear = time.Now()
	log.Printf("INFO: Cleared transcript cache")
}

// getVideoTitle is a helper that uses any available strategy to get the video title
func (s *FallbackTranscriptService) getVideoTitle(videoID string) (string, error) {
	for _, strategy := range s.strategies {
		// We call GetTranscript but only care about the title
		_, title, err := strategy.GetTranscript(videoID, "en", "US")
		if err == nil && title != "" {
			return title, nil
		}
	}
	return "YouTube Video: " + videoID, nil
}

// ExtractVideoID extracts the YouTube video ID from various URL formats
func ExtractVideoID(videoURL string) (string, error) {
	// Handle youtu.be URLs
	if strings.Contains(videoURL, "youtu.be/") {
		parts := strings.Split(videoURL, "youtu.be/")
		if len(parts) < 2 {
			return "", fmt.Errorf("invalid youtu.be URL format: %s", videoURL)
		}
		// Extract ID and remove any query parameters
		id := strings.Split(parts[1], "?")[0]
		return id, nil
	}

	// Handle youtube.com URLs
	parsedURL, err := url.Parse(videoURL)
	if err != nil {
		return "", err
	}

	// Handle youtube.com/watch?v= format
	if strings.Contains(videoURL, "youtube.com/watch") {
		vParam := parsedURL.Query().Get("v")
		if vParam == "" {
			return "", fmt.Errorf("no video ID found in URL: %s", videoURL)
		}
		return vParam, nil
	}

	// Handle youtube.com/embed/ format
	if strings.Contains(videoURL, "youtube.com/embed/") {
		parts := strings.Split(videoURL, "youtube.com/embed/")
		if len(parts) < 2 {
			return "", fmt.Errorf("invalid embed URL format: %s", videoURL)
		}
		// Extract ID and remove any query parameters
		id := strings.Split(parts[1], "?")[0]
		return id, nil
	}

	// Handle youtube.com/v/ format
	if strings.Contains(videoURL, "youtube.com/v/") {
		parts := strings.Split(videoURL, "youtube.com/v/")
		if len(parts) < 2 {
			return "", fmt.Errorf("invalid v URL format: %s", videoURL)
		}
		// Extract ID and remove any query parameters
		id := strings.Split(parts[1], "?")[0]
		return id, nil
	}

	return "", fmt.Errorf("unsupported YouTube URL format: %s", videoURL)
}
