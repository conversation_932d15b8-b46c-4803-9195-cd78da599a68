package services

import (
	"fmt"
	"log"
	"net/url"
	"os"
	"regexp"
	"sort"
	"strings"
	"time"
)

// FileNameGenerator generates suggested file names based on prompts and variables
type FileNameGenerator struct {
	// No database dependency needed
}

// NewFileNameGenerator creates a new file name generator
func NewFileNameGenerator() *FileNameGenerator {
	return &FileNameGenerator{}
}

// GenerateSuggestedFileName generates a file name based on the prompt execution
// Format: <YYYYMMDD>-<prompt-name>-<variable_summary>
//
// Example: 20250323-compare-two-stocks-AAPL-MSFT.pdf
//
// Notes:
// * The date is in YYYYMMDD format for international compatibility.
// * The prompt name is simplified to be safe for file names.
// * Variable summary includes key variables from the prompt.
//
// This format ensures a unique and descriptive file name that is safe for file systems.
func (g *FileNameGenerator) GenerateSuggestedFileName(
	promptContent string,
	variables map[string]interface{},
) (string, error) {
	// Log the incoming request
	log.Printf("FileNameGenerator: Generating file name for prompt with length %d and %d variables",
		len(promptContent), len(variables))

	// Print more details in debug mode
	if os.Getenv("PROMZ_DEBUG") == "true" {
		// Log a sample of the prompt content (first 50 chars)
		promptSample := promptContent
		if len(promptSample) > 50 {
			promptSample = promptSample[:50] + "..."
		}
		log.Printf("FileNameGenerator: Prompt sample: %s", promptSample)

		// Log variable keys
		var varKeys []string
		for k := range variables {
			varKeys = append(varKeys, k)
		}
		log.Printf("FileNameGenerator: Variable keys: %v", varKeys)
	}

	// Get current date in YYYYMMDD format
	dateStr := time.Now().Format("20060102")
	log.Printf("FileNameGenerator: Using date stamp: %s", dateStr)

	// Extract a simplified prompt name from the prompt content
	promptName := extractPromptName(promptContent)
	log.Printf("FileNameGenerator: Extracted prompt name: %s", promptName)

	// Extract important variables for the file name
	variableSummary := extractVariableSummary(variables)
	log.Printf("FileNameGenerator: Extracted variable summary: %s", variableSummary)

	// Use balanced length management approach
	targetLength := 80 // Target total length
	dateLength := len(dateStr)
	hyphenLength := 1 // Length of the hyphen separator

	// Calculate available space for prompt name and variables
	availableSpace := targetLength - dateLength - hyphenLength

	// Allocate space between prompt name and variables (40% to prompt, 60% to variables)
	promptNameSpace := availableSpace * 4 / 10
	variableSpace := availableSpace - promptNameSpace

	// Ensure minimum spaces
	if promptNameSpace < 10 {
		promptNameSpace = 10
		variableSpace = availableSpace - promptNameSpace
	}

	if variableSpace < 0 {
		variableSpace = 0
	}

	// Truncate prompt name if needed
	if len(promptName) > promptNameSpace {
		promptName = truncateText(promptName, promptNameSpace)
		log.Printf("FileNameGenerator: Truncated prompt name to fit space: %s", promptName)
	}

	// Truncate variable summary if needed
	if len(variableSummary) > variableSpace && variableSpace > 0 {
		variableSummary = truncateText(variableSummary, variableSpace)
		log.Printf("FileNameGenerator: Truncated variable summary to fit space: %s", variableSummary)
	}

	// Build the file name
	fileName := fmt.Sprintf("%s-%s", dateStr, promptName)
	log.Printf("FileNameGenerator: Initial file name: %s", fileName)

	if variableSummary != "" {
		fileName = fmt.Sprintf("%s-%s", fileName, variableSummary)
		log.Printf("FileNameGenerator: File name with variables: %s", fileName)
	}

	// Final safety check to ensure the file name isn't too long (max 100 characters)
	// This is to prevent issues with file systems that have length limitations
	maxTotalLen := 100
	if len(fileName) > maxTotalLen {
		log.Printf("FileNameGenerator: File name too long (%d chars), truncating to %d chars",
			len(fileName), maxTotalLen)

		// Keep the date and at least part of the prompt name, but truncate the rest
		dateWithHyphen := dateStr + "-"
		remaining := maxTotalLen - len(dateWithHyphen)
		if remaining > 0 {
			fileName = dateWithHyphen + fileName[len(dateWithHyphen):len(dateWithHyphen)+remaining]
		} else {
			fileName = dateStr
		}
		log.Printf("FileNameGenerator: Truncated file name: %s", fileName)
	}

	// Apply final sanitization to ensure a clean file name
	fileName = finalSanitize(fileName)
	log.Printf("FileNameGenerator: Final sanitized file name: %s", fileName)

	log.Printf("FileNameGenerator: Final file name: %s (length: %d)", fileName, len(fileName))
	return fileName, nil
}

// finalSanitize performs a final pass of sanitization on the complete file name
func finalSanitize(fileName string) string {
	// Ensure no double hyphens anywhere in the file name
	for strings.Contains(fileName, "--") {
		fileName = strings.ReplaceAll(fileName, "--", "-")
	}

	// Remove any remaining problematic characters
	unsafeChars := []string{
		",", ".", ";", "'", "`", "~", "!", "@", "#", "$",
		"%", "^", "&", "(", ")", "=", "+", "[", "]", "{", "}",
	}
	for _, char := range unsafeChars {
		fileName = strings.ReplaceAll(fileName, char, "-")
	}

	// Ensure no double hyphens again (after character replacement)
	for strings.Contains(fileName, "--") {
		fileName = strings.ReplaceAll(fileName, "--", "-")
	}

	// Trim any leading or trailing hyphens
	return strings.Trim(fileName, "-")
}

// extractPromptName creates a simplified name from the prompt content
// It takes the first few words of the prompt content, up to a reasonable length
func extractPromptName(content string) string {
	if os.Getenv("PROMZ_DEBUG") == "true" {
		log.Printf("FileNameGenerator.extractPromptName: Processing content with length %d", len(content))
	}
	if content == "" {
		return ""
	}

	// Take first 50 characters or less
	maxLen := 50
	if len(content) > maxLen {
		content = content[:maxLen]
	}

	// Find the last space to avoid cutting words
	lastSpaceIndex := strings.LastIndex(content, " ")
	if lastSpaceIndex > 30 { // Ensure we have at least a few characters
		content = content[:lastSpaceIndex]
	}

	// Format prompt name (lowercase, replace spaces with hyphens)
	content = strings.ToLower(content)
	content = strings.ReplaceAll(content, " ", "-")

	// Remove special characters except hyphens
	result := sanitizeForFileName(content)

	if os.Getenv("PROMZ_DEBUG") == "true" {
		log.Printf("FileNameGenerator.extractPromptName: Extracted name: %s", result)
	}

	return result
}

// variableInfo stores processed information about a variable
type variableInfo struct {
	key       string
	value     string
	processed string
	score     int
}

// extractVariableSummary extracts key variables for the file name using smart processing
func extractVariableSummary(variables map[string]interface{}) string {
	if os.Getenv("PROMZ_DEBUG") == "true" {
		log.Printf("FileNameGenerator.extractVariableSummary: Processing %d variables", len(variables))
		for k, v := range variables {
			// Print the first 20 characters of each variable
			runes := []rune(fmt.Sprintf("%v", v))
			if len(runes) > 20 {
				runes = runes[:20]
			}
			log.Printf("FileNameGenerator.extractVariableSummary: Variable '%s' has value '%s'", k, string(runes))
		}
	}
	if len(variables) == 0 {
		return ""
	}

	// Process and score all variables, but skip content variables
	var varInfos []variableInfo
	for key, value := range variables {
		// Skip variables that end with ":CONTENTS" as they typically contain large bodies of text
		if strings.HasSuffix(key, ":CONTENTS") {
			if os.Getenv("PROMZ_DEBUG") == "true" {
				log.Printf("FileNameGenerator.extractVariableSummary: Skipping content variable '%s'", key)
			}
			continue
		}

		// Convert value to string
		valueStr := fmt.Sprintf("%v", value)
		if valueStr == "" {
			continue
		}

		// Process the variable value
		processed, score := processVariable(valueStr)
		if processed == "" {
			continue
		}

		varInfos = append(varInfos, variableInfo{
			key:       key,
			value:     valueStr,
			processed: processed,
			score:     score,
		})

		if os.Getenv("PROMZ_DEBUG") == "true" {
			log.Printf("FileNameGenerator.extractVariableSummary: Processed variable '%s' to '%s' with score %d",
				key, processed, score)
		}
	}

	// Sort variables by score (highest first)
	sort.Slice(varInfos, func(i, j int) bool {
		return varInfos[i].score > varInfos[j].score
	})

	// Take top variables (limit to 4 max)
	maxVars := 4
	if len(varInfos) > maxVars {
		varInfos = varInfos[:maxVars]
		if os.Getenv("PROMZ_DEBUG") == "true" {
			log.Printf("FileNameGenerator.extractVariableSummary: Limited to top %d variables by score", maxVars)
		}
	}

	// Extract processed values
	var values []string
	for _, info := range varInfos {
		values = append(values, info.processed)
	}

	// Join variable values with hyphens
	result := strings.Join(values, "-")

	if os.Getenv("PROMZ_DEBUG") == "true" {
		log.Printf("FileNameGenerator.extractVariableSummary: Final summary: %s", result)
	}

	return result
}

// processVariable intelligently processes a variable based on its content type
// Returns the processed value and a score indicating its importance
// Helper functions for variable processing

// isURL checks if a string is a URL
func isURL(s string) bool {
	s = strings.TrimSpace(s)
	_, err := url.Parse(s)
	if err != nil {
		return false
	}

	// Check for common URL patterns
	return strings.HasPrefix(s, "http://") ||
		strings.HasPrefix(s, "https://") ||
		strings.HasPrefix(s, "www.")
}

// extractDomain extracts the domain from a URL
func extractDomain(urlStr string) string {
	u, err := url.Parse(urlStr)
	if err != nil {
		return ""
	}

	// Get the hostname
	hostname := u.Hostname()

	// Extract domain without www prefix
	hostname = strings.TrimPrefix(hostname, "www.")

	// Extract the main domain (e.g., "example" from "example.com")
	parts := strings.Split(hostname, ".")
	if len(parts) > 0 {
		// Use the domain name without TLD
		return parts[0]
	}

	return hostname
}

// isEmail checks if a string is an email address
func isEmail(s string) bool {
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	return emailRegex.MatchString(s)
}

// extractEmailUsername extracts the username part from an email address
func extractEmailUsername(email string) string {
	parts := strings.Split(email, "@")
	if len(parts) < 2 {
		return ""
	}
	return sanitizeForFileName(parts[0])
}

// isDate checks if a string looks like a date
func isDate(s string) bool {
	// Check for common date formats
	dateRegex := regexp.MustCompile(`\b\d{1,4}[-/.]\d{1,2}[-/.]\d{1,4}\b`)
	return dateRegex.MatchString(s)
}

// isNumber checks if a string is a numeric value
func isNumber(s string) bool {
	numberRegex := regexp.MustCompile(`^-?\d+(\.\d+)?$`)
	return numberRegex.MatchString(s)
}

// extractMeaningfulWords extracts meaningful words from long text
func extractMeaningfulWords(text string) string {
	// Extract first few words (up to 25 chars)
	words := strings.Fields(text)
	var result string

	for _, word := range words {
		// Skip very short words and common words
		if len(word) <= 2 || isCommonWord(word) {
			continue
		}

		// Add word if it doesn't make the result too long
		if len(result) == 0 {
			result = word
		} else if len(result)+len(word)+1 <= 25 {
			result += "-" + word
		} else {
			break
		}
	}

	return sanitizeForFileName(result)
}

// isCommonWord checks if a word is a common word that should be excluded
func isCommonWord(word string) bool {
	word = strings.ToLower(word)
	commonWords := map[string]bool{
		"the": true, "and": true, "for": true, "with": true, "this": true,
		"that": true, "from": true, "have": true, "not": true, "are": true,
		"but": true, "was": true, "were": true, "they": true, "their": true,
	}
	return commonWords[word]
}

func processVariable(value string) (string, int) {
	// Check if it's a URL
	if isURL(value) {
		domain := extractDomain(value)
		if domain != "" {
			// URLs are important and make good identifiers
			return domain, 80
		}
	}

	// Check if it's an email address
	if isEmail(value) {
		username := extractEmailUsername(value)
		if username != "" {
			return username, 70
		}
	}

	// Check if it's a date
	if isDate(value) {
		// Dates are good identifiers but we already have a date stamp
		return sanitizeForFileName(value), 60
	}

	// Check if it's a number
	if isNumber(value) {
		// Numbers are usually important
		return sanitizeForFileName(value), 75
	}

	// Handle long text
	if len(value) > 100 {
		// Long text is probably content, extract meaningful words
		return extractMeaningfulWords(value), 40
	}

	// For medium text
	if len(value) > 30 {
		// Medium text might be a title or description
		return truncateText(value, 25), 50
	}

	// Short values are kept as is (default score: 60)
	return sanitizeForFileName(value), 60
}

// truncateText truncates text to the specified length at word boundaries
func truncateText(text string, maxLength int) string {
	if len(text) <= maxLength {
		return text
	}

	// Try to break at a word boundary
	lastHyphenIndex := strings.LastIndex(text[:maxLength], "-")
	lastSpaceIndex := strings.LastIndex(text[:maxLength], " ")

	// Find the best breaking point
	breakPoint := maxLength
	if lastHyphenIndex > maxLength/2 {
		breakPoint = lastHyphenIndex
	} else if lastSpaceIndex > maxLength/2 {
		breakPoint = lastSpaceIndex
	}

	return text[:breakPoint]
}

// sanitizeForFileName removes characters that aren't safe for file names
// and ensures the file name doesn't exceed the maximum allowed length
func sanitizeForFileName(s string) string {
	if os.Getenv("PROMZ_DEBUG") == "true" {
		log.Printf("FileNameGenerator.sanitizeForFileName: Sanitizing string with length %d", len(s))
	}

	// Process dates in a consistent format
	s = formatDateIfPresent(s)

	// Replace unsafe characters with hyphens
	// Expanded list to include more punctuation and special characters
	unsafe := []string{
		":", "/", "\\", "*", "?", "\"", "<", ">", "|", " ",
		",", ".", ";", "'", "`", "~", "!", "@", "#", "$",
		"%", "^", "&", "(", ")", "=", "+", "[", "]", "{", "}",
	}
	result := s

	for _, char := range unsafe {
		result = strings.ReplaceAll(result, char, "-")
	}

	// Replace multiple hyphens with a single one
	for strings.Contains(result, "--") {
		result = strings.ReplaceAll(result, "--", "-")
	}

	// Trim hyphens from start and end
	result = strings.Trim(result, "-")

	// Ensure the result isn't too long (max 50 characters)
	// This is a safety measure to prevent file name length issues
	maxLen := 50
	if len(result) > maxLen {
		if os.Getenv("PROMZ_DEBUG") == "true" {
			log.Printf("FileNameGenerator.sanitizeForFileName: String too long (%d chars), truncating to %d chars",
				len(result), maxLen)
		}

		// Try to break at a hyphen if possible
		lastHyphenIndex := strings.LastIndex(result[:maxLen], "-")
		if lastHyphenIndex > maxLen/2 {
			result = result[:lastHyphenIndex]
		} else {
			// Just truncate if no good breaking point
			result = result[:maxLen]
		}

		if os.Getenv("PROMZ_DEBUG") == "true" {
			log.Printf("FileNameGenerator.sanitizeForFileName: Truncated to: %s", result)
		}
	}

	// Final cleanup - ensure no double hyphens remain
	for strings.Contains(result, "--") {
		result = strings.ReplaceAll(result, "--", "-")
	}

	// Ensure no leading or trailing hyphens
	result = strings.Trim(result, "-")

	if os.Getenv("PROMZ_DEBUG") == "true" {
		log.Printf("FileNameGenerator.sanitizeForFileName: Final sanitized string: %s", result)
	}
	return result
}

// formatDateIfPresent detects and formats dates consistently
func formatDateIfPresent(s string) string {
	// First, handle common date formats with time components (like 10/13/23, 9:08 PM)
	// This regex matches patterns like MM/DD/YY or MM-DD-YY followed by time
	dateTimeRegex := regexp.MustCompile(`(\d{1,2}[/-]\d{1,2}[/-]\d{2,4})\s*,?\s*(\d{1,2}:\d{2}(:\d{2})?\s*(AM|PM|EDT|PDT|UTC|GMT)?)`)
	if dateTimeRegex.MatchString(s) {
		// Replace with just the date part, properly formatted
		s = dateTimeRegex.ReplaceAllStringFunc(s, func(match string) string {
			parts := dateTimeRegex.FindStringSubmatch(match)
			if len(parts) >= 2 {
				// Convert the date part to a consistent format with hyphens
				datePart := parts[1]
				datePart = strings.ReplaceAll(datePart, "/", "-")
				return datePart
			}
			return match
		})
	}

	// Check for month names followed by day and year
	monthNameRegex := regexp.MustCompile(`(January|February|March|April|May|June|July|August|September|October|November|December)\s+(\d{1,2})\s*,?\s*(\d{4})`)
	if monthNameRegex.MatchString(s) {
		// Replace with a consistent format: Month-Day-Year
		s = monthNameRegex.ReplaceAllStringFunc(s, func(match string) string {
			parts := monthNameRegex.FindStringSubmatch(match)
			if len(parts) >= 4 {
				return fmt.Sprintf("%s-%s-%s", parts[1], parts[2], parts[3])
			}
			return match
		})
	}

	// Check for any remaining time components and remove them
	timeRegex := regexp.MustCompile(`\s*\d{1,2}:\d{2}(:\d{2})?\s*(AM|PM|EDT|PDT|UTC|GMT)?\s*`)
	if timeRegex.MatchString(s) {
		// Replace with a single space
		s = timeRegex.ReplaceAllString(s, " ")
	}

	// Clean up the string - trim spaces and remove double spaces
	s = strings.TrimSpace(s)
	for strings.Contains(s, "  ") {
		s = strings.ReplaceAll(s, "  ", " ")
	}

	return s
}
