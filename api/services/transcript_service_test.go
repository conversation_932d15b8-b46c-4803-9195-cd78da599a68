package services

import (
	"io"
	"net/http"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// MockRoundTripper is a custom http.RoundTripper for testing
type MockRoundTripper struct {
	// Map of URL patterns to responses
	responses map[string]mockResponse
}

type mockResponse struct {
	statusCode int
	body       string
	headers    map[string]string
}

// RoundTrip implements the http.RoundTripper interface
func (m *MockRoundTripper) RoundTrip(req *http.Request) (*http.Response, error) {
	url := req.URL.String()

	// Find matching response by checking if URL contains any of our defined patterns
	for pattern, resp := range m.responses {
		if strings.Contains(url, pattern) {
			// Create response
			response := &http.Response{
				StatusCode: resp.statusCode,
				Body:       io.NopCloser(strings.NewReader(resp.body)),
				Header:     make(http.Header),
			}

			// Add headers
			for key, value := range resp.headers {
				response.Header.Add(key, value)
			}

			return response, nil
		}
	}

	// No match found, return 404
	return &http.Response{
		StatusCode: http.StatusNotFound,
		Body:       io.NopCloser(strings.NewReader("Not found")),
		Header:     make(http.Header),
	}, nil
}

// Create a legacy service for testing
func createLegacyService() *TranscriptService {
	return &TranscriptService{
		cache:      make(map[string][]TranscriptData),
		cacheTTL:   24 * time.Hour,
		lastClear:  time.Now(),
		httpClient: &http.Client{Timeout: 10 * time.Second},
		userAgent:  "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
	}
}

func TestNewTranscriptService(t *testing.T) {
	service := createLegacyService()
	assert.NotNil(t, service)
	assert.NotNil(t, service.cache)
	assert.Equal(t, 24*time.Hour, service.cacheTTL)
}

func TestGetTranscript(t *testing.T) {
	// This is an integration test that requires internet connection
	// and depends on YouTube's API, so we'll skip it in automated testing
	t.Skip("Skipping integration test that requires internet connection")

	service := createLegacyService()
	videoURL := "https://www.youtube.com/watch?v=dQw4w9WgXcQ" // Rick Astley - Never Gonna Give You Up
	transcript, title, err := service.GetTranscript(videoURL, "en", "US")

	assert.NoError(t, err)
	assert.NotEmpty(t, title)
	assert.NotEmpty(t, transcript)
	assert.Greater(t, len(transcript), 0)

	// Test caching
	// The second call should use the cached data
	cachedTranscript, cachedTitle, err := service.GetTranscript(videoURL, "en", "US")
	assert.NoError(t, err)
	assert.Equal(t, title, cachedTitle)
	assert.Equal(t, len(transcript), len(cachedTranscript))
}

func TestGetFullTranscriptText(t *testing.T) {
	// This is an integration test that requires internet connection
	// and depends on YouTube's API, so we'll skip it in automated testing
	t.Skip("Skipping integration test that requires internet connection")

	service := createLegacyService()
	videoURL := "https://www.youtube.com/watch?v=dQw4w9WgXcQ" // Rick Astley - Never Gonna Give You Up
	text, err := service.GetFullTranscriptText(videoURL, "en", "US")

	assert.NoError(t, err)
	assert.NotEmpty(t, text)
}

func TestCleanCache(t *testing.T) {
	service := createLegacyService()

	// Manually add some data to the cache
	service.mutex.Lock()
	service.cache["test"] = []TranscriptData{
		{
			Text:     "Test transcript",
			Duration: 1000,
			Offset:   0,
		},
	}
	service.mutex.Unlock()

	// Set lastClear to a time in the past that exceeds cacheTTL
	service.lastClear = time.Now().Add(-25 * time.Hour)

	// Clean the cache
	service.CleanCache()

	// Verify the cache is empty
	service.mutex.RLock()
	defer service.mutex.RUnlock()
	assert.Empty(t, service.cache)
}

// TestExtractVideoID tests the ExtractVideoID function
func TestExtractVideoID(t *testing.T) {
	// We test the function directly, not through a service instance
	// Correction: ExtractVideoID is a method, so we need a service instance.
	service := createLegacyService()
	if service == nil {
		t.Fatal("Failed to create service instance for testing ExtractVideoID")
	}

	tests := []struct {
		name    string
		url     string
		wantID  string
		wantErr bool
	}{
		{
			name:    "Standard YouTube URL",
			url:     "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
			wantID:  "dQw4w9WgXcQ",
			wantErr: false,
		},
		{
			name:    "YouTube Short URL",
			url:     "https://youtu.be/dQw4w9WgXcQ",
			wantID:  "dQw4w9WgXcQ",
			wantErr: false,
		},
		{
			name:    "YouTube Embed URL",
			url:     "https://www.youtube.com/embed/dQw4w9WgXcQ",
			wantID:  "dQw4w9WgXcQ",
			wantErr: false,
		},
		{
			name:    "YouTube V URL",
			url:     "https://www.youtube.com/v/dQw4w9WgXcQ",
			wantID:  "dQw4w9WgXcQ",
			wantErr: false,
		},
		{
			name:    "URL with additional parameters",
			url:     "https://www.youtube.com/watch?v=dQw4w9WgXcQ&t=120s&feature=youtu.be",
			wantID:  "dQw4w9WgXcQ",
			wantErr: false,
		},
		{
			name:    "Invalid YouTube URL",
			url:     "https://www.example.com/watch?v=123",
			wantID:  "",
			wantErr: true,
		},
		{
			name:    "Empty URL",
			url:     "",
			wantID:  "",
			wantErr: true,
		},
		{
			name:    "Malformed URL",
			url:     "not a url",
			wantID:  "",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotID, err := service.ExtractVideoID(tt.url)

			if (err != nil) != tt.wantErr {
				t.Errorf("ExtractVideoID() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if gotID != tt.wantID {
				t.Errorf("ExtractVideoID() = %v, want %v", gotID, tt.wantID)
			}
		})
	}
}
