package main

import (
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"regexp"
	"strings"
	"time"
)

// TranscriptSegment represents a segment of a YouTube transcript
type TranscriptSegment struct {
	Text     string  `json:"text"`
	Start    float64 `json:"start"`
	Duration float64 `json:"duration"`
}

// RobustTranscriptFetcher provides a more reliable way to fetch YouTube transcripts
type RobustTranscriptFetcher struct {
	client      *http.Client
	userAgent   string
	httpTimeout time.Duration
}

// NewTranscriptFetcher creates a new transcript fetcher with default settings
func NewTranscriptFetcher() *RobustTranscriptFetcher {
	return &RobustTranscriptFetcher{
		client: &http.Client{
			Timeout: 10 * time.Second,
		},
		userAgent:   "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
		httpTimeout: 10 * time.Second,
	}
}

// ExtractVideoID extracts the YouTube video ID from various URL formats
func (r *RobustTranscriptFetcher) ExtractVideoID(videoURL string) (string, error) {
	// Handle youtu.be URLs
	if strings.Contains(videoURL, "youtu.be/") {
		parts := strings.Split(videoURL, "youtu.be/")
		if len(parts) < 2 {
			return "", fmt.Errorf("invalid youtu.be URL format: %s", videoURL)
		}
		// Extract ID and remove any query parameters
		id := strings.Split(parts[1], "?")[0]
		return id, nil
	}

	// Handle youtube.com URLs
	parsedURL, err := url.Parse(videoURL)
	if err != nil {
		return "", err
	}

	// Handle youtube.com/watch?v= format
	if strings.Contains(videoURL, "youtube.com/watch") {
		vParam := parsedURL.Query().Get("v")
		if vParam == "" {
			return "", fmt.Errorf("no video ID found in URL: %s", videoURL)
		}
		return vParam, nil
	}

	// Handle youtube.com/embed/ format
	if strings.Contains(videoURL, "youtube.com/embed/") {
		parts := strings.Split(videoURL, "youtube.com/embed/")
		if len(parts) < 2 {
			return "", fmt.Errorf("invalid embed URL format: %s", videoURL)
		}
		// Extract ID and remove any query parameters
		id := strings.Split(parts[1], "?")[0]
		return id, nil
	}

	// Handle youtube.com/v/ format
	if strings.Contains(videoURL, "youtube.com/v/") {
		parts := strings.Split(videoURL, "youtube.com/v/")
		if len(parts) < 2 {
			return "", fmt.Errorf("invalid v URL format: %s", videoURL)
		}
		// Extract ID and remove any query parameters
		id := strings.Split(parts[1], "?")[0]
		return id, nil
	}

	return "", fmt.Errorf("unsupported YouTube URL format: %s", videoURL)
}

// FetchTranscript fetches the transcript for a YouTube video
func (r *RobustTranscriptFetcher) FetchTranscript(videoID string, lang string) ([]TranscriptSegment, error) {
	// Multiple strategies to fetch transcripts

	// Strategy 1: Use YouTube's transcript API
	transcript, err := r.fetchTranscriptUsingAPI(videoID, lang)
	if err == nil && len(transcript) > 0 {
		return transcript, nil
	}

	fmt.Printf("Strategy 1 failed: %v\nTrying strategy 2...\n", err)

	// Strategy 2: Parse the video page and extract transcript data
	transcript, err = r.fetchTranscriptFromPage(videoID)
	if err == nil && len(transcript) > 0 {
		return transcript, nil
	}

	fmt.Printf("Strategy 2 failed: %v\n", err)

	// All strategies failed
	return nil, fmt.Errorf("failed to fetch transcript: %w", err)
}

// fetchTranscriptUsingAPI attempts to fetch transcript using YouTube's transcript API
func (r *RobustTranscriptFetcher) fetchTranscriptUsingAPI(videoID, lang string) ([]TranscriptSegment, error) {
	// Construct the URL to fetch the transcript
	apiURL := fmt.Sprintf("https://www.youtube.com/api/timedtext?v=%s&lang=%s", videoID, lang)

	req, err := http.NewRequest("GET", apiURL, nil)
	if err != nil {
		return nil, err
	}

	req.Header.Set("User-Agent", r.userAgent)

	resp, err := r.client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API returned status code %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	// Parse the XML response
	// This is a simple implementation - a production version would use proper XML parsing
	if len(body) < 100 || !strings.Contains(string(body), "<text") {
		return nil, fmt.Errorf("no transcript found in API response")
	}

	// Extract transcript segments from XML
	segments := r.extractSegmentsFromXML(string(body))
	if len(segments) == 0 {
		return nil, fmt.Errorf("failed to extract segments from XML")
	}

	return segments, nil
}

// fetchTranscriptFromPage attempts to fetch transcript by parsing the video page
func (r *RobustTranscriptFetcher) fetchTranscriptFromPage(videoID string) ([]TranscriptSegment, error) {
	// Fetch the video page
	videoURL := fmt.Sprintf("https://www.youtube.com/watch?v=%s", videoID)

	req, err := http.NewRequest("GET", videoURL, nil)
	if err != nil {
		return nil, err
	}

	req.Header.Set("User-Agent", r.userAgent)

	resp, err := r.client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("page returned status code %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	pageContent := string(body)

	// Try to find transcript data in the page
	// This uses regex to find parts that might contain transcript data

	// Look for captionTracks in the page
	captionRegex := regexp.MustCompile(`"captionTracks":\s*(\[.*?\])`)
	matches := captionRegex.FindStringSubmatch(pageContent)

	if len(matches) < 2 {
		return nil, fmt.Errorf("could not find caption tracks in page")
	}

	captionJSON := matches[1]

	// Extract the URL for transcript JSON
	urlRegex := regexp.MustCompile(`"baseUrl":\s*"(.*?)"`)
	urlMatches := urlRegex.FindStringSubmatch(captionJSON)

	if len(urlMatches) < 2 {
		return nil, fmt.Errorf("could not extract transcript URL")
	}

	// Replace escaped characters in the URL
	transcriptURL := strings.ReplaceAll(urlMatches[1], "\\u0026", "&")

	// Fetch the transcript JSON
	req, err = http.NewRequest("GET", transcriptURL, nil)
	if err != nil {
		return nil, err
	}

	req.Header.Set("User-Agent", r.userAgent)

	resp, err = r.client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("transcript URL returned status code %d", resp.StatusCode)
	}

	transcriptBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	// Parse the transcript XML
	segments := r.extractSegmentsFromXML(string(transcriptBody))
	if len(segments) == 0 {
		return nil, fmt.Errorf("failed to extract segments from transcript XML")
	}

	return segments, nil
}

// extractSegmentsFromXML extracts transcript segments from XML content
func (r *RobustTranscriptFetcher) extractSegmentsFromXML(xmlContent string) []TranscriptSegment {
	var segments []TranscriptSegment

	// Extract text elements
	regex := regexp.MustCompile(`<text start="([\d.]+)" dur="([\d.]+)"[^>]*>(.*?)</text>`)
	matches := regex.FindAllStringSubmatch(xmlContent, -1)

	for _, match := range matches {
		if len(match) < 4 {
			continue
		}

		startTime, _ := fmt.Sscanf(match[1], "%f")
		duration, _ := fmt.Sscanf(match[2], "%f")
		text := match[3]

		// Handle HTML entities and formatting
		text = strings.ReplaceAll(text, "&#39;", "'")
		text = strings.ReplaceAll(text, "&amp;", "&")
		text = strings.ReplaceAll(text, "&quot;", "\"")
		text = strings.ReplaceAll(text, "&lt;", "<")
		text = strings.ReplaceAll(text, "&gt;", ">")

		// Remove HTML tags
		htmlTagRegex := regexp.MustCompile(`<[^>]*>`)
		text = htmlTagRegex.ReplaceAllString(text, "")

		segment := TranscriptSegment{
			Text:     text,
			Start:    float64(startTime),
			Duration: float64(duration),
		}

		segments = append(segments, segment)
	}

	return segments
}

// FetchTranscriptText fetches the transcript as a plain text string
func (r *RobustTranscriptFetcher) FetchTranscriptText(videoURL, lang string) (string, error) {
	videoID, err := r.ExtractVideoID(videoURL)
	if err != nil {
		return "", err
	}

	segments, err := r.FetchTranscript(videoID, lang)
	if err != nil {
		return "", err
	}

	var textBuilder strings.Builder
	for _, segment := range segments {
		textBuilder.WriteString(segment.Text)
		textBuilder.WriteString(" ")
	}

	return textBuilder.String(), nil
}

// GetVideoInfo fetches basic info about the video (title, etc.)
func (r *RobustTranscriptFetcher) GetVideoInfo(videoID string) (map[string]string, error) {
	videoURL := fmt.Sprintf("https://www.youtube.com/watch?v=%s", videoID)

	req, err := http.NewRequest("GET", videoURL, nil)
	if err != nil {
		return nil, err
	}

	req.Header.Set("User-Agent", r.userAgent)

	resp, err := r.client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("page returned status code %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	pageContent := string(body)

	// Extract title
	titleRegex := regexp.MustCompile(`<meta\s+property="og:title"\s+content="([^"]+)"`)
	titleMatch := titleRegex.FindStringSubmatch(pageContent)

	// Extract channel name
	channelRegex := regexp.MustCompile(`<link\s+itemprop="name"\s+content="([^"]+)"`)
	channelMatch := channelRegex.FindStringSubmatch(pageContent)

	info := make(map[string]string)

	if len(titleMatch) > 1 {
		info["title"] = titleMatch[1]
	} else {
		info["title"] = "YouTube Video: " + videoID
	}

	if len(channelMatch) > 1 {
		info["channel"] = channelMatch[1]
	} else {
		info["channel"] = "Unknown Channel"
	}

	return info, nil
}

// Main function for testing
func main() {
	if len(os.Args) < 2 {
		fmt.Println("Usage: go run robust_transcript.go <youtube_url> [language]")
		os.Exit(1)
	}

	videoURL := os.Args[1]
	lang := "en"

	if len(os.Args) >= 3 {
		lang = os.Args[2]
	}

	fetcher := NewTranscriptFetcher()

	fmt.Printf("Testing transcript fetcher for URL: %s\n", videoURL)

	// Extract video ID
	videoID, err := fetcher.ExtractVideoID(videoURL)
	if err != nil {
		fmt.Printf("Error extracting video ID: %v\n", err)
		os.Exit(1)
	}

	fmt.Printf("Extracted video ID: %s\n", videoID)

	// Try to get video info
	info, err := fetcher.GetVideoInfo(videoID)
	if err != nil {
		fmt.Printf("Error getting video info: %v\n", err)
	} else {
		fmt.Printf("Video title: %s\n", info["title"])
		fmt.Printf("Channel: %s\n", info["channel"])
	}

	fmt.Println("Fetching transcript...")

	// Try to get full transcript text
	text, err := fetcher.FetchTranscriptText(videoURL, lang)
	if err != nil {
		fmt.Printf("Error fetching transcript: %v\n", err)
		os.Exit(1)
	}

	// Print transcript preview and length
	preview := text
	if len(text) > 500 {
		preview = text[:500] + "..."
	}

	fmt.Printf("\nTranscript successfully retrieved! Length: %d characters\n", len(text))
	fmt.Printf("\nPreview:\n%s\n", preview)

	// Save to file as an option
	if len(os.Args) >= 4 && os.Args[3] == "--save" {
		fileName := videoID + "_transcript.txt"
		err := os.WriteFile(fileName, []byte(text), 0o644)
		if err != nil {
			fmt.Printf("Error saving to file: %v\n", err)
		} else {
			fmt.Printf("\nTranscript saved to %s\n", fileName)
		}
	}

	// Try to get structured transcript
	fmt.Println("\nFetching structured transcript...")
	segments, err := fetcher.FetchTranscript(videoID, lang)
	if err != nil {
		fmt.Printf("Error fetching structured transcript: %v\n", err)
		os.Exit(1)
	}

	fmt.Printf("\nTranscript has %d segments\n", len(segments))

	// Print first 3 segments
	previewCount := 3
	if len(segments) < previewCount {
		previewCount = len(segments)
	}

	fmt.Println("\nFirst few segments:")
	for i := 0; i < previewCount; i++ {
		segment := segments[i]
		fmt.Printf("[%0.1fs - %0.1fs]: %s\n",
			segment.Start,
			segment.Start+segment.Duration,
			segment.Text)
	}
}
