package services

import (
	"fmt"
	"log"
	"net/http"
	"strings"
	"sync"
	"time"
)

// NewTranscriptService creates a new transcript service with default settings
// This is for backwards compatibility with existing code
func NewTranscriptService() TranscriptServiceInterface {
	service, err := NewFallbackTranscriptService(NewDefaultTranscriptServiceConfig())
	if err != nil {
		// Fall back to the original scraping-only implementation if the fallback service fails
		return &LegacyTranscriptService{
			cache:      make(map[string][]TranscriptData),
			cacheTTL:   24 * time.Hour,
			lastClear:  time.Now(),
			httpClient: &http.Client{Timeout: 10 * time.Second},
			userAgent:  "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
		}
	}
	return service
}

// NewTranscriptServiceWithAPIKey creates a new transcript service with the provided YouTube API key
func NewTranscriptServiceWithAPIKey(apiKey string) TranscriptServiceInterface {
	config := NewDefaultTranscriptServiceConfig()
	config.APIKey = apiKey

	// Log API key length to verify it's being passed correctly
	maskedKey := "****"
	if len(apiKey) > 8 {
		maskedKey = apiKey[:4] + "..." + apiKey[len(apiKey)-4:]
	}
	log.Printf("DEBUG: Creating transcript service with API key: %s (length: %d)", maskedKey, len(apiKey))

	service, err := NewFallbackTranscriptService(config)
	if err != nil {
		log.Printf("ERROR: Failed to create FallbackTranscriptService: %v", err)
		// Fall back to the original scraping-only implementation if the fallback service fails
		return &LegacyTranscriptService{
			cache:      make(map[string][]TranscriptData),
			cacheTTL:   24 * time.Hour,
			lastClear:  time.Now(),
			httpClient: &http.Client{Timeout: 10 * time.Second},
			userAgent:  "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
		}
	}
	return service
}

// LegacyTranscriptService is the original implementation, kept for backwards compatibility
type LegacyTranscriptService struct {
	cache      map[string][]TranscriptData // In-memory cache
	mutex      sync.RWMutex
	cacheTTL   time.Duration // Time-to-live for cache entries
	lastClear  time.Time     // Last time cache was cleared
	httpClient *http.Client
	userAgent  string
}

// GetTranscript uses the scraping strategy
func (s *LegacyTranscriptService) GetTranscript(videoURL, lang, country string) ([]TranscriptData, string, error) {
	// Extract video ID
	videoID, err := ExtractVideoID(videoURL)
	if err != nil {
		return nil, "", err
	}

	// Create a scraping strategy and delegate to it
	strategy := NewScrapingStrategy()
	transcript, title, err := strategy.GetTranscript(videoID, lang, country)
	if err != nil {
		return nil, "", err
	}

	// Convert to our data structure
	var data []TranscriptData
	for _, seg := range transcript {
		data = append(data, TranscriptData{
			Text:     seg.Text,
			Duration: int(seg.Duration * 1000), // Convert to milliseconds
			Offset:   int(seg.Start * 1000),    // Convert to milliseconds
		})
	}

	// Cache the result
	s.mutex.Lock()
	s.cache[videoID] = data
	s.mutex.Unlock()

	return data, title, nil
}

// GetFullTranscriptText returns the full transcript as a single string
func (s *LegacyTranscriptService) GetFullTranscriptText(videoURL, lang, country string) (string, error) {
	transcript, _, err := s.GetTranscript(videoURL, lang, country)
	if err != nil {
		log.Printf("Error getting transcript for %s: %v", videoURL, err)

		// Generate a simple fallback message when GetTranscript fails
		fallbackMsg := fmt.Sprintf(
			"[Transcript unavailable for video: %s. Error: %v]",
			videoURL, err,
		)
		return fallbackMsg, nil // Return message but no error to prevent processing failures
	}

	// Check if we have any transcript segments
	if len(transcript) == 0 {
		log.Printf("Warning: Empty transcript returned for %s", videoURL)
		return fmt.Sprintf("[No transcript content available for video: %s]", videoURL), nil
	}

	// Build the full text from transcript segments
	var fullText strings.Builder
	for _, t := range transcript {
		fullText.WriteString(t.Text)
		fullText.WriteString(" ")
	}

	return fullText.String(), nil
}

// CleanCache removes expired entries from the cache
func (s *LegacyTranscriptService) CleanCache() {
	if time.Since(s.lastClear) < s.cacheTTL {
		return
	}

	s.mutex.Lock()
	defer s.mutex.Unlock()

	s.cache = make(map[string][]TranscriptData)
	s.lastClear = time.Now()
}
