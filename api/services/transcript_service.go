package services

import (
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"regexp"
	"strings"
	"sync"
	"time"
)

// TranscriptData represents a segment of a YouTube transcript
type TranscriptData struct {
	Text     string `json:"text"`
	Duration int    `json:"duration"`
	Offset   int    `json:"offset_ms"`
}

// TranscriptServiceInterface defines the interface for transcript service
type TranscriptServiceInterface interface {
	GetTranscript(videoURL, lang, country string) ([]TranscriptData, string, error)
	GetFullTranscriptText(videoURL, lang, country string) (string, error)
	CleanCache()
}

// TranscriptService handles YouTube transcript operations
type TranscriptService struct {
	cache      map[string][]TranscriptData // In-memory cache
	mutex      sync.RWMutex
	cacheTTL   time.Duration // Time-to-live for cache entries
	lastClear  time.Time     // Last time cache was cleared
	httpClient *http.Client
	userAgent  string
}

// NewLegacyTranscriptService creates a new transcript service with default settings
// This function is kept for historical reference and should not be used in new code.
// Use NewTranscriptService from transcript_service_factory.go instead.
func NewLegacyTranscriptService() *TranscriptService {
	return &TranscriptService{
		cache:      make(map[string][]TranscriptData),
		cacheTTL:   24 * time.Hour, // Cache entries expire after 24 hours
		lastClear:  time.Now(),
		httpClient: &http.Client{Timeout: 10 * time.Second},
		userAgent:  "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
	}
}

// ExtractVideoID extracts the YouTube video ID from various URL formats
func (s *TranscriptService) ExtractVideoID(videoURL string) (string, error) {
	// Handle youtu.be URLs
	if strings.Contains(videoURL, "youtu.be/") {
		parts := strings.Split(videoURL, "youtu.be/")
		if len(parts) < 2 {
			return "", fmt.Errorf("invalid youtu.be URL format: %s", videoURL)
		}
		// Extract ID and remove any query parameters
		id := strings.Split(parts[1], "?")[0]
		return id, nil
	}

	// Handle youtube.com URLs
	parsedURL, err := url.Parse(videoURL)
	if err != nil {
		return "", err
	}

	// Handle youtube.com/watch?v= format
	if strings.Contains(videoURL, "youtube.com/watch") {
		vParam := parsedURL.Query().Get("v")
		if vParam == "" {
			return "", fmt.Errorf("no video ID found in URL: %s", videoURL)
		}
		return vParam, nil
	}

	// Handle youtube.com/embed/ format
	if strings.Contains(videoURL, "youtube.com/embed/") {
		parts := strings.Split(videoURL, "youtube.com/embed/")
		if len(parts) < 2 {
			return "", fmt.Errorf("invalid embed URL format: %s", videoURL)
		}
		// Extract ID and remove any query parameters
		id := strings.Split(parts[1], "?")[0]
		return id, nil
	}

	// Handle youtube.com/v/ format
	if strings.Contains(videoURL, "youtube.com/v/") {
		parts := strings.Split(videoURL, "youtube.com/v/")
		if len(parts) < 2 {
			return "", fmt.Errorf("invalid v URL format: %s", videoURL)
		}
		// Extract ID and remove any query parameters
		id := strings.Split(parts[1], "?")[0]
		return id, nil
	}

	return "", fmt.Errorf("unsupported YouTube URL format: %s", videoURL)
}

// GetVideoTitle tries to get the title of a YouTube video from its page
func (s *TranscriptService) GetVideoTitle(videoID string) (string, error) {
	videoURL := fmt.Sprintf("https://www.youtube.com/watch?v=%s", videoID)

	req, err := http.NewRequest("GET", videoURL, nil)
	if err != nil {
		return "", err
	}

	req.Header.Set("User-Agent", s.userAgent)

	resp, err := s.httpClient.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("page returned status code %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	pageContent := string(body)

	// Extract title using regex
	titleRegex := regexp.MustCompile(`<meta\s+property="og:title"\s+content="([^"]+)"`)
	titleMatch := titleRegex.FindStringSubmatch(pageContent)

	if len(titleMatch) > 1 {
		return titleMatch[1], nil
	}

	// Fallback to another method if the first one fails
	titleRegex = regexp.MustCompile(`<title>([^<]+)</title>`)
	titleMatch = titleRegex.FindStringSubmatch(pageContent)

	if len(titleMatch) > 1 {
		// Remove " - YouTube" from the title if present
		title := titleMatch[1]
		title = strings.TrimSuffix(title, " - YouTube")
		return title, nil
	}

	return "YouTube Video: " + videoID, nil
}

// GetTranscript fetches a transcript for a YouTube video with caching
func (s *TranscriptService) GetTranscript(videoURL, lang, country string) ([]TranscriptData, string, error) {
	// Extract video ID
	videoID, err := s.ExtractVideoID(videoURL)
	if err != nil {
		return nil, "", err
	}

	// Check cache first
	s.mutex.RLock()
	if data, ok := s.cache[videoID]; ok {
		s.mutex.RUnlock()
		title, _ := s.GetVideoTitle(videoID)
		log.Printf("DEBUG: Using cached transcript for video %s", videoID)
		return data, title, nil
	}
	s.mutex.RUnlock()

	// Parse the video page to get transcript
	transcript, err := s.fetchTranscriptFromPage(videoID)
	if err != nil {
		log.Printf("Failed to fetch transcript: %v", err)
		return nil, "", err
	}

	// Convert transcript segments to our data structure
	var data []TranscriptData
	for _, seg := range transcript {
		// Convert float duration to int milliseconds
		durationMs := int(seg.Duration * 1000)
		offsetMs := int(seg.Start * 1000)

		data = append(data, TranscriptData{
			Text:     seg.Text,
			Duration: durationMs,
			Offset:   offsetMs,
		})
	}

	// Cache the result
	s.mutex.Lock()
	s.cache[videoID] = data
	s.mutex.Unlock()

	// Get the video title
	title, err := s.GetVideoTitle(videoID)
	if err != nil {
		title = "YouTube Video: " + videoID
	}

	return data, title, nil
}

// LegacyTranscriptSegment represents a segment returned by the robust fetcher
// This type is kept for backwards compatibility with the original implementation
// Use TranscriptSegment from fallback_transcript_service.go instead
type LegacyTranscriptSegment struct {
	Text     string
	Start    float64
	Duration float64
}

// fetchTranscriptFromPage attempts to fetch transcript by parsing the video page
func (s *TranscriptService) fetchTranscriptFromPage(videoID string) ([]LegacyTranscriptSegment, error) {
	// Fetch the video page
	videoURL := fmt.Sprintf("https://www.youtube.com/watch?v=%s", videoID)

	req, err := http.NewRequest("GET", videoURL, nil)
	if err != nil {
		return nil, err
	}

	req.Header.Set("User-Agent", s.userAgent)

	resp, err := s.httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("page returned status code %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	pageContent := string(body)

	// Try to find transcript data in the page
	// Look for captionTracks in the page
	captionRegex := regexp.MustCompile(`"captionTracks":\s*(\[.*?\])`)
	matches := captionRegex.FindStringSubmatch(pageContent)

	if len(matches) < 2 {
		return nil, fmt.Errorf("could not find caption tracks in page")
	}

	captionJSON := matches[1]

	// Extract the URL for transcript JSON
	urlRegex := regexp.MustCompile(`"baseUrl":\s*"(.*?)"`)
	urlMatches := urlRegex.FindStringSubmatch(captionJSON)

	if len(urlMatches) < 2 {
		return nil, fmt.Errorf("could not extract transcript URL")
	}

	// Replace escaped characters in the URL
	transcriptURL := strings.ReplaceAll(urlMatches[1], "\\u0026", "&")

	// Fetch the transcript JSON
	req, err = http.NewRequest("GET", transcriptURL, nil)
	if err != nil {
		return nil, err
	}

	req.Header.Set("User-Agent", s.userAgent)

	resp, err = s.httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("transcript URL returned status code %d", resp.StatusCode)
	}

	transcriptBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	// Parse the transcript XML
	segments := s.extractSegmentsFromXML(string(transcriptBody))
	if len(segments) == 0 {
		return nil, fmt.Errorf("failed to extract segments from transcript XML")
	}

	return segments, nil
}

// extractSegmentsFromXML extracts transcript segments from XML content
func (s *TranscriptService) extractSegmentsFromXML(xmlContent string) []LegacyTranscriptSegment {
	var segments []LegacyTranscriptSegment

	// Extract text elements
	regex := regexp.MustCompile(`<text start="([\d.]+)" dur="([\d.]+)"[^>]*>(.*?)</text>`)
	matches := regex.FindAllStringSubmatch(xmlContent, -1)

	for _, match := range matches {
		if len(match) < 4 {
			continue
		}

		var startTime, duration float64
		fmt.Sscanf(match[1], "%f", &startTime)
		fmt.Sscanf(match[2], "%f", &duration)
		text := match[3]

		// Handle HTML entities and formatting
		text = strings.ReplaceAll(text, "&#39;", "'")
		text = strings.ReplaceAll(text, "&amp;", "&")
		text = strings.ReplaceAll(text, "&quot;", "\"")
		text = strings.ReplaceAll(text, "&lt;", "<")
		text = strings.ReplaceAll(text, "&gt;", ">")

		// Remove HTML tags
		htmlTagRegex := regexp.MustCompile(`<[^>]*>`)
		text = htmlTagRegex.ReplaceAllString(text, "")
		segment := LegacyTranscriptSegment{
			Text:     text,
			Start:    startTime,
			Duration: duration,
		}

		segments = append(segments, segment)
	}

	return segments
}

// GetFullTranscriptText returns the full transcript as a single string
func (s *TranscriptService) GetFullTranscriptText(videoURL, lang, country string) (string, error) {
	transcript, title, err := s.GetTranscript(videoURL, lang, country)
	if err != nil {
		log.Printf("Error getting transcript for %s: %v", videoURL, err)

		// Generate a simple fallback message when GetTranscript fails
		fallbackMsg := fmt.Sprintf(
			"[Transcript unavailable for video: %s. Error: %v]",
			videoURL, err,
		)
		return fallbackMsg, nil // Return message but no error to prevent processing failures
	}

	// Check if we have any transcript segments
	if len(transcript) == 0 {
		log.Printf("Warning: Empty transcript returned for %s", videoURL)
		return fmt.Sprintf("[No transcript content available for video: %s]", title), nil
	}

	// Build the full text from transcript segments
	var fullText strings.Builder
	for _, t := range transcript {
		fullText.WriteString(t.Text)
		fullText.WriteString(" ")
	}

	return fullText.String(), nil
}

// CleanCache removes expired entries from the cache
func (s *TranscriptService) CleanCache() {
	if time.Since(s.lastClear) < s.cacheTTL {
		return
	}

	s.mutex.Lock()
	defer s.mutex.Unlock()

	s.cache = make(map[string][]TranscriptData)
	s.lastClear = time.Now()
}
