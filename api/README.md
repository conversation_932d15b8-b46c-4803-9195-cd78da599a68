# PROMZ API Server

This is the API server for the PROMZ application, providing LLM integration and prompt management.

## Configuration

The API server can be configured using environment variables:

| Environment Variable | Description | Default |
|----------------------|-------------|---------|
| PROMZ_SUPABASE_URL | Supabase URL for database access | (required) |
| PROMZ_SUPABASE_KEY | Supabase API key | (required) |
| PROMZ_GOOGLE_API_KEY | Google AI (Gemini) API key | (optional) |
| PROMZ_OPENAI_API_KEY | OpenAI API key | (optional) |
| PROMZ_USE_REAL_EXECUTION | Enable real LLM execution | `false` in development, `true` in production |
| APP_ENV | Application environment | `development` |
| PORT | Server port | `8080` |

## Mock Mode vs. Real Execution

The API server supports two execution modes:

### Mock Mode (Default in Development)

In mock mode, the API server will not make actual calls to LLM providers. Instead, it will return the request details that were received. This is useful for:

- Debugging client requests without consuming LLM API quotas
- Testing the API integration without needing valid API keys
- Viewing the exact data being sent to LLM providers

To run in mock mode:
```
# Either don't set PROMZ_USE_REAL_EXECUTION or set it to false
PROMZ_USE_REAL_EXECUTION=false go run cmd/server/main.go
```

### Real Execution Mode (Default in Production)

In real execution mode, the API server will make actual calls to configured LLM providers (Google AI/Gemini or OpenAI). This requires valid API keys to be set.

To run in real execution mode:
```
PROMZ_USE_REAL_EXECUTION=true go run cmd/server/main.go
```

## Running the Server

```bash
# Development with mock mode (default)
go run cmd/server/main.go

# Development with real execution
PROMZ_USE_REAL_EXECUTION=true go run cmd/server/main.go

# Production (real execution by default)
APP_ENV=production go run cmd/server/main.go
```
