package llm

import (
	"context"
	"fmt"
	"log"
	"os"
	"time"

	"github.com/google/generative-ai-go/genai"
	"google.golang.org/api/option"
)

// DefaultGeminiModel is the default model name to use for Gemini
const DefaultGeminiModel = "gemini-2.5-pro-exp-03-25"

// GoogleAIProvider implements the Provider interface for Google AI
type GoogleAIProvider struct {
	client    *genai.Client
	model     *genai.GenerativeModel
	apiKey    string
	config    Config
	modelName string
}

// NewGoogleAIProvider creates a new Google AI provider
func NewGoogleAIProvider(config Config) (*GoogleAIProvider, error) {
	if config.APIKey == "" {
		return nil, fmt.Errorf("key for Google API is required")
	}

	// Default model name - in case config.ModelName doesn't have one
	modelName := DefaultGeminiModel

	// If model name is specified in options, use that instead
	if config.Options != nil {
		if customModel, ok := config.Options["model"].(string); ok && customModel != "" {
			modelName = customModel
		}
	}

	// Create a new client using the API key
	ctx := context.Background()
	client, err := genai.NewClient(ctx, option.WithAPIKey(config.APIKey))
	if err != nil {
		return nil, fmt.Errorf("failed to create Gemini client: %w", err)
	}

	// Create a generative model using the configured model name
	model := client.GenerativeModel(modelName)

	return &GoogleAIProvider{
		client:    client,
		model:     model,
		apiKey:    config.APIKey,
		config:    config,
		modelName: modelName,
	}, nil
}

// Name returns the provider name
func (p *GoogleAIProvider) Name() string {
	return "google"
}

// IsAvailable checks if the Google AI API is available
func (p *GoogleAIProvider) IsAvailable(ctx context.Context) bool {
	if p.client == nil || p.model == nil {
		return false
	}

	// A more thorough check would make a test API call here
	return true
}

// Execute sends a request to Google AI and returns the response
func (p *GoogleAIProvider) Execute(ctx context.Context, req *ExecuteRequest) (*ExecuteResponse, error) {
	isDebug := os.Getenv("PROMZ_DEBUG") == "true"
	if isDebug {
		// Log the incoming request details only in debug mode
		log.Printf("[GoogleAIProvider DEBUG] Executing request for prompt ID '%s', content length: %d, temp: %.2f, maxTokens: %d",
			req.PromptID, len(req.PromptContent), req.Temperature, req.MaxTokens)
	}
	// Set timeout for the request
	timeoutSecs := 30
	if p.config.TimeoutSecs > 0 {
		timeoutSecs = p.config.TimeoutSecs
	}

	ctx, cancel := context.WithTimeout(ctx, time.Duration(timeoutSecs)*time.Second)
	defer cancel()

	// Set temperature from request or default
	temperature := float32(0.7)
	if req.Temperature > 0 {
		temperature = req.Temperature
	}

	// Set max output tokens if specified
	var maxOutputTokens int32 = 2048
	if req.MaxTokens > 0 {
		maxOutputTokens = int32(req.MaxTokens)
	}

	// Use the default model and model name
	model := p.model
	modelName := p.modelName

	// IMPORTANT: This section should only be used for internal purposes (e.g., testing)
	// Client requests should NEVER provide their own API keys
	if req.APIKey != "" && req.APIKey != p.apiKey {
		// Create a new client with the custom API key
		// This should only happen for internal/admin requests, not client requests
		newClient, err := genai.NewClient(ctx, option.WithAPIKey(req.APIKey))
		if err != nil {
			return nil, fmt.Errorf("failed to create Gemini client with custom key: %w", err)
		}
		defer newClient.Close()

		// Use the same model name as the provider is configured with
		model = newClient.GenerativeModel(modelName)
	}

	// Configure the model with the request parameters
	model.Temperature = &temperature
	model.MaxOutputTokens = &maxOutputTokens

	// Create content parts for the request
	parts := []genai.Part{genai.Text(req.PromptContent)}

	// Generate content with a single request (no chat session)
	resp, err := model.GenerateContent(ctx, parts...)
	if err != nil {
		return nil, fmt.Errorf("google API error: %w", err)
	}

	if len(resp.Candidates) == 0 || len(resp.Candidates[0].Content.Parts) == 0 {
		return nil, ErrInvalidResponse
	}

	// Extract the response text
	var responseText string
	for _, part := range resp.Candidates[0].Content.Parts {
		if textPart, ok := part.(genai.Text); ok {
			responseText += string(textPart)
		}
	}

	// Calculate tokens (this is an estimate as Gemini doesn't directly provide token count)
	// Rough estimate: 1 token ≈ 4 characters for English text
	estimatedTokens := len(req.PromptContent)/4 + len(responseText)/4
	if isDebug {
		log.Printf("[GoogleAIProvider DEBUG] Google AI response for prompt ID '%s': estimated tokens: %d, chars: %d",
			req.PromptID, estimatedTokens, len(responseText))

		// Log a sample of the response text (first 100 chars)
		responseSample := responseText
		if len(responseSample) > 100 {
			responseSample = responseSample[:100] + "..."
		}
		log.Printf("[GoogleAIProvider DEBUG] Response sample: %s", responseSample)
	}

	return &ExecuteResponse{
		Text:     responseText,
		Provider: p.Name(),
		Tokens:   estimatedTokens,
		Metadata: map[string]string{
			"model": modelName,
		},
	}, nil
}

// Close releases any resources used by the provider
func (p *GoogleAIProvider) Close() {
	if p.client != nil {
		p.client.Close()
	}
}
