package llm

import (
	"context"
	"fmt"
	"log"
	"os"
	"time"

	"github.com/sa<PERSON><PERSON>nov/go-openai"
)

// OpenAIProvider implements the Provider interface for OpenAI
type OpenAIProvider struct {
	client *openai.Client
	config Config
}

// NewOpenAIProvider creates a new OpenAI provider instance
func NewOpenAIProvider(config Config) (*OpenAIProvider, error) {
	if config.APIKey == "" {
		return nil, fmt.Errorf("OpenAI API key is required")
	}

	clientConfig := openai.DefaultConfig(config.APIKey)
	if config.BaseURL != "" {
		clientConfig.BaseURL = config.BaseURL
	}

	client := openai.NewClientWithConfig(clientConfig)
	return &OpenAIProvider{
		client: client,
		config: config,
	}, nil
}

// Name returns the provider name
func (p *OpenAIProvider) Name() string {
	return "openai"
}

// IsAvailable checks if the OpenAI API is available
func (p *OpenAIProvider) IsAvailable(ctx context.Context) bool {
	// Simple model list request to check availability
	_, err := p.client.ListModels(ctx)
	return err == nil
}

// Execute sends a prompt to OpenAI and returns the response
func (p *OpenAIProvider) Execute(ctx context.Context, req *ExecuteRequest) (*ExecuteResponse, error) {
	// Log the incoming request
	log.Printf("OpenAI executing request for prompt ID '%s', content length: %d", 
		req.PromptID, len(req.PromptContent))
	timeoutSecs := 30
	if p.config.TimeoutSecs > 0 {
		timeoutSecs = p.config.TimeoutSecs
	}

	ctx, cancel := context.WithTimeout(ctx, time.Duration(timeoutSecs)*time.Second)
	defer cancel()

	// Set default values if not provided
	maxTokens := 2048
	if req.MaxTokens > 0 {
		maxTokens = req.MaxTokens
	}

	temperature := float32(0.7)
	if req.Temperature > 0 {
		temperature = req.Temperature
	}

	// Use the default client configuration
	client := p.client

	// IMPORTANT: This section should only be used for internal purposes (e.g., testing)
	// Client requests should NEVER provide their own API keys
	if req.APIKey != "" && req.APIKey != p.config.APIKey {
		// This should only happen for internal/admin requests, not client requests
		clientConfig := openai.DefaultConfig(req.APIKey)
		if p.config.BaseURL != "" {
			clientConfig.BaseURL = p.config.BaseURL
		}
		client = openai.NewClientWithConfig(clientConfig)
	}

	resp, err := client.CreateChatCompletion(
		ctx,
		openai.ChatCompletionRequest{
			Model: openai.GPT3Dot5Turbo,
			Messages: []openai.ChatCompletionMessage{
				{
					Role:    openai.ChatMessageRoleUser,
					Content: req.PromptContent, // Updated to use PromptContent instead of Prompt
				},
			},
			MaxTokens:   maxTokens,
			Temperature: float32(temperature),
		},
	)
	if err != nil {
		return nil, fmt.Errorf("openai api error: %w", err)
	}

	if len(resp.Choices) == 0 {
		return nil, ErrInvalidResponse
	}

	// Log response details
	responseText := resp.Choices[0].Message.Content
	log.Printf("OpenAI response for prompt ID '%s': %d tokens, %d chars", 
		req.PromptID, resp.Usage.TotalTokens, len(responseText))
	
	// Only log sample in debug mode
	if os.Getenv("PROMZ_DEBUG") == "true" {
		// Log a sample of the response text (first 100 chars)
		responseSample := responseText
		if len(responseSample) > 100 {
			responseSample = responseSample[:100] + "..."
		}
		log.Printf("Response sample: %s", responseSample)
	}
	
	return &ExecuteResponse{
		Text:     responseText,
		Tokens:   resp.Usage.TotalTokens,
		Provider: p.Name(),
	}, nil
}
