package llm

import (
	"errors"
)

// Common errors
var (
	ErrProviderUnavailable = errors.New("llm provider is unavailable")
	ErrInvalidResponse     = errors.New("invalid response from llm provider")
	ErrRateLimitExceeded   = errors.New("rate limit exceeded")
)

// Config holds the configuration for LLM providers
type Config struct {
	// APIKey is the authentication key for the LLM service
	APIKey string

	// BaseURL is the endpoint URL for the LLM API
	BaseURL string

	// Model specifies the model name to use (e.g., "gemini-2.0-flash")
	Model string

	// MaxTokens defines the maximum number of tokens in the generated response
	MaxTokens int

	// TimeoutSecs specifies the request timeout in seconds
	TimeoutSecs int

	// Defaults holds default parameter values for LLM requests
	Defaults map[string]interface{}

	// Parameters contains model-specific parameters
	Parameters map[string]interface{}

	// Options contains additional provider configuration options
	// For example, specific model options or provider-specific features
	Options map[string]interface{}
}

// QualityCheck defines criteria for validating LLM responses
type QualityCheck struct {
	MinLength    int      `json:"min_length"`
	MaxLength    int      `json:"max_length"`
	MinScore     float32  `json:"min_score"`
	RequiredTags []string `json:"required_tags"`
}
