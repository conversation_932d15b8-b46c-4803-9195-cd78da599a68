package llm

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"promz.ai/api/services"
)

// MockInstructionsService implements InstructionsService for testing
type MockInstructionsService struct {
	instructions    map[string]string
	instructionSets map[string]*services.InstructionSet
}

func NewMockInstructionsService() *MockInstructionsService {
	return &MockInstructionsService{
		instructions:    make(map[string]string),
		instructionSets: make(map[string]*services.InstructionSet),
	}
}

// GetInstructions returns mock instructions for testing
func (m *MockInstructionsService) GetInstructions(ctx context.Context, promptID string) (string, error) {
	if promptID != "" && m.instructions[promptID] != "" {
		return m.instructions[promptID], nil
	}
	return "", nil
}

// GetInstructionSet returns mock instruction sets for testing
func (m *MockInstructionsService) GetInstructionSet(ctx context.Context, promptID string) (*services.InstructionSet, error) {
	if promptID != "" && m.instructionSets[promptID] != nil {
		return m.instructionSets[promptID], nil
	}
	return &services.InstructionSet{}, nil
}

// MockProvider implements the Provider interface for testing
type MockProvider struct {
	name           string
	isAvailable    bool
	executeError   error
	executeResp    *ExecuteResponse
	executeCounter int
}

func (m *MockProvider) Name() string {
	return m.name
}

func (m *MockProvider) IsAvailable(ctx context.Context) bool {
	return m.isAvailable
}

func (m *MockProvider) Execute(ctx context.Context, req *ExecuteRequest) (*ExecuteResponse, error) {
	m.executeCounter++
	if m.executeError != nil {
		return nil, m.executeError
	}
	return m.executeResp, nil
}

// MockTemplateProcessor implements the TemplateProcessor interface for testing
type MockTemplateProcessor struct{}

// ProcessTemplate is a mock implementation that simply returns the template text
func (m *MockTemplateProcessor) ProcessTemplate(templateText string, variables map[string]interface{}) (string, error) {
	// For testing, we'll just return the original text
	// In a real implementation, this would process variables
	return templateText, nil
}

// MockFileNameGenerator implements the FileNameGenerator interface for testing
type MockFileNameGenerator struct {
	suggestedFileName string
	err               error
}

// GenerateSuggestedFileName is a mock implementation that returns a predefined file name
func (m *MockFileNameGenerator) GenerateSuggestedFileName(
	promptContent string,
	variables map[string]interface{},
) (string, error) {
	if m.err != nil {
		return "", m.err
	}
	if m.suggestedFileName != "" {
		return m.suggestedFileName, nil
	}
	return "mock-filename.txt", nil
}

// Note: MockModelSelectionService is now imported from mock_model_selection.go
// It implements the ModelSelectionService interface with the following methods:
// - GetEligibleModels(ctx, userCtx, preferredModel) - Returns eligible models for a tier
// - SelectModelForProvider(ctx, userCtx, provider, preferredModel) - Returns a model for a provider
// - SelectEligibleModelsInOrder(ctx, userCtx, preferredModel) - Returns models in priority order

func TestExecutor_ExecuteWithFallback(t *testing.T) {
	// Create mock instruction service for all tests
	instrSvc := NewMockInstructionsService()
	instrSvc.instructions["test-prompt"] = "You are a helpful assistant."
	instrSvc.instructionSets["test-prompt"] = &services.InstructionSet{
		InstructionText: "You are a helpful assistant.",
		Params: services.InstructionParams{
			MaxTokens:   100,
			Temperature: 0.7,
		},
	}

	// Create mock template processor
	tmplProc := &MockTemplateProcessor{}

	// Create mock model selection service with support for the new interface
	modelSvc := NewMockModelSelectionService()

	// Create mock file name generator
	fileNameGen := &MockFileNameGenerator{
		suggestedFileName: "test-prompt-20250407-var1-var2.txt",
	}

	tests := []struct {
		name          string
		providers     []Provider
		request       *ExecuteRequest
		expectedResp  *ExecuteResponse
		expectedError error
		expectedCalls map[string]int
	}{
		{
			name: "success with first provider",
			providers: []Provider{
				&MockProvider{
					name:        "provider1",
					isAvailable: true,
					executeResp: &ExecuteResponse{
						Text:     "This is a long enough response that meets the validation criteria",
						Tokens:   10,
						Provider: "provider1",
					},
				},
				&MockProvider{
					name:        "provider2",
					isAvailable: true,
				},
			},
			request: &ExecuteRequest{
				PromptContent: "test prompt",
				// Not setting Options to ensure we test the nil case properly
			},
			expectedResp: &ExecuteResponse{
				Text:              "This is a long enough response that meets the validation criteria",
				Tokens:            10,
				Provider:          "provider1",
				SuggestedFileName: "test-prompt-20250407-var1-var2.txt",
			},
			expectedCalls: map[string]int{
				"provider1": 1,
				"provider2": 0,
			},
		},
		{
			name: "fallback to second provider",
			providers: []Provider{
				&MockProvider{
					name:         "provider1",
					isAvailable:  true,
					executeError: errors.New("provider1 error"),
				},
				&MockProvider{
					name:        "provider2",
					isAvailable: true,
					executeResp: &ExecuteResponse{
						Text:     "This is a fallback response that is long enough to pass validation",
						Tokens:   15,
						Provider: "provider2",
					},
				},
			},
			request: &ExecuteRequest{
				PromptContent: "test prompt",
			},
			expectedResp: &ExecuteResponse{
				Text:              "This is a fallback response that is long enough to pass validation",
				Tokens:            15,
				Provider:          "provider2",
				SuggestedFileName: "test-prompt-20250407-var1-var2.txt",
			},
			expectedCalls: map[string]int{
				"provider1": 1,
				"provider2": 1,
			},
		},
		{
			name: "all providers fail",
			providers: []Provider{
				&MockProvider{
					name:         "provider1",
					isAvailable:  true,
					executeError: errors.New("provider1 error"),
				},
				&MockProvider{
					name:         "provider2",
					isAvailable:  true,
					executeError: errors.New("provider2 error"),
				},
			},
			request: &ExecuteRequest{
				PromptContent: "test prompt",
			},
			expectedError: errors.New("all providers failed"),
			expectedCalls: map[string]int{
				"provider1": 1,
				"provider2": 1,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Pass the instruction service and template processor
			// Use true for useRealExecution to test model selection
			executor := NewExecutor(instrSvc, tmplProc, modelSvc, fileNameGen, true, tt.providers...)
			resp, err := executor.ExecuteWithFallback(context.Background(), tt.request)

			if tt.expectedError != nil {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedResp, resp)
			}

			// Check call counts
			for _, p := range tt.providers {
				mock := p.(*MockProvider)
				expectedCalls := tt.expectedCalls[mock.name]
				assert.Equal(t, expectedCalls, mock.executeCounter,
					"unexpected number of calls for provider %s", mock.name)
			}
		})
	}
}

func TestExecutor_GetAvailableProviders(t *testing.T) {
	// Create mock instruction service
	instrSvc := NewMockInstructionsService()

	// Create mock template processor
	tmplProc := &MockTemplateProcessor{}

	// Create mock model selection service with support for the new interface
	modelSvc := NewMockModelSelectionService()

	// Create mock file name generator
	fileNameGen := &MockFileNameGenerator{}

	providers := []Provider{
		&MockProvider{
			name:        "provider1",
			isAvailable: true,
		},
		&MockProvider{
			name:        "provider2",
			isAvailable: false,
		},
		&MockProvider{
			name:        "provider3",
			isAvailable: true,
		},
	}

	// Pass both instruction service and template processor
	// Use false for useRealExecution to bypass model selection
	executor := NewExecutor(instrSvc, tmplProc, modelSvc, fileNameGen, false, providers...)
	available := executor.GetAvailableProviders(context.Background())

	assert.Equal(t, []string{"provider1", "provider3"}, available)
}

func TestExecuteWithInstructions(t *testing.T) {
	// Setup
	instrSvc := NewMockInstructionsService()
	instrSvc.instructions["prompt123"] = "You are a helpful assistant."

	// Create mock template processor
	tmplProc := &MockTemplateProcessor{}

	// Create mock model selection service with support for the new interface
	modelSvc := NewMockModelSelectionService()

	// Create mock file name generator
	fileNameGen := &MockFileNameGenerator{}

	mockProvider := &MockProvider{
		name:        "mock",
		isAvailable: true,
		executeResp: &ExecuteResponse{
			Text:     "I'll help you with that.",
			Provider: "mock",
			Tokens:   15,
		},
	}

	// Add the useRealExecution parameter (false to bypass model selection)
	executor := NewExecutor(instrSvc, tmplProc, modelSvc, fileNameGen, false, mockProvider)

	// Execute
	req := &ExecuteRequest{
		PromptID:      "prompt123",
		PromptContent: "Tell me about apples.",
	}
	resp, err := executor.ExecuteWithFallback(context.Background(), req)

	// Verify
	assert.NoError(t, err)
	assert.Equal(t, "I'll help you with that.", resp.Text)
	assert.Equal(t, "mock", resp.Provider)
}
