package llm

import (
	"context"
)

// MockModelSelectionService implements the ModelSelectionService interface for testing
type MockModelSelectionService struct{}

// NewMockModelSelectionService creates a new mock model selection service
func NewMockModelSelectionService() *MockModelSelectionService {
	return &MockModelSelectionService{}
}

// SelectModelForProvider returns a mock model for testing
func (m *MockModelSelectionService) SelectModelForProvider(
	ctx context.Context,
	userCtx *UserContext,
	provider string,
	preferredModel string,
) (ModelInfo, error) {
	// Create a ModelInfo with the correct fields based on the actual struct
	return ModelInfo{
		ModelID:    "test-model",
		ProviderID: provider,
	}, nil
}

// GetEligibleModels returns eligible models for a specific tier
func (m *MockModelSelectionService) GetEligibleModels(
	ctx context.Context,
	userCtx *UserContext,
	preferredModel string,
) ([]ModelInfo, error) {
	// Return a simple list of mock models
	models := []ModelInfo{
		{
			ModelID:    "test-model-1",
			ProviderID: "test-provider",
		},
		{
			ModelID:    "test-model-2",
			ProviderID: "test-provider",
		},
	}

	// If preferred model is specified, prioritize it
	if preferredModel != "" {
		for i, model := range models {
			if model.ModelID == preferredModel {
				// Move this model to the front
				models = append([]ModelInfo{model}, append(models[:i], models[i+1:]...)...)
				break
			}
		}
	}

	return models, nil
}

// SelectEligibleModelsInOrder returns all eligible models sorted by priority
func (m *MockModelSelectionService) SelectEligibleModelsInOrder(
	ctx context.Context,
	userCtx *UserContext,
	preferredModel string,
) ([]ModelInfo, error) {
	// Simply delegate to GetEligibleModels
	return m.GetEligibleModels(ctx, userCtx, preferredModel)
}
