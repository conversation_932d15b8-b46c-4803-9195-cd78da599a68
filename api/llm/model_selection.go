package llm

import (
	"context"
	"fmt"
	"log"

	"promz.ai/api/internal/repository/db"
)

// UserTier represents subscription levels for users
type UserTier string

const (
	// UserTierFree represents users on the free tier
	UserTierFree UserTier = "free"
	// UserTierTrial represents users on a pro trial
	UserTierTrial UserTier = "trial"
	// UserTierPro represents paid pro users
	UserTierPro UserTier = "pro"
)

// UserContext contains information about the user making the request
type UserContext struct {
	// UserID is the unique identifier for the user
	UserID string
	// Tier represents the user's subscription tier
	Tier UserTier
}

// ModelInfo contains information about an LLM model
type ModelInfo struct {
	// ProviderID is the LLM provider identifier (e.g., "google", "openai")
	ProviderID string
	// ModelID is the specific model identifier (e.g., "gemini-2.0-flash", "gpt-4")
	ModelID string
	// IsFree indicates if this model is available on the free tier
	IsFree bool
	// Capabilities indicates what the model can do (e.g., coding, vision)
	Capabilities []string
}

// ModelSelectionService handles selecting appropriate LLM models based on user tier
type ModelSelectionService interface {
	// GetEligibleModels returns models available to a user based on their tier
	GetEligibleModels(ctx context.Context, userCtx *UserContext, preferredModel string) ([]ModelInfo, error)

	// SelectModelForProvider chooses the best model for a given provider and user tier
	// If preferredModel is specified, it will be validated against the user's tier
	SelectModelForProvider(ctx context.Context, userCtx *UserContext, providerID string, preferredModel string) (ModelInfo, error)

	// SelectEligibleModelsInOrder returns all eligible models sorted by priority
	// This allows for fallback if one model fails
	SelectEligibleModelsInOrder(ctx context.Context, userCtx *UserContext, preferredModel string) ([]ModelInfo, error)
}

// modelSelectionService implements ModelSelectionService
type modelSelectionService struct {
	db db.SupabaseClientInterface
}

// NewModelSelectionService creates a new model selection service
func NewModelSelectionService(db db.SupabaseClientInterface) ModelSelectionService {
	return &modelSelectionService{
		db: db,
	}
}

// GetEligibleModels returns models available to a user based on their tier
func (s *modelSelectionService) GetEligibleModels(ctx context.Context, userCtx *UserContext, preferredModel string) ([]ModelInfo, error) {
	if userCtx == nil {
		return nil, fmt.Errorf("user context cannot be nil")
	}

	// Get eligible models from the database using the Supabase client
	models, err := s.db.GetEligibleModelsForTier(ctx, string(userCtx.Tier), preferredModel)
	if err != nil {
		return nil, fmt.Errorf("failed to get eligible models: %w", err)
	}

	// Convert from generic interface{} to our ModelInfo struct
	result := make([]ModelInfo, 0, len(models))
	for _, model := range models {
		modelMap, ok := model.(map[string]interface{})
		if !ok {
			log.Printf("Warning: model data not in expected format, skipping")
			continue
		}

		modelInfo, err := convertToModelInfo(modelMap)
		if err != nil {
			log.Printf("Warning: failed to convert model data: %v", err)
			continue
		}

		result = append(result, modelInfo)
	}

	if len(result) == 0 && len(models) > 0 {
		return nil, fmt.Errorf("failed to process any models due to format issues")
	}

	return result, nil
}

// SelectModelForProvider chooses the best model for a given provider and user tier
func (s *modelSelectionService) SelectModelForProvider(
	ctx context.Context,
	userCtx *UserContext,
	providerID string,
	preferredModel string,
) (ModelInfo, error) {
	if userCtx == nil {
		return ModelInfo{}, fmt.Errorf("user context cannot be nil")
	}

	// Special case for mock provider - maintain existing behavior
	if providerID == "mock" {
		// Get the best model from the database using the Supabase client
		modelData, err := s.db.GetBestModelForProvider(ctx, string(userCtx.Tier), providerID, preferredModel)
		if err != nil {
			return ModelInfo{}, fmt.Errorf("failed to get model for provider: %w", err)
		}

		// Convert from generic map to our ModelInfo struct
		modelInfo, err := convertToModelInfo(modelData)
		if err != nil {
			return ModelInfo{}, fmt.Errorf("failed to convert model data: %w", err)
		}

		return modelInfo, nil
	}

	// For other providers, get all eligible models and filter by provider
	eligibleModels, err := s.SelectEligibleModelsInOrder(ctx, userCtx, preferredModel)
	if err != nil {
		return ModelInfo{}, fmt.Errorf("failed to get eligible models: %w", err)
	}

	// Find the first model that matches the requested provider
	for _, model := range eligibleModels {
		if model.ProviderID == providerID {
			return model, nil
		}
	}

	return ModelInfo{}, fmt.Errorf("no eligible %s models available for %s tier", providerID, userCtx.Tier)
}

// SelectEligibleModelsInOrder returns all eligible models sorted by priority
func (s *modelSelectionService) SelectEligibleModelsInOrder(ctx context.Context, userCtx *UserContext, preferredModel string) ([]ModelInfo, error) {
	if userCtx == nil {
		return nil, fmt.Errorf("user context cannot be nil")
	}

	// Get eligible models sorted by priority
	return s.GetEligibleModels(ctx, userCtx, preferredModel)
}

// convertToModelInfo converts a generic map to a ModelInfo struct
func convertToModelInfo(modelData map[string]interface{}) (ModelInfo, error) {
	var result ModelInfo

	// Extract provider_id
	if providerID, ok := modelData["provider_id"].(string); ok {
		result.ProviderID = providerID
	} else {
		return ModelInfo{}, fmt.Errorf("missing or invalid provider_id field")
	}

	// Extract model_id
	if modelID, ok := modelData["model_id"].(string); ok {
		result.ModelID = modelID
	} else {
		return ModelInfo{}, fmt.Errorf("missing or invalid model_id field")
	}

	// Extract is_free (with fallback to false)
	if isFree, ok := modelData["is_free"].(bool); ok {
		result.IsFree = isFree
	}

	// Extract capabilities (this may be a JSON array)
	if caps, ok := modelData["capabilities"]; ok && caps != nil {
		// Try to convert capabilities to a string slice
		if capsArray, ok := caps.([]interface{}); ok {
			for _, cap := range capsArray {
				if capStr, ok := cap.(string); ok {
					result.Capabilities = append(result.Capabilities, capStr)
				}
			}
		}
	}

	return result, nil
}
