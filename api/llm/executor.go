package llm

import (
	"context"
	"fmt"
	"log"
	"sync"

	"promz.ai/api/services"
)

// ExecuteRequest contains all information needed to execute a prompt
type ExecuteRequest struct {
	// Identification fields - which prompt/category to use
	PromptID   string `json:"prompt_id,omitempty"`
	CategoryID string `json:"category_id,omitempty"`

	// Content fields - what the user is asking
	PromptContent string          `json:"prompt_content"`           // User's input text
	SourceContent []SourceContent `json:"source_content,omitempty"` // Additional context

	// Variables for template substitution
	Variables map[string]interface{} `json:"variables,omitempty"` // Variables for template substitution

	// Model parameters - how to generate the response
	MaxTokens   int     `json:"max_tokens,omitempty"`  // Controls response length
	Temperature float32 `json:"temperature,omitempty"` // Controls randomness/creativity

	// Provider selection
	Provider string `json:"provider,omitempty"` // Specific LLM provider to use (e.g., "google", "openai")

	// Additional options
	Options map[string]string `json:"options,omitempty"`

	// Authentication
	// WARNING: This should NOT be populated from client requests
	// This field is for internal use only, e.g., for testing or admin tools
	// Normal client requests should use the provider's configured API key from environment variables
	APIKey string `json:"api_key,omitempty"` // API key for the LLM provider

	// User information for selecting appropriate models
	UserContext *UserContext `json:"-"` // Not serialized in JSON
}

// ExecuteResponse represents the response from an LLM execution
type ExecuteResponse struct {
	Text              string            `json:"text"`
	Provider          string            `json:"provider"`
	Metadata          map[string]string `json:"metadata,omitempty"`
	Tokens            int               `json:"tokens,omitempty"`
	FinishReason      string            `json:"finish_reason,omitempty"`
	SuggestedFileName string            `json:"suggested_file_name,omitempty"`
}

// SourceContent represents content from a file or text input
type SourceContent struct {
	Type        string `json:"type"` // "file", "text", etc.
	Content     string `json:"content"`
	FileName    string `json:"file_name,omitempty"`
	ContentType string `json:"content_type,omitempty"`
}

// Provider defines the interface for LLM providers
type Provider interface {
	Name() string
	IsAvailable(ctx context.Context) bool
	Execute(ctx context.Context, req *ExecuteRequest) (*ExecuteResponse, error)
}

// TemplateProcessor defines the interface for processing templates with variables
type TemplateProcessor interface {
	ProcessTemplate(templateText string, variables map[string]interface{}) (string, error)
}

// FileNameGenerator generates a suggested file name based on the prompt execution
type FileNameGenerator interface {
	GenerateSuggestedFileName(promptContent string, variables map[string]interface{}) (string, error)
}

// Executor handles prompt execution with multiple LLM providers and fallback logic
type Executor struct {
	providers         []Provider
	instrSvc          InstructionsService
	tmplProc          TemplateProcessor
	modelSelectionSvc ModelSelectionService
	fileNameGen       FileNameGenerator
	useRealExecution  bool
	mu                sync.RWMutex
}

// InstructionsService defines the interface for fetching and processing instructions
type InstructionsService interface {
	GetInstructions(ctx context.Context, promptID string) (string, error)
	GetInstructionSet(ctx context.Context, promptID string) (*services.InstructionSet, error)
}

// NewExecutor creates a new executor with the given providers
func NewExecutor(instrSvc InstructionsService, tmplProc TemplateProcessor,
	modelSelectionSvc ModelSelectionService, fileNameGen FileNameGenerator, useRealExecution bool, providers ...Provider,
) *Executor {
	return &Executor{
		providers:         providers,
		instrSvc:          instrSvc,
		tmplProc:          tmplProc,
		modelSelectionSvc: modelSelectionSvc,
		fileNameGen:       fileNameGen,
		useRealExecution:  useRealExecution,
	}
}

// AddProvider adds a new LLM provider to the executor
func (e *Executor) AddProvider(p Provider) {
	e.mu.Lock()
	defer e.mu.Unlock()
	e.providers = append(e.providers, p)
}

// ExecuteWithFallback executes a prompt with fallback support
func (e *Executor) ExecuteWithFallback(ctx context.Context, req *ExecuteRequest) (*ExecuteResponse, error) {
	e.mu.RLock()
	defer e.mu.RUnlock()

	if len(e.providers) == 0 {
		return nil, fmt.Errorf("no LLM providers available")
	}

	// Either PromptId or PromptContent must be provided
	if req.PromptID == "" && req.PromptContent == "" {
		return nil, fmt.Errorf("either prompt_id or prompt_content must be provided")
	}

	// Generate suggested file name if we have a file name generator
	var suggestedFileName string
	var err error
	if e.fileNameGen != nil {
		suggestedFileName, err = e.fileNameGen.GenerateSuggestedFileName(req.PromptContent, req.Variables)
		if err != nil {
			log.Printf("Warning: Failed to generate file name: %v", err)
		}
	}

	// If we have a promptID, get instructions and parameter overrides
	if req.PromptID != "" {
		instructionSet, err := e.instrSvc.GetInstructionSet(ctx, req.PromptID)
		if err != nil {
			log.Printf("ERROR: Failed to get instructions: %v", err)
			return nil, fmt.Errorf("failed to get instructions: %w", err)
		}

		// Apply instructionSet parameters with proper precedence:
		// 1. Request parameters (highest priority - already set in req)
		// 2. Instruction parameters from promptID (applied here)
		// 3. Default values (applied by provider)

		// Only apply instruction parameters if not overridden by request
		if instructionSet.Params.MaxTokens > 0 && req.MaxTokens <= 0 {
			req.MaxTokens = instructionSet.Params.MaxTokens
		}

		if instructionSet.Params.Temperature > 0 && req.Temperature <= 0 {
			req.Temperature = instructionSet.Params.Temperature
		}

		// Check if provider is specified in the request (highest priority)
		// If not, check if provider is specified in the dedicated provider field (second priority)
		// If not, check if provider is specified in the instruction parameters (third priority)
		if req.Provider == "" {
			if instructionSet.Provider != "" {
				req.Provider = instructionSet.Provider
				log.Printf("Using provider '%s' from instruction set's provider field", instructionSet.Provider)
			} else if instructionSet.Params.Options != nil {
				if providerName, ok := instructionSet.Params.Options["provider"].(string); ok && providerName != "" {
					req.Provider = providerName
					log.Printf("Using provider '%s' from instruction parameters", providerName)
				}
			}
		}

		// Merge options if available and not overridden
		if instructionSet.Params.Options != nil {
			// Initialize req.Options if needed
			if req.Options == nil {
				req.Options = make(map[string]string)
			}

			for k, v := range instructionSet.Params.Options {
				// Only add options that aren't already set
				if _, exists := req.Options[k]; !exists {
					// Convert interface{} to string as needed for req.Options
					if strValue, ok := v.(string); ok {
						req.Options[k] = strValue
					}
				}
			}
		}

		// Process variables in the instruction text if available
		if instructionSet.InstructionText != "" {
			// Use len() directly without nil check since len() on nil maps returns 0
			if len(req.Variables) > 0 {
				processedInstructions, err := e.tmplProc.ProcessTemplate(instructionSet.InstructionText, req.Variables)
				if err != nil {
					// Continue with original instructions if variable processing fails
					req.PromptContent = fmt.Sprintf("%s\n\n%s", instructionSet.InstructionText, req.PromptContent)
				} else {
					// Use processed instructions with variables substituted
					req.PromptContent = fmt.Sprintf("%s\n\n%s", processedInstructions, req.PromptContent)
				}
			} else {
				// No variables to process, just prepend instructions
				req.PromptContent = fmt.Sprintf("%s\n\n%s", instructionSet.InstructionText, req.PromptContent)
			}
		}
	}

	// Process variables in the prompt content if available
	if len(req.Variables) > 0 {
		processedContent, err := e.tmplProc.ProcessTemplate(req.PromptContent, req.Variables)
		if err != nil {
			log.Printf("WARNING: Failed to process variables in prompt content: %v", err)
			// Continue with original content if variable processing fails
		} else {
			// Use processed content with variables substituted
			req.PromptContent = processedContent
		}
	}

	// Add source context if available
	if len(req.SourceContent) > 0 {
		contextStr := prepareSourceContext(req.SourceContent)
		if contextStr != "" {
			req.PromptContent = fmt.Sprintf("Context:\n%s\n\nQuery:\n%s", contextStr, req.PromptContent)
		}
	}

	var lastErr error

	// Set default user context if none provided
	if req.UserContext == nil {
		req.UserContext = &UserContext{
			Tier: UserTierFree, // Default to free tier if no user context provided
		}
	}

	// If a specific provider is requested, try that first
	if req.Provider != "" {
		log.Printf("Client requested specific provider: %s", req.Provider)
		// Try the requested provider first
		for _, provider := range e.providers {
			if provider.Name() == req.Provider && provider.IsAvailable(ctx) {
				// Skip model selection when not in real execution mode
				if e.useRealExecution && e.modelSelectionSvc != nil {
					// Select the appropriate model based on user tier
					var preferredModel string
					if req.Options != nil {
						preferredModel = req.Options["model"]
					}

					// If we have a model selection service, use it
					modelInfo, err := e.modelSelectionSvc.SelectModelForProvider(
						ctx, req.UserContext, req.Provider, preferredModel)
					if err != nil {
						lastErr = err
						log.Printf("Model selection for provider '%s' failed: %v", req.Provider, err)
						continue // Try next provider
					}

					// Set the selected model in options
					if req.Options == nil {
						req.Options = make(map[string]string)
					}
					req.Options["model"] = modelInfo.ModelID

					log.Printf("Selected model '%s' for provider '%s' (user tier: %s)",
						modelInfo.ModelID, req.Provider, req.UserContext.Tier)
				}

				// Execute using the selected model
				resp, err := provider.Execute(ctx, req)
				if err != nil {
					lastErr = err
					log.Printf("Requested provider '%s' failed: %v", req.Provider, err)
					break // Don't try other providers with the same name
				}

				// Validate response if quality check is provided
				if req.Options != nil {
					if err := validateResponse(resp, req.Options); err != nil {
						lastErr = err
						log.Printf("Response validation failed for provider '%s': %v", req.Provider, err)
						break
					}
				}

				resp.SuggestedFileName = suggestedFileName
				return resp, nil
			}
		}

		// If we get here, the requested provider either wasn't found or failed
		log.Printf("Requested provider '%s' not available or failed, falling back to default providers", req.Provider)
	}

	// Try all providers in the order they were registered (with Google as default)
	for _, provider := range e.providers {
		// Skip provider if it's not available
		if !provider.IsAvailable(ctx) {
			continue
		}

		// Skip model selection when not in real execution mode
		if e.useRealExecution && e.modelSelectionSvc != nil {
			// Default to any model for this provider
			var preferredModel string
			if req.Options != nil {
				preferredModel = req.Options["model"]
			}

			modelInfo, err := e.modelSelectionSvc.SelectModelForProvider(
				ctx, req.UserContext, provider.Name(), preferredModel)
			if err != nil {
				lastErr = err
				log.Printf("Model selection for provider '%s' failed: %v", provider.Name(), err)
				continue // Try next provider
			}

			// Set the selected model in options
			if req.Options == nil {
				req.Options = make(map[string]string)
			}
			req.Options["model"] = modelInfo.ModelID

			log.Printf("Selected model '%s' for provider '%s' (user tier: %s)",
				modelInfo.ModelID, provider.Name(), req.UserContext.Tier)
		}

		resp, err := provider.Execute(ctx, req)
		if err != nil {
			lastErr = err
			log.Printf("Provider '%s' failed: %v", provider.Name(), err)
			continue
		}

		// Validate response if quality check is provided
		if req.Options != nil {
			if err := validateResponse(resp, req.Options); err != nil {
				lastErr = err
				log.Printf("Response validation failed for provider '%s': %v", provider.Name(), err)
				continue
			}
		}

		// Successfully executed with this provider
		resp.SuggestedFileName = suggestedFileName
		return resp, nil
	}

	return nil, fmt.Errorf("all providers failed, last error: %v", lastErr)
}

// prepareSourceContext formats the source content for inclusion in the prompt
func prepareSourceContext(sources []SourceContent) string {
	if len(sources) == 0 {
		return ""
	}

	result := ""
	for i, src := range sources {
		if i > 0 {
			result += "\n---\n"
		}

		if src.FileName != "" {
			result += fmt.Sprintf("Source: %s\n", src.FileName)
		}

		result += src.Content
	}

	return result
}

// validateResponse checks if the response meets the quality criteria
func validateResponse(resp *ExecuteResponse, options map[string]string) error {
	// If options is nil, skip validation
	if options == nil {
		return nil
	}

	// Example validation criteria
	minLength := 10 // Minimum response length
	if len(resp.Text) < minLength {
		return fmt.Errorf("response too short (length: %d, minimum: %d)", len(resp.Text), minLength)
	}

	return nil
}

// GetAvailableProviders returns a list of currently available providers
func (e *Executor) GetAvailableProviders(ctx context.Context) []string {
	e.mu.RLock()
	defer e.mu.RUnlock()

	var available []string
	for _, p := range e.providers {
		if p.IsAvailable(ctx) {
			available = append(available, p.Name())
		}
	}
	return available
}
