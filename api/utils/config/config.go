package config

import (
	"fmt"
	"os"
	"strings"
)

// TierConfig defines processing limits for different license tiers
type TierConfig struct {
	MaxFileSizeBytes   int64
	MaxStorageDays     int
	ProcessingPriority int
	MaxTokens          int   // Maximum number of tokens to process per file
	MaxTokensPerFile   int64 // Maximum number of tokens to process per file (legacy)
}

// Config holds all configuration for the application
type Config struct {
	SupabaseURL      string
	SupabaseKey      string
	Environment      string
	Debug            bool
	UseRealExecution bool
	// New fields
	DBConnection  string
	GoogleAPIKey  string
	OpenAIAPIKey  string
	Port          string
	StorageBucket string
	// Base URL for API endpoints
	APIBaseURL    string
	// gRPC server address
	GRPCAddress   string
	// TLS configuration
	UseTLS        bool
	TLSCertPath   string
	TLSKeyPath    string
	// Processing tier limits
	TierLimits map[string]TierConfig
	// Trusted proxies for the Gin framework
	TrustedProxies []string
}

// New creates a new configuration with default values
func New() *Config {
	// Parse trusted proxies from environment variable
	trustedProxiesStr := os.Getenv("PROMZ_TRUSTED_PROXIES")
	var trustedProxies []string
	if trustedProxiesStr != "" {
		// Split by comma
		trustedProxies = strings.Split(trustedProxiesStr, ",")
		// Trim whitespace
		for i := range trustedProxies {
			trustedProxies[i] = strings.TrimSpace(trustedProxies[i])
		}
	}
	supabaseURL := os.Getenv("PROMZ_SUPABASE_URL")
	supabaseKey := os.Getenv("PROMZ_SUPABASE_KEY")
	dbConnection := os.Getenv("PROMZ_DB_CONNECTION")
	googleAPIKey := os.Getenv("PROMZ_GOOGLE_API_KEY")
	openAIAPIKey := os.Getenv("PROMZ_OPENAI_API_KEY")
	port := os.Getenv("PORT")
	storageBucket := os.Getenv("PROMZ_STORAGE_BUCKET")
	apiBaseURL := os.Getenv("PROMZ_API_BASE_URL")
	grpcPort := os.Getenv("GRPC_PORT")
	tlsCertPath := os.Getenv("PROMZ_TLS_CERT_PATH")
	tlsKeyPath := os.Getenv("PROMZ_TLS_KEY_PATH")
	useTLS := os.Getenv("PROMZ_USE_TLS") == "true"

	env := os.Getenv("APP_ENV")
	if env == "" {
		env = "development"
	}

	if port == "" {
		port = "8080"
	}

	if grpcPort == "" {
		grpcPort = "9090"
	}
	grpcAddress := ":" + grpcPort

	if storageBucket == "" {
		storageBucket = "promz-content-upload"
	}

	// Set default API base URL based on environment
	if apiBaseURL == "" {
		if env == "production" {
			apiBaseURL = "https://api.promz.ai"
		} else {
			apiBaseURL = fmt.Sprintf("http://localhost:%s", port)
		}
	}

	debug := env == "development"

	// Determine if we should use real execution
	// In development mode, default to mock mode (false)
	// In production mode, default to real execution (true)
	useRealExecution := os.Getenv("PROMZ_USE_REAL_EXECUTION") == "true"
	if env == "production" && os.Getenv("PROMZ_USE_REAL_EXECUTION") != "false" {
		useRealExecution = true
	}

	return &Config{
		SupabaseURL:      supabaseURL,
		SupabaseKey:      supabaseKey,
		DBConnection:     dbConnection,
		GoogleAPIKey:     googleAPIKey,
		OpenAIAPIKey:     openAIAPIKey,
		Port:             port,
		Environment:      env,
		Debug:            debug,
		UseRealExecution: useRealExecution,
		StorageBucket:    storageBucket,
		APIBaseURL:       apiBaseURL,
		GRPCAddress:      grpcAddress,
		UseTLS:           useTLS,
		TLSCertPath:      tlsCertPath,
		TLSKeyPath:       tlsKeyPath,
		TrustedProxies:   trustedProxies,
		TierLimits: map[string]TierConfig{
			"free": {
				MaxFileSizeBytes:   10 * 1024 * 1024, // 10MB
				MaxStorageDays:     1,
				ProcessingPriority: 0,
				MaxTokens:          100000, // 100K tokens for free tier
				MaxTokensPerFile:   100000, // 100K tokens for free tier (legacy)
			},
			"pro": {
				MaxFileSizeBytes:   50 * 1024 * 1024, // 50MB
				MaxStorageDays:     7,
				ProcessingPriority: 1,
				MaxTokens:          500000, // 500K tokens for pro tier
				MaxTokensPerFile:   500000, // 500K tokens for pro tier (legacy)
			},
			"enterprise": {
				MaxFileSizeBytes:   200 * 1024 * 1024, // 200MB
				MaxStorageDays:     30,
				ProcessingPriority: 2,
				MaxTokens:          2000000, // 2M tokens for enterprise tier
				MaxTokensPerFile:   2000000, // 2M tokens for enterprise tier (legacy)
			},
		},
	}
}

// Validate checks if all required configuration values are set
func (c *Config) Validate() error {
	if c.SupabaseURL == "" || c.SupabaseKey == "" {
		return fmt.Errorf("PROMZ_SUPABASE_URL and PROMZ_SUPABASE_KEY are required")
	}

	return nil
}

// ValidateDBConnection checks if database connection is configured
func (c *Config) ValidateDBConnection() error {
	if c.DBConnection == "" {
		return fmt.Errorf("PROMZ_DB_CONNECTION is required")
	}

	return nil
}

// HasGoogleAI checks if Google AI API key is configured
func (c *Config) HasGoogleAI() bool {
	return c.GoogleAPIKey != ""
}

// HasOpenAI checks if OpenAI API key is configured
func (c *Config) HasOpenAI() bool {
	return c.OpenAIAPIKey != ""
}

// IsDevelopment checks if the environment is development
func (c *Config) IsDevelopment() bool {
	return c.Environment == "development"
}

// IsProduction checks if the environment is production
func (c *Config) IsProduction() bool {
	return c.Environment == "production"
}

// GetTierConfig returns the configuration for a specific license tier
func (c *Config) GetTierConfig(tier string) TierConfig {
	if config, exists := c.TierLimits[tier]; exists {
		return config
	}
	// Default to free tier if the tier doesn't exist
	return c.TierLimits["free"]
}
