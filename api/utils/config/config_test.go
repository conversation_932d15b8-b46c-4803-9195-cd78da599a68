package config

import (
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNew(t *testing.T) {
	// Save the original environment variables
	originalSupabaseURL := os.Getenv("PROMZ_SUPABASE_URL")
	originalSupabaseKey := os.Getenv("PROMZ_SUPABASE_KEY")
	originalAppEnv := os.Getenv("APP_ENV")
	originalDBConnection := os.Getenv("PROMZ_DB_CONNECTION")
	originalGoogleAPIKey := os.Getenv("PROMZ_GOOGLE_API_KEY")
	originalOpenAIAPIKey := os.Getenv("PROMZ_OPENAI_API_KEY")
	originalPort := os.Getenv("PORT")
	originalUseRealExecution := os.Getenv("PROMZ_USE_REAL_EXECUTION")
	
	// Restore the original environment variables after the test
	defer func() {
		os.Setenv("PROMZ_SUPABASE_URL", originalSupabaseURL)
		os.Setenv("PROMZ_SUPABASE_KEY", originalSupabaseKey)
		os.Setenv("APP_ENV", originalAppEnv)
		os.Setenv("PROMZ_DB_CONNECTION", originalDBConnection)
		os.Setenv("PROMZ_GOOGLE_API_KEY", originalGoogleAPIKey)
		os.Setenv("PROMZ_OPENAI_API_KEY", originalOpenAIAPIKey)
		os.Setenv("PORT", originalPort)
		os.Setenv("PROMZ_USE_REAL_EXECUTION", originalUseRealExecution)
	}()
	
	// Test with all environment variables set
	os.Setenv("PROMZ_SUPABASE_URL", "https://test.supabase.co")
	os.Setenv("PROMZ_SUPABASE_KEY", "test-key")
	os.Setenv("APP_ENV", "production")
	os.Setenv("PROMZ_DB_CONNECTION", "postgres://user:pass@localhost:5432/db")
	os.Setenv("PROMZ_GOOGLE_API_KEY", "google-key")
	os.Setenv("PROMZ_OPENAI_API_KEY", "openai-key")
	os.Setenv("PORT", "9000")
	os.Setenv("PROMZ_USE_REAL_EXECUTION", "true")
	
	cfg := New()
	
	assert.Equal(t, "https://test.supabase.co", cfg.SupabaseURL)
	assert.Equal(t, "test-key", cfg.SupabaseKey)
	assert.Equal(t, "production", cfg.Environment)
	assert.Equal(t, "postgres://user:pass@localhost:5432/db", cfg.DBConnection)
	assert.Equal(t, "google-key", cfg.GoogleAPIKey)
	assert.Equal(t, "openai-key", cfg.OpenAIAPIKey)
	assert.Equal(t, "9000", cfg.Port)
	assert.False(t, cfg.Debug)
	assert.True(t, cfg.UseRealExecution)
	
	// Test with APP_ENV not set (should default to "development")
	os.Unsetenv("APP_ENV")
	os.Unsetenv("PORT")
	
	cfg = New()
	
	assert.Equal(t, "https://test.supabase.co", cfg.SupabaseURL)
	assert.Equal(t, "test-key", cfg.SupabaseKey)
	assert.Equal(t, "development", cfg.Environment)
	assert.Equal(t, "8080", cfg.Port) // Default port
	assert.True(t, cfg.Debug)
}

func TestConfigHelperMethods(t *testing.T) {
	// Test helper methods
	cfg := &Config{
		SupabaseURL:      "https://test.supabase.co",
		SupabaseKey:      "test-key",
		Environment:      "development",
		Debug:            true,
		UseRealExecution: false,
		DBConnection:     "postgres://user:pass@localhost:5432/db",
		GoogleAPIKey:     "google-key",
		OpenAIAPIKey:     "openai-key",
		Port:             "8080",
	}
	
	// Test environment checks
	assert.True(t, cfg.IsDevelopment())
	assert.False(t, cfg.IsProduction())
	
	// Test API key checks
	assert.True(t, cfg.HasGoogleAI())
	assert.True(t, cfg.HasOpenAI())
	
	// Test validation
	assert.NoError(t, cfg.Validate())
	assert.NoError(t, cfg.ValidateDBConnection())
	
	// Test validation with missing values
	cfgMissing := &Config{
		SupabaseURL: "",
		SupabaseKey: "",
		DBConnection: "",
	}
	
	assert.Error(t, cfgMissing.Validate())
	assert.Error(t, cfgMissing.ValidateDBConnection())
}
