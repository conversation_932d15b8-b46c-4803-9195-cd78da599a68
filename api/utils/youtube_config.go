package utils

import (
	"os"
	"strings"
)

// YouTubeConfig holds YouTube-related configuration
type YouTubeConfig struct {
	APIKey string
}

// GetYouTubeConfig loads YouTube configuration from environment variables
func GetYouTubeConfig() YouTubeConfig {
	return YouTubeConfig{
		APIKey: getEnv("PROMZ_YOUTUBE_API_KEY", ""),
	}
}

// getEnv gets an environment variable or returns a default value
func getEnv(key, defaultValue string) string {
	value := os.Getenv(key)
	if len(strings.TrimSpace(value)) == 0 {
		return defaultValue
	}
	return value
}
