syntax = "proto3";

package promz.api.v1;

option go_package = "promz.ai/api/proto/gen";

import "common.proto";
import "whatsapp_metadata.proto";

// Content upload service for handling various types of content
service ContentUploadService {
  // Upload a file and extract its content and metadata
  rpc UploadFile(UploadFileRequest) returns (ProcessingResult);
  
  // Get the status and result of an upload job
  rpc GetUploadStatus(UploadStatusRequest) returns (ProcessingResult);
  
  // Stream upload updates for real-time feedback
  rpc StreamUploadUpdates(UploadStatusRequest) returns (stream UploadUpdate);
}

// Request to upload a file
message UploadFileRequest {
  bytes file_content = 1;
  string file_name = 2;
  string mime_type = 3;
  string license_tier = 4;
  string file_path = 5;
  string processing_type = 6;
}

// Request to get upload status
message UploadStatusRequest {
  string job_id = 1;
}

// Result of content upload
message ProcessingResult {
  string job_id = 1;
  string content_type = 2;
  string content = 3;
  string expires_at = 4;
  
  // Processing status
  UploadStatus status = 5;
  string error_message = 6;
  
  // Fields extracted from metadata
  string file_name = 7;
  string mime_type = 8;
  string display_name = 9;
  string file_path = 10;
  int64 timestamp = 11;
  bool is_server_processed = 12;
  string processing_type = 13;
  string source_url = 14;
  string author = 15;
  
  // Additional fields needed by various services
  string source = 26;
  string app_name = 27;
  bool is_zip_content = 28;
  string source_type = 29;
  string title = 30;
  
  // Processing progress information
  float processing_progress = 31;
  string processing_message = 32;
  
  // Content-specific metadata
  oneof content_metadata {
    ZipMetadata zip_metadata = 33;
    WhatsAppMetadata whatsapp_metadata = 34;
    FileMetadata file_metadata = 35;
    ArticleMetadata article_metadata = 36;
    YouTubeMetadata youtube_metadata = 37;
  }
}

// Real-time upload update
message UploadUpdate {
  string job_id = 1;
  UploadStatus status = 2;
  float progress_percentage = 3;
  string current_stage = 4;
  string message = 5;
  
  // Partial metadata that becomes available during processing
  // This allows the client to display meaningful information before processing completes
  oneof partial_metadata {
    WhatsAppMetadata whatsapp_metadata = 6;
    ZipMetadata zip_metadata = 7;
    FileMetadata file_metadata = 8;
    ArticleMetadata article_metadata = 9;
    YouTubeMetadata youtube_metadata = 10;
  }
}

// Metadata for ZIP files
message ZipMetadata {
  int32 file_count = 1;
  repeated FileInfo files = 2;
  int64 total_size_bytes = 3;
  string extracted_at = 4;
  bool is_whatsapp_chat = 5;
}

// Metadata for generic files
message FileMetadata {
  int64 size_bytes = 1;
  string last_modified = 2;
  string content_hash = 3;
  string detected_language = 4;
  int32 line_count = 5;
  int32 word_count = 6;
  int32 char_count = 7;
  string author = 8;
  string title = 9;
  string creation_date = 10;
}

// Information about a file within a ZIP archive
message FileInfo {
  string name = 1;
  string path = 2;
  int64 size_bytes = 3;
  string mime_type = 4;
  bool is_text = 5;
  string last_modified = 6;
}

// Metadata for news articles
message ArticleMetadata {
  string url = 1;
  string final_url = 2;
  string title = 3;
  string excerpt = 4;
  string site_name = 5;
  string author = 6;
  string publish_date = 7;
  string language = 8;
  string image_url = 9;
  string html_content = 10;
}

// Metadata for YouTube videos
message YouTubeMetadata {
  string url = 1;
  string video_id = 2;
  string title = 3;
  string description = 4;
  string thumbnail_url = 5;
  string publish_date = 6;
  string language = 7;
  string duration = 8;
  string channel_id = 9;
  string channel_name = 10;
  string category = 11;
  string tags = 12;
  string view_count = 13;
  string like_count = 14;
  string dislike_count = 15;
  string comment_count = 16;
  string image_url = 17;
}
