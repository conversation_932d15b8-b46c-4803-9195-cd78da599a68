syntax = "proto3";

package promz.api.v1;

option go_package = "promz.ai/api/proto/gen";

// WebSocketMessage represents a message sent over WebSocket
message WebSocketMessage {
  // Type of the message
  string type = 1;
  
  // Action to perform
  string action = 2;
  
  // Payload data (JSON-encoded)
  bytes payload = 3;
  
  // Topic for pub/sub messaging
  string topic = 4;
  
  // Message ID for correlation
  string message_id = 5;
  
  // Request ID for request-response pattern
  string request_id = 6;
  
  // Error information if applicable
  ErrorInfo error = 7;
}

// ErrorInfo contains error details
message ErrorInfo {
  // Error code
  string code = 1;
  
  // Error message
  string message = 2;
  
  // Additional error details
  map<string, string> details = 3;
}

// FileUploadUpdate represents a file processing status update
message FileUploadUpdate {
  // Job ID
  string id = 1;
  
  // Current status (queued, processing, completed, failed)
  string status = 2;
  
  // Progress (0.0 to 1.0)
  double progress = 3;
  
  // Error message if any
  string error = 4;
  
  // Informational message
  string message = 5;
  
  // Tokens processed so far
  int32 tokens_processed = 6;
  
  // Token limit for the job
  int32 tokens_limit = 7;
  
  // Whether token limit was exceeded
  bool tokens_exceeded = 8;
  
  // Timestamp of the update
  int64 timestamp = 9;
}

// SubscriptionRequest represents a request to subscribe to a topic
message SubscriptionRequest {
  // Topic to subscribe to
  string topic = 1;
  
  // Optional filter criteria
  map<string, string> filter = 2;
}

// SubscriptionResponse represents a response to a subscription request
message SubscriptionResponse {
  // Whether the subscription was successful
  bool success = 1;
  
  // Topic subscribed to
  string topic = 2;
  
  // Subscription ID
  string subscription_id = 3;
  
  // Error information if applicable
  ErrorInfo error = 4;
}
