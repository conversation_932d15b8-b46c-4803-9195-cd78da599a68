syntax = "proto3";

package promz.api.v1;

option go_package = "promz.ai/api/proto/gen";

// Error codes specific to file processing operations
enum FileProcessingErrorCode {
  // Unknown or unspecified error
  FILE_PROCESSING_ERROR_UNSPECIFIED = 0;
  
  // File size exceeds the limit for the user's license tier
  FILE_SIZE_LIMIT_EXCEEDED = 1;
  
  // File format is not supported
  FILE_FORMAT_UNSUPPORTED = 2;
  
  // File is corrupted or cannot be read
  FILE_CORRUPTED = 3;
  
  // User has reached their quota limit
  QUOTA_EXCEEDED = 4;
  
  // Authentication or authorization error
  AUTHENTICATION_ERROR = 5;
}

// Request to validate file size before upload
message FileSizeValidationRequest {
  // Name of the file to validate
  string file_name = 1;
  
  // Size of the file in bytes
  int64 file_size_bytes = 2;
}

// Response for file size validation
message FileSizeValidationResponse {
  // Whether the file size is valid for the user's license tier
  bool is_valid = 1;
  
  // Error code if validation fails
  FileProcessingErrorCode error_code = 2;
  
  // Error message if validation fails
  string error_message = 3;
  
  // Additional error details
  map<string, string> error_details = 4;
  
  // Next tier that would support this file size (if applicable)
  string next_tier = 5;
  
  // Maximum file size allowed for the current tier in bytes
  int64 max_size_bytes = 6;
}

// FileProcessingService handles file uploads and processing
service FileProcessingService {
  // Validate file size before upload
  rpc ValidateFileSize(FileSizeValidationRequest) returns (FileSizeValidationResponse);
  
  // Upload a file for processing with streaming support
  rpc UploadFile(stream FileUploadRequest) returns (FileUploadResponse);

  // Get processing status
  rpc GetStatus(StatusRequest) returns (StatusResponse);

  // Get processing results with streaming support for large content
  rpc GetResults(ResultsRequest) returns (stream ResultsResponse);

  // Cancel processing
  rpc CancelProcessing(CancelRequest) returns (CancelResponse);
}

// FileUploadRequest is used for streaming file uploads
message FileUploadRequest {
  // First message contains metadata
  message Metadata {
    string file_name = 1;
    string mime_type = 2;
    int64 file_size = 3;
    string license_tier = 4;
    map<string, string> custom_metadata = 5;
  }

  // Subsequent messages contain file chunks
  message Chunk {
    bytes data = 1;
    int32 chunk_index = 2;
  }

  oneof request {
    Metadata metadata = 1;
    Chunk chunk = 2;
  }
}

// FileUploadResponse is returned after a successful upload
message FileUploadResponse {
  string id = 1;
  string status = 2;
  int32 estimated_time_seconds = 3;
  int32 max_tokens = 4;
  int64 file_size = 5;
  string license_tier = 6;
}

// StatusRequest is used to request the status of a processing job
message StatusRequest {
  string id = 1;
}

// StatusResponse contains the current status of a processing job
message StatusResponse {
  string id = 1;
  string status = 2;
  double progress = 3;
  string error = 4;
  string message = 5;
  int32 tokens_processed = 6;
  int32 tokens_limit = 7;
  bool tokens_exceeded = 8;
}

// ResultsRequest is used to request the results of a processing job
message ResultsRequest {
  string id = 1;
}

// ResultsResponse is used to stream processing results
message ResultsResponse {
  // First message contains metadata
  message Metadata {
    string id = 1;
    string content_type = 2;
    map<string, string> metadata = 3;
    bool has_full_content = 4;
    string content_url = 5;
    int64 expires_at = 6;
    int32 tokens_processed = 7;
    int32 tokens_limit = 8;
    bool tokens_exceeded = 9;
  }

  // Subsequent messages contain content chunks
  message ContentChunk {
    bytes data = 1;
    int32 chunk_index = 2;
    bool is_last = 3;
  }

  oneof response {
    Metadata metadata = 1;
    ContentChunk content = 2;
  }
}

// CancelRequest is used to cancel a processing job
message CancelRequest {
  string id = 1;
}

// CancelResponse is returned after a cancellation request
message CancelResponse {
  bool success = 1;
  string message = 2;
}
