syntax = "proto3";

package promz.api.v1;

option go_package = "promz.ai/api/proto/gen";

// LicenseTier represents the user's license tier
enum LicenseTier {
  // Unknown or unspecified tier
  LICENSE_TIER_UNSPECIFIED = 0;
  
  // Free tier
  LICENSE_TIER_FREE = 1;
  
  // Pro tier
  LICENSE_TIER_PRO = 2;
  
  // Enterprise tier
  LICENSE_TIER_ENTERPRISE = 3;
}

// UploadStatus represents the status of a processing job
enum UploadStatus {
  // Unknown or unspecified status
  UPLOAD_STATUS_UNSPECIFIED = 0;
  
  // Job is queued for processing
  UPLOAD_STATUS_QUEUED = 1;
  
  // Job is currently processing
  UPLOAD_STATUS_PROCESSING = 2;
  
  // Job has completed successfully
  UPLOAD_STATUS_COMPLETED = 3;
  
  // Job has failed
  UPLOAD_STATUS_FAILED = 4;
  
  // Job has been cancelled
  UPLOAD_STATUS_CANCELLED = 5;
}

// Error represents an error response
message Error {
  // Error code
  string code = 1;
  
  // Error message
  string message = 2;
  
  // Additional error details
  map<string, string> details = 3;
}

// TierLimits represents the limits for a license tier
message TierLimits {
  // Maximum file size in bytes
  int64 max_file_size_bytes = 1;
  
  // Maximum token limit
  int32 max_tokens = 2;
  
  // Maximum storage days
  int32 max_storage_days = 3;
  
  // Maximum concurrent jobs
  int32 max_concurrent_jobs = 4;
}

// Timestamp represents a point in time
message Timestamp {
  // Seconds since epoch
  int64 seconds = 1;
  
  // Nanoseconds within the second
  int32 nanos = 2;
}
