syntax = "proto3";

package promz.api.v1;

option go_package = "promz.ai/api/proto/gen";

// WhatsAppProcessingService handles WhatsApp chat detection and processing
service WhatsAppProcessingService {
  // Detect if content is a WhatsApp chat
  rpc DetectWhatsAppContent(DetectWhatsAppRequest) returns (DetectWhatsAppResponse);
  
  // Process WhatsApp chat content
  rpc ProcessWhatsAppContent(ProcessWhatsAppRequest) returns (ProcessWhatsAppResponse);
}

// Request to detect WhatsApp content
message DetectWhatsAppRequest {
  // Content can be provided directly or referenced by ID
  oneof content_source {
    string content = 1;
    string content_id = 2;
  }
  string file_name = 3;
}

// Response with WhatsApp detection results
message DetectWhatsAppResponse {
  bool is_whatsapp_chat = 1;
  float confidence_score = 2;
  WhatsAppMetadata metadata = 3;
}

// Request to process WhatsApp content
message ProcessWhatsAppRequest {
  // Content can be provided directly or referenced by ID
  oneof content_source {
    string content = 1;
    string content_id = 2;
  }
  string file_name = 3;
}

// Response with processed WhatsApp content
message ProcessWhatsAppResponse {
  string processed_content = 1;
  WhatsAppMetadata metadata = 2;
  map<string, string> variables = 3;
}

// Metadata specific to WhatsApp chats
message WhatsAppMetadata {
  string group_name = 1;
  string chat_name = 2;
  repeated string participants = 3;
  int32 participant_count = 4;
  int32 message_count = 5;
  int64 first_message_timestamp = 6;
  int64 last_message_timestamp = 7;
  repeated WhatsAppMessage sample_messages = 8;
  bool is_group_chat = 9;
}

// Represents a WhatsApp message
message WhatsAppMessage {
  string sender = 1;
  int64 timestamp = 2;
  string content = 3;
  bool has_media = 4;
  string media_type = 5;
}

// WhatsAppResultsResponse extends the standard ResultsResponse with WhatsApp-specific fields
message WhatsAppResultsResponse {
  // Standard metadata fields from ResultsResponse.Metadata
  string id = 1;
  string content_type = 2;
  map<string, string> metadata = 3;
  bool has_full_content = 4;
  string content_url = 5;
  int64 expires_at = 6;
  
  // WhatsApp-specific fields
  WhatsAppMetadata whatsapp_metadata = 10;
  bool is_whatsapp_chat = 11;
}
