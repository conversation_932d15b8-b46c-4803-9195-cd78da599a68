// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: content_upload.proto

package gen

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Request to upload a file
type UploadFileRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	FileContent    []byte                 `protobuf:"bytes,1,opt,name=file_content,json=fileContent,proto3" json:"file_content,omitempty"`
	FileName       string                 `protobuf:"bytes,2,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	MimeType       string                 `protobuf:"bytes,3,opt,name=mime_type,json=mimeType,proto3" json:"mime_type,omitempty"`
	LicenseTier    string                 `protobuf:"bytes,4,opt,name=license_tier,json=licenseTier,proto3" json:"license_tier,omitempty"`
	FilePath       string                 `protobuf:"bytes,5,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"`
	ProcessingType string                 `protobuf:"bytes,6,opt,name=processing_type,json=processingType,proto3" json:"processing_type,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *UploadFileRequest) Reset() {
	*x = UploadFileRequest{}
	mi := &file_content_upload_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UploadFileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadFileRequest) ProtoMessage() {}

func (x *UploadFileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_content_upload_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadFileRequest.ProtoReflect.Descriptor instead.
func (*UploadFileRequest) Descriptor() ([]byte, []int) {
	return file_content_upload_proto_rawDescGZIP(), []int{0}
}

func (x *UploadFileRequest) GetFileContent() []byte {
	if x != nil {
		return x.FileContent
	}
	return nil
}

func (x *UploadFileRequest) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *UploadFileRequest) GetMimeType() string {
	if x != nil {
		return x.MimeType
	}
	return ""
}

func (x *UploadFileRequest) GetLicenseTier() string {
	if x != nil {
		return x.LicenseTier
	}
	return ""
}

func (x *UploadFileRequest) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *UploadFileRequest) GetProcessingType() string {
	if x != nil {
		return x.ProcessingType
	}
	return ""
}

// Request to get upload status
type UploadStatusRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	JobId         string                 `protobuf:"bytes,1,opt,name=job_id,json=jobId,proto3" json:"job_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UploadStatusRequest) Reset() {
	*x = UploadStatusRequest{}
	mi := &file_content_upload_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UploadStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadStatusRequest) ProtoMessage() {}

func (x *UploadStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_content_upload_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadStatusRequest.ProtoReflect.Descriptor instead.
func (*UploadStatusRequest) Descriptor() ([]byte, []int) {
	return file_content_upload_proto_rawDescGZIP(), []int{1}
}

func (x *UploadStatusRequest) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

// Result of content upload
type ProcessingResult struct {
	state       protoimpl.MessageState `protogen:"open.v1"`
	JobId       string                 `protobuf:"bytes,1,opt,name=job_id,json=jobId,proto3" json:"job_id,omitempty"`
	ContentType string                 `protobuf:"bytes,2,opt,name=content_type,json=contentType,proto3" json:"content_type,omitempty"`
	Content     string                 `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	ExpiresAt   string                 `protobuf:"bytes,4,opt,name=expires_at,json=expiresAt,proto3" json:"expires_at,omitempty"`
	// Processing status
	Status       UploadStatus `protobuf:"varint,5,opt,name=status,proto3,enum=promz.api.v1.UploadStatus" json:"status,omitempty"`
	ErrorMessage string       `protobuf:"bytes,6,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	// Fields extracted from metadata
	FileName          string `protobuf:"bytes,7,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	MimeType          string `protobuf:"bytes,8,opt,name=mime_type,json=mimeType,proto3" json:"mime_type,omitempty"`
	DisplayName       string `protobuf:"bytes,9,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	FilePath          string `protobuf:"bytes,10,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"`
	Timestamp         int64  `protobuf:"varint,11,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	IsServerProcessed bool   `protobuf:"varint,12,opt,name=is_server_processed,json=isServerProcessed,proto3" json:"is_server_processed,omitempty"`
	ProcessingType    string `protobuf:"bytes,13,opt,name=processing_type,json=processingType,proto3" json:"processing_type,omitempty"`
	SourceUrl         string `protobuf:"bytes,14,opt,name=source_url,json=sourceUrl,proto3" json:"source_url,omitempty"`
	Author            string `protobuf:"bytes,15,opt,name=author,proto3" json:"author,omitempty"`
	// Additional fields needed by various services
	Source       string `protobuf:"bytes,26,opt,name=source,proto3" json:"source,omitempty"`
	AppName      string `protobuf:"bytes,27,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`
	IsZipContent bool   `protobuf:"varint,28,opt,name=is_zip_content,json=isZipContent,proto3" json:"is_zip_content,omitempty"`
	SourceType   string `protobuf:"bytes,29,opt,name=source_type,json=sourceType,proto3" json:"source_type,omitempty"`
	Title        string `protobuf:"bytes,30,opt,name=title,proto3" json:"title,omitempty"`
	// Processing progress information
	ProcessingProgress float32 `protobuf:"fixed32,31,opt,name=processing_progress,json=processingProgress,proto3" json:"processing_progress,omitempty"`
	ProcessingMessage  string  `protobuf:"bytes,32,opt,name=processing_message,json=processingMessage,proto3" json:"processing_message,omitempty"`
	// Content-specific metadata
	//
	// Types that are valid to be assigned to ContentMetadata:
	//
	//	*ProcessingResult_ZipMetadata
	//	*ProcessingResult_WhatsappMetadata
	//	*ProcessingResult_FileMetadata
	//	*ProcessingResult_ArticleMetadata
	//	*ProcessingResult_YoutubeMetadata
	ContentMetadata isProcessingResult_ContentMetadata `protobuf_oneof:"content_metadata"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ProcessingResult) Reset() {
	*x = ProcessingResult{}
	mi := &file_content_upload_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProcessingResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessingResult) ProtoMessage() {}

func (x *ProcessingResult) ProtoReflect() protoreflect.Message {
	mi := &file_content_upload_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessingResult.ProtoReflect.Descriptor instead.
func (*ProcessingResult) Descriptor() ([]byte, []int) {
	return file_content_upload_proto_rawDescGZIP(), []int{2}
}

func (x *ProcessingResult) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

func (x *ProcessingResult) GetContentType() string {
	if x != nil {
		return x.ContentType
	}
	return ""
}

func (x *ProcessingResult) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *ProcessingResult) GetExpiresAt() string {
	if x != nil {
		return x.ExpiresAt
	}
	return ""
}

func (x *ProcessingResult) GetStatus() UploadStatus {
	if x != nil {
		return x.Status
	}
	return UploadStatus_UPLOAD_STATUS_UNSPECIFIED
}

func (x *ProcessingResult) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *ProcessingResult) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *ProcessingResult) GetMimeType() string {
	if x != nil {
		return x.MimeType
	}
	return ""
}

func (x *ProcessingResult) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *ProcessingResult) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *ProcessingResult) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *ProcessingResult) GetIsServerProcessed() bool {
	if x != nil {
		return x.IsServerProcessed
	}
	return false
}

func (x *ProcessingResult) GetProcessingType() string {
	if x != nil {
		return x.ProcessingType
	}
	return ""
}

func (x *ProcessingResult) GetSourceUrl() string {
	if x != nil {
		return x.SourceUrl
	}
	return ""
}

func (x *ProcessingResult) GetAuthor() string {
	if x != nil {
		return x.Author
	}
	return ""
}

func (x *ProcessingResult) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *ProcessingResult) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *ProcessingResult) GetIsZipContent() bool {
	if x != nil {
		return x.IsZipContent
	}
	return false
}

func (x *ProcessingResult) GetSourceType() string {
	if x != nil {
		return x.SourceType
	}
	return ""
}

func (x *ProcessingResult) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *ProcessingResult) GetProcessingProgress() float32 {
	if x != nil {
		return x.ProcessingProgress
	}
	return 0
}

func (x *ProcessingResult) GetProcessingMessage() string {
	if x != nil {
		return x.ProcessingMessage
	}
	return ""
}

func (x *ProcessingResult) GetContentMetadata() isProcessingResult_ContentMetadata {
	if x != nil {
		return x.ContentMetadata
	}
	return nil
}

func (x *ProcessingResult) GetZipMetadata() *ZipMetadata {
	if x != nil {
		if x, ok := x.ContentMetadata.(*ProcessingResult_ZipMetadata); ok {
			return x.ZipMetadata
		}
	}
	return nil
}

func (x *ProcessingResult) GetWhatsappMetadata() *WhatsAppMetadata {
	if x != nil {
		if x, ok := x.ContentMetadata.(*ProcessingResult_WhatsappMetadata); ok {
			return x.WhatsappMetadata
		}
	}
	return nil
}

func (x *ProcessingResult) GetFileMetadata() *FileMetadata {
	if x != nil {
		if x, ok := x.ContentMetadata.(*ProcessingResult_FileMetadata); ok {
			return x.FileMetadata
		}
	}
	return nil
}

func (x *ProcessingResult) GetArticleMetadata() *ArticleMetadata {
	if x != nil {
		if x, ok := x.ContentMetadata.(*ProcessingResult_ArticleMetadata); ok {
			return x.ArticleMetadata
		}
	}
	return nil
}

func (x *ProcessingResult) GetYoutubeMetadata() *YouTubeMetadata {
	if x != nil {
		if x, ok := x.ContentMetadata.(*ProcessingResult_YoutubeMetadata); ok {
			return x.YoutubeMetadata
		}
	}
	return nil
}

type isProcessingResult_ContentMetadata interface {
	isProcessingResult_ContentMetadata()
}

type ProcessingResult_ZipMetadata struct {
	ZipMetadata *ZipMetadata `protobuf:"bytes,33,opt,name=zip_metadata,json=zipMetadata,proto3,oneof"`
}

type ProcessingResult_WhatsappMetadata struct {
	WhatsappMetadata *WhatsAppMetadata `protobuf:"bytes,34,opt,name=whatsapp_metadata,json=whatsappMetadata,proto3,oneof"`
}

type ProcessingResult_FileMetadata struct {
	FileMetadata *FileMetadata `protobuf:"bytes,35,opt,name=file_metadata,json=fileMetadata,proto3,oneof"`
}

type ProcessingResult_ArticleMetadata struct {
	ArticleMetadata *ArticleMetadata `protobuf:"bytes,36,opt,name=article_metadata,json=articleMetadata,proto3,oneof"`
}

type ProcessingResult_YoutubeMetadata struct {
	YoutubeMetadata *YouTubeMetadata `protobuf:"bytes,37,opt,name=youtube_metadata,json=youtubeMetadata,proto3,oneof"`
}

func (*ProcessingResult_ZipMetadata) isProcessingResult_ContentMetadata() {}

func (*ProcessingResult_WhatsappMetadata) isProcessingResult_ContentMetadata() {}

func (*ProcessingResult_FileMetadata) isProcessingResult_ContentMetadata() {}

func (*ProcessingResult_ArticleMetadata) isProcessingResult_ContentMetadata() {}

func (*ProcessingResult_YoutubeMetadata) isProcessingResult_ContentMetadata() {}

// Real-time upload update
type UploadUpdate struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	JobId              string                 `protobuf:"bytes,1,opt,name=job_id,json=jobId,proto3" json:"job_id,omitempty"`
	Status             UploadStatus           `protobuf:"varint,2,opt,name=status,proto3,enum=promz.api.v1.UploadStatus" json:"status,omitempty"`
	ProgressPercentage float32                `protobuf:"fixed32,3,opt,name=progress_percentage,json=progressPercentage,proto3" json:"progress_percentage,omitempty"`
	CurrentStage       string                 `protobuf:"bytes,4,opt,name=current_stage,json=currentStage,proto3" json:"current_stage,omitempty"`
	Message            string                 `protobuf:"bytes,5,opt,name=message,proto3" json:"message,omitempty"`
	// Partial metadata that becomes available during processing
	// This allows the client to display meaningful information before processing completes
	//
	// Types that are valid to be assigned to PartialMetadata:
	//
	//	*UploadUpdate_WhatsappMetadata
	//	*UploadUpdate_ZipMetadata
	//	*UploadUpdate_FileMetadata
	//	*UploadUpdate_ArticleMetadata
	//	*UploadUpdate_YoutubeMetadata
	PartialMetadata isUploadUpdate_PartialMetadata `protobuf_oneof:"partial_metadata"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *UploadUpdate) Reset() {
	*x = UploadUpdate{}
	mi := &file_content_upload_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UploadUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadUpdate) ProtoMessage() {}

func (x *UploadUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_content_upload_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadUpdate.ProtoReflect.Descriptor instead.
func (*UploadUpdate) Descriptor() ([]byte, []int) {
	return file_content_upload_proto_rawDescGZIP(), []int{3}
}

func (x *UploadUpdate) GetJobId() string {
	if x != nil {
		return x.JobId
	}
	return ""
}

func (x *UploadUpdate) GetStatus() UploadStatus {
	if x != nil {
		return x.Status
	}
	return UploadStatus_UPLOAD_STATUS_UNSPECIFIED
}

func (x *UploadUpdate) GetProgressPercentage() float32 {
	if x != nil {
		return x.ProgressPercentage
	}
	return 0
}

func (x *UploadUpdate) GetCurrentStage() string {
	if x != nil {
		return x.CurrentStage
	}
	return ""
}

func (x *UploadUpdate) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *UploadUpdate) GetPartialMetadata() isUploadUpdate_PartialMetadata {
	if x != nil {
		return x.PartialMetadata
	}
	return nil
}

func (x *UploadUpdate) GetWhatsappMetadata() *WhatsAppMetadata {
	if x != nil {
		if x, ok := x.PartialMetadata.(*UploadUpdate_WhatsappMetadata); ok {
			return x.WhatsappMetadata
		}
	}
	return nil
}

func (x *UploadUpdate) GetZipMetadata() *ZipMetadata {
	if x != nil {
		if x, ok := x.PartialMetadata.(*UploadUpdate_ZipMetadata); ok {
			return x.ZipMetadata
		}
	}
	return nil
}

func (x *UploadUpdate) GetFileMetadata() *FileMetadata {
	if x != nil {
		if x, ok := x.PartialMetadata.(*UploadUpdate_FileMetadata); ok {
			return x.FileMetadata
		}
	}
	return nil
}

func (x *UploadUpdate) GetArticleMetadata() *ArticleMetadata {
	if x != nil {
		if x, ok := x.PartialMetadata.(*UploadUpdate_ArticleMetadata); ok {
			return x.ArticleMetadata
		}
	}
	return nil
}

func (x *UploadUpdate) GetYoutubeMetadata() *YouTubeMetadata {
	if x != nil {
		if x, ok := x.PartialMetadata.(*UploadUpdate_YoutubeMetadata); ok {
			return x.YoutubeMetadata
		}
	}
	return nil
}

type isUploadUpdate_PartialMetadata interface {
	isUploadUpdate_PartialMetadata()
}

type UploadUpdate_WhatsappMetadata struct {
	WhatsappMetadata *WhatsAppMetadata `protobuf:"bytes,6,opt,name=whatsapp_metadata,json=whatsappMetadata,proto3,oneof"`
}

type UploadUpdate_ZipMetadata struct {
	ZipMetadata *ZipMetadata `protobuf:"bytes,7,opt,name=zip_metadata,json=zipMetadata,proto3,oneof"`
}

type UploadUpdate_FileMetadata struct {
	FileMetadata *FileMetadata `protobuf:"bytes,8,opt,name=file_metadata,json=fileMetadata,proto3,oneof"`
}

type UploadUpdate_ArticleMetadata struct {
	ArticleMetadata *ArticleMetadata `protobuf:"bytes,9,opt,name=article_metadata,json=articleMetadata,proto3,oneof"`
}

type UploadUpdate_YoutubeMetadata struct {
	YoutubeMetadata *YouTubeMetadata `protobuf:"bytes,10,opt,name=youtube_metadata,json=youtubeMetadata,proto3,oneof"`
}

func (*UploadUpdate_WhatsappMetadata) isUploadUpdate_PartialMetadata() {}

func (*UploadUpdate_ZipMetadata) isUploadUpdate_PartialMetadata() {}

func (*UploadUpdate_FileMetadata) isUploadUpdate_PartialMetadata() {}

func (*UploadUpdate_ArticleMetadata) isUploadUpdate_PartialMetadata() {}

func (*UploadUpdate_YoutubeMetadata) isUploadUpdate_PartialMetadata() {}

// Metadata for ZIP files
type ZipMetadata struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	FileCount      int32                  `protobuf:"varint,1,opt,name=file_count,json=fileCount,proto3" json:"file_count,omitempty"`
	Files          []*FileInfo            `protobuf:"bytes,2,rep,name=files,proto3" json:"files,omitempty"`
	TotalSizeBytes int64                  `protobuf:"varint,3,opt,name=total_size_bytes,json=totalSizeBytes,proto3" json:"total_size_bytes,omitempty"`
	ExtractedAt    string                 `protobuf:"bytes,4,opt,name=extracted_at,json=extractedAt,proto3" json:"extracted_at,omitempty"`
	IsWhatsappChat bool                   `protobuf:"varint,5,opt,name=is_whatsapp_chat,json=isWhatsappChat,proto3" json:"is_whatsapp_chat,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ZipMetadata) Reset() {
	*x = ZipMetadata{}
	mi := &file_content_upload_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ZipMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ZipMetadata) ProtoMessage() {}

func (x *ZipMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_content_upload_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ZipMetadata.ProtoReflect.Descriptor instead.
func (*ZipMetadata) Descriptor() ([]byte, []int) {
	return file_content_upload_proto_rawDescGZIP(), []int{4}
}

func (x *ZipMetadata) GetFileCount() int32 {
	if x != nil {
		return x.FileCount
	}
	return 0
}

func (x *ZipMetadata) GetFiles() []*FileInfo {
	if x != nil {
		return x.Files
	}
	return nil
}

func (x *ZipMetadata) GetTotalSizeBytes() int64 {
	if x != nil {
		return x.TotalSizeBytes
	}
	return 0
}

func (x *ZipMetadata) GetExtractedAt() string {
	if x != nil {
		return x.ExtractedAt
	}
	return ""
}

func (x *ZipMetadata) GetIsWhatsappChat() bool {
	if x != nil {
		return x.IsWhatsappChat
	}
	return false
}

// Metadata for generic files
type FileMetadata struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	SizeBytes        int64                  `protobuf:"varint,1,opt,name=size_bytes,json=sizeBytes,proto3" json:"size_bytes,omitempty"`
	LastModified     string                 `protobuf:"bytes,2,opt,name=last_modified,json=lastModified,proto3" json:"last_modified,omitempty"`
	ContentHash      string                 `protobuf:"bytes,3,opt,name=content_hash,json=contentHash,proto3" json:"content_hash,omitempty"`
	DetectedLanguage string                 `protobuf:"bytes,4,opt,name=detected_language,json=detectedLanguage,proto3" json:"detected_language,omitempty"`
	LineCount        int32                  `protobuf:"varint,5,opt,name=line_count,json=lineCount,proto3" json:"line_count,omitempty"`
	WordCount        int32                  `protobuf:"varint,6,opt,name=word_count,json=wordCount,proto3" json:"word_count,omitempty"`
	CharCount        int32                  `protobuf:"varint,7,opt,name=char_count,json=charCount,proto3" json:"char_count,omitempty"`
	Author           string                 `protobuf:"bytes,8,opt,name=author,proto3" json:"author,omitempty"`
	Title            string                 `protobuf:"bytes,9,opt,name=title,proto3" json:"title,omitempty"`
	CreationDate     string                 `protobuf:"bytes,10,opt,name=creation_date,json=creationDate,proto3" json:"creation_date,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *FileMetadata) Reset() {
	*x = FileMetadata{}
	mi := &file_content_upload_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileMetadata) ProtoMessage() {}

func (x *FileMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_content_upload_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileMetadata.ProtoReflect.Descriptor instead.
func (*FileMetadata) Descriptor() ([]byte, []int) {
	return file_content_upload_proto_rawDescGZIP(), []int{5}
}

func (x *FileMetadata) GetSizeBytes() int64 {
	if x != nil {
		return x.SizeBytes
	}
	return 0
}

func (x *FileMetadata) GetLastModified() string {
	if x != nil {
		return x.LastModified
	}
	return ""
}

func (x *FileMetadata) GetContentHash() string {
	if x != nil {
		return x.ContentHash
	}
	return ""
}

func (x *FileMetadata) GetDetectedLanguage() string {
	if x != nil {
		return x.DetectedLanguage
	}
	return ""
}

func (x *FileMetadata) GetLineCount() int32 {
	if x != nil {
		return x.LineCount
	}
	return 0
}

func (x *FileMetadata) GetWordCount() int32 {
	if x != nil {
		return x.WordCount
	}
	return 0
}

func (x *FileMetadata) GetCharCount() int32 {
	if x != nil {
		return x.CharCount
	}
	return 0
}

func (x *FileMetadata) GetAuthor() string {
	if x != nil {
		return x.Author
	}
	return ""
}

func (x *FileMetadata) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *FileMetadata) GetCreationDate() string {
	if x != nil {
		return x.CreationDate
	}
	return ""
}

// Information about a file within a ZIP archive
type FileInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Path          string                 `protobuf:"bytes,2,opt,name=path,proto3" json:"path,omitempty"`
	SizeBytes     int64                  `protobuf:"varint,3,opt,name=size_bytes,json=sizeBytes,proto3" json:"size_bytes,omitempty"`
	MimeType      string                 `protobuf:"bytes,4,opt,name=mime_type,json=mimeType,proto3" json:"mime_type,omitempty"`
	IsText        bool                   `protobuf:"varint,5,opt,name=is_text,json=isText,proto3" json:"is_text,omitempty"`
	LastModified  string                 `protobuf:"bytes,6,opt,name=last_modified,json=lastModified,proto3" json:"last_modified,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FileInfo) Reset() {
	*x = FileInfo{}
	mi := &file_content_upload_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileInfo) ProtoMessage() {}

func (x *FileInfo) ProtoReflect() protoreflect.Message {
	mi := &file_content_upload_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileInfo.ProtoReflect.Descriptor instead.
func (*FileInfo) Descriptor() ([]byte, []int) {
	return file_content_upload_proto_rawDescGZIP(), []int{6}
}

func (x *FileInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *FileInfo) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *FileInfo) GetSizeBytes() int64 {
	if x != nil {
		return x.SizeBytes
	}
	return 0
}

func (x *FileInfo) GetMimeType() string {
	if x != nil {
		return x.MimeType
	}
	return ""
}

func (x *FileInfo) GetIsText() bool {
	if x != nil {
		return x.IsText
	}
	return false
}

func (x *FileInfo) GetLastModified() string {
	if x != nil {
		return x.LastModified
	}
	return ""
}

// Metadata for news articles
type ArticleMetadata struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Url           string                 `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	FinalUrl      string                 `protobuf:"bytes,2,opt,name=final_url,json=finalUrl,proto3" json:"final_url,omitempty"`
	Title         string                 `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	Excerpt       string                 `protobuf:"bytes,4,opt,name=excerpt,proto3" json:"excerpt,omitempty"`
	SiteName      string                 `protobuf:"bytes,5,opt,name=site_name,json=siteName,proto3" json:"site_name,omitempty"`
	Author        string                 `protobuf:"bytes,6,opt,name=author,proto3" json:"author,omitempty"`
	PublishDate   string                 `protobuf:"bytes,7,opt,name=publish_date,json=publishDate,proto3" json:"publish_date,omitempty"`
	Language      string                 `protobuf:"bytes,8,opt,name=language,proto3" json:"language,omitempty"`
	ImageUrl      string                 `protobuf:"bytes,9,opt,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty"`
	HtmlContent   string                 `protobuf:"bytes,10,opt,name=html_content,json=htmlContent,proto3" json:"html_content,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ArticleMetadata) Reset() {
	*x = ArticleMetadata{}
	mi := &file_content_upload_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArticleMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArticleMetadata) ProtoMessage() {}

func (x *ArticleMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_content_upload_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArticleMetadata.ProtoReflect.Descriptor instead.
func (*ArticleMetadata) Descriptor() ([]byte, []int) {
	return file_content_upload_proto_rawDescGZIP(), []int{7}
}

func (x *ArticleMetadata) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *ArticleMetadata) GetFinalUrl() string {
	if x != nil {
		return x.FinalUrl
	}
	return ""
}

func (x *ArticleMetadata) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *ArticleMetadata) GetExcerpt() string {
	if x != nil {
		return x.Excerpt
	}
	return ""
}

func (x *ArticleMetadata) GetSiteName() string {
	if x != nil {
		return x.SiteName
	}
	return ""
}

func (x *ArticleMetadata) GetAuthor() string {
	if x != nil {
		return x.Author
	}
	return ""
}

func (x *ArticleMetadata) GetPublishDate() string {
	if x != nil {
		return x.PublishDate
	}
	return ""
}

func (x *ArticleMetadata) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *ArticleMetadata) GetImageUrl() string {
	if x != nil {
		return x.ImageUrl
	}
	return ""
}

func (x *ArticleMetadata) GetHtmlContent() string {
	if x != nil {
		return x.HtmlContent
	}
	return ""
}

// Metadata for YouTube videos
type YouTubeMetadata struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Url           string                 `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	VideoId       string                 `protobuf:"bytes,2,opt,name=video_id,json=videoId,proto3" json:"video_id,omitempty"`
	Title         string                 `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	Description   string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	ThumbnailUrl  string                 `protobuf:"bytes,5,opt,name=thumbnail_url,json=thumbnailUrl,proto3" json:"thumbnail_url,omitempty"`
	PublishDate   string                 `protobuf:"bytes,6,opt,name=publish_date,json=publishDate,proto3" json:"publish_date,omitempty"`
	Language      string                 `protobuf:"bytes,7,opt,name=language,proto3" json:"language,omitempty"`
	Duration      string                 `protobuf:"bytes,8,opt,name=duration,proto3" json:"duration,omitempty"`
	ChannelId     string                 `protobuf:"bytes,9,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelName   string                 `protobuf:"bytes,10,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	Category      string                 `protobuf:"bytes,11,opt,name=category,proto3" json:"category,omitempty"`
	Tags          string                 `protobuf:"bytes,12,opt,name=tags,proto3" json:"tags,omitempty"`
	ViewCount     string                 `protobuf:"bytes,13,opt,name=view_count,json=viewCount,proto3" json:"view_count,omitempty"`
	LikeCount     string                 `protobuf:"bytes,14,opt,name=like_count,json=likeCount,proto3" json:"like_count,omitempty"`
	DislikeCount  string                 `protobuf:"bytes,15,opt,name=dislike_count,json=dislikeCount,proto3" json:"dislike_count,omitempty"`
	CommentCount  string                 `protobuf:"bytes,16,opt,name=comment_count,json=commentCount,proto3" json:"comment_count,omitempty"`
	ImageUrl      string                 `protobuf:"bytes,17,opt,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *YouTubeMetadata) Reset() {
	*x = YouTubeMetadata{}
	mi := &file_content_upload_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *YouTubeMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*YouTubeMetadata) ProtoMessage() {}

func (x *YouTubeMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_content_upload_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use YouTubeMetadata.ProtoReflect.Descriptor instead.
func (*YouTubeMetadata) Descriptor() ([]byte, []int) {
	return file_content_upload_proto_rawDescGZIP(), []int{8}
}

func (x *YouTubeMetadata) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *YouTubeMetadata) GetVideoId() string {
	if x != nil {
		return x.VideoId
	}
	return ""
}

func (x *YouTubeMetadata) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *YouTubeMetadata) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *YouTubeMetadata) GetThumbnailUrl() string {
	if x != nil {
		return x.ThumbnailUrl
	}
	return ""
}

func (x *YouTubeMetadata) GetPublishDate() string {
	if x != nil {
		return x.PublishDate
	}
	return ""
}

func (x *YouTubeMetadata) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *YouTubeMetadata) GetDuration() string {
	if x != nil {
		return x.Duration
	}
	return ""
}

func (x *YouTubeMetadata) GetChannelId() string {
	if x != nil {
		return x.ChannelId
	}
	return ""
}

func (x *YouTubeMetadata) GetChannelName() string {
	if x != nil {
		return x.ChannelName
	}
	return ""
}

func (x *YouTubeMetadata) GetCategory() string {
	if x != nil {
		return x.Category
	}
	return ""
}

func (x *YouTubeMetadata) GetTags() string {
	if x != nil {
		return x.Tags
	}
	return ""
}

func (x *YouTubeMetadata) GetViewCount() string {
	if x != nil {
		return x.ViewCount
	}
	return ""
}

func (x *YouTubeMetadata) GetLikeCount() string {
	if x != nil {
		return x.LikeCount
	}
	return ""
}

func (x *YouTubeMetadata) GetDislikeCount() string {
	if x != nil {
		return x.DislikeCount
	}
	return ""
}

func (x *YouTubeMetadata) GetCommentCount() string {
	if x != nil {
		return x.CommentCount
	}
	return ""
}

func (x *YouTubeMetadata) GetImageUrl() string {
	if x != nil {
		return x.ImageUrl
	}
	return ""
}

var File_content_upload_proto protoreflect.FileDescriptor

const file_content_upload_proto_rawDesc = "" +
	"\n" +
	"\x14content_upload.proto\x12\fpromz.api.v1\x1a\fcommon.proto\x1a\x17whatsapp_metadata.proto\"\xd9\x01\n" +
	"\x11UploadFileRequest\x12!\n" +
	"\ffile_content\x18\x01 \x01(\fR\vfileContent\x12\x1b\n" +
	"\tfile_name\x18\x02 \x01(\tR\bfileName\x12\x1b\n" +
	"\tmime_type\x18\x03 \x01(\tR\bmimeType\x12!\n" +
	"\flicense_tier\x18\x04 \x01(\tR\vlicenseTier\x12\x1b\n" +
	"\tfile_path\x18\x05 \x01(\tR\bfilePath\x12'\n" +
	"\x0fprocessing_type\x18\x06 \x01(\tR\x0eprocessingType\",\n" +
	"\x13UploadStatusRequest\x12\x15\n" +
	"\x06job_id\x18\x01 \x01(\tR\x05jobId\"\xf4\b\n" +
	"\x10ProcessingResult\x12\x15\n" +
	"\x06job_id\x18\x01 \x01(\tR\x05jobId\x12!\n" +
	"\fcontent_type\x18\x02 \x01(\tR\vcontentType\x12\x18\n" +
	"\acontent\x18\x03 \x01(\tR\acontent\x12\x1d\n" +
	"\n" +
	"expires_at\x18\x04 \x01(\tR\texpiresAt\x122\n" +
	"\x06status\x18\x05 \x01(\x0e2\x1a.promz.api.v1.UploadStatusR\x06status\x12#\n" +
	"\rerror_message\x18\x06 \x01(\tR\ferrorMessage\x12\x1b\n" +
	"\tfile_name\x18\a \x01(\tR\bfileName\x12\x1b\n" +
	"\tmime_type\x18\b \x01(\tR\bmimeType\x12!\n" +
	"\fdisplay_name\x18\t \x01(\tR\vdisplayName\x12\x1b\n" +
	"\tfile_path\x18\n" +
	" \x01(\tR\bfilePath\x12\x1c\n" +
	"\ttimestamp\x18\v \x01(\x03R\ttimestamp\x12.\n" +
	"\x13is_server_processed\x18\f \x01(\bR\x11isServerProcessed\x12'\n" +
	"\x0fprocessing_type\x18\r \x01(\tR\x0eprocessingType\x12\x1d\n" +
	"\n" +
	"source_url\x18\x0e \x01(\tR\tsourceUrl\x12\x16\n" +
	"\x06author\x18\x0f \x01(\tR\x06author\x12\x16\n" +
	"\x06source\x18\x1a \x01(\tR\x06source\x12\x19\n" +
	"\bapp_name\x18\x1b \x01(\tR\aappName\x12$\n" +
	"\x0eis_zip_content\x18\x1c \x01(\bR\fisZipContent\x12\x1f\n" +
	"\vsource_type\x18\x1d \x01(\tR\n" +
	"sourceType\x12\x14\n" +
	"\x05title\x18\x1e \x01(\tR\x05title\x12/\n" +
	"\x13processing_progress\x18\x1f \x01(\x02R\x12processingProgress\x12-\n" +
	"\x12processing_message\x18  \x01(\tR\x11processingMessage\x12>\n" +
	"\fzip_metadata\x18! \x01(\v2\x19.promz.api.v1.ZipMetadataH\x00R\vzipMetadata\x12M\n" +
	"\x11whatsapp_metadata\x18\" \x01(\v2\x1e.promz.api.v1.WhatsAppMetadataH\x00R\x10whatsappMetadata\x12A\n" +
	"\rfile_metadata\x18# \x01(\v2\x1a.promz.api.v1.FileMetadataH\x00R\ffileMetadata\x12J\n" +
	"\x10article_metadata\x18$ \x01(\v2\x1d.promz.api.v1.ArticleMetadataH\x00R\x0farticleMetadata\x12J\n" +
	"\x10youtube_metadata\x18% \x01(\v2\x1d.promz.api.v1.YouTubeMetadataH\x00R\x0fyoutubeMetadataB\x12\n" +
	"\x10content_metadata\"\xc7\x04\n" +
	"\fUploadUpdate\x12\x15\n" +
	"\x06job_id\x18\x01 \x01(\tR\x05jobId\x122\n" +
	"\x06status\x18\x02 \x01(\x0e2\x1a.promz.api.v1.UploadStatusR\x06status\x12/\n" +
	"\x13progress_percentage\x18\x03 \x01(\x02R\x12progressPercentage\x12#\n" +
	"\rcurrent_stage\x18\x04 \x01(\tR\fcurrentStage\x12\x18\n" +
	"\amessage\x18\x05 \x01(\tR\amessage\x12M\n" +
	"\x11whatsapp_metadata\x18\x06 \x01(\v2\x1e.promz.api.v1.WhatsAppMetadataH\x00R\x10whatsappMetadata\x12>\n" +
	"\fzip_metadata\x18\a \x01(\v2\x19.promz.api.v1.ZipMetadataH\x00R\vzipMetadata\x12A\n" +
	"\rfile_metadata\x18\b \x01(\v2\x1a.promz.api.v1.FileMetadataH\x00R\ffileMetadata\x12J\n" +
	"\x10article_metadata\x18\t \x01(\v2\x1d.promz.api.v1.ArticleMetadataH\x00R\x0farticleMetadata\x12J\n" +
	"\x10youtube_metadata\x18\n" +
	" \x01(\v2\x1d.promz.api.v1.YouTubeMetadataH\x00R\x0fyoutubeMetadataB\x12\n" +
	"\x10partial_metadata\"\xd1\x01\n" +
	"\vZipMetadata\x12\x1d\n" +
	"\n" +
	"file_count\x18\x01 \x01(\x05R\tfileCount\x12,\n" +
	"\x05files\x18\x02 \x03(\v2\x16.promz.api.v1.FileInfoR\x05files\x12(\n" +
	"\x10total_size_bytes\x18\x03 \x01(\x03R\x0etotalSizeBytes\x12!\n" +
	"\fextracted_at\x18\x04 \x01(\tR\vextractedAt\x12(\n" +
	"\x10is_whatsapp_chat\x18\x05 \x01(\bR\x0eisWhatsappChat\"\xd2\x02\n" +
	"\fFileMetadata\x12\x1d\n" +
	"\n" +
	"size_bytes\x18\x01 \x01(\x03R\tsizeBytes\x12#\n" +
	"\rlast_modified\x18\x02 \x01(\tR\flastModified\x12!\n" +
	"\fcontent_hash\x18\x03 \x01(\tR\vcontentHash\x12+\n" +
	"\x11detected_language\x18\x04 \x01(\tR\x10detectedLanguage\x12\x1d\n" +
	"\n" +
	"line_count\x18\x05 \x01(\x05R\tlineCount\x12\x1d\n" +
	"\n" +
	"word_count\x18\x06 \x01(\x05R\twordCount\x12\x1d\n" +
	"\n" +
	"char_count\x18\a \x01(\x05R\tcharCount\x12\x16\n" +
	"\x06author\x18\b \x01(\tR\x06author\x12\x14\n" +
	"\x05title\x18\t \x01(\tR\x05title\x12#\n" +
	"\rcreation_date\x18\n" +
	" \x01(\tR\fcreationDate\"\xac\x01\n" +
	"\bFileInfo\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x12\n" +
	"\x04path\x18\x02 \x01(\tR\x04path\x12\x1d\n" +
	"\n" +
	"size_bytes\x18\x03 \x01(\x03R\tsizeBytes\x12\x1b\n" +
	"\tmime_type\x18\x04 \x01(\tR\bmimeType\x12\x17\n" +
	"\ais_text\x18\x05 \x01(\bR\x06isText\x12#\n" +
	"\rlast_modified\x18\x06 \x01(\tR\flastModified\"\xa4\x02\n" +
	"\x0fArticleMetadata\x12\x10\n" +
	"\x03url\x18\x01 \x01(\tR\x03url\x12\x1b\n" +
	"\tfinal_url\x18\x02 \x01(\tR\bfinalUrl\x12\x14\n" +
	"\x05title\x18\x03 \x01(\tR\x05title\x12\x18\n" +
	"\aexcerpt\x18\x04 \x01(\tR\aexcerpt\x12\x1b\n" +
	"\tsite_name\x18\x05 \x01(\tR\bsiteName\x12\x16\n" +
	"\x06author\x18\x06 \x01(\tR\x06author\x12!\n" +
	"\fpublish_date\x18\a \x01(\tR\vpublishDate\x12\x1a\n" +
	"\blanguage\x18\b \x01(\tR\blanguage\x12\x1b\n" +
	"\timage_url\x18\t \x01(\tR\bimageUrl\x12!\n" +
	"\fhtml_content\x18\n" +
	" \x01(\tR\vhtmlContent\"\x8d\x04\n" +
	"\x0fYouTubeMetadata\x12\x10\n" +
	"\x03url\x18\x01 \x01(\tR\x03url\x12\x19\n" +
	"\bvideo_id\x18\x02 \x01(\tR\avideoId\x12\x14\n" +
	"\x05title\x18\x03 \x01(\tR\x05title\x12 \n" +
	"\vdescription\x18\x04 \x01(\tR\vdescription\x12#\n" +
	"\rthumbnail_url\x18\x05 \x01(\tR\fthumbnailUrl\x12!\n" +
	"\fpublish_date\x18\x06 \x01(\tR\vpublishDate\x12\x1a\n" +
	"\blanguage\x18\a \x01(\tR\blanguage\x12\x1a\n" +
	"\bduration\x18\b \x01(\tR\bduration\x12\x1d\n" +
	"\n" +
	"channel_id\x18\t \x01(\tR\tchannelId\x12!\n" +
	"\fchannel_name\x18\n" +
	" \x01(\tR\vchannelName\x12\x1a\n" +
	"\bcategory\x18\v \x01(\tR\bcategory\x12\x12\n" +
	"\x04tags\x18\f \x01(\tR\x04tags\x12\x1d\n" +
	"\n" +
	"view_count\x18\r \x01(\tR\tviewCount\x12\x1d\n" +
	"\n" +
	"like_count\x18\x0e \x01(\tR\tlikeCount\x12#\n" +
	"\rdislike_count\x18\x0f \x01(\tR\fdislikeCount\x12#\n" +
	"\rcomment_count\x18\x10 \x01(\tR\fcommentCount\x12\x1b\n" +
	"\timage_url\x18\x11 \x01(\tR\bimageUrl2\x93\x02\n" +
	"\x14ContentUploadService\x12M\n" +
	"\n" +
	"UploadFile\x12\x1f.promz.api.v1.UploadFileRequest\x1a\x1e.promz.api.v1.ProcessingResult\x12T\n" +
	"\x0fGetUploadStatus\x12!.promz.api.v1.UploadStatusRequest\x1a\x1e.promz.api.v1.ProcessingResult\x12V\n" +
	"\x13StreamUploadUpdates\x12!.promz.api.v1.UploadStatusRequest\x1a\x1a.promz.api.v1.UploadUpdate0\x01B\x18Z\x16promz.ai/api/proto/genb\x06proto3"

var (
	file_content_upload_proto_rawDescOnce sync.Once
	file_content_upload_proto_rawDescData []byte
)

func file_content_upload_proto_rawDescGZIP() []byte {
	file_content_upload_proto_rawDescOnce.Do(func() {
		file_content_upload_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_content_upload_proto_rawDesc), len(file_content_upload_proto_rawDesc)))
	})
	return file_content_upload_proto_rawDescData
}

var file_content_upload_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_content_upload_proto_goTypes = []any{
	(*UploadFileRequest)(nil),   // 0: promz.api.v1.UploadFileRequest
	(*UploadStatusRequest)(nil), // 1: promz.api.v1.UploadStatusRequest
	(*ProcessingResult)(nil),    // 2: promz.api.v1.ProcessingResult
	(*UploadUpdate)(nil),        // 3: promz.api.v1.UploadUpdate
	(*ZipMetadata)(nil),         // 4: promz.api.v1.ZipMetadata
	(*FileMetadata)(nil),        // 5: promz.api.v1.FileMetadata
	(*FileInfo)(nil),            // 6: promz.api.v1.FileInfo
	(*ArticleMetadata)(nil),     // 7: promz.api.v1.ArticleMetadata
	(*YouTubeMetadata)(nil),     // 8: promz.api.v1.YouTubeMetadata
	(UploadStatus)(0),           // 9: promz.api.v1.UploadStatus
	(*WhatsAppMetadata)(nil),    // 10: promz.api.v1.WhatsAppMetadata
}
var file_content_upload_proto_depIdxs = []int32{
	9,  // 0: promz.api.v1.ProcessingResult.status:type_name -> promz.api.v1.UploadStatus
	4,  // 1: promz.api.v1.ProcessingResult.zip_metadata:type_name -> promz.api.v1.ZipMetadata
	10, // 2: promz.api.v1.ProcessingResult.whatsapp_metadata:type_name -> promz.api.v1.WhatsAppMetadata
	5,  // 3: promz.api.v1.ProcessingResult.file_metadata:type_name -> promz.api.v1.FileMetadata
	7,  // 4: promz.api.v1.ProcessingResult.article_metadata:type_name -> promz.api.v1.ArticleMetadata
	8,  // 5: promz.api.v1.ProcessingResult.youtube_metadata:type_name -> promz.api.v1.YouTubeMetadata
	9,  // 6: promz.api.v1.UploadUpdate.status:type_name -> promz.api.v1.UploadStatus
	10, // 7: promz.api.v1.UploadUpdate.whatsapp_metadata:type_name -> promz.api.v1.WhatsAppMetadata
	4,  // 8: promz.api.v1.UploadUpdate.zip_metadata:type_name -> promz.api.v1.ZipMetadata
	5,  // 9: promz.api.v1.UploadUpdate.file_metadata:type_name -> promz.api.v1.FileMetadata
	7,  // 10: promz.api.v1.UploadUpdate.article_metadata:type_name -> promz.api.v1.ArticleMetadata
	8,  // 11: promz.api.v1.UploadUpdate.youtube_metadata:type_name -> promz.api.v1.YouTubeMetadata
	6,  // 12: promz.api.v1.ZipMetadata.files:type_name -> promz.api.v1.FileInfo
	0,  // 13: promz.api.v1.ContentUploadService.UploadFile:input_type -> promz.api.v1.UploadFileRequest
	1,  // 14: promz.api.v1.ContentUploadService.GetUploadStatus:input_type -> promz.api.v1.UploadStatusRequest
	1,  // 15: promz.api.v1.ContentUploadService.StreamUploadUpdates:input_type -> promz.api.v1.UploadStatusRequest
	2,  // 16: promz.api.v1.ContentUploadService.UploadFile:output_type -> promz.api.v1.ProcessingResult
	2,  // 17: promz.api.v1.ContentUploadService.GetUploadStatus:output_type -> promz.api.v1.ProcessingResult
	3,  // 18: promz.api.v1.ContentUploadService.StreamUploadUpdates:output_type -> promz.api.v1.UploadUpdate
	16, // [16:19] is the sub-list for method output_type
	13, // [13:16] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_content_upload_proto_init() }
func file_content_upload_proto_init() {
	if File_content_upload_proto != nil {
		return
	}
	file_common_proto_init()
	file_whatsapp_metadata_proto_init()
	file_content_upload_proto_msgTypes[2].OneofWrappers = []any{
		(*ProcessingResult_ZipMetadata)(nil),
		(*ProcessingResult_WhatsappMetadata)(nil),
		(*ProcessingResult_FileMetadata)(nil),
		(*ProcessingResult_ArticleMetadata)(nil),
		(*ProcessingResult_YoutubeMetadata)(nil),
	}
	file_content_upload_proto_msgTypes[3].OneofWrappers = []any{
		(*UploadUpdate_WhatsappMetadata)(nil),
		(*UploadUpdate_ZipMetadata)(nil),
		(*UploadUpdate_FileMetadata)(nil),
		(*UploadUpdate_ArticleMetadata)(nil),
		(*UploadUpdate_YoutubeMetadata)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_content_upload_proto_rawDesc), len(file_content_upload_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_content_upload_proto_goTypes,
		DependencyIndexes: file_content_upload_proto_depIdxs,
		MessageInfos:      file_content_upload_proto_msgTypes,
	}.Build()
	File_content_upload_proto = out.File
	file_content_upload_proto_goTypes = nil
	file_content_upload_proto_depIdxs = nil
}
