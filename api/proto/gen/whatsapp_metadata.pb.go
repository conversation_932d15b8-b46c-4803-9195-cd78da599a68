// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: whatsapp_metadata.proto

package gen

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Request to detect WhatsApp content
type DetectWhatsAppRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Content can be provided directly or referenced by ID
	//
	// Types that are valid to be assigned to ContentSource:
	//
	//	*DetectWhatsAppRequest_Content
	//	*DetectWhatsAppRequest_ContentId
	ContentSource isDetectWhatsAppRequest_ContentSource `protobuf_oneof:"content_source"`
	FileName      string                                `protobuf:"bytes,3,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DetectWhatsAppRequest) Reset() {
	*x = DetectWhatsAppRequest{}
	mi := &file_whatsapp_metadata_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DetectWhatsAppRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetectWhatsAppRequest) ProtoMessage() {}

func (x *DetectWhatsAppRequest) ProtoReflect() protoreflect.Message {
	mi := &file_whatsapp_metadata_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetectWhatsAppRequest.ProtoReflect.Descriptor instead.
func (*DetectWhatsAppRequest) Descriptor() ([]byte, []int) {
	return file_whatsapp_metadata_proto_rawDescGZIP(), []int{0}
}

func (x *DetectWhatsAppRequest) GetContentSource() isDetectWhatsAppRequest_ContentSource {
	if x != nil {
		return x.ContentSource
	}
	return nil
}

func (x *DetectWhatsAppRequest) GetContent() string {
	if x != nil {
		if x, ok := x.ContentSource.(*DetectWhatsAppRequest_Content); ok {
			return x.Content
		}
	}
	return ""
}

func (x *DetectWhatsAppRequest) GetContentId() string {
	if x != nil {
		if x, ok := x.ContentSource.(*DetectWhatsAppRequest_ContentId); ok {
			return x.ContentId
		}
	}
	return ""
}

func (x *DetectWhatsAppRequest) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

type isDetectWhatsAppRequest_ContentSource interface {
	isDetectWhatsAppRequest_ContentSource()
}

type DetectWhatsAppRequest_Content struct {
	Content string `protobuf:"bytes,1,opt,name=content,proto3,oneof"`
}

type DetectWhatsAppRequest_ContentId struct {
	ContentId string `protobuf:"bytes,2,opt,name=content_id,json=contentId,proto3,oneof"`
}

func (*DetectWhatsAppRequest_Content) isDetectWhatsAppRequest_ContentSource() {}

func (*DetectWhatsAppRequest_ContentId) isDetectWhatsAppRequest_ContentSource() {}

// Response with WhatsApp detection results
type DetectWhatsAppResponse struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	IsWhatsappChat  bool                   `protobuf:"varint,1,opt,name=is_whatsapp_chat,json=isWhatsappChat,proto3" json:"is_whatsapp_chat,omitempty"`
	ConfidenceScore float32                `protobuf:"fixed32,2,opt,name=confidence_score,json=confidenceScore,proto3" json:"confidence_score,omitempty"`
	Metadata        *WhatsAppMetadata      `protobuf:"bytes,3,opt,name=metadata,proto3" json:"metadata,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *DetectWhatsAppResponse) Reset() {
	*x = DetectWhatsAppResponse{}
	mi := &file_whatsapp_metadata_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DetectWhatsAppResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DetectWhatsAppResponse) ProtoMessage() {}

func (x *DetectWhatsAppResponse) ProtoReflect() protoreflect.Message {
	mi := &file_whatsapp_metadata_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DetectWhatsAppResponse.ProtoReflect.Descriptor instead.
func (*DetectWhatsAppResponse) Descriptor() ([]byte, []int) {
	return file_whatsapp_metadata_proto_rawDescGZIP(), []int{1}
}

func (x *DetectWhatsAppResponse) GetIsWhatsappChat() bool {
	if x != nil {
		return x.IsWhatsappChat
	}
	return false
}

func (x *DetectWhatsAppResponse) GetConfidenceScore() float32 {
	if x != nil {
		return x.ConfidenceScore
	}
	return 0
}

func (x *DetectWhatsAppResponse) GetMetadata() *WhatsAppMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

// Request to process WhatsApp content
type ProcessWhatsAppRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Content can be provided directly or referenced by ID
	//
	// Types that are valid to be assigned to ContentSource:
	//
	//	*ProcessWhatsAppRequest_Content
	//	*ProcessWhatsAppRequest_ContentId
	ContentSource isProcessWhatsAppRequest_ContentSource `protobuf_oneof:"content_source"`
	FileName      string                                 `protobuf:"bytes,3,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ProcessWhatsAppRequest) Reset() {
	*x = ProcessWhatsAppRequest{}
	mi := &file_whatsapp_metadata_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProcessWhatsAppRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessWhatsAppRequest) ProtoMessage() {}

func (x *ProcessWhatsAppRequest) ProtoReflect() protoreflect.Message {
	mi := &file_whatsapp_metadata_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessWhatsAppRequest.ProtoReflect.Descriptor instead.
func (*ProcessWhatsAppRequest) Descriptor() ([]byte, []int) {
	return file_whatsapp_metadata_proto_rawDescGZIP(), []int{2}
}

func (x *ProcessWhatsAppRequest) GetContentSource() isProcessWhatsAppRequest_ContentSource {
	if x != nil {
		return x.ContentSource
	}
	return nil
}

func (x *ProcessWhatsAppRequest) GetContent() string {
	if x != nil {
		if x, ok := x.ContentSource.(*ProcessWhatsAppRequest_Content); ok {
			return x.Content
		}
	}
	return ""
}

func (x *ProcessWhatsAppRequest) GetContentId() string {
	if x != nil {
		if x, ok := x.ContentSource.(*ProcessWhatsAppRequest_ContentId); ok {
			return x.ContentId
		}
	}
	return ""
}

func (x *ProcessWhatsAppRequest) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

type isProcessWhatsAppRequest_ContentSource interface {
	isProcessWhatsAppRequest_ContentSource()
}

type ProcessWhatsAppRequest_Content struct {
	Content string `protobuf:"bytes,1,opt,name=content,proto3,oneof"`
}

type ProcessWhatsAppRequest_ContentId struct {
	ContentId string `protobuf:"bytes,2,opt,name=content_id,json=contentId,proto3,oneof"`
}

func (*ProcessWhatsAppRequest_Content) isProcessWhatsAppRequest_ContentSource() {}

func (*ProcessWhatsAppRequest_ContentId) isProcessWhatsAppRequest_ContentSource() {}

// Response with processed WhatsApp content
type ProcessWhatsAppResponse struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	ProcessedContent string                 `protobuf:"bytes,1,opt,name=processed_content,json=processedContent,proto3" json:"processed_content,omitempty"`
	Metadata         *WhatsAppMetadata      `protobuf:"bytes,2,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Variables        map[string]string      `protobuf:"bytes,3,rep,name=variables,proto3" json:"variables,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *ProcessWhatsAppResponse) Reset() {
	*x = ProcessWhatsAppResponse{}
	mi := &file_whatsapp_metadata_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProcessWhatsAppResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessWhatsAppResponse) ProtoMessage() {}

func (x *ProcessWhatsAppResponse) ProtoReflect() protoreflect.Message {
	mi := &file_whatsapp_metadata_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessWhatsAppResponse.ProtoReflect.Descriptor instead.
func (*ProcessWhatsAppResponse) Descriptor() ([]byte, []int) {
	return file_whatsapp_metadata_proto_rawDescGZIP(), []int{3}
}

func (x *ProcessWhatsAppResponse) GetProcessedContent() string {
	if x != nil {
		return x.ProcessedContent
	}
	return ""
}

func (x *ProcessWhatsAppResponse) GetMetadata() *WhatsAppMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *ProcessWhatsAppResponse) GetVariables() map[string]string {
	if x != nil {
		return x.Variables
	}
	return nil
}

// Metadata specific to WhatsApp chats
type WhatsAppMetadata struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	GroupName             string                 `protobuf:"bytes,1,opt,name=group_name,json=groupName,proto3" json:"group_name,omitempty"`
	ChatName              string                 `protobuf:"bytes,2,opt,name=chat_name,json=chatName,proto3" json:"chat_name,omitempty"`
	Participants          []string               `protobuf:"bytes,3,rep,name=participants,proto3" json:"participants,omitempty"`
	ParticipantCount      int32                  `protobuf:"varint,4,opt,name=participant_count,json=participantCount,proto3" json:"participant_count,omitempty"`
	MessageCount          int32                  `protobuf:"varint,5,opt,name=message_count,json=messageCount,proto3" json:"message_count,omitempty"`
	FirstMessageTimestamp int64                  `protobuf:"varint,6,opt,name=first_message_timestamp,json=firstMessageTimestamp,proto3" json:"first_message_timestamp,omitempty"`
	LastMessageTimestamp  int64                  `protobuf:"varint,7,opt,name=last_message_timestamp,json=lastMessageTimestamp,proto3" json:"last_message_timestamp,omitempty"`
	SampleMessages        []*WhatsAppMessage     `protobuf:"bytes,8,rep,name=sample_messages,json=sampleMessages,proto3" json:"sample_messages,omitempty"`
	IsGroupChat           bool                   `protobuf:"varint,9,opt,name=is_group_chat,json=isGroupChat,proto3" json:"is_group_chat,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *WhatsAppMetadata) Reset() {
	*x = WhatsAppMetadata{}
	mi := &file_whatsapp_metadata_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WhatsAppMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WhatsAppMetadata) ProtoMessage() {}

func (x *WhatsAppMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_whatsapp_metadata_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WhatsAppMetadata.ProtoReflect.Descriptor instead.
func (*WhatsAppMetadata) Descriptor() ([]byte, []int) {
	return file_whatsapp_metadata_proto_rawDescGZIP(), []int{4}
}

func (x *WhatsAppMetadata) GetGroupName() string {
	if x != nil {
		return x.GroupName
	}
	return ""
}

func (x *WhatsAppMetadata) GetChatName() string {
	if x != nil {
		return x.ChatName
	}
	return ""
}

func (x *WhatsAppMetadata) GetParticipants() []string {
	if x != nil {
		return x.Participants
	}
	return nil
}

func (x *WhatsAppMetadata) GetParticipantCount() int32 {
	if x != nil {
		return x.ParticipantCount
	}
	return 0
}

func (x *WhatsAppMetadata) GetMessageCount() int32 {
	if x != nil {
		return x.MessageCount
	}
	return 0
}

func (x *WhatsAppMetadata) GetFirstMessageTimestamp() int64 {
	if x != nil {
		return x.FirstMessageTimestamp
	}
	return 0
}

func (x *WhatsAppMetadata) GetLastMessageTimestamp() int64 {
	if x != nil {
		return x.LastMessageTimestamp
	}
	return 0
}

func (x *WhatsAppMetadata) GetSampleMessages() []*WhatsAppMessage {
	if x != nil {
		return x.SampleMessages
	}
	return nil
}

func (x *WhatsAppMetadata) GetIsGroupChat() bool {
	if x != nil {
		return x.IsGroupChat
	}
	return false
}

// Represents a WhatsApp message
type WhatsAppMessage struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Sender        string                 `protobuf:"bytes,1,opt,name=sender,proto3" json:"sender,omitempty"`
	Timestamp     int64                  `protobuf:"varint,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Content       string                 `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	HasMedia      bool                   `protobuf:"varint,4,opt,name=has_media,json=hasMedia,proto3" json:"has_media,omitempty"`
	MediaType     string                 `protobuf:"bytes,5,opt,name=media_type,json=mediaType,proto3" json:"media_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WhatsAppMessage) Reset() {
	*x = WhatsAppMessage{}
	mi := &file_whatsapp_metadata_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WhatsAppMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WhatsAppMessage) ProtoMessage() {}

func (x *WhatsAppMessage) ProtoReflect() protoreflect.Message {
	mi := &file_whatsapp_metadata_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WhatsAppMessage.ProtoReflect.Descriptor instead.
func (*WhatsAppMessage) Descriptor() ([]byte, []int) {
	return file_whatsapp_metadata_proto_rawDescGZIP(), []int{5}
}

func (x *WhatsAppMessage) GetSender() string {
	if x != nil {
		return x.Sender
	}
	return ""
}

func (x *WhatsAppMessage) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *WhatsAppMessage) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *WhatsAppMessage) GetHasMedia() bool {
	if x != nil {
		return x.HasMedia
	}
	return false
}

func (x *WhatsAppMessage) GetMediaType() string {
	if x != nil {
		return x.MediaType
	}
	return ""
}

// WhatsAppResultsResponse extends the standard ResultsResponse with WhatsApp-specific fields
type WhatsAppResultsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Standard metadata fields from ResultsResponse.Metadata
	Id             string            `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ContentType    string            `protobuf:"bytes,2,opt,name=content_type,json=contentType,proto3" json:"content_type,omitempty"`
	Metadata       map[string]string `protobuf:"bytes,3,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	HasFullContent bool              `protobuf:"varint,4,opt,name=has_full_content,json=hasFullContent,proto3" json:"has_full_content,omitempty"`
	ContentUrl     string            `protobuf:"bytes,5,opt,name=content_url,json=contentUrl,proto3" json:"content_url,omitempty"`
	ExpiresAt      int64             `protobuf:"varint,6,opt,name=expires_at,json=expiresAt,proto3" json:"expires_at,omitempty"`
	// WhatsApp-specific fields
	WhatsappMetadata *WhatsAppMetadata `protobuf:"bytes,10,opt,name=whatsapp_metadata,json=whatsappMetadata,proto3" json:"whatsapp_metadata,omitempty"`
	IsWhatsappChat   bool              `protobuf:"varint,11,opt,name=is_whatsapp_chat,json=isWhatsappChat,proto3" json:"is_whatsapp_chat,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *WhatsAppResultsResponse) Reset() {
	*x = WhatsAppResultsResponse{}
	mi := &file_whatsapp_metadata_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WhatsAppResultsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WhatsAppResultsResponse) ProtoMessage() {}

func (x *WhatsAppResultsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_whatsapp_metadata_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WhatsAppResultsResponse.ProtoReflect.Descriptor instead.
func (*WhatsAppResultsResponse) Descriptor() ([]byte, []int) {
	return file_whatsapp_metadata_proto_rawDescGZIP(), []int{6}
}

func (x *WhatsAppResultsResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *WhatsAppResultsResponse) GetContentType() string {
	if x != nil {
		return x.ContentType
	}
	return ""
}

func (x *WhatsAppResultsResponse) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *WhatsAppResultsResponse) GetHasFullContent() bool {
	if x != nil {
		return x.HasFullContent
	}
	return false
}

func (x *WhatsAppResultsResponse) GetContentUrl() string {
	if x != nil {
		return x.ContentUrl
	}
	return ""
}

func (x *WhatsAppResultsResponse) GetExpiresAt() int64 {
	if x != nil {
		return x.ExpiresAt
	}
	return 0
}

func (x *WhatsAppResultsResponse) GetWhatsappMetadata() *WhatsAppMetadata {
	if x != nil {
		return x.WhatsappMetadata
	}
	return nil
}

func (x *WhatsAppResultsResponse) GetIsWhatsappChat() bool {
	if x != nil {
		return x.IsWhatsappChat
	}
	return false
}

var File_whatsapp_metadata_proto protoreflect.FileDescriptor

const file_whatsapp_metadata_proto_rawDesc = "" +
	"\n" +
	"\x17whatsapp_metadata.proto\x12\fpromz.api.v1\"\x83\x01\n" +
	"\x15DetectWhatsAppRequest\x12\x1a\n" +
	"\acontent\x18\x01 \x01(\tH\x00R\acontent\x12\x1f\n" +
	"\n" +
	"content_id\x18\x02 \x01(\tH\x00R\tcontentId\x12\x1b\n" +
	"\tfile_name\x18\x03 \x01(\tR\bfileNameB\x10\n" +
	"\x0econtent_source\"\xa9\x01\n" +
	"\x16DetectWhatsAppResponse\x12(\n" +
	"\x10is_whatsapp_chat\x18\x01 \x01(\bR\x0eisWhatsappChat\x12)\n" +
	"\x10confidence_score\x18\x02 \x01(\x02R\x0fconfidenceScore\x12:\n" +
	"\bmetadata\x18\x03 \x01(\v2\x1e.promz.api.v1.WhatsAppMetadataR\bmetadata\"\x84\x01\n" +
	"\x16ProcessWhatsAppRequest\x12\x1a\n" +
	"\acontent\x18\x01 \x01(\tH\x00R\acontent\x12\x1f\n" +
	"\n" +
	"content_id\x18\x02 \x01(\tH\x00R\tcontentId\x12\x1b\n" +
	"\tfile_name\x18\x03 \x01(\tR\bfileNameB\x10\n" +
	"\x0econtent_source\"\x94\x02\n" +
	"\x17ProcessWhatsAppResponse\x12+\n" +
	"\x11processed_content\x18\x01 \x01(\tR\x10processedContent\x12:\n" +
	"\bmetadata\x18\x02 \x01(\v2\x1e.promz.api.v1.WhatsAppMetadataR\bmetadata\x12R\n" +
	"\tvariables\x18\x03 \x03(\v24.promz.api.v1.ProcessWhatsAppResponse.VariablesEntryR\tvariables\x1a<\n" +
	"\x0eVariablesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\x9e\x03\n" +
	"\x10WhatsAppMetadata\x12\x1d\n" +
	"\n" +
	"group_name\x18\x01 \x01(\tR\tgroupName\x12\x1b\n" +
	"\tchat_name\x18\x02 \x01(\tR\bchatName\x12\"\n" +
	"\fparticipants\x18\x03 \x03(\tR\fparticipants\x12+\n" +
	"\x11participant_count\x18\x04 \x01(\x05R\x10participantCount\x12#\n" +
	"\rmessage_count\x18\x05 \x01(\x05R\fmessageCount\x126\n" +
	"\x17first_message_timestamp\x18\x06 \x01(\x03R\x15firstMessageTimestamp\x124\n" +
	"\x16last_message_timestamp\x18\a \x01(\x03R\x14lastMessageTimestamp\x12F\n" +
	"\x0fsample_messages\x18\b \x03(\v2\x1d.promz.api.v1.WhatsAppMessageR\x0esampleMessages\x12\"\n" +
	"\ris_group_chat\x18\t \x01(\bR\visGroupChat\"\x9d\x01\n" +
	"\x0fWhatsAppMessage\x12\x16\n" +
	"\x06sender\x18\x01 \x01(\tR\x06sender\x12\x1c\n" +
	"\ttimestamp\x18\x02 \x01(\x03R\ttimestamp\x12\x18\n" +
	"\acontent\x18\x03 \x01(\tR\acontent\x12\x1b\n" +
	"\thas_media\x18\x04 \x01(\bR\bhasMedia\x12\x1d\n" +
	"\n" +
	"media_type\x18\x05 \x01(\tR\tmediaType\"\xbb\x03\n" +
	"\x17WhatsAppResultsResponse\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12!\n" +
	"\fcontent_type\x18\x02 \x01(\tR\vcontentType\x12O\n" +
	"\bmetadata\x18\x03 \x03(\v23.promz.api.v1.WhatsAppResultsResponse.MetadataEntryR\bmetadata\x12(\n" +
	"\x10has_full_content\x18\x04 \x01(\bR\x0ehasFullContent\x12\x1f\n" +
	"\vcontent_url\x18\x05 \x01(\tR\n" +
	"contentUrl\x12\x1d\n" +
	"\n" +
	"expires_at\x18\x06 \x01(\x03R\texpiresAt\x12K\n" +
	"\x11whatsapp_metadata\x18\n" +
	" \x01(\v2\x1e.promz.api.v1.WhatsAppMetadataR\x10whatsappMetadata\x12(\n" +
	"\x10is_whatsapp_chat\x18\v \x01(\bR\x0eisWhatsappChat\x1a;\n" +
	"\rMetadataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x012\xe6\x01\n" +
	"\x19WhatsAppProcessingService\x12b\n" +
	"\x15DetectWhatsAppContent\x12#.promz.api.v1.DetectWhatsAppRequest\x1a$.promz.api.v1.DetectWhatsAppResponse\x12e\n" +
	"\x16ProcessWhatsAppContent\x12$.promz.api.v1.ProcessWhatsAppRequest\x1a%.promz.api.v1.ProcessWhatsAppResponseB\x18Z\x16promz.ai/api/proto/genb\x06proto3"

var (
	file_whatsapp_metadata_proto_rawDescOnce sync.Once
	file_whatsapp_metadata_proto_rawDescData []byte
)

func file_whatsapp_metadata_proto_rawDescGZIP() []byte {
	file_whatsapp_metadata_proto_rawDescOnce.Do(func() {
		file_whatsapp_metadata_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_whatsapp_metadata_proto_rawDesc), len(file_whatsapp_metadata_proto_rawDesc)))
	})
	return file_whatsapp_metadata_proto_rawDescData
}

var file_whatsapp_metadata_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_whatsapp_metadata_proto_goTypes = []any{
	(*DetectWhatsAppRequest)(nil),   // 0: promz.api.v1.DetectWhatsAppRequest
	(*DetectWhatsAppResponse)(nil),  // 1: promz.api.v1.DetectWhatsAppResponse
	(*ProcessWhatsAppRequest)(nil),  // 2: promz.api.v1.ProcessWhatsAppRequest
	(*ProcessWhatsAppResponse)(nil), // 3: promz.api.v1.ProcessWhatsAppResponse
	(*WhatsAppMetadata)(nil),        // 4: promz.api.v1.WhatsAppMetadata
	(*WhatsAppMessage)(nil),         // 5: promz.api.v1.WhatsAppMessage
	(*WhatsAppResultsResponse)(nil), // 6: promz.api.v1.WhatsAppResultsResponse
	nil,                             // 7: promz.api.v1.ProcessWhatsAppResponse.VariablesEntry
	nil,                             // 8: promz.api.v1.WhatsAppResultsResponse.MetadataEntry
}
var file_whatsapp_metadata_proto_depIdxs = []int32{
	4, // 0: promz.api.v1.DetectWhatsAppResponse.metadata:type_name -> promz.api.v1.WhatsAppMetadata
	4, // 1: promz.api.v1.ProcessWhatsAppResponse.metadata:type_name -> promz.api.v1.WhatsAppMetadata
	7, // 2: promz.api.v1.ProcessWhatsAppResponse.variables:type_name -> promz.api.v1.ProcessWhatsAppResponse.VariablesEntry
	5, // 3: promz.api.v1.WhatsAppMetadata.sample_messages:type_name -> promz.api.v1.WhatsAppMessage
	8, // 4: promz.api.v1.WhatsAppResultsResponse.metadata:type_name -> promz.api.v1.WhatsAppResultsResponse.MetadataEntry
	4, // 5: promz.api.v1.WhatsAppResultsResponse.whatsapp_metadata:type_name -> promz.api.v1.WhatsAppMetadata
	0, // 6: promz.api.v1.WhatsAppProcessingService.DetectWhatsAppContent:input_type -> promz.api.v1.DetectWhatsAppRequest
	2, // 7: promz.api.v1.WhatsAppProcessingService.ProcessWhatsAppContent:input_type -> promz.api.v1.ProcessWhatsAppRequest
	1, // 8: promz.api.v1.WhatsAppProcessingService.DetectWhatsAppContent:output_type -> promz.api.v1.DetectWhatsAppResponse
	3, // 9: promz.api.v1.WhatsAppProcessingService.ProcessWhatsAppContent:output_type -> promz.api.v1.ProcessWhatsAppResponse
	8, // [8:10] is the sub-list for method output_type
	6, // [6:8] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_whatsapp_metadata_proto_init() }
func file_whatsapp_metadata_proto_init() {
	if File_whatsapp_metadata_proto != nil {
		return
	}
	file_whatsapp_metadata_proto_msgTypes[0].OneofWrappers = []any{
		(*DetectWhatsAppRequest_Content)(nil),
		(*DetectWhatsAppRequest_ContentId)(nil),
	}
	file_whatsapp_metadata_proto_msgTypes[2].OneofWrappers = []any{
		(*ProcessWhatsAppRequest_Content)(nil),
		(*ProcessWhatsAppRequest_ContentId)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_whatsapp_metadata_proto_rawDesc), len(file_whatsapp_metadata_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_whatsapp_metadata_proto_goTypes,
		DependencyIndexes: file_whatsapp_metadata_proto_depIdxs,
		MessageInfos:      file_whatsapp_metadata_proto_msgTypes,
	}.Build()
	File_whatsapp_metadata_proto = out.File
	file_whatsapp_metadata_proto_goTypes = nil
	file_whatsapp_metadata_proto_depIdxs = nil
}
