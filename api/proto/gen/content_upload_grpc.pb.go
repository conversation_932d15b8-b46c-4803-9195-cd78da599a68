// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.30.2
// source: content_upload.proto

package gen

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	ContentUploadService_UploadFile_FullMethodName          = "/promz.api.v1.ContentUploadService/UploadFile"
	ContentUploadService_GetUploadStatus_FullMethodName     = "/promz.api.v1.ContentUploadService/GetUploadStatus"
	ContentUploadService_StreamUploadUpdates_FullMethodName = "/promz.api.v1.ContentUploadService/StreamUploadUpdates"
)

// ContentUploadServiceClient is the client API for ContentUploadService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// Content upload service for handling various types of content
type ContentUploadServiceClient interface {
	// Upload a file and extract its content and metadata
	UploadFile(ctx context.Context, in *UploadFileRequest, opts ...grpc.CallOption) (*ProcessingResult, error)
	// Get the status and result of an upload job
	GetUploadStatus(ctx context.Context, in *UploadStatusRequest, opts ...grpc.CallOption) (*ProcessingResult, error)
	// Stream upload updates for real-time feedback
	StreamUploadUpdates(ctx context.Context, in *UploadStatusRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[UploadUpdate], error)
}

type contentUploadServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewContentUploadServiceClient(cc grpc.ClientConnInterface) ContentUploadServiceClient {
	return &contentUploadServiceClient{cc}
}

func (c *contentUploadServiceClient) UploadFile(ctx context.Context, in *UploadFileRequest, opts ...grpc.CallOption) (*ProcessingResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ProcessingResult)
	err := c.cc.Invoke(ctx, ContentUploadService_UploadFile_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contentUploadServiceClient) GetUploadStatus(ctx context.Context, in *UploadStatusRequest, opts ...grpc.CallOption) (*ProcessingResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ProcessingResult)
	err := c.cc.Invoke(ctx, ContentUploadService_GetUploadStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *contentUploadServiceClient) StreamUploadUpdates(ctx context.Context, in *UploadStatusRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[UploadUpdate], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &ContentUploadService_ServiceDesc.Streams[0], ContentUploadService_StreamUploadUpdates_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[UploadStatusRequest, UploadUpdate]{ClientStream: stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type ContentUploadService_StreamUploadUpdatesClient = grpc.ServerStreamingClient[UploadUpdate]

// ContentUploadServiceServer is the server API for ContentUploadService service.
// All implementations must embed UnimplementedContentUploadServiceServer
// for forward compatibility.
//
// Content upload service for handling various types of content
type ContentUploadServiceServer interface {
	// Upload a file and extract its content and metadata
	UploadFile(context.Context, *UploadFileRequest) (*ProcessingResult, error)
	// Get the status and result of an upload job
	GetUploadStatus(context.Context, *UploadStatusRequest) (*ProcessingResult, error)
	// Stream upload updates for real-time feedback
	StreamUploadUpdates(*UploadStatusRequest, grpc.ServerStreamingServer[UploadUpdate]) error
	mustEmbedUnimplementedContentUploadServiceServer()
}

// UnimplementedContentUploadServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedContentUploadServiceServer struct{}

func (UnimplementedContentUploadServiceServer) UploadFile(context.Context, *UploadFileRequest) (*ProcessingResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadFile not implemented")
}
func (UnimplementedContentUploadServiceServer) GetUploadStatus(context.Context, *UploadStatusRequest) (*ProcessingResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUploadStatus not implemented")
}
func (UnimplementedContentUploadServiceServer) StreamUploadUpdates(*UploadStatusRequest, grpc.ServerStreamingServer[UploadUpdate]) error {
	return status.Errorf(codes.Unimplemented, "method StreamUploadUpdates not implemented")
}
func (UnimplementedContentUploadServiceServer) mustEmbedUnimplementedContentUploadServiceServer() {}
func (UnimplementedContentUploadServiceServer) testEmbeddedByValue()                              {}

// UnsafeContentUploadServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ContentUploadServiceServer will
// result in compilation errors.
type UnsafeContentUploadServiceServer interface {
	mustEmbedUnimplementedContentUploadServiceServer()
}

func RegisterContentUploadServiceServer(s grpc.ServiceRegistrar, srv ContentUploadServiceServer) {
	// If the following call pancis, it indicates UnimplementedContentUploadServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ContentUploadService_ServiceDesc, srv)
}

func _ContentUploadService_UploadFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadFileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContentUploadServiceServer).UploadFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ContentUploadService_UploadFile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContentUploadServiceServer).UploadFile(ctx, req.(*UploadFileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContentUploadService_GetUploadStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ContentUploadServiceServer).GetUploadStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ContentUploadService_GetUploadStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ContentUploadServiceServer).GetUploadStatus(ctx, req.(*UploadStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ContentUploadService_StreamUploadUpdates_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(UploadStatusRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(ContentUploadServiceServer).StreamUploadUpdates(m, &grpc.GenericServerStream[UploadStatusRequest, UploadUpdate]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type ContentUploadService_StreamUploadUpdatesServer = grpc.ServerStreamingServer[UploadUpdate]

// ContentUploadService_ServiceDesc is the grpc.ServiceDesc for ContentUploadService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ContentUploadService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "promz.api.v1.ContentUploadService",
	HandlerType: (*ContentUploadServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UploadFile",
			Handler:    _ContentUploadService_UploadFile_Handler,
		},
		{
			MethodName: "GetUploadStatus",
			Handler:    _ContentUploadService_GetUploadStatus_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "StreamUploadUpdates",
			Handler:       _ContentUploadService_StreamUploadUpdates_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "content_upload.proto",
}
