// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: common.proto

package gen

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// LicenseTier represents the user's license tier
type LicenseTier int32

const (
	// Unknown or unspecified tier
	LicenseTier_LICENSE_TIER_UNSPECIFIED LicenseTier = 0
	// Free tier
	LicenseTier_LICENSE_TIER_FREE LicenseTier = 1
	// Pro tier
	LicenseTier_LICENSE_TIER_PRO LicenseTier = 2
	// Enterprise tier
	LicenseTier_LICENSE_TIER_ENTERPRISE LicenseTier = 3
)

// Enum value maps for LicenseTier.
var (
	LicenseTier_name = map[int32]string{
		0: "LICENSE_TIER_UNSPECIFIED",
		1: "LICENSE_TIER_FREE",
		2: "LICENSE_TIER_PRO",
		3: "LICENSE_TIER_ENTERPRISE",
	}
	LicenseTier_value = map[string]int32{
		"LICENSE_TIER_UNSPECIFIED": 0,
		"LICENSE_TIER_FREE":        1,
		"LICENSE_TIER_PRO":         2,
		"LICENSE_TIER_ENTERPRISE":  3,
	}
)

func (x LicenseTier) Enum() *LicenseTier {
	p := new(LicenseTier)
	*p = x
	return p
}

func (x LicenseTier) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LicenseTier) Descriptor() protoreflect.EnumDescriptor {
	return file_common_proto_enumTypes[0].Descriptor()
}

func (LicenseTier) Type() protoreflect.EnumType {
	return &file_common_proto_enumTypes[0]
}

func (x LicenseTier) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LicenseTier.Descriptor instead.
func (LicenseTier) EnumDescriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{0}
}

// UploadStatus represents the status of a processing job
type UploadStatus int32

const (
	// Unknown or unspecified status
	UploadStatus_UPLOAD_STATUS_UNSPECIFIED UploadStatus = 0
	// Job is queued for processing
	UploadStatus_UPLOAD_STATUS_QUEUED UploadStatus = 1
	// Job is currently processing
	UploadStatus_UPLOAD_STATUS_PROCESSING UploadStatus = 2
	// Job has completed successfully
	UploadStatus_UPLOAD_STATUS_COMPLETED UploadStatus = 3
	// Job has failed
	UploadStatus_UPLOAD_STATUS_FAILED UploadStatus = 4
	// Job has been cancelled
	UploadStatus_UPLOAD_STATUS_CANCELLED UploadStatus = 5
)

// Enum value maps for UploadStatus.
var (
	UploadStatus_name = map[int32]string{
		0: "UPLOAD_STATUS_UNSPECIFIED",
		1: "UPLOAD_STATUS_QUEUED",
		2: "UPLOAD_STATUS_PROCESSING",
		3: "UPLOAD_STATUS_COMPLETED",
		4: "UPLOAD_STATUS_FAILED",
		5: "UPLOAD_STATUS_CANCELLED",
	}
	UploadStatus_value = map[string]int32{
		"UPLOAD_STATUS_UNSPECIFIED": 0,
		"UPLOAD_STATUS_QUEUED":      1,
		"UPLOAD_STATUS_PROCESSING":  2,
		"UPLOAD_STATUS_COMPLETED":   3,
		"UPLOAD_STATUS_FAILED":      4,
		"UPLOAD_STATUS_CANCELLED":   5,
	}
)

func (x UploadStatus) Enum() *UploadStatus {
	p := new(UploadStatus)
	*p = x
	return p
}

func (x UploadStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UploadStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_common_proto_enumTypes[1].Descriptor()
}

func (UploadStatus) Type() protoreflect.EnumType {
	return &file_common_proto_enumTypes[1]
}

func (x UploadStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UploadStatus.Descriptor instead.
func (UploadStatus) EnumDescriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{1}
}

// Error represents an error response
type Error struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Error code
	Code string `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	// Error message
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	// Additional error details
	Details       map[string]string `protobuf:"bytes,3,rep,name=details,proto3" json:"details,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Error) Reset() {
	*x = Error{}
	mi := &file_common_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Error) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Error) ProtoMessage() {}

func (x *Error) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Error.ProtoReflect.Descriptor instead.
func (*Error) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{0}
}

func (x *Error) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *Error) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *Error) GetDetails() map[string]string {
	if x != nil {
		return x.Details
	}
	return nil
}

// TierLimits represents the limits for a license tier
type TierLimits struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Maximum file size in bytes
	MaxFileSizeBytes int64 `protobuf:"varint,1,opt,name=max_file_size_bytes,json=maxFileSizeBytes,proto3" json:"max_file_size_bytes,omitempty"`
	// Maximum token limit
	MaxTokens int32 `protobuf:"varint,2,opt,name=max_tokens,json=maxTokens,proto3" json:"max_tokens,omitempty"`
	// Maximum storage days
	MaxStorageDays int32 `protobuf:"varint,3,opt,name=max_storage_days,json=maxStorageDays,proto3" json:"max_storage_days,omitempty"`
	// Maximum concurrent jobs
	MaxConcurrentJobs int32 `protobuf:"varint,4,opt,name=max_concurrent_jobs,json=maxConcurrentJobs,proto3" json:"max_concurrent_jobs,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *TierLimits) Reset() {
	*x = TierLimits{}
	mi := &file_common_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TierLimits) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TierLimits) ProtoMessage() {}

func (x *TierLimits) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TierLimits.ProtoReflect.Descriptor instead.
func (*TierLimits) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{1}
}

func (x *TierLimits) GetMaxFileSizeBytes() int64 {
	if x != nil {
		return x.MaxFileSizeBytes
	}
	return 0
}

func (x *TierLimits) GetMaxTokens() int32 {
	if x != nil {
		return x.MaxTokens
	}
	return 0
}

func (x *TierLimits) GetMaxStorageDays() int32 {
	if x != nil {
		return x.MaxStorageDays
	}
	return 0
}

func (x *TierLimits) GetMaxConcurrentJobs() int32 {
	if x != nil {
		return x.MaxConcurrentJobs
	}
	return 0
}

// Timestamp represents a point in time
type Timestamp struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Seconds since epoch
	Seconds int64 `protobuf:"varint,1,opt,name=seconds,proto3" json:"seconds,omitempty"`
	// Nanoseconds within the second
	Nanos         int32 `protobuf:"varint,2,opt,name=nanos,proto3" json:"nanos,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Timestamp) Reset() {
	*x = Timestamp{}
	mi := &file_common_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Timestamp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Timestamp) ProtoMessage() {}

func (x *Timestamp) ProtoReflect() protoreflect.Message {
	mi := &file_common_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Timestamp.ProtoReflect.Descriptor instead.
func (*Timestamp) Descriptor() ([]byte, []int) {
	return file_common_proto_rawDescGZIP(), []int{2}
}

func (x *Timestamp) GetSeconds() int64 {
	if x != nil {
		return x.Seconds
	}
	return 0
}

func (x *Timestamp) GetNanos() int32 {
	if x != nil {
		return x.Nanos
	}
	return 0
}

var File_common_proto protoreflect.FileDescriptor

const file_common_proto_rawDesc = "" +
	"\n" +
	"\fcommon.proto\x12\fpromz.api.v1\"\xad\x01\n" +
	"\x05Error\x12\x12\n" +
	"\x04code\x18\x01 \x01(\tR\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12:\n" +
	"\adetails\x18\x03 \x03(\v2 .promz.api.v1.Error.DetailsEntryR\adetails\x1a:\n" +
	"\fDetailsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xb4\x01\n" +
	"\n" +
	"TierLimits\x12-\n" +
	"\x13max_file_size_bytes\x18\x01 \x01(\x03R\x10maxFileSizeBytes\x12\x1d\n" +
	"\n" +
	"max_tokens\x18\x02 \x01(\x05R\tmaxTokens\x12(\n" +
	"\x10max_storage_days\x18\x03 \x01(\x05R\x0emaxStorageDays\x12.\n" +
	"\x13max_concurrent_jobs\x18\x04 \x01(\x05R\x11maxConcurrentJobs\";\n" +
	"\tTimestamp\x12\x18\n" +
	"\aseconds\x18\x01 \x01(\x03R\aseconds\x12\x14\n" +
	"\x05nanos\x18\x02 \x01(\x05R\x05nanos*u\n" +
	"\vLicenseTier\x12\x1c\n" +
	"\x18LICENSE_TIER_UNSPECIFIED\x10\x00\x12\x15\n" +
	"\x11LICENSE_TIER_FREE\x10\x01\x12\x14\n" +
	"\x10LICENSE_TIER_PRO\x10\x02\x12\x1b\n" +
	"\x17LICENSE_TIER_ENTERPRISE\x10\x03*\xb9\x01\n" +
	"\fUploadStatus\x12\x1d\n" +
	"\x19UPLOAD_STATUS_UNSPECIFIED\x10\x00\x12\x18\n" +
	"\x14UPLOAD_STATUS_QUEUED\x10\x01\x12\x1c\n" +
	"\x18UPLOAD_STATUS_PROCESSING\x10\x02\x12\x1b\n" +
	"\x17UPLOAD_STATUS_COMPLETED\x10\x03\x12\x18\n" +
	"\x14UPLOAD_STATUS_FAILED\x10\x04\x12\x1b\n" +
	"\x17UPLOAD_STATUS_CANCELLED\x10\x05B\x18Z\x16promz.ai/api/proto/genb\x06proto3"

var (
	file_common_proto_rawDescOnce sync.Once
	file_common_proto_rawDescData []byte
)

func file_common_proto_rawDescGZIP() []byte {
	file_common_proto_rawDescOnce.Do(func() {
		file_common_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_common_proto_rawDesc), len(file_common_proto_rawDesc)))
	})
	return file_common_proto_rawDescData
}

var file_common_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_common_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_common_proto_goTypes = []any{
	(LicenseTier)(0),   // 0: promz.api.v1.LicenseTier
	(UploadStatus)(0),  // 1: promz.api.v1.UploadStatus
	(*Error)(nil),      // 2: promz.api.v1.Error
	(*TierLimits)(nil), // 3: promz.api.v1.TierLimits
	(*Timestamp)(nil),  // 4: promz.api.v1.Timestamp
	nil,                // 5: promz.api.v1.Error.DetailsEntry
}
var file_common_proto_depIdxs = []int32{
	5, // 0: promz.api.v1.Error.details:type_name -> promz.api.v1.Error.DetailsEntry
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_common_proto_init() }
func file_common_proto_init() {
	if File_common_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_common_proto_rawDesc), len(file_common_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_common_proto_goTypes,
		DependencyIndexes: file_common_proto_depIdxs,
		EnumInfos:         file_common_proto_enumTypes,
		MessageInfos:      file_common_proto_msgTypes,
	}.Build()
	File_common_proto = out.File
	file_common_proto_goTypes = nil
	file_common_proto_depIdxs = nil
}
