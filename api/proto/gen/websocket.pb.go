// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: websocket.proto

package gen

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// WebSocketMessage represents a message sent over WebSocket
type WebSocketMessage struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Type of the message
	Type string `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	// Action to perform
	Action string `protobuf:"bytes,2,opt,name=action,proto3" json:"action,omitempty"`
	// Payload data (JSON-encoded)
	Payload []byte `protobuf:"bytes,3,opt,name=payload,proto3" json:"payload,omitempty"`
	// Topic for pub/sub messaging
	Topic string `protobuf:"bytes,4,opt,name=topic,proto3" json:"topic,omitempty"`
	// Message ID for correlation
	MessageId string `protobuf:"bytes,5,opt,name=message_id,json=messageId,proto3" json:"message_id,omitempty"`
	// Request ID for request-response pattern
	RequestId string `protobuf:"bytes,6,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// Error information if applicable
	Error         *ErrorInfo `protobuf:"bytes,7,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WebSocketMessage) Reset() {
	*x = WebSocketMessage{}
	mi := &file_websocket_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WebSocketMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebSocketMessage) ProtoMessage() {}

func (x *WebSocketMessage) ProtoReflect() protoreflect.Message {
	mi := &file_websocket_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebSocketMessage.ProtoReflect.Descriptor instead.
func (*WebSocketMessage) Descriptor() ([]byte, []int) {
	return file_websocket_proto_rawDescGZIP(), []int{0}
}

func (x *WebSocketMessage) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *WebSocketMessage) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

func (x *WebSocketMessage) GetPayload() []byte {
	if x != nil {
		return x.Payload
	}
	return nil
}

func (x *WebSocketMessage) GetTopic() string {
	if x != nil {
		return x.Topic
	}
	return ""
}

func (x *WebSocketMessage) GetMessageId() string {
	if x != nil {
		return x.MessageId
	}
	return ""
}

func (x *WebSocketMessage) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *WebSocketMessage) GetError() *ErrorInfo {
	if x != nil {
		return x.Error
	}
	return nil
}

// ErrorInfo contains error details
type ErrorInfo struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Error code
	Code string `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	// Error message
	Message string `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	// Additional error details
	Details       map[string]string `protobuf:"bytes,3,rep,name=details,proto3" json:"details,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ErrorInfo) Reset() {
	*x = ErrorInfo{}
	mi := &file_websocket_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ErrorInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ErrorInfo) ProtoMessage() {}

func (x *ErrorInfo) ProtoReflect() protoreflect.Message {
	mi := &file_websocket_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ErrorInfo.ProtoReflect.Descriptor instead.
func (*ErrorInfo) Descriptor() ([]byte, []int) {
	return file_websocket_proto_rawDescGZIP(), []int{1}
}

func (x *ErrorInfo) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *ErrorInfo) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ErrorInfo) GetDetails() map[string]string {
	if x != nil {
		return x.Details
	}
	return nil
}

// FileUploadUpdate represents a file processing status update
type FileUploadUpdate struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Job ID
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Current status (queued, processing, completed, failed)
	Status string `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	// Progress (0.0 to 1.0)
	Progress float64 `protobuf:"fixed64,3,opt,name=progress,proto3" json:"progress,omitempty"`
	// Error message if any
	Error string `protobuf:"bytes,4,opt,name=error,proto3" json:"error,omitempty"`
	// Informational message
	Message string `protobuf:"bytes,5,opt,name=message,proto3" json:"message,omitempty"`
	// Tokens processed so far
	TokensProcessed int32 `protobuf:"varint,6,opt,name=tokens_processed,json=tokensProcessed,proto3" json:"tokens_processed,omitempty"`
	// Token limit for the job
	TokensLimit int32 `protobuf:"varint,7,opt,name=tokens_limit,json=tokensLimit,proto3" json:"tokens_limit,omitempty"`
	// Whether token limit was exceeded
	TokensExceeded bool `protobuf:"varint,8,opt,name=tokens_exceeded,json=tokensExceeded,proto3" json:"tokens_exceeded,omitempty"`
	// Timestamp of the update
	Timestamp     int64 `protobuf:"varint,9,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FileUploadUpdate) Reset() {
	*x = FileUploadUpdate{}
	mi := &file_websocket_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileUploadUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileUploadUpdate) ProtoMessage() {}

func (x *FileUploadUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_websocket_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileUploadUpdate.ProtoReflect.Descriptor instead.
func (*FileUploadUpdate) Descriptor() ([]byte, []int) {
	return file_websocket_proto_rawDescGZIP(), []int{2}
}

func (x *FileUploadUpdate) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *FileUploadUpdate) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *FileUploadUpdate) GetProgress() float64 {
	if x != nil {
		return x.Progress
	}
	return 0
}

func (x *FileUploadUpdate) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

func (x *FileUploadUpdate) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *FileUploadUpdate) GetTokensProcessed() int32 {
	if x != nil {
		return x.TokensProcessed
	}
	return 0
}

func (x *FileUploadUpdate) GetTokensLimit() int32 {
	if x != nil {
		return x.TokensLimit
	}
	return 0
}

func (x *FileUploadUpdate) GetTokensExceeded() bool {
	if x != nil {
		return x.TokensExceeded
	}
	return false
}

func (x *FileUploadUpdate) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

// SubscriptionRequest represents a request to subscribe to a topic
type SubscriptionRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Topic to subscribe to
	Topic string `protobuf:"bytes,1,opt,name=topic,proto3" json:"topic,omitempty"`
	// Optional filter criteria
	Filter        map[string]string `protobuf:"bytes,2,rep,name=filter,proto3" json:"filter,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SubscriptionRequest) Reset() {
	*x = SubscriptionRequest{}
	mi := &file_websocket_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscriptionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscriptionRequest) ProtoMessage() {}

func (x *SubscriptionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_websocket_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscriptionRequest.ProtoReflect.Descriptor instead.
func (*SubscriptionRequest) Descriptor() ([]byte, []int) {
	return file_websocket_proto_rawDescGZIP(), []int{3}
}

func (x *SubscriptionRequest) GetTopic() string {
	if x != nil {
		return x.Topic
	}
	return ""
}

func (x *SubscriptionRequest) GetFilter() map[string]string {
	if x != nil {
		return x.Filter
	}
	return nil
}

// SubscriptionResponse represents a response to a subscription request
type SubscriptionResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Whether the subscription was successful
	Success bool `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	// Topic subscribed to
	Topic string `protobuf:"bytes,2,opt,name=topic,proto3" json:"topic,omitempty"`
	// Subscription ID
	SubscriptionId string `protobuf:"bytes,3,opt,name=subscription_id,json=subscriptionId,proto3" json:"subscription_id,omitempty"`
	// Error information if applicable
	Error         *ErrorInfo `protobuf:"bytes,4,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SubscriptionResponse) Reset() {
	*x = SubscriptionResponse{}
	mi := &file_websocket_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscriptionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscriptionResponse) ProtoMessage() {}

func (x *SubscriptionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_websocket_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscriptionResponse.ProtoReflect.Descriptor instead.
func (*SubscriptionResponse) Descriptor() ([]byte, []int) {
	return file_websocket_proto_rawDescGZIP(), []int{4}
}

func (x *SubscriptionResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *SubscriptionResponse) GetTopic() string {
	if x != nil {
		return x.Topic
	}
	return ""
}

func (x *SubscriptionResponse) GetSubscriptionId() string {
	if x != nil {
		return x.SubscriptionId
	}
	return ""
}

func (x *SubscriptionResponse) GetError() *ErrorInfo {
	if x != nil {
		return x.Error
	}
	return nil
}

var File_websocket_proto protoreflect.FileDescriptor

const file_websocket_proto_rawDesc = "" +
	"\n" +
	"\x0fwebsocket.proto\x12\fpromz.api.v1\"\xdb\x01\n" +
	"\x10WebSocketMessage\x12\x12\n" +
	"\x04type\x18\x01 \x01(\tR\x04type\x12\x16\n" +
	"\x06action\x18\x02 \x01(\tR\x06action\x12\x18\n" +
	"\apayload\x18\x03 \x01(\fR\apayload\x12\x14\n" +
	"\x05topic\x18\x04 \x01(\tR\x05topic\x12\x1d\n" +
	"\n" +
	"message_id\x18\x05 \x01(\tR\tmessageId\x12\x1d\n" +
	"\n" +
	"request_id\x18\x06 \x01(\tR\trequestId\x12-\n" +
	"\x05error\x18\a \x01(\v2\x17.promz.api.v1.ErrorInfoR\x05error\"\xb5\x01\n" +
	"\tErrorInfo\x12\x12\n" +
	"\x04code\x18\x01 \x01(\tR\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12>\n" +
	"\adetails\x18\x03 \x03(\v2$.promz.api.v1.ErrorInfo.DetailsEntryR\adetails\x1a:\n" +
	"\fDetailsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\x9b\x02\n" +
	"\x10FileUploadUpdate\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x16\n" +
	"\x06status\x18\x02 \x01(\tR\x06status\x12\x1a\n" +
	"\bprogress\x18\x03 \x01(\x01R\bprogress\x12\x14\n" +
	"\x05error\x18\x04 \x01(\tR\x05error\x12\x18\n" +
	"\amessage\x18\x05 \x01(\tR\amessage\x12)\n" +
	"\x10tokens_processed\x18\x06 \x01(\x05R\x0ftokensProcessed\x12!\n" +
	"\ftokens_limit\x18\a \x01(\x05R\vtokensLimit\x12'\n" +
	"\x0ftokens_exceeded\x18\b \x01(\bR\x0etokensExceeded\x12\x1c\n" +
	"\ttimestamp\x18\t \x01(\x03R\ttimestamp\"\xad\x01\n" +
	"\x13SubscriptionRequest\x12\x14\n" +
	"\x05topic\x18\x01 \x01(\tR\x05topic\x12E\n" +
	"\x06filter\x18\x02 \x03(\v2-.promz.api.v1.SubscriptionRequest.FilterEntryR\x06filter\x1a9\n" +
	"\vFilterEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\x9e\x01\n" +
	"\x14SubscriptionResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x14\n" +
	"\x05topic\x18\x02 \x01(\tR\x05topic\x12'\n" +
	"\x0fsubscription_id\x18\x03 \x01(\tR\x0esubscriptionId\x12-\n" +
	"\x05error\x18\x04 \x01(\v2\x17.promz.api.v1.ErrorInfoR\x05errorB\x18Z\x16promz.ai/api/proto/genb\x06proto3"

var (
	file_websocket_proto_rawDescOnce sync.Once
	file_websocket_proto_rawDescData []byte
)

func file_websocket_proto_rawDescGZIP() []byte {
	file_websocket_proto_rawDescOnce.Do(func() {
		file_websocket_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_websocket_proto_rawDesc), len(file_websocket_proto_rawDesc)))
	})
	return file_websocket_proto_rawDescData
}

var file_websocket_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_websocket_proto_goTypes = []any{
	(*WebSocketMessage)(nil),     // 0: promz.api.v1.WebSocketMessage
	(*ErrorInfo)(nil),            // 1: promz.api.v1.ErrorInfo
	(*FileUploadUpdate)(nil),     // 2: promz.api.v1.FileUploadUpdate
	(*SubscriptionRequest)(nil),  // 3: promz.api.v1.SubscriptionRequest
	(*SubscriptionResponse)(nil), // 4: promz.api.v1.SubscriptionResponse
	nil,                          // 5: promz.api.v1.ErrorInfo.DetailsEntry
	nil,                          // 6: promz.api.v1.SubscriptionRequest.FilterEntry
}
var file_websocket_proto_depIdxs = []int32{
	1, // 0: promz.api.v1.WebSocketMessage.error:type_name -> promz.api.v1.ErrorInfo
	5, // 1: promz.api.v1.ErrorInfo.details:type_name -> promz.api.v1.ErrorInfo.DetailsEntry
	6, // 2: promz.api.v1.SubscriptionRequest.filter:type_name -> promz.api.v1.SubscriptionRequest.FilterEntry
	1, // 3: promz.api.v1.SubscriptionResponse.error:type_name -> promz.api.v1.ErrorInfo
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_websocket_proto_init() }
func file_websocket_proto_init() {
	if File_websocket_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_websocket_proto_rawDesc), len(file_websocket_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_websocket_proto_goTypes,
		DependencyIndexes: file_websocket_proto_depIdxs,
		MessageInfos:      file_websocket_proto_msgTypes,
	}.Build()
	File_websocket_proto = out.File
	file_websocket_proto_goTypes = nil
	file_websocket_proto_depIdxs = nil
}
