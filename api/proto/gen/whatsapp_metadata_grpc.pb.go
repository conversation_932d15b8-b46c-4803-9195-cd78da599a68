// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.30.2
// source: whatsapp_metadata.proto

package gen

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	WhatsAppProcessingService_DetectWhatsAppContent_FullMethodName  = "/promz.api.v1.WhatsAppProcessingService/DetectWhatsAppContent"
	WhatsAppProcessingService_ProcessWhatsAppContent_FullMethodName = "/promz.api.v1.WhatsAppProcessingService/ProcessWhatsAppContent"
)

// WhatsAppProcessingServiceClient is the client API for WhatsAppProcessingService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// WhatsAppProcessingService handles WhatsApp chat detection and processing
type WhatsAppProcessingServiceClient interface {
	// Detect if content is a WhatsApp chat
	DetectWhatsAppContent(ctx context.Context, in *DetectWhatsAppRequest, opts ...grpc.CallOption) (*DetectWhatsAppResponse, error)
	// Process WhatsApp chat content
	ProcessWhatsAppContent(ctx context.Context, in *ProcessWhatsAppRequest, opts ...grpc.CallOption) (*ProcessWhatsAppResponse, error)
}

type whatsAppProcessingServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewWhatsAppProcessingServiceClient(cc grpc.ClientConnInterface) WhatsAppProcessingServiceClient {
	return &whatsAppProcessingServiceClient{cc}
}

func (c *whatsAppProcessingServiceClient) DetectWhatsAppContent(ctx context.Context, in *DetectWhatsAppRequest, opts ...grpc.CallOption) (*DetectWhatsAppResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DetectWhatsAppResponse)
	err := c.cc.Invoke(ctx, WhatsAppProcessingService_DetectWhatsAppContent_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *whatsAppProcessingServiceClient) ProcessWhatsAppContent(ctx context.Context, in *ProcessWhatsAppRequest, opts ...grpc.CallOption) (*ProcessWhatsAppResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ProcessWhatsAppResponse)
	err := c.cc.Invoke(ctx, WhatsAppProcessingService_ProcessWhatsAppContent_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WhatsAppProcessingServiceServer is the server API for WhatsAppProcessingService service.
// All implementations must embed UnimplementedWhatsAppProcessingServiceServer
// for forward compatibility.
//
// WhatsAppProcessingService handles WhatsApp chat detection and processing
type WhatsAppProcessingServiceServer interface {
	// Detect if content is a WhatsApp chat
	DetectWhatsAppContent(context.Context, *DetectWhatsAppRequest) (*DetectWhatsAppResponse, error)
	// Process WhatsApp chat content
	ProcessWhatsAppContent(context.Context, *ProcessWhatsAppRequest) (*ProcessWhatsAppResponse, error)
	mustEmbedUnimplementedWhatsAppProcessingServiceServer()
}

// UnimplementedWhatsAppProcessingServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedWhatsAppProcessingServiceServer struct{}

func (UnimplementedWhatsAppProcessingServiceServer) DetectWhatsAppContent(context.Context, *DetectWhatsAppRequest) (*DetectWhatsAppResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DetectWhatsAppContent not implemented")
}
func (UnimplementedWhatsAppProcessingServiceServer) ProcessWhatsAppContent(context.Context, *ProcessWhatsAppRequest) (*ProcessWhatsAppResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessWhatsAppContent not implemented")
}
func (UnimplementedWhatsAppProcessingServiceServer) mustEmbedUnimplementedWhatsAppProcessingServiceServer() {
}
func (UnimplementedWhatsAppProcessingServiceServer) testEmbeddedByValue() {}

// UnsafeWhatsAppProcessingServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to WhatsAppProcessingServiceServer will
// result in compilation errors.
type UnsafeWhatsAppProcessingServiceServer interface {
	mustEmbedUnimplementedWhatsAppProcessingServiceServer()
}

func RegisterWhatsAppProcessingServiceServer(s grpc.ServiceRegistrar, srv WhatsAppProcessingServiceServer) {
	// If the following call pancis, it indicates UnimplementedWhatsAppProcessingServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&WhatsAppProcessingService_ServiceDesc, srv)
}

func _WhatsAppProcessingService_DetectWhatsAppContent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DetectWhatsAppRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WhatsAppProcessingServiceServer).DetectWhatsAppContent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WhatsAppProcessingService_DetectWhatsAppContent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WhatsAppProcessingServiceServer).DetectWhatsAppContent(ctx, req.(*DetectWhatsAppRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _WhatsAppProcessingService_ProcessWhatsAppContent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessWhatsAppRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WhatsAppProcessingServiceServer).ProcessWhatsAppContent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WhatsAppProcessingService_ProcessWhatsAppContent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WhatsAppProcessingServiceServer).ProcessWhatsAppContent(ctx, req.(*ProcessWhatsAppRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// WhatsAppProcessingService_ServiceDesc is the grpc.ServiceDesc for WhatsAppProcessingService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var WhatsAppProcessingService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "promz.api.v1.WhatsAppProcessingService",
	HandlerType: (*WhatsAppProcessingServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "DetectWhatsAppContent",
			Handler:    _WhatsAppProcessingService_DetectWhatsAppContent_Handler,
		},
		{
			MethodName: "ProcessWhatsAppContent",
			Handler:    _WhatsAppProcessingService_ProcessWhatsAppContent_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "whatsapp_metadata.proto",
}
