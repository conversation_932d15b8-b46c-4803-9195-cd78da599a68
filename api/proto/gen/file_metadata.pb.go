// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: file_metadata.proto

package gen

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Error codes specific to file processing operations
type FileProcessingErrorCode int32

const (
	// Unknown or unspecified error
	FileProcessingErrorCode_FILE_PROCESSING_ERROR_UNSPECIFIED FileProcessingErrorCode = 0
	// File size exceeds the limit for the user's license tier
	FileProcessingErrorCode_FILE_SIZE_LIMIT_EXCEEDED FileProcessingErrorCode = 1
	// File format is not supported
	FileProcessingErrorCode_FILE_FORMAT_UNSUPPORTED FileProcessingErrorCode = 2
	// File is corrupted or cannot be read
	FileProcessingErrorCode_FILE_CORRUPTED FileProcessingErrorCode = 3
	// User has reached their quota limit
	FileProcessingErrorCode_QUOTA_EXCEEDED FileProcessingErrorCode = 4
	// Authentication or authorization error
	FileProcessingErrorCode_AUTHENTICATION_ERROR FileProcessingErrorCode = 5
)

// Enum value maps for FileProcessingErrorCode.
var (
	FileProcessingErrorCode_name = map[int32]string{
		0: "FILE_PROCESSING_ERROR_UNSPECIFIED",
		1: "FILE_SIZE_LIMIT_EXCEEDED",
		2: "FILE_FORMAT_UNSUPPORTED",
		3: "FILE_CORRUPTED",
		4: "QUOTA_EXCEEDED",
		5: "AUTHENTICATION_ERROR",
	}
	FileProcessingErrorCode_value = map[string]int32{
		"FILE_PROCESSING_ERROR_UNSPECIFIED": 0,
		"FILE_SIZE_LIMIT_EXCEEDED":          1,
		"FILE_FORMAT_UNSUPPORTED":           2,
		"FILE_CORRUPTED":                    3,
		"QUOTA_EXCEEDED":                    4,
		"AUTHENTICATION_ERROR":              5,
	}
)

func (x FileProcessingErrorCode) Enum() *FileProcessingErrorCode {
	p := new(FileProcessingErrorCode)
	*p = x
	return p
}

func (x FileProcessingErrorCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FileProcessingErrorCode) Descriptor() protoreflect.EnumDescriptor {
	return file_file_metadata_proto_enumTypes[0].Descriptor()
}

func (FileProcessingErrorCode) Type() protoreflect.EnumType {
	return &file_file_metadata_proto_enumTypes[0]
}

func (x FileProcessingErrorCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FileProcessingErrorCode.Descriptor instead.
func (FileProcessingErrorCode) EnumDescriptor() ([]byte, []int) {
	return file_file_metadata_proto_rawDescGZIP(), []int{0}
}

// Request to validate file size before upload
type FileSizeValidationRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Name of the file to validate
	FileName string `protobuf:"bytes,1,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	// Size of the file in bytes
	FileSizeBytes int64 `protobuf:"varint,2,opt,name=file_size_bytes,json=fileSizeBytes,proto3" json:"file_size_bytes,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FileSizeValidationRequest) Reset() {
	*x = FileSizeValidationRequest{}
	mi := &file_file_metadata_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileSizeValidationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileSizeValidationRequest) ProtoMessage() {}

func (x *FileSizeValidationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_file_metadata_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileSizeValidationRequest.ProtoReflect.Descriptor instead.
func (*FileSizeValidationRequest) Descriptor() ([]byte, []int) {
	return file_file_metadata_proto_rawDescGZIP(), []int{0}
}

func (x *FileSizeValidationRequest) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *FileSizeValidationRequest) GetFileSizeBytes() int64 {
	if x != nil {
		return x.FileSizeBytes
	}
	return 0
}

// Response for file size validation
type FileSizeValidationResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Whether the file size is valid for the user's license tier
	IsValid bool `protobuf:"varint,1,opt,name=is_valid,json=isValid,proto3" json:"is_valid,omitempty"`
	// Error code if validation fails
	ErrorCode FileProcessingErrorCode `protobuf:"varint,2,opt,name=error_code,json=errorCode,proto3,enum=promz.api.v1.FileProcessingErrorCode" json:"error_code,omitempty"`
	// Error message if validation fails
	ErrorMessage string `protobuf:"bytes,3,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	// Additional error details
	ErrorDetails map[string]string `protobuf:"bytes,4,rep,name=error_details,json=errorDetails,proto3" json:"error_details,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// Next tier that would support this file size (if applicable)
	NextTier string `protobuf:"bytes,5,opt,name=next_tier,json=nextTier,proto3" json:"next_tier,omitempty"`
	// Maximum file size allowed for the current tier in bytes
	MaxSizeBytes  int64 `protobuf:"varint,6,opt,name=max_size_bytes,json=maxSizeBytes,proto3" json:"max_size_bytes,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FileSizeValidationResponse) Reset() {
	*x = FileSizeValidationResponse{}
	mi := &file_file_metadata_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileSizeValidationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileSizeValidationResponse) ProtoMessage() {}

func (x *FileSizeValidationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_file_metadata_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileSizeValidationResponse.ProtoReflect.Descriptor instead.
func (*FileSizeValidationResponse) Descriptor() ([]byte, []int) {
	return file_file_metadata_proto_rawDescGZIP(), []int{1}
}

func (x *FileSizeValidationResponse) GetIsValid() bool {
	if x != nil {
		return x.IsValid
	}
	return false
}

func (x *FileSizeValidationResponse) GetErrorCode() FileProcessingErrorCode {
	if x != nil {
		return x.ErrorCode
	}
	return FileProcessingErrorCode_FILE_PROCESSING_ERROR_UNSPECIFIED
}

func (x *FileSizeValidationResponse) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *FileSizeValidationResponse) GetErrorDetails() map[string]string {
	if x != nil {
		return x.ErrorDetails
	}
	return nil
}

func (x *FileSizeValidationResponse) GetNextTier() string {
	if x != nil {
		return x.NextTier
	}
	return ""
}

func (x *FileSizeValidationResponse) GetMaxSizeBytes() int64 {
	if x != nil {
		return x.MaxSizeBytes
	}
	return 0
}

// FileUploadRequest is used for streaming file uploads
type FileUploadRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Request:
	//
	//	*FileUploadRequest_Metadata_
	//	*FileUploadRequest_Chunk_
	Request       isFileUploadRequest_Request `protobuf_oneof:"request"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FileUploadRequest) Reset() {
	*x = FileUploadRequest{}
	mi := &file_file_metadata_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileUploadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileUploadRequest) ProtoMessage() {}

func (x *FileUploadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_file_metadata_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileUploadRequest.ProtoReflect.Descriptor instead.
func (*FileUploadRequest) Descriptor() ([]byte, []int) {
	return file_file_metadata_proto_rawDescGZIP(), []int{2}
}

func (x *FileUploadRequest) GetRequest() isFileUploadRequest_Request {
	if x != nil {
		return x.Request
	}
	return nil
}

func (x *FileUploadRequest) GetMetadata() *FileUploadRequest_Metadata {
	if x != nil {
		if x, ok := x.Request.(*FileUploadRequest_Metadata_); ok {
			return x.Metadata
		}
	}
	return nil
}

func (x *FileUploadRequest) GetChunk() *FileUploadRequest_Chunk {
	if x != nil {
		if x, ok := x.Request.(*FileUploadRequest_Chunk_); ok {
			return x.Chunk
		}
	}
	return nil
}

type isFileUploadRequest_Request interface {
	isFileUploadRequest_Request()
}

type FileUploadRequest_Metadata_ struct {
	Metadata *FileUploadRequest_Metadata `protobuf:"bytes,1,opt,name=metadata,proto3,oneof"`
}

type FileUploadRequest_Chunk_ struct {
	Chunk *FileUploadRequest_Chunk `protobuf:"bytes,2,opt,name=chunk,proto3,oneof"`
}

func (*FileUploadRequest_Metadata_) isFileUploadRequest_Request() {}

func (*FileUploadRequest_Chunk_) isFileUploadRequest_Request() {}

// FileUploadResponse is returned after a successful upload
type FileUploadResponse struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	Id                   string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Status               string                 `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	EstimatedTimeSeconds int32                  `protobuf:"varint,3,opt,name=estimated_time_seconds,json=estimatedTimeSeconds,proto3" json:"estimated_time_seconds,omitempty"`
	MaxTokens            int32                  `protobuf:"varint,4,opt,name=max_tokens,json=maxTokens,proto3" json:"max_tokens,omitempty"`
	FileSize             int64                  `protobuf:"varint,5,opt,name=file_size,json=fileSize,proto3" json:"file_size,omitempty"`
	LicenseTier          string                 `protobuf:"bytes,6,opt,name=license_tier,json=licenseTier,proto3" json:"license_tier,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *FileUploadResponse) Reset() {
	*x = FileUploadResponse{}
	mi := &file_file_metadata_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileUploadResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileUploadResponse) ProtoMessage() {}

func (x *FileUploadResponse) ProtoReflect() protoreflect.Message {
	mi := &file_file_metadata_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileUploadResponse.ProtoReflect.Descriptor instead.
func (*FileUploadResponse) Descriptor() ([]byte, []int) {
	return file_file_metadata_proto_rawDescGZIP(), []int{3}
}

func (x *FileUploadResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *FileUploadResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *FileUploadResponse) GetEstimatedTimeSeconds() int32 {
	if x != nil {
		return x.EstimatedTimeSeconds
	}
	return 0
}

func (x *FileUploadResponse) GetMaxTokens() int32 {
	if x != nil {
		return x.MaxTokens
	}
	return 0
}

func (x *FileUploadResponse) GetFileSize() int64 {
	if x != nil {
		return x.FileSize
	}
	return 0
}

func (x *FileUploadResponse) GetLicenseTier() string {
	if x != nil {
		return x.LicenseTier
	}
	return ""
}

// StatusRequest is used to request the status of a processing job
type StatusRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StatusRequest) Reset() {
	*x = StatusRequest{}
	mi := &file_file_metadata_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatusRequest) ProtoMessage() {}

func (x *StatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_file_metadata_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatusRequest.ProtoReflect.Descriptor instead.
func (*StatusRequest) Descriptor() ([]byte, []int) {
	return file_file_metadata_proto_rawDescGZIP(), []int{4}
}

func (x *StatusRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// StatusResponse contains the current status of a processing job
type StatusResponse struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Id              string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Status          string                 `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	Progress        float64                `protobuf:"fixed64,3,opt,name=progress,proto3" json:"progress,omitempty"`
	Error           string                 `protobuf:"bytes,4,opt,name=error,proto3" json:"error,omitempty"`
	Message         string                 `protobuf:"bytes,5,opt,name=message,proto3" json:"message,omitempty"`
	TokensProcessed int32                  `protobuf:"varint,6,opt,name=tokens_processed,json=tokensProcessed,proto3" json:"tokens_processed,omitempty"`
	TokensLimit     int32                  `protobuf:"varint,7,opt,name=tokens_limit,json=tokensLimit,proto3" json:"tokens_limit,omitempty"`
	TokensExceeded  bool                   `protobuf:"varint,8,opt,name=tokens_exceeded,json=tokensExceeded,proto3" json:"tokens_exceeded,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *StatusResponse) Reset() {
	*x = StatusResponse{}
	mi := &file_file_metadata_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatusResponse) ProtoMessage() {}

func (x *StatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_file_metadata_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StatusResponse.ProtoReflect.Descriptor instead.
func (*StatusResponse) Descriptor() ([]byte, []int) {
	return file_file_metadata_proto_rawDescGZIP(), []int{5}
}

func (x *StatusResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *StatusResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *StatusResponse) GetProgress() float64 {
	if x != nil {
		return x.Progress
	}
	return 0
}

func (x *StatusResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

func (x *StatusResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *StatusResponse) GetTokensProcessed() int32 {
	if x != nil {
		return x.TokensProcessed
	}
	return 0
}

func (x *StatusResponse) GetTokensLimit() int32 {
	if x != nil {
		return x.TokensLimit
	}
	return 0
}

func (x *StatusResponse) GetTokensExceeded() bool {
	if x != nil {
		return x.TokensExceeded
	}
	return false
}

// ResultsRequest is used to request the results of a processing job
type ResultsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResultsRequest) Reset() {
	*x = ResultsRequest{}
	mi := &file_file_metadata_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResultsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResultsRequest) ProtoMessage() {}

func (x *ResultsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_file_metadata_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResultsRequest.ProtoReflect.Descriptor instead.
func (*ResultsRequest) Descriptor() ([]byte, []int) {
	return file_file_metadata_proto_rawDescGZIP(), []int{6}
}

func (x *ResultsRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// ResultsResponse is used to stream processing results
type ResultsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Response:
	//
	//	*ResultsResponse_Metadata_
	//	*ResultsResponse_Content
	Response      isResultsResponse_Response `protobuf_oneof:"response"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResultsResponse) Reset() {
	*x = ResultsResponse{}
	mi := &file_file_metadata_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResultsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResultsResponse) ProtoMessage() {}

func (x *ResultsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_file_metadata_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResultsResponse.ProtoReflect.Descriptor instead.
func (*ResultsResponse) Descriptor() ([]byte, []int) {
	return file_file_metadata_proto_rawDescGZIP(), []int{7}
}

func (x *ResultsResponse) GetResponse() isResultsResponse_Response {
	if x != nil {
		return x.Response
	}
	return nil
}

func (x *ResultsResponse) GetMetadata() *ResultsResponse_Metadata {
	if x != nil {
		if x, ok := x.Response.(*ResultsResponse_Metadata_); ok {
			return x.Metadata
		}
	}
	return nil
}

func (x *ResultsResponse) GetContent() *ResultsResponse_ContentChunk {
	if x != nil {
		if x, ok := x.Response.(*ResultsResponse_Content); ok {
			return x.Content
		}
	}
	return nil
}

type isResultsResponse_Response interface {
	isResultsResponse_Response()
}

type ResultsResponse_Metadata_ struct {
	Metadata *ResultsResponse_Metadata `protobuf:"bytes,1,opt,name=metadata,proto3,oneof"`
}

type ResultsResponse_Content struct {
	Content *ResultsResponse_ContentChunk `protobuf:"bytes,2,opt,name=content,proto3,oneof"`
}

func (*ResultsResponse_Metadata_) isResultsResponse_Response() {}

func (*ResultsResponse_Content) isResultsResponse_Response() {}

// CancelRequest is used to cancel a processing job
type CancelRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CancelRequest) Reset() {
	*x = CancelRequest{}
	mi := &file_file_metadata_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CancelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelRequest) ProtoMessage() {}

func (x *CancelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_file_metadata_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelRequest.ProtoReflect.Descriptor instead.
func (*CancelRequest) Descriptor() ([]byte, []int) {
	return file_file_metadata_proto_rawDescGZIP(), []int{8}
}

func (x *CancelRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// CancelResponse is returned after a cancellation request
type CancelResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CancelResponse) Reset() {
	*x = CancelResponse{}
	mi := &file_file_metadata_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CancelResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelResponse) ProtoMessage() {}

func (x *CancelResponse) ProtoReflect() protoreflect.Message {
	mi := &file_file_metadata_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelResponse.ProtoReflect.Descriptor instead.
func (*CancelResponse) Descriptor() ([]byte, []int) {
	return file_file_metadata_proto_rawDescGZIP(), []int{9}
}

func (x *CancelResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *CancelResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// First message contains metadata
type FileUploadRequest_Metadata struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	FileName       string                 `protobuf:"bytes,1,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	MimeType       string                 `protobuf:"bytes,2,opt,name=mime_type,json=mimeType,proto3" json:"mime_type,omitempty"`
	FileSize       int64                  `protobuf:"varint,3,opt,name=file_size,json=fileSize,proto3" json:"file_size,omitempty"`
	LicenseTier    string                 `protobuf:"bytes,4,opt,name=license_tier,json=licenseTier,proto3" json:"license_tier,omitempty"`
	CustomMetadata map[string]string      `protobuf:"bytes,5,rep,name=custom_metadata,json=customMetadata,proto3" json:"custom_metadata,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *FileUploadRequest_Metadata) Reset() {
	*x = FileUploadRequest_Metadata{}
	mi := &file_file_metadata_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileUploadRequest_Metadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileUploadRequest_Metadata) ProtoMessage() {}

func (x *FileUploadRequest_Metadata) ProtoReflect() protoreflect.Message {
	mi := &file_file_metadata_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileUploadRequest_Metadata.ProtoReflect.Descriptor instead.
func (*FileUploadRequest_Metadata) Descriptor() ([]byte, []int) {
	return file_file_metadata_proto_rawDescGZIP(), []int{2, 0}
}

func (x *FileUploadRequest_Metadata) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *FileUploadRequest_Metadata) GetMimeType() string {
	if x != nil {
		return x.MimeType
	}
	return ""
}

func (x *FileUploadRequest_Metadata) GetFileSize() int64 {
	if x != nil {
		return x.FileSize
	}
	return 0
}

func (x *FileUploadRequest_Metadata) GetLicenseTier() string {
	if x != nil {
		return x.LicenseTier
	}
	return ""
}

func (x *FileUploadRequest_Metadata) GetCustomMetadata() map[string]string {
	if x != nil {
		return x.CustomMetadata
	}
	return nil
}

// Subsequent messages contain file chunks
type FileUploadRequest_Chunk struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          []byte                 `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	ChunkIndex    int32                  `protobuf:"varint,2,opt,name=chunk_index,json=chunkIndex,proto3" json:"chunk_index,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FileUploadRequest_Chunk) Reset() {
	*x = FileUploadRequest_Chunk{}
	mi := &file_file_metadata_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileUploadRequest_Chunk) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileUploadRequest_Chunk) ProtoMessage() {}

func (x *FileUploadRequest_Chunk) ProtoReflect() protoreflect.Message {
	mi := &file_file_metadata_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileUploadRequest_Chunk.ProtoReflect.Descriptor instead.
func (*FileUploadRequest_Chunk) Descriptor() ([]byte, []int) {
	return file_file_metadata_proto_rawDescGZIP(), []int{2, 1}
}

func (x *FileUploadRequest_Chunk) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *FileUploadRequest_Chunk) GetChunkIndex() int32 {
	if x != nil {
		return x.ChunkIndex
	}
	return 0
}

// First message contains metadata
type ResultsResponse_Metadata struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Id              string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ContentType     string                 `protobuf:"bytes,2,opt,name=content_type,json=contentType,proto3" json:"content_type,omitempty"`
	Metadata        map[string]string      `protobuf:"bytes,3,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	HasFullContent  bool                   `protobuf:"varint,4,opt,name=has_full_content,json=hasFullContent,proto3" json:"has_full_content,omitempty"`
	ContentUrl      string                 `protobuf:"bytes,5,opt,name=content_url,json=contentUrl,proto3" json:"content_url,omitempty"`
	ExpiresAt       int64                  `protobuf:"varint,6,opt,name=expires_at,json=expiresAt,proto3" json:"expires_at,omitempty"`
	TokensProcessed int32                  `protobuf:"varint,7,opt,name=tokens_processed,json=tokensProcessed,proto3" json:"tokens_processed,omitempty"`
	TokensLimit     int32                  `protobuf:"varint,8,opt,name=tokens_limit,json=tokensLimit,proto3" json:"tokens_limit,omitempty"`
	TokensExceeded  bool                   `protobuf:"varint,9,opt,name=tokens_exceeded,json=tokensExceeded,proto3" json:"tokens_exceeded,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ResultsResponse_Metadata) Reset() {
	*x = ResultsResponse_Metadata{}
	mi := &file_file_metadata_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResultsResponse_Metadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResultsResponse_Metadata) ProtoMessage() {}

func (x *ResultsResponse_Metadata) ProtoReflect() protoreflect.Message {
	mi := &file_file_metadata_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResultsResponse_Metadata.ProtoReflect.Descriptor instead.
func (*ResultsResponse_Metadata) Descriptor() ([]byte, []int) {
	return file_file_metadata_proto_rawDescGZIP(), []int{7, 0}
}

func (x *ResultsResponse_Metadata) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ResultsResponse_Metadata) GetContentType() string {
	if x != nil {
		return x.ContentType
	}
	return ""
}

func (x *ResultsResponse_Metadata) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *ResultsResponse_Metadata) GetHasFullContent() bool {
	if x != nil {
		return x.HasFullContent
	}
	return false
}

func (x *ResultsResponse_Metadata) GetContentUrl() string {
	if x != nil {
		return x.ContentUrl
	}
	return ""
}

func (x *ResultsResponse_Metadata) GetExpiresAt() int64 {
	if x != nil {
		return x.ExpiresAt
	}
	return 0
}

func (x *ResultsResponse_Metadata) GetTokensProcessed() int32 {
	if x != nil {
		return x.TokensProcessed
	}
	return 0
}

func (x *ResultsResponse_Metadata) GetTokensLimit() int32 {
	if x != nil {
		return x.TokensLimit
	}
	return 0
}

func (x *ResultsResponse_Metadata) GetTokensExceeded() bool {
	if x != nil {
		return x.TokensExceeded
	}
	return false
}

// Subsequent messages contain content chunks
type ResultsResponse_ContentChunk struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          []byte                 `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	ChunkIndex    int32                  `protobuf:"varint,2,opt,name=chunk_index,json=chunkIndex,proto3" json:"chunk_index,omitempty"`
	IsLast        bool                   `protobuf:"varint,3,opt,name=is_last,json=isLast,proto3" json:"is_last,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResultsResponse_ContentChunk) Reset() {
	*x = ResultsResponse_ContentChunk{}
	mi := &file_file_metadata_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResultsResponse_ContentChunk) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResultsResponse_ContentChunk) ProtoMessage() {}

func (x *ResultsResponse_ContentChunk) ProtoReflect() protoreflect.Message {
	mi := &file_file_metadata_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResultsResponse_ContentChunk.ProtoReflect.Descriptor instead.
func (*ResultsResponse_ContentChunk) Descriptor() ([]byte, []int) {
	return file_file_metadata_proto_rawDescGZIP(), []int{7, 1}
}

func (x *ResultsResponse_ContentChunk) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *ResultsResponse_ContentChunk) GetChunkIndex() int32 {
	if x != nil {
		return x.ChunkIndex
	}
	return 0
}

func (x *ResultsResponse_ContentChunk) GetIsLast() bool {
	if x != nil {
		return x.IsLast
	}
	return false
}

var File_file_metadata_proto protoreflect.FileDescriptor

const file_file_metadata_proto_rawDesc = "" +
	"\n" +
	"\x13file_metadata.proto\x12\fpromz.api.v1\"`\n" +
	"\x19FileSizeValidationRequest\x12\x1b\n" +
	"\tfile_name\x18\x01 \x01(\tR\bfileName\x12&\n" +
	"\x0ffile_size_bytes\x18\x02 \x01(\x03R\rfileSizeBytes\"\x87\x03\n" +
	"\x1aFileSizeValidationResponse\x12\x19\n" +
	"\bis_valid\x18\x01 \x01(\bR\aisValid\x12D\n" +
	"\n" +
	"error_code\x18\x02 \x01(\x0e2%.promz.api.v1.FileProcessingErrorCodeR\terrorCode\x12#\n" +
	"\rerror_message\x18\x03 \x01(\tR\ferrorMessage\x12_\n" +
	"\rerror_details\x18\x04 \x03(\v2:.promz.api.v1.FileSizeValidationResponse.ErrorDetailsEntryR\ferrorDetails\x12\x1b\n" +
	"\tnext_tier\x18\x05 \x01(\tR\bnextTier\x12$\n" +
	"\x0emax_size_bytes\x18\x06 \x01(\x03R\fmaxSizeBytes\x1a?\n" +
	"\x11ErrorDetailsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\x94\x04\n" +
	"\x11FileUploadRequest\x12F\n" +
	"\bmetadata\x18\x01 \x01(\v2(.promz.api.v1.FileUploadRequest.MetadataH\x00R\bmetadata\x12=\n" +
	"\x05chunk\x18\x02 \x01(\v2%.promz.api.v1.FileUploadRequest.ChunkH\x00R\x05chunk\x1a\xae\x02\n" +
	"\bMetadata\x12\x1b\n" +
	"\tfile_name\x18\x01 \x01(\tR\bfileName\x12\x1b\n" +
	"\tmime_type\x18\x02 \x01(\tR\bmimeType\x12\x1b\n" +
	"\tfile_size\x18\x03 \x01(\x03R\bfileSize\x12!\n" +
	"\flicense_tier\x18\x04 \x01(\tR\vlicenseTier\x12e\n" +
	"\x0fcustom_metadata\x18\x05 \x03(\v2<.promz.api.v1.FileUploadRequest.Metadata.CustomMetadataEntryR\x0ecustomMetadata\x1aA\n" +
	"\x13CustomMetadataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\x1a<\n" +
	"\x05Chunk\x12\x12\n" +
	"\x04data\x18\x01 \x01(\fR\x04data\x12\x1f\n" +
	"\vchunk_index\x18\x02 \x01(\x05R\n" +
	"chunkIndexB\t\n" +
	"\arequest\"\xd1\x01\n" +
	"\x12FileUploadResponse\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x16\n" +
	"\x06status\x18\x02 \x01(\tR\x06status\x124\n" +
	"\x16estimated_time_seconds\x18\x03 \x01(\x05R\x14estimatedTimeSeconds\x12\x1d\n" +
	"\n" +
	"max_tokens\x18\x04 \x01(\x05R\tmaxTokens\x12\x1b\n" +
	"\tfile_size\x18\x05 \x01(\x03R\bfileSize\x12!\n" +
	"\flicense_tier\x18\x06 \x01(\tR\vlicenseTier\"\x1f\n" +
	"\rStatusRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\"\xfb\x01\n" +
	"\x0eStatusResponse\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x16\n" +
	"\x06status\x18\x02 \x01(\tR\x06status\x12\x1a\n" +
	"\bprogress\x18\x03 \x01(\x01R\bprogress\x12\x14\n" +
	"\x05error\x18\x04 \x01(\tR\x05error\x12\x18\n" +
	"\amessage\x18\x05 \x01(\tR\amessage\x12)\n" +
	"\x10tokens_processed\x18\x06 \x01(\x05R\x0ftokensProcessed\x12!\n" +
	"\ftokens_limit\x18\a \x01(\x05R\vtokensLimit\x12'\n" +
	"\x0ftokens_exceeded\x18\b \x01(\bR\x0etokensExceeded\" \n" +
	"\x0eResultsRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\"\xb9\x05\n" +
	"\x0fResultsResponse\x12D\n" +
	"\bmetadata\x18\x01 \x01(\v2&.promz.api.v1.ResultsResponse.MetadataH\x00R\bmetadata\x12F\n" +
	"\acontent\x18\x02 \x01(\v2*.promz.api.v1.ResultsResponse.ContentChunkH\x00R\acontent\x1a\xad\x03\n" +
	"\bMetadata\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12!\n" +
	"\fcontent_type\x18\x02 \x01(\tR\vcontentType\x12P\n" +
	"\bmetadata\x18\x03 \x03(\v24.promz.api.v1.ResultsResponse.Metadata.MetadataEntryR\bmetadata\x12(\n" +
	"\x10has_full_content\x18\x04 \x01(\bR\x0ehasFullContent\x12\x1f\n" +
	"\vcontent_url\x18\x05 \x01(\tR\n" +
	"contentUrl\x12\x1d\n" +
	"\n" +
	"expires_at\x18\x06 \x01(\x03R\texpiresAt\x12)\n" +
	"\x10tokens_processed\x18\a \x01(\x05R\x0ftokensProcessed\x12!\n" +
	"\ftokens_limit\x18\b \x01(\x05R\vtokensLimit\x12'\n" +
	"\x0ftokens_exceeded\x18\t \x01(\bR\x0etokensExceeded\x1a;\n" +
	"\rMetadataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\x1a\\\n" +
	"\fContentChunk\x12\x12\n" +
	"\x04data\x18\x01 \x01(\fR\x04data\x12\x1f\n" +
	"\vchunk_index\x18\x02 \x01(\x05R\n" +
	"chunkIndex\x12\x17\n" +
	"\ais_last\x18\x03 \x01(\bR\x06isLastB\n" +
	"\n" +
	"\bresponse\"\x1f\n" +
	"\rCancelRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\"D\n" +
	"\x0eCancelResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage*\xbd\x01\n" +
	"\x17FileProcessingErrorCode\x12%\n" +
	"!FILE_PROCESSING_ERROR_UNSPECIFIED\x10\x00\x12\x1c\n" +
	"\x18FILE_SIZE_LIMIT_EXCEEDED\x10\x01\x12\x1b\n" +
	"\x17FILE_FORMAT_UNSUPPORTED\x10\x02\x12\x12\n" +
	"\x0eFILE_CORRUPTED\x10\x03\x12\x12\n" +
	"\x0eQUOTA_EXCEEDED\x10\x04\x12\x18\n" +
	"\x14AUTHENTICATION_ERROR\x10\x052\xb5\x03\n" +
	"\x15FileProcessingService\x12e\n" +
	"\x10ValidateFileSize\x12'.promz.api.v1.FileSizeValidationRequest\x1a(.promz.api.v1.FileSizeValidationResponse\x12Q\n" +
	"\n" +
	"UploadFile\x12\x1f.promz.api.v1.FileUploadRequest\x1a .promz.api.v1.FileUploadResponse(\x01\x12F\n" +
	"\tGetStatus\x12\x1b.promz.api.v1.StatusRequest\x1a\x1c.promz.api.v1.StatusResponse\x12K\n" +
	"\n" +
	"GetResults\x12\x1c.promz.api.v1.ResultsRequest\x1a\x1d.promz.api.v1.ResultsResponse0\x01\x12M\n" +
	"\x10CancelProcessing\x12\x1b.promz.api.v1.CancelRequest\x1a\x1c.promz.api.v1.CancelResponseB\x18Z\x16promz.ai/api/proto/genb\x06proto3"

var (
	file_file_metadata_proto_rawDescOnce sync.Once
	file_file_metadata_proto_rawDescData []byte
)

func file_file_metadata_proto_rawDescGZIP() []byte {
	file_file_metadata_proto_rawDescOnce.Do(func() {
		file_file_metadata_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_file_metadata_proto_rawDesc), len(file_file_metadata_proto_rawDesc)))
	})
	return file_file_metadata_proto_rawDescData
}

var file_file_metadata_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_file_metadata_proto_msgTypes = make([]protoimpl.MessageInfo, 17)
var file_file_metadata_proto_goTypes = []any{
	(FileProcessingErrorCode)(0),         // 0: promz.api.v1.FileProcessingErrorCode
	(*FileSizeValidationRequest)(nil),    // 1: promz.api.v1.FileSizeValidationRequest
	(*FileSizeValidationResponse)(nil),   // 2: promz.api.v1.FileSizeValidationResponse
	(*FileUploadRequest)(nil),            // 3: promz.api.v1.FileUploadRequest
	(*FileUploadResponse)(nil),           // 4: promz.api.v1.FileUploadResponse
	(*StatusRequest)(nil),                // 5: promz.api.v1.StatusRequest
	(*StatusResponse)(nil),               // 6: promz.api.v1.StatusResponse
	(*ResultsRequest)(nil),               // 7: promz.api.v1.ResultsRequest
	(*ResultsResponse)(nil),              // 8: promz.api.v1.ResultsResponse
	(*CancelRequest)(nil),                // 9: promz.api.v1.CancelRequest
	(*CancelResponse)(nil),               // 10: promz.api.v1.CancelResponse
	nil,                                  // 11: promz.api.v1.FileSizeValidationResponse.ErrorDetailsEntry
	(*FileUploadRequest_Metadata)(nil),   // 12: promz.api.v1.FileUploadRequest.Metadata
	(*FileUploadRequest_Chunk)(nil),      // 13: promz.api.v1.FileUploadRequest.Chunk
	nil,                                  // 14: promz.api.v1.FileUploadRequest.Metadata.CustomMetadataEntry
	(*ResultsResponse_Metadata)(nil),     // 15: promz.api.v1.ResultsResponse.Metadata
	(*ResultsResponse_ContentChunk)(nil), // 16: promz.api.v1.ResultsResponse.ContentChunk
	nil,                                  // 17: promz.api.v1.ResultsResponse.Metadata.MetadataEntry
}
var file_file_metadata_proto_depIdxs = []int32{
	0,  // 0: promz.api.v1.FileSizeValidationResponse.error_code:type_name -> promz.api.v1.FileProcessingErrorCode
	11, // 1: promz.api.v1.FileSizeValidationResponse.error_details:type_name -> promz.api.v1.FileSizeValidationResponse.ErrorDetailsEntry
	12, // 2: promz.api.v1.FileUploadRequest.metadata:type_name -> promz.api.v1.FileUploadRequest.Metadata
	13, // 3: promz.api.v1.FileUploadRequest.chunk:type_name -> promz.api.v1.FileUploadRequest.Chunk
	15, // 4: promz.api.v1.ResultsResponse.metadata:type_name -> promz.api.v1.ResultsResponse.Metadata
	16, // 5: promz.api.v1.ResultsResponse.content:type_name -> promz.api.v1.ResultsResponse.ContentChunk
	14, // 6: promz.api.v1.FileUploadRequest.Metadata.custom_metadata:type_name -> promz.api.v1.FileUploadRequest.Metadata.CustomMetadataEntry
	17, // 7: promz.api.v1.ResultsResponse.Metadata.metadata:type_name -> promz.api.v1.ResultsResponse.Metadata.MetadataEntry
	1,  // 8: promz.api.v1.FileProcessingService.ValidateFileSize:input_type -> promz.api.v1.FileSizeValidationRequest
	3,  // 9: promz.api.v1.FileProcessingService.UploadFile:input_type -> promz.api.v1.FileUploadRequest
	5,  // 10: promz.api.v1.FileProcessingService.GetStatus:input_type -> promz.api.v1.StatusRequest
	7,  // 11: promz.api.v1.FileProcessingService.GetResults:input_type -> promz.api.v1.ResultsRequest
	9,  // 12: promz.api.v1.FileProcessingService.CancelProcessing:input_type -> promz.api.v1.CancelRequest
	2,  // 13: promz.api.v1.FileProcessingService.ValidateFileSize:output_type -> promz.api.v1.FileSizeValidationResponse
	4,  // 14: promz.api.v1.FileProcessingService.UploadFile:output_type -> promz.api.v1.FileUploadResponse
	6,  // 15: promz.api.v1.FileProcessingService.GetStatus:output_type -> promz.api.v1.StatusResponse
	8,  // 16: promz.api.v1.FileProcessingService.GetResults:output_type -> promz.api.v1.ResultsResponse
	10, // 17: promz.api.v1.FileProcessingService.CancelProcessing:output_type -> promz.api.v1.CancelResponse
	13, // [13:18] is the sub-list for method output_type
	8,  // [8:13] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_file_metadata_proto_init() }
func file_file_metadata_proto_init() {
	if File_file_metadata_proto != nil {
		return
	}
	file_file_metadata_proto_msgTypes[2].OneofWrappers = []any{
		(*FileUploadRequest_Metadata_)(nil),
		(*FileUploadRequest_Chunk_)(nil),
	}
	file_file_metadata_proto_msgTypes[7].OneofWrappers = []any{
		(*ResultsResponse_Metadata_)(nil),
		(*ResultsResponse_Content)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_file_metadata_proto_rawDesc), len(file_file_metadata_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   17,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_file_metadata_proto_goTypes,
		DependencyIndexes: file_file_metadata_proto_depIdxs,
		EnumInfos:         file_file_metadata_proto_enumTypes,
		MessageInfos:      file_file_metadata_proto_msgTypes,
	}.Build()
	File_file_metadata_proto = out.File
	file_file_metadata_proto_goTypes = nil
	file_file_metadata_proto_depIdxs = nil
}
