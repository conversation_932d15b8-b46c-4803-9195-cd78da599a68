// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.30.2
// source: file_metadata.proto

package gen

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	FileProcessingService_ValidateFileSize_FullMethodName = "/promz.api.v1.FileProcessingService/ValidateFileSize"
	FileProcessingService_UploadFile_FullMethodName       = "/promz.api.v1.FileProcessingService/UploadFile"
	FileProcessingService_GetStatus_FullMethodName        = "/promz.api.v1.FileProcessingService/GetStatus"
	FileProcessingService_GetResults_FullMethodName       = "/promz.api.v1.FileProcessingService/GetResults"
	FileProcessingService_CancelProcessing_FullMethodName = "/promz.api.v1.FileProcessingService/CancelProcessing"
)

// FileProcessingServiceClient is the client API for FileProcessingService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// FileProcessingService handles file uploads and processing
type FileProcessingServiceClient interface {
	// Validate file size before upload
	ValidateFileSize(ctx context.Context, in *FileSizeValidationRequest, opts ...grpc.CallOption) (*FileSizeValidationResponse, error)
	// Upload a file for processing with streaming support
	UploadFile(ctx context.Context, opts ...grpc.CallOption) (grpc.ClientStreamingClient[FileUploadRequest, FileUploadResponse], error)
	// Get processing status
	GetStatus(ctx context.Context, in *StatusRequest, opts ...grpc.CallOption) (*StatusResponse, error)
	// Get processing results with streaming support for large content
	GetResults(ctx context.Context, in *ResultsRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[ResultsResponse], error)
	// Cancel processing
	CancelProcessing(ctx context.Context, in *CancelRequest, opts ...grpc.CallOption) (*CancelResponse, error)
}

type fileProcessingServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewFileProcessingServiceClient(cc grpc.ClientConnInterface) FileProcessingServiceClient {
	return &fileProcessingServiceClient{cc}
}

func (c *fileProcessingServiceClient) ValidateFileSize(ctx context.Context, in *FileSizeValidationRequest, opts ...grpc.CallOption) (*FileSizeValidationResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FileSizeValidationResponse)
	err := c.cc.Invoke(ctx, FileProcessingService_ValidateFileSize_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fileProcessingServiceClient) UploadFile(ctx context.Context, opts ...grpc.CallOption) (grpc.ClientStreamingClient[FileUploadRequest, FileUploadResponse], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &FileProcessingService_ServiceDesc.Streams[0], FileProcessingService_UploadFile_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[FileUploadRequest, FileUploadResponse]{ClientStream: stream}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type FileProcessingService_UploadFileClient = grpc.ClientStreamingClient[FileUploadRequest, FileUploadResponse]

func (c *fileProcessingServiceClient) GetStatus(ctx context.Context, in *StatusRequest, opts ...grpc.CallOption) (*StatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(StatusResponse)
	err := c.cc.Invoke(ctx, FileProcessingService_GetStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *fileProcessingServiceClient) GetResults(ctx context.Context, in *ResultsRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[ResultsResponse], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &FileProcessingService_ServiceDesc.Streams[1], FileProcessingService_GetResults_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[ResultsRequest, ResultsResponse]{ClientStream: stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type FileProcessingService_GetResultsClient = grpc.ServerStreamingClient[ResultsResponse]

func (c *fileProcessingServiceClient) CancelProcessing(ctx context.Context, in *CancelRequest, opts ...grpc.CallOption) (*CancelResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CancelResponse)
	err := c.cc.Invoke(ctx, FileProcessingService_CancelProcessing_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// FileProcessingServiceServer is the server API for FileProcessingService service.
// All implementations must embed UnimplementedFileProcessingServiceServer
// for forward compatibility.
//
// FileProcessingService handles file uploads and processing
type FileProcessingServiceServer interface {
	// Validate file size before upload
	ValidateFileSize(context.Context, *FileSizeValidationRequest) (*FileSizeValidationResponse, error)
	// Upload a file for processing with streaming support
	UploadFile(grpc.ClientStreamingServer[FileUploadRequest, FileUploadResponse]) error
	// Get processing status
	GetStatus(context.Context, *StatusRequest) (*StatusResponse, error)
	// Get processing results with streaming support for large content
	GetResults(*ResultsRequest, grpc.ServerStreamingServer[ResultsResponse]) error
	// Cancel processing
	CancelProcessing(context.Context, *CancelRequest) (*CancelResponse, error)
	mustEmbedUnimplementedFileProcessingServiceServer()
}

// UnimplementedFileProcessingServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedFileProcessingServiceServer struct{}

func (UnimplementedFileProcessingServiceServer) ValidateFileSize(context.Context, *FileSizeValidationRequest) (*FileSizeValidationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidateFileSize not implemented")
}
func (UnimplementedFileProcessingServiceServer) UploadFile(grpc.ClientStreamingServer[FileUploadRequest, FileUploadResponse]) error {
	return status.Errorf(codes.Unimplemented, "method UploadFile not implemented")
}
func (UnimplementedFileProcessingServiceServer) GetStatus(context.Context, *StatusRequest) (*StatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStatus not implemented")
}
func (UnimplementedFileProcessingServiceServer) GetResults(*ResultsRequest, grpc.ServerStreamingServer[ResultsResponse]) error {
	return status.Errorf(codes.Unimplemented, "method GetResults not implemented")
}
func (UnimplementedFileProcessingServiceServer) CancelProcessing(context.Context, *CancelRequest) (*CancelResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelProcessing not implemented")
}
func (UnimplementedFileProcessingServiceServer) mustEmbedUnimplementedFileProcessingServiceServer() {}
func (UnimplementedFileProcessingServiceServer) testEmbeddedByValue()                               {}

// UnsafeFileProcessingServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to FileProcessingServiceServer will
// result in compilation errors.
type UnsafeFileProcessingServiceServer interface {
	mustEmbedUnimplementedFileProcessingServiceServer()
}

func RegisterFileProcessingServiceServer(s grpc.ServiceRegistrar, srv FileProcessingServiceServer) {
	// If the following call pancis, it indicates UnimplementedFileProcessingServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&FileProcessingService_ServiceDesc, srv)
}

func _FileProcessingService_ValidateFileSize_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FileSizeValidationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FileProcessingServiceServer).ValidateFileSize(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FileProcessingService_ValidateFileSize_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FileProcessingServiceServer).ValidateFileSize(ctx, req.(*FileSizeValidationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FileProcessingService_UploadFile_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(FileProcessingServiceServer).UploadFile(&grpc.GenericServerStream[FileUploadRequest, FileUploadResponse]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type FileProcessingService_UploadFileServer = grpc.ClientStreamingServer[FileUploadRequest, FileUploadResponse]

func _FileProcessingService_GetStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FileProcessingServiceServer).GetStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FileProcessingService_GetStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FileProcessingServiceServer).GetStatus(ctx, req.(*StatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _FileProcessingService_GetResults_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(ResultsRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(FileProcessingServiceServer).GetResults(m, &grpc.GenericServerStream[ResultsRequest, ResultsResponse]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type FileProcessingService_GetResultsServer = grpc.ServerStreamingServer[ResultsResponse]

func _FileProcessingService_CancelProcessing_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FileProcessingServiceServer).CancelProcessing(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: FileProcessingService_CancelProcessing_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FileProcessingServiceServer).CancelProcessing(ctx, req.(*CancelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// FileProcessingService_ServiceDesc is the grpc.ServiceDesc for FileProcessingService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var FileProcessingService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "promz.api.v1.FileProcessingService",
	HandlerType: (*FileProcessingServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ValidateFileSize",
			Handler:    _FileProcessingService_ValidateFileSize_Handler,
		},
		{
			MethodName: "GetStatus",
			Handler:    _FileProcessingService_GetStatus_Handler,
		},
		{
			MethodName: "CancelProcessing",
			Handler:    _FileProcessingService_CancelProcessing_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "UploadFile",
			Handler:       _FileProcessingService_UploadFile_Handler,
			ClientStreams: true,
		},
		{
			StreamName:    "GetResults",
			Handler:       _FileProcessingService_GetResults_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "file_metadata.proto",
}
