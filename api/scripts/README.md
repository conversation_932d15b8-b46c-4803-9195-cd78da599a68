# API Client Scripts

This directory contains platform-specific client scripts for interacting with the Promz API.

## Scripts

- `windows_client.ps1`: PowerShell script for Windows systems
- `unix_client.sh`: Bash script for Linux and macOS systems
- `supabase_checks.ps1`: PowerShell script for running Supabase checks, including API key validation.
- `verify_api_key.sql`: SQL script to create the `verify_api_key` stored procedure in Supabase.

Each script will automatically check if it's running on the correct platform and exit with instructions if not.

## Usage

### Windows

```powershell
# From PowerShell
./windows_client.ps1
```

### Linux/macOS

```bash
# From bash/zsh
./unix_client.sh
```

### Supabase Checks
The supabase_checks.ps1 script is used to validate the Supabase setup, including database connectivity and API key validation.

To run the Supabase checks:

```powershell
# From PowerShell
./supabase_checks.ps1
```

## Requirements

### Windows

- PowerShell 5.1 or later
- PostgreSQL client tools
- Go 1.21 or later
- curl (for API testing)

### Linux/macOS

- Bash 4.0 or later
- PostgreSQL client tools
- Go 1.21 or later
- curl and jq (for API testing and JSON parsing)
- netcat (nc) for port checking

## Configuration

Both scripts expect:

1. PostgreSQL running on localhost:5432
2. Environment variables:
  * PROMZ_OPENAI_API_KEY: Your OpenAI API key
  * PROMZ_GOOGLE_API_KEY: Your Google AI API key
  * PROMZ_DB_CONNECTION: Database connection string (defaults to "postgres://postgres:postgres@localhost:5432/promz?sslmode=disable")
  * PROMZ_SUPABASE_URL: Your Supabase URL
  * PROMZ_SUPABASE_KEY: Your Supabase Key
3. Go installed and in PATH

## Log Files

- Windows: `%APPDATA%\promz\api.log`
- Linux/macOS: `~/.local/share/promz/api.log`
