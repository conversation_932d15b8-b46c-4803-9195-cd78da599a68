-- Script to secure the licenses table and API keys

-- First, let's check if <PERSON><PERSON> is enabled
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_tables 
    WHERE schemaname = 'public' 
    AND tablename = 'licenses' 
    AND rowsecurity = true
  ) THEN
    RAISE NOTICE 'Enabling Row Level Security on licenses table';
    ALTER TABLE public.licenses ENABLE ROW LEVEL SECURITY;
  ELSE
    RAISE NOTICE 'Row Level Security is already enabled on licenses table';
  END IF;
END $$;

-- Drop existing policies to clean up duplicates
DO $$
DECLARE
  r RECORD;
BEGIN
  -- Get all policies for the licenses table
  FOR r IN (
    SELECT policyname 
    FROM pg_policies 
    WHERE tablename = 'licenses' AND schemaname = 'public'
  ) LOOP
    EXECUTE format('DROP POLICY IF EXISTS %I ON public.licenses', r.policyname);
    RAISE NOTICE 'Dropped policy: %', r.policyname;
  END LOOP;
END $$;

-- Create clean, well-defined policies

-- 1. Allow users to view only their own licenses (except api_key column)
CREATE POLICY "users_select_own_licenses" 
ON public.licenses FOR SELECT 
USING (auth.uid() = user_id);

-- 2. Allow service role to do anything
CREATE POLICY "service_role_all_licenses" 
ON public.licenses FOR ALL 
USING (auth.role() = 'service_role');

-- 3. Allow authenticated users to insert their own licenses
CREATE POLICY "users_insert_own_licenses" 
ON public.licenses FOR INSERT 
WITH CHECK (auth.uid() = user_id);

-- 4. Allow authenticated users to update their own licenses
CREATE POLICY "users_update_own_licenses" 
ON public.licenses FOR UPDATE 
USING (auth.uid() = user_id);

-- Protect the api_key column specifically
DO $$
BEGIN
  -- Revoke SELECT on api_key column for public roles
  EXECUTE 'REVOKE SELECT (api_key) ON public.licenses FROM anon, authenticated';
  RAISE NOTICE 'Revoked direct SELECT access to api_key column';
  
  -- Grant execute on the verify_api_key function
  EXECUTE 'GRANT EXECUTE ON FUNCTION public.verify_api_key(TEXT) TO anon, authenticated';
  RAISE NOTICE 'Granted execute permission on verify_api_key function';
EXCEPTION WHEN OTHERS THEN
  RAISE NOTICE 'Error setting column permissions: %', SQLERRM;
END $$;

-- Create a view that excludes the api_key column for safer access
CREATE OR REPLACE VIEW public.licenses_safe AS
SELECT 
  id, 
  user_id, 
  license_type, 
  is_active, 
  expiry_date, 
  created_at, 
  updated_at
FROM public.licenses;

-- Set permissions on the safe view
GRANT SELECT ON public.licenses_safe TO anon, authenticated;

-- Verify the current policies
SELECT 
  tablename, 
  policyname, 
  permissive, 
  roles, 
  cmd, 
  qual 
FROM pg_policies 
WHERE tablename = 'licenses' AND schemaname = 'public'
ORDER BY policyname;
