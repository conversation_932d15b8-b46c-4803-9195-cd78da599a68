#!/bin/bash

# Git Helper Script with Submodule Support - Force Reset to Main
set -e  # Exit on error

# Function to check git configuration
check_git_config() {
    local user_name=$(git config --get user.name)
    local user_email=$(git config --get user.email)
    
    if [ -z "$user_name" ] || [ -z "$user_email" ]; then
        echo -e "\033[0;31m❌ Git user identity not configured!\033[0m"
        echo "Please set your Git identity with:"
        echo "  git config --global user.name \"Your Name\""
        echo "  git config --global user.email \"<EMAIL>\""
        return 1
    fi
    return 0
}

# Ensure Git uses the same credentials as your interactive shell
export GIT_SSH_COMMAND="ssh -o BatchMode=no"

# Function to check for uncommitted changes in the repo and submodules
check_uncommitted_changes() {
    local has_changes=0
    
    # Check root repository
    if [ -n "$(git status --porcelain)" ]; then
        echo -e "\033[0;33m⚠️  Uncommitted changes detected in root repository\033[0m"
        has_changes=1
    fi
    
    # Check all submodules
    git submodule foreach '
        if [ -n "$(git status --porcelain)" ]; then
            echo -e "\033[0;33m⚠️  Uncommitted changes detected in submodule $name\033[0m"
            exit 1
        else
            exit 0
        fi
    ' >/dev/null 2>&1 || has_changes=1
    
    return $has_changes
}

# Function to verify git repository status
verify_git_repo() {
    if [ ! -d ".git" ] && [ ! -f ".git" ]; then
        echo -e "\033[0;31m❌ Not inside a Git repository. Please run this script from a Git repo.\033[0m"
        exit 1
    fi
}

# Function to check for the existence of the main branch
check_main_branch() {
    if ! git show-ref --verify --quiet refs/heads/main; then
        echo -e "\033[0;31m❌ 'main' branch does not exist. Aborting.\033[0m"
        return 1
    fi
    return 0
}

# Function to check for the existence of the remote
check_remote() {
    if ! git remote get-url origin > /dev/null 2>&1; then
        echo -e "\033[0;31m❌ Remote 'origin' does not exist. Aborting.\033[0m"
        return 1
    fi
    return 0
}

# Clean up function
cleanup() {
    echo -e "\n\033[0;33m⚠️  Operation interrupted. Cleaning up...\033[0m"
    # Additional cleanup can be added here if needed
    exit 1
}

# Set up trap for clean exits
trap cleanup INT TERM

clear
echo "==============================="
echo "  ⚠️  Git Force Reset Script  ⚠️ "
echo "==============================="
echo "[1] Go from 'main' to 'dev'"
echo "[2] Go from 'dev' to 'main'"
echo "[3] Exit"
echo "==============================="

read -p "Choose an option (1-3): " choice

# Verify we're in a git repo first thing
verify_git_repo

# Check Git configuration
check_git_config || {
    echo -e "\033[0;33m⚠️ Please configure Git user identity before proceeding.\033[0m"
    exit 1
}

if [ "$choice" = "1" ]; then
    # Early check for uncommitted changes
    echo "🔍 Checking for uncommitted changes..."
    if check_uncommitted_changes; then
        echo -e "\033[0;32m✅ Clean working directory confirmed\033[0m"
    else
        exit 1
    fi

    # Check for remote and main branch before restoring
    check_remote || exit 1
    check_main_branch || exit 1

    echo "🔀 Checking out 'dev' branch..."
    git checkout dev

    echo "⚠️  Force resetting 'dev' branch to 'main'..."
    git reset --hard main

    # Also checkout 'dev' and reset to main on submodules
    echo "⚙️ Resetting submodules to 'main'..."
    git submodule foreach '
        echo "  ⚙️ Resetting submodule: $name"
	git checkout dev || echo " Failed to checkout dev for $name"
        git reset --hard main || echo "  ⚠️ Failed to reset submodule $name"
    '

    echo -e "\033[0;32m✅ 'dev' branch force reset to 'main' completed!\033[0m"

elif [ "$choice" = "2" ]; then
    # Early check for uncommitted changes
    echo "🔍 Checking for uncommitted changes..."
    if check_uncommitted_changes; then
        echo -e "\033[0;32m✅ Clean working directory confirmed\033[0m"
    else
        echo -e "\033[0;31m❌ Uncommitted changes detected!\033[0m"
        echo -e "\033[0;31m❌ This operation requires a clean working directory to prevent data loss.\033[0m"
        echo -e "\033[0;33m⚠️  Please commit or stash your changes first.\033[0m"
        exit 1
    fi

    # Ensure dev branch exists before proceeding
    if ! git show-ref --verify --quiet refs/heads/dev; then
        echo -e "\033[0;31m❌ 'dev' branch does not exist in root repository. No changes to restore.\033[0m"
        exit 1
    fi

    # Check for remote and main branch before restoring
    check_remote || exit 1
    check_main_branch || exit 1

    # Triple-check with the user due to the destructive nature of this operation
    echo -e "\033[0;33m⚠️  WARNING: This will overwrite your main branch with content from dev branch!\033[0m"
    read -p "⚠️ Type 'yes' to confirm you want to continue: " confirm
    if [ "$confirm" != "yes" ]; then
        echo -e "\033[0;33m🛑 Restore operation aborted.\033[0m"
        exit 0
    fi

    # Create a backup point in case recovery is needed
    echo "📦 Creating backup point..."
    backup_branch="backup-main-$(date +%Y%m%d-%H%M%S)"
    if ! git branch "$backup_branch"; then
        echo -e "\033[0;31m❌ Failed to create backup branch. Aborting for safety.\033[0m"
        exit 1
    fi
    echo -e "\033[0;32m✅ Backup created as branch: $backup_branch\033[0m"

    # Switch to main branch in root repository
    echo "🔀 Switching to 'main' branch in root repository..."
    if ! git checkout main; then
        echo -e "\033[0;31m❌ Failed to switch to main branch. Aborting.\033[0m"
        exit 1
    fi

    # Check submodules for main branch and switch if available
    echo "🔀 Handling submodules..."
    git submodule foreach '
        if git show-ref --verify --quiet refs/heads/main; then
            echo "  🔀 Switching to main branch in submodule $name"
            if ! git checkout main; then
                echo "  ⚠️ Failed to switch to main branch in submodule $name"
                exit 1
            fi
        else
            echo "  ℹ️ No main branch in submodule $name, staying on current branch"
        fi
        # Create backup point in submodule
        backup_branch="backup-'"$backup_branch"'"
        git branch "$backup_branch" || echo "  ⚠️ Could not create backup in submodule"
    ' || {
        echo -e "\033[0;31m❌ Error applying changes to some submodules.\033[0m"
    }

    # Bring changes from dev but DO NOT commit them in root repo
    echo "📂 Moving changes from 'dev' to 'main' in root repository (as unstaged changes)..."
    if ! git checkout dev -- .; then
        echo -e "\033[0;31m❌ Failed to apply dev changes to main. You can try manually with:\033[0m"
        echo "    git checkout dev -- ."
        exit 1
    fi

    # Bring dev changes into each submodule that has a dev branch
    echo "📂 Processing submodules..."
    git submodule foreach '
        if git show-ref --verify --quiet refs/heads/dev; then
            echo "  📂 Moving changes from dev to current branch in submodule $name"
            if ! git checkout dev -- .; then
                echo "  ⚠️ Failed to apply dev changes in submodule $name"
                exit 1
            fi
        else
            echo "  ℹ️ No dev branch in submodule $name, skipping"
        fi
    ' || {
        echo -e "\033[0;31m❌ Error applying changes to some submodules.\033[0m"
    }

    echo -e "\033[0;32m✅ Operation completed!\033[0m"
    echo "📝 All changes are now in 'main' but unstaged across repository and submodules."
    echo "💡 To review changes:"
    echo "    git status               # See what changed in root"
    echo "    git submodule foreach git status  # See what changed in submodules"
    echo ""
    echo "💡 To commit changes:"
    echo "    git add <files>          # Stage files selectively"
    echo "    git commit -m 'Your message'      # Commit changes"
    echo ""
    echo "💡 If you need to revert this operation:"
    echo "    git checkout $backup_branch -- .  # Restore from backup"

elif [ "$choice" = "3" ]; then
    echo "👋 Exiting script. Have a great day!"
    exit 0

else
    echo -e "\033[0;31m❌ Invalid option. Please select 1, 2, or 3.\033[0m"
    exit 1
fi
