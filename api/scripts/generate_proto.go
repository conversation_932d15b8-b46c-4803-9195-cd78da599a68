// +build ignore

package main

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
)

func main() {
	// Define the proto files to generate
	protoFiles := []string{
		"proto/v1/common.proto",
		"proto/v1/file_processing.proto",
		"proto/v1/websocket.proto",
	}

	// Get the absolute path to the project root
	projectRoot, err := filepath.Abs(".")
	if err != nil {
		fmt.Printf("Error getting project root: %v\n", err)
		os.Exit(1)
	}

	// Generate Go code for each proto file
	for _, protoFile := range protoFiles {
		protoPath := filepath.Join(projectRoot, protoFile)
		outputDir := filepath.Join(projectRoot, "internal")

		// Generate the Go code
		cmd := exec.Command("protoc",
			"--go_out="+outputDir,
			"--go_opt=paths=source_relative",
			"--go-grpc_out="+outputDir,
			"--go-grpc_opt=paths=source_relative",
			protoPath,
		)

		cmd.Stdout = os.Stdout
		cmd.Stderr = os.Stderr

		fmt.Printf("Generating code for %s...\n", protoFile)
		if err := cmd.Run(); err != nil {
			fmt.Printf("Error generating code for %s: %v\n", protoFile, err)
			os.Exit(1)
		}
	}

	fmt.Println("Code generation completed successfully!")
}
