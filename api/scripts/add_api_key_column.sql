-- Add API key column to licenses table and update functions

-- First, add the api_key column to the licenses table if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_schema = 'public' 
    AND table_name = 'licenses' 
    AND column_name = 'api_key'
  ) THEN
    ALTER TABLE public.licenses ADD COLUMN api_key UUID DEFAULT gen_random_uuid();
    RAISE NOTICE 'Added api_key column to licenses table';
  ELSE
    RAISE NOTICE 'api_key column already exists in licenses table';
  END IF;
END $$;

-- Update existing licenses to have API keys if they don't already
UPDATE public.licenses 
SET api_key = gen_random_uuid() 
WHERE api_key IS NULL;

-- Update the get_or_create_license function to use the api_key column
CREATE OR REPLACE FUNCTION get_or_create_license(
  p_user_id UUID,
  p_license_type TEXT,
  p_days_valid INTEGER
) RETURNS JSONB AS $$
DECLARE
  v_license_id UUID;
  v_api_key UUID;
  v_is_new BOOLEAN := FALSE;
  v_license JSONB;
  v_expiry_date TIMESTAMP WITH TIME ZONE;
  v_license_record RECORD;
BEGIN
  -- Try to find existing license
  SELECT * INTO v_license_record
  FROM public.licenses
  WHERE user_id = p_user_id
  AND license_type = p_license_type
  AND is_active = TRUE;
  
  -- Create new license if none exists
  IF v_license_record IS NULL THEN
    v_is_new := TRUE;
    v_api_key := gen_random_uuid();
    v_expiry_date := CURRENT_DATE + (p_days_valid || ' days')::INTERVAL;
    
    INSERT INTO public.licenses (
      user_id,
      license_type,
      is_active,
      expiry_date,
      api_key
    ) VALUES (
      p_user_id,
      p_license_type,
      TRUE,
      v_expiry_date,
      v_api_key
    )
    RETURNING id, expiry_date, api_key INTO v_license_id, v_expiry_date, v_api_key;
  ELSE
    -- Use existing license
    v_license_id := v_license_record.id;
    v_expiry_date := v_license_record.expiry_date;
    v_api_key := v_license_record.api_key;
    
    -- If no API key exists, generate one
    IF v_api_key IS NULL THEN
      v_api_key := gen_random_uuid();
      UPDATE public.licenses
      SET api_key = v_api_key
      WHERE id = v_license_id;
    END IF;
  END IF;
  
  -- Return the license information
  v_license := jsonb_build_object(
    'license_id', v_license_id,
    'license_type', p_license_type,
    'is_active', TRUE,
    'expiry_date', v_expiry_date,
    'api_key', v_api_key,
    'is_new', v_is_new
  );
  
  RETURN v_license;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the verify_api_key function to check the licenses table
CREATE OR REPLACE FUNCTION verify_api_key(p_api_key TEXT)
RETURNS JSON AS $$
DECLARE
  license_record RECORD;
  result JSON;
BEGIN
  -- Special case for CLI API key
  IF is_cli_api_key(p_api_key) THEN
    result := json_build_object(
      'valid', true,
      'user_id', 'CLI',
      'license_type', 'API',
      'expiry_date', (now() + interval '100 years')::timestamp
    );
    RETURN result;
  END IF;

  -- Try to find a license with this API key
  SELECT * INTO license_record
  FROM public.licenses
  WHERE api_key = p_api_key::UUID
  AND is_active = TRUE;
  
  -- If no license found, return invalid
  IF license_record IS NULL THEN
    result := json_build_object(
      'valid', false,
      'reason', 'invalid_key'
    );
    RETURN result;
  END IF;
  
  -- Check if license is expired
  IF license_record.expiry_date < now() THEN
    -- Update license to inactive
    UPDATE public.licenses
    SET is_active = false
    WHERE id = license_record.id;
    
    result := json_build_object(
      'valid', false,
      'reason', 'expired',
      'license_type', license_record.license_type,
      'expiry_date', license_record.expiry_date
    );
    RETURN result;
  END IF;
  
  -- License is valid
  result := json_build_object(
    'valid', true,
    'user_id', license_record.user_id,
    'license_type', license_record.license_type,
    'expiry_date', license_record.expiry_date
  );
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Special case for CLI API key
CREATE OR REPLACE FUNCTION is_cli_api_key(p_api_key TEXT)
RETURNS BOOLEAN AS $$
BEGIN
  -- Check if this is the CLI API key
  RETURN p_api_key = '30212873-5ab8-4f1a-83c1-1f13121b6549';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
