-- Fix the verify_api_key function to handle non-UUID user IDs
CREATE OR REPLACE FUNCTION verify_api_key(api_key TEXT)
RETURNS JSON AS $$
DECLARE
  secret_name TEXT;
  v_user_id TEXT;
  v_license_type TEXT;
  license_record RECORD;
  result JSON;
BEGIN
  -- Check if the API key matches any secret in the vault
  SELECT name INTO secret_name
  FROM vault.decrypted_secrets
  WHERE decrypted_secret = api_key
  LIMIT 1;
  
  -- If no matching secret is found, return invalid
  IF secret_name IS NULL THEN
    result := json_build_object(
      'valid', false,
      'reason', 'invalid_key'
    );
    RETURN result;
  END IF;
  
  -- Log the secret name for debugging
  RAISE NOTICE 'Secret name: %', secret_name;
  
  -- Extract user_id and license_type from the secret name
  -- Format is: user_id_license_type_API_KEY or CLI_license_type_API_KEY
  v_user_id := split_part(secret_name, '_', 1);
  v_license_type := split_part(secret_name, '_', 2);
  
  -- Special handling for CLI keys
  IF v_user_id = 'CLI' THEN
    -- CLI keys are valid by default
    result := json_build_object(
      'valid', true,
      'user_id', 'CLI',
      'license_type', v_license_type,
      'expiry_date', (now() + interval '100 years')::timestamp
    );
    RETURN result;
  END IF;
  
  -- Check if v_user_id is a valid UUID
  BEGIN
    -- Try to cast to UUID
    PERFORM v_user_id::UUID;
  EXCEPTION WHEN others THEN
    -- Not a valid UUID
    result := json_build_object(
      'valid', false,
      'reason', 'invalid_user_id_format',
      'details', 'The user ID extracted from the secret name is not a valid UUID'
    );
    RETURN result;
  END;
  
  -- Get the license record for this user
  SELECT * INTO license_record
  FROM public.licenses
  WHERE user_id = v_user_id::UUID
  AND license_type = v_license_type
  AND is_active = true
  LIMIT 1;
  
  -- If no active license found, return invalid
  IF license_record IS NULL THEN
    result := json_build_object(
      'valid', false,
      'reason', 'no_active_license'
    );
    RETURN result;
  END IF;
  
  -- Check if license is expired
  IF license_record.expiry_date < now() THEN
    -- Update license to inactive
    UPDATE public.licenses
    SET is_active = false
    WHERE id = license_record.id;
    
    result := json_build_object(
      'valid', false,
      'reason', 'expired',
      'license_type', license_record.license_type,
      'expiry_date', license_record.expiry_date
    );
    RETURN result;
  END IF;
  
  -- License is valid
  result := json_build_object(
    'valid', true,
    'user_id', v_user_id,
    'license_type', license_record.license_type,
    'expiry_date', license_record.expiry_date
  );
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
