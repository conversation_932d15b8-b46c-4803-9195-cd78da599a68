-- Function to properly store API keys in the vault with better logging
CREATE OR REPLACE FUNCTION store_api_key_in_vault(
  p_user_id UUID,
  p_license_type TEXT,
  p_api_key TEXT
) RETURNS BOOLEAN AS $$
DECLARE
  secret_name TEXT;
BEGIN
  -- Create a secret name in the format user_id_license_type_API_KEY
  secret_name := p_user_id || '_' || p_license_type || '_API_KEY';
  
  -- Log the operation for debugging
  RAISE NOTICE 'Storing API key in vault with name: %', secret_name;
  
  -- Store the API key in the vault
  PERFORM vault.create_secret(
    name => secret_name,
    secret => p_api_key
  );
  
  -- Verify the secret was stored
  IF EXISTS (
    SELECT 1 FROM vault.decrypted_secrets 
    WHERE name = secret_name AND decrypted_secret = p_api_key
  ) THEN
    RAISE NOTICE 'API key successfully stored in vault';
    RETURN TRUE;
  ELSE
    RAISE NOTICE 'Failed to verify API key was stored in vault';
    RETURN FALSE;
  END IF;
EXCEPTION WHEN OTHERS THEN
  RAISE NOTICE 'Failed to store API key in vault: %', SQLERRM;
  RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the get_or_create_license function to generate and store API keys in the vault
CREATE OR REPLACE FUNCTION get_or_create_license(
  p_user_id UUID,
  p_license_type TEXT,
  p_days_valid INTEGER
) RETURNS JSONB AS $$
DECLARE
  v_license_id UUID;
  v_api_key UUID;
  v_is_new BOOLEAN := FALSE;
  v_license JSONB;
  v_expiry_date TIMESTAMP WITH TIME ZONE;
  v_license_record RECORD;
BEGIN
  -- Try to find existing license
  SELECT * INTO v_license_record
  FROM public.licenses
  WHERE user_id = p_user_id
  AND license_type = p_license_type
  AND is_active = TRUE;
  
  -- Create new license if none exists
  IF v_license_record IS NULL THEN
    v_is_new := TRUE;
    v_api_key := gen_random_uuid();
    v_expiry_date := CURRENT_DATE + (p_days_valid || ' days')::INTERVAL;
    
    INSERT INTO public.licenses (
      user_id,
      license_type,
      is_active,
      expiry_date
    ) VALUES (
      p_user_id,
      p_license_type,
      TRUE,
      v_expiry_date
    )
    RETURNING id, expiry_date INTO v_license_id, v_expiry_date;
    
    -- Store the API key in the vault
    PERFORM store_api_key_in_vault(p_user_id, p_license_type, v_api_key::TEXT);
  ELSE
    -- Use existing license
    v_license_id := v_license_record.id;
    v_expiry_date := v_license_record.expiry_date;
    
    -- Generate a new API key for existing license
    v_api_key := gen_random_uuid();
    
    -- Store the API key in the vault
    PERFORM store_api_key_in_vault(p_user_id, p_license_type, v_api_key::TEXT);
  END IF;
  
  -- Return the license information
  v_license := jsonb_build_object(
    'license_id', v_license_id,
    'license_type', p_license_type,
    'is_active', TRUE,
    'expiry_date', v_expiry_date,
    'api_key', v_api_key,
    'is_new', v_is_new
  );
  
  RETURN v_license;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the verify_api_key function to add more logging
CREATE OR REPLACE FUNCTION verify_api_key(api_key TEXT)
RETURNS JSON AS $$
DECLARE
  secret_name TEXT;
  v_user_id TEXT;
  v_license_type TEXT;
  license_record RECORD;
  result JSON;
BEGIN
  -- Check if the API key matches any secret in the vault
  SELECT name INTO secret_name
  FROM vault.decrypted_secrets
  WHERE decrypted_secret = api_key
  LIMIT 1;
  
  -- Log for debugging
  RAISE NOTICE 'Verifying API key, found secret name: %', secret_name;
  
  -- If no matching secret is found, return invalid
  IF secret_name IS NULL THEN
    RAISE NOTICE 'No matching secret found for API key';
    result := json_build_object(
      'valid', false,
      'reason', 'invalid_key'
    );
    RETURN result;
  END IF;
  
  -- Extract user_id and license_type from the secret name
  -- Format is: user_id_license_type_API_KEY or CLI_license_type_API_KEY
  v_user_id := split_part(secret_name, '_', 1);
  v_license_type := split_part(secret_name, '_', 2);
  
  RAISE NOTICE 'Extracted user_id: %, license_type: %', v_user_id, v_license_type;
  
  -- Special handling for CLI keys
  IF v_user_id = 'CLI' THEN
    -- CLI keys are valid by default
    result := json_build_object(
      'valid', true,
      'user_id', 'CLI',
      'license_type', v_license_type,
      'expiry_date', (now() + interval '100 years')::timestamp
    );
    RETURN result;
  END IF;
  
  -- Check if v_user_id is a valid UUID
  BEGIN
    -- Try to cast to UUID
    PERFORM v_user_id::UUID;
  EXCEPTION WHEN others THEN
    -- Not a valid UUID
    RAISE NOTICE 'Invalid UUID format for user_id: %', v_user_id;
    result := json_build_object(
      'valid', false,
      'reason', 'invalid_user_id_format',
      'details', 'The user ID extracted from the secret name is not a valid UUID'
    );
    RETURN result;
  END;
  
  -- Get the license record for this user
  SELECT * INTO license_record
  FROM public.licenses
  WHERE user_id = v_user_id::UUID
  AND license_type = v_license_type
  AND is_active = true
  LIMIT 1;
  
  -- If no active license found, return invalid
  IF license_record IS NULL THEN
    RAISE NOTICE 'No active license found for user_id: %, license_type: %', v_user_id, v_license_type;
    result := json_build_object(
      'valid', false,
      'reason', 'no_active_license'
    );
    RETURN result;
  END IF;
  
  -- Check if license is expired
  IF license_record.expiry_date < now() THEN
    -- Update license to inactive
    UPDATE public.licenses
    SET is_active = false
    WHERE id = license_record.id;
    
    RAISE NOTICE 'License expired on %', license_record.expiry_date;
    result := json_build_object(
      'valid', false,
      'reason', 'expired',
      'license_type', license_record.license_type,
      'expiry_date', license_record.expiry_date
    );
    RETURN result;
  END IF;
  
  -- License is valid
  RAISE NOTICE 'License is valid for user_id: %, license_type: %', v_user_id, v_license_type;
  result := json_build_object(
    'valid', true,
    'user_id', v_user_id,
    'license_type', license_record.license_type,
    'expiry_date', license_record.expiry_date
  );
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
