-- Create a function to create the licenses table if it doesn't exist
CREATE OR REPLACE FUNCTION create_licenses_table()
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
  -- Check if the table already exists
  IF NOT EXISTS (
    SELECT FROM pg_tables
    WHERE schemaname = 'public'
    AND tablename = 'licenses'
  ) THEN
    -- Create the licenses table
    CREATE TABLE public.licenses (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
      license_type TEXT NOT NULL DEFAULT 'free',
      is_active BOOLEAN NOT NULL DEFAULT true,
      expiry_date TIMESTAMP WITH TIME ZONE NOT NULL,
      active_sessions INTEGER NOT NULL DEFAULT 1,
      max_sessions INTEGER NOT NULL DEFAULT 3,
      created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
      updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
    );

    -- Add RLS policies
    ALTER TABLE public.licenses ENABLE ROW LEVEL SECURITY;

    -- Create policy to allow users to read only their own licenses
    CREATE POLICY "Users can read their own licenses"
      ON public.licenses
      FOR SELECT
      USING (auth.uid() = user_id);

    -- Create policy to allow users to update only their own licenses
    CREATE POLICY "Users can update their own licenses"
      ON public.licenses
      FOR UPDATE
      USING (auth.uid() = user_id);

    -- Create policy to allow users to insert their own licenses
    CREATE POLICY "Users can insert their own licenses"
      ON public.licenses
      FOR INSERT
      WITH CHECK (auth.uid() = user_id);

    -- Create trigger to update updated_at column
    CREATE TRIGGER set_updated_at
      BEFORE UPDATE ON public.licenses
      FOR EACH ROW
      EXECUTE FUNCTION public.set_updated_at();
  END IF;
END;
$$;

-- Create the set_updated_at function if it doesn't exist
CREATE OR REPLACE FUNCTION public.set_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;
