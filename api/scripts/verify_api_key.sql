CREATE OR R<PERSON>LACE FUNCTION verify_api_key(api_key TEXT)
RETURNS BOOLEAN AS $$
DECLARE
    secret_name TEXT;
BEGIN
    -- Check if the API key matches any secret in the vault
    SELECT name INTO secret_name
    FROM vault.decrypted_secrets
    WHERE decrypted_secret = api_key
    AND name LIKE '%_API_KEY'
    LIMIT 1;

    -- If a matching secret is found, return true
    IF secret_name IS NOT NULL THEN
        RETURN TRUE;
    ELSE
        RETURN FALSE;
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- <PERSON>oke execute permission from public
REVOKE EXECUTE ON FUNCTION verify_api_key(TEXT) FROM PUBLIC;

-- Grant execute permission to the authenticator role
GRANT EXECUTE ON FUNCTION verify_api_key(TEXT) TO authenticator;