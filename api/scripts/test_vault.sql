-- Test script for vault functionality
-- This script tests direct access to the vault to diagnose issues

-- Function to test direct vault access
CREATE OR REPLACE FUNCTION test_vault_access()
RETURNS JSONB AS $$
DECLARE
  test_key TEXT := 'test-key-' || gen_random_uuid();
  test_secret TEXT := 'test-secret-' || gen_random_uuid();
  test_name TEXT := 'TEST_ACCESS_KEY';
  result JSONB;
  found_secret TEXT;
BEGIN
  -- Log the test values
  RAISE NOTICE 'Testing vault access with name: %, secret: %', test_name, test_secret;
  
  -- Try to store a secret
  BEGIN
    PERFORM vault.create_secret(
      name => test_name,
      secret => test_secret
    );
    
    RAISE NOTICE 'Secret created successfully';
    
    -- Try to retrieve the secret
    SELECT decrypted_secret INTO found_secret
    FROM vault.decrypted_secrets
    WHERE name = test_name;
    
    IF found_secret = test_secret THEN
      RAISE NOTICE 'Secret retrieved successfully: %', found_secret;
      result := jsonb_build_object(
        'success', true,
        'message', 'Vault access is working correctly',
        'secret_name', test_name,
        'secret_value', found_secret
      );
    ELSE
      RAISE NOTICE 'Secret retrieved but does not match: %', found_secret;
      result := jsonb_build_object(
        'success', false,
        'message', 'Secret retrieved but does not match',
        'expected', test_secret,
        'actual', found_secret
      );
    END IF;
  EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'Error accessing vault: %', SQLERRM;
    result := jsonb_build_object(
      'success', false,
      'message', 'Error accessing vault: ' || SQLERRM
    );
  END;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Function to list all secrets in the vault
CREATE OR REPLACE FUNCTION list_vault_secrets()
RETURNS JSONB AS $$
DECLARE
  secrets JSONB;
  secret_record RECORD;
BEGIN
  -- Create an array to hold the secrets
  secrets := '[]'::JSONB;
  
  -- Try to list all secrets
  BEGIN
    FOR secret_record IN 
      SELECT name, decrypted_secret 
      FROM vault.decrypted_secrets
    LOOP
      secrets := secrets || jsonb_build_object(
        'name', secret_record.name,
        'secret', secret_record.decrypted_secret
      );
    END LOOP;
    
    RETURN jsonb_build_object(
      'success', true,
      'count', jsonb_array_length(secrets),
      'secrets', secrets
    );
  EXCEPTION WHEN OTHERS THEN
    RETURN jsonb_build_object(
      'success', false,
      'message', 'Error listing vault secrets: ' || SQLERRM
    );
  END;
END;
$$ LANGUAGE plpgsql;

-- Test direct API key storage and retrieval
CREATE OR REPLACE FUNCTION test_api_key_storage(
  p_user_id TEXT,
  p_license_type TEXT
) RETURNS JSONB AS $$
DECLARE
  test_api_key TEXT := gen_random_uuid()::TEXT;
  secret_name TEXT;
  found_secret TEXT;
  result JSONB;
BEGIN
  -- Create the secret name
  secret_name := p_user_id || '_' || p_license_type || '_API_KEY';
  
  RAISE NOTICE 'Testing API key storage with name: %, key: %', secret_name, test_api_key;
  
  -- Try to store the API key
  BEGIN
    PERFORM vault.create_secret(
      name => secret_name,
      secret => test_api_key
    );
    
    RAISE NOTICE 'API key stored successfully';
    
    -- Try to retrieve the API key
    SELECT decrypted_secret INTO found_secret
    FROM vault.decrypted_secrets
    WHERE name = secret_name;
    
    IF found_secret = test_api_key THEN
      RAISE NOTICE 'API key retrieved successfully: %', found_secret;
      result := jsonb_build_object(
        'success', true,
        'message', 'API key storage is working correctly',
        'secret_name', secret_name,
        'api_key', found_secret
      );
    ELSE
      RAISE NOTICE 'API key retrieved but does not match: %', found_secret;
      result := jsonb_build_object(
        'success', false,
        'message', 'API key retrieved but does not match',
        'expected', test_api_key,
        'actual', found_secret
      );
    END IF;
  EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'Error storing/retrieving API key: %', SQLERRM;
    result := jsonb_build_object(
      'success', false,
      'message', 'Error storing/retrieving API key: ' || SQLERRM
    );
  END;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Run the tests
SELECT 'Testing vault access' AS test, test_vault_access();
SELECT 'Listing vault secrets' AS test, list_vault_secrets();
SELECT 'Testing API key storage' AS test, test_api_key_storage('006f85f7-ce10-4f1a-8f84-4546a890cfd1', 'free');
