// YouTube transcript retrieval command-line tool
package main

import (
	"flag"
	"fmt"
	"os"

	"github.com/joho/godotenv"
	"promz.ai/api/services"
)

func main() {
	// Load .env file if it exists
	_ = godotenv.Load()

	// Parse command line arguments
	videoURL := flag.String("url", "", "YouTube video URL (required)")
	lang := flag.String("lang", "en", "Language code (default: en)")
	country := flag.String("country", "US", "Country code (default: US)")
	apiKey := flag.String("api-key", os.Getenv("PROMZ_YOUTUBE_API_KEY"), "API key (or PROMZ_YOUTUBE_API_KEY env var)")
	flag.Parse()

	// Check if video URL is provided
	if *videoURL == "" {
		fmt.Println("Error: YouTube video URL is required")
		fmt.Println("Usage: go run integration_example.go -url=https://www.youtube.com/watch?v=VIDEO_ID [-lang=en] [-country=US] [-api-key=YOUR_API_KEY]")
		os.Exit(1)
	}

	// Create transcript service with API key if available
	var transcriptService services.TranscriptServiceInterface
	if *apiKey != "" {
		fmt.Println("Using YouTube API with provided API key")
		transcriptService = services.NewTranscriptServiceWithAPIKey(*apiKey)
	} else {
		fmt.Println("Using fallback scraping method (no API key provided)")
		transcriptService = services.NewTranscriptService()
	}

	// Get transcript and print it
	fmt.Printf("Fetching transcript for %s (language: %s, country: %s)...\n", *videoURL, *lang, *country)
	transcript, title, err := transcriptService.GetTranscript(*videoURL, *lang, *country)

	if err != nil {
		fmt.Printf("Error fetching transcript: %v\n", err)
		os.Exit(1)
	}

	fmt.Printf("Video Title: %s\n\n", title)
	fmt.Println("Transcript:")
	fmt.Println("===========")

	for _, segment := range transcript {
		// Convert offset from milliseconds to seconds for display
		timeInSeconds := float64(segment.Offset) / 1000.0
		fmt.Printf("[%.1fs]: %s\n", timeInSeconds, segment.Text)
	}
}
