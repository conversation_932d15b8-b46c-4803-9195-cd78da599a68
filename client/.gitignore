# Flutter/Dart related ignores
.dart_tool/
.packages
pubspec.lock
*.flutter-plugins
*.flutter-plugins-dependencies
generated_plugin_registrant.dart
macos/Flutter/GeneratedPluginRegistrant.swift

# Build outputs
build/
ios/Flutter/Generated.xcconfig
ios/Flutter/Debug.xcconfig
ios/Flutter/Release.xcconfig
ios/Runner.xcworkspace/contents.xcworkspacedata
ios/Runner.xcodeproj/xcshareddata/xcschemes/Runner.xcscheme
ios/Flutter/Release.xcconfig
ios/Flutter/Debug.xcconfig
ios/.symlinks/
ios/Pods/
ios/Podfile.lock
macos/.symlinks/
macos/Flutter/ephemeral/
windows/flutter/ephemeral/
windows/flutter/generated*
linux/flutter/

# IDE files
.idea/
*.iml
.vscode/

# Miscellaneous
.DS_Store
*.log
*.swp
*.swo
*.swn

# Gradle files
.gradle/
build/

# Local configuration file (sdk path, etc)
local.properties

# Log/OS Files
*.log

# Android Studio generated files and folders
captures/
.externalNativeBuild/
.cxx/
*.apk
output.json

# IntelliJ
*.iml
.idea/
misc.xml
deploymentTargetDropDown.xml
render.experimental.xml

# Keystore files
*.jks
*.keystore

# Google Services (e.g. APIs or Firebase)
google-services.json

# Android Profiling
*.hprof

# SQLite binaries and temporary files
*.dll
*.def
sqlite-dll.zip

# Docs
dartdoc_output/
