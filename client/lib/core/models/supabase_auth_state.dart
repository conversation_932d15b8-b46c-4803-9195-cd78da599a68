import 'package:supabase_flutter/supabase_flutter.dart';

/// Represents the authentication state of the application
class SupabaseAuthState {
  /// Whether the user is authenticated
  final bool isAuthenticated;

  /// The authenticated user, if any
  final User? user;

  /// Whether authentication is in progress
  final bool isLoading;

  /// Creates a new authentication state
  SupabaseAuthState({
    required this.isAuthenticated,
    this.user,
    required this.isLoading,
  });

  /// Creates a copy of this authentication state with the given fields replaced
  SupabaseAuthState copyWith({
    bool? isAuthenticated,
    User? user,
    bool? isLoading,
  }) {
    return SupabaseAuthState(
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
      user: user ?? this.user,
      isLoading: isLoading ?? this.isLoading,
    );
  }
}
