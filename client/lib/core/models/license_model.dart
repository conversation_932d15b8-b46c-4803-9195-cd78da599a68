import 'package:flutter/foundation.dart';

/// License type enum
enum LicenseType {
  none,
  free,
  trial,
  pro,
  enterprise,
  expired,
  error,
}

/// User license class
@immutable
class UserLicense {
  /// Whether the user has a valid license
  final bool hasLicense;

  /// The type of license
  final LicenseType licenseType;

  /// When the license expires (null for perpetual licenses)
  final DateTime? expiresAt;

  /// Creates a new user license
  const UserLicense({
    required this.hasLicense,
    required this.licenseType,
    required this.expiresAt,
  });

  /// Creates an empty license (no license)
  static UserLicense empty() {
    return const UserLicense(
      hasLicense: false,
      licenseType: LicenseType.none,
      expiresAt: null,
    );
  }

  /// Creates a copy of this license with the given fields replaced
  UserLicense copyWith({
    bool? hasLicense,
    LicenseType? licenseType,
    DateTime? expiresAt,
  }) {
    return UserLicense(
      hasLicense: hasLicense ?? this.hasLicense,
      licenseType: licenseType ?? this.licenseType,
      expiresAt: expiresAt ?? this.expiresAt,
    );
  }

  /// Helper function to convert license type string to enum
  static LicenseType getLicenseTypeFromString(String? type) {
    if (type == null || type.isEmpty) return LicenseType.none;

    try {
      // Convert the string to lowercase and try to match with enum values
      final enumString = type.toLowerCase();
      return LicenseType.values.firstWhere(
        (e) => e.toString().split('.').last.toLowerCase() == enumString,
        orElse: () => LicenseType.none,
      );
    } catch (_) {
      return LicenseType.none;
    }
  }

  /// Formats license type for display with first letter capitalized
  /// Returns a user-friendly formatted string representation of the license type
  static String formatLicenseType(LicenseType? licenseType, {bool hasLicense = true}) {
    if (!hasLicense) return 'No license';

    final typeStr = licenseType?.toString().split('.').last ?? '';
    if (typeStr.isEmpty) return '';

    return typeStr.substring(0, 1).toUpperCase() + typeStr.substring(1).toLowerCase();
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserLicense &&
        other.hasLicense == hasLicense &&
        other.licenseType == licenseType &&
        other.expiresAt == expiresAt;
  }

  @override
  int get hashCode => Object.hash(hasLicense, licenseType, expiresAt);

  @override
  String toString() {
    return 'UserLicense(hasLicense: $hasLicense, licenseType: $licenseType, expiresAt: $expiresAt)';
  }
}
