import 'package:promz/core/models/license_model.dart';

/// Represents a user profile in the application
///
/// This model centralizes all user-related information, including authentication
/// state, license information, and display properties
class UserProfileModel {
  /// Whether the user is authenticated
  final bool isAuthenticated;

  /// Whether the user has a valid license
  final bool hasLicense;

  /// The license type if applicable
  final LicenseType licenseType;

  /// User's email address
  final String? email;

  /// User's full name
  final String? fullName;

  /// User's avatar URL
  final String? avatarUrl;

  /// User's ID from authentication provider
  final String? userId;

  /// When the user's license expires (if applicable)
  final DateTime? licenseExpiresAt;

  /// When the profile was last updated
  final DateTime lastUpdated;

  UserProfileModel({
    this.isAuthenticated = false,
    this.hasLicense = false,
    this.licenseType = LicenseType.none,
    this.email,
    this.fullName,
    this.avatarUrl,
    this.userId,
    this.licenseExpiresAt,
    DateTime? lastUpdated,
  }) : lastUpdated = lastUpdated ?? DateTime.now();

  /// Create an empty profile (not authenticated)
  factory UserProfileModel.empty() {
    return UserProfileModel(
      isAuthenticated: false,
      hasLicense: false,
      licenseType: LicenseType.none,
    );
  }

  /// Create from cached data (secure storage)
  factory UserProfileModel.fromCachedData(Map<String, dynamic> data) {
    return UserProfileModel(
      isAuthenticated: data['isAuthenticated'] == true,
      hasLicense: data['hasLicense'] == true,
      licenseType: UserLicense.getLicenseTypeFromString(data['licenseType'] as String?),
      email: data['email'] as String?,
      fullName: data['fullName'] as String?,
      avatarUrl: data['avatarUrl'] as String?,
      userId: data['userId'] as String?,
      licenseExpiresAt: data['licenseExpiresAt'] != null
          ? DateTime.parse(data['licenseExpiresAt'] as String)
          : null,
      lastUpdated: data['lastUpdated'] != null
          ? DateTime.parse(data['lastUpdated'] as String)
          : DateTime.now(),
    );
  }

  /// Convert to a map for storage
  Map<String, dynamic> toMap() {
    return {
      'isAuthenticated': isAuthenticated,
      'hasLicense': hasLicense,
      'licenseType': licenseType.toString().split('.').last,
      'email': email,
      'fullName': fullName,
      'avatarUrl': avatarUrl,
      'userId': userId,
      'licenseExpiresAt': licenseExpiresAt?.toIso8601String(),
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  /// Create a copy with updated properties
  UserProfileModel copyWith({
    bool? isAuthenticated,
    bool? hasLicense,
    LicenseType? licenseType,
    String? email,
    String? fullName,
    String? avatarUrl,
    String? userId,
    DateTime? licenseExpiresAt,
    DateTime? lastUpdated,
  }) {
    return UserProfileModel(
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
      hasLicense: hasLicense ?? this.hasLicense,
      licenseType: licenseType ?? this.licenseType,
      email: email ?? this.email,
      fullName: fullName ?? this.fullName,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      userId: userId ?? this.userId,
      licenseExpiresAt: licenseExpiresAt ?? this.licenseExpiresAt,
      lastUpdated: lastUpdated ?? DateTime.now(),
    );
  }
}
