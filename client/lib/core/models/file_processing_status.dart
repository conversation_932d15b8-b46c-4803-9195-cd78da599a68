/// Status of file processing
class FileProcessingStatus {
  final String id;
  final String status;
  final double progress;
  final String? error;
  final String? message;
  final int? tokensProcessed;
  final int? tokensLimit;
  final bool tokensExceeded;

  FileProcessingStatus({
    required this.id,
    required this.status,
    required this.progress,
    this.error,
    this.message,
    this.tokensProcessed,
    this.tokensLimit,
    this.tokensExceeded = false,
  });

  bool get isCompleted => status == 'completed';
  bool get isFailed => status == 'failed';
  bool get isProcessing => status == 'processing' || status == 'queued';

  /// Returns true if the processing was limited by token count
  bool get wasLimitedByTokens => tokensExceeded;

  /// Returns a human-readable description of token usage
  String? get tokenUsageDescription {
    if (tokensProcessed == null || tokensLimit == null) return null;

    final processedK = (tokensProcessed! / 1000).round();
    final limitK = (tokensLimit! / 1000).round();

    return tokensExceeded
        ? 'Processed $processedK K tokens (limited to $limitK K)'
        : 'Processed $processedK K tokens of $limitK K limit';
  }
}

/// Result of file processing
class FileProcessingResult {
  final String id;
  final String contentType;
  final Map<String, dynamic> metadata;
  final bool hasFullContent;
  final String? contentUrl;
  final DateTime? expiresAt;
  final int? tokensProcessed;
  final int? tokensLimit;
  final bool tokensExceeded;

  FileProcessingResult({
    required this.id,
    required this.contentType,
    required this.metadata,
    this.hasFullContent = true,
    this.contentUrl,
    this.expiresAt,
    this.tokensProcessed,
    this.tokensLimit,
    this.tokensExceeded = false,
  });

  /// Returns true if the processing was limited by token count
  bool get wasLimitedByTokens => tokensExceeded;

  /// Returns a human-readable description of token usage
  String? get tokenUsageDescription {
    if (tokensProcessed == null || tokensLimit == null) return null;

    final processedK = (tokensProcessed! / 1000).round();
    final limitK = (tokensLimit! / 1000).round();

    return tokensExceeded
        ? 'Processed $processedK K tokens (limited to $limitK K)'
        : 'Processed $processedK K tokens of $limitK K limit';
  }

  /// Returns true if token information is available
  bool get hasTokenInfo => tokensProcessed != null && tokensLimit != null;

  /// Returns true if content is available via URL
  bool get hasContentUrl => contentUrl != null && contentUrl!.isNotEmpty;
}
