import 'package:promz_common/promz_common.dart';

/// Represents the status of a shared content processing task
enum ContentProcessingTaskStatus {
  /// Task is created but not yet started
  pending,

  /// Task is actively being processed
  processing,

  /// Task completed successfully
  completed,

  /// Task encountered an error
  failed,

  /// Task failed due to file size limit exceeded
  sizeLimitExceeded,
}

/// Extension to provide human-readable labels for task statuses
extension ContentProcessingTaskStatusLabels on ContentProcessingTaskStatus {
  /// Returns a user-friendly label for the status
  String get label {
    switch (this) {
      case ContentProcessingTaskStatus.pending:
        return 'Pending';
      case ContentProcessingTaskStatus.processing:
        return 'Processing';
      case ContentProcessingTaskStatus.completed:
        return 'Completed';
      case ContentProcessingTaskStatus.failed:
        return 'Failed';
      case ContentProcessingTaskStatus.sizeLimitExceeded:
        return 'Size Limit';
    }
  }
}

/// Represents a file processing task for shared content
class ContentProcessingTask {
  /// Unique identifier for the task
  final String id;

  /// Path to the file being processed
  final String filePath;

  /// Name of the file
  final String fileName;

  /// Display name for the task (more user-friendly than fileName)
  final String displayName;

  /// Size of the file in bytes
  final int fileSize;

  /// Content hash of the file (for duplicate detection)
  final String? fileHash;

  /// Current status of the task
  final ContentProcessingTaskStatus status;

  /// Progress from 0.0 to 1.0
  final double progress;

  /// Optional error message if status is failed
  final String? errorMessage;

  /// When the task was created
  final DateTime createdAt;

  /// When the task was last updated
  final DateTime updatedAt;

  /// ID of the processed content (if completed)
  final String? resultId;

  /// Source of the task (e.g., 'share_intent', 'internal', etc.)
  final String source;

  /// Creates a new shared content task
  ContentProcessingTask({
    required this.id,
    required this.filePath,
    required this.fileName,
    String? displayName,
    required this.fileSize,
    required this.status,
    this.fileHash,
    this.progress = 0.0,
    this.errorMessage,
    required this.createdAt,
    required this.updatedAt,
    this.resultId,
    this.source = 'internal',
  }) : displayName = displayName ?? fileName;

  /// Creates a new task from a file path
  factory ContentProcessingTask.fromFilePath(String filePath,
      {String source = 'internal', String? displayName}) {
    final fileName = filePath.split('/').last;

    // Generate initial display name based on content type
    String initialDisplayName = displayName ?? _generateInitialDisplayName(filePath, fileName);

    // Generate a unique ID based on timestamp and file path
    final id = '${DateTime.now().millisecondsSinceEpoch}_${filePath.hashCode}';

    final now = DateTime.now();

    return ContentProcessingTask(
      id: id,
      filePath: filePath,
      fileName: fileName,
      displayName: initialDisplayName,
      fileSize: 0, // Will be updated when file is accessed
      status: ContentProcessingTaskStatus.pending,
      createdAt: now,
      updatedAt: now,
      source: source,
    );
  }

  /// Generates an initial display name based on file type detection
  static String _generateInitialDisplayName(String filePath, String fileName) {
    final lowerPath = filePath.toLowerCase();
    final lowerName = fileName.toLowerCase();

    // Handle YouTube URLs
    if (lowerPath.contains('youtube.com') || lowerPath.contains('youtu.be')) {
      return 'YouTube Video';
    }

    // Handle News Articles
    if (lowerPath.startsWith('http') &&
        (lowerPath.contains('news') ||
            lowerPath.contains('article') ||
            lowerPath.contains('.com/'))) {
      return 'News Article';
    }

    // Handle WhatsApp chats
    if (lowerName.contains('whatsapp')) {
      return 'WhatsApp Chat';
    }

    // Handle ZIP files
    if (lowerName.endsWith('.zip')) {
      return 'ZIP Archive';
    }

    // Handle PDF documents
    if (lowerName.endsWith('.pdf')) {
      return 'PDF Document';
    }

    // Default to the filename
    return fileName;
  }

  /// Creates a copy of this task with updated properties
  ContentProcessingTask copyWith({
    String? id,
    String? filePath,
    String? fileName,
    String? displayName,
    int? fileSize,
    String? fileHash,
    ContentProcessingTaskStatus? status,
    double? progress,
    String? errorMessage,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? resultId,
    String? source,
    bool clearErrorMessage = false,
    bool clearResultId = false,
  }) {
    return ContentProcessingTask(
      id: id ?? this.id,
      filePath: filePath ?? this.filePath,
      fileName: fileName ?? this.fileName,
      displayName: displayName ?? this.displayName,
      fileSize: fileSize ?? this.fileSize,
      fileHash: fileHash ?? this.fileHash,
      status: status ?? this.status,
      progress: progress ?? this.progress,
      errorMessage: clearErrorMessage ? null : (errorMessage ?? this.errorMessage),
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
      resultId: clearResultId ? null : (resultId ?? this.resultId),
      source: source ?? this.source,
    );
  }

  /// Converts this task to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'filePath': filePath,
      'fileName': fileName,
      'displayName': displayName,
      'fileSize': fileSize,
      'fileHash': fileHash,
      'status': status.toString().split('.').last,
      'progress': progress,
      'errorMessage': errorMessage,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'resultId': resultId,
      'source': source,
    };
  }

  /// Creates a task from a JSON map
  factory ContentProcessingTask.fromJson(Map<String, dynamic> json) {
    return ContentProcessingTask(
      id: json['id'] as String,
      filePath: json['filePath'] as String,
      fileName: json['fileName'] as String,
      displayName: json['displayName'] as String? ?? json['fileName'] as String,
      fileSize: json['fileSize'] as int,
      fileHash: json['fileHash'] as String?,
      status: _statusFromString(json['status'] as String),
      progress: (json['progress'] as num).toDouble(),
      errorMessage: json['errorMessage'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      resultId: json['resultId'] as String?,
      source: json['source'] as String? ?? 'internal',
    );
  }

  /// Parses a status string to the enum value
  static ContentProcessingTaskStatus _statusFromString(String statusStr) {
    return ContentProcessingTaskStatus.values.firstWhere(
      (status) => status.toString().split('.').last == statusStr,
      orElse: () => ContentProcessingTaskStatus.pending,
    );
  }

  @override
  String toString() {
    return 'ContentProcessingTask(id: $id, displayName: $displayName, status: ${status.label}, progress: ${(progress * 100).toStringAsFixed(1)}%)';
  }

  /// Logs task information for debugging
  void logTaskInfo({String? message}) {
    appLog.debug(
      '${message ?? 'Task info'}: $toString()',
      name: 'ContentProcessingTask',
    );
  }
}
