/// Represents a processed file with its content and metadata
class ProcessedFile {
  /// The name of the file
  final String fileName;

  /// The text content of the file, if it's a text file
  final String? textContent;

  /// The binary content of the file, if it's a binary file
  final List<int>? binaryContent;

  /// The size of the file in bytes
  final int size;

  /// The last modified date of the file
  final DateTime? lastModified;

  /// The MIME type of the file
  final String? mimeType;

  ProcessedFile({
    required this.fileName,
    this.textContent,
    this.binaryContent,
    required this.size,
    this.lastModified,
    this.mimeType,
  });

  /// Creates a ProcessedFile from text content
  factory ProcessedFile.fromText({
    required String fileName,
    required String content,
    int? size,
    DateTime? lastModified,
    String? mimeType,
  }) {
    return ProcessedFile(
      fileName: fileName,
      textContent: content,
      size: size ?? content.length,
      lastModified: lastModified,
      mimeType: mimeType,
    );
  }

  /// Creates a ProcessedFile from binary content
  factory ProcessedFile.fromBinary({
    required String fileName,
    required List<int> content,
    DateTime? lastModified,
    String? mimeType,
  }) {
    return ProcessedFile(
      fileName: fileName,
      binaryContent: content,
      size: content.length,
      lastModified: lastModified,
      mimeType: mimeType,
    );
  }

  /// Check if this file has any text content
  bool get hasTextContent => textContent?.isNotEmpty ?? false;

  /// Check if this file has any binary content
  bool get hasBinaryContent => binaryContent?.isNotEmpty ?? false;

  /// Get the file extension
  String get extension =>
      fileName.contains('.') ? fileName.substring(fileName.lastIndexOf('.') + 1) : '';

  /// Create a copy of this file with some fields replaced
  ProcessedFile copyWith({
    String? fileName,
    String? textContent,
    List<int>? binaryContent,
    int? size,
    DateTime? lastModified,
    String? mimeType,
  }) {
    return ProcessedFile(
      fileName: fileName ?? this.fileName,
      textContent: textContent ?? this.textContent,
      binaryContent: binaryContent ?? this.binaryContent,
      size: size ?? this.size,
      lastModified: lastModified ?? this.lastModified,
      mimeType: mimeType ?? this.mimeType,
    );
  }
}
