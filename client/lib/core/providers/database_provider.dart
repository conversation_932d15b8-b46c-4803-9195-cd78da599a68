import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../database/database.dart';

/// Global database provider that ensures a single database instance throughout the app
final databaseProvider = FutureProvider<AppDatabase>((ref) async {
  final database = await AppDatabase.getInstance();
  database.ensureOpen();

  ref.onDispose(() async {
    await database.close();
  });

  return database;
});
