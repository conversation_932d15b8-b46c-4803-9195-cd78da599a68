import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:promz/core/providers/database_provider.dart';
import 'package:promz/core/providers/file_processing_provider.dart';
import 'package:promz/core/services/attachment/attachment_registry_service.dart';
import 'package:promz/core/services/client_context_service.dart';
import 'package:promz/core/services/content_processing_service.dart';
import 'package:promz/core/services/deep_link_service.dart';
import 'package:promz/core/services/file/base/file_type_service.dart';
import 'package:promz/core/services/file/containers/zip_service.dart';
import 'package:promz/core/services/file/file_upload_service.dart';
import 'package:promz/core/services/file/file_upload_service_provider.dart';
import 'package:promz/core/services/file/metadata_enhancer_registry.dart';
import 'package:promz/core/services/file/types/whatsapp_service.dart';
import 'package:promz/core/services/file_processing_service.dart';
import 'package:promz/core/services/license_manager_service.dart';
import 'package:promz/core/services/supabase_service.dart';
import 'package:promz/features/home/<USER>/entity_detection_service.dart';
import 'package:promz/features/news/services/news_article_service.dart';
import 'package:promz/features/youtube/services/youtube_service.dart';
import 'package:promz/core/services/windows_oauth_service.dart';

/// Provider for ClientContextService singleton
/// This initializes the singleton instance and returns it
final clientContextServiceProvider = FutureProvider<ClientContextService>((ref) async {
  final db = ref.watch(databaseProvider.future);

  // First initialize the ClientContextService
  ClientContextService.initialize(db);

  // Get the singleton instance
  final clientContext = ClientContextService();

  // Then inject the ContentProcessingService
  final contentProcessingService = await ref.watch(contentProcessingServiceProvider.future);
  clientContext.contentProcessingService = contentProcessingService;

  return clientContext;
});

/// Provider for FileProcessingService singleton
final fileProcessingServiceProvider = Provider<FileProcessingService>((ref) {
  return FileProcessingService();
});

/// Provider for AttachmentRegistryService singleton
final attachmentRegistryServiceProvider = Provider<AttachmentRegistryService>((ref) {
  return AttachmentRegistryService();
});

/// Provider for EntityDetectionService singleton
final entityDetectionServiceProvider = Provider<EntityDetectionService>((ref) {
  final db = ref.watch(databaseProvider.future);
  return EntityDetectionService(db: db);
});

/// Provider for NewsArticleService singleton
final newsArticleServiceProvider = Provider<NewsArticleService>((ref) {
  final attachmentRegistry = ref.watch(attachmentRegistryServiceProvider);
  final entityDetection = ref.watch(entityDetectionServiceProvider);
  return NewsArticleService(
    attachmentRegistry: attachmentRegistry,
    entityDetectionService: entityDetection,
  );
});

/// Provider for WhatsAppService
final whatsAppServiceProvider = Provider<WhatsAppService>((ref) {
  final whatsAppService = WhatsAppService();

  // Register the WhatsAppService as a metadata enhancer
  MetadataEnhancerRegistry.instance.registerEnhancer(whatsAppService);

  return whatsAppService;
});

/// Provider for the MetadataEnhancerRegistry singleton
final metadataEnhancerRegistryProvider = Provider<MetadataEnhancerRegistry>((ref) {
  // Ensure WhatsAppService is initialized which registers itself as an enhancer
  ref.watch(whatsAppServiceProvider);

  // Return the singleton instance
  return MetadataEnhancerRegistry.instance;
});

/// Provider for ZipService singleton
final zipServiceProvider = Provider<ZipService>((ref) {
  final attachmentRegistry = ref.watch(attachmentRegistryServiceProvider);
  final whatsAppService = ref.watch(whatsAppServiceProvider);

  // Create a list of file type services
  final fileServices = <FileTypeService>[whatsAppService];

  return ZipService(fileServices, attachmentRegistry);
});

/// Provider for YouTubeService singleton
final youtubeServiceProvider = Provider<YouTubeService>((ref) {
  final attachmentRegistry = ref.watch(attachmentRegistryServiceProvider);
  final entityDetection = ref.watch(entityDetectionServiceProvider);
  return YouTubeService(
    attachmentRegistry: attachmentRegistry,
    entityDetectionService: entityDetection,
  );
});

/// Provider for FileUploadService singleton
final fileUploadServiceProvider = Provider<FileUploadService>((ref) {
  // Use the factory method to create the service with proper configuration
  return FileUploadServiceProvider.create();
});

/// Provider for LicenseManagerService singleton
final licenseManagerServiceProvider = Provider<LicenseManagerService>((ref) {
  return LicenseManagerService();
});

/// Provider for ContentProcessingService singleton
final contentProcessingServiceProvider = FutureProvider<ContentProcessingService>((ref) async {
  final fileProcessingService = ref.watch(fileProcessingServiceProvider);
  final attachmentRegistry = ref.watch(attachmentRegistryServiceProvider);
  final newsArticleService = ref.watch(newsArticleServiceProvider);
  final zipService = ref.watch(zipServiceProvider);
  final youtubeService = ref.watch(youtubeServiceProvider);
  final licenseManager = ref.watch(licenseManagerServiceProvider);
  final fileProcessingClient = await ref.watch(fileProcessingClientProvider.future);
  final entityDetectionService = ref.watch(entityDetectionServiceProvider);

  return ContentProcessingService(
    fileProcessingService: fileProcessingService,
    attachmentRegistry: attachmentRegistry,
    newsArticleService: newsArticleService,
    zipService: zipService,
    youtubeService: youtubeService,
    fileUploadService: ref.watch(fileUploadServiceProvider),
    licenseManagerService: licenseManager,
    fileProcessingClient: fileProcessingClient,
    entityDetectionService: entityDetectionService,
  );
});

/// Provider for the SupabaseService
final supabaseServiceProvider = Provider<SupabaseService>((ref) {
  return SupabaseService();
});

/// Provider for WindowsOAuthService
final windowsOAuthServiceProvider = Provider<WindowsOAuthService>((ref) {
  final supabaseService = ref.watch(supabaseServiceProvider);
  final windowsOAuthService = WindowsOAuthService();
  windowsOAuthService.initialize(
      supabaseService.client, supabaseService.supabaseUrl, supabaseService.supabaseAnonKey);
  return windowsOAuthService;
});

/// Provider for the DeepLinkService
final deepLinkServiceProvider = Provider<DeepLinkService>((ref) {
  return DeepLinkService();
});
