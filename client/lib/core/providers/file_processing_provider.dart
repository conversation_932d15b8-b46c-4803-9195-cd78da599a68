import 'dart:developer' as dev;
import 'dart:io';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:grpc/grpc.dart';
import 'package:promz/core/models/file/processed_file.dart';
import 'package:promz/core/providers/api_client_provider.dart';
import 'package:promz/core/services/file/file_processing_client.dart';
import 'package:promz/core/services/file/file_upload_service.dart';
import 'package:promz/generated/content_upload.pbgrpc.dart';
import 'package:promz_common/promz_common.dart';

/// Provider for FileProcessingClient
///
/// This provider creates and configures a FileProcessingClient instance
/// with the appropriate API client.
final fileProcessingClientProvider = FutureProvider<FileProcessingClient>((ref) async {
  final apiClient = ref.watch(apiClientProvider);
  // Get the gRPC channel from ApiClient
  final channel = await apiClient.getGrpcChannel();

  // Get auth headers for gRPC calls
  final authHeaders = await apiClient.getAuthHeaders(isProtected: true);
  final apiKey = authHeaders['X-API-Key'] ?? '';

  // Create gRPC metadata with auth information
  final callOptions = CallOptions(
    metadata: {
      'x-api-key': apiKey,
    },
  );

  // Create the specific gRPC client with auth options
  final grpcClient = ContentUploadServiceClient(
    channel,
    options: callOptions,
  );

  appLog.debug('Created gRPC client with API key authentication',
      name: 'fileProcessingClientProvider');

  return FileProcessingClient(
    apiClient: apiClient,
    grpcClient: grpcClient,
  );
});

/// Provider for tracking processed files
///
/// This provider maintains a list of processed files from zip archives
final processedFilesProvider =
    StateNotifierProvider<ProcessedFilesNotifier, List<ProcessedFile>>((ref) {
  return ProcessedFilesNotifier();
});

class ProcessedFilesNotifier extends StateNotifier<List<ProcessedFile>> {
  ProcessedFilesNotifier() : super([]);

  /// Process a zip file using the FileProcessingClient
  ///
  /// This method uploads the file to the server for processing
  /// and updates the state with the processed files
  Future<void> processZipFile(String filePath, FileProcessingClient client) async {
    try {
      // Clear the current state
      state = [];

      // Create a File object from the path
      final file = File(filePath);

      // Check metadata to ensure the file can be uploaded to the server
      final metadataCheck = await client.checkFileMetadata(
        file,
        metadata: {'fileType': 'zip'},
      );

      if (!metadataCheck.canUpload) {
        throw Exception('File cannot be uploaded: ${metadataCheck.message}');
      }

      // Upload the file for processing
      final uploadResponse = await client.uploadFile(
        file,
        metadataCheck: metadataCheck,
        metadata: {'fileType': 'zip'},
      );

      // Subscribe to status updates stream
      final statusStream = client.subscribeToStatusUpdates(uploadResponse.id);

      // Wait for processing to complete or fail
      // Note: In a real UI, you'd listen to this stream continuously
      //       to update progress indicators.
      final FileProcessingStatus finalStatus = await statusStream.firstWhere(
        (status) => !status.isProcessing, // Wait for a non-processing status
        orElse: () => FileProcessingStatus(
          // Fallback in case stream closes unexpectedly
          id: uploadResponse.id,
          status: 'error',
          progress: 0.0, // Added missing progress parameter
          error: 'Status stream closed before completion.',
        ),
      );

      dev.log('Processing finished with status: ${finalStatus.status}',
          name: 'ProcessedFilesNotifier');

      if (finalStatus.isFailed) {
        throw Exception('Processing failed: ${finalStatus.error ?? 'Unknown error'}');
      }

      // Get the results and convert them to ProcessedFile objects
      final processingResults = await client.getResults(uploadResponse.id);

      // Convert the results to ProcessedFile objects
      // This is a simplified implementation - in a real app, you would
      // parse the server response and create appropriate objects
      final List<ProcessedFile> processedFiles = [];

      // Use the processing results to create ProcessedFile objects
      if (processingResults.contentType == 'zip' &&
          processingResults.metadata.containsKey('files')) {
        // Extract files from metadata if available
        final filesList = processingResults.metadata['files'] as List?;
        if (filesList != null) {
          for (final fileData in filesList) {
            if (fileData is Map<String, dynamic>) {
              // Create a ProcessedFile from the data
              final fileName = fileData['name'] as String? ?? 'Unknown';
              final content = fileData['content'] as String? ?? '';
              final mimeType = fileData['contentType'] as String? ?? 'text/plain';

              processedFiles.add(ProcessedFile.fromText(
                fileName: fileName,
                content: content,
                mimeType: mimeType,
              ));
            }
          }
        }
      }

      state = processedFiles;
    } catch (e, stack) {
      dev.log('Error in ProcessedFilesNotifier', error: e, stackTrace: stack);
      state = [];
      rethrow;
    }
  }

  /// Find the first text file in the processed files
  ProcessedFile? findFirstTextFile() {
    return state.cast<ProcessedFile?>().firstWhere(
          (file) => file?.hasTextContent ?? false,
          orElse: () => null,
        );
  }
}
