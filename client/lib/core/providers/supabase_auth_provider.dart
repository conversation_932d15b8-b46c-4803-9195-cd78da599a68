import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:promz/core/providers/service_providers.dart';
import 'package:promz_common/promz_common.dart';
import 'package:promz/core/models/supabase_auth_state.dart';

/// Provider for the current authentication state
final authStateProvider = StreamProvider<SupabaseAuthState>((ref) {
  final supabaseService = ref.watch(supabaseServiceProvider);

  if (!supabaseService.isInitialized) {
    appLog.debug('Supabase not initialized, returning unauthenticated state',
        name: 'authStateProvider');
    // Return a stream with an initial unauthenticated state
    return Stream.value(SupabaseAuthState(
      isAuthenticated: false,
      user: null,
      isLoading: true,
    ));
  }

  appLog.debug('Setting up auth state listener', name: 'authStateProvider');

  // Listen to our custom auth state stream for more reliable updates
  return supabaseService.authStateStream;
});
