import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:promz/core/models/license_model.dart';
import 'package:promz/core/providers/license_manager_provider.dart';
import 'package:promz/core/providers/service_providers.dart';
import 'package:promz/core/providers/supabase_auth_provider.dart';
import 'package:promz/core/services/license_service.dart';
import 'package:promz_common/promz_common.dart';

/// Provider for the LicenseService
final licenseServiceProvider = Provider<LicenseService>((ref) {
  final supabaseService = ref.watch(supabaseServiceProvider);
  return LicenseService(supabaseService);
});

/// Provider for the current API key
final apiKeyProvider = FutureProvider<String?>((ref) async {
  final licenseService = ref.watch(licenseServiceProvider);
  final authState = ref.watch(authStateProvider);

  // If not authenticated, return null
  if (authState.value == null || !authState.value!.isAuthenticated) {
    return null;
  }

  try {
    // Initialize the license service and return the API key
    return await licenseService.initialize();
  } catch (e, stack) {
    appLog.error('Error getting API key', name: 'apiKeyProvider', error: e, stackTrace: stack);
    return null;
  }
});

/// Provider for checking if the user has an active trial
final hasActiveTrialProvider = FutureProvider<bool>((ref) async {
  final licenseService = ref.watch(licenseServiceProvider);
  final authState = ref.watch(authStateProvider);

  // If not authenticated, return false
  if (authState.value == null || !authState.value!.isAuthenticated) {
    return false;
  }

  try {
    return await licenseService.hasActiveTrial();
  } catch (e, stack) {
    appLog.error('Error checking trial status',
        name: 'hasActiveTrialProvider', error: e, stackTrace: stack);
    return false;
  }
});

/// Provider for user license status
final userLicenseProvider = FutureProvider<UserLicense>((ref) async {
  final authState = ref.watch(authStateProvider);
  final licenseManagerService = ref.watch(licenseManagerProvider);

  // Return immediately if not authenticated
  if (authState.value == null || !authState.value!.isAuthenticated) {
    return const UserLicense(
      hasLicense: false,
      licenseType: LicenseType.none,
      expiresAt: null,
    );
  }

  try {
    final licenseData = await licenseManagerService.getUserLicense();

    // Add detailed logging for debugging
    if (kDebugMode) {
      appLog.debug('License data from database: ${licenseData?.toString() ?? 'null'}',
          name: 'userLicenseProvider');
    }

    if (licenseData == null) {
      if (kDebugMode) {
        appLog.debug('No license found for user, returning none', name: 'userLicenseProvider');
      }

      // Try to create a free license automatically
      try {
        final success = await licenseManagerService.ensureUserHasFreeLicense();

        if (success) {
          if (kDebugMode) {
            appLog.debug('Created free license automatically in provider',
                name: 'userLicenseProvider');
          }

          // Fetch the newly created license
          final newLicenseData = await licenseManagerService.getUserLicense();

          if (newLicenseData != null) {
            if (kDebugMode) {
              appLog.debug('Retrieved newly created license: ${newLicenseData.toString()}',
                  name: 'userLicenseProvider');
            }

            // The newLicenseData is already a UserLicense object, so we can use its properties directly
            final licenseType = newLicenseData.licenseType;
            final expiresAt = newLicenseData.expiresAt;

            return UserLicense(
              hasLicense: true, // Free license is always valid
              licenseType: licenseType,
              expiresAt: expiresAt,
            );
          }
        } else if (kDebugMode) {
          appLog.debug('Failed to create free license automatically', name: 'userLicenseProvider');
        }
      } catch (e) {
        appLog.error('Error creating free license in provider',
            name: 'userLicenseProvider', error: e);
      }

      return const UserLicense(
        hasLicense: false,
        licenseType: LicenseType.none,
        expiresAt: null,
      );
    }

    // Log the license data for debugging
    if (kDebugMode) {
      appLog.debug('License data retrieved: ${licenseData.toString()}',
          name: 'userLicenseProvider');
    }

    // licenseData is already a UserLicense object, so we can use its properties directly
    final licenseType = licenseData.licenseType;

    // Get the expiration date from the license data
    final expiresAt = licenseData.expiresAt ??
        DateTime.now().add(const Duration(days: 36500)); // Default to 100 years for free licenses

    // Consider a license valid if it's not expired OR if it's a free license
    final isFree = licenseType == LicenseType.free;
    final isValid = expiresAt.isAfter(DateTime.now()) || isFree;

    if (kDebugMode) {
      appLog.debug(
          'License validation - type: ${licenseType.toString()}, '
          'isFree: $isFree, expiresAt: $expiresAt, isValid: $isValid',
          name: 'userLicenseProvider');
    }

    return UserLicense(
      hasLicense: isValid,
      licenseType: isValid ? licenseType : LicenseType.expired,
      expiresAt: expiresAt,
    );
  } catch (e, stack) {
    appLog.error('Error getting user license',
        name: 'userLicenseProvider', error: e, stackTrace: stack);

    return const UserLicense(
      hasLicense: false,
      licenseType: LicenseType.error,
      expiresAt: null,
    );
  }
});
