import 'package:flutter_riverpod/flutter_riverpod.dart';

/// A provider that manages app restart state
///
/// This provider is used to trigger a full app restart when certain
/// configuration changes require reinitializing all services.
class AppRestartNotifier extends StateNotifier<int> {
  AppRestartNotifier() : super(0);

  /// Increment the restart counter to trigger a rebuild of the entire app
  void restartApp() {
    state = state + 1;
  }
}

/// Provider for app restart functionality
final appRestartProvider = StateNotifierProvider<AppRestartNotifier, int>((ref) {
  return AppRestartNotifier();
});
