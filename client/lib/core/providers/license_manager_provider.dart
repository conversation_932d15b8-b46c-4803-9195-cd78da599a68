import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:promz/core/models/license_model.dart';
import 'package:promz/core/providers/supabase_auth_provider.dart';
import 'package:promz/core/services/license_manager_service.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Provider for the LicenseManagerService
final licenseManagerProvider = Provider<LicenseManagerService>((ref) {
  // Initialize the service if not already initialized
  LicenseManagerService.initialize();
  return LicenseManagerService();
});

/// Stream provider for the current license state
final licenseStateProvider = StreamProvider<UserLicense?>((ref) {
  final manager = ref.watch(licenseManagerProvider);
  return manager.licenseStream;
});

/// Provider for the current license
final currentLicenseProvider = Provider<UserLicense?>((ref) {
  final manager = ref.watch(licenseManagerProvider);
  return manager.currentLicense;
});

/// Provider for checking if the current license is valid
final isLicenseValidProvider = Provider<bool>((ref) {
  final manager = ref.watch(licenseManagerProvider);
  return manager.isLicenseValid();
});

/// Provider for the current API key
final apiKeyProvider = Provider<String?>((ref) {
  final manager = ref.watch(licenseManagerProvider);
  return manager.apiKey;
});

/// Provider that combines auth state and license state
final authAndLicenseProvider = Provider<AuthAndLicenseState>((ref) {
  final authState = ref.watch(authStateProvider).value;
  final isLicenseValid = ref.watch(isLicenseValidProvider);

  return AuthAndLicenseState(
    isAuthenticated: authState?.isAuthenticated ?? false,
    isLicenseValid: isLicenseValid,
    user: authState?.user,
  );
});

/// Combined auth and license state
class AuthAndLicenseState {
  final bool isAuthenticated;
  final bool isLicenseValid;
  final User? user;

  AuthAndLicenseState({
    required this.isAuthenticated,
    required this.isLicenseValid,
    this.user,
  });

  /// Check if the user has access to the application
  bool get hasAccess => isAuthenticated && isLicenseValid;

  @override
  String toString() => 'AuthAndLicenseState(isAuthenticated: $isAuthenticated, '
      'isLicenseValid: $isLicenseValid, user: ${user?.email})';
}
