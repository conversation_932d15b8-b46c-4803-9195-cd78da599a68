import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:promz/core/providers/content_processing_task_provider.dart';
import 'package:promz/core/providers/service_providers.dart';
import '../services/shared_content_handler.dart';
import 'package:promz/features/input_selection/models/input_source.dart';

/// Provider for the SharedContentHandler.
final sharedContentHandlerProvider = FutureProvider<SharedContentHandler>((ref) async {
  // Watch and await the FutureProvider for ContentProcessingService
  final processingService = await ref.watch(contentProcessingServiceProvider.future);

  // Get the task manager from the provider
  final taskManager = ref.watch(contentProcessingTaskManagerProvider);

  // Create the shared content handler with the Ref for license validation
  final handler = SharedContentHandler(
    contentProcessingService: processingService,
    taskManager: taskManager,
    ref: ref, // Pass the ref to access license provider
  );

  // Ensure the handler is disposed when the provider is disposed
  ref.onDispose(() {
    handler.dispose();
  });

  return handler;
});

/// Provider for the stream of shared content
final sharedContentStreamProvider = StreamProvider<InputSource>((ref) {
  final stream = ref.watch(sharedContentHandlerProvider.select((asyncValue) {
    // Access the value safely, return null if not AsyncData
    return asyncValue.value?.sharedContent;
  }));
  // Return the stream if available, otherwise an empty stream.
  return stream ?? const Stream.empty();
});
