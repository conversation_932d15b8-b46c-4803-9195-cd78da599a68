import 'dart:async';
import 'package:promz/core/models/content_processing_task.dart';
import 'package:promz/core/services/content_processing_task_manager.dart';
import 'package:riverpod/riverpod.dart';

/// Provider for the ContentProcessingTaskManager
final contentProcessingTaskManagerProvider = Provider<ContentProcessingTaskManager>((ref) {
  return ContentProcessingTaskManager.instance;
});

/// Provider for active tasks count
final activeTasksCountProvider = StreamProvider<int>((ref) {
  final taskManager = ref.watch(contentProcessingTaskManagerProvider);

  return taskManager.tasksStream.map((tasks) {
    return tasks
        .where((task) =>
            task.status == ContentProcessingTaskStatus.pending ||
            task.status == ContentProcessingTaskStatus.processing)
        .length;
  });
});

/// Provider for all tasks
final contentProcessingTasksProvider = StreamProvider<List<ContentProcessingTask>>((ref) {
  final taskManager = ref.watch(contentProcessingTaskManagerProvider);
  return taskManager.tasksStream;
});

/// Provider that checks if any tasks have size limit errors
final hasSizeLimitErrorsProvider = StreamProvider<bool>((ref) {
  final taskManager = ref.watch(contentProcessingTaskManagerProvider);
  final recentSizeLimitErrorProvider = ref.watch(recentSizeLimitErrorStateProvider.notifier);

  return taskManager.tasksStream.map((tasks) {
    final hasSizeLimitError = tasks.any((task) =>
        task.status == ContentProcessingTaskStatus.sizeLimitExceeded ||
        (task.status == ContentProcessingTaskStatus.failed &&
            task.errorMessage != null &&
            task.errorMessage!.toLowerCase().contains('size limit')));

    // If we detect a size limit error, update the recent error state
    if (hasSizeLimitError) {
      recentSizeLimitErrorProvider.setHasRecentError();
    }

    // Return true if there's either a current error or a recent one
    return hasSizeLimitError || recentSizeLimitErrorProvider.hasRecentError;
  });
});

/// State provider to track recent file size limit errors
/// This ensures the error indicator remains visible for a period of time
/// even if the task is removed from the active tasks list
final recentSizeLimitErrorStateProvider =
    StateNotifierProvider<RecentSizeLimitErrorNotifier, bool>((ref) {
  return RecentSizeLimitErrorNotifier();
});

/// Notifier for tracking recent file size limit errors
class RecentSizeLimitErrorNotifier extends StateNotifier<bool> {
  Timer? _errorTimer;
  bool _hasRecentError = false;

  RecentSizeLimitErrorNotifier() : super(false);

  bool get hasRecentError => _hasRecentError;

  /// Set that a recent error has occurred and start a timer to clear it
  void setHasRecentError() {
    _hasRecentError = true;
    state = true;

    // Cancel any existing timer
    _errorTimer?.cancel();

    // Set a timer to clear the error state after 30 seconds
    _errorTimer = Timer(const Duration(seconds: 30), () {
      _hasRecentError = false;
      state = false;
    });
  }

  @override
  void dispose() {
    _errorTimer?.cancel();
    super.dispose();
  }
}
