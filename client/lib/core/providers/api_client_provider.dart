import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:promz/core/services/api/api_client.dart';
import 'package:promz/core/services/client_context_service.dart';
import 'package:promz/core/providers/license_manager_provider.dart';
import 'package:promz_common/promz_common.dart';

/// Provider for the ApiClient service
///
/// This provider returns the ApiClient from ClientContextService to ensure
/// there's only one instance of ApiClient throughout the app.
final apiClientProvider = Provider<ApiClient>((ref) {
  // Get the license manager from the provider (for dependency tracking)
  final licenseManager = ref.watch(licenseManagerProvider);

  // Try to get the ApiClient from ClientContextService
  try {
    final clientContext = ClientContextService();
    return clientContext.apiClient;
  } catch (e) {
    // If ClientContextService is not initialized, log a warning and create a temporary ApiClient
    // This should only happen during testing or early app initialization
    appLog.warning(
      'ClientContextService not initialized in apiClientProvider, creating temporary ApiClient',
      name: 'apiClientProvider',
    );
    return ApiClient(
      licenseManager: licenseManager,
      lazyWebSocketInit: true,
    );
  }
});
