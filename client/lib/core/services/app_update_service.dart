import 'package:flutter/material.dart';
import 'package:upgrader/upgrader.dart';
import 'package:promz_common/promz_common.dart';

/// Custom UpgraderMessages class for Promz app
class PromzUpgraderMessages extends UpgraderMessages {
  @override
  String get title => 'Update Available';

  @override
  String get body => 'A new version of Promz is available. Would you like to update now?';

  @override
  String get buttonTitleIgnore => 'Ignore';

  @override
  String get buttonTitleLater => 'Later';

  @override
  String get buttonTitleUpdate => 'Update';
}

/// Service that manages app update checks and notifications
class AppUpdateService {
  static const String _logName = 'AppUpdateService';

  /// Singleton instance
  static final AppUpdateService _instance = AppUpdateService._internal();

  /// Factory constructor
  factory AppUpdateService() => _instance;

  /// Private constructor
  AppUpdateService._internal();

  /// The upgrader instance
  late final Upgrader _upgrader;

  /// Initialize the update service with custom configurations
  Future<void> initialize() async {
    appLog.debug('Initializing app update service', name: _logName);

    // Create the upgrader instance with proper configuration for version 11.3.2
    _upgrader = Upgrader(
      durationUntilAlertAgain: const Duration(days: 1),
      debugLogging: false,
      messages: PromzUpgraderMessages(),
    );

    // In version 11.3.2, we don't need to manually configure appcast
    // The Upgrader package will handle platform-specific update checks automatically

    appLog.debug('App update service initialized', name: _logName);
  }

  // The Upgrader package automatically handles platform-specific update checks
  // No need for custom appcast URLs in version 11.3.2

  /// Wrap a widget with the upgrader widget to show update notifications
  Widget wrapWithUpgrader(
    Widget child, {
    UpgradeDialogStyle dialogStyle = UpgradeDialogStyle.material,
  }) {
    return UpgradeAlert(
      upgrader: _upgrader,
      dialogStyle: dialogStyle,
      child: child,
    );
  }

  /// Manually check for updates and show the upgrade dialog if needed
  Future<bool> checkForUpdates(BuildContext context) async {
    appLog.debug('Manually checking for updates', name: _logName);

    // The simplest approach is to just show the upgrade alert dialog
    // The Upgrader package will handle checking for updates internally
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return UpgradeAlert(
          upgrader: _upgrader,
          child: Container(), // Empty container as child
        );
      },
    );

    // Return true to indicate that the check was performed
    // The actual update will be handled by the UpgradeAlert widget
    return true;
  }
}
