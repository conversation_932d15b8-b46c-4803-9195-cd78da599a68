import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math' as math;
import 'package:promz_common/config/api_config.dart';
import 'package:promz_common/promz_common.dart';
import 'package:web_socket_channel/io.dart';
import 'package:web_socket_channel/status.dart' as status;
import 'package:web_socket_channel/web_socket_channel.dart';

/// Service for managing WebSocket connections and subscriptions
class WebSocketService {
  static const _logName = 'WebSocketService';

  /// WebSocket channel
  WebSocketChannel? _channel;

  /// Connection status
  bool isConnected = false;

  /// API key for authentication
  final String _apiKey;

  /// Stream controllers for different topics
  final Map<String, StreamController> _topicControllers = {};

  /// Reconnect timer
  Timer? _reconnectTimer;

  /// Connection attempts
  int _connectionAttempts = 0;

  /// Maximum connection attempts before giving up
  static const int _maxConnectionAttempts = 5;

  /// Base delay for exponential backoff (in milliseconds)
  static const int _baseReconnectDelay = 1000;

  /// Maximum reconnect delay (in milliseconds)
  static const int _maxReconnectDelay = 30000;

  /// Random number generator for jitter
  final _random = math.Random();

  /// Track the last connection attempt time to avoid hammering the server
  static DateTime? _lastConnectionAttempt;

  /// Global cooldown period between connection waves in milliseconds
  static const int _globalCooldownPeriod = 60000; // 1 minute cooldown

  /// Flag to track if a global cooldown is active
  static bool _inGlobalCooldown = false;

  /// Connection timeout in seconds
  static const int _connectionTimeoutSeconds = 20; // Increased from 10 seconds

  /// Constructor
  ///
  /// [apiKey] API key from LicenseManagerProvider for authentication
  WebSocketService({required String apiKey}) : _apiKey = apiKey;

  /// Connect to the WebSocket server
  Future<void> connect() async {
    if (isConnected) {
      appLog.debug('Already connected to WebSocket server', name: _logName);
      return;
    }

    // Validate API key
    if (_apiKey.isEmpty) {
      appLog.warning('Cannot connect to WebSocket server: API key is empty', name: _logName);
      return;
    }

    // Check if we're in a global cooldown period after multiple failed attempts
    if (_inGlobalCooldown) {
      appLog.warning('WebSocket connection in global cooldown period, skipping connection attempt',
          name: _logName);
      return;
    }

    // Check if enough time has passed since last connection attempt
    final now = DateTime.now();
    if (_lastConnectionAttempt != null) {
      final timeSinceLastAttempt = now.difference(_lastConnectionAttempt!).inMilliseconds;

      // If we've made attempts too recently, enforce a minimum delay
      if (timeSinceLastAttempt < 500) {
        // At least 500ms between connection attempts
        appLog.debug(
            'Connection attempt too soon after previous attempt (${timeSinceLastAttempt}ms), enforcing delay',
            name: _logName);
        await Future.delayed(Duration(milliseconds: 500 - timeSinceLastAttempt));
      }
    }

    // Update last connection attempt time
    _lastConnectionAttempt = DateTime.now();

    try {
      // Reset connection attempts if this is a new connection attempt
      if (_reconnectTimer == null || !_reconnectTimer!.isActive) {
        _connectionAttempts = 0;
      }

      // Increment connection attempts
      _connectionAttempts++;

      // Get WebSocket URL from API config
      final wsUrl = ApiConfig.webSocketUrl;

      // Add detailed logging
      appLog.debug('WebSocket base URL: $wsUrl', name: _logName);
      appLog.debug('Connection attempt $_connectionAttempts', name: _logName);
      appLog.debug('API key present: ${_apiKey.isNotEmpty}', name: _logName);

      // Ensure the URL has the correct WebSocket protocol
      final String uriString;
      if (wsUrl.startsWith('ws://') || wsUrl.startsWith('wss://')) {
        uriString = '$wsUrl?api_key=$_apiKey';
      } else {
        // Add ws:// prefix if missing
        uriString = 'ws://$wsUrl?api_key=$_apiKey';
        appLog.warning('WebSocket URL did not contain protocol prefix, adding ws:// - $uriString',
            name: _logName);
      }

      // Parse the URI properly
      final uri = Uri.parse(uriString);
      appLog.debug('Connecting to WebSocket server with URI: $uri', name: _logName);
      appLog.debug('URI scheme: ${uri.scheme}', name: _logName);
      appLog.debug('URI host: ${uri.host}', name: _logName);
      appLog.debug('URI port: ${uri.port}', name: _logName);
      appLog.debug('URI path: ${uri.path}', name: _logName);
      appLog.debug('URI query: ${uri.query}', name: _logName);

      // Add proper headers for the WebSocket handshake
      final headers = <String, dynamic>{
        'Connection': 'Upgrade',
        'Upgrade': 'websocket',
        'Sec-WebSocket-Version': '13',
        'Sec-WebSocket-Key': _generateWebSocketKey(),
        'User-Agent': 'Promz WebSocket Client'
      };

      appLog.debug('Using WebSocket connection with headers for proper protocol upgrade',
          name: _logName);

      // Use an asynchronous timeout pattern to catch connection issues more reliably
      final connectionCompleter = Completer<WebSocket>();

      // Create a timeout for the connection attempt
      final timeoutTimer = Timer(const Duration(seconds: _connectionTimeoutSeconds), () {
        if (!connectionCompleter.isCompleted) {
          appLog.warning(
              'WebSocket connection attempt timed out after $_connectionTimeoutSeconds seconds',
              name: _logName);
          connectionCompleter.completeError(TimeoutException(
              'WebSocket connection timed out after $_connectionTimeoutSeconds seconds'));
        }
      });

      // Start the connection attempt
      appLog.debug('Attempting WebSocket connection with $_connectionTimeoutSeconds second timeout',
          name: _logName);

      WebSocket.connect(
        uriString,
        headers: headers,
        protocols: ['promz-websocket-protocol'], // Optional custom protocol
      ).then((socket) {
        // Cancel the timeout timer as we succeeded
        timeoutTimer.cancel();

        if (!connectionCompleter.isCompleted) {
          connectionCompleter.complete(socket);
        }
      }).catchError((error) {
        // Cancel the timeout timer as we got a real error
        timeoutTimer.cancel();

        if (!connectionCompleter.isCompleted) {
          connectionCompleter.completeError(error);
        }
      });

      // Wait for either successful connection or timeout
      final socket = await connectionCompleter.future;

      _channel = IOWebSocketChannel(socket);

      appLog.debug('WebSocket channel created with direct socket connection', name: _logName);

      // Listen for messages
      _channel!.stream.listen(
        _onMessage,
        onError: _onError,
        onDone: _onDone,
        cancelOnError: false,
      );

      isConnected = true;
      _connectionAttempts = 0;

      // Reset global cooldown since we connected successfully
      _inGlobalCooldown = false;

      appLog.info('Connected to WebSocket server', name: _logName);

      // Resubscribe to all active topics
      _resubscribeToTopics();
    } catch (e, stackTrace) {
      // Provide more specific error information for common WebSocket errors
      String errorMessage = 'Error connecting to WebSocket server: ${e.toString()}';

      if (e is SocketException) {
        errorMessage = 'Socket connection failed. Is the server running? Error: ${e.message}';
      } else if (e is HttpException) {
        errorMessage =
            'HTTP handshake failed. This could be a protocol mismatch or server configuration issue. Error: ${e.message}';
        appLog.debug(
            'The server may not be configured to handle WebSocket connections on the specified endpoint.',
            name: _logName);
      } else if (e is WebSocketException) {
        errorMessage = 'WebSocket protocol error. Error: ${e.message}';
      } else if (e is TimeoutException) {
        errorMessage =
            'WebSocket connection timed out. The server might be unreachable or too slow to respond.';
      }

      appLog.warning(errorMessage, name: _logName, error: e, stackTrace: stackTrace);

      // Set connection status to false
      isConnected = false;

      // Schedule reconnect attempt
      _scheduleReconnect();

      // Check if we've reached the maximum attempts and should enter cooldown
      if (_connectionAttempts >= _maxConnectionAttempts) {
        _enterGlobalCooldown();
      }
    }
  }

  /// Generate a random WebSocket key for the handshake
  String _generateWebSocketKey() {
    final random = math.Random();
    final bytes = List<int>.generate(16, (_) => random.nextInt(256));
    return base64Encode(bytes);
  }

  /// Enter a global cooldown period after multiple failed attempts
  void _enterGlobalCooldown() {
    _inGlobalCooldown = true;
    appLog.warning(
        'Entering global WebSocket connection cooldown for ${_globalCooldownPeriod / 1000} seconds',
        name: _logName);

    // Schedule the end of the cooldown period
    Timer(const Duration(milliseconds: _globalCooldownPeriod), () {
      _inGlobalCooldown = false;
      _connectionAttempts = 0;
      appLog.info('WebSocket global cooldown ended, reconnection attempts can resume',
          name: _logName);

      // Try to reconnect after the cooldown if we still need the connection
      if (!isConnected && _topicControllers.isNotEmpty) {
        appLog.info('Attempting reconnection after cooldown period', name: _logName);
        connect();
      }
    });
  }

  /// Resubscribe to all active topics after reconnection
  void _resubscribeToTopics() {
    if (!isConnected || _channel == null) return;

    appLog.debug('Resubscribing to ${_topicControllers.length} active topics', name: _logName);

    // Resubscribe to each topic
    for (final topic in _topicControllers.keys) {
      _sendSubscribeMessage(topic);
    }
  }

  /// Disconnect from the WebSocket server
  void disconnect() {
    appLog.info('Disconnecting from WebSocket server', name: _logName);

    // Stop timers
    _reconnectTimer?.cancel();
    _reconnectTimer = null;

    // Close the channel
    if (_channel != null) {
      _channel!.sink.close(status.goingAway);
      _channel = null;
    }

    isConnected = false;
  }

  /// Subscribe to a topic
  Stream<T> subscribe<T>(String topic, T Function(Map<String, dynamic>) converter) {
    // Create a stream controller for this topic if it doesn't exist
    if (!_topicControllers.containsKey(topic)) {
      _topicControllers[topic] = StreamController<T>.broadcast();

      // Send a subscribe message to the server
      if (isConnected && _channel != null) {
        _sendSubscribeMessage(topic);
      } else {
        // If not connected, attempt to connect now since we have a subscription
        if (!_inGlobalCooldown) {
          connect();
        }
      }
    }

    // Cast the controller to the correct type
    final controller = _topicControllers[topic]! as StreamController<T>;

    // Return the stream
    return controller.stream;
  }

  /// Unsubscribe from a topic
  void unsubscribe(String topic) {
    // Send an unsubscribe message to the server
    if (isConnected && _channel != null) {
      _sendUnsubscribeMessage(topic);
    }

    // Close and remove the stream controller
    if (_topicControllers.containsKey(topic)) {
      _topicControllers[topic]!.close();
      _topicControllers.remove(topic);
    }
  }

  /// Send a subscribe message to the server
  void _sendSubscribeMessage(String topic) {
    if (_channel != null) {
      final message = {
        'type': 'subscribe',
        'topic': topic,
      };
      _channel!.sink.add(jsonEncode(message));
      appLog.debug('Sent subscribe message for topic: $topic', name: _logName);
    } else {
      appLog.warning('Cannot send subscribe message, channel is null', name: _logName);
    }
  }

  /// Send an unsubscribe message to the server
  void _sendUnsubscribeMessage(String topic) {
    if (_channel != null) {
      final message = {
        'type': 'unsubscribe',
        'topic': topic,
      };
      _channel!.sink.add(jsonEncode(message));
      appLog.debug('Sent unsubscribe message for topic: $topic', name: _logName);
    } else {
      appLog.warning('Cannot send unsubscribe message, channel is null', name: _logName);
    }
  }

  /// Send a custom message through the WebSocket channel
  /// This is used for sending custom requests to the server
  void sendCustomMessage(String message) {
    if (_channel != null && isConnected) {
      _channel!.sink.add(message);
      appLog.debug('Sent custom message: $message', name: _logName);
    } else {
      appLog.warning('Cannot send custom message, WebSocket not connected', name: _logName);

      // Try to reconnect if not in cooldown and we have a message to send
      if (!_inGlobalCooldown) {
        connect();
      }
    }
  }

  /// Schedule a reconnect attempt
  void _scheduleReconnect() {
    _reconnectTimer?.cancel();

    if (_connectionAttempts >= _maxConnectionAttempts) {
      appLog.warning(
          'Maximum connection attempts reached ($_maxConnectionAttempts), entering cooldown period',
          name: _logName);
      return;
    }

    // Ensure we have at least one attempt registered before reconnecting
    if (_connectionAttempts <= 0) {
      _connectionAttempts = 1;
    }

    // Calculate delay with exponential backoff, ensuring the shift value is non-negative
    final shiftValue = math.max(0, _connectionAttempts - 1);
    var delay = _baseReconnectDelay * (1 << shiftValue);

    // Apply a maximum delay cap
    delay = math.min(delay, _maxReconnectDelay);

    // Add jitter to prevent thundering herd problem (±20%)
    final jitter = (delay * 0.2 * (_random.nextDouble() * 2 - 1)).toInt();
    delay += jitter;

    appLog.info(
        'Scheduling reconnect in ${delay}ms (attempt $_connectionAttempts of $_maxConnectionAttempts)',
        name: _logName);

    _reconnectTimer = Timer(Duration(milliseconds: delay), _reconnect);
  }

  /// Reconnect to the WebSocket server
  Future<void> _reconnect() async {
    appLog.info(
        'Attempting to reconnect to WebSocket server (attempt $_connectionAttempts of $_maxConnectionAttempts)',
        name: _logName);

    // Disconnect first to ensure clean state
    _disconnect();

    // Then try to connect again
    await connect();
  }

  /// Handle incoming messages
  void _onMessage(dynamic message) {
    try {
      // Parse the message
      final Map<String, dynamic> data = jsonDecode(message);
      final String type = data['type'] as String? ?? '';

      if (type == 'pong') {
        // Pong response, no need to do anything
        return;
      }

      // Log any message other than pong
      appLog.debug('Received WebSocket message: $message', name: _logName);

      // Handle different message types
      switch (type) {
        case 'message':
          _handleTopicMessage(data);
          break;
        case 'response':
          _handleResponseMessage(data);
          break;
        default:
          // Try to handle as a topic message if it has a topic field
          if (data.containsKey('topic')) {
            _handleTopicMessage(data);
          } else {
            appLog.warning('Received message with unknown type: $type', name: _logName);
          }
      }
    } catch (e, stackTrace) {
      appLog.error('Error processing WebSocket message',
          name: _logName, error: e, stackTrace: stackTrace);
    }
  }

  /// Handle a topic message (broadcast from server)
  void _handleTopicMessage(Map<String, dynamic> data) {
    final String topic = data['topic'] as String? ?? '';
    final payload = data['payload'];

    // Forward the message to the appropriate topic controller
    if (_topicControllers.containsKey(topic)) {
      final controller = _topicControllers[topic]!;
      if (controller.isClosed) return;

      if (payload is Map<String, dynamic>) {
        controller.add(payload);
      } else {
        appLog.warning('Received message with invalid payload type: ${payload.runtimeType}',
            name: _logName);
      }
    }
  }

  /// Handle a response message (response to a request)
  void _handleResponseMessage(Map<String, dynamic> data) {
    // Extract topic from response
    final String topic = data['topic'] as String? ?? '';
    final payload = data['payload'];

    // If no topic specified but has a request_id, try to find the topic
    // This is a fallback mechanism

    // Forward the response to the appropriate topic controller
    if (topic.isNotEmpty && _topicControllers.containsKey(topic)) {
      final controller = _topicControllers[topic]!;
      if (controller.isClosed) return;

      if (payload is Map<String, dynamic>) {
        controller.add(payload);
      } else if (payload != null) {
        // Try to wrap non-map payloads in a standard format
        controller.add({'data': payload});
      } else {
        appLog.warning('Received response with null payload for topic: $topic', name: _logName);
      }
    } else if (topic.isNotEmpty) {
      appLog.debug('Received response for topic with no subscribers: $topic', name: _logName);
    } else {
      appLog.warning('Received response with no topic', name: _logName);
    }
  }

  /// Handle WebSocket errors
  void _onError(dynamic error, StackTrace stackTrace) {
    appLog.error('WebSocket error: ${error.toString()}',
        name: _logName, error: error, stackTrace: stackTrace);
    isConnected = false;

    _scheduleReconnect();
  }

  /// Handle WebSocket connection closed
  void _onDone() {
    appLog.info('WebSocket connection closed', name: _logName);
    isConnected = false;

    _scheduleReconnect();
  }

  /// Dispose resources
  void dispose() {
    appLog.info('Disposing WebSocket service', name: _logName);

    // Cancel any pending reconnect timer
    _reconnectTimer?.cancel();

    // Disconnect from WebSocket server if connected
    if (isConnected) {
      appLog.info('Disconnecting from WebSocket server', name: _logName);
      _disconnect();
    }

    // Close all topic controllers
    for (final controller in _topicControllers.values) {
      if (!controller.isClosed) {
        controller.close();
      }
    }
    _topicControllers.clear();
  }

  /// Disconnect from WebSocket server
  void _disconnect() {
    if (_channel != null) {
      try {
        _channel!.sink.close(status.normalClosure, 'Client disconnected');
      } catch (e) {
        // Ignore errors during closing
        appLog.debug('Error closing WebSocket connection: $e', name: _logName);
      }
      _channel = null;
    }

    isConnected = false;
  }

  /// Check if the WebSocket service has a valid API key
  bool hasValidApiKey(String apiKey) {
    return _apiKey == apiKey;
  }

  /// Force a manual reconnection attempt, respecting cooldown periods
  Future<bool> forceReconnect() async {
    // If we're in global cooldown, respect that and don't attempt reconnection
    if (_inGlobalCooldown) {
      appLog.warning('Reconnection attempt rejected due to active cooldown period', name: _logName);
      return false;
    }

    appLog.info('Manual reconnection attempt initiated', name: _logName);

    // Disconnect first
    _disconnect();

    // Reset connection attempts count to avoid triggering cooldown immediately
    // But don't reset to 0 to maintain some backoff if this fails too
    if (_connectionAttempts > 2) {
      _connectionAttempts = 2;
    }

    // Attempt reconnection
    await connect();

    // Return current connection status
    return isConnected;
  }
}
