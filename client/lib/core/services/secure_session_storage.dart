import 'dart:convert';
import 'package:promz/core/services/secure_storage_service.dart';
import 'package:promz_common/promz_common.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Custom storage implementation for Supabase sessions
class SecureSessionStorage extends LocalStorage {
  static const _logName = 'SecureSessionStorage';

  @override
  Future<void> initialize() async {
    try {
      final hasSession = await SecureStorageService.hasSupabaseSession();
      appLog.debug('initialize(): hasSession check result: $hasSession', name: _logName);
    } catch (e, stack) {
      appLog.error('initialize(): Error during init check',
          name: _logName, error: e, stackTrace: stack);
    }
  }

  @override
  Future<void> persistSession(String persistSessionStr) async {
    try {
      // Validate that the session string is valid JSON before persisting
      try {
        final sessionJson = jsonDecode(persistSessionStr);
        if (sessionJson['access_token'] == null) {
          appLog.warning('persistSession(): Session JSON missing access_token', name: _logName);
        }
      } catch (e) {
        appLog.error('persistSession(): Invalid session JSON format', name: _logName, error: e);
        // Still continue to save as Supabase might be providing a valid format we don't recognize
      }

      await SecureStorageService.saveSupabaseSession(persistSessionStr);
    } catch (e, stack) {
      appLog.error('persistSession(): Error persisting session',
          name: _logName, error: e, stackTrace: stack);
      rethrow;
    }
  }

  @override
  Future<void> removePersistedSession() async {
    try {
      await SecureStorageService.deleteSupabaseSession();
    } catch (e, stack) {
      appLog.error('removePersistedSession(): Error removing session',
          name: _logName, error: e, stackTrace: stack);
      rethrow;
    }
  }

  @override
  Future<bool> hasAccessToken() async {
    try {
      final result = await SecureStorageService.hasSupabaseSession();
      return result;
    } catch (e, stack) {
      appLog.error('hasAccessToken(): Error checking session existence',
          name: _logName, error: e, stackTrace: stack);
      return false;
    }
  }

  /// Get the session string (not just the access token); This is critical for session restoration
  @override
  Future<String?> accessToken() async {
    try {
      // IMPORTANT: Return the entire session string, not just the token
      // This is what fixes the session restoration issue
      final sessionStr = await SecureStorageService.getSupabaseSession();
      if (sessionStr == null || sessionStr.isEmpty) {
        return null;
      }

      // Validate the session JSON but return the full string
      try {
        final sessionJson = jsonDecode(sessionStr);
        if (sessionJson['access_token'] == null) {
          appLog.warning('accessToken(): Session JSON missing access_token', name: _logName);
          return null;
        }
        appLog.debug('accessToken(): Valid session JSON found', name: _logName);
      } catch (e) {
        appLog.error('accessToken(): Invalid session JSON format', name: _logName, error: e);
        await removePersistedSession();
        return null;
      }

      return sessionStr; // Return the full session string, not just the token
    } catch (e, stack) {
      appLog.error('accessToken(): Error retrieving session',
          name: _logName, error: e, stackTrace: stack);
      return null;
    }
  }

  // These are standard localStorage methods that Supabase uses internally
  Future<String?> getItem(String key) async {
    try {
      if (key == supabasePersistSessionKey) {
        // Use the key Supabase defines
        return await SecureStorageService.getSupabaseSession();
      } else {
        appLog.debug('getItem(): Called with key: $key', name: _logName);
        return await SecureStorageService.getValue(key); // Delegate generic gets
      }
    } catch (e) {
      appLog.error('getItem(): Error retrieving item with key: $key', name: _logName, error: e);
      return null;
    }
  }

  Future<void> setItem(String key, String value) async {
    try {
      if (key == supabasePersistSessionKey) {
        // Use the key Supabase defines
        await persistSession(value); // Use our existing persist logic
      } else {
        appLog.debug('setItem(): Saving item with key: $key', name: _logName);
        await SecureStorageService.saveValue(key, value); // Delegate generic sets
      }
    } catch (e, stack) {
      appLog.error('setItem(): Error saving item with key: $key',
          name: _logName, error: e, stackTrace: stack);
      rethrow;
    }
  }

  Future<void> removeItem(String key) async {
    try {
      if (key == supabasePersistSessionKey) {
        // Use the key Supabase defines
        await removePersistedSession(); // Use our existing remove logic
      } else {
        appLog.debug('removeItem(): Removing item with key: $key', name: _logName);
        await SecureStorageService.deleteValue(key); // Delegate generic deletes
      }
    } catch (e) {
      appLog.error('removeItem(): Error removing item with key: $key', name: _logName, error: e);
      rethrow;
    }
  }

  Future<void> clear() async {
    try {
      // Be careful implementing this. Usually, we only want to delete the session.
      await removePersistedSession();
      appLog.debug('clear(): Session removed successfully', name: _logName);
    } catch (e) {
      appLog.error('clear(): Error clearing session data', name: _logName, error: e);
      rethrow;
    }
  }
}
