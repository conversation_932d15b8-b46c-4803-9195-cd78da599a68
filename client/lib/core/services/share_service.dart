import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:share_plus/share_plus.dart';
import 'package:promz_common/promz_common.dart';
import 'package:base_codecs/base_codecs.dart';
import 'dart:convert';
import 'dart:typed_data';

final shareServiceProvider = Provider<ShareService>((ref) {
  return ShareService();
});

/// Service for sharing content from the app to external applications
class ShareService {
  static const _logName = 'ShareService';

  ShareService();

  /// Share a prompt or other text content with external applications
  ///
  /// [content] is the text to be shared
  /// [title] is an optional subject line (used in email sharing)
  Future<void> sharePrompt(String content, {String? title}) async {
    appLog.debug('Sharing content with external app', name: _logName);
    await Share.share(content, subject: title);
  }

  /// Share a prompt with its shareable URL
  ///
  /// [promptId] is the UUID of the prompt
  /// [content] is the text content of the prompt
  /// [title] is an optional subject line (used in email sharing)
  Future<void> sharePromptWithUrl(String promptId, String content, {String? title}) async {
    // Generate a Base58 short ID for sharing
    final encoded = base58BitcoinEncode(Uint8List.fromList(utf8.encode(promptId)));
    appLog.debug('Generated short ID for sharing: $encoded', name: _logName);
    final shareableUrl = 'https://promz.ai/p/$encoded';
    final shareText = '$content\n\nCheck out this prompt at: $shareableUrl';

    appLog.debug('Sharing prompt with URL: $shareableUrl', name: _logName);
    await Share.share(shareText, subject: title);
  }

  /// Share a URL with external applications
  Future<void> shareUrl(String url, {String? title}) async {
    appLog.debug('Sharing URL with external app: $url', name: _logName);
    await Share.share(url, subject: title);
  }

  /// Share multiple items with external applications
  /// Note: This is limited on some platforms and may fall back to sharing only the text
  Future<void> shareMultiple(List<String> items, {String? subject}) async {
    appLog.debug('Sharing multiple items with external app', name: _logName);
    await Share.shareXFiles(
      items.map((item) => XFile(item)).toList(),
      subject: subject,
    );
  }
}
