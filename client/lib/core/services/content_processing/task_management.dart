part of '../content_processing_service.dart';

/// Extension that adds task management capabilities to ContentProcessingService
extension TaskManagement on ContentProcessingService {
  static const _logName = 'ContentProcessingService.TaskManagement';

  /// Create a new processing task from any content (file path, URL, or text)
  Future<ContentProcessingTask> createFileTask(String content, {String source = 'internal'}) async {
    appLog.debug('Creating content task for: ${truncateForLogging(content)}', name: _logName);

    // Check for duplicates first
    final existingTask = await _taskManager.findDuplicateTask(content);
    if (existingTask != null) {
      appLog.info('Found duplicate task, reusing: ${existingTask.id}', name: _logName);
      return existingTask;
    }

    // Create and add the task
    final task = await _taskManager.addTask(content, source: source);
    return task;
  }

  /// Process a task and handle its lifecycle
  Future<InputSource?> processTask(ContentProcessingTask task) async {
    appLog.info('Processing task: ${task.fileName} (ID: ${task.id})', name: _logName);

    try {
      // Update task status to processing
      final updatedTask =
          await _taskManager.updateTaskStatus(task.id, ContentProcessingTaskStatus.processing);

      if (updatedTask == null) {
        appLog.error('Failed to update task status, task not found: ${task.id}', name: _logName);
        return null;
      }

      _processingStatusController.add(ContentProcessingStatus.started);

      // Process the content using the main processInput method
      // This will handle any type of content (file, URL, text)
      try {
        // Update progress to indicate processing started
        await _taskManager.updateTaskProgress(task.id, 0.1);

        // Use the main processInput method which can handle any content type
        final result = await processInput(task.filePath);

        if (result != null) {
          // Mark task as completed
          final attachmentId = result.processingResult?.jobId ?? '';
          await _taskManager.completeTask(task.id, attachmentId);

          // Update processing status
          _processingStatusController.add(ContentProcessingStatus.completed);
        } else {
          // Processing failed
          await _taskManager.failTask(task.id, 'Processing failed');
          _processingStatusController.add(ContentProcessingStatus.error);
        }

        return result;
      } catch (e) {
        // If processInput fails, rethrow to be handled by the outer catch block
        rethrow;
      }
    } catch (e, stack) {
      await _handleTaskError(task, e, stack);
      return null;
    }
  }

  /// Handle task errors and update task status accordingly
  Future<void> _handleTaskError(
      ContentProcessingTask task, dynamic error, StackTrace? stack) async {
    appLog.debug('Handling error for task ${task.id}', name: _logName);

    // Handle specific error types
    if (error is FileSizeLimitExceededException) {
      await _handleFileSizeLimitError(task, error);
    } else if (_isAuthenticationError(error)) {
      // Update task status to pending for retry after auth
      await _taskManager.updateTask(
        task.id,
        (t) => t.copyWith(
          status: ContentProcessingTaskStatus.pending,
          errorMessage: 'Authentication required',
        ),
      );

      _handleAuthenticationRequired();
    } else if (error is SocketException) {
      appLog.error('Network error while processing file: ${error.message}',
          name: _logName, error: error, stackTrace: stack);

      _processingStatusController.add(ContentProcessingStatus.networkError);
      await _taskManager.failTask(task.id, 'Network error: ${error.message}');
    } else if (error is TimeoutException) {
      appLog.error('Timeout while processing file. This may happen with large files.',
          name: _logName, error: error, stackTrace: stack);

      _processingStatusController.add(ContentProcessingStatus.timeout);
      await _taskManager.failTask(task.id, 'Processing timed out');
    } else if (error is FileSystemException) {
      appLog.error('File system error: ${error.message}',
          name: _logName, error: error, stackTrace: stack);

      _processingStatusController.add(ContentProcessingStatus.error);
      await _taskManager.failTask(task.id, 'File error: ${error.message}');
    } else {
      appLog.error('Error processing file', name: _logName, error: error, stackTrace: stack);
      _processingStatusController.add(ContentProcessingStatus.error);
      await _taskManager.failTask(task.id, 'Processing error: ${error.toString()}');
    }
  }

  /// Helper method to handle file size limit errors
  Future<void> _handleFileSizeLimitError(ContentProcessingTask task, Exception error) async {
    appLog.warning('File size limit exceeded for task ${task.id}', name: _logName, error: error);

    _processingStatusController.add(ContentProcessingStatus.fileSizeLimitExceeded);

    await _taskManager.updateTask(
      task.id,
      (t) => t.copyWith(
        status: ContentProcessingTaskStatus.sizeLimitExceeded,
        errorMessage: error.toString(),
      ),
    );

    // Emit the event for UI components
    _fileSizeLimitController.add(error);
  }

  /// Helper method to check if an error is authentication-related
  bool _isAuthenticationError(dynamic error) {
    final errorMessage = error.toString().toLowerCase();
    return errorMessage.contains('license required') ||
        errorMessage.contains('authentication required') ||
        errorMessage.contains('sign in');
  }

  /// Helper method to handle authentication errors consistently
  void _handleAuthenticationRequired() {
    appLog.warning('Authentication required for processing content', name: _logName);
    _processingStatusController.add(ContentProcessingStatus.authRequired);
    _authRequiredController.add(null); // Notify listeners that auth is required
  }

  /// Process any pending tasks
  Future<List<InputSource>> processPendingTasks() async {
    appLog.info('Processing pending tasks', name: _logName);

    // Get pending tasks
    final pendingTasks = await _taskManager.processPendingTasks();

    if (pendingTasks.isEmpty) {
      appLog.info('No pending tasks to process', name: _logName);
      return [];
    }

    appLog.info('Processing ${pendingTasks.length} pending tasks', name: _logName);

    final results = <InputSource>[];

    // Process each task
    for (final task in pendingTasks) {
      final result = await processTask(task);
      if (result != null) {
        results.add(result);
      }
    }

    return results;
  }

  /// Dispose task management resources
  void disposeTaskManagement() {
    _processingStatusController.close();
    _authRequiredController.close();
    _fileSizeLimitController.close();
  }
}
