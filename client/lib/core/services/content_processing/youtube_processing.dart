part of '../content_processing_service.dart';

extension YoutubeProcessing on ContentProcessingService {
  static const _logName = 'ContentProcessingService.YoutubeProcessing';

  /// Process YouTube video content from URL or text with URL
  Future<Map<String, dynamic>?> _processYouTubeVideo(String input,
      {String? extractedYouTubeUrl}) async {
    appLog.debug('Processing YouTube video from input', name: _logName);

    try {
      // Extract YouTube URL if not provided
      final url = extractedYouTubeUrl ?? extractFirstUrl(input);
      if (url == null) {
        appLog.debug('No URL found in input, cannot process as YouTube video', name: _logName);
        return null;
      }

      // Process the URL using our consolidated approach
      final processedUrl = await UrlResolver.processUrl(
        url,
        checkYouTube: true,
        checkNews: false, // We only care about YouTube URLs here
      );

      // Validate that this is indeed a YouTube URL
      if (!processedUrl.isYouTubeUrl) {
        appLog.debug('URL is not a YouTube URL: $url', name: _logName);
        return null;
      }

      // Use the best URL for content extraction
      final cleanUrl = processedUrl.bestUrl;
      appLog.debug('Using URL for YouTube processing: $cleanUrl', name: _logName);

      // Check if YouTube service is available
      final youtubeService = _youtubeService;

      // Fetch video metadata using the YouTubeService with the clean URL
      final processingResult = await youtubeService.fetchVideoMetadata(cleanUrl);
      final youTubeMetadata = processingResult.youtubeMetadata;

      // Check if metadata was successfully fetched
      if (youTubeMetadata.videoId.isEmpty) {
        appLog.debug('Failed to fetch YouTube metadata from URL: $url', name: _logName);
        return null;
      }

      // Create a unique attachment ID
      final attachmentId = 'youtube_${DateTime.now().millisecondsSinceEpoch}';
      final contentHash = HashHelper.calculateStringHash(youTubeMetadata.videoId);

      // Add job id and status to the result
      processingResult
        ..jobId = attachmentId
        ..status = UploadStatus.UPLOAD_STATUS_COMPLETED;

      // Update fields as needed
      youTubeMetadata
        ..url = cleanUrl
        ..title = youTubeMetadata.title.isNotEmpty ? youTubeMetadata.title : 'YouTube Video';

      // Register with the attachment registry using protobuf-based method
      _attachmentRegistry.registerAttachment(
        id: attachmentId,
        result: processingResult,
      );

      // Register with entity detection service (still using legacy format)
      _entityDetectionService.registerEntity(
        id: youTubeMetadata.videoId,
        text: youTubeMetadata.title,
        type: 'youtube',
        processingResult: processingResult,
      );

      // Create a map with the necessary data instead of InputContentInfo
      final contentInfo = {
        'type': InputType.url.name,
        'filePath': input,
        'fileName': youTubeMetadata.title,
        'mimeType': 'video/youtube',
        'content': processingResult.content,
        'contentHash': contentHash,
        'sourceType': InputSourceType.youtubeVideo.name,
        'attachmentId': attachmentId,
        'processingResult': processingResult,
      };

      // Create and emit a source processed event
      final source = InputSource(
        type: InputSourceType.youtubeVideo,
        filePath: input,
        fileName: youTubeMetadata.title,
        mimeType: 'video/youtube',
        content: processingResult.content,
        contentHash: contentHash,
        processingResult: processingResult,
      );

      // Notify listeners that a source has been processed
      notifySourceProcessed(source);

      return contentInfo;
    } catch (e, stack) {
      appLog.error('Error processing YouTube video', name: _logName, error: e, stackTrace: stack);
      return null;
    }
  }
}
