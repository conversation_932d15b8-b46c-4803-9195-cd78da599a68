part of '../content_processing_service.dart';

/// Extension that adds variable processing capabilities to ContentProcessingService
extension VariableProcessing on ContentProcessingService {
  static const _logName = 'ContentProcessingService.VariableProcessing';

  /// Get variable package for a specific entity type
  Map<String, dynamic> getVariablePackage(EntityType entityType) {
    final attachment = getAttachmentByType(entityType.name);
    if (attachment == null) {
      appLog.warning('No attachment found for entity type: ${entityType.name}', name: _logName);
      return {};
    }
    switch (entityType) {
      case EntityType.news:
        return getNewsVariables(attachment);
      case EntityType.youtubeVideo:
        return getYoutubeVariables(attachment);
      default:
        return {};
    }
  }

  /// Get variable package for news
  Map<String, dynamic> getNewsVariables(ProcessingResult processingResult) {
    final variables = <String, dynamic>{};
    final article = processingResult.articleMetadata;

    variables['NEWS:ARTICLE_URL'] = article.finalUrl;
    variables['NEWS:ARTICLE_CONTENTS'] = processingResult.content;
    variables['NEWS:ARTICLE_TITLE'] = article.title;
    variables['NEWS:ARTICLE_AUTHOR'] = article.author;
    variables['NEWS:ARTICLE_SITE_NAME'] = article.siteName;
    return variables;
  }

  /// Get variable package for youtube
  Map<String, dynamic> getYoutubeVariables(ProcessingResult processingResult) {
    final variables = <String, dynamic>{};
    final youtube = processingResult.youtubeMetadata;
    variables['MEDIA:YOUTUBE_URL'] = YouTubeUrlHandling.getWatchUrl(youtube.videoId);
    variables['MEDIA:YOUTUBE_TITLE'] = youtube.title;
    variables['MEDIA:YOUTUBE_DESCRIPTION'] = youtube.description;
    variables['MEDIA:YOUTUBE_THUMBNAIL'] = youtube.thumbnailUrl;
    variables['MEDIA:YOUTUBE_CHANNEL'] = youtube.channelName;
    return variables;
  }
}
