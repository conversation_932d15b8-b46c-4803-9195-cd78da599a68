part of '../content_processing_service.dart';

/// Types of input that can be processed
enum InputType {
  file,
  url,
  text,
}

/// Information about the input type before processing
class InputTypeInfo {
  final InputType type;
  final String originalInput;
  final String? fileName;
  final String? mimeType;
  final String? content;
  final ProcessingResult? processingResult;

  InputTypeInfo({
    required this.type,
    required this.originalInput,
    this.fileName,
    this.mimeType,
    this.content,
    this.processingResult,
  });
}

extension InputProcessing on ContentProcessingService {
  static const _logName = 'ContentProcessingService';

  /// Categorize input as file, URL, or text
  Future<InputTypeInfo> _categorizeInput(String input) async {
    // First check if the input contains a URL but isn't itself a URL
    // This is common for shared content like "Title + URL"
    final hasUrl = containsUrl(input);
    final exactlyUrl = isExactlyUrl(input);

    appLog.debug('Input contains URL: $hasUrl, is exactly URL: $exactlyUrl', name: _logName);

    // If input contains a URL but isn't exactly a URL, use UrlExtractor to analyze it
    if (hasUrl && !exactlyUrl) {
      appLog.debug('Input contains URL and is not exactly a URL, using UrlExtractor',
          name: _logName);

      // Use UrlExtractor to analyze the content
      final processedUrlInfo = await UrlExtractor.extractUrl(input);

      if (processedUrlInfo != null) {
        // Create appropriate InputTypeInfo based on URL type
        appLog.debug('URL extracted: ${processedUrlInfo.bestUrl}', name: _logName);

        final firstLine = input.split('\n').first.trim();
        final fileName = firstLine.length > 100 ? '${firstLine.substring(0, 100)}...' : firstLine;

        // Store the extracted URL in ProcessingResult
        final processingResult = ProcessingResult()..sourceUrl = processedUrlInfo.bestUrl;

        return InputTypeInfo(
          type: InputType.url,
          originalInput: input,
          fileName: fileName,
          mimeType: 'text/html',
          content: input,
          processingResult: processingResult,
        );
      }
    }

    // Check if input is a direct URL
    bool isUrl = isValidUrl(input);
    if (isUrl) {
      appLog.debug('Input is a valid URL', name: _logName);
      return InputTypeInfo(
        type: InputType.url,
        originalInput: input,
      );
    }

    // Check if input is a file path
    bool isFilePath = false;
    try {
      final file = File(input);
      isFilePath = await file.exists();
    } catch (e) {
      isFilePath = false;
    }

    if (isFilePath) {
      appLog.debug('Input is a file path', name: _logName);
      final fileName = input.split(RegExp(r'[/\\]')).last;
      final mimeType = _fileProcessingService.getMimeType(fileName);

      return InputTypeInfo(
        type: InputType.file,
        originalInput: input,
        fileName: fileName,
        mimeType: mimeType,
      );
    } else {
      // It's direct text content
      appLog.debug('Input is text content', name: _logName);
      final firstLine = input.split('\n').first.trim();
      final fileName = firstLine.length > 100 ? '${firstLine.substring(0, 100)}...' : firstLine;

      return InputTypeInfo(
        type: InputType.text,
        originalInput: input,
        fileName: fileName,
        mimeType: 'text/plain',
        content: input,
      );
    }
  }
}
