part of '../content_processing_service.dart';

extension FileProcessing on ContentProcessingService {
  static const _logName = 'ContentProcessingService';

  /// Process file input
  /// All files are processed server-side to ensure content is available for prompt execution
  Future<Map<String, dynamic>> _processFile(
      String filePath, String fileName, String mimeType) async {
    appLog.debug('Processing file: $filePath', name: _logName);

    final file = File(filePath);

    // Ensure file exists
    if (!await file.exists()) {
      appLog.error('File does not exist: $filePath', name: _logName);
      return {
        'type': InputType.file.name,
        'filePath': filePath,
        'fileName': fileName,
        'mimeType': mimeType,
        'content': 'File not found',
        'contentHash': HashHelper.calculateStringHash(filePath),
        'sourceType': InputSourceType.file.name,
        'isDuplicate': false,
        'processingResult': ProcessingResult()
          ..errorMessage = 'File not found'
          ..filePath = filePath,
      };
    }

    // Validate file size before processing
    try {
      await validateFileSize(file);
    } catch (e) {
      appLog.warning('File size validation failed: ${e.toString()}', name: _logName);
      // Let the exception propagate to be handled by the caller
      rethrow;
    }

    // Process all files using server-side processing
    try {
      appLog.debug('Using server-side processing for file: $filePath', name: _logName);

      // For ZIP files, use the specialized ZIP processing
      final isZipFile = mimeType.contains('zip') || fileName.toLowerCase().endsWith('.zip');
      if (isZipFile) {
        return await _processZipFile(file, fileName);
      }

      // For all other files, use generic server-side processing
      return await _processGenericFile(file, fileName, mimeType);
    } catch (e, stack) {
      appLog.error('Error in server-side file processing',
          name: _logName, error: e, stackTrace: stack);

      // Return error info instead of falling back to local processing
      return {
        'type': InputType.file.name,
        'filePath': filePath,
        'fileName': fileName,
        'mimeType': mimeType,
        'content': 'Error processing file: ${e.toString()}',
        'contentHash': HashHelper.calculateStringHash(filePath),
        'sourceType': InputSourceType.file.name,
        'isDuplicate': false,
        'processingResult': ProcessingResult()
          ..errorMessage = e.toString()
          ..filePath = filePath
          ..fileName = fileName
          ..mimeType = mimeType,
      };
    }
  }

  /// Process a generic file using server-side processing. This handles all non-ZIP files
  Future<Map<String, dynamic>> _processGenericFile(
      File file, String fileName, String mimeType) async {
    appLog.debug('Processing generic file with server: ${file.path}', name: _logName);

    try {
      // Check license tier before proceeding
      if (!_licenseManagerService.isLicenseValid()) {
        throw Exception('License required: Please sign in to process files');
      }

      // Prepare file for upload
      final fileBytes = await file.readAsBytes();
      final fileSize = fileBytes.length;

      appLog.debug('Uploading file to server, size: $fileSize bytes', name: _logName);

      // Upload file to server using FileUploadService
      final uploadResponse = await _fileUploadService.uploadFile(
        file,
        metadata: {
          'processingType': 'content_extraction',
        },
      );

      // uploadFile now returns the jobId directly (String) and throws on error.
      // A successful return means the job was accepted by the server.
      final jobId = uploadResponse;
      appLog.debug('File uploaded successfully, job ID: $jobId', name: _logName);

      // Generate a placeholder attachment ID
      final attachmentId = HashHelper.calculateStringHash('${file.path}_$jobId');

      // Create a ProcessingResult for the initial server attachment
      final processingResult = ProcessingResult()
        ..jobId = jobId
        ..contentType = 'file'
        ..content = 'Processing file content...'
        ..status = UploadStatus.UPLOAD_STATUS_PROCESSING;

      // Add metadata
      processingResult.fileName = fileName;
      processingResult.mimeType = mimeType;
      processingResult.processingType = 'content_extraction';
      processingResult.filePath = file.path;

      if (mimeType.contains('pdf')) {
        // Create file metadata for PDF
        final fileMetadata = FileMetadata();
        fileMetadata.sizeBytes = fileBytes.length as Int64;
        processingResult.fileMetadata = fileMetadata;
      }

      // Register a server attachment that will be updated when processing completes
      _attachmentRegistry.registerServerAttachment(
        id: attachmentId,
        initialResult: processingResult,
        client: _fileProcessingClient,
      );

      // Return a placeholder content info that will be updated when processing completes
      return {
        'type': InputType.file.name,
        'filePath': file.path,
        'fileName': fileName,
        'mimeType': mimeType,
        'content': 'Processing file content...',
        'contentHash': HashHelper.calculateStringHash(file.path),
        'sourceType': InputSourceType.file.name,
        'attachmentId': attachmentId,
        'processingResult': processingResult,
      };
    } catch (e, stack) {
      appLog.error('Error processing file with server',
          name: _logName, error: e, stackTrace: stack);
      rethrow;
    }
  }

  /// Process a ZIP file using server-side processing. This is a generic method for handling any ZIP file.
  /// File size validation is handled centrally before upload.
  Future<Map<String, dynamic>> _processZipFile(File file, String fileName) async {
    appLog.debug('Processing ZIP file: ${file.path}', name: _logName);

    // Get the file upload service
    final fileUploadService = _fileUploadService;

    // Get license tier
    String licenseTier = 'free';
    final currentLicense = _licenseManagerService.currentLicense;
    if (currentLicense != null) {
      licenseTier = currentLicense.licenseType.toString().split('.').last;
    }

    // Prepare metadata for the upload
    final metadata = <String, dynamic>{
      'contentType': 'zip',
      'fileName': fileName,
      'fileHash': HashHelper.calculateStringHash(file.path),
      'processingType': 'content_detection', // Server should detect content type
    };

    try {
      // Note: File size validation is now handled by validateFileSize method in _processFile
      // Upload the file to the server
      final String uploadResponse = await fileUploadService.uploadFile(
        file,
        metadata: metadata,
        licenseTier: licenseTier,
      );

      // The response *is* the Job ID
      final jobId = uploadResponse;
      appLog.debug('File uploaded successfully, job ID: $jobId', name: _logName);

      // Generate a placeholder attachment ID
      const placeholderContent = 'Processing ZIP content...';
      final hash = HashHelper.calculateStringHash(file.path);
      final attachmentId = '${hash}_$jobId';

      // Create a ProcessingResult for the initial server attachment
      final processingResult = ProcessingResult()
        ..jobId = jobId
        ..contentType = 'zip'
        ..content = placeholderContent
        ..status = UploadStatus.UPLOAD_STATUS_PROCESSING
        ..filePath = file.path
        ..source = 'ZIP File'
        ..appName = 'File Upload'
        ..isZipContent = true
        ..sourceType = 'zip'
        ..title = 'ZIP File'
        ..processingType = 'content_detection'
        ..fileName = fileName;

      // Create ZIP metadata
      final zipMetadata = ZipMetadata();
      // We'll set more detailed ZIP metadata when processing completes
      processingResult.zipMetadata = zipMetadata;

      // Register with the new protobuf-based method
      _attachmentRegistry.registerServerAttachment(
        id: attachmentId,
        initialResult: processingResult,
        client: _fileProcessingClient,
      );

      // Create and emit a source processed event
      final source = InputSource(
        type: InputSourceType.zip,
        filePath: file.path,
        fileName: fileName,
        mimeType: 'application/zip',
        content: processingResult.content,
        contentHash: hash,
        processingResult: processingResult,
      );

      // Notify listeners that a source has been processed
      notifySourceProcessed(source);

      // Return a placeholder map that will be updated when processing completes
      return {
        'type': InputType.file.name,
        'filePath': file.path,
        'fileName': fileName,
        'mimeType': 'application/zip',
        'content': placeholderContent,
        'contentHash': HashHelper.calculateStringHash(placeholderContent),
        'sourceType': InputSourceType.zip.name,
        'attachmentId': attachmentId,
        'processingResult': processingResult,
      };
    } catch (e, stack) {
      appLog.error('Error uploading ZIP file for processing',
          name: _logName, error: e, stackTrace: stack);
      rethrow;
    }
  }
}
