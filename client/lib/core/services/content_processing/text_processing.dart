part of '../content_processing_service.dart';

/// Implementation of text processing functionality
extension TextProcessing on ContentProcessingService {
  static const _logName = 'ContentProcessingService.TextProcessing';

  /// Process text input - extract relevant information, detect entities, etc.
  /// Returns a Map with extracted data instead of creating an InputSource directly
  Future<Map<String, dynamic>> _processText(String text, String fileName) async {
    appLog.debug('Starting _processText with: ${truncateForLogging(text)}',
        name: '$_logName.TextProcessing');

    try {
      // Create a unique content hash for de-duplication
      final contentHash = HashHelper.calculateStringHash(text);
      appLog.debug('Generated content hash: $contentHash', name: '$_logName.TextProcessing');

      // Check for duplicates before doing entity detection
      bool isDuplicate = false;

      // First check the centralized registry for recent duplicates
      if (DuplicateDetectionRegistry.instance.hashExists(contentHash)) {
        appLog.debug('Recently processed this exact content, checking for existing source',
            name: '$_logName.TextProcessing');
        isDuplicate = true;
      }

      // Only detect entities if this is not a duplicate
      if (!isDuplicate) {
        // Detect entities in the text
        await _entityDetectionService.detectEntities(text);
        appLog.debug('Entity detection completed', name: '$_logName.TextProcessing');
      } else {
        appLog.debug('Skipping entity detection for duplicate content',
            name: '$_logName.TextProcessing');
      }

      // Create a ProcessingResult for the content
      final processingResult = ProcessingResult()
        ..content = text
        ..contentType = InputType.text.name
        ..timestamp = Int64(DateTime.now().millisecondsSinceEpoch);

      // Generate attachment ID
      final attachmentId = 'text_${DateTime.now().millisecondsSinceEpoch}';
      processingResult.jobId = attachmentId;

      // Only register with attachment registry if not a duplicate
      if (!isDuplicate) {
        _attachmentRegistry.registerAttachment(
          id: attachmentId,
          result: processingResult,
        );
        appLog.debug('Registered attachment with id: $attachmentId',
            name: '$_logName.TextProcessing');
      } else {
        appLog.debug('Skipping attachment registration for duplicate content',
            name: '$_logName.TextProcessing');
      }

      // Return extracted data as a Map
      return {
        'type': InputType.text,
        'originalInput': text,
        'fileName': fileName,
        'mimeType': 'text/plain',
        'content': text,
        'contentHash': contentHash,
        'sourceType': InputSourceType.text,
        'processingResult': processingResult,
        'attachmentId': attachmentId,
      };
    } catch (e, stack) {
      appLog.error('Error in _processText',
          name: '$_logName.TextProcessing', error: e, stackTrace: stack);

      // Return a basic result with error information
      final contentHash = HashHelper.calculateStringHash(text);

      final processingResult = ProcessingResult()
        ..content = text
        ..contentType = InputType.text.name
        ..timestamp = Int64(DateTime.now().millisecondsSinceEpoch)
        ..errorMessage = e.toString();

      // Return extracted data as a Map even in error case
      return {
        'type': InputType.text,
        'originalInput': text,
        'fileName': fileName,
        'mimeType': 'text/plain',
        'content': text,
        'contentHash': contentHash,
        'sourceType': InputSourceType.text,
        'processingResult': processingResult,
      };
    }
  }

  /// Helper method to truncate text for logging purposes
  String truncateForLogging(String text) {
    const maxLength = 100;
    if (text.length <= maxLength) return text;
    return '${text.substring(0, maxLength)}...';
  }
}
