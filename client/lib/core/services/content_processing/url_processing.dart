part of '../content_processing_service.dart';

extension UrlProcessing on ContentProcessingService {
  static const _logName = 'ContentProcessingService';

  /// Process URL input using NewsArticleService
  /// This method handles general URLs that may be news articles or other web content
  Future<Map<String, dynamic>> _processUrlInput(String url) async {
    appLog.debug('Processing URL: $url', name: _logName);

    try {
      final processingResult =
          await _newsArticleService.fetchAndRegisterArticleWithMetadata(url: url);

      // Create a more comprehensive metadata map with fallbacks for essential fields
      final title = processingResult.articleMetadata.title.isNotEmpty
          ? processingResult.articleMetadata.title
          : 'Article from ${Uri.parse(url).host}';
      final content = processingResult.content;
      final contentHash = HashHelper.calculateStringHash(content);

      // Determine if this is a news article based on available metadata
      final bool isNewsArticle = processingResult.articleMetadata.hasSiteName() ||
          processingResult.articleMetadata.hasAuthor();

      return {
        'type': InputType.url.name,
        'filePath': url,
        'fileName': title,
        'mimeType': 'text/html',
        'content': content,
        'contentHash': contentHash,
        'sourceType': (isNewsArticle ? InputSourceType.news : InputSourceType.text).name,
        'attachmentId': processingResult.jobId,
        'processingResult': processingResult,
      };
    } catch (e, stack) {
      appLog.error('Error processing URL input', name: _logName, error: e, stackTrace: stack);
      // Instead of rethrowing, return a minimal content info with the error message
      return {
        'type': InputType.url.name,
        'filePath': url,
        'fileName': 'Failed to process URL',
        'mimeType': 'text/plain',
        'content': 'Error processing URL: $url\n\nError: ${e.toString()}',
        'contentHash': HashHelper.calculateStringHash(url),
        'sourceType': InputSourceType.text.name,
        'processingResult': ProcessingResult()
          ..errorMessage = e.toString()
          ..sourceUrl = url,
      };
    }
  }
}
