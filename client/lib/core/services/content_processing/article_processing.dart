part of '../content_processing_service.dart';

extension ArticleProcessing on ContentProcessingService {
  static const _logName = 'ContentProcessingService.ArticleProcessing';

  /// Process news article from URL or text content
  /// Returns information about the processed article
  Future<Map<String, dynamic>> _processNewsArticle(String input, {String? extractedUrl}) async {
    appLog.debug('Processing news article from input', name: _logName);

    try {
      // Extract URL using UrlExtractor if not provided
      String cleanUrl;

      if (extractedUrl != null) {
        // URL was already extracted and provided
        cleanUrl = extractedUrl;
      } else {
        // Use the dedicated UrlExtractor for comprehensive URL extraction
        final processedUrlInfo = await UrlExtractor.extractUrl(input);

        if (processedUrlInfo == null) {
          appLog.debug('No URL found in input using UrlExtractor, processing as text',
              name: _logName);
          return await _processText(input, 'Text Content');
        }

        // Use the best URL provided by the processor (handles redirects, embeds, etc.)
        cleanUrl = processedUrlInfo.bestUrl;
        appLog.debug('Using best URL from UrlExtractor: $cleanUrl (from input: $input)',
            name: _logName);
      }

      // Fetch article content directly using the clean URL (NewsArticleService expects direct URLs)
      final processingResult = await _newsArticleService.fetchArticleContent(cleanUrl);

      // Check if we have valid article content
      if (processingResult.content.isEmpty) {
        appLog.debug('Failed to fetch article from URL: $cleanUrl', name: _logName);
        return await _processText(input, 'Text Content');
      }

      // Calculate hash from the article content
      final hash = HashHelper.calculateStringHash(processingResult.content);

      // Check if we have article metadata in the processing result
      if (!processingResult.hasArticleMetadata()) {
        appLog.debug('No article metadata found, processing as text', name: _logName);
        return await _processText(input, 'Text Content');
      }

      // Access the article metadata directly
      final articleMetadata = processingResult.articleMetadata;

      // Extract site name from URL if not available in metadata
      if (articleMetadata.siteName.isEmpty) {
        articleMetadata.siteName = extractSiteName(cleanUrl);
        appLog.debug('Using extracted site name: ${articleMetadata.siteName}', name: _logName);
      }

      // Make sure title is populated - use site name as fallback if available
      if (articleMetadata.title.isEmpty) {
        articleMetadata.title = 'Article from ${articleMetadata.siteName}';
        appLog.debug('Using fallback title: ${articleMetadata.title}', name: _logName);
      }

      // Set the URL properties for reference
      articleMetadata
        ..url = extractedUrl ?? input // Original input that contained the URL
        ..finalUrl = cleanUrl; // Final URL after extraction and processing

      // Ensure we have a display name for the article
      final displayName = articleMetadata.title;
      appLog.debug('Using display name for attachment: $displayName', name: _logName);

      // Register with attachment registry using the Protocol Buffer result
      final attachmentId = 'news_${DateTime.now().millisecondsSinceEpoch}';

      // Set the display name in the processing result
      processingResult
        ..status = UploadStatus.UPLOAD_STATUS_COMPLETED
        ..displayName = displayName
        ..jobId = attachmentId;

      // Register the ProcessingResult directly with the attachment registry
      _attachmentRegistry.registerAttachment(
        id: attachmentId,
        result: processingResult,
      );

      // Get the entity detection service from the client context service
      final clientContext = ClientContextService();
      clientContext.entityDetection.registerEntity(
        id: cleanUrl, // Use final URL after redirects
        text: displayName,
        type: 'news',
        processingResult: processingResult,
      );

      // Find and update the task associated with this URL
      // This is key to ensuring the display name is updated in the task list
      final taskManager = ContentProcessingTaskManager.instance;
      // Find matching task by filePath - use try/catch to handle the case where no task is found
      try {
        final matchingTask = taskManager.getTaskByFilePath(input, cleanUrl);
        appLog.debug('Updating task display name: ${matchingTask.id}', name: _logName);

        // Create a metadata map for updating the task
        final taskMetadata = {
          'title': displayName,
          'siteName': articleMetadata.siteName,
        };
        await taskManager.updateTaskWithMetadata(matchingTask.id, taskMetadata);
      } catch (e) {
        appLog.debug('No matching task found for URL: $input', name: _logName);
      }

      // Create a map with the necessary data
      final contentInfo = {
        'type': InputType.url.name,
        'filePath': input,
        'fileName': displayName,
        'mimeType': 'text/html',
        'content': processingResult.content,
        'contentHash': hash,
        'sourceType': InputSourceType.news.name,
        'attachmentId': attachmentId,
      };

      // Create and emit a source processed event
      final source = InputSource(
        type: InputSourceType.news,
        filePath: input,
        fileName: displayName,
        mimeType: 'text/html',
        content: processingResult.content,
        contentHash: hash,
        processingResult: processingResult,
      );

      // Notify listeners that a source has been processed
      notifySourceProcessed(source);

      // Return processed article information
      return contentInfo;
    } catch (e, stack) {
      appLog.error('Error processing news article', name: _logName, error: e, stackTrace: stack);

      // If article processing fails, fall back to processing as text
      return await _processText(input, 'Text Content');
    }
  }
}
