import 'package:flutter/foundation.dart';
import 'package:promz/core/models/license_model.dart';
import 'package:promz/core/services/api/api_client.dart';
import 'package:promz/core/services/app_update_service.dart';
import 'package:promz/core/services/attachment/attachment_registry_service.dart';
import 'package:promz/core/services/content_processing_service.dart';
import 'package:promz/core/services/grpc/whatsapp_processing_service.dart';
import 'package:promz/core/services/license_manager_service.dart';
import 'package:promz/core/services/secure_storage_service_provider.dart';
import 'package:promz/core/services/user_profile_service.dart';
import 'package:promz/database/database.dart';
import 'package:promz/features/home/<USER>/prompt_usage_event.dart';
import 'package:promz/features/home/<USER>/variable.dart';
import 'package:promz/features/home/<USER>/local_prompt_usage_repository.dart';
import 'package:promz/features/home/<USER>/entity_detection_service.dart';
import 'package:promz/features/home/<USER>/prompt_suggestion_service.dart';
import 'package:promz/features/home/<USER>/prompt_usage_service.dart';
import 'package:promz/features/home/<USER>/ticker_loading_service.dart';
import 'package:promz/features/home/<USER>/variable_manager.dart';
import 'package:promz/features/youtube/services/youtube_service.dart';
import 'package:promz/generated/content_upload.pb.dart';
import 'package:promz_common/llm/base_context_service.dart';
import 'package:promz_common/promz_common.dart';

/// ClientContextService: Centralized Application Context and Services
///
/// This service provides a centralized point for accessing application context,
/// services, and state. It follows a hybrid pattern that supports both singleton
/// access for production code and dependency injection for testing.
///
/// Key Responsibilities:
///   - Provides access to core services:
///     - EntityDetectionService: Detects entities within text.
///     - PromptSuggestionService: Provides prompt suggestions based on keywords.
///     - FileProcessingService: Processes files and extracts content.
///     - TickerLoadingService: Loads ticker data in the background.
///     - LicenseManagerService: Manages licenses and their validation.
///     - UserProfileService: Manages user profile data.
///   - Manages application-wide state:
///     - Entities: Stores detected entities for use throughout the app.
///     - Variable Values: Stores user-defined variable values for prompt execution.
///     - Variable Manager: Manages variable resolution and state.
///   - Facilitates communication and data sharing between different parts of the application.
///
/// Usage:
///   - Call ClientContextService.initialize() during app startup to initialize the instance.
///   - Access the instance using the factory constructor: ClientContextService().
///   - For testing, use ClientContextService.forTesting() to create isolated instances.

class ClientContextService extends ChangeNotifier implements BaseContextService {
  static const _logName = 'ClientContextService';

  /// Private constructor for singleton instance
  ClientContextService._({
    required Future<AppDatabase> db,
    VariableManager? variableManager,
    EntityDetectionService? entityDetection,
    PromptSuggestionService? promptSuggestion,
    TickerLoadingService? tickerLoading,
    LicenseManagerService? licenseManager,
    UserProfileService? userProfile,
    PromptUsageService? promptUsageService,
    ContentProcessingService? contentProcessingService,
    WhatsAppProcessingService? whatsAppProcessingService,
    AppUpdateService? appUpdateService,
    ApiClient? apiClient,
  }) {
    _variableManager = variableManager ?? VariableManager(clientContextService: this);
    _entityDetection = entityDetection ?? EntityDetectionService(db: db);
    _promptSuggestion =
        promptSuggestion ?? PromptSuggestionService(db: db, clientContextService: this);
    _tickerLoading = tickerLoading ?? TickerLoadingService(db: db);
    _licenseManager = licenseManager ?? LicenseManagerService();
    _userProfileService = userProfile ?? UserProfileService();
    _apiClient = apiClient ?? ApiClient(licenseManager: _licenseManager);

    // Set ContentProcessingService if provided
    _contentProcessingService = contentProcessingService;

    // Set WhatsAppProcessingService if provided, or create a new one
    _whatsAppProcessingService =
        whatsAppProcessingService ?? WhatsAppProcessingService(apiClient: _apiClient);

    // Initialize app update service
    _appUpdateService = appUpdateService ?? AppUpdateService();
    _appUpdateService.initialize();

    // Initialize prompt usage service with repository
    db.then((database) {
      final repository = LocalPromptUsageRepository(database);
      _promptUsageService = promptUsageService ??
          PromptUsageService(
            repository: repository,
            database: database,
            apiClient: _apiClient,
          );

      // Fetch popular prompts at startup
      _promptUsageService.fetchPopularPrompts();
    });
  }

  /// Factory constructor for testing
  factory ClientContextService.forTesting({
    required Future<AppDatabase> db,
    VariableManager? variableManager,
    EntityDetectionService? entityDetection,
    PromptSuggestionService? promptSuggestion,
    TickerLoadingService? tickerLoading,
    LicenseManagerService? licenseManager,
    UserProfileService? userProfile,
    PromptUsageService? promptUsageService,
    ContentProcessingService? contentProcessingService,
    AppUpdateService? appUpdateService,
    ApiClient? apiClient,
    bool useMockStorage = true, // Add parameter to control storage implementation
  }) {
    // Initialize license manager for testing
    LicenseManagerService.initialize();

    // Use mock storage for testing to avoid platform channel errors
    if (useMockStorage) {
      SecureStorageServiceProvider.useTestImplementation();
    }

    final instance = ClientContextService._(
      db: db,
      variableManager: variableManager,
      entityDetection: entityDetection,
      promptSuggestion: promptSuggestion,
      tickerLoading: tickerLoading,
      licenseManager: licenseManager,
      userProfile: userProfile,
      promptUsageService: promptUsageService,
      contentProcessingService: contentProcessingService,
      appUpdateService: appUpdateService,
      apiClient: apiClient,
    );

    return instance;
  }

  /// Singleton instance
  static ClientContextService? _instance;

  /// Initialize the singleton instance
  static ClientContextService initialize(
    Future<AppDatabase> db, {
    VariableManager? variableManager,
    EntityDetectionService? entityDetection,
    PromptSuggestionService? promptSuggestion,
    TickerLoadingService? tickerLoading,
    LicenseManagerService? licenseManager,
    UserProfileService? userProfile,
    PromptUsageService? promptUsageService,
    ContentProcessingService? contentProcessingService,
    WhatsAppProcessingService? whatsAppProcessingService,
    ApiClient? apiClient,
  }) {
    appLog.debug('START: Initializing ClientContextService', name: _logName);
    if (_instance != null) {
      appLog.debug('END: ClientContextService already initialized', name: _logName);
      return _instance!;
    }

    // Initialize license manager first
    LicenseManagerService.initialize();

    // Create API client if not provided
    final client = apiClient ??
        ApiClient(
          licenseManager: licenseManager ?? LicenseManagerService(),
          // Use lazy WebSocket initialization to prevent race conditions
          lazyWebSocketInit: true,
        );

    _instance = ClientContextService._(
      db: db,
      variableManager: variableManager,
      entityDetection: entityDetection,
      promptSuggestion: promptSuggestion,
      tickerLoading: tickerLoading,
      licenseManager: licenseManager,
      userProfile: userProfile,
      promptUsageService: promptUsageService,
      contentProcessingService: contentProcessingService,
      whatsAppProcessingService: whatsAppProcessingService,
      apiClient: client,
    );

    // Start background loading of tickers
    _instance!._tickerLoading.startLoading();

    // Initialize WebSocket connection with a delay to ensure proper app initialization
    // This prevents premature disposal of the WebSocket connection
    Future.delayed(const Duration(seconds: 1), () {
      // Check if client still exists (not disposed)
      if (_instance != null) {
        appLog.debug('Initializing WebSocket connection after delay', name: _logName);
        client.initializeWebSocket().then((success) {
          if (success) {
            appLog.debug('WebSocket connection initialized successfully', name: _logName);
          } else {
            appLog.warning('Failed to initialize WebSocket connection', name: _logName);

            // Schedule a retry after a longer delay
            Future.delayed(const Duration(seconds: 3), () {
              if (_instance != null) {
                appLog.debug('Retrying WebSocket connection initialization', name: _logName);
                client.refreshWebSocketConnection();
              }
            });
          }
        });
      }
    });

    appLog.debug('END: ClientContextService initialized', name: _logName);
    return _instance!;
  }

  /// Reset the singleton instance (for testing)
  @visibleForTesting
  static void reset() {
    _instance = null;
    appLog.debug('END: ClientContextService reset', name: _logName);
  }

  /// Factory constructor to access the singleton instance
  factory ClientContextService() {
    if (_instance == null) {
      appLog.debug('END: ClientContextService not initialized. Call initialize() first.',
          name: _logName);
      throw StateError('ClientContextService not initialized. Call initialize() first.');
    }
    appLog.debug('END: ClientContextService initialized', name: _logName);
    return _instance!;
  }

  // Embedded services
  late VariableManager _variableManager;
  late EntityDetectionService _entityDetection;
  late PromptSuggestionService _promptSuggestion;
  late TickerLoadingService _tickerLoading;
  late LicenseManagerService _licenseManager;
  late UserProfileService _userProfileService;
  late final PromptUsageService _promptUsageService;
  ContentProcessingService? _contentProcessingService;
  late final WhatsAppProcessingService _whatsAppProcessingService;
  late final ApiClient _apiClient;
  late final AppUpdateService _appUpdateService;

  // Cache for attachment-based variables to avoid repetitive lookups
  final Map<String, ProcessingResult> _attachmentCache = {};
  DateTime _lastAttachmentCacheUpdate = DateTime.fromMillisecondsSinceEpoch(0);
  // Cache TTL in milliseconds (5 seconds by default)
  final int _attachmentCacheTtl = 5000;
  bool _attachmentCacheForceRefresh = false;

  /// Force a refresh of the attachment cache on next variable request
  void invalidateAttachmentCache() {
    appLog.debug('Invalidating attachment cache', name: _logName);
    _attachmentCacheForceRefresh = true;
  }

  /// Update the attachment cache if needed
  Map<String, dynamic> _getAttachmentsWithCache() {
    final now = DateTime.now();
    final elapsedMs = now.difference(_lastAttachmentCacheUpdate).inMilliseconds;

    // Skip cache update if it's recent enough and no force refresh is requested
    if (!_attachmentCacheForceRefresh &&
        elapsedMs < _attachmentCacheTtl &&
        _attachmentCache.isNotEmpty) {
      appLog.debug('Using cached attachments (age: ${elapsedMs}ms)', name: _logName);
      return _attachmentCache;
    }

    appLog.debug('Refreshing attachment cache', name: _logName);
    _attachmentCacheForceRefresh = false;
    _lastAttachmentCacheUpdate = now;

    // Fetch all attachments at once instead of by type
    final allAttachments = attachmentRegistry.getAllAttachmentsByType();

    // Cache is empty if no attachments found
    if (allAttachments.isEmpty) {
      _attachmentCache.clear();
      return {};
    }

    // Update cache with all attachments
    _attachmentCache.clear();
    for (final entry in allAttachments.entries) {
      final type = entry.key;
      final attachment = entry.value;
      if (attachment != null) {
        appLog.debug('Caching attachment of type: $type, id: ${attachment.jobId}', name: _logName);
        _attachmentCache[type] = attachment;
      }
    }

    // Also cache server attachments
    final serverAttachments = attachmentRegistry.getAllServerAttachments();
    if (serverAttachments.isNotEmpty) {
      appLog.debug('Caching ${serverAttachments.length} server attachments', name: _logName);
      for (final attachment in serverAttachments) {
        if (attachment.contentType.isNotEmpty) {
          // Cache by content type using the same key structure as regular attachments
          _attachmentCache[attachment.contentType] = attachment;
        }
      }
    }

    return _attachmentCache;
  }

  // Getters for services
  VariableManager get variableManager => _variableManager;
  EntityDetectionService get entityDetection => _entityDetection;
  PromptSuggestionService get promptSuggestion => _promptSuggestion;
  TickerLoadingService get tickerLoading => _tickerLoading;
  LicenseManagerService get licenseManager => _licenseManager;
  UserProfileService get userProfile => _userProfileService;
  PromptUsageService get promptUsage => _promptUsageService;
  ApiClient get apiClient => _apiClient;

  /// For backward compatibility with existing code
  ContentProcessingService get contentProcessing {
    if (_contentProcessingService == null) {
      throw Exception('ContentProcessingService not initialized');
    }
    return _contentProcessingService!;
  }

  /// Set the content processing service from an external provider
  set contentProcessingService(ContentProcessingService service) {
    _contentProcessingService = service;
  }

  /// Get the content processing service
  ContentProcessingService get contentProcessingService {
    if (_contentProcessingService == null) {
      throw Exception('ContentProcessingService not initialized');
    }
    return _contentProcessingService!;
  }

  /// Get the WhatsApp processing service
  WhatsAppProcessingService? get whatsAppProcessingService => _whatsAppProcessingService;

  // For backward compatibility, provide direct accessors to services in ContentProcessingService
  // These should eventually be removed in favor of accessing through ContentProcessingService

  /// Get the attachment registry service
  AttachmentRegistryService get attachmentRegistry {
    return _contentProcessingService?.getAttachmentRegistry() ?? AttachmentRegistryService();
  }

  /// Get the news article service (for backward compatibility)
  dynamic get newsArticle => contentProcessing.getNewsArticleService();

  /// Get the YouTube service
  YouTubeService get youtube {
    return _contentProcessingService?.getYouTubeService() ??
        YouTubeService(
          attachmentRegistry: attachmentRegistry,
          entityDetectionService: entityDetection,
        );
  }

  /// Implementation for BaseContextService
  @override
  String transformTitleWithAllValues(String title) {
    final values = getAllVariableValues();
    return TemplateUtils.transformTitleWithValues(title, values);
  }

  // Entity management
  Map<String, Entity> _entities = {};
  Map<String, Entity> get entities => _entities;

  // Variable values management - central store for all variable values
  final Map<String, String> _variableValues = {};

  // Hashes to track changes
  int _variableValuesHash = 0;
  int _entitiesHash = 0;

  // Utility to compute hash for a map
  int _computeHash(Map<String, dynamic> map) {
    return map.entries.fold(0, (hash, entry) => hash ^ entry.key.hashCode ^ entry.value.hashCode);
  }

  /// Get all variable values
  Map<String, String> get variableValues => getAllVariableValues();

  /// Updates entities and notifies variable manager
  Future<void> detectEntities(String text) async {
    final detectedEntities = await _entityDetection.detectEntities(text);
    updateEntities(detectedEntities);
  }

  /// Updates the stored entities
  void updateEntities(List<Entity> newEntities) {
    final newEntitiesMap = {
      for (var entity in newEntities) entity.text.toLowerCase(): entity,
    };

    final newHash = _computeHash(newEntitiesMap);
    if (newHash == _entitiesHash) {
      return;
    }

    _entities = newEntitiesMap;
    _entitiesHash = newHash;
    notifyListeners();
  }

  /// Set a variable value in the centralized store
  /// This is the primary method for updating variable values throughout the application
  void setVariableValue(String name, String value) {
    appLog.debug('START: Setting variable value: $name = $value', name: _logName);

    // Record previous value if any. Always use synonymized canonical name
    final canonicalName = EntityUtils.getCanonicalName(name);
    final oldValue = _variableValues[canonicalName];

    if (oldValue == value) {
      return;
    }

    // Update the centralized value
    _variableValues[canonicalName] = value;

    // Notify VariableManager to update its internal state
    _variableManager.notifyVariableValueChanged();

    // Notify listeners of the change
    final newHash = _computeHash(_variableValues);
    if (newHash != _variableValuesHash) {
      _variableValuesHash = newHash;
      notifyListeners();
    }
    appLog.debug('END: Variable value set: $name = $value', name: _logName);
  }

  /// Clear a variable value from the centralized store
  void clearVariableValue(String name) {
    final synonymizedCanonicalName = EntityUtils.getCanonicalName(name);
    final oldValue = _variableValues.remove(synonymizedCanonicalName);

    if (oldValue == null) {
      appLog.debug('No value to clear for variable: $name', name: _logName);
      return;
    }

    if (EntityUtils.isFinanceVariable(name)) {
      _clearRelatedFinanceVariables(name);
    }

    _variableManager.notifyVariableValueChanged();
    final newHash = _computeHash(_variableValues);
    if (newHash != _variableValuesHash) {
      _variableValuesHash = newHash;
      notifyListeners();
    }
  }

  /// Get a variable value from the centralized store
  /// Returns null if the variable doesn't exist
  String? getVariableValue(String name) {
    // Always use synonymized canonical name for consistent lookup
    final synonymizedCanonicalName = EntityUtils.getCanonicalName(name);
    return getAllVariableValues()[synonymizedCanonicalName];
  }

  /// Get all variable values
  Map<String, String> getAllVariableValues() {
    appLog.debug('START: Retrieving all variable values', name: _logName);
    final Map<String, String> allValues = {};

    // Add centralized variable values, which take precedence
    _variableValues.forEach((key, value) {
      allValues[EntityUtils.getCanonicalName(key)] = value;
    });

    // Get all attachments using the cached method
    final attachments = _getAttachmentsWithCache();
    if (attachments.isEmpty) {
      return allValues;
    }

    // Process YouTube video variables if available
    final youtubeAttachment = attachments[EntityType.youtubeVideo.name];
    if (youtubeAttachment != null && youtubeAttachment.hasYoutubeMetadata()) {
      final youtubeVariables = contentProcessingService.getVariablePackage(EntityType.youtubeVideo);
      youtubeVariables.forEach((key, value) {
        appLog.debug('Adding YouTube variable: $key = $value', name: _logName);
        allValues[EntityUtils.getCanonicalName(key)] = value.toString();
      });
    }

    // Process news article variables if available
    final newsAttachment = attachments[EntityType.news.name];
    if (newsAttachment != null && newsAttachment.hasArticleMetadata()) {
      final newsVariables = contentProcessingService.getVariablePackage(EntityType.news);
      newsVariables.forEach((key, value) {
        appLog.debug('Adding news variable: $key = $value', name: _logName);
        allValues[EntityUtils.getCanonicalName(key)] = value.toString();
      });
    }

    // Process conversation variables if available
    final conversationAttachment = attachments[EntityType.conversation.name];
    if (conversationAttachment != null) {
      final attachmentId = conversationAttachment.jobId;
      appLog.debug('Processing conversation variables for ID: $attachmentId', name: _logName);

      // Convert attachment to map format for entity detection service
      final conversationAttachmentMap = {
        'id': attachmentId,
        'content': conversationAttachment.content,
        'contentType': conversationAttachment.contentType,
        'metadata': conversationAttachment,
      };

      // Pass the attachment map as additionalData since conversations need more context
      final conversationVariables = _entityDetection.getVariablePackage(
        EntityType.conversation,
        attachmentId,
        additionalData: conversationAttachmentMap,
      );

      conversationVariables.forEach((key, value) {
        appLog.debug('Adding conversation variable: $key = $value', name: _logName);
        allValues[EntityUtils.getCanonicalName(key)] = value.toString();
      });
    }

    // Process general attachment variables if available
    final attachment = attachments[EntityType.attachment.name];
    if (attachment != null) {
      final id = attachment.jobId;
      final content = attachment.content;
      appLog.debug('Adding attachment variable: $id', name: _logName);
      allValues[EntityUtils.getCanonicalName('ATTACHMENT:CONTENTS')] = content;
    }

    // Add server attachment variables in the format ATTACHMENT:UPLOAD_ID
    final serverAttachments = attachments['serverAttachments'] as List<dynamic>?;
    if (serverAttachments != null && serverAttachments.isNotEmpty) {
      _addServerAttachmentVariables(allValues, serverAttachments);
    } else {
      appLog.debug('No server attachments found', name: _logName);
    }

    appLog.debug('END: Retrieved all variable values: ${allValues.length} values', name: _logName);
    return allValues;
  }

  /// Clear all related finance variable names
  void _clearRelatedFinanceVariables(String name) {
    // Skip if this is already a related update to avoid infinite recursion
    if (name.startsWith('__RELATED_UPDATE__')) return;

    appLog.debug('Clearing related finance variables for $name', name: _logName);

    const marker = '__RELATED_UPDATE__';

    List<String> synonyms = EntityUtils.getCanonicalNames(name);
    for (final synonym in synonyms) {
      if (synonym != name) {
        _variableValues.remove('$marker:$synonym');
      }
    }

    // Remove the marker after clearing values
    _cleanupRelatedUpdateMarkers();
  }

  /// Clean up temporary markers used for related updates
  void _cleanupRelatedUpdateMarkers() {
    final keysToRemove = <String>[];
    for (final key in _variableValues.keys) {
      if (key.startsWith('__RELATED_UPDATE__')) {
        keysToRemove.add(key);
      }
    }

    for (final key in keysToRemove) {
      final value = _variableValues[key]!;
      final cleanKey = key.replaceFirst('__RELATED_UPDATE__:', '');
      _variableValues[cleanKey] = value;
      _variableValues.remove(key);
    }
  }

  /// Add server attachment variables to the variables map
  /// This method adds variables for server-processed attachments
  void _addServerAttachmentVariables(
      Map<String, String> variables, List<dynamic> serverAttachments) {
    if (serverAttachments.isEmpty) {
      appLog.debug('No server attachments found', name: _logName);
      return;
    }

    appLog.debug('Processing ${serverAttachments.length} server attachments for variables',
        name: _logName);

    // Add count variable
    variables[EntityUtils.getCanonicalName('ATTACHMENT:COUNT')] =
        serverAttachments.length.toString();

    // Add single attachment in simple format for backwards compatibility
    if (serverAttachments.length == 1) {
      final attachment = serverAttachments.first;
      final serverId = attachment.jobId;
      variables[EntityUtils.getCanonicalName('ATTACHMENT:UPLOAD_ID')] = serverId;
      appLog.debug('Added single server attachment variable: ATTACHMENT:UPLOAD_ID = $serverId',
          name: _logName);
    }

    // Always add each attachment with index for consistent access pattern
    for (int i = 0; i < serverAttachments.length; i++) {
      final attachment = serverAttachments[i];
      if (attachment != null) {
        // Try to get uploadId safely with fallback to jobId
        final uploadId = attachment.uploadId ?? attachment.jobId ?? '';
        if (uploadId.isNotEmpty) {
          final varKey = 'ATTACHMENT:UPLOAD_ID_${i + 1}';
          variables[EntityUtils.getCanonicalName(varKey)] = uploadId;
          appLog.debug('Added server attachment variable: $varKey = $uploadId', name: _logName);
        }
      }
    }

    // Add with prefix notation for backward compatibility with older code
    for (int i = 0; i < serverAttachments.length; i++) {
      final attachment = serverAttachments[i];
      final serverId = attachment.jobId;
      if (serverId != null && serverId.isNotEmpty) {
        final variableName = '{#${i + 1}}ATTACHMENT:UPLOAD_ID';
        variables[EntityUtils.getCanonicalName(variableName)] = serverId;
        appLog.debug('Added prefixed server attachment variable: $variableName = $serverId',
            name: _logName);
      }
    }
  }

  /// Get a list of all variables with values
  List<Variable> getVariablesWithValues() {
    final variables = <Variable>[];
    final allValues = getAllVariableValues();

    for (final entry in allValues.entries) {
      final canonicalName = EntityUtils.getCanonicalName(entry.key);
      appLog.debug('Adding variable: $canonicalName = ${entry.value}', name: _logName);
      variables.add(Variable(
        name: canonicalName,
        value: entry.value,
        type: VariableType.customVariable,
        displayName: getDisplayNameForVariable(canonicalName),
        originalDisplay: entry.key,
        fullTemplate: '{{$canonicalName}}',
      ));
    }

    return variables;
  }

  /// Clear all variable values
  void clearAllVariableValues() {
    if (_variableValues.isEmpty) return;

    _variableValues.clear();
    appLog.debug('All variables cleared', name: _logName);
    notifyListeners();
  }

  /// Update license state across the application
  /// This method is called by LicenseManagerService when license state changes
  void updateLicenseState(UserLicense? license) {
    appLog.debug('START: Updating license state throughout application', name: _logName);

    // Update UserProfileService with the new license state
    if (license != null) {
      appLog.debug('Updating UserProfileService with license: ${license.licenseType}',
          name: _logName);
      _userProfileService.updateProfileFromLicense(license);
    } else {
      appLog.debug('Clearing license information in UserProfileService', name: _logName);
      _userProfileService.updateProfileFromLicense(UserLicense.empty());
    }

    // Could update any other components that need to know about license changes

    appLog.debug('END: License state updated throughout application', name: _logName);
  }
}

/// Extension on ClientContextService for template-related operations
extension CcsTemplateExtensions on ClientContextService {
  String transformTitleForDisplay(String title) {
    return TemplateUtils.transformTitleForDisplay(title);
  }

  String transformTitleWithValues(String title, Map<String, String> values) {
    return TemplateUtils.transformTitleWithValues(title, values);
  }
}

/// Extension on ClientContextService for finance-related operations
extension CcsFinanceExtensions on ClientContextService {
  /// Check if a prompt contains financial variables
  bool hasFinancialVariables(PromptModel promptModel) {
    final title = promptModel.title;
    final financialPattern = RegExp(r'\{\{(FINANCE|STOCK|SP500)[^}]*\}\}', caseSensitive: false);
    return financialPattern.hasMatch(title);
  }
}

/// Extension on ClientContextService for entity-related operations
extension CcsEntityExtensions on ClientContextService {
  /// Get the display name for an entity
  String getDisplayNameForEntity(Entity entity) {
    return EntityUtils.getDisplayName(entity: entity);
  }

  /// Get the display name for a variable
  String getDisplayNameForVariable(String variableName) {
    return EntityUtils.getDisplayName(variableName: variableName);
  }
}

/// Extension on ClientContextService for API client access
extension CcsApiClientExtension on ClientContextService {
  /// Get the API client instance
  /// This is the single source of truth for API communication throughout the app
  ApiClient get apiClient => _apiClient;
}

/// Extension on ClientContextService for prompt usage tracking
extension CcsPromptUsageExtensions on ClientContextService {
  /// Records that a prompt was selected by the user
  ///
  /// This should be called when a user selects a prompt from any list
  /// (suggestions, recent, popular).
  Future<void> recordPromptSelected(String promptId) async {
    try {
      await promptUsage.recordUsage(promptId, PromptUsageEventType.selected);
    } catch (e, stack) {
      appLog.error('Error recording prompt selection',
          name: 'CcsPromptUsageExtensions', error: e, stackTrace: stack);
    }
  }

  /// Records that a prompt was executed by the user
  ///
  /// This should be called when a user executes a prompt (e.g.,
  /// by clicking Execute in VariablesDialog or directly executing
  /// from a prompt list).
  Future<void> recordPromptExecuted(String promptId) async {
    try {
      await promptUsage.recordUsage(promptId, PromptUsageEventType.executed);
    } catch (e, stack) {
      appLog.error('Error recording prompt execution',
          name: 'CcsPromptUsageExtensions', error: e, stackTrace: stack);
    }
  }
}
