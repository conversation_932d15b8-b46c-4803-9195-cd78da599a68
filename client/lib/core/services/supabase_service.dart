import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:crypto/crypto.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:promz/core/models/supabase_auth_state.dart';
import 'package:promz/core/services/secure_session_storage.dart';
import 'package:promz/core/services/secure_storage_service.dart';
import 'package:promz/core/services/windows_oauth_service.dart';
import 'package:promz_common/promz_common.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// SupabaseService: Authentication and Database Management Service
///
/// This service provides a centralized interface for all Supabase-related operations
/// including authentication, user management, and database access.
///
/// Key responsibilities:
/// - User authentication (email/password, Google, Apple)
/// - Session management and token refresh
/// - Supabase database operations
/// - API key storage (for license management)
///
/// Usage:
/// 1. Initialize during app startup:
///    ```
///    await SupabaseService().initialize(
///      supabaseUrl: 'your-url',
///      supabaseAnonKey: 'your-key'
///    );
///    ```
///
/// 2. Access authentication methods:
///    ```
///    final user = await SupabaseService().signInWithEmail(email: '<EMAIL>', password: 'password');
///    ```
///
/// 3. Listen to authentication state changes:
///    ```
///    SupabaseService().authStateStream.listen((state) {
///      // Handle auth state changes
///    });
///    ```
///
/// Note: License management functionality has been moved to LicenseManagerService.
class SupabaseService {
  static const _logName = 'SupabaseService';

  // Singleton instance
  static final SupabaseService _instance = SupabaseService._internal();
  factory SupabaseService() => _instance;
  SupabaseService._internal();

  // Supabase client
  late final SupabaseClient _client;
  SupabaseClient get client => _client;

  late String supabaseUrl;
  late String supabaseAnonKey;
  late String serverClientId;

  // Authentication state
  bool _isInitialized = false;
  bool get isInitialized => _isInitialized;

  // User state
  User? _currentUser;
  User? get currentUser => _currentUser;
  bool get isAuthenticated => _currentUser != null;

  /// Get the current user ID or null if not authenticated
  String? get currentUserId => _currentUser?.id;

  // Custom auth state controller for more reliable state propagation
  final StreamController<SupabaseAuthState> _authStateController =
      StreamController<SupabaseAuthState>.broadcast();
  Stream<SupabaseAuthState> get authStateStream => _authStateController.stream;

  // In-memory fallback for API key storage
  String? _inMemoryApiKey;

  /// Initialize Supabase with the provided URL and key
  Future<void> initialize({
    required String supabaseUrl,
    required String supabaseAnonKey,
    required String serverClientId,
  }) async {
    if (_isInitialized) {
      appLog.debug('Supabase already initialized', name: _logName);
      return;
    }

    try {
      appLog.debug('START: Initializing Supabase', name: _logName);
      final secureStorage = SecureSessionStorage(); // Use your custom storage

      await Supabase.initialize(
        url: supabaseUrl,
        anonKey: supabaseAnonKey,
        debug: kDebugMode, // Keep Supabase debug logs enabled for now
        authOptions: FlutterAuthClientOptions(
          localStorage: secureStorage,
          authFlowType: AuthFlowType.pkce,
          autoRefreshToken: true,
        ),
      );

      _client = Supabase.instance.client;
      _isInitialized = true;
      this.supabaseUrl = supabaseUrl;
      this.supabaseAnonKey = supabaseAnonKey;
      this.serverClientId = serverClientId;

      // Initialize WindowsOAuthService on Windows
      if (Platform.isWindows) {
        appLog.debug('Initializing Windows-specific OAuth service', name: _logName);
        final windowsOAuthService = WindowsOAuthService();
        windowsOAuthService.initialize(_client, supabaseUrl, supabaseAnonKey);
      }

      // Check if Supabase automatically restored the session
      _currentUser = _client.auth.currentUser;
      if (_currentUser != null) {
        appLog.debug('Supabase.initialize successfully restored user: ${_currentUser!.email}',
            name: _logName);
        _emitAuthState(); // Emit initial state if restored automatically
      } else {
        appLog.warning('Supabase.initialize FAILED to restore user. Attempting manual recovery...',
            name: _logName);
        // Attempt manual recovery if auto-restore failed
        bool recoveryAttempted = await _attemptManualSessionRecovery();
        if (!recoveryAttempted) {
          // If manual recovery wasn't even attempted or failed early,
          // emit the unauthenticated state.
          _emitAuthState();
        }
      }

      // Listen for auth state changes (Handles sign-in, sign-out, refresh AFTER init)
      _client.auth.onAuthStateChange.listen((data) {
        final AuthChangeEvent event = data.event;
        final Session? session = data.session;
        appLog.debug('Auth state change detected: $event', name: _logName);

        User? previousUser = _currentUser; // Keep track of previous state

        // Update internal state based on event
        if (event == AuthChangeEvent.signedIn ||
            event == AuthChangeEvent.tokenRefreshed ||
            event == AuthChangeEvent.userUpdated ||
            event == AuthChangeEvent.initialSession) {
          // Treat initialSession like signedIn if session is present
          _currentUser = session?.user;
        } else if (event == AuthChangeEvent.signedOut) {
          _currentUser = null;
        }

        // Log user changes
        if (previousUser?.id != _currentUser?.id) {
          appLog.debug(
              'User changed from ${previousUser?.email ?? "null"} to ${_currentUser?.email ?? "null"}',
              name: _logName);
        }

        // Always emit the current state after processing the event
        _emitAuthState();
      });
    } catch (e, stack) {
      appLog.error('END: Failed to initialize Supabase',
          name: _logName, error: e, stackTrace: stack);
      _emitAuthState(); // Ensure state is emitted even on error (as unauthenticated)
      rethrow;
    }
  }

  // Enhanced helper for more robust session recovery
  Future<bool> _attemptManualSessionRecovery() async {
    bool recoverySucceeded = false;
    try {
      appLog.debug('Starting manual user session recovery', name: _logName);
      final sessionString = await SecureStorageService.getSupabaseSession();

      if (sessionString == null || sessionString.isEmpty) {
        appLog.debug('No session string found in storage, recovery not possible', name: _logName);
        return false;
      }

      appLog.debug('Found session string in storage, attempting to parse', name: _logName);

      try {
        // Try to parse the session JSON
        final sessionJson = jsonDecode(sessionString);
        final refreshToken = sessionJson['refresh_token'] as String?;

        if (refreshToken == null || refreshToken.isEmpty) {
          appLog.warning('Session JSON missing or has empty refresh_token', name: _logName);
          // Delete the invalid session data
          await SecureStorageService.deleteSupabaseSession();
          return false;
        }

        // Check if the session is expired
        final expiresAt = sessionJson['expires_at'];
        if (expiresAt != null) {
          final expiryTime = int.tryParse(expiresAt.toString()) ?? 0;
          final now = DateTime.now().millisecondsSinceEpoch ~/ 1000; // Current time in seconds

          if (expiryTime > 0 && now >= expiryTime) {
            appLog.warning('Found expired session, cleaning up', name: _logName);
            await SecureStorageService.deleteSupabaseSession();
            return false;
          }
        }

        // Attempt to recover the session
        appLog.debug('Found valid refresh token, attempting to recover session', name: _logName);

        try {
          // Try to set the session with the refresh token
          final response = await _client.auth.setSession(refreshToken);

          if (response.session != null) {
            _currentUser = response.user;
            recoverySucceeded = true;
            appLog.info('Manual session recovery succeeded for user: ${_currentUser?.email}',
                name: _logName);
            _emitAuthState();

            // Cache user profile after successful recovery
            await cacheUserProfileInfo();
          } else {
            appLog.error('Session recovery response was null', name: _logName);
            await SecureStorageService.deleteSupabaseSession();
          }
        } catch (e, stack) {
          // If session recovery fails, clean up the invalid session
          appLog.error('Session recovery failed, may be invalid or expired',
              name: _logName, error: e, stackTrace: stack);
          await SecureStorageService.deleteSupabaseSession();
        }
      } on FormatException catch (e, stack) {
        appLog.error('Failed to parse session JSON', name: _logName, error: e, stackTrace: stack);
        // Clean up corrupted session data
        await SecureStorageService.deleteSupabaseSession();
      } catch (e, stack) {
        appLog.error('Unexpected error during session recovery',
            name: _logName, error: e, stackTrace: stack);
        // Clean up potentially corrupted session data
        await SecureStorageService.deleteSupabaseSession();
      }
    } catch (e, stack) {
      appLog.error('Error accessing secure storage during session recovery',
          name: _logName, error: e, stackTrace: stack);
    }

    return recoverySucceeded;
  }

  // Manually update and emit the auth state - useful for when Supabase events don't fire correctly
  void updateAuthState() {
    final user = _client.auth.currentUser;
    _currentUser = user;

    // Emit the updated auth state
    _emitAuthState();
  }

  // Helper method to emit the current auth state
  void _emitAuthState() {
    // TODO: Consider adding logic to prevent rapid duplicate emissions if necessary,
    // though the StreamController.broadcast should handle listeners correctly.
    final isAuthenticated = _currentUser != null;
    appLog.debug(
        'Emitting auth state: isAuthenticated=$isAuthenticated, user=${_currentUser?.email ?? 'null'}',
        name: _logName);
    _authStateController.add(SupabaseAuthState(
      isAuthenticated: isAuthenticated,
      user: _currentUser,
      isLoading: false,
    ));
  }

  /// Sign in with Google OAuth using native integration or Windows browser
  Future<User?> signInWithGoogle() async {
    appLog.debug('START: Attempting to sign in with Google', name: _logName);

    try {
      // Use Windows-specific OAuth flow when running on Windows
      if (Platform.isWindows) {
        appLog.debug('Using Windows-specific Google sign-in flow', name: _logName);
        final windowsOAuthService = WindowsOAuthService();
        final user = await windowsOAuthService.signInWithGoogle();

        if (user != null) {
          _currentUser = user;

          // Update user metadata if needed
          await _updateUserMetadataIfNeeded(_currentUser);

          // Handle first-time sign-in
          await handleFirstTimeSignIn();

          appLog.debug('END: Successfully signed in with Google OAuth via Windows browser',
              name: _logName);
          return _currentUser;
        } else {
          appLog.debug('END: Windows browser Google sign-in was canceled or failed',
              name: _logName);
          return null;
        }
      }

      // Default mobile flow for other platforms
      final GoogleSignIn googleSignIn = GoogleSignIn(serverClientId: serverClientId);

      // Attempt to sign in with Google
      final GoogleSignInAccount? googleUser = await googleSignIn.signIn();
      if (googleUser == null) {
        appLog.debug('END: Google sign in was canceled by user', name: _logName);
        return null;
      }

      appLog.debug('Google sign in successful for: ${googleUser.email}', name: _logName);

      // Get auth tokens
      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;
      final idToken = googleAuth.idToken;
      final accessToken = googleAuth.accessToken;

      if (idToken != null) {
        try {
          // Try to sign in with OAuth
          final response = await _client.auth.signInWithIdToken(
            provider: OAuthProvider.google,
            idToken: idToken,
            accessToken: accessToken,
          );

          _currentUser = response.user;

          // Update user metadata if needed
          await _updateUserMetadataIfNeeded(_currentUser);

          // Handle first-time sign-in
          await handleFirstTimeSignIn();

          appLog.debug('END: Successfully signed in with Google OAuth', name: _logName);
          return _currentUser;
        } catch (e) {
          appLog.error('END: OAuth sign in failed', name: _logName, error: e);
        }
      } else {
        appLog.error('END: OAuth sign in failed: idToken is null', name: _logName);
      }

      // This line should never be reached, but added to satisfy the linter
      return null;
    } catch (e, stack) {
      appLog.error('END: Google sign in failed', name: _logName, error: e, stackTrace: stack);
      return null;
    }
  }

  /// Update user metadata if needed
  Future<void> _updateUserMetadataIfNeeded(User? user) async {
    try {
      if (user == null) return;

      // Get user information from the user object
      final email = user.email;
      final displayName = user.userMetadata?['display_name'] ??
          user.userMetadata?['full_name'] ??
          (email != null ? email.split('@').first : '');
      final photoUrl = user.userMetadata?['photo_url'] ?? user.userMetadata?['avatar_url'];

      // Check if we need to update user metadata
      bool needsUpdate = false;
      Map<String, dynamic> metadata = {};

      // Ensure consistent metadata field names
      if (user.userMetadata?['full_name'] == null && displayName.isNotEmpty) {
        metadata['full_name'] = displayName;
        needsUpdate = true;
      }

      if (user.userMetadata?['avatar_url'] == null && photoUrl != null) {
        metadata['avatar_url'] = photoUrl;
        needsUpdate = true;
      }

      if (needsUpdate) {
        appLog.debug('Updating user metadata for ${user.email}', name: _logName);
        await _client.auth.updateUser(UserAttributes(
          data: metadata,
        ));
        appLog.debug('User metadata updated successfully', name: _logName);
      }
    } catch (e) {
      appLog.error('Failed to update user metadata: ${e.toString()}', name: _logName);
      // Don't rethrow as this is not critical
    }
  }

  /// Sign in with Apple OAuth using native integration
  Future<User?> signInWithApple() async {
    try {
      appLog.debug('START: Signing in with Apple', name: _logName);

      // Check if Supabase is initialized
      if (!_isInitialized) {
        appLog.error('Supabase not initialized', name: _logName);
        throw Exception('Supabase client not initialized. Call initialize() first.');
      }

      // Generate a random string for the nonce
      final rawNonce = _generateRandomString();
      final nonce = _sha256ofString(rawNonce);

      // Request Apple credentials with platform-specific options
      final appleCredential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
        nonce: nonce,
        // Add web authentication options for Android
        webAuthenticationOptions: WebAuthenticationOptions(
          clientId: Platform.isAndroid ? 'ai.promz.services' : 'ai.promz.app',
          redirectUri: Uri.parse('https://djqmdekosqnyxoqujxpm.supabase.co/auth/v1/callback'),
        ),
      );

      appLog.debug('Received Apple credential', name: _logName);

      // If no credential was returned, the user canceled the sign-in flow
      if (appleCredential.identityToken == null) {
        appLog.debug('Apple sign in canceled by user', name: _logName);
        return null;
      }

      appLog.debug('Calling Supabase auth.signInWithIdToken', name: _logName);

      // Sign in to Supabase with the Apple OAuth credential
      final authResponse = await _client.auth.signInWithIdToken(
        provider: OAuthProvider.apple,
        idToken: appleCredential.identityToken!,
        nonce: rawNonce,
      );

      // Update the current user
      _currentUser = authResponse.user;

      appLog.debug('Apple sign in successful: ${_currentUser?.email}', name: _logName);

      // Update user metadata if needed
      await _updateUserMetadataIfNeeded(_currentUser);

      // Handle first-time sign-in
      await handleFirstTimeSignIn();

      return _currentUser;
    } catch (e, stack) {
      appLog.error('Apple sign in failed: ${e.toString()}',
          name: _logName, error: e, stackTrace: stack);
      return null;
    }
  }

  /// Sign in with Microsoft OAuth
  Future<User?> signInWithMicrosoft() async {
    try {
      appLog.debug('START: Signing in with Microsoft', name: _logName);

      // Check if Supabase is initialized
      if (!_isInitialized) {
        appLog.error('Supabase not initialized', name: _logName);
        throw Exception('Supabase client not initialized. Call initialize() first.');
      }

      // Use Windows-specific OAuth flow when running on Windows
      if (Platform.isWindows) {
        appLog.debug('Using Windows-specific Microsoft sign-in flow', name: _logName);
        final windowsOAuthService = WindowsOAuthService();
        final user = await windowsOAuthService.signInWithMicrosoft();

        if (user != null) {
          _currentUser = user;

          // Update user metadata if needed
          await _updateUserMetadataIfNeeded(_currentUser);

          // Handle first-time sign-in
          await handleFirstTimeSignIn();

          appLog.debug('END: Successfully signed in with Microsoft OAuth via Windows browser',
              name: _logName);
          return _currentUser;
        } else {
          appLog.debug('END: Windows browser Microsoft sign-in was canceled or failed',
              name: _logName);
          return null;
        }
      }

      // Default mobile/web flow for other platforms
      final success = await _client.auth.signInWithOAuth(
        OAuthProvider.azure,
        queryParams: {
          'prompt': 'select_account', // Force account selection each time
        },
        scopes: 'email profile offline_access User.Read', // More comprehensive approach
      );

      // Success only means the OAuth flow was initiated successfully
      // The actual authentication happens in the browser/OAuth provider
      if (success) {
        appLog.debug('Microsoft OAuth flow initiated successfully', name: _logName);

        // At this point, the browser has opened and the user is authenticating with Microsoft
        // The auth state change will be handled by the auth state listener in initialize()

        // We'll wait for a short period to allow the auth flow to complete and the auth state to update
        // Note: This is not a robust solution, but it works for a simple implementation
        // A better approach would be to use a completer and resolve it when the auth state changes
        await Future.delayed(const Duration(seconds: 5));

        // Get the current user after the auth flow completes
        _currentUser = _client.auth.currentUser;

        if (_currentUser != null) {
          // Update user metadata if needed
          await _updateUserMetadataIfNeeded(_currentUser);

          // Handle first-time sign-in
          await handleFirstTimeSignIn();

          appLog.debug('END: Successfully signed in with Microsoft: ${_currentUser!.email}',
              name: _logName);
          return _currentUser;
        } else {
          appLog.debug('END: Microsoft sign-in flow completed but no user was returned',
              name: _logName);
        }
      } else {
        appLog.debug('END: Failed to initiate Microsoft sign-in flow', name: _logName);
      }

      return null;
    } catch (e, stack) {
      appLog.error('END: Microsoft sign in failed: ${e.toString()}',
          name: _logName, error: e, stackTrace: stack);
      return null;
    }
  }

  /// Generates a cryptographically secure random string
  String _generateRandomString([int length = 32]) {
    const charset = '0123456789ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvwxyz-._';
    final random = Random.secure();
    return List.generate(length, (_) => charset[random.nextInt(charset.length)]).join();
  }

  /// Returns the sha256 hash of [input] in hex notation.
  String _sha256ofString(String input) {
    final bytes = utf8.encode(input);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// Sign up with email and password
  Future<User?> signUpWithEmail({
    required String email,
    required String password,
    Map<String, dynamic>? userData,
  }) async {
    try {
      appLog.debug('Signing up with email', name: _logName);

      await _client.auth.signUp(
        email: email,
        password: password,
        data: userData,
      );

      _currentUser = _client.auth.currentUser;

      appLog.debug('Sign up successful: ${_currentUser?.email}', name: _logName);

      return _currentUser;
    } catch (e, stack) {
      appLog.error('Sign up failed', name: _logName, error: e, stackTrace: stack);
      rethrow;
    }
  }

  /// Sign out the current user
  Future<void> signOut() async {
    try {
      appLog.debug('Signing out', name: _logName);

      // Clear all user-related data from secure storage
      // This uses our new method that handles both profile and session data
      await SecureStorageService.clearUserData();

      // Sign out from Supabase
      await _client.auth.signOut();
      _currentUser = null;

      // Clear in-memory API key reference
      // Note: We're not deleting the API key from secure storage
      // as it might be needed for future sign-ins
      _inMemoryApiKey = null;

      appLog.debug('Sign out successful', name: _logName);
    } catch (e, stack) {
      appLog.error('Sign out failed', name: _logName, error: e, stackTrace: stack);
      rethrow;
    }
  }

  /// Get user profile data
  Future<Map<String, dynamic>?> getUserProfile() async {
    if (_currentUser == null) {
      appLog.warning('Cannot get profile: No user is logged in', name: _logName);
      return null;
    }

    try {
      appLog.debug('Getting user profile', name: _logName);

      final response = await _client.from('profiles').select().eq('id', _currentUser!.id).single();

      appLog.debug('Got user profile', name: _logName);

      return response;
    } catch (e, stack) {
      appLog.error('Failed to get user profile', name: _logName, error: e, stackTrace: stack);
      return null;
    }
  }

  /// Store API key securely
  Future<bool> storeApiKey(String apiKey) async {
    try {
      // Skip if the API key is already stored in memory
      if (_inMemoryApiKey == apiKey) {
        appLog.debug('API key already stored in memory, skipping storage', name: _logName);
        return true;
      }

      appLog.debug('Storing API key in memory', name: _logName);

      // Always store in memory first for immediate access
      _inMemoryApiKey = apiKey;

      // Don't block the UI with secure storage operations
      // Instead, do it in a fire-and-forget manner
      _storeApiKeyInSecureStorage(apiKey).then((_) {
        // Success is handled in the method
      }).catchError((e) {
        // Error is handled in the method
      });

      return true;
    } catch (e, stack) {
      appLog.error('Failed to store API key', name: _logName, error: e, stackTrace: stack);
      return false;
    }
  }

  /// Internal method to store API key in secure storage without blocking
  Future<void> _storeApiKeyInSecureStorage(String apiKey) async {
    try {
      appLog.debug('Attempting to store API key in secure storage', name: _logName);

      // Use the unified SecureStorageService
      await SecureStorageService.saveApiKey(apiKey);

      appLog.debug('API key stored successfully in secure local storage', name: _logName);
    } catch (storageError) {
      appLog.warning('Failed to use secure storage: $storageError', name: _logName);
      // Already stored in memory, so we're good
    }
  }

  /// Retrieve the stored API key
  Future<String?> getStoredApiKey() async {
    if (_currentUser == null) {
      appLog.warning('Cannot get API key: No user is logged in', name: _logName);
      return null;
    }

    try {
      appLog.debug('Retrieving stored API key', name: _logName);

      // Check in-memory first for best performance
      if (_inMemoryApiKey != null && _inMemoryApiKey!.isNotEmpty) {
        appLog.debug('API key retrieved from in-memory storage', name: _logName);
        return _inMemoryApiKey;
      }

      // Try to get from secure storage if not in memory
      try {
        final apiKey = await SecureStorageService.getApiKey();

        if (apiKey != null && apiKey.isNotEmpty) {
          appLog.debug('API key retrieved from secure storage', name: _logName);
          // Update in-memory cache
          _inMemoryApiKey = apiKey;
          return apiKey;
        }
      } catch (storageError) {
        appLog.warning('Failed to read from secure storage: $storageError', name: _logName);
      }

      return null;
    } catch (e, stack) {
      appLog.error('Failed to retrieve API key', name: _logName, error: e, stackTrace: stack);
      return null;
    }
  }

  /// Check and refresh the current session if needed
  Future<bool> checkAndRefreshSession() async {
    if (!_isInitialized) {
      appLog.error('Supabase not initialized', name: _logName);
      return false;
    }

    try {
      // For debugging: manually check secure storage contents
      await _debugCheckSecureStorage();

      // Check if there's an existing session
      final session = _client.auth.currentSession;
      _currentUser = _client.auth.currentUser;

      appLog.debug('Current session: ${session != null ? 'Present' : 'Null'}', name: _logName);
      appLog.debug('Current user: ${_currentUser != null ? _currentUser!.email : 'Null'}',
          name: _logName);

      if (_currentUser != null && session != null) {
        appLog.debug('Found existing session for user: ${_currentUser!.email}', name: _logName);
        appLog.debug('Session access token: ${session.accessToken.substring(0, 10)}...',
            name: _logName);
        appLog.debug('Session refresh token: ${session.refreshToken?.substring(0, 5)}...',
            name: _logName);

        // Check if the session is close to expiry (within 1 hour)
        final now = DateTime.now().millisecondsSinceEpoch / 1000;
        const oneHourInSeconds = 60 * 60;

        if (session.expiresAt != null) {
          final timeToExpiry = session.expiresAt! - now;
          appLog.debug('Session expires in ${(timeToExpiry / 60).toStringAsFixed(2)} minutes',
              name: _logName);

          if (timeToExpiry < oneHourInSeconds) {
            // Session is close to expiry, refresh it
            appLog.debug('Session is close to expiry, refreshing token', name: _logName);
            try {
              // Use the improved refreshSession method
              final response = await _client.auth.refreshSession();
              if (response.session != null) {
                _currentUser = response.user;
                appLog.debug('Token refreshed, new expiry: ${response.session!.expiresAt}',
                    name: _logName);
                return true;
              } else {
                appLog.error('Failed to refresh token: No session returned', name: _logName);
              }
            } catch (e) {
              appLog.error('Error refreshing token: $e', name: _logName);
              // Continue with the existing session even if refresh fails
            }
          } else {
            // Session is still valid
            appLog.debug('Session is still valid', name: _logName);
            return true;
          }
        }

        // Session exists but might not have an expiry
        return true;
      } else {
        appLog.debug('No valid session found', name: _logName);
        return false;
      }
    } catch (e) {
      appLog.error('Error checking session: $e', name: _logName);
      return false;
    }
  }

  /// Debug method to check secure storage contents
  Future<void> _debugCheckSecureStorage() async {
    try {
      appLog.debug('Checking secure storage contents using unified service', name: _logName);

      // Check if API key exists
      final hasApiKey = await SecureStorageService.hasApiKey();
      appLog.debug('Secure storage contains API key: $hasApiKey', name: _logName);

      // Check if the session exists
      final hasSession = await SecureStorageService.hasSupabaseSession();
      appLog.debug('Secure storage contains Supabase session: $hasSession', name: _logName);

      // Check if user profile is cached
      final hasProfile = await SecureStorageService.hasCachedUserProfile();
      appLog.debug('Secure storage contains cached user profile: $hasProfile', name: _logName);
    } catch (e) {
      appLog.error('Error checking secure storage: $e', name: _logName);
    }
  }

  /// Get the current authentication token
  /// Returns the access token from the current session or null if not authenticated
  Future<String> getAuthToken() async {
    try {
      // Get the current session token
      final session = _client.auth.currentSession;
      if (session != null) {
        return session.accessToken;
      }

      // If no session, return empty string
      return '';
    } catch (e, stack) {
      appLog.error('Failed to get auth token', name: _logName, error: e, stackTrace: stack);
      return '';
    }
  }

  /// Update and cache current user profile information
  Future<void> cacheUserProfileInfo() async {
    try {
      appLog.debug('Caching user profile information', name: _logName);
      final user = _currentUser;
      if (user == null) {
        appLog.debug('No user to cache profile for', name: _logName);
        return;
      }

      // Extract user details from user object
      final userId = user.id;
      final email = user.email;
      final displayName = user.userMetadata?['full_name'] ??
          user.userMetadata?['display_name'] ??
          (email != null ? email.split('@').first : '');
      final avatarUrl = user.userMetadata?['avatar_url'] ?? user.userMetadata?['photo_url'];

      // Cache profile in secure storage
      await SecureStorageService.cacheUserProfile(
        userId: userId,
        email: email,
        displayName: displayName?.toString(),
        avatarUrl: avatarUrl?.toString(),
      );

      appLog.debug('User profile cached successfully', name: _logName);
    } catch (e) {
      appLog.error('Failed to cache user profile', name: _logName, error: e);
      // Don't rethrow as this is non-critical functionality
    }
  }

  /// Handle first-time sign-in or sign-in after reinstall
  /// This ensures we have a clean state for the user
  Future<void> handleFirstTimeSignIn() async {
    try {
      appLog.debug('Handling first-time sign-in or sign-in after reinstall', name: _logName);

      // Check if we have a user
      if (_currentUser == null) {
        appLog.debug('No user to handle first-time sign-in for', name: _logName);
        return;
      }

      // Clear any existing data to ensure a clean state
      // This helps prevent issues with leftover data from previous installations
      await SecureStorageService.deleteAll();
      appLog.debug('Cleared all secure storage for fresh start', name: _logName);

      // Cache the user profile
      await cacheUserProfileInfo();

      // If we have an API key in memory, save it to secure storage
      if (_inMemoryApiKey != null) {
        await SecureStorageService.saveApiKey(_inMemoryApiKey!);
      }

      appLog.debug('First-time sign-in handled successfully', name: _logName);
    } catch (e) {
      appLog.error('Failed to handle first-time sign-in', name: _logName, error: e);
      // Don't rethrow as this is non-critical functionality
    }
  }

  /// Clear cached user profile on sign out
  Future<void> clearCachedProfile() async {
    try {
      appLog.debug('Clearing cached user profile', name: _logName);
      await SecureStorageService.deleteCachedUserProfile();
      appLog.debug('Cached user profile cleared', name: _logName);
    } catch (e) {
      appLog.error('Failed to clear cached user profile', name: _logName, error: e);
      // Don't rethrow as this is non-critical functionality
    }
  }

  /// Get the cached user profile with minimal information needed for UI
  Future<Map<String, dynamic>?> getCachedUserProfile() async {
    try {
      appLog.debug('Retrieving cached user profile', name: _logName);
      return await SecureStorageService.getCachedUserProfile();
    } catch (e) {
      appLog.error('Failed to get cached user profile', name: _logName, error: e);
      return null;
    }
  }
}
