import 'package:flutter/services.dart';
import 'package:promz/core/utils/hash_helper.dart';
import 'package:promz/features/input_selection/models/input_source.dart';
import 'package:promz_common/promz_common.dart';
import 'package:uuid/uuid.dart';

class ClipboardService {
  static const String _logName = 'ClipboardService';

  /// Checks if clipboard has text content
  Future<bool> hasClipboardText() async {
    try {
      final ClipboardData? data = await Clipboard.getData(Clipboard.kTextPlain);
      return data?.text?.isNotEmpty ?? false;
    } catch (e, stack) {
      appLog.error('Error checking clipboard content', name: _logName, error: e, stackTrace: stack);
      return false;
    }
  }

  /// Gets clipboard content as an InputSource
  Future<InputSource?> getClipboardAsSource() async {
    try {
      final ClipboardData? data = await Clipboard.getData(Clipboard.kTextPlain);

      if (data?.text == null || data!.text!.isEmpty) {
        appLog.warning('Clipboard is empty', name: _logName);
        return null;
      }

      final content = data.text!;
      final timestamp = DateTime.now().toString().substring(0, 19); // YYYY-MM-DD HH:MM:SS
      final contentHash = HashHelper.calculateStringHash(content);

      return InputSource(
        id: const Uuid().v4(),
        type: InputSourceType.clipboard,
        fileName: 'Clipboard Text $timestamp',
        content: content,
        mimeType: 'text/plain',
        contentHash: contentHash,
      );
    } catch (e, stack) {
      appLog.error('Error getting clipboard content', name: _logName, error: e, stackTrace: stack);
      return null;
    }
  }
}
