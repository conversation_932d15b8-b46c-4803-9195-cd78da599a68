import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:promz/app.dart';
import 'package:promz/database/database.dart';
import 'package:promz/database/db_utils.dart';
import 'package:promz/features/home/<USER>/home_viewmodel.dart';
import 'package:promz_common/promz_common.dart';
import 'package:base_codecs/base_codecs.dart';
import 'dart:convert';

/// Service for handling deep links to the app
class DeepLinkService {
  static const _logName = 'DeepLinkService';

  /// Process an incoming link and determine if it's a valid prompt link
  /// Returns a tuple with (isValid, message, promptId)
  Future<(bool, String, String?)> processPromptLink(String url) async {
    appLog.debug('Processing prompt link: $url', name: _logName);

    // Check if the URL is empty
    if (url.isEmpty) {
      appLog.warning('Empty URL provided', name: _logName);
      return (false, 'Empty URL', null);
    }

    // Validate URL format
    if (!url.startsWith('promz://p/') && !url.startsWith('https://www.promz.ai/p/')) {
      appLog.warning('Invalid URL format: $url', name: _logName);
      return (false, 'Invalid prompt link', null);
    }

    // Extract the short ID from the URL
    final shortId = url.split('/').last;
    if (shortId.isEmpty) {
      appLog.warning('Short ID is empty in URL: $url', name: _logName);
      return (false, 'Invalid prompt link format', null);
    }

    // Decode the short ID to a UUID
    String decodedUuid;
    try {
      decodedUuid = utf8.decode(base58BitcoinDecode(shortId));
    } catch (e) {
      appLog.warning('Failed to decode short ID: $shortId', name: _logName, error: e);
      return (false, 'Invalid prompt ID', null);
    }

    appLog.debug('Decoded UUID: $decodedUuid', name: _logName);

    // Check if the prompt exists in the local database
    final db = await AppDatabase.getInstance();
    final prompt = await db.getPrompt(decodedUuid);

    if (prompt != null) {
      // Prompt exists in the database
      appLog.info('Prompt found in local database: $decodedUuid', name: _logName);
      return (true, 'Prompt "${prompt.title}" is available in your library', decodedUuid);
    } else {
      // Prompt doesn't exist yet
      appLog.info('Prompt not found in local database: $decodedUuid', name: _logName);
      return (
        false,
        'Prompt not found in your library. Would you like to search for it online?',
        decodedUuid
      );
    }
  }

  /// Determine the type of deep link and process accordingly
  Future<(bool, String, String?)> processDeepLink(String url) async {
    appLog.debug('Processing deep link: $url', name: _logName);

    // Check if this is a prompt link (promz.ai/p/... or promz://...)
    if (url.contains('/p/') || url.contains('/prompt/') || url.startsWith('promz://')) {
      return processPromptLink(url);
    }

    // Could handle other types of deep links here in the future
    // For example: user profiles, collections, etc.

    // Default case: unknown link type
    return (false, 'Unknown link type', null);
  }

  /// Show a dialog with the result of processing a prompt link
  void showPromptLinkResultDialog(BuildContext context, String message, {String? promptId}) {
    final bool isPromptFound = message.contains('available in your library');
    final bool isPromptNotFound = message.contains('not found');

    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: Text(isPromptFound ? 'Prompt Found' : 'Shared Prompt'),
        content: Text(message),
        actions: [
          if (isPromptNotFound && promptId != null)
            TextButton(
              onPressed: () {
                Navigator.of(dialogContext).pop();
                appLog.debug('User chose to search for prompt online: $promptId', name: _logName);
                if (appNavigatorKey.currentContext != null &&
                    appNavigatorKey.currentContext!.mounted) {
                  _fetchPromptFromServer(appNavigatorKey.currentContext!, promptId);
                } else {
                  appLog.warning(
                      'Cannot fetch prompt from server: appNavigatorKey.currentContext is null or unmounted',
                      name: _logName);
                }
              },
              child: const Text('Search Online'),
            ),
          if (isPromptFound && promptId != null)
            TextButton(
              onPressed: () {
                final stableContext = appNavigatorKey.currentContext;
                Navigator.of(dialogContext).pop();

                if (stableContext != null && stableContext.mounted) {
                  _navigateToPromptDetail(stableContext, promptId);
                } else {
                  appLog.warning(
                      'Cannot navigate to prompt detail: stableContext is null or unmounted',
                      name: _logName);
                }
                appLog.debug('User chose to view prompt: $promptId', name: _logName);
              },
              child: const Text('View Prompt'),
            ),
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  /// Navigate to the prompt detail view for a specific prompt
  void _navigateToPromptDetail(BuildContext context, String promptId) async {
    try {
      if (!context.mounted) {
        appLog.warning('Context for _navigateToPromptDetail is not mounted at start.',
            name: _logName);
        return;
      }

      final db = await AppDatabase.getInstance();
      final prompt = await db.getPrompt(promptId);

      if (!context.mounted) {
        appLog.warning('Context for _navigateToPromptDetail unmounted after db.getPrompt.',
            name: _logName);
        return;
      }

      if (prompt != null) {
        final promptModel = DatabaseUtils.createPromptModelFromDb(prompt);
        final displayItem = DisplayItem(
          text: prompt.title,
          displayText: prompt.title,
          subtitle: prompt.subtitle,
          type: DisplayItemType.promptTitle,
          prompt: promptModel,
        );

        final container = ProviderScope.containerOf(context);
        final homeViewModel = container.read(homeViewModelProvider);
        await homeViewModel.onPromptSelected(context, displayItem);
      } else {
        appLog.warning('Failed to navigate to prompt: prompt not found', name: _logName);
        if (!context.mounted) {
          appLog.warning(
              'Context for _navigateToPromptDetail unmounted before showing "not found" snackbar.',
              name: _logName);
          return;
        }
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Prompt not found')),
        );
      }
    } catch (e) {
      appLog.error('Error navigating to prompt detail', name: _logName, error: e);
      if (!context.mounted) {
        appLog.warning('Context for _navigateToPromptDetail unmounted in catch block.',
            name: _logName);
        return;
      }
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Error opening prompt')),
      );
    }
  }

  /// Fetch prompt from server (to be implemented)
  void _fetchPromptFromServer(BuildContext context, String promptId) {
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('This feature is not yet available')),
      );
    }
  }
}
