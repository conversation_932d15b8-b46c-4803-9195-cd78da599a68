import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/scheduler.dart';
import 'package:promz/core/services/client_context_service.dart';
import 'package:promz/features/prompts/services/prompt_sync_service.dart';
import 'package:promz_common/promz_common.dart';

/// Service to handle background tasks in the application
class BackgroundTaskService {
  static const _logName = 'BackgroundTaskService';

  /// Singleton instance
  static BackgroundTaskService? _instance;

  /// Flag to prevent multiple initializations
  static bool _isInitialized = false;

  /// Timer for prompt usage sync
  Timer? _promptUsageSyncTimer;

  /// Timer for prompt sync
  Timer? _promptSyncTimer;

  /// Delay between sync attempts (24 hours)
  static const Duration _syncInterval = Duration(hours: 24);

  /// Shorter interval for debug mode (5 minutes)
  static const Duration _debugSyncInterval = Duration(minutes: 5);

  /// Initial delay before starting background tasks (3 seconds)
  static const Duration _initialDelay = Duration(seconds: 3);

  /// Prompt sync interval (6 hours)
  static const Duration _promptSyncInterval = Duration(hours: 6);

  /// Shorter interval for debug mode (10 minutes)
  static const Duration _debugPromptSyncInterval = Duration(minutes: 10);

  /// Get the appropriate sync interval based on debug mode
  Duration get _currentSyncInterval => kDebugMode ? _debugSyncInterval : _syncInterval;

  /// Get the appropriate prompt sync interval based on debug mode
  Duration get _currentPromptSyncInterval =>
      kDebugMode ? _debugPromptSyncInterval : _promptSyncInterval;

  /// Flag to prevent multiple simultaneous sync operations
  bool _isSyncing = false;

  /// Prompt sync service
  PromptSyncService? _promptSyncService;

  /// Initialize the singleton instance
  static void initialize() {
    if (_isInitialized) {
      appLog.debug('BackgroundTaskService already initialized', name: _logName);
      return;
    }

    _instance = BackgroundTaskService._();
    _isInitialized = true;
    appLog.debug('BackgroundTaskService initialized', name: _logName);
  }

  /// Private constructor
  BackgroundTaskService._() {
    // Delay starting background tasks to ensure app is fully initialized
    _scheduleBackgroundTasksStart();
  }

  /// Schedule the start of background tasks after a delay
  void _scheduleBackgroundTasksStart() {
    appLog.debug('Scheduling background tasks to start after app initialization', name: _logName);

    // Use a post-frame callback to ensure the app is rendered before starting tasks
    SchedulerBinding.instance.addPostFrameCallback((_) {
      // Add a small delay to ensure all services are fully initialized
      Timer(_initialDelay, () {
        _startBackgroundTasks();
      });
    });
  }

  /// Factory constructor to access the singleton instance
  factory BackgroundTaskService() {
    if (_instance == null) {
      appLog.debug('BackgroundTaskService not initialized. Call initialize() first.',
          name: _logName);
      throw StateError('BackgroundTaskService not initialized. Call initialize() first.');
    }
    return _instance!;
  }

  /// Start all background tasks
  void _startBackgroundTasks() {
    appLog.debug('Starting all background tasks', name: _logName);
    _startPromptUsageSync();
    _startPromptSync();
  }

  /// Start the prompt usage sync task
  void _startPromptUsageSync() {
    appLog.debug('Starting prompt usage sync task', name: _logName);

    // Run once after a short delay
    Timer(const Duration(seconds: 1), () {
      _syncPromptUsageData();
    });

    // Then schedule periodic execution
    _promptUsageSyncTimer = Timer.periodic(_currentSyncInterval, (_) {
      _syncPromptUsageData();
    });
  }

  /// Start the prompt sync task
  void _startPromptSync() {
    appLog.debug('Starting prompt sync task', name: _logName);

    // Run once after a short delay
    Timer(const Duration(seconds: 5), () {
      _syncPrompts();
    });

    // Then schedule periodic execution
    _promptSyncTimer = Timer.periodic(_currentPromptSyncInterval, (_) {
      _syncPrompts();
    });
  }

  /// Sync prompt usage data with the server
  Future<void> _syncPromptUsageData() async {
    // Prevent multiple simultaneous sync operations
    if (_isSyncing) {
      appLog.debug('Prompt usage sync already in progress, skipping', name: _logName);
      return;
    }

    _isSyncing = true;
    appLog.debug('Starting prompt usage sync', name: _logName);

    try {
      // Safely check if ClientContextService is initialized
      ClientContextService? clientContextService;
      try {
        clientContextService = ClientContextService();
      } catch (e) {
        appLog.warning('ClientContextService not initialized, skipping sync', name: _logName);
        _isSyncing = false;
        return;
      }

      // Safely access the prompt usage service
      final promptUsageService = clientContextService.promptUsage;

      if (promptUsageService.shouldSyncData()) {
        await promptUsageService.syncUsageData();
        appLog.debug('Prompt usage sync completed', name: _logName);
      } else {
        appLog.debug('Skipping prompt usage sync, not needed at this time', name: _logName);
      }
    } catch (e, stackTrace) {
      appLog.error('Error in prompt usage sync task',
          name: _logName, error: e, stackTrace: stackTrace);
    } finally {
      _isSyncing = false;
    }
  }

  /// Force a sync of prompt usage data
  /// This can be called from other parts of the application when needed
  Future<void> forceSyncPromptUsageData() async {
    appLog.debug('Force syncing prompt usage data', name: _logName);
    await _syncPromptUsageData();
  }

  /// Sync prompts with the server
  Future<void> _syncPrompts() async {
    try {
      // Check if ClientContextService is initialized
      try {
        // Just check if we can access the service
        ClientContextService();
      } catch (e) {
        appLog.warning('ClientContextService not initialized, skipping sync', name: _logName);
        return;
      }

      // Initialize the prompt sync service if needed
      _promptSyncService ??= PromptSyncService();
      await _promptSyncService!.initialize();

      // Use the service to sync prompts
      final success = await _promptSyncService!.syncPrompts();

      if (success) {
        appLog.debug('Prompt sync completed successfully', name: _logName);
      } else {
        appLog.warning('Prompt sync completed with issues', name: _logName);
      }
    } catch (e, stackTrace) {
      appLog.error('Error in prompt sync task', name: _logName, error: e, stackTrace: stackTrace);
    }
  }

  /// Force a fetch of popular prompts via the WebSocket service
  /// This can be called from other parts of the application when needed
  Future<void> forceFetchPopularPrompts() async {
    appLog.debug('Force fetching popular prompts via WebSocket', name: _logName);
    try {
      // Check if ClientContextService is initialized
      try {
        // Just check if we can access the service
        final contextService = ClientContextService();
        await contextService.promptUsage.fetchPopularPrompts(true);
      } catch (e) {
        appLog.warning('ClientContextService not initialized, skipping popular prompts fetch',
            name: _logName);
        return;
      }
    } catch (e, stackTrace) {
      appLog.error('Error forcing popular prompts fetch',
          name: _logName, error: e, stackTrace: stackTrace);
    }
  }

  /// Force a sync of prompts
  /// This can be called from other parts of the application when needed
  Future<void> forceSyncPrompts() async {
    appLog.debug('Force syncing prompts', name: _logName);
    await _syncPrompts();
  }

  /// Stop all background tasks
  void stopAllTasks() {
    appLog.debug('Stopping all background tasks', name: _logName);
    _promptUsageSyncTimer?.cancel();
    _promptUsageSyncTimer = null;
    // Popular prompts now handled via WebSockets
    _promptSyncTimer?.cancel();
    _promptSyncTimer = null;
  }

  /// Dispose resources
  void dispose() {
    stopAllTasks();
    _promptSyncService?.dispose();
    _instance = null;
    _isInitialized = false;
    appLog.debug('BackgroundTaskService disposed', name: _logName);
  }
}
