import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
// ignore: implementation_imports
import 'package:fixnum/src/int64.dart';
import 'package:path/path.dart' as path;
import 'package:promz/core/models/content_processing_task.dart';
import 'package:promz/core/services/attachment/attachment_registry_service.dart';
import 'package:promz/core/services/client_context_service.dart';
import 'package:promz/core/services/content_processing_task_manager.dart';
import 'package:promz/core/services/file/containers/zip_service.dart';
import 'package:promz/core/services/file/file_processing_client.dart';
import 'package:promz/core/services/file/file_upload_service.dart';
import 'package:promz/core/services/file_processing_service.dart';
import 'package:promz/core/services/license_manager_service.dart';
import 'package:promz/core/utils/duplicate_detection_registry.dart';
import 'package:promz/core/utils/hash_helper.dart';
import 'package:promz/core/utils/url_extractor.dart';
import 'package:promz/core/utils/url_resolver.dart';
import 'package:promz/features/home/<USER>/entity_detection_service.dart';
import 'package:promz/features/input_selection/models/input_source.dart';
import 'package:promz/features/news/services/news_article_service.dart';
import 'package:promz/features/youtube/services/youtube_service.dart';
import 'package:promz/generated/common.pbenum.dart';
import 'package:promz/generated/content_upload.pb.dart';
import 'package:promz_common/promz_common.dart';

part 'content_processing/url_processing.dart';
part 'content_processing/file_processing.dart';
part 'content_processing/text_processing.dart';
part 'content_processing/input_processing.dart';
part 'content_processing/youtube_processing.dart';
part 'content_processing/article_processing.dart';
part 'content_processing/task_management.dart';
part 'content_processing/variable_processing.dart';

/// Result of URL resolution that contains final URL and content type information
class UrlResolutionResult {
  final String originalUrl;
  final String finalUrl;
  final bool isYouTubeVideo;
  final bool isNewsArticle;

  UrlResolutionResult({
    required this.originalUrl,
    required this.finalUrl,
    required this.isYouTubeVideo,
    required this.isNewsArticle,
  });
}

/// Result of content type detection for consistent processing decisions
class ContentTypeDetectionResult {
  /// The primary detected content type
  final ContentType primaryType;

  /// Whether the content appears to be a YouTube video
  final bool isYouTubeVideo;

  /// Whether the content appears to be a news article
  final bool isNewsArticle;

  /// Extracted YouTube URL if detected
  final String? youtubeUrl;

  /// Extracted news article URL if detected
  final String? newsUrl;

  /// Original content (might be truncated for logging)
  final String originalContent;

  ContentTypeDetectionResult({
    required this.primaryType,
    required this.isYouTubeVideo,
    required this.isNewsArticle,
    required this.originalContent,
    this.youtubeUrl,
    this.newsUrl,
  });

  /// Creates a new instance with updated fields
  ContentTypeDetectionResult copyWith({
    ContentType? primaryType,
    bool? isYouTubeVideo,
    bool? isNewsArticle,
    String? youtubeUrl,
    String? newsUrl,
    String? originalContent,
  }) {
    return ContentTypeDetectionResult(
      primaryType: primaryType ?? this.primaryType,
      isYouTubeVideo: isYouTubeVideo ?? this.isYouTubeVideo,
      isNewsArticle: isNewsArticle ?? this.isNewsArticle,
      originalContent: originalContent ?? this.originalContent,
      youtubeUrl: youtubeUrl ?? this.youtubeUrl,
      newsUrl: newsUrl ?? this.newsUrl,
    );
  }

  @override
  String toString() {
    return 'ContentTypeDetectionResult{primaryType: $primaryType, '
        'isYouTubeVideo: $isYouTubeVideo, isNewsArticle: $isNewsArticle}';
  }
}

/// Primary content type enum with clear priority ordering
enum ContentType {
  youtubeVideo, // YouTube video content (highest priority)
  newsArticle, // News article content
  file, // File-based content
  text, // Plain text content
  unknown, // Unknown content type (lowest priority)
}

/// Status of content processing
enum ContentProcessingStatus {
  /// Processing started
  started,

  /// Processing large file
  processingLargeFile,

  /// Processing completed successfully
  completed,

  /// Processing failed due to network error
  networkError,

  /// Processing failed due to timeout
  timeout,

  /// Processing failed due to authentication error
  authRequired,

  /// Processing failed due to file size limit exceeded
  fileSizeLimitExceeded,

  /// Processing failed due to other error
  error
}

/// Service for processing different types of content (files, URLs, text)
/// and registering them with the appropriate registry services
class ContentProcessingService {
  static const _logName = 'ContentProcessingService';

  // Size threshold to use server-side processing when threshold is exceeded
  // All files are now processed on the server to ensure content is available for prompt execution

  // Helper method to get current license tier string
  String _getCurrentLicenseTier() {
    final currentLicense = _licenseManagerService.currentLicense;
    if (currentLicense == null) {
      return 'free';
    }

    // Convert enum to string and get the last part (e.g., LicenseType.pro -> 'pro')
    return currentLicense.licenseType.toString().split('.').last.toLowerCase();
  }

  /// Validate file size before upload
  /// This method calls the server to validate the file size against the user's license tier
  /// Throws FileSizeLimitExceededException if the file size exceeds the limit
  Future<void> validateFileSize(File file, {String? licenseTier}) async {
    final fileSize = await file.length();
    final fileName = path.basename(file.path);

    // Get current license tier if not provided
    final tier = licenseTier ?? _getCurrentLicenseTier();

    try {
      appLog.debug('Validating file size before upload: $fileName, size: $fileSize bytes',
          name: _logName);

      // Call the FileUploadService to validate the file size
      // This will throw an exception if validation fails
      await _fileUploadService.validateFileSize(
        fileName: fileName,
        fileSize: fileSize,
        licenseTier: tier,
      );

      appLog.debug('File size validation passed', name: _logName);
    } catch (e) {
      appLog.warning('File size validation failed: ${e.toString()}', name: _logName);
      rethrow;
    }
  }

  final FileProcessingService _fileProcessingService;
  final AttachmentRegistryService _attachmentRegistry;
  final NewsArticleService _newsArticleService;
  final ZipService _zipService;
  final YouTubeService _youtubeService;
  final EntityDetectionService _entityDetectionService;
  final LicenseManagerService _licenseManagerService;
  final FileUploadService _fileUploadService;
  final FileProcessingClient _fileProcessingClient;
  final _processingStatusController = StreamController<ContentProcessingStatus>.broadcast();
  final _authRequiredController = StreamController<void>.broadcast();
  final _fileSizeLimitController = StreamController<Exception>.broadcast();

  // List of currently available sources (used for duplicate detection)
  final List<InputSource> _sources = [];

  // Stream controller for source processed events
  final _sourceProcessedController = StreamController<InputSource>.broadcast();

  late final ContentProcessingTaskManager _taskManager;

  ContentProcessingService({
    required FileProcessingService fileProcessingService,
    required AttachmentRegistryService attachmentRegistry,
    required NewsArticleService newsArticleService,
    required ZipService zipService,
    required YouTubeService youtubeService,
    required FileUploadService fileUploadService,
    required LicenseManagerService licenseManagerService,
    required FileProcessingClient fileProcessingClient,
    required EntityDetectionService entityDetectionService,
  })  : _fileProcessingService = fileProcessingService,
        _attachmentRegistry = attachmentRegistry,
        _newsArticleService = newsArticleService,
        _zipService = zipService,
        _youtubeService = youtubeService,
        _entityDetectionService = entityDetectionService,
        _licenseManagerService = licenseManagerService,
        _fileUploadService = fileUploadService,
        _fileProcessingClient = fileProcessingClient,
        _taskManager = ContentProcessingTaskManager.instance;

  /// Process input that could be a file path, URL, or direct text content
  /// Returns an InputSource if processing was successful, or null if it failed
  /// A non-null return value indicates success (either a new source was created or an existing duplicate was found)
  /// A null return value indicates a processing failure (e.g., network error, invalid input, license issue)
  Future<InputSource?> processInput(String input) async {
    appLog.debug('Processing input: ${truncateForLogging(input)}', name: _logName);

    if (!_licenseManagerService.isLicenseValid()) {
      appLog.warning('No valid license found. Cannot process content.', name: _logName);
      throw Exception('License required: Please sign in to process content.');
    }

    try {
      // Detect content type in one place with consistent priorities
      final detection = await _detectContentType(input);
      appLog.debug('Content detection result: $detection', name: _logName);

      // Process based on detected primary content type to extract raw data
      Map<String, dynamic> extractedData = {};

      switch (detection.primaryType) {
        case ContentType.youtubeVideo:
          appLog.debug('Processing as YouTube video', name: _logName);
          // Use the detected YouTube URL if available
          final youtubeUrl = detection.youtubeUrl;

          if (youtubeUrl != null) {
            // For now, use the legacy method but we'll create the internal version soon
            final result = await _processYouTubeVideo(input, extractedYouTubeUrl: youtubeUrl);
            if (result != null) {
              // The result is already a Map<String, dynamic>
              extractedData = result;
              break;
            }

            appLog.debug('YouTube processing failed, falling back to next content type',
                name: _logName);
            // Fall through to next case
          } else {
            appLog.debug('No YouTube URL found, falling back to next content type', name: _logName);
            // Fall through to next case
          }
        // Intentional fall-through if YouTube processing fails or service isn't available

        case ContentType.newsArticle:
          appLog.debug('Processing as news article', name: _logName);
          // For now, use the legacy method but we'll create the internal version soon
          final newsResult = await _processNewsArticle(input, extractedUrl: detection.newsUrl);
          // The result is already a Map<String, dynamic>
          extractedData = newsResult;
          break;

        case ContentType.file:
          appLog.debug('Processing as file', name: _logName);
          final fileName = input.split(RegExp(r'[/\\]')).last;
          final mimeType = _fileProcessingService.getMimeType(fileName);
          // For now, use the legacy method but we'll create the internal version soon
          final fileResult = await _processFile(input, fileName, mimeType);
          // The result is already a Map<String, dynamic>
          extractedData = fileResult;
          // Add file path for duplicate detection if not already present
          if (!extractedData.containsKey('filePath')) {
            extractedData['filePath'] = input;
          }
          break;

        default:
          // For text or unknown types, use _categorizeInput to handle more specific logic
          appLog.debug('Processing as text or unknown type', name: _logName);
          // For now, use the legacy method but we'll create the internal version soon
          final inputType = await _categorizeInput(input);

          switch (inputType.type) {
            case InputType.url:
              // Check if this has an extracted URL in metadata
              if (inputType.processingResult != null &&
                  inputType.processingResult!.sourceUrl.isNotEmpty) {
                final extractedUrl = inputType.processingResult!.sourceUrl;
                appLog.debug('Processing URL type with extracted URL: $extractedUrl',
                    name: _logName);
                final urlResult = await _processNewsArticle(input, extractedUrl: extractedUrl);
                // The result is already a Map<String, dynamic>
                extractedData = urlResult;
              } else {
                final urlResult = await _processUrlInput(inputType.originalInput);
                // The result is already a Map<String, dynamic>
                extractedData = urlResult;
              }
              break;

            case InputType.file:
              final fileResult = await _processFile(
                  inputType.originalInput, inputType.fileName!, inputType.mimeType!);
              // The result is already a Map<String, dynamic>
              extractedData = fileResult;
              // Add file path for duplicate detection if not already present
              if (!extractedData.containsKey('filePath')) {
                extractedData['filePath'] = inputType.originalInput;
              }
              break;

            case InputType.text:
              // Use the new internal method
              extractedData = await _processText(inputType.content!, inputType.fileName!);
              break;
          }

          // If no case matched or processing failed, use default text processing
          if (extractedData.isEmpty) {
            extractedData = await _processText(input, 'Text Content');
          }
      }

      // If we have no extracted data, processing failed
      if (extractedData.isEmpty) {
        appLog.warning('Processing failed: No data extracted', name: _logName);
        return null;
      }

      // Check for duplicates using the content hash
      final String? contentHash = extractedData['contentHash'];
      final String? filePath = extractedData['filePath'];

      if (contentHash != null && contentHash.isNotEmpty) {
        // First check the centralized registry for recent duplicates
        if (DuplicateDetectionRegistry.instance.hashExists(contentHash)) {
          appLog.debug('Recently processed this exact content, checking for existing source',
              name: _logName);

          // Look for an existing source with this hash
          for (final source in _sources) {
            if (source.contentHash == contentHash) {
              appLog.debug(
                  'Found existing source with matching hash: ${source.fileName ?? "Unknown"}',
                  name: _logName);
              return source; // Return the existing source
            }
          }
        }

        // Check against existing sources
        for (final source in _sources) {
          final hashMatch = source.contentHash == contentHash;
          final pathMatch = filePath != null &&
              source.filePath != null &&
              File(source.filePath!).absolute.path == File(filePath).absolute.path;

          if (hashMatch || pathMatch) {
            appLog.debug('Duplicate detected: ${source.fileName ?? "Unknown"}', name: _logName);
            return source; // Return the existing source
          }
        }

        // Register the hash with the centralized registry
        DuplicateDetectionRegistry.instance.registerHash(contentHash);
      }

      // Create a new InputSource from the extracted data
      final InputSource newSource = InputSource(
        // Convert the sourceType string back to an enum value
        type: InputSourceType.values.firstWhere(
          (e) => e.name == extractedData['sourceType'],
          orElse: () => InputSourceType.text, // Default to text if not found
        ),
        filePath: extractedData['filePath'],
        fileName: extractedData['fileName'],
        content: extractedData['content'],
        mimeType: extractedData['mimeType'],
        processingResult: extractedData['processingResult'],
        contentHash: contentHash,
        sourceApp: extractedData['sourceApp'],
      );

      // Add the new source to our internal list for future duplicate detection
      _sources.add(newSource);

      // Notify listeners about the new source
      notifySourceProcessed(newSource);

      return newSource;
    } catch (e, stack) {
      appLog.error('Error in processInput', name: _logName, error: e, stackTrace: stack);
      return null; // Return null to indicate processing failure
    }
  }

  Stream<ContentProcessingStatus> get processingStatus => _processingStatusController.stream;
  Stream<void> get authenticationRequired => _authRequiredController.stream;
  Stream<Exception> get fileSizeLimitExceeded => _fileSizeLimitController.stream;

  /// Stream of source processed events
  /// Emits an InputSource when a source is fully processed and ready to be displayed
  Stream<InputSource> get sourceProcessed => _sourceProcessedController.stream;

  // Add this method to dispose resources
  void dispose() {
    _processingStatusController.close();
    _authRequiredController.close();
    _fileSizeLimitController.close();
    _sourceProcessedController.close();
  }

  /// Detect content type with consistent priority rules
  /// This is the central point for content type determination
  Future<ContentTypeDetectionResult> _detectContentType(String content) async {
    appLog.debug('Detecting content type for: ${truncateForLogging(content)}', name: _logName);

    // Store the original content
    final originalContent = content;

    // Default to unknown content type
    ContentTypeDetectionResult result = ContentTypeDetectionResult(
      primaryType: ContentType.unknown,
      isYouTubeVideo: false,
      isNewsArticle: false,
      originalContent: originalContent,
    );

    // Step 1: Check if content is a file path (do this first as it's a quick local check)
    bool isFilePath = false;
    try {
      final file = File(content);
      isFilePath = await file.exists();
    } catch (e) {
      isFilePath = false;
    }

    if (isFilePath) {
      appLog.debug('Content detected as file path', name: _logName);
      return result.copyWith(
        primaryType: ContentType.file,
      );
    }

    // Step 2: Use unified URL extraction to detect URLs and their types
    try {
      // Use UrlExtractor for comprehensive URL extraction and classification
      final processedUrlInfo = await UrlExtractor.extractUrl(content);

      if (processedUrlInfo != null) {
        appLog.debug('URL extraction successful: ${processedUrlInfo.bestUrl}', name: _logName);

        // Check if it's a YouTube URL (highest priority)
        if (processedUrlInfo.isYouTubeUrl) {
          appLog.debug('Content detected as YouTube video', name: _logName);
          return result.copyWith(
            primaryType: ContentType.youtubeVideo,
            isYouTubeVideo: true,
            youtubeUrl: processedUrlInfo.bestUrl,
          );
        }

        // Check if it's a news article URL
        if (processedUrlInfo.isNewsUrl) {
          appLog.debug('Content detected as news article', name: _logName);
          return result.copyWith(
            primaryType: ContentType.newsArticle,
            isNewsArticle: true,
            newsUrl: processedUrlInfo.bestUrl,
          );
        }

        // It's some other type of URL - default to news article processing
        // since that can handle general web content
        appLog.debug('Content contains URL, treating as general web content', name: _logName);
        return result.copyWith(
          primaryType: ContentType.newsArticle,
          isNewsArticle: true,
          newsUrl: processedUrlInfo.bestUrl,
        );
      } else {
        appLog.debug('No URL detected in content, treating as text', name: _logName);
      }
    } catch (e) {
      appLog.warning('Error during URL extraction: ${e.toString()}', name: _logName);
      // Continue to text processing on error
    }

    // Step 3: Default to text content if no URL found or extraction failed
    appLog.debug('Content detected as plain text', name: _logName);
    return result.copyWith(
      primaryType: ContentType.text,
    );
  }

  /// Check if a source with the given file path or content hash already exists in the provided list
  /// Returns true if a duplicate is found
  bool checkForDuplicate(String? filePath, String? contentHash, List<InputSource> existingSources) {
    return checkForDuplicateInternal(filePath, contentHash, existingSources);
  }

  /// Get the most recent attachment of a specific type
  /// This is a convenience method to access the attachment registry
  ProcessingResult? getAttachmentByType(String type) {
    appLog.debug('Getting most recent attachment of type: $type', name: _logName);
    return _attachmentRegistry.getMostRecentAttachmentByTypeProto(type);
  }

  /// Get the attachment registry service (for backward compatibility)
  AttachmentRegistryService getAttachmentRegistry() {
    return _attachmentRegistry;
  }

  /// Get the news article service (for backward compatibility)
  NewsArticleService getNewsArticleService() {
    return _newsArticleService;
  }

  /// Get the file processing service (for backward compatibility)
  FileProcessingService getFileProcessingService() {
    return _fileProcessingService;
  }

  /// Get the zip service (for backward compatibility)
  ZipService getZipService() {
    return _zipService;
  }

  /// Get the YouTube service (for backward compatibility)
  YouTubeService? getYouTubeService() {
    return _youtubeService;
  }

  /// Notify listeners that a source has been processed
  /// This should be called whenever a source is fully processed and ready to be displayed
  void notifySourceProcessed(InputSource source) {
    _sourceProcessedController.add(source);
  }

  /// Internal implementation that can be overridden in tests
  /// This method exists to make testing easier with mock frameworks
  @visibleForTesting
  bool checkForDuplicateInternal(
      String? filePath, String? contentHash, List<InputSource>? existingSources) {
    if (contentHash == null || existingSources == null) return false;

    return existingSources.any((source) {
      final hashMatch = source.contentHash == contentHash;
      final pathMatch = filePath != null &&
          source.filePath != null &&
          File(source.filePath!).absolute.path == File(filePath).absolute.path;

      if (hashMatch || pathMatch) {
        appLog.debug('Duplicate detected: ${source.fileName ?? "Unknown"}', name: _logName);
      }

      return hashMatch || pathMatch;
    });
  }
}
