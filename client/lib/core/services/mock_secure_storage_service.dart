import 'package:promz/core/services/i_secure_storage_service.dart';
import 'package:promz_common/promz_common.dart';

/// Mock implementation of ISecureStorageService for testing
///
/// This class provides an in-memory implementation of the secure storage
/// interface that can be used during unit tests without requiring platform channels.
class MockSecureStorageService implements ISecureStorageService {
  static const _logName = 'MockSecureStorageService';

  // In-memory storage for test data
  static final Map<String, String> _storage = {};

  /// Retrieve a value from the mock storage
  static Future<String?> getValue(String key) async {
    appLog.debug('Getting value for key: $key', name: _logName);
    return _storage[key];
  }

  /// Save a value to the mock storage
  static Future<void> saveValue(String key, String value) async {
    appLog.debug('Saving value for key: $key', name: _logName);
    _storage[key] = value;
  }

  /// Check if a key exists in the mock storage
  static Future<bool> contains<PERSON>ey(String key) async {
    appLog.debug('Checking if key exists: $key', name: _logName);
    return _storage.contains<PERSON>ey(key);
  }

  /// Delete a value from the mock storage
  static Future<void> deleteValue(String key) async {
    appLog.debug('Deleting value for key: $key', name: _logName);
    _storage.remove(key);
  }

  /// Delete all values from the mock storage
  static Future<void> deleteAll() async {
    appLog.debug('Deleting all values from storage', name: _logName);
    _storage.clear();
  }

  /// Reset the mock storage (helper for tests)
  static void reset() {
    appLog.debug('Resetting mock storage', name: _logName);
    _storage.clear();
  }
}
