import 'dart:convert';
import 'dart:developer' as dev;
import 'dart:io';

import 'package:archive/archive.dart';
import 'package:path/path.dart' as path;
import 'package:promz_common/promz_common.dart';
import 'package:promz/core/models/file/processed_file.dart';
import 'file/types/pdf_service.dart';

class FileProcessingService {
  static const _logName = 'FileProcessingService';

  // File type checks
  bool isZipFile(String filePath, [String? mimeType]) {
    final pathLower = filePath.toLowerCase();
    final mimeTypeLower = (mimeType ?? '').toLowerCase();

    return pathLower.endsWith('.zip') ||
        mimeTypeLower == 'application/zip' ||
        mimeTypeLower == 'application/x-zip-compressed' ||
        mimeTypeLower == 'application/x-zip';
  }

  bool isTextFile(String filePath, [String? mimeType]) {
    final mimeTypeLower = (mimeType ?? '').toLowerCase();
    return mimeTypeLower.startsWith('text/') || filePath.toLowerCase().endsWith('.txt');
  }

  bool isPdfFile(String filePath, [String? mimeType]) {
    final pathLower = filePath.toLowerCase();
    final mimeTypeLower = (mimeType ?? '').toLowerCase();
    return pathLower.endsWith('.pdf') || mimeTypeLower == 'application/pdf';
  }

  bool isWhatsAppExport(String filePath) {
    if (!isZipFile(filePath)) return false;

    final pathLower = filePath.toLowerCase();
    return pathLower.contains('whatsapp') &&
        (pathLower.contains('chat') || pathLower.contains('conversation'));
  }

  // File content processing
  Future<String?> getFileContent(String filePath, [String? mimeType]) async {
    try {
      // Validate file path and existence
      final file = File(filePath);
      if (!await file.exists()) {
        appLog.error('File does not exist: $filePath', name: _logName);
        return null;
      }

      appLog.debug('Getting content from file: $filePath (size: ${await file.length()} bytes)',
          name: _logName);

      if (isZipFile(filePath, mimeType)) {
        try {
          final processedFiles = await processZipFile(filePath);
          if (processedFiles.isNotEmpty) {
            appLog.debug('Successfully processed ZIP with ${processedFiles.length} files',
                name: _logName);
            return processedFiles.map((f) => f.textContent).join('\n\n');
          } else {
            appLog.warning('ZIP file contained no valid files', name: _logName);
            return '';
          }
        } catch (e, stack) {
          appLog.error('Error processing ZIP file', name: _logName, error: e, stackTrace: stack);
          // Return empty string instead of null to avoid exceptions in calling code
          return '';
        }
      } else if (isTextFile(filePath, mimeType)) {
        return await file.readAsString();
      } else if (isPdfFile(filePath, mimeType)) {
        final pdfService = PDFService();
        return await pdfService.extractContent(filePath);
      }

      appLog.warning('Unsupported file type: $filePath (mime: $mimeType)', name: _logName);
      return '';
    } catch (e, stack) {
      appLog.error('Error getting file content', name: _logName, error: e, stackTrace: stack);
      // Return empty string instead of null to avoid exceptions in calling code
      return '';
    }
  }

  /// Synchronously read text content from a file
  /// Returns null if the file is not a text file or if there's an error
  String? getTextContent(String filePath, [String? mimeType]) {
    try {
      if (isTextFile(filePath, mimeType)) {
        return File(filePath).readAsStringSync();
      }
      return null;
    } catch (e) {
      appLog.error('Error getting text content', name: _logName, error: e);
      return null;
    }
  }

  Future<List<ProcessedFile>> processZipFile(String filePath) async {
    try {
      appLog.debug('Reading ZIP file: $filePath', name: _logName);
      final file = File(filePath);

      if (!await file.exists()) {
        appLog.error('ZIP file does not exist: $filePath', name: _logName);
        return [];
      }

      final fileSize = await file.length();
      appLog.debug('ZIP file size: $fileSize bytes', name: _logName);

      if (fileSize == 0) {
        appLog.error('ZIP file is empty: $filePath', name: _logName);
        return [];
      }

      final bytes = await file.readAsBytes();
      appLog.debug('Successfully read ${bytes.length} bytes from ZIP file', name: _logName);

      // Validate ZIP format before decoding
      if (bytes.length < 4 ||
          bytes[0] != 0x50 ||
          bytes[1] != 0x4B || // PK signature
          (bytes[2] != 0x03 && bytes[2] != 0x05 && bytes[2] != 0x07)) {
        appLog.error('Invalid ZIP file format: $filePath', name: _logName);
        return [];
      }

      final archive = ZipDecoder().decodeBytes(bytes);
      final processedFiles = <ProcessedFile>[];

      for (final file in archive) {
        if (file.isFile) {
          try {
            final content = await _decodeFileContent(file);
            final mimeType = getMimeType(file.name);

            processedFiles.add(ProcessedFile(
              fileName: file.name,
              mimeType: mimeType,
              textContent: content,
              binaryContent: file.content as List<int>,
              size: file.content.length,
              lastModified: DateTime.now(),
            ));
          } catch (e) {
            appLog.warning('Error processing file ${file.name} in ZIP', name: _logName, error: e);
            // Continue processing other files
          }
        }
      }

      appLog.info('Processed ${processedFiles.length} files from ZIP', name: _logName);
      return processedFiles;
    } catch (e, stack) {
      appLog.error('Error processing ZIP file', name: _logName, error: e, stackTrace: stack);
      // Return empty list instead of rethrowing to avoid exceptions in calling code
      return [];
    }
  }

  Future<String> _decodeFileContent(ArchiveFile file) async {
    try {
      return utf8.decode(file.content as List<int>);
    } catch (e) {
      appLog.error('Failed to decode ${file.name} with UTF-8: $e', name: _logName, error: e);
      try {
        final content = latin1.decode(file.content as List<int>);
        appLog.info('Successfully decoded ${file.name} with latin1', name: _logName);
        return content;
      } catch (e) {
        appLog.error('Failed to decode ${file.name} with latin1: $e', name: _logName, error: e);
        return 'Failed to decode file content.';
      }
    }
  }

  String getMimeType(String fileName) {
    final ext = path.extension(fileName).toLowerCase();
    switch (ext) {
      case '.txt':
        return 'text/plain';
      case '.json':
        return 'application/json';
      case '.xml':
        return 'application/xml';
      case '.zip':
        return 'application/zip';
      case '.pdf':
        return 'application/pdf';
      default:
        return 'application/octet-stream';
    }
  }

  String determineSourceApp(String filePath) {
    final pathLower = filePath.toLowerCase();
    if (pathLower.contains('whatsapp')) {
      return 'WhatsApp';
    } else if (pathLower.contains('telegram')) {
      return 'Telegram';
    } else if (pathLower.contains('download')) {
      return 'Downloads';
    }
    return 'Unknown';
  }

  Map<String, dynamic> extractWhatsAppMetadata(String content, String fileName, String filePath) {
    final lines = content.split('\n');
    final participants = <String>{};
    DateTime? startDate;
    DateTime? endDate;
    int messageCount = 0;

    // WhatsApp date format regex (covers most common formats)
    final dateRegex =
        RegExp(r'(\d{1,2}[./]\d{1,2}[./]\d{2,4}),? (\d{1,2}:\d{2}(?::\d{2})?) ?(?:[AP]M)? - ');
    final participantRegex = RegExp(r' - (.*?): ');

    // Check if the file is a WhatsApp export based on filename
    final isWhatsAppExport =
        fileName.toLowerCase().contains('whatsapp') && fileName.toLowerCase().endsWith('.txt');

    if (!isWhatsAppExport) {
      return {}; // Return empty map if not a WhatsApp export
    }

    for (final line in lines) {
      final dateMatch = dateRegex.firstMatch(line);
      if (dateMatch != null) {
        messageCount++; // Increment message count for each line with a date
        final dateStr = dateMatch.group(1);
        final timeStr = dateMatch.group(2);
        DateTime? date;
        if (dateStr != null && timeStr != null) {
          // Attempt to parse with different separators
          date = DateTime.tryParse('$dateStr $timeStr');
          if (date == null) {
            // If parsing fails, try replacing dots with slashes
            final normalizedDateStr = dateStr.replaceAll('.', '/');
            date = DateTime.tryParse('$normalizedDateStr $timeStr');
          }
        }
        if (date != null) {
          startDate ??= date;
          endDate = date;
        }
      }

      final participantMatch = participantRegex.firstMatch(line);
      if (participantMatch != null) {
        participants.add(participantMatch.group(1)!);
      }
    }

    // Get file size if available
    int? fileSize;
    try {
      final file = File(filePath);
      fileSize = file.lengthSync();
    } catch (e) {
      dev.log('Could not determine file size: $e');
      fileSize = null;
    }

    return {
      'participants': participants.toList(),
      'startDate': startDate,
      'endDate': endDate,
      'messageCount': messageCount,
      'fileSize': fileSize,
    };
  }
}
