import 'package:promz/core/models/license_model.dart';
import 'package:promz/core/services/license_manager_service.dart';
import 'package:promz/core/services/supabase_service.dart';
import 'package:promz_common/promz_common.dart';

/// Service for managing license keys and API access
class LicenseService {
  final SupabaseService _supabaseService;
  final LicenseManagerService _licenseManagerService = LicenseManagerService();

  String? _apiKey;
  Map<String, dynamic>? _currentLicense;

  final String _logName = 'LicenseService';

  LicenseService(this._supabaseService);

  /// Current license information
  Map<String, dynamic>? get currentLicense => _currentLicense;

  /// Current API key
  String? get apiKey => _apiKey;

  /// Initialize the license service
  /// Retrieves the stored API key or generates a new one if needed
  Future<String?> initialize() async {
    try {
      appLog.debug('Initializing license service', name: _logName);

      // Try to get the stored API key first
      _apiKey = await _supabaseService.getStoredApiKey();

      if (_apiKey != null) {
        appLog.debug('Using stored API key', name: _logName);
        return _apiKey;
      }

      // If no API key is stored, get or create a license
      final licenseData = await _licenseManagerService.getOrCreateLicense('free');

      if (licenseData != null && licenseData['api_key'] != null) {
        _currentLicense = licenseData;
        _apiKey = licenseData['api_key'] as String;

        // Store the API key for future use
        await _supabaseService.storeApiKey(_apiKey!);

        appLog.debug('Created new license and stored API key', name: _logName);
        return _apiKey;
      }

      appLog.warning('Failed to initialize license', name: _logName);
      return null;
    } catch (e, stack) {
      appLog.error('Error initializing license', name: _logName, error: e, stackTrace: stack);
      return null;
    }
  }

  /// Ensure the user has a valid license (creates a free license if none exists)
  /// Returns true if a license was created, false if one already existed or creation failed
  Future<bool> ensureUserHasLicense() async {
    try {
      // Check if user already has any license
      final existingLicense = await _licenseManagerService.getUserLicense();

      if (existingLicense != null) {
        appLog.debug('User already has a license: ${existingLicense.toString()}', name: _logName);

        // Check if the license is a free license
        final licenseType = existingLicense.licenseType;
        final isActive = existingLicense.hasLicense;

        appLog.debug('Existing license type: $licenseType, isActive: $isActive', name: _logName);

        // If it's already a valid free license, no need to create a new one
        if (licenseType == LicenseType.free && isActive) {
          return false;
        }
      }

      // Create a free license
      appLog.debug('Creating a new free license', name: _logName);
      final licenseData = await _licenseManagerService.getOrCreateLicense('free');

      if (licenseData != null && licenseData['api_key'] != null) {
        _currentLicense = licenseData;
        _apiKey = licenseData['api_key'] as String;

        // Store the API key for future use
        await _supabaseService.storeApiKey(_apiKey!);

        appLog.debug('Created free license for user: ${licenseData.toString()}', name: _logName);
        return true;
      }

      appLog.warning('Failed to create free license', name: _logName);
      return false;
    } catch (e, stack) {
      appLog.error('Error ensuring user has license', name: _logName, error: e, stackTrace: stack);
      return false;
    }
  }

  /// Generate a trial license for the current user
  Future<bool> generateTrialLicense() async {
    try {
      appLog.debug('Generating trial license', name: _logName);

      // Use LicenseManagerService to generate a trial license
      final licenseData = await _licenseManagerService.getOrCreateLicense('trial');

      if (licenseData != null && licenseData['api_key'] != null) {
        _currentLicense = licenseData;
        _apiKey = licenseData['api_key'] as String;

        // Store the API key for future use
        await _supabaseService.storeApiKey(_apiKey!);

        appLog.debug('Trial license generated successfully', name: _logName);
        return true;
      }

      appLog.warning('Failed to generate trial license', name: _logName);
      return false;
    } catch (e, stack) {
      appLog.error('Error generating trial license', name: _logName, error: e, stackTrace: stack);
      return false;
    }
  }

  /// Check if the current user has an active trial license
  Future<bool> hasActiveTrial() async {
    try {
      final license = await _licenseManagerService.getUserLicense();

      if (license == null) {
        return false;
      }

      final licenseType = license.licenseType;
      final isActive = license.hasLicense;
      final expiryDate = license.expiresAt;

      return licenseType == LicenseType.trial &&
          isActive &&
          expiryDate != null &&
          expiryDate.isAfter(DateTime.now());
    } catch (e, stack) {
      appLog.error('Error checking trial status', name: _logName, error: e, stackTrace: stack);
      return false;
    }
  }

  /// Track previously used accounts on this device to prevent multiple trials
  Future<void> trackAccountUsage(String userId, String email) async {
    try {
      // Implementation would store this information in secure local storage
      // This is a placeholder for the actual implementation
      appLog.debug('Tracking account usage for $email', name: _logName);
    } catch (e, stack) {
      appLog.error('Error tracking account usage', name: _logName, error: e, stackTrace: stack);
    }
  }
}
