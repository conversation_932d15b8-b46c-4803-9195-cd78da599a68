import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:promz/core/services/i_secure_storage_service.dart';
import 'package:promz_common/promz_common.dart';

/// Unified secure storage service for the entire application
///
/// This service centralizes all secure storage operations, providing a consistent
/// interface for storing sensitive data such as API keys, authentication tokens,
/// and user profile information.
class SecureStorageService implements ISecureStorageService {
  static const _logName = 'SecureStorageService';

  // Use proper configuration for secure storage
  static const _storage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
  );

  // Keys for different types of stored data
  static const _apiKeyKey = 'promz_api_key';
  static const _supabaseSessionKey = 'supabase_session';
  static const _userProfileKey = 'user_profile_data';

  // Simple memory cache to avoid repeated secure storage reads
  static final Map<String, String> _memoryCache = {};
  static bool _memoryCacheEnabled = true;

  // API Key Management

  /// Store the API key securely
  static Future<void> saveApiKey(String apiKey) async {
    try {
      // Check if the key already exists
      final exists = await _storage.containsKey(key: _apiKeyKey);

      if (exists) {
        // Delete the existing key first to avoid the "already exists" error
        await _storage.delete(key: _apiKeyKey);
      }

      // Now write the new key
      await _storage.write(key: _apiKeyKey, value: apiKey);
      // Update memory cache
      _updateMemoryCache(_apiKeyKey, apiKey);
    } catch (e) {
      appLog.error('Failed to save API key', name: _logName, error: e);
      rethrow;
    }
  }

  /// Retrieve the API key
  static Future<String?> getApiKey() async {
    try {
      // Check memory cache first
      if (_memoryCacheEnabled && _memoryCache.containsKey(_apiKeyKey)) {
        return _memoryCache[_apiKeyKey];
      }

      final value = await _storage.read(key: _apiKeyKey);
      _updateMemoryCache(_apiKeyKey, value);
      return value;
    } catch (e) {
      appLog.error('Failed to retrieve API key', name: _logName, error: e);
      return null;
    }
  }

  /// Check if API key exists
  static Future<bool> hasApiKey() async {
    try {
      // Check memory cache first
      if (_memoryCacheEnabled && _memoryCache.containsKey(_apiKeyKey)) {
        return _memoryCache[_apiKeyKey] != null && _memoryCache[_apiKeyKey]!.isNotEmpty;
      }

      final apiKey = await _storage.read(key: _apiKeyKey);
      _updateMemoryCache(_apiKeyKey, apiKey);
      return apiKey != null && apiKey.isNotEmpty;
    } catch (e) {
      appLog.error('Failed to check API key', name: _logName, error: e);
      return false;
    }
  }

  /// Delete the API key
  static Future<void> deleteApiKey() async {
    try {
      await _storage.delete(key: _apiKeyKey);
      _removeFromMemoryCache(_apiKeyKey);
      appLog.debug('API key deleted', name: _logName);
    } catch (e) {
      appLog.error('Failed to delete API key', name: _logName, error: e);
      rethrow;
    }
  }

  // User Profile Caching

  /// Cache essential user profile data for faster app startup
  static Future<void> cacheUserProfile({
    required String userId,
    required String? email,
    required String? displayName,
    required String? avatarUrl,
  }) async {
    try {
      final profileData = {
        'userId': userId,
        'email': email,
        'displayName': displayName ?? '',
        'avatarUrl': avatarUrl ?? '',
        'cachedAt': DateTime.now().toIso8601String(),
      };

      final profileStr = jsonEncode(profileData);

      // Use safe write to prevent "already exists" errors
      await safeWrite(_userProfileKey, profileStr);
      _updateMemoryCache(_userProfileKey, profileStr);
    } catch (e) {
      appLog.error('END: Failed to cache user profile data', name: _logName, error: e);
      // Don't rethrow as this is non-critical functionality
    }
  }

  /// Retrieve cached user profile data
  /// Returns null if no profile data is cached or if it's invalid
  static Future<Map<String, dynamic>?> getCachedUserProfile() async {
    try {
      String? profileStr;
      // Check memory cache first
      if (_memoryCacheEnabled && _memoryCache.containsKey(_userProfileKey)) {
        profileStr = _memoryCache[_userProfileKey];
      } else {
        profileStr = await _storage.read(key: _userProfileKey);
        _updateMemoryCache(_userProfileKey, profileStr);
      }

      if (profileStr == null || profileStr.isEmpty) {
        appLog.debug('END: No cached user profile found', name: _logName);
        return null;
      }

      final profileData = jsonDecode(profileStr) as Map<String, dynamic>;

      // Check if profile data is valid
      if (!profileData.containsKey('userId') ||
          profileData['userId'] == null ||
          profileData['userId'].isEmpty) {
        appLog.debug('END: Cached profile data is invalid, missing userId', name: _logName);
        return null;
      }

      return profileData;
    } catch (e) {
      appLog.error('END: Failed to retrieve cached user profile', name: _logName, error: e);
      return null;
    }
  }

  /// Check if cached profile data exists
  static Future<bool> hasCachedUserProfile() async {
    try {
      // Check memory cache first
      if (_memoryCacheEnabled && _memoryCache.containsKey(_userProfileKey)) {
        final hasProfile =
            _memoryCache[_userProfileKey] != null && _memoryCache[_userProfileKey]!.isNotEmpty;
        appLog.debug('END: User profile cache exists (from memory): $hasProfile', name: _logName);
        return hasProfile;
      }

      return await _storage.containsKey(key: _userProfileKey);
    } catch (e) {
      appLog.error('END: Failed to check user profile cache', name: _logName, error: e);
      return false;
    }
  }

  /// Delete cached user profile data
  static Future<void> deleteCachedUserProfile() async {
    try {
      await _storage.delete(key: _userProfileKey);
      _removeFromMemoryCache(_userProfileKey);
    } catch (e) {
      appLog.error('END: Failed to delete user profile cache', name: _logName, error: e);
      // Don't rethrow as this is non-critical functionality
    }
  }

  // Supabase Session Management

  /// Store Supabase session data (MUST be valid JSON from Supabase)
  static Future<void> saveSupabaseSession(String sessionData) async {
    try {
      // Validate that the session data is valid JSON
      try {
        final jsonData = jsonDecode(sessionData);
        if (jsonData['access_token'] == null) {
          throw const FormatException('Invalid session data: missing access_token');
        }
      } catch (e) {
        appLog.error('Invalid session data format', name: _logName, error: e);
        throw FormatException('Invalid session data format: $e');
      }

      // Use safe write to prevent "already exists" errors
      await safeWrite(_supabaseSessionKey, sessionData);
      _updateMemoryCache(_supabaseSessionKey, sessionData);
    } catch (e) {
      appLog.error('Failed to save Supabase session', name: _logName, error: e);
      rethrow;
    }
  }

  /// Retrieve the raw Supabase session JSON string
  static Future<String?> getSupabaseSession() async {
    try {
      // Check memory cache first
      if (_memoryCacheEnabled && _memoryCache.containsKey(_supabaseSessionKey)) {
        final sessionData = _memoryCache[_supabaseSessionKey];

        if (sessionData == null || sessionData.isEmpty) {
          return null;
        }

        // Validate that the retrieved data is valid JSON
        try {
          jsonDecode(sessionData);
        } catch (e) {
          appLog.error('Retrieved invalid session data format from memory cache',
              name: _logName, error: e);
          _removeFromMemoryCache(_supabaseSessionKey);
          return null;
        }

        // Only log a truncated version of the session data for security
        final truncatedSession =
            sessionData.length > 50 ? '${sessionData.substring(0, 47)}...' : sessionData;
        appLog.debug('Retrieved Supabase session from memory cache: $truncatedSession',
            name: _logName);
        return sessionData;
      }

      final sessionData = await _storage.read(key: _supabaseSessionKey);

      if (sessionData == null || sessionData.isEmpty) {
        return null;
      }

      // Validate that the retrieved data is valid JSON
      try {
        jsonDecode(sessionData);
      } catch (e) {
        appLog.error('Retrieved invalid session data format', name: _logName, error: e);
        return null;
      }

      // Update memory cache
      _updateMemoryCache(_supabaseSessionKey, sessionData);

      return sessionData;
    } catch (e) {
      appLog.error('Failed to retrieve Supabase session', name: _logName, error: e);
      return null;
    }
  }

  /// Delete the Supabase session
  static Future<void> deleteSupabaseSession() async {
    try {
      await _storage.delete(key: _supabaseSessionKey);
      _removeFromMemoryCache(_supabaseSessionKey);
    } catch (e) {
      appLog.error('Failed to delete Supabase session', name: _logName, error: e);
      rethrow;
    }
  }

  /// Check if Supabase session exists and is valid
  static Future<bool> hasSupabaseSession() async {
    try {
      // First check memory cache
      if (_memoryCacheEnabled && _memoryCache.containsKey(_supabaseSessionKey)) {
        final sessionData = _memoryCache[_supabaseSessionKey];
        if (sessionData == null || sessionData.isEmpty) {
          return false;
        }

        try {
          return jsonDecode(sessionData)['access_token'] != null;
        } catch (e) {
          // If we can't parse it, it's not valid
          _removeFromMemoryCache(_supabaseSessionKey);
          return false;
        }
      }

      // If not in memory cache, check storage
      final hasSession = await _storage.containsKey(key: _supabaseSessionKey);
      if (!hasSession) {
        return false;
      }

      // If it exists, also verify that the content is valid JSON
      final sessionData = await _storage.read(key: _supabaseSessionKey);
      if (sessionData == null || sessionData.isEmpty) {
        return false;
      }

      try {
        final jsonData = jsonDecode(sessionData);
        final isValid = jsonData['access_token'] != null;

        if (isValid) {
          // Update memory cache
          _updateMemoryCache(_supabaseSessionKey, sessionData);
        }

        return isValid;
      } catch (e) {
        // If we can't parse it, it's not valid
        return false;
      }
    } catch (e) {
      appLog.error('Failed to check Supabase session', name: _logName, error: e);
      return false;
    }
  }

  // Generic Storage Methods (for ISecureStorageService interface)

  /// Retrieve a value from secure storage
  static Future<String?> getValue(String key) async {
    try {
      // Check memory cache first
      if (_memoryCacheEnabled && _memoryCache.containsKey(key)) {
        return _memoryCache[key];
      }

      final value = await _storage.read(key: key);
      _updateMemoryCache(key, value);
      return value;
    } catch (e) {
      appLog.error('Failed to retrieve value for key: $key', name: _logName, error: e);
      return null;
    }
  }

  /// Save a value to secure storage
  static Future<void> saveValue(String key, String value) async {
    // Use the safeWrite method to prevent "already exists" errors
    await safeWrite(key, value);
  }

  /// Check if a key exists in secure storage
  static Future<bool> containsKey(String key) async {
    try {
      // Check memory cache first
      if (_memoryCacheEnabled && _memoryCache.containsKey(key)) {
        return _memoryCache[key] != null;
      }

      return await _storage.containsKey(key: key);
    } catch (e) {
      appLog.error('Failed to check if key exists: $key', name: _logName, error: e);
      return false;
    }
  }

  /// Delete a value from secure storage
  static Future<void> deleteValue(String key) async {
    try {
      await _storage.delete(key: key);
      _removeFromMemoryCache(key);
    } catch (e) {
      appLog.error('Failed to delete value for key: $key', name: _logName, error: e);
      rethrow;
    }
  }

  /// Delete all values from secure storage
  static Future<void> deleteAll() async {
    try {
      await _storage.deleteAll();
      _clearMemoryCache();
    } catch (e) {
      appLog.error('Failed to delete all values from storage', name: _logName, error: e);
      rethrow;
    }
  }

  /// Clear all user-related data but keep API key
  /// This is useful when signing out but wanting to keep the API key
  static Future<void> clearUserData() async {
    try {
      // Delete user profile
      await deleteCachedUserProfile();

      // Delete Supabase session
      await deleteSupabaseSession();
    } catch (e) {
      appLog.error('Failed to clear user data', name: _logName, error: e);
      // Don't rethrow as this is non-critical functionality
    }
  }

  /// Safe write operation that deletes the key first if it exists
  /// This prevents the "already exists" error in the keychain
  static Future<void> safeWrite(String key, String value) async {
    try {
      // Check if the key already exists
      final exists = await _storage.containsKey(key: key);

      if (exists) {
        // Delete the existing key first
        await _storage.delete(key: key);
      }

      // Now write the new value
      await _storage.write(key: key, value: value);
      _updateMemoryCache(key, value);
    } catch (e) {
      appLog.error('Failed to save value for key: $key', name: _logName, error: e);
      rethrow;
    }
  }

  /// Methods for memory cache management

  /// Update a value in the memory cache
  static void _updateMemoryCache(String key, String? value) {
    if (!_memoryCacheEnabled || value == null) return;
    _memoryCache[key] = value;
  }

  /// Remove a value from the memory cache
  static void _removeFromMemoryCache(String key) {
    if (!_memoryCacheEnabled) return;
    _memoryCache.remove(key);
  }

  /// Clear the entire memory cache
  static void _clearMemoryCache() {
    if (!_memoryCacheEnabled) return;
    _memoryCache.clear();
  }

  /// Enable or disable memory cache
  static void setMemoryCacheEnabled(bool enabled) {
    _memoryCacheEnabled = enabled;
    if (!enabled) {
      _clearMemoryCache();
    }
  }

  /// For testing purposes only - clear memory cache
  @visibleForTesting
  static void clearMemoryCacheForTesting() {
    _clearMemoryCache();
  }
}
