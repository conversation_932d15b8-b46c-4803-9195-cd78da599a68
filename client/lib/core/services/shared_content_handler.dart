import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_sharing_intent/flutter_sharing_intent.dart';
import 'package:flutter_sharing_intent/model/sharing_file.dart';
import 'package:promz/core/providers/license_manager_provider.dart';
import 'package:promz/core/services/content_processing_service.dart';
import 'package:promz/core/services/content_processing_task_manager.dart';
import 'package:promz/core/utils/deep_link_utils.dart';
import 'package:promz/features/input_selection/models/input_source.dart';
import 'package:promz_common/promz_common.dart';

/// Status of shared content processing
enum SharedProcessingStatus {
  /// Processing started
  started,

  /// Processing large file
  processingLargeFile,

  /// Processing completed successfully
  completed,

  /// Processing failed due to network error
  networkError,

  /// Processing failed due to timeout
  timeout,

  /// Processing failed due to authentication error
  authRequired,

  /// Processing failed due to file size limit exceeded
  fileSizeLimitExceeded,

  /// Processing failed due to other error
  error
}

/// A service that handles content shared from other apps
/// This centralizes the handling of shared files to avoid duplicate processing
class SharedContentHandler {
  static const _logName = 'SharedContentHandler';

  final ContentProcessingService _contentProcessingService;
  final Ref _ref;
  StreamSubscription? _intentDataStreamSubscription;

  // Stream controllers for emitting events
  final _sharedContentController = StreamController<InputSource>.broadcast();
  final _authRequiredController = StreamController<void>.broadcast();
  final _processingStatusController = StreamController<SharedProcessingStatus>.broadcast();
  final _fileSizeLimitController = StreamController<Exception>.broadcast();

  /// Stream of processed shared content information
  Stream<InputSource> get sharedContent => _sharedContentController.stream;

  /// Stream that emits when authentication is required
  Stream<void> get authenticationRequired => _authRequiredController.stream;

  /// Stream that emits processing status updates
  Stream<SharedProcessingStatus> get processingStatus => _processingStatusController.stream;

  /// Stream that emits when file size limit is exceeded
  Stream<Exception> get fileSizeLimitExceeded => _fileSizeLimitController.stream;

  /// Flag to track if we're currently processing files
  bool _isProcessing = false;

  /// Creates a new instance of SharedContentHandler
  ///
  /// This handler processes files shared from other apps and manages the tasks
  /// File size validation is now handled centrally by ContentProcessingService
  SharedContentHandler({
    required ContentProcessingService contentProcessingService,
    ContentProcessingTaskManager? taskManager,
    required Ref ref,
  })  : _contentProcessingService = contentProcessingService,
        _ref = ref {
    _initialize();
  }

  /// Initialize the sharing intent listeners
  void _initialize() {
    appLog.debug('Initializing shared content handler', name: _logName);
    _initializeIntentListener();
    _initializeLicenseListener();
  }

  /// Initialize listener for license status changes
  void _initializeLicenseListener() {
    // Listen for changes to license validity
    _ref.listen<bool>(isLicenseValidProvider, (previous, current) {
      appLog.debug('License validity changed: $previous -> $current', name: _logName);

      // If license became valid, process any pending tasks
      if (previous == false && current == true) {
        appLog.info('License became valid, processing pending tasks', name: _logName);
        _contentProcessingService.processPendingTasks();
      }
    });
  }

  /// Initialize the sharing intent listeners
  void _initializeIntentListener() {
    // For sharing files coming from outside the app while the app is in the memory
    _intentDataStreamSubscription =
        FlutterSharingIntent.instance.getMediaStream().listen((List<SharedFile> value) {
      appLog.info('Received shared files while app is running: ${value.length} files',
          name: _logName);
      _handleSharedFiles(value);
    }, onError: (err, stack) {
      appLog.error('getMediaStream error', name: _logName, error: err, stackTrace: stack);
    });

    // For sharing files coming from outside the app while the app is closed
    FlutterSharingIntent.instance.getInitialSharing().then((List<SharedFile> value) {
      appLog.info('Received initial shared files: ${value.length} files', name: _logName);
      _handleSharedFiles(value);
    }).catchError((err, stack) {
      appLog.error('getInitialSharing error', name: _logName, error: err, stackTrace: stack);
    });
  }

  /// Handle shared files from the intent
  void _handleSharedFiles(List<SharedFile> files) {
    if (files.isEmpty) {
      appLog.debug('No shared files received', name: _logName);
      return;
    }

    // Filter out Promz-specific deep links before processing
    final filesToProcessRegularly = <SharedFile>[];
    for (final file in files) {
      if (file.value != null) {
        final uriString = file.value!;
        // Check for promz:// scheme or promz.ai/p/ paths
        if (uriString.startsWith('promz://') || uriString.contains('www.promz.ai/p/')) {
          appLog.info(
              'Identified Promz deep link in SharedContentHandler: $uriString. Forwarding to DeepLinkUtils.',
              name: _logName);
          try {
            DeepLinkUtils.notifyLinkReceived(uriString);
            appLog.info('Notified DeepLinkUtils of Promz deep link: $uriString', name: _logName);
          } catch (e) {
            appLog.error(
                'Failed to parse or notify deep link from SharedContentHandler: $uriString',
                name: _logName,
                error: e);
          }
        } else {
          filesToProcessRegularly.add(file);
        }
      } else {
        filesToProcessRegularly.add(file);
      }
    }

    if (filesToProcessRegularly.isEmpty) {
      appLog.debug(
          'All shared items were Promz deep links, nothing to process by SharedContentHandler',
          name: _logName);
      return;
    }

    // Emit processing started status
    _processingStatusController.add(SharedProcessingStatus.started);
    _isProcessing = true;

    appLog.info('Processing ${filesToProcessRegularly.length} shared files by SharedContentHandler',
        name: _logName);

    // Create tasks for each file and process them sequentially
    _processSharedFilesWithTaskManager(filesToProcessRegularly);
  }

  /// Update the _processSharedFilesWithTaskManager method
  Future<void> _processSharedFilesWithTaskManager(List<SharedFile> files) async {
    for (final file in files) {
      if (file.value == null || file.value!.isEmpty) {
        appLog.warning('Shared file has null path, skipping', name: _logName);
        continue;
      }

      // Create a task through ContentProcessingService instead of directly
      await _contentProcessingService.createFileTask(
        file.value!,
        source: 'share_intent',
      );
    }

    // Let ContentProcessingService handle the processing
    await _contentProcessingService.processPendingTasks();
  }

  /// Check if currently processing files
  bool get isProcessing => _isProcessing;

  /// Add processed content to the shared content stream
  void addProcessedContent(InputSource source) {
    if (!_sharedContentController.isClosed) {
      appLog.debug('Adding processed content to shared stream: ${source.fileName}', name: _logName);
      _sharedContentController.add(source);
    } else {
      appLog.warning('Cannot add content to closed stream', name: _logName);
    }
  }

  /// Dispose resources
  void dispose() {
    _intentDataStreamSubscription?.cancel();
    _sharedContentController.close();
    _authRequiredController.close();
    _processingStatusController.close();
    _fileSizeLimitController.close();
    // Note: We don't dispose the task manager here as it's a singleton
  }
}
