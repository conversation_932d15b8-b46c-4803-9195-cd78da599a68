import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:fixnum/fixnum.dart';
import 'package:promz/core/services/file/enhancers/whatsapp_metadata_enhancer.dart';
import 'package:promz/core/services/file/file_processing_client.dart';
import 'package:promz/core/services/file/metadata_enhancer_registry.dart';
import 'package:promz/generated/common.pb.dart';
import 'package:promz/generated/content_upload.pb.dart';
import 'package:promz_common/promz_common.dart';

/// Service for managing attachments and making them available for variable resolution
class AttachmentRegistryService extends ChangeNotifier {
  static const _logName = 'AttachmentRegistryService';

  final Map<String, dynamic> _attachmentContents = {};

  // New storage for protobuf-based attachments
  final Map<String, ProcessingResult> _protoAttachmentContents = {};

  final Map<String, StreamSubscription> _protoSubscriptions =
      {}; // Store proto stream subscriptions

  /// Register an attachment with its content and metadata using Protocol Buffers
  void registerAttachment({
    required String id,
    required ProcessingResult result,
  }) {
    appLog.debug('Registering proto attachment: $id, type: ${result.contentType}', name: _logName);

    // Extract and set the display name
    final displayName = _extractDisplayNameProto(result);
    result.displayName = displayName;

    // Set timestamp if not already set
    if (!result.hasTimestamp()) {
      result.timestamp = Int64(DateTime.now().millisecondsSinceEpoch);
    }

    // Store the result in the proto attachment contents map
    _protoAttachmentContents[id] = result;

    // Notify listeners that a new attachment has been registered
    notifyListeners();

    appLog.debug('Proto attachment registered successfully with display name: $displayName',
        name: _logName);
  }

  /// Registers an attachment that exists on the server and starts monitoring.
  void registerServerAttachment({
    required String id,
    required ProcessingResult initialResult,
    required FileProcessingClient client,
  }) {
    appLog.debug('Registering server attachment: $id with serverId: ${initialResult.jobId}');

    // Apply generic metadata enhancements using the registry
    _enhanceMetadata(initialResult);

    _protoAttachmentContents[id] = initialResult;

    // Notify listeners that a new attachment has been registered
    notifyListeners();

    // Start monitoring using the proto method
    _monitorProcessingStatusProto(id, initialResult.jobId, client);
  }

  /// Get attachment content by ID using Protocol Buffers
  Future<ProcessingResult?> getAttachmentProto(String id) async {
    return _protoAttachmentContents[id];
  }

  /// Get attachment content string, handling server references
  Future<String?> getAttachmentContentProto(String id, FileProcessingClient? client) async {
    final result = _protoAttachmentContents[id];
    if (result == null) return null;

    final content = result.content;

    // Check if it's a server reference
    if (isServerReference(content) && client != null) {
      final serverId = extractServerId(content);
      if (serverId != null) {
        // Just return the reference, _monitorProcessingStatusProto will
        // update the content when processing completes
        return content;
      }
    }

    return content;
  }

  /// Extract a display name from a ProcessingResult
  String _extractDisplayNameProto(ProcessingResult result) {
    // Use display_name if already set
    if (result.hasDisplayName() && result.displayName.isNotEmpty) {
      return result.displayName;
    }

    // Check WhatsApp metadata first if available
    if (result.hasWhatsappMetadata()) {
      final whatsappMeta = result.whatsappMetadata;
      if (whatsappMeta.groupName.isNotEmpty) {
        return whatsappMeta.groupName;
      } else if (whatsappMeta.chatName.isNotEmpty) {
        return whatsappMeta.chatName;
      }
    }

    // Check article or YouTube metadata
    if (result.hasArticleMetadata() && result.articleMetadata.title.isNotEmpty) {
      return result.articleMetadata.title;
    }

    if (result.hasYoutubeMetadata() && result.youtubeMetadata.title.isNotEmpty) {
      return result.youtubeMetadata.title;
    }

    // Check file metadata for title
    if (result.hasFileMetadata() &&
        result.fileMetadata.hasTitle() &&
        result.fileMetadata.title.isNotEmpty) {
      return result.fileMetadata.title;
    }

    // Use fileName if available
    if (result.hasFileName() && result.fileName.isNotEmpty) {
      final nameWithoutExt = result.fileName.split('/').last.split('\\').last;
      final dotIndex = nameWithoutExt.lastIndexOf('.');
      if (dotIndex > 0) {
        return nameWithoutExt.substring(0, dotIndex);
      }
      return nameWithoutExt;
    }

    // Check if author is available
    if (result.hasAuthor() && result.author.isNotEmpty) {
      return 'Content by ${result.author}';
    }

    // If all else fails, use job ID or content type
    if (result.jobId.isNotEmpty) {
      return 'Job ${result.jobId.substring(0, 8)}...';
    }

    return result.contentType.isNotEmpty ? 'Untitled ${result.contentType}' : 'Untitled attachment';
  }

  /// Apply type-specific metadata enhancements using registered enhancers
  bool _enhanceMetadata(ProcessingResult result, [Map<String, dynamic>? extractedMetadata]) {
    return MetadataEnhancerRegistry.instance.enhanceMetadata(result, extractedMetadata);
  }

  /// Monitors the processing status of a server attachment using gRPC streaming.
  Future<void> _monitorProcessingStatusProto(
      String id, String serverId, FileProcessingClient client) async {
    appLog.debug('Starting proto monitoring for attachment $id (serverId: $serverId)',
        name: _logName);
    if (_protoSubscriptions.containsKey(id)) {
      appLog.warning('Proto monitoring already active for attachment $id', name: _logName);
      return; // Avoid duplicate monitoring
    }

    final stream = client.streamUploadUpdatesProto(serverId);
    double lastKnownProgress =
        _protoAttachmentContents[id]?.processingProgress ?? 0.0; // Store last known progress

    // If this is a WhatsApp file, ensure it has at least basic WhatsApp metadata
    // even during processing
    final attachment = _protoAttachmentContents[id];
    if (attachment != null) {
      final fileName = attachment.fileName.toLowerCase();
      if (fileName.contains('whatsapp') || fileName.contains('chat')) {
        // Use the WhatsApp metadata enhancer to create placeholder metadata
        final enhancer = MetadataEnhancerRegistry.instance.getEnhancerForType('whatsapp');
        if (enhancer is WhatsAppMetadataEnhancer) {
          appLog.debug('Using WhatsAppMetadataEnhancer to create placeholder metadata for $id',
              name: _logName);
          enhancer.enhanceProcessingStateMetadata(attachment);
        } else {
          appLog.warning('WhatsAppMetadataEnhancer not found in registry', name: _logName);
        }
      }
    }

    final subscription = stream.listen(
      (UploadUpdate update) {
        appLog.debug(
            'Received proto update for $id: Status=${update.status}, Progress=${update.progressPercentage}, Msg=${update.message}',
            name: _logName);
        final attachment = _protoAttachmentContents[id];
        if (attachment != null) {
          // Update metadata based on the stream update
          attachment.status = update.status;
          // Only update progress if it's provided in the update
          if (update.hasProgressPercentage()) {
            attachment.processingProgress = update.progressPercentage;
            lastKnownProgress = update.progressPercentage; // Update last known progress
          }
          attachment.processingMessage = update.message;

          // Set error message from the update message if status is FAILED
          if (update.status == UploadStatus.UPLOAD_STATUS_FAILED && update.message.isNotEmpty) {
            attachment.errorMessage = update.message;
          }

          // Handle partial metadata from the update if available
          if (update.hasWhatsappMetadata()) {
            appLog.debug('Received partial WhatsApp metadata in update for $id', name: _logName);
            attachment.whatsappMetadata = update.whatsappMetadata;
            // Set content type if not already set
            if (attachment.contentType.isEmpty) {
              attachment.contentType = 'conversation';
            }
          } else if (update.hasZipMetadata()) {
            appLog.debug('Received partial ZIP metadata in update for $id', name: _logName);
            attachment.zipMetadata = update.zipMetadata;
          } else if (update.hasFileMetadata()) {
            appLog.debug('Received partial file metadata in update for $id', name: _logName);
            attachment.fileMetadata = update.fileMetadata;
          } else if (update.hasArticleMetadata()) {
            appLog.debug('Received partial article metadata in update for $id', name: _logName);
            attachment.articleMetadata = update.articleMetadata;
            // Set content type if not already set
            if (attachment.contentType.isEmpty) {
              attachment.contentType = 'news';
            }
          } else if (update.hasYoutubeMetadata()) {
            appLog.debug('Received partial YouTube metadata in update for $id', name: _logName);
            attachment.youtubeMetadata = update.youtubeMetadata;
            // Set content type if not already set
            if (attachment.contentType.isEmpty) {
              attachment.contentType = 'youtube';
            }
          } else if (attachment.status == UploadStatus.UPLOAD_STATUS_PROCESSING) {
            // No partial metadata available in the update, but we're still processing
            // Use the appropriate enhancer to create placeholder metadata
            final fileName = attachment.fileName.toLowerCase();
            if (fileName.contains('whatsapp') || fileName.contains('chat')) {
              final enhancer = MetadataEnhancerRegistry.instance.getEnhancerForType('whatsapp');
              if (enhancer is WhatsAppMetadataEnhancer) {
                appLog.debug(
                    'No partial metadata in update, using WhatsAppMetadataEnhancer for $id',
                    name: _logName);
                enhancer.enhanceProcessingStateMetadata(attachment);
              }
            }
          }

          notifyListeners();
        } else {
          appLog.warning('Received proto update for non-existent attachment: $id', name: _logName);
          _protoSubscriptions[id]?.cancel(); // Cancel subscription if attachment is gone
          _protoSubscriptions.remove(id);
        }
      },
      onError: (error, stackTrace) {
        appLog.error('Error in proto monitoring stream for $id: $error',
            name: _logName, error: error, stackTrace: stackTrace);
        final attachment = _protoAttachmentContents[id];
        if (attachment != null) {
          attachment.status = UploadStatus.UPLOAD_STATUS_FAILED;
          attachment.errorMessage = 'Monitoring stream error: $error';
          attachment.processingProgress = lastKnownProgress; // Keep last known progress on error
          notifyListeners();
        }
        _protoSubscriptions.remove(id); // Remove subscription on error
      },
      onDone: () async {
        // Check if attachment still exists before doing anything
        if (!_protoAttachmentContents.containsKey(id)) {
          appLog.warning('Proto monitoring stream done, but attachment $id no longer exists.',
              name: _logName);
          _protoSubscriptions.remove(id); // Ensure subscription reference is cleaned up
          return; // Exit early if attachment was removed
        }

        appLog.debug('Proto monitoring stream done for $id.', name: _logName);
        final attachment = _protoAttachmentContents[id]!; // Safe to assume non-null now

        // Always try to fetch the final result to get authoritative status, content URL, final error message etc.
        appLog.debug('Stream for $id done, fetching final proto result...', name: _logName);
        try {
          // Fetch the final result to confirm status and get final details
          final finalResult = await client.getProcessingResultProto(serverId);
          appLog.debug('Fetched final proto result for $id: Status=${finalResult.status}',
              name: _logName);

          // Log detailed metadata information for debugging
          if (finalResult.hasWhatsappMetadata()) {
            final whatsAppData = finalResult.whatsappMetadata;
            appLog.debug(
                'Final WhatsApp metadata for $id: ${whatsAppData.participants.length} participants, '
                '${whatsAppData.messageCount} messages, isGroupChat: ${whatsAppData.isGroupChat}',
                name: _logName);
          } else {
            appLog.warning('Final result for $id does not contain WhatsApp metadata',
                name: _logName);
          }

          // Check *again* if attachment exists after await
          if (!_protoAttachmentContents.containsKey(id)) {
            appLog.warning('Attachment $id removed while fetching final result.', name: _logName);
            _protoSubscriptions.remove(id);
            return;
          }

          // Update based on final result, potentially preserving last known progress from stream
          double progressToKeep =
              attachment.processingProgress; // Keep progress from stream updates by default
          if (finalResult.status == UploadStatus.UPLOAD_STATUS_COMPLETED) {
            progressToKeep = 100.0; // Ensure 100% on completion
          } else if (finalResult.status == UploadStatus.UPLOAD_STATUS_FAILED ||
              finalResult.status == UploadStatus.UPLOAD_STATUS_CANCELLED) {
            // Keep last known progress unless final result explicitly provides one (though unlikely for failed/cancelled)
            if (finalResult.hasProcessingProgress()) {
              progressToKeep = finalResult.processingProgress;
            }
          }

          // Apply type-specific metadata enhancements using registered enhancers
          _enhanceMetadata(finalResult);

          appLog.debug('Final result received for $id with status: ${finalResult.status}',
              name: _logName);
          if (finalResult.hasWhatsappMetadata()) {
            appLog.debug(
                'Final WhatsApp metadata received: ${finalResult.whatsappMetadata.messageCount} messages, '
                '${finalResult.whatsappMetadata.participantCount} participants, '
                'isGroupChat: ${finalResult.whatsappMetadata.isGroupChat}',
                name: _logName);
          }

          // Update content type
          if (finalResult.contentType.isNotEmpty) {
            attachment.contentType = finalResult.contentType;
          }

          // Make sure the URL content is properly formatted
          if (finalResult.status == UploadStatus.UPLOAD_STATUS_COMPLETED &&
              finalResult.content.isNotEmpty &&
              !finalResult.content.startsWith('URL:')) {
            // Ensure content starts with URL: prefix for completed status
            attachment.content = 'URL:${finalResult.content}';
          } else {
            attachment.content = finalResult.content; // Update content as-is
          }

          attachment.status = finalResult.status;
          attachment.errorMessage = finalResult.errorMessage; // Update error message
          attachment.expiresAt = finalResult.expiresAt;
          attachment.fileName = finalResult.fileName;
          attachment.mimeType = finalResult.mimeType;
          attachment.processingProgress = progressToKeep; // Apply potentially adjusted progress

          // Copy over other specific metadata fields
          if (finalResult.hasWhatsappMetadata()) {
            attachment.whatsappMetadata = finalResult.whatsappMetadata;
            appLog.debug('Applied WhatsApp metadata from final result to attachment $id',
                name: _logName);

            // Force a notification to ensure UI updates
            appLog.debug('Forcing notification for WhatsApp metadata update', name: _logName);
            notifyListeners();
          } else if (attachment.fileName.toLowerCase().contains('whatsapp') ||
              attachment.fileName.toLowerCase().contains('chat')) {
            appLog.warning(
                'WhatsApp file $id completed processing but final result has no WhatsApp metadata',
                name: _logName);
          }

          if (finalResult.hasArticleMetadata()) {
            attachment.articleMetadata = finalResult.articleMetadata;
          }
          if (finalResult.hasYoutubeMetadata()) {
            attachment.youtubeMetadata = finalResult.youtubeMetadata;
          }
          if (finalResult.hasZipMetadata()) {
            attachment.zipMetadata = finalResult.zipMetadata;
            appLog.debug('Applied ZIP metadata from final result to attachment $id',
                name: _logName);
          }
          if (finalResult.hasFileMetadata()) {
            attachment.fileMetadata = finalResult.fileMetadata;
          }

          notifyListeners();
        } catch (e, s) {
          appLog.error('Failed to fetch final proto processing result for $id ($serverId)',
              name: _logName, error: e, stackTrace: s);
          // Update attachment to failed state if fetching final result fails
          if (_protoAttachmentContents.containsKey(id)) {
            final currentAttachment = _protoAttachmentContents[id]!;
            currentAttachment.status = UploadStatus.UPLOAD_STATUS_FAILED;
            currentAttachment.errorMessage = 'Failed to fetch final result: $e';
            notifyListeners();
          }
        }
        _protoSubscriptions.remove(id); // Remove subscription when done
      },
      cancelOnError: true, // Cancel subscription automatically on error
    );

    _protoSubscriptions[id] = subscription; // Store the subscription

    // Handle immediate cancellation if attachment is removed right after registration
    if (!_protoAttachmentContents.containsKey(id)) {
      appLog.warning(
          'Attachment $id removed immediately after monitoring started. Cancelling subscription.',
          name: _logName);
      subscription.cancel();
      _protoSubscriptions.remove(id);
    }
  }

  /// Check if an attachment content is a server reference
  bool isServerReference(String content) {
    return content.startsWith('ID:') && content.length > 3;
  }

  /// Check if an attachment content is a URL reference
  bool isUrlReference(String content) {
    return content.startsWith('URL:') && content.length > 4;
  }

  /// Check if an attachment content is any kind of reference (not direct content)
  bool isReference(String content) {
    return isServerReference(content) || isUrlReference(content);
  }

  /// Extract server ID from reference
  String? extractServerId(String content) {
    if (isServerReference(content)) {
      return content.substring(3); // Remove 'ID:' prefix
    }
    return null;
  }

  /// Extract content URL from reference
  String? extractContentUrl(String content) {
    if (isUrlReference(content)) {
      return content.substring(4); // Remove 'URL:' prefix
    }
    return null;
  }

  /// Get attachment content, potentially from server
  /// Returns either the direct content or a reference to where the content can be accessed
  /// Content references can be:
  /// - 'ID:xxx' - Server processing ID reference
  /// - 'URL:xxx' - URL where content can be accessed
  Future<String?> getAttachmentContent(String id, FileProcessingClient? client) async {
    final attachment = _attachmentContents[id];
    if (attachment == null) return null;

    final content = attachment['content'] as String?;
    if (content == null) return null;

    // Check if it's a server reference
    if (isServerReference(content) && client != null) {
      final serverId = extractServerId(content);
      if (serverId != null) {
        // Don't check status here. Just return the reference.
        // The _monitorProcessingStatus method will update the content
        // to a URL reference upon completion.
        return content; // Return 'ID:serverId'
      }
    }

    // If it's not a server reference or no client provided, return content directly
    return content;
  }

  /// Get attachment content by ID
  Map<String, dynamic>? getAttachment(String id) {
    return _attachmentContents[id];
  }

  /// Get the most recent attachment of a specific type using Protocol Buffers
  ProcessingResult? getMostRecentAttachmentByTypeProto(String type) {
    ProcessingResult? mostRecent;
    DateTime mostRecentTime = DateTime(1970);

    appLog.debug('Searching for most recent proto attachment of type: $type', name: _logName);

    for (final entry in _protoAttachmentContents.entries) {
      if (entry.value.contentType == type) {
        // Use the expiresAt field or current time if not available
        DateTime timestamp;
        if (entry.value.expiresAt.isNotEmpty) {
          try {
            timestamp = DateTime.parse(entry.value.expiresAt);
          } catch (_) {
            timestamp = DateTime.now();
          }
        } else {
          timestamp = DateTime.now();
        }

        if (timestamp.isAfter(mostRecentTime)) {
          mostRecentTime = timestamp;
          mostRecent = entry.value;
        }
      }
    }

    appLog.debug('Most recent proto attachment found: ${mostRecent?.jobId}', name: _logName);
    return mostRecent;
  }

  /// Check if there's an attachment of a specific type available
  bool hasAttachmentOfType(String type) {
    return _attachmentContents.values.any((attachment) => attachment['type'] == type);
  }

  /// Check if there's an attachment of a specific type available using Protocol Buffers
  bool hasAttachmentOfTypeProto(String type) {
    return _protoAttachmentContents.values.any((result) => result.contentType == type);
  }

  /// Removes a specific attachment by its ID.
  void removeAttachment(String id) {
    final attachment = _attachmentContents.remove(id);
    final protoAttachment = _protoAttachmentContents.remove(id);

    if (attachment != null || protoAttachment != null) {
      appLog.debug('Removing attachment: $id', name: _logName);
      // Cancel proto subscription if exists
      _protoSubscriptions[id]?.cancel();
      _protoSubscriptions.remove(id);

      notifyListeners();
    }
  }

  /// Clear all attachments including both legacy and proto formats
  void clearAttachments() {
    _attachmentContents.clear();
    _protoAttachmentContents.clear();

    // Cancel all proto subscriptions
    for (var sub in _protoSubscriptions.values) {
      sub.cancel();
    }
    _protoSubscriptions.clear();

    appLog.debug('All attachments cleared (both legacy and proto)', name: _logName);
    notifyListeners();
  }

  /// Get all server attachments using Protocol Buffers
  List<ProcessingResult> getAllServerAttachments() {
    return _protoAttachmentContents.values.where((result) => result.jobId.isNotEmpty).toList();
  }

  /// Get the most recent attachment for each content type using Protocol Buffers
  /// Returns a map where keys are content types and values are the most recent attachment of that type
  Map<String, ProcessingResult?> getAllAttachmentsByType() {
    appLog.debug('Getting all attachments by type in batch', name: _logName);

    // First, collect all content types
    final Set<String> contentTypes =
        _protoAttachmentContents.values.map((result) => result.contentType).toSet();

    // If there are no attachments, return an empty map to avoid unnecessary processing
    if (contentTypes.isEmpty) {
      appLog.debug('No attachments found for any type', name: _logName);
      return {};
    }

    // For each content type, get the most recent attachment
    final Map<String, ProcessingResult?> result = {};
    for (final type in contentTypes) {
      result[type] = getMostRecentAttachmentByTypeProto(type);
    }

    appLog.debug('Found ${result.length} attachment types in batch query', name: _logName);
    return result;
  }
}
