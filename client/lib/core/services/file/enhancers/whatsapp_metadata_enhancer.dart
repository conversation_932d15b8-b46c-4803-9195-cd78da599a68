import 'package:promz/core/services/file/base/enhanced_file_type_service.dart';
import 'package:promz/generated/common.pb.dart'; // For UploadStatus
import 'package:promz/generated/content_upload.pb.dart';
import 'package:promz/generated/whatsapp_metadata.pb.dart';
import 'package:promz_common/promz_common.dart';

/// Service for enhancing WhatsApp chat metadata
///
/// This service enhances ProcessingResult objects with proper WhatsApp chat metadata,
/// ensuring that participants, message count, and other information is properly populated
/// for display in the UI.
class WhatsAppMetadataEnhancer extends EnhancedFileTypeService {
  static const _logName = 'WhatsAppMetadataEnhancer';

  @override
  bool canEnhanceMetadata(ProcessingResult result) {
    // Check if this is a WhatsApp chat or related content
    final fileNameLower = result.fileName.toLowerCase();
    final hasWhatsAppInName = fileNameLower.contains('whatsapp');
    final hasChatInName = fileNameLower.contains('chat');
    final hasConversationContentType = result.contentType == 'conversation';
    final hasWhatsAppMetadata = result.hasWhatsappMetadata();
    final sourceTypeIsWhatsApp = result.sourceType.toLowerCase() == 'whatsapp';

    final isWhatsAppContent = hasWhatsAppInName ||
        (hasChatInName && hasConversationContentType) ||
        hasWhatsAppMetadata ||
        sourceTypeIsWhatsApp;

    appLog.debug('canEnhanceMetadata: $isWhatsAppContent for ${result.fileName}', name: _logName);
    return isWhatsAppContent;
  }

  @override
  bool enhanceProcessingResult(ProcessingResult result, [Map<String, dynamic>? extractedMetadata]) {
    appLog.debug('Enhancing WhatsApp metadata for ${result.fileName}', name: _logName);

    // First, check if WhatsApp metadata already exists
    WhatsAppMetadata metadata;
    if (result.hasWhatsappMetadata()) {
      // Update existing metadata
      metadata = result.whatsappMetadata;
      appLog.debug('Updating existing WhatsApp metadata', name: _logName);
    } else {
      // Create new metadata
      metadata = WhatsAppMetadata();
      appLog.debug('Creating new WhatsApp metadata', name: _logName);

      // Set chat name from filename
      final fileName = result.fileName;
      if (fileName.toLowerCase().contains('whatsapp chat with')) {
        final nameOnly = fileName.split('.').first;
        metadata.chatName = nameOnly;
      } else {
        // Extract basename without extension
        final nameParts = fileName.split('.');
        if (nameParts.isNotEmpty) {
          metadata.chatName = nameParts.first;
        } else {
          metadata.chatName = fileName;
        }
      }
    }

    // Extract metadata from the server's response
    // If we have extracted metadata, use it
    if (extractedMetadata != null) {
      appLog.debug('Using extracted metadata for WhatsApp enhancement', name: _logName);

      if (extractedMetadata.containsKey('groupName') &&
          extractedMetadata['groupName'] != null &&
          extractedMetadata['groupName'].toString().isNotEmpty) {
        metadata.groupName = extractedMetadata['groupName'].toString();
        appLog.debug('Set group name to: ${metadata.groupName}', name: _logName);
      }

      // Extract participants
      if (extractedMetadata.containsKey('participants')) {
        try {
          final participants = extractedMetadata['participants'];
          if (participants is List) {
            metadata.participants.clear();
            for (final participant in participants) {
              if (participant is String && participant.isNotEmpty) {
                metadata.participants.add(participant);
              }
            }
            metadata.participantCount = metadata.participants.length;
            appLog.debug('Added ${metadata.participantCount} participants', name: _logName);
          }
        } catch (e) {
          appLog.error('Error extracting participants', name: _logName, error: e);
        }
      }

      // Extract message count
      if (extractedMetadata.containsKey('messageCount')) {
        try {
          final count = extractedMetadata['messageCount'];
          if (count is int) {
            metadata.messageCount = count;
          } else if (count is String) {
            metadata.messageCount = int.tryParse(count) ?? 0;
          }
          appLog.debug('Set message count to: ${metadata.messageCount}', name: _logName);
        } catch (e) {
          appLog.error('Error extracting message count', name: _logName, error: e);
        }
      }

      // Determine if it's a group chat
      final isGroupChat = metadata.participantCount > 1 ||
          (extractedMetadata.containsKey('isGroupChat') &&
              extractedMetadata['isGroupChat'] == true);
      metadata.isGroupChat = isGroupChat;
    }

    // Try to extract additional information from the metadata fields
    if (result.hasZipMetadata() && result.zipMetadata.hasIsWhatsappChat()) {
      appLog.debug('Processing WhatsApp chat from ZIP metadata', name: _logName);

      // If we have ZIP metadata that indicates this is a WhatsApp chat,
      // ensure the metadata reflects this
      if (metadata.chatName.isEmpty && result.hasDisplayName()) {
        metadata.chatName = result.displayName;
      }
    }

    // Extract additional metadata from specific fields in the ProcessingResult
    if (result.sourceType.isNotEmpty && result.sourceType.toLowerCase() == 'whatsapp') {
      metadata.chatName = metadata.chatName.isEmpty ? 'WhatsApp Chat' : metadata.chatName;

      if (metadata.participantCount == 0 && result.hasTitle()) {
        // Try to extract participant info from the title
        final title = result.title;
        if (title.contains('with')) {
          final parts = title.split('with');
          if (parts.length > 1 && parts[1].trim().isNotEmpty) {
            metadata.participants.add(parts[1].trim());
            metadata.participantCount = 1;
          }
        }
      }
    }

    // Ensure participants count is consistent
    if (metadata.participants.isNotEmpty && metadata.participantCount == 0) {
      metadata.participantCount = metadata.participants.length;
    }

    // If it has participants and no message count, add a default
    if (metadata.participantCount > 0 && metadata.messageCount == 0) {
      metadata.messageCount = 5; // Default to show some messages exist
    }

    // Determine if it's a group chat based on available data
    if (metadata.participantCount > 1) {
      metadata.isGroupChat = true;
    }

    // Update content type if needed
    if (result.contentType.isEmpty) {
      result.contentType = 'conversation';
    }

    // Set sourceType if not already set
    if (result.sourceType.isEmpty) {
      result.sourceType = 'whatsapp';
    }

    // Apply the metadata to the result
    result.whatsappMetadata = metadata;

    appLog.info(
        'Enhanced WhatsApp metadata: ${metadata.participantCount} participants, ${metadata.messageCount} messages, group chat: ${metadata.isGroupChat}',
        name: _logName);

    return true;
  }

  @override
  bool canHandle(String path, [String? mimeType]) {
    final lowerPath = path.toLowerCase();
    return lowerPath.contains('whatsapp') || lowerPath.contains('chat');
  }

  @override
  Future<String?> extractContent(String path) async {
    // This service doesn't extract content directly
    return null;
  }

  @override
  Future<Map<String, dynamic>> extractMetadata(String path) async {
    // This service focuses on enhancing server-provided metadata rather than extraction
    return {};
  }

  @override
  String getMimeType(String path) {
    // Call the super implementation as required by @mustCallSuper
    super.getMimeType(path);
    return 'text/plain';
  }

  @override
  String getTypeName() {
    // Call the super implementation as required by @mustCallSuper
    super.getTypeName();
    return 'WhatsApp Chat';
  }

  /// Creates or updates placeholder metadata for a WhatsApp chat during processing
  ///
  /// This method is specifically for handling the processing state, creating appropriate
  /// placeholder values that indicate to the UI that the content is still being processed.
  ///
  /// @param result The ProcessingResult to enhance with placeholder metadata
  /// @return True if metadata was created or updated, false otherwise
  bool enhanceProcessingStateMetadata(ProcessingResult result) {
    appLog.debug('Creating placeholder WhatsApp metadata for processing state', name: _logName);

    // Only create placeholder metadata if the result is in processing state
    if (result.status != UploadStatus.UPLOAD_STATUS_PROCESSING) {
      appLog.debug('Not in processing state, skipping placeholder metadata', name: _logName);
      return false;
    }

    // Create new metadata if none exists
    if (!result.hasWhatsappMetadata()) {
      final metadata = WhatsAppMetadata();

      // Extract chat name from filename
      final fileName = result.fileName.split('.').first;
      metadata.chatName = fileName;

      // Set placeholder values for processing state
      metadata.messageCount = -1; // -1 indicates "processing"
      metadata.participantCount = -1; // -1 indicates "processing"

      // Apply the metadata to the result
      result.whatsappMetadata = metadata;

      // Ensure content type is set
      if (result.contentType.isEmpty) {
        result.contentType = 'conversation';
      }

      appLog.debug('Created new placeholder WhatsApp metadata for ${result.fileName}',
          name: _logName);
      return true;
    }
    // Update existing metadata with placeholder values if needed
    else {
      final metadata = result.whatsappMetadata;
      bool updated = false;

      // Set message count placeholder if not set
      if (metadata.messageCount == 0) {
        metadata.messageCount = -1; // -1 indicates "processing"
        updated = true;
      }

      // Set participant count placeholder if not set
      if (metadata.participants.isEmpty && metadata.participantCount == 0) {
        metadata.participantCount = -1; // -1 indicates "processing"
        updated = true;
      }

      if (updated) {
        appLog.debug('Updated existing WhatsApp metadata with placeholder values', name: _logName);
      }

      return updated;
    }
  }
}
