import 'package:grpc/grpc.dart';
import 'package:promz/core/services/api/api_client.dart';
import 'package:promz/core/services/client_context_service.dart';
import 'package:promz/core/services/file/file_upload_service.dart';
import 'package:promz/core/services/license_manager_service.dart';
import 'package:promz/generated/content_upload.pbgrpc.dart';
import 'package:promz_common/config/api_config.dart';
import 'package:promz_common/promz_common.dart';

/// Provider for creating and configuring FileUploadService instances
class FileUploadServiceProvider {
  static const _logName = 'FileUploadServiceProvider';

  /// Create a properly configured FileUploadService
  static FileUploadService create() {
    appLog.debug('Creating FileUploadService instance', name: _logName);

    // Get license manager instance
    final licenseManager = LicenseManagerService();

    // Get the ApiClient from ClientContextService to ensure we're using the singleton instance
    ApiClient apiClient;
    try {
      // Try to get the ApiClient from ClientContextService
      final clientContext = ClientContextService();
      apiClient = clientContext.apiClient;
      appLog.debug('Using ApiClient from ClientContextService', name: _logName);
    } catch (e) {
      // If ClientContextService is not initialized, create a temporary ApiClient
      // This should only happen during testing or early app initialization
      appLog.warning('ClientContextService not initialized, creating temporary ApiClient',
          name: _logName);
      apiClient = ApiClient(licenseManager: licenseManager);
    }

    // Get the gRPC URL and port from ApiConfig
    final grpcUrl = ApiConfig.grpcUrl;
    final host = ApiConfig.baseHost;
    final port = ApiConfig.grpcPort;

    // Determine if we should use secure credentials - use same logic as ApiClient
    final isLocalDevelopment = ApiConfig.isLocalDevelopmentEnvironment;
    final useSecure = ApiConfig.protocol == 'https' && !isLocalDevelopment;

    appLog.debug('Creating gRPC channel for $grpcUrl with secure: $useSecure', name: _logName);

    // Create a gRPC channel with proper configuration
    final channel = ClientChannel(
      host,
      port: port,
      options: ChannelOptions(
        // Use same TLS determination logic as ApiClient
        credentials:
            useSecure ? const ChannelCredentials.secure() : const ChannelCredentials.insecure(),
        // Add timeout settings
        idleTimeout: const Duration(minutes: 5),
        connectTimeout: const Duration(seconds: 30),
      ),
    );

    appLog.debug('gRPC channel configured for ${ApiConfig.baseHost}:${ApiConfig.grpcPort}',
        name: _logName);

    // Create the gRPC client with auth metadata
    final grpcClient = ContentUploadServiceClient(
      channel,
      options: CallOptions(
        metadata: _createAuthMetadata(licenseManager),
      ),
    );

    appLog.debug('ContentUploadServiceClient created with auth metadata', name: _logName);

    return FileUploadService(
      apiClient: apiClient,
      grpcClient: grpcClient,
    );
  }

  /// Create authentication metadata for gRPC calls
  static Map<String, String> _createAuthMetadata(LicenseManagerService licenseManager) {
    // If we have an API key, add it to metadata
    final apiKey = licenseManager.apiKey;
    if (apiKey != null && apiKey.isNotEmpty) {
      return {'x-api-key': apiKey};
    }

    // Otherwise, return empty metadata
    return {};
  }
}
