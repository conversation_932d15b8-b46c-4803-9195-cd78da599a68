import 'package:promz/core/services/file/base/enhanced_file_type_service.dart';
import 'package:promz/core/services/file/enhancers/whatsapp_metadata_enhancer.dart';
import 'package:promz/generated/content_upload.pb.dart';
import 'package:promz_common/promz_common.dart';

/// Registry for metadata enhancer services
///
/// This class maintains a registry of services that can enhance
/// metadata in ProcessingResults. It provides a centralized way to
/// enhance metadata without requiring service-specific logic in the
/// attachment registry or other services.
class MetadataEnhancerRegistry {
  static const _logName = 'MetadataEnhancerRegistry';

  /// Singleton instance
  static final MetadataEnhancerRegistry instance = MetadataEnhancerRegistry._();

  /// Private constructor
  MetadataEnhancerRegistry._() {
    // Register default enhancers
    initialize();
  }

  final List<EnhancedFileTypeService> _enhancers = [];

  /// Initialize the registry with default enhancers
  /// This method registers all the standard enhancers
  void initialize() {
    appLog.debug('Initializing MetadataEnhancerRegistry with default enhancers', name: _logName);

    // Register WhatsApp metadata enhancer
    registerEnhancer(WhatsAppMetadataEnhancer());

    // Add other enhancers here as needed

    appLog.debug('MetadataEnhancerRegistry initialized with ${_enhancers.length} enhancers',
        name: _logName);
  }

  /// Register a metadata enhancer service
  void registerEnhancer(EnhancedFileTypeService enhancer) {
    if (!_enhancers.contains(enhancer)) {
      _enhancers.add(enhancer);
      appLog.debug('Registered metadata enhancer: ${enhancer.runtimeType}', name: _logName);
    }
  }

  /// Get an enhancer by type name
  ///
  /// This method finds an enhancer that can handle the specified type.
  /// It's useful when you need to access a specific enhancer directly.
  ///
  /// @param typeName The type name to find an enhancer for (e.g., 'whatsapp', 'pdf')
  /// @return The enhancer if found, null otherwise
  EnhancedFileTypeService? getEnhancerForType(String typeName) {
    final lowerTypeName = typeName.toLowerCase();

    for (final enhancer in _enhancers) {
      final enhancerTypeName = enhancer.getTypeName().toLowerCase();
      if (enhancerTypeName.contains(lowerTypeName)) {
        return enhancer;
      }
    }

    appLog.debug('No enhancer found for type: $typeName', name: _logName);
    return null;
  }

  /// Enhance a ProcessingResult with appropriate metadata
  ///
  /// This method will find the appropriate enhancer for the given result
  /// and use it to enhance the metadata.
  ///
  /// @param result The ProcessingResult to enhance
  /// @param extractedMetadata Optional pre-extracted metadata
  /// @return True if any enhancer was able to enhance the metadata, false otherwise
  bool enhanceMetadata(ProcessingResult result, [Map<String, dynamic>? extractedMetadata]) {
    if (_enhancers.isEmpty) {
      appLog.debug('No metadata enhancers registered', name: _logName);
      return false;
    }

    // Find the appropriate enhancer for this result
    for (final enhancer in _enhancers) {
      if (enhancer.canEnhanceMetadata(result)) {
        appLog.debug('Using enhancer ${enhancer.runtimeType} for ${result.fileName}',
            name: _logName);
        try {
          return enhancer.enhanceProcessingResult(result, extractedMetadata);
        } catch (e) {
          appLog.error('Error enhancing metadata with ${enhancer.runtimeType}',
              name: _logName, error: e);
        }
      }
    }

    appLog.debug('No suitable enhancer found for ${result.fileName}', name: _logName);
    return false;
  }
}
