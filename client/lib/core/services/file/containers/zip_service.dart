import 'dart:convert';
import 'dart:developer' as dev;
import 'dart:io';
import 'package:archive/archive.dart';
import 'package:path/path.dart' as path;
import 'package:promz/core/models/file/processed_file.dart';
import 'package:promz/core/services/attachment/attachment_registry_service.dart';
import 'package:promz/core/services/file/base/container_file_service.dart';
import 'package:promz/core/services/file/base/file_type_service.dart';
import 'package:promz/core/services/file/types/pdf_service.dart';
import 'package:promz/core/services/file/types/whatsapp_service.dart';
import 'package:promz/generated/content_upload.pb.dart';
import 'package:promz/generated/whatsapp_metadata.pb.dart';
import 'package:promz_common/promz_common.dart';

class ZipService implements ContainerFileService {
  static const _logName = 'ZipService';

  final List<FileTypeService> _fileServices;
  final AttachmentRegistryService _attachmentRegistry;

  ZipService(this._fileServices, this._attachmentRegistry);

  @override
  bool canHandle(String path, [String? mimeType]) {
    final pathLower = path.toLowerCase();
    final mimeTypeLower = (mimeType ?? '').toLowerCase();

    return pathLower.endsWith('.zip') ||
        mimeTypeLower == 'application/zip' ||
        mimeTypeLower == 'application/x-zip-compressed' ||
        mimeTypeLower == 'application/x-zip';
  }

  @override
  Future<List<ProcessedFile>> extractFiles(String path) async {
    try {
      final bytes = await File(path).readAsBytes();
      final archive = ZipDecoder().decodeBytes(bytes);
      final processedFiles = <ProcessedFile>[];

      for (final file in archive) {
        if (!file.isFile) continue;

        try {
          final service = _findAppropriateService(file.name);
          final fileContent = file.content as List<int>;
          String? textContent;

          if (service is PDFService) {
            // Create a temporary file to process PDF content
            final tempDir = Directory.systemTemp;
            final tempFile = File('${tempDir.path}/${file.name}');
            await tempFile.writeAsBytes(fileContent);
            textContent = await service.extractContent(tempFile.path);
            await tempFile.delete();
          } else {
            textContent = await _decodeFileContent(file);
          }

          processedFiles.add(ProcessedFile(
            fileName: file.name,
            mimeType: service.getMimeType(file.name),
            textContent: textContent,
            binaryContent: fileContent,
            size: file.size,
            lastModified: DateTime.fromMillisecondsSinceEpoch(file.lastModTime * 1000),
          ));
        } catch (e) {
          dev.log('Error processing file in zip: ${file.name}', error: e);
        }
      }

      // After extracting all files, analyze them for specific content types
      await _analyzeExtractedContent(processedFiles, path);

      appLog.debug('Processed ${processedFiles.length} files from ZIP', name: _logName);
      return processedFiles;
    } catch (e, stack) {
      appLog.error('Error processing ZIP file', name: _logName, error: e, stackTrace: stack);
      rethrow;
    }
  }

  /// Analyzes extracted content to detect specific content types like WhatsApp chats
  Future<void> _analyzeExtractedContent(List<ProcessedFile> files, String originalPath) async {
    try {
      // Look for WhatsApp chat files
      final whatsAppFiles = findWhatsAppFiles(files);
      if (whatsAppFiles.isNotEmpty) {
        await processWhatsAppContent(whatsAppFiles, originalPath);
      }

      // Future: Add detection for other chat platforms (Telegram, Signal, etc.)
    } catch (e) {
      appLog.error('Error analyzing extracted content', name: _logName, error: e);
    }
  }

  /// Finds WhatsApp chat files in a list of processed files
  /// Made public to be used by ContentProcessingService
  List<ProcessedFile> findWhatsAppFiles(List<ProcessedFile> files) {
    appLog.debug('Searching for WhatsApp files among ${files.length} extracted files',
        name: _logName);

    // Look for common WhatsApp export patterns
    final whatsAppFiles = files.where((file) {
      final fileName = file.fileName.toLowerCase();
      return fileName.contains('chat') && fileName.endsWith('.txt');
    }).toList();

    if (whatsAppFiles.isNotEmpty) {
      appLog.debug(
          'Found ${whatsAppFiles.length} WhatsApp files: ${whatsAppFiles.map((f) => f.fileName).join(', ')}',
          name: _logName);
    } else {
      appLog.debug('No WhatsApp files found in the ZIP archive', name: _logName);
    }

    return whatsAppFiles;
  }

  /// Processes WhatsApp content using WhatsAppService
  /// Returns the attachment ID if successfully processed, null otherwise
  Future<String?> processWhatsAppContent(
      List<ProcessedFile> whatsAppFiles, String originalPath) async {
    appLog.debug('Processing WhatsApp content from ${whatsAppFiles.length} files', name: _logName);

    try {
      if (whatsAppFiles.isEmpty) {
        appLog.debug('No WhatsApp files to process', name: _logName);
        return null;
      }

      // Find WhatsApp service from registered services
      final whatsAppService = _fileServices.firstWhere(
        (service) => service is WhatsAppService,
        orElse: () => throw Exception('WhatsAppService not registered'),
      ) as WhatsAppService;

      // Process the first valid WhatsApp file
      final file = whatsAppFiles.first;
      if (file.textContent != null) {
        final result = await whatsAppService.processContent(
          content: file.textContent!,
          fileName: file.fileName,
          originalPath: originalPath,
        );

        if (result.content != null) {
          // Create a ProcessingResult for WhatsApp chat content
          final attachmentId = 'whatsapp_${DateTime.now().millisecondsSinceEpoch}';

          final processingResult = ProcessingResult()
            ..contentType = 'conversation'
            ..jobId = attachmentId
            ..content = result.content!
            ..expiresAt = DateTime.now().add(const Duration(hours: 24)).toIso8601String();

          // Add WhatsApp metadata if available
          if (result.metadata != null) {
            if (result.metadata!.containsKey('participants')) {
              // Create a protobuf WhatsAppMetadata instance
              final whatsappMetadata = WhatsAppMetadata();

              if (result.metadata!.containsKey('groupName')) {
                // Set the groupName field directly
                whatsappMetadata.groupName = result.metadata!['groupName'] as String;
              }

              if (result.metadata!.containsKey('chatName')) {
                // Set the chatName field directly
                whatsappMetadata.chatName = result.metadata!['chatName'] as String;
              }

              final participants = result.metadata!['participants'] as List<dynamic>;
              // Set the participantCount field directly
              whatsappMetadata.participantCount = participants.length;

              for (var participant in participants) {
                whatsappMetadata.participants.add(participant.toString());
              }

              if (result.metadata!.containsKey('messageCount')) {
                // Set the messageCount field directly
                whatsappMetadata.messageCount = result.metadata!['messageCount'] as int;
              }

              // Set the whatsappMetadata field directly
              processingResult.whatsappMetadata = whatsappMetadata;
            }
          }

          // Register with the new protobuf-based method
          _attachmentRegistry.registerAttachment(
            id: attachmentId,
            result: processingResult,
          );

          appLog.debug('Registered WhatsApp chat as conversation attachment with ID: $attachmentId',
              name: _logName);
          return attachmentId;
        }
      }
      return null;
    } catch (e) {
      appLog.error('Error processing WhatsApp content', name: _logName, error: e);
      return null;
    }
  }

  @override
  Future<Map<String, dynamic>> extractMetadata(String path) async {
    try {
      final bytes = await File(path).readAsBytes();
      final archive = ZipDecoder().decodeBytes(bytes);
      final stat = await File(path).stat();

      return {
        'fileCount': archive.files.length,
        'totalSize': bytes.length,
        'compressedSize': stat.size,
        'compressionRatio': bytes.isNotEmpty ? (stat.size / bytes.length * 100).round() : 100,
        'lastModified': stat.modified.toIso8601String(),
        'files': archive.files
            .where((f) => f.isFile)
            .map((f) => {
                  'name': f.name,
                  'size': f.size,
                  'lastModified':
                      DateTime.fromMillisecondsSinceEpoch(f.lastModTime * 1000).toIso8601String(),
                })
            .toList(),
      };
    } catch (e) {
      appLog.error('Error extracting ZIP metadata', name: _logName, error: e);
      return {};
    }
  }

  @override
  String getMimeType(String path) => 'application/zip';

  @override
  String getTypeName() => 'ZIP Archive';

  FileTypeService _findAppropriateService(String fileName) {
    return _fileServices.firstWhere(
      (s) => s.canHandle(fileName),
      orElse: () => _DefaultFileService(),
    );
  }

  Future<String?> _decodeFileContent(ArchiveFile file) async {
    try {
      return utf8.decode(file.content as List<int>);
    } catch (e) {
      appLog.debug('Failed to decode ${file.name} with UTF-8: $e', name: _logName);
      try {
        return latin1.decode(file.content as List<int>);
      } catch (e) {
        appLog.debug('Failed to decode ${file.name} with latin1: $e', name: _logName);
        return null;
      }
    }
  }
}

/// Default service for unknown file types
class _DefaultFileService implements FileTypeService {
  @override
  bool canHandle(String path, [String? mimeType]) => true;

  @override
  Future<String?> extractContent(String path) async => null;

  @override
  Future<Map<String, dynamic>> extractMetadata(String path) async => {};

  @override
  String getMimeType(String filePath) {
    final ext = path.extension(filePath);
    switch (ext) {
      case '.txt':
        return 'text/plain';
      case '.pdf':
        return 'application/pdf';
      case '.json':
        return 'application/json';
      default:
        return 'application/octet-stream';
    }
  }

  @override
  String getTypeName() => 'Unknown';
}
