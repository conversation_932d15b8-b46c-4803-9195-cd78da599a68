import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'package:http/http.dart' as http;
import 'package:path/path.dart' as path;
import 'package:promz/core/services/api/api_client.dart';
import 'package:promz/core/services/file/file_upload_service.dart';
import 'package:promz_common/config/api_config.dart';
import 'package:promz_common/promz_common.dart';
import 'package:promz/generated/content_upload.pbgrpc.dart';
import 'package:grpc/grpc.dart';

/// Client for interacting with the server-side file processing service
///
/// This client handles:
/// 1. Initial metadata check to verify file size limits
/// 2. File upload with streaming support
/// 3. Result retrieval
class FileProcessingClient {
  static const _logName = 'FileProcessingClient';

  final ApiClient _apiClient;
  final http.Client _httpClient;
  final ContentUploadServiceClient _grpcClient;

  FileProcessingClient({
    required ApiClient apiClient,
    http.Client? httpClient,
    required ContentUploadServiceClient grpcClient,
  })  : _apiClient = apiClient,
        _httpClient = httpClient ?? http.Client(),
        _grpcClient = grpcClient;

  /// Step 1: Check file metadata with the server
  /// This allows the server to determine if the file can be processed
  /// based on size limits, license tier, etc.
  Future<FileMetadataCheckResponse> checkFileMetadata(
    File file, {
    String? licenseTier,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final fileSize = await file.length();
      final fileName = path.basename(file.path);
      final mimeType = _getMimeType(fileName);

      // Default to free tier if not specified
      final tier = (licenseTier ?? 'free').toLowerCase();

      appLog.info('Checking file metadata: $fileName (${fileSize / (1024 * 1024)} MB)',
          name: _logName);

      // Use endpoint string instead of URI
      const endpoint = '/processing/check';

      final requestBody = {
        'fileName': fileName,
        'fileSize': fileSize,
        'mimeType': mimeType,
        'licenseTier': tier,
      };

      if (metadata != null) {
        requestBody['metadata'] = metadata;
      }

      final response = await _apiClient.post(
        endpoint,
        isProtected: true,
        body: jsonEncode(requestBody),
      );

      if (response.statusCode != 200) {
        // Handle specific error codes
        if (response.statusCode == 413) {
          // File size limit exceeded
          final errorResponse = jsonDecode(response.body);
          final maxSize = errorResponse['maxSize'] as int? ?? 10 * 1024 * 1024; // Default to 10MB
          final errorMessage =
              errorResponse['error'] as String? ?? 'File size exceeds the limit for $tier tier';

          throw FileSizeLimitExceededException(
            fileSize: fileSize,
            maxSize: maxSize,
            currentTier: tier,
            nextTier: _getNextTierForFileSize(fileSize),
            message: errorMessage,
          );
        }

        throw Exception('Failed to check file metadata: ${response.statusCode} - ${response.body}');
      }

      final Map<String, dynamic> responseData = jsonDecode(response.body);

      return FileMetadataCheckResponse(
        canUpload: responseData['canUpload'] as bool? ?? false,
        maxSize: responseData['maxSize'] as int? ?? 0,
        maxTokens: responseData['maxTokens'] as int? ?? 0,
        message: responseData['message'] as String? ?? '',
        estimatedTimeSeconds: responseData['estimatedTimeSeconds'] as int? ?? 30,
        licenseTier: responseData['licenseTier'] as String? ?? tier,
      );
    } catch (e, stack) {
      // Rethrow FileSizeLimitExceededException directly
      if (e is FileSizeLimitExceededException) {
        rethrow;
      }

      appLog.error('Error checking file metadata', name: _logName, error: e, stackTrace: stack);
      rethrow;
    }
  }

  /// Helper method to get MIME type based on file extension
  String _getMimeType(String fileName) {
    final extension = path.extension(fileName).toLowerCase();

    switch (extension) {
      case '.txt':
        return 'text/plain';
      case '.pdf':
        return 'application/pdf';
      case '.doc':
      case '.docx':
        return 'application/msword';
      case '.zip':
        return 'application/zip';
      case '.json':
        return 'application/json';
      default:
        return 'application/octet-stream';
    }
  }

  /// Helper method to determine the next tier based on file size
  String? _getNextTierForFileSize(int fileSize) {
    // These limits should match the server-side limits
    const Map<String, int> tierFileSizeLimits = {
      'free': 10 * 1024 * 1024, // 10MB
      'pro': 50 * 1024 * 1024, // 50MB
      'enterprise': 200 * 1024 * 1024, // 200MB
    };

    if (fileSize <= tierFileSizeLimits['free']!) {
      return null; // Free tier is sufficient
    } else if (fileSize <= tierFileSizeLimits['pro']!) {
      return 'Pro';
    } else if (fileSize <= tierFileSizeLimits['enterprise']!) {
      return 'Enterprise';
    } else {
      return null; // No tier supports this size
    }
  }

  /// Step 2: Upload file to server for processing after metadata check
  ///
  /// This method uploads the file in chunks for better performance and reliability
  /// It returns a FileUploadResponse with the job ID and initial status
  Future<FileUploadResponse> uploadFile(
    File file, {
    required FileMetadataCheckResponse metadataCheck,
    Map<String, dynamic>? metadata,
    void Function(double)? onProgress,
  }) async {
    try {
      final fileSize = await file.length();
      final fileName = path.basename(file.path);
      final mimeType = _getMimeType(fileName);

      appLog.info('Uploading file: $fileName (${fileSize / (1024 * 1024)} MB, type: $mimeType)',
          name: _logName);

      // Create a custom HTTP client with increased timeout for large files
      // The timeout increases with file size, with a minimum of 60 seconds
      final fileSizeMB = fileSize / (1024 * 1024);
      final timeout = Duration(seconds: max(60, (fileSizeMB * 5).round()));
      appLog.debug('Using timeout of ${timeout.inSeconds} seconds for upload', name: _logName);

      final uri = Uri.parse('${ApiConfig.baseUrl}/processing/upload');

      // Create multipart request
      final request = http.MultipartRequest('POST', uri);

      // Set longer timeouts for the request
      request.persistentConnection = true;

      // Add authentication header through ApiClient
      final headers = await _apiClient.getAuthHeaders(isProtected: true);
      request.headers.addAll(headers);

      // Add file with a buffered stream to handle large files better
      final fileStream = http.ByteStream(file.openRead().asBroadcastStream());

      final multipartFile = http.MultipartFile(
        'file',
        fileStream,
        fileSize,
        filename: fileName,
      );

      request.files.add(multipartFile);

      // Add metadata if provided
      if (metadata != null) {
        request.fields['metadata'] = jsonEncode(metadata);
      }

      // Add license tier from metadata check
      request.fields['licenseTier'] = metadataCheck.licenseTier;

      // Add file size information to help server prepare resources
      request.fields['fileSize'] = fileSize.toString();

      // Log upload start with detailed information
      appLog.debug('Starting upload of $fileName with size $fileSize bytes', name: _logName);

      // Send the request with timeout
      final streamedResponse = await request.send().timeout(timeout);

      // Log progress after send completes
      appLog.debug('Upload completed, waiting for server response', name: _logName);

      final response = await http.Response.fromStream(streamedResponse).timeout(timeout);

      if (response.statusCode != 200) {
        throw Exception('Failed to upload file: ${response.statusCode} - ${response.body}');
      }

      // Parse response
      final Map<String, dynamic> responseData = jsonDecode(response.body);

      appLog.info('File uploaded successfully: $fileName', name: _logName);

      // Check for token limit information
      final maxTokens = responseData['maxTokens'] as int?;
      if (maxTokens != null) {
        appLog.debug('Server token limit: $maxTokens tokens', name: _logName);
      }

      return FileUploadResponse(
        id: responseData['id'] as String? ?? responseData['jobId'] as String,
        status: responseData['status'] as String,
        estimatedTimeSeconds: responseData['estimatedTimeSeconds'] as int? ?? 30,
        maxTokens: maxTokens,
        fileSize: responseData['fileSize'] as int?,
        licenseTier: responseData['licenseTier'] as String?,
      );
    } catch (e, stack) {
      // Rethrow FileSizeLimitExceededException directly
      if (e is FileSizeLimitExceededException) {
        rethrow;
      }

      if (e is SocketException) {
        appLog.error('Network error during file upload: ${e.message}',
            name: _logName, error: e, stackTrace: stack);
      } else if (e is TimeoutException) {
        appLog.error('Upload timed out. This may happen with large files on slow connections.',
            name: _logName, error: e, stackTrace: stack);
      } else {
        appLog.error('Error uploading file', name: _logName, error: e, stackTrace: stack);
      }
      rethrow;
    }
  }

  /// Get processing results via gRPC
  Future<ProcessingResult> getProcessingResultProto(String jobId) async {
    appLog.debug('Fetching proto processing result for job ID: $jobId', name: _logName);
    try {
      final request = UploadStatusRequest()..jobId = jobId;
      final result = await _grpcClient.getUploadStatus(request);
      appLog.debug('Successfully fetched proto processing result for job ID: $jobId',
          name: _logName);
      return result;
    } on GrpcError catch (e, stack) {
      appLog.error('gRPC error getting processing result for job $jobId',
          name: _logName, error: e, stackTrace: stack);
      throw Exception(
          'Failed to get processing result via gRPC: ${e.message} (Code: ${e.codeName})');
    } catch (e, stack) {
      appLog.error('Error getting processing result proto for job $jobId',
          name: _logName, error: e, stackTrace: stack);
      rethrow;
    }
  }

  /// Get processing results (Legacy REST implementation)
  Future<FileProcessingResult> getResults(String id) async {
    try {
      appLog.debug('Getting processing results for ID: $id', name: _logName);

      final endpoint = '/processing/job/$id/result';

      // Use the ApiClient for authenticated requests
      final response = await _apiClient.get(endpoint, isProtected: true);

      if (response.statusCode != 200) {
        throw Exception('Failed to get results: ${response.statusCode}');
      }

      final Map<String, dynamic> responseData = jsonDecode(response.body);

      // Extract metadata and check for token information
      final metadata = responseData['metadata'] as Map<String, dynamic>? ?? {};

      // Check for token information in metadata
      int? tokensProcessed;
      int? tokensLimit;
      bool tokensExceeded = false;

      // First check in the metadata
      if (metadata.containsKey('tokensProcessed')) {
        tokensProcessed = metadata['tokensProcessed'] as int?;
      }

      if (metadata.containsKey('tokensLimit')) {
        tokensLimit = metadata['tokensLimit'] as int?;
      }

      if (metadata.containsKey('tokensExceeded')) {
        tokensExceeded = metadata['tokensExceeded'] as bool? ?? false;
      }

      // Then check in the main response (higher priority)
      if (responseData.containsKey('tokensProcessed')) {
        tokensProcessed = responseData['tokensProcessed'] as int?;
      }

      if (responseData.containsKey('tokensLimit')) {
        tokensLimit = responseData['tokensLimit'] as int?;
      }

      if (responseData.containsKey('tokensExceeded')) {
        tokensExceeded = responseData['tokensExceeded'] as bool? ?? false;
      }

      // Check for partial processing flag
      final partialProcessing = metadata['partialProcessing'] as bool? ?? false;

      // Log token usage if available
      if (tokensProcessed != null && tokensLimit != null) {
        final processedK = (tokensProcessed / 1000).round();
        final limitK = (tokensLimit / 1000).round();
        appLog.debug(
          'Result token usage: $processedK K / $limitK K ${tokensExceeded ? "(limit exceeded)" : ""}',
          name: _logName,
        );
      }

      // Ensure contentType is available, defaulting to 'unknown' if missing
      final contentType = responseData['contentType'] as String? ?? 'unknown';

      // Check for hasFullContent flag
      final hasFullContent =
          (responseData['hasFullContent'] as bool? ?? true) && !partialProcessing;

      // Get contentUrl - this is now the primary way to access content
      final contentUrl = responseData['contentUrl'] as String?;

      // Log if contentUrl is missing
      if (contentUrl == null || contentUrl.isEmpty) {
        appLog.warning(
          'Content URL is missing for processing result ID: $id',
          name: _logName,
        );
      }

      return FileProcessingResult(
        id: id,
        contentType: contentType,
        metadata: metadata,
        hasFullContent: hasFullContent,
        contentUrl: contentUrl,
        expiresAt: responseData['expiresAt'] != null
            ? DateTime.fromMillisecondsSinceEpoch(responseData['expiresAt'] as int)
            : null,
        tokensProcessed: tokensProcessed,
        tokensLimit: tokensLimit,
        tokensExceeded: tokensExceeded || partialProcessing,
      );
    } catch (e, stack) {
      appLog.error('Error getting processing results', name: _logName, error: e, stackTrace: stack);
      rethrow;
    }
  }

  /// Subscribe to real-time status updates for a processing job
  Stream<FileProcessingStatus> subscribeToStatusUpdates(String id) async* {
    try {
      // Initialize WebSocket connection through ApiClient
      final topic = 'processing:$id';

      // Subscribe to the topic through ApiClient
      final statusStream = _apiClient.subscribeToTopic(topic);

      // Transform the raw WebSocket messages into FileProcessingStatus objects
      yield* statusStream.map((data) {
        try {
          // Extract status information
          final status = data['status'] as String? ?? 'unknown';

          // Handle progress safely
          double progress = 0.0;
          if (data.containsKey('progress')) {
            final progressValue = data['progress'];
            if (progressValue is int) {
              progress = progressValue.toDouble();
            } else if (progressValue is double) {
              progress = progressValue;
            } else if (progressValue is String) {
              progress = double.tryParse(progressValue) ?? 0.0;
            }
          }

          // Extract token information if available
          final tokensProcessed = data['tokensProcessed'] as int?;
          final tokensLimit = data['tokensLimit'] as int?;
          final tokensExceeded = data['tokensExceeded'] as bool? ?? false;

          return FileProcessingStatus(
            id: id,
            status: status,
            progress: progress,
            error: data['error'] as String?,
            message: data['message'] as String?,
            tokensProcessed: tokensProcessed,
            tokensLimit: tokensLimit,
            tokensExceeded: tokensExceeded,
          );
        } catch (e, stack) {
          appLog.error('Error parsing status update', name: _logName, error: e, stackTrace: stack);

          // Return a fallback status on parsing error
          return FileProcessingStatus(
            id: id,
            status: 'error',
            progress: 0.0,
            error: 'Error parsing status update: ${e.toString()}',
            message: 'Client-side error',
          );
        }
      });
    } catch (e, stack) {
      appLog.error('Error subscribing to status updates',
          name: _logName, error: e, stackTrace: stack);

      // Yield a single error status and complete the stream
      yield FileProcessingStatus(
        id: id,
        status: 'error',
        progress: 0.0,
        error: 'Error subscribing to status updates: ${e.toString()}',
        message: 'WebSocket connection failed',
      );
    }
  }

  /// Subscribe to real-time status updates via gRPC Stream
  Stream<UploadUpdate> streamUploadUpdatesProto(String jobId) {
    appLog.debug('Subscribing to gRPC stream for job ID: $jobId', name: _logName);
    try {
      final request = UploadStatusRequest()..jobId = jobId;
      // Return the stream directly
      return _grpcClient.streamUploadUpdates(request);
    } catch (e, stack) {
      appLog.error('Error initiating gRPC stream subscription for job $jobId',
          name: _logName, error: e, stackTrace: stack);
      // Return an error stream if initiation fails
      return Stream.error(Exception('Failed to subscribe to gRPC status updates: ${e.toString()}'));
    }
  }

  /// Cancel processing
  Future<bool> cancelProcessing(String id) async {
    try {
      appLog.debug('Cancelling processing for ID: $id', name: _logName);

      final endpoint = '/processing/job/$id';

      // Use the ApiClient for authenticated requests
      final response = await _apiClient.delete(endpoint, isProtected: true);

      return response.statusCode == 200;
    } catch (e, stack) {
      appLog.error('Error cancelling processing', name: _logName, error: e, stackTrace: stack);
      return false;
    }
  }

  /// Dispose resources
  void dispose() {
    _httpClient.close();
  }
}

/// Response from file metadata check
class FileMetadataCheckResponse {
  final bool canUpload;
  final int maxSize;
  final int maxTokens;
  final String message;
  final int estimatedTimeSeconds;
  final String licenseTier;

  FileMetadataCheckResponse({
    required this.canUpload,
    required this.maxSize,
    required this.maxTokens,
    required this.message,
    required this.estimatedTimeSeconds,
    required this.licenseTier,
  });
}
