import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:fixnum/fixnum.dart';
import 'package:grpc/grpc.dart';
import 'package:path/path.dart' as path;
import 'package:promz/core/services/api/api_client.dart';
import 'package:promz/generated/content_upload.pbgrpc.dart';
import 'package:promz/generated/file_metadata.pbgrpc.dart';
import 'package:promz_common/promz_common.dart';

/// Exception thrown when a file exceeds the size limit for a license tier
class FileSizeLimitExceededException implements Exception {
  final int fileSize;
  final int maxSize;
  final String currentTier;
  final String? nextTier;
  final String message;

  FileSizeLimitExceededException({
    required this.fileSize,
    required this.maxSize,
    required this.currentTier,
    this.nextTier,
    required this.message,
  });

  @override
  String toString() => message;

  /// Returns file size in MB for display
  String get fileSizeMB => (fileSize / (1024 * 1024)).toStringAsFixed(2);

  /// Returns max allowed size in MB for display
  String get maxSizeMB => (maxSize / (1024 * 1024)).toStringAsFixed(2);
}

/// Service for uploading files to the server for processing
class FileUploadService {
  static const _logName = 'FileUploadService';
  final ApiClient _apiClient;
  final ContentUploadServiceClient _grpcClient;

  // License tier file size limits in bytes (matching server config)
  static const Map<String, int> _tierFileSizeLimits = {
    'free': 10 * 1024 * 1024, // 10MB
    'pro': 50 * 1024 * 1024, // 50MB
    'enterprise': 200 * 1024 * 1024, // 200MB
  };

  FileUploadService({
    required ApiClient apiClient,
    required ContentUploadServiceClient grpcClient,
  })  : _apiClient = apiClient,
        _grpcClient = grpcClient;

  /// Check if file size is within the allowed limit for the specified license tier
  /// Returns true if within limit, false if exceeding limit
  bool isFileSizeWithinLimit(int fileSize, String licenseTier) {
    final maxSize =
        _tierFileSizeLimits[licenseTier.toLowerCase()] ?? _tierFileSizeLimits['free'] ?? 0;
    return fileSize <= maxSize;
  }

  /// Get the next tier that would support the file size
  String? getNextTierForFileSize(int fileSize) {
    if (fileSize <= _tierFileSizeLimits['free']!) {
      return null; // Free tier is sufficient
    } else if (fileSize <= _tierFileSizeLimits['pro']!) {
      return 'Pro';
    } else if (fileSize <= _tierFileSizeLimits['enterprise']!) {
      return 'Enterprise';
    } else {
      return null; // No tier supports this size
    }
  }

  /// Get the maximum file size allowed for a license tier
  int getMaxFileSizeForTier(String licenseTier) {
    // Use null-aware operator with non-nullable default to ensure int return type
    return _tierFileSizeLimits[licenseTier.toLowerCase()] ??
        _tierFileSizeLimits['free']!; // Force non-null with ! since 'free' always exists
  }

  /// Validate file size with the server before upload
  /// This uses the ValidateFileSize RPC endpoint to check file size against the user's license tier
  /// If the server is not available, it falls back to client-side validation
  Future<void> validateFileSize({
    required String fileName,
    required int fileSize,
    required String licenseTier,
  }) async {
    try {
      appLog.debug('Validating file size: $fileName, size: $fileSize bytes, tier: $licenseTier',
          name: _logName);

      try {
        // Call the server-side validation endpoint
        final client = await _getFileProcessingClient();

        // Create the validation request
        final request = FileSizeValidationRequest(
          fileName: fileName,
          fileSizeBytes: Int64(fileSize),
        );

        // Get authentication headers
        final authMetadata = await _apiClient.getAuthHeaders(isProtected: true);
        final options = CallOptions(metadata: authMetadata);

        // Make the RPC call
        final response = await client.validateFileSize(request, options: options);

        // Check if validation passed
        if (!response.isValid) {
          // Create a FileSizeLimitExceededException with details from the response
          final errorMessage = response.errorMessage;
          final maxSize = response.maxSizeBytes.toInt();
          final nextTier = response.nextTier.isEmpty ? null : response.nextTier;

          appLog.warning(errorMessage, name: _logName);
          throw FileSizeLimitExceededException(
            fileSize: fileSize,
            maxSize: maxSize,
            currentTier: licenseTier.toLowerCase(),
            nextTier: nextTier,
            message: errorMessage,
          );
        }

        appLog.debug('Server validation passed for file size', name: _logName);
        return;
      } catch (serverError) {
        // If server validation fails (e.g., server unavailable), fall back to client-side validation
        appLog.warning('Server validation failed, falling back to client-side validation',
            name: _logName, error: serverError);

        // Client-side fallback validation
        final tier = licenseTier.toLowerCase();
        if (!isFileSizeWithinLimit(fileSize, tier)) {
          final maxSize = getMaxFileSizeForTier(tier);
          final nextTier = getNextTierForFileSize(fileSize);
          final fileSizeMB = fileSize / (1024 * 1024);

          final tierDisplay = tier.substring(0, 1).toUpperCase() + tier.substring(1);
          String errorMessage;

          if (nextTier != null) {
            errorMessage =
                'File size (${fileSizeMB.toStringAsFixed(2)} MB) exceeds the $tierDisplay tier limit of ${(maxSize / (1024 * 1024)).toStringAsFixed(2)} MB. Please upgrade to $nextTier tier.';
          } else {
            errorMessage =
                'File size (${fileSizeMB.toStringAsFixed(2)} MB) exceeds the $tierDisplay tier limit of ${(maxSize / (1024 * 1024)).toStringAsFixed(2)} MB.';
          }

          appLog.warning(errorMessage, name: _logName);
          throw FileSizeLimitExceededException(
            fileSize: fileSize,
            maxSize: maxSize,
            currentTier: tier,
            nextTier: nextTier,
            message: errorMessage,
          );
        }
      }

      appLog.debug('File size validation passed', name: _logName);
    } catch (e) {
      appLog.error('File size validation failed', name: _logName, error: e);
      rethrow;
    }
  }

  /// Get a FileProcessingServiceClient for making gRPC calls
  Future<FileProcessingServiceClient> _getFileProcessingClient() async {
    // Get the gRPC channel from ApiClient
    final channel = await _apiClient.getGrpcChannel();
    return FileProcessingServiceClient(channel);
  }

  /// Upload file to server for processing using gRPC
  ///
  /// This method includes:
  /// - Calls the server's gRPC `processFile` method
  /// - Detailed logging for debugging upload issues
  /// Note: File size validation should be done separately using validateFileSize before calling this method
  Future<String> uploadFile(File file,
      {Map<String, dynamic>? metadata, String? licenseTier}) async {
    try {
      final fileSize = await file.length();
      final fileSizeMB = fileSize / (1024 * 1024);
      final fileName = path.basename(file.path);

      // Default to free tier if not specified
      final tier = (licenseTier ?? 'free').toLowerCase();

      appLog.info('Uploading file via gRPC: $fileName (${fileSizeMB.toStringAsFixed(2)} MB)',
          name: _logName);

      // Read file content
      final fileBytes = await file.readAsBytes();

      // Construct the gRPC request message without metadata (metadata is sent in CallOptions)
      final request = UploadFileRequest(
        fileName: fileName,
        fileContent: fileBytes,
      );

      // Prepare metadata map for gRPC request (customMetadata) - this is sent in CallOptions below
      // Convert dynamic values to strings as protobuf map expects <string, string>
      final Map<String, String> requestMetadata = {};
      if (metadata != null) {
        metadata.forEach((key, value) {
          requestMetadata[key] = value.toString();
        });
      }

      // Get API key for authentication (from LicenseManagerService)
      final apiKey = _apiClient.apiKey;
      if (apiKey == null || apiKey.isEmpty) {
        throw Exception('API key not available for authentication');
      }

      // Create metadata map for CallOptions (API key and other relevant info)
      final Map<String, String> authMetadata = {
        'x-api-key': apiKey,
      };

      // Add other relevant info to metadata
      requestMetadata['licenseTier'] = tier;
      requestMetadata['fileSize'] = fileSize.toString();

      // Add metadata to CallOptions
      authMetadata.addAll(requestMetadata);

      appLog.debug('Sending UploadFileRequest to gRPC server for $fileName', name: _logName);

      try {
        // Make the gRPC call with the API key in metadata
        final response = await _grpcClient.uploadFile(
          request,
          options: CallOptions(
            metadata: authMetadata,
            timeout: const Duration(seconds: 30),
          ),
        );

        appLog.info('gRPC upload successful for $fileName, Job ID: ${response.jobId}',
            name: _logName);

        // Return the Job ID received from the server
        return response.jobId;
      } on GrpcError catch (e, stack) {
        if (e.code == StatusCode.unavailable) {
          appLog.error('gRPC server unavailable. Check server address and port configuration: $e',
              name: _logName, error: e, stackTrace: stack);
          throw Exception(
              'Cannot connect to file processing server. Please check your network connection or try again later.');
        }

        appLog.error('gRPC error during file upload: ${e.message}',
            name: _logName, error: e, stackTrace: stack);
        throw Exception('Failed to upload file via gRPC: ${e.message}');
      }
    } on FileSizeLimitExceededException {
      // Re-throw the specific exception for UI handling
      rethrow;
    } catch (e, stack) {
      appLog.error('Unexpected error during file upload: $e',
          name: _logName, error: e, stackTrace: stack);
      throw Exception('An unexpected error occurred during file upload: $e');
    }
  }

  /// Get processing results
  Future<FileProcessingResult> getResults(String id) async {
    try {
      appLog.debug('Getting processing results for ID: $id', name: _logName);

      final endpoint = '/processing/job/$id/result';

      // Use the ApiClient for authenticated requests
      final response = await _apiClient.get(endpoint, isProtected: true);

      if (response.statusCode != 200) {
        throw Exception('Failed to get results: ${response.statusCode}');
      }

      final Map<String, dynamic> responseData = jsonDecode(response.body);

      // Extract metadata and check for token information
      final metadata = responseData['metadata'] as Map<String, dynamic>? ?? {};

      // Check for token information in metadata
      int? tokensProcessed;
      int? tokensLimit;
      bool tokensExceeded = false;

      // First check in the metadata
      if (metadata.containsKey('tokensProcessed')) {
        tokensProcessed = metadata['tokensProcessed'] as int?;
      }

      if (metadata.containsKey('tokensLimit')) {
        tokensLimit = metadata['tokensLimit'] as int?;
      }

      if (metadata.containsKey('tokensExceeded')) {
        tokensExceeded = metadata['tokensExceeded'] as bool? ?? false;
      }

      // Then check in the main response (higher priority)
      if (responseData.containsKey('tokensProcessed')) {
        tokensProcessed = responseData['tokensProcessed'] as int?;
      }

      if (responseData.containsKey('tokensLimit')) {
        tokensLimit = responseData['tokensLimit'] as int?;
      }

      if (responseData.containsKey('tokensExceeded')) {
        tokensExceeded = responseData['tokensExceeded'] as bool? ?? false;
      }

      // Check for partial processing flag
      final partialProcessing = metadata['partialProcessing'] as bool? ?? false;

      // Log token usage if available
      if (tokensProcessed != null && tokensLimit != null) {
        final processedK = (tokensProcessed / 1000).round();
        final limitK = (tokensLimit / 1000).round();
        appLog.debug(
          'Result token usage: $processedK K / $limitK K ${tokensExceeded ? "(limit exceeded)" : ""}',
          name: _logName,
        );
      }

      // Ensure contentType is available, defaulting to 'unknown' if missing
      final contentType = responseData['contentType'] as String? ?? 'unknown';

      // Check for hasFullContent flag
      final hasFullContent =
          (responseData['hasFullContent'] as bool? ?? true) && !partialProcessing;

      // Get contentUrl - this is now the primary way to access content
      final contentUrl = responseData['contentUrl'] as String?;

      // Log if contentUrl is missing
      if (contentUrl == null || contentUrl.isEmpty) {
        appLog.warning(
          'Content URL is missing for processing result ID: $id',
          name: _logName,
        );
      }

      return FileProcessingResult(
        id: id,
        contentType: contentType,
        metadata: metadata,
        hasFullContent: hasFullContent,
        contentUrl: contentUrl,
        expiresAt: responseData['expiresAt'] != null
            ? DateTime.fromMillisecondsSinceEpoch(responseData['expiresAt'] as int)
            : null,
        tokensProcessed: tokensProcessed,
        tokensLimit: tokensLimit,
        tokensExceeded: tokensExceeded || partialProcessing,
      );
    } catch (e, stack) {
      appLog.error('Error getting processing results', name: _logName, error: e, stackTrace: stack);
      rethrow;
    }
  }

  /// Cancel processing
  Future<bool> cancelProcessing(String id) async {
    try {
      appLog.debug('Cancelling processing for ID: $id', name: _logName);

      final endpoint = '/processing/job/$id';

      // Use the ApiClient for authenticated requests
      final response = await _apiClient.delete(endpoint, isProtected: true);

      return response.statusCode == 200;
    } catch (e, stack) {
      appLog.error('Error cancelling processing', name: _logName, error: e, stackTrace: stack);
      return false;
    }
  }

  void dispose() {
    // _httpClient.close(); // Removed since httpClient is no longer used
  }
}

/// Response from file upload request
class FileUploadResponse {
  final String id;
  final String status;
  final int estimatedTimeSeconds;
  final int? maxTokens;
  final int? fileSize;
  final String? licenseTier;

  FileUploadResponse({
    required this.id,
    required this.status,
    required this.estimatedTimeSeconds,
    this.maxTokens,
    this.fileSize,
    this.licenseTier,
  });

  /// Returns a human-readable description of token limits
  String? get tokenLimitDescription {
    if (maxTokens == null) return null;

    final maxTokensK = (maxTokens! / 1000).round();
    return 'Processing limited to $maxTokensK K tokens for $licenseTier tier';
  }
}

/// Status of file processing
class FileProcessingStatus {
  final String id;
  final String status;
  final double progress;
  final String? error;
  final String? message;
  final int? tokensProcessed;
  final int? tokensLimit;
  final bool tokensExceeded;

  FileProcessingStatus({
    required this.id,
    required this.status,
    required this.progress,
    this.error,
    this.message,
    this.tokensProcessed,
    this.tokensLimit,
    this.tokensExceeded = false,
  });

  bool get isCompleted => status == 'completed';
  bool get isFailed => status == 'failed';
  bool get isProcessing => status == 'processing' || status == 'queued';

  /// Returns true if the processing was limited by token count
  bool get wasLimitedByTokens => tokensExceeded;

  /// Returns a human-readable description of token usage
  String? get tokenUsageDescription {
    if (tokensProcessed == null || tokensLimit == null) return null;

    final processedK = (tokensProcessed! / 1000).round();
    final limitK = (tokensLimit! / 1000).round();

    return tokensExceeded
        ? 'Processed $processedK K tokens (limited to $limitK K)'
        : 'Processed $processedK K tokens of $limitK K limit';
  }
}

/// Result of file processing
class FileProcessingResult {
  final String id;
  final String contentType;
  final Map<String, dynamic> metadata;
  final bool hasFullContent;
  final String? contentUrl;
  final DateTime? expiresAt;
  final int? tokensProcessed;
  final int? tokensLimit;
  final bool tokensExceeded;

  FileProcessingResult({
    required this.id,
    required this.contentType,
    required this.metadata,
    this.hasFullContent = true,
    this.contentUrl,
    this.expiresAt,
    this.tokensProcessed,
    this.tokensLimit,
    this.tokensExceeded = false,
  });

  /// Returns true if the processing was limited by token count
  bool get wasLimitedByTokens => tokensExceeded;

  /// Returns a human-readable description of token usage
  String? get tokenUsageDescription {
    if (tokensProcessed == null || tokensLimit == null) return null;

    final processedK = (tokensProcessed! / 1000).round();
    final limitK = (tokensLimit! / 1000).round();

    return tokensExceeded
        ? 'Processed $processedK K tokens (limited to $limitK K)'
        : 'Processed $processedK K tokens of $limitK K limit';
  }

  /// Returns true if token information is available
  bool get hasTokenInfo => tokensProcessed != null && tokensLimit != null;

  /// Returns true if content is available via URL
  bool get hasContentUrl => contentUrl != null && contentUrl!.isNotEmpty;
}
