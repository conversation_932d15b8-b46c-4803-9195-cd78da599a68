import 'dart:developer' as dev;
import 'dart:io';
import 'package:syncfusion_flutter_pdf/pdf.dart';
import 'package:promz/core/services/file/base/file_type_service.dart';

class PDFService implements FileTypeService {
  @override
  bool canHandle(String path, [String? mimeType]) {
    final pathLower = path.toLowerCase();
    final mimeTypeLower = (mimeType ?? '').toLowerCase();

    return pathLower.endsWith('.pdf') || mimeTypeLower == 'application/pdf';
  }

  /// Extract content from PDF bytes directly
  Future<String?> extractContentFromBytes(List<int> bytes, {String? sourcePath}) async {
    try {
      final document = PdfDocument(inputBytes: bytes);
      try {
        final extractor = PdfTextExtractor(document);
        final text = extractor.extractText();
        dev.log('Successfully extracted text from PDF bytes', name: 'PDFService');
        return text;
      } catch (e) {
        dev.log('Error extracting text from PDF bytes', error: e);
        return null;
      } finally {
        document.dispose();
      }
    } catch (e) {
      dev.log('Error loading PDF document from bytes', error: e);
      return null;
    }
  }

  @override
  Future<String?> extractContent(String path) async {
    try {
      final file = File(path);
      if (!await file.exists()) {
        dev.log('PDF file does not exist: $path', name: 'PDFService');
        return null;
      }

      final bytes = await file.readAsBytes();
      return extractContentFromBytes(bytes, sourcePath: path);
    } catch (e) {
      dev.log('Error extracting PDF content', error: e);
      return null;
    }
  }

  @override
  Future<Map<String, dynamic>> extractMetadata(String path) async {
    try {
      final file = File(path);
      final bytes = await file.readAsBytes();
      final stat = await file.stat();

      Map<String, dynamic> metadata = {
        'size': stat.size,
        'lastModified': stat.modified.toIso8601String(),
        'lastAccessed': stat.accessed.toIso8601String(),
        'isEncrypted': false,
        'hasUserPassword': false,
        'hasOwnerPassword': false,
        'encryptionAlgorithm': null,
      };

      try {
        // Try to load without password first
        final PdfDocument doc = PdfDocument(inputBytes: bytes);
        metadata['pageCount'] = doc.pages.count;
        metadata['author'] = doc.documentInformation.author;
        metadata['title'] = doc.documentInformation.title;
        metadata['subject'] = doc.documentInformation.subject;
        metadata['keywords'] = doc.documentInformation.keywords;
        doc.dispose();
      } catch (e) {
        if (e.toString().toLowerCase().contains('password')) {
          // If we get a password error, the document is encrypted
          metadata['isEncrypted'] = true;
          metadata['hasUserPassword'] = true;
          // We can't determine the exact algorithm without decrypting
          metadata['encryptionAlgorithm'] = 'unknown';
        } else {
          rethrow;
        }
      }

      return metadata;
    } catch (e) {
      dev.log('Error extracting PDF metadata', error: e);
      return {};
    }
  }

  @override
  String getMimeType(String path) => 'application/pdf';

  @override
  String getTypeName() => 'PDF Document';
}
