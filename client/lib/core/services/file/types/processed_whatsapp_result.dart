/// Helper class to return both content and file info from WhatsApp processing
class ProcessedWhatsAppResult {
  /// The extracted text content from the WhatsApp chat
  final String? content;

  /// The filename of the chat file
  final String? fileName;

  /// Metadata extracted from the chat content
  final Map<String, dynamic>? metadata;

  /// Creates a new instance with the extracted content, filename, and optional metadata
  ProcessedWhatsAppResult(this.content, this.fileName, [this.metadata]);
}
