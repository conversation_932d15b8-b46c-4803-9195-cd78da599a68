import 'dart:io';
import 'dart:convert';
import 'dart:developer' as dev;
import 'package:promz/core/services/file/base/file_type_service.dart';

class TextFileService implements FileTypeService {
  @override
  bool canHandle(String path, [String? mimeType]) {
    final pathLower = path.toLowerCase();
    final mimeTypeLower = (mimeType ?? '').toLowerCase();

    return pathLower.endsWith('.txt') ||
        pathLower.endsWith('.json') ||
        pathLower.endsWith('.xml') ||
        pathLower.endsWith('.csv') ||
        mimeTypeLower.startsWith('text/') ||
        mimeTypeLower == 'application/json' ||
        mimeTypeLower == 'application/xml';
  }

  @override
  Future<String?> extractContent(String path) async {
    try {
      final file = File(path);
      // Try UTF-8 first
      try {
        return await file.readAsString(encoding: utf8);
      } catch (e) {
        dev.log('Failed to decode with UTF-8, trying latin1', error: e);
        // Fallback to latin1
        return await file.readAsString(encoding: latin1);
      }
    } catch (e) {
      dev.log('Error reading text file', error: e);
      return null;
    }
  }

  @override
  Future<Map<String, dynamic>> extractMetadata(String path) async {
    try {
      final file = File(path);
      if (!await file.exists()) {
        return {};
      }

      final stat = await file.stat();
      return {
        'size': stat.size,
        'modified': stat.modified.toIso8601String(),
        'accessed': stat.accessed.toIso8601String(),
        'mode': stat.mode,
        'encoding': await _detectEncoding(file),
        'lineCount': await _countLines(file),
      };
    } catch (e) {
      dev.log('Error extracting text file metadata', error: e);
      return {};
    }
  }

  @override
  String getMimeType(String path) {
    final ext = path.toLowerCase();
    if (ext.endsWith('.json')) return 'application/json';
    if (ext.endsWith('.xml')) return 'application/xml';
    if (ext.endsWith('.csv')) return 'text/csv';
    return 'text/plain';
  }

  @override
  String getTypeName() => 'Text File';

  Future<String> _detectEncoding(File file) async {
    try {
      final bytes = await file.readAsBytes();
      if (bytes.length >= 3 && bytes[0] == 0xEF && bytes[1] == 0xBB && bytes[2] == 0xBF) {
        return 'UTF-8 with BOM';
      }

      try {
        utf8.decode(bytes);
        return 'UTF-8';
      } catch (_) {
        return 'latin1';
      }
    } catch (e) {
      return 'unknown';
    }
  }

  Future<int> _countLines(File file) async {
    try {
      final contents = await file.readAsString();
      return '\n'.allMatches(contents).length + 1;
    } catch (e) {
      return 0;
    }
  }
}
