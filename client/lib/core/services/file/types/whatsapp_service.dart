import 'dart:io';
import 'package:promz/core/services/file/base/enhanced_file_type_service.dart';
import 'package:promz/core/services/file/types/processed_whatsapp_result.dart';
import 'package:promz/generated/content_upload.pb.dart';
import 'package:promz/generated/whatsapp_metadata.pb.dart';
import 'package:promz_common/promz_common.dart';

/// A service for processing and analyzing WhatsApp chat content
///
/// This service is responsible for extracting metadata and content from WhatsApp chat exports.
/// It is used by the ZipService when WhatsApp content is detected in a zip archive.
class WhatsAppService implements EnhancedFileTypeService {
  static const _logName = 'WhatsAppService';

  @override
  bool canHandle(String path, [String? mimeType]) {
    final pathLower = path.toLowerCase();
    // Only handle direct WhatsApp text files, not zip files
    return pathLower.contains('whatsapp') && pathLower.endsWith('.txt');
  }

  @override
  Future<String?> extractContent(String path) async {
    try {
      // Read the file content
      final content = await File(path).readAsString();

      // Process the content and return it
      final result = await processContent(
        content: content,
        fileName: path.split('/').last.split('\\').last,
        originalPath: path,
      );

      return result.content;
    } catch (e) {
      appLog.error('Error extracting WhatsApp content', name: _logName, error: e);
      return null;
    }
  }

  /// Process WhatsApp chat content and extract metadata
  ///
  /// This is the main method for processing WhatsApp content, regardless of source.
  /// It extracts metadata, formats content, and returns a ProcessedWhatsAppResult.
  Future<ProcessedWhatsAppResult> processContent({
    required String content,
    required String fileName,
    String? originalPath,
  }) async {
    try {
      // Extract metadata from the content
      final metadata = _extractChatMetadata(content);

      // Try to extract group name from chat content
      final groupName = _extractGroupName(content);
      if (groupName != null) {
        metadata['groupName'] = groupName;

        // Add as CONVERSATION:GROUP_NAME variable
        metadata['variables'] = {
          'CONVERSATION:GROUP_NAME': groupName,
          'CONVERSATION:CONTENTS': content,
        };

        // Add other chat variables
        if (metadata.containsKey('participants')) {
          metadata['variables']['CONVERSATION:PARTICIPANTS'] = metadata['participants'];
          metadata['variables']['CONVERSATION:PARTICIPANT_COUNT'] = metadata['participantCount'];
        }

        if (metadata.containsKey('startDate')) {
          metadata['variables']['CONVERSATION:START_DATE'] = metadata['startDate'];
        }

        if (metadata.containsKey('endDate')) {
          metadata['variables']['CONVERSATION:END_DATE'] = metadata['endDate'];
        }

        if (metadata.containsKey('messageCount')) {
          metadata['variables']['CONVERSATION:MESSAGE_COUNT'] = metadata['messageCount'];
        }
      }

      return ProcessedWhatsAppResult(content, fileName, metadata);
    } catch (e) {
      appLog.error('Error processing WhatsApp content', name: _logName, error: e);
      return ProcessedWhatsAppResult(null, null);
    }
  }

  /// Enhance a ProcessingResult with WhatsApp metadata
  ///
  /// This method enriches a ProcessingResult with WhatsApp-specific metadata.
  /// It can be used when receiving processing results from the server or when
  /// creating a new result locally.
  ///
  /// @param result The ProcessingResult to enhance
  /// @param extractedMetadata Optional pre-extracted metadata
  /// @return True if metadata was enhanced, false otherwise
  @override
  bool enhanceProcessingResult(ProcessingResult result, [Map<String, dynamic>? extractedMetadata]) {
    try {
      if (result.contentType != 'conversation' && !_fileNameLooksLikeWhatsApp(result.fileName)) {
        // Not a WhatsApp chat
        return false;
      }

      appLog.debug('Enhancing WhatsApp ProcessingResult: ${result.fileName}', name: _logName);

      // If we already have WhatsApp metadata and it's complete, nothing to do
      if (result.hasWhatsappMetadata() &&
          result.whatsappMetadata.messageCount > 0 &&
          result.whatsappMetadata.participantCount > 0) {
        appLog.debug('WhatsApp metadata already complete, skipping enhancement', name: _logName);
        return true;
      }

      Map<String, dynamic> metadata = extractedMetadata ?? {};

      // If no metadata was provided and we have content, extract it
      if (metadata.isEmpty && result.content.isNotEmpty) {
        if (result.content.startsWith('URL:')) {
          // Content is a URL reference, can't extract metadata from it
          appLog.debug('Content is a URL reference, can\'t extract metadata', name: _logName);
        } else {
          // Extract metadata from content
          metadata = _extractChatMetadata(result.content);

          // Try to extract group name
          final groupName = _extractGroupName(result.content);
          if (groupName != null) {
            metadata['groupName'] = groupName;
          }
        }
      }

      // Create or update WhatsApp metadata
      WhatsAppMetadata whatsappMetadata;
      if (result.hasWhatsappMetadata()) {
        // Use existing metadata as base
        whatsappMetadata = result.whatsappMetadata;
      } else {
        // Create new metadata
        whatsappMetadata = WhatsAppMetadata();
        result.whatsappMetadata = whatsappMetadata;
      }

      // Set content type if not already set
      if (result.contentType.isEmpty) {
        result.contentType = 'conversation';
      }

      // Set groupName if available
      if (metadata.containsKey('groupName') &&
          (whatsappMetadata.groupName.isEmpty || whatsappMetadata.groupName == 'ZIP File')) {
        whatsappMetadata.groupName = metadata['groupName'] as String;

        // Also update the result title
        if (result.title.isEmpty || result.title == 'ZIP File') {
          result.title = whatsappMetadata.groupName;
        }
      }

      // Set chatName if groupName is not available
      if (whatsappMetadata.groupName.isEmpty && whatsappMetadata.chatName.isEmpty) {
        final fileName = result.fileName.split('/').last.split('\\').last.split('.').first;
        whatsappMetadata.chatName = fileName;

        // Also update the result title if needed
        if (result.title.isEmpty || result.title == 'ZIP File') {
          result.title = fileName;
        }
      }

      // Set participants if available
      if (metadata.containsKey('participants') && whatsappMetadata.participants.isEmpty) {
        final participants = metadata['participants'] as List<dynamic>;
        for (var participant in participants) {
          whatsappMetadata.participants.add(participant.toString());
        }
      }

      // Set participantCount if available
      if (metadata.containsKey('participantCount') && whatsappMetadata.participantCount == 0) {
        whatsappMetadata.participantCount = metadata['participantCount'] as int;
      } else if (whatsappMetadata.participants.isNotEmpty &&
          whatsappMetadata.participantCount == 0) {
        // Calculate from participants list
        whatsappMetadata.participantCount = whatsappMetadata.participants.length;
      }

      // Set messageCount if available
      if (metadata.containsKey('messageCount') && whatsappMetadata.messageCount == 0) {
        whatsappMetadata.messageCount = metadata['messageCount'] as int;
      }

      // Set isGroupChat based on participant count
      whatsappMetadata.isGroupChat = whatsappMetadata.participantCount > 1;

      return true;
    } catch (e, stack) {
      appLog.error('Error enhancing WhatsApp metadata',
          name: _logName, error: e, stackTrace: stack);
      return false;
    }
  }

  /// Determine if a filename is likely to be a WhatsApp chat file
  bool _fileNameLooksLikeWhatsApp(String fileName) {
    final lowerName = fileName.toLowerCase();
    return lowerName.contains('whatsapp') ||
        (lowerName.contains('chat') && lowerName.endsWith('.txt'));
  }

  @override
  Future<Map<String, dynamic>> extractMetadata(String path) async {
    try {
      // Read the file content
      final content = await File(path).readAsString();

      // Extract metadata from the content
      return _extractChatMetadata(content);
    } catch (e) {
      appLog.error('Error extracting WhatsApp metadata', name: _logName, error: e);
      return {};
    }
  }

  @override
  String getMimeType(String path) => 'text/plain';

  @override
  String getTypeName() => 'WhatsApp Chat';

  @override
  bool canEnhanceMetadata(ProcessingResult result) {
    // Check if this is a WhatsApp result
    return result.contentType == 'conversation' || _fileNameLooksLikeWhatsApp(result.fileName);
  }

  /// Extract group name from chat content
  String? _extractGroupName(String content) {
    // Try to find group name in first few lines
    final lines = content.split('\n').take(10).toList();

    // Common patterns in WhatsApp exports
    final groupNamePatterns = [
      RegExp(r'Group: "(.*?)"'),
      RegExp(r'Chat: "(.*?)"'),
      RegExp(r'WhatsApp Chat - (.*?)$', multiLine: true),
      RegExp(r'\[.*?\] .* created group "(.*?)"'),
    ];

    for (final line in lines) {
      for (final pattern in groupNamePatterns) {
        final match = pattern.firstMatch(line);
        if (match != null && match.groupCount >= 1) {
          final name = match.group(1)?.trim();
          if (name != null && name.isNotEmpty) {
            return name;
          }
        }
      }
    }

    return null;
  }

  Map<String, dynamic> _extractChatMetadata(String content) {
    final lines = content.split('\n');
    final participants = <String>{};
    DateTime? startDate;
    DateTime? endDate;
    int messageCount = 0;
    final mediaCount = RegExp(r'<Media omitted>').allMatches(content).length;

    // WhatsApp date format regex (covers most common formats)
    final dateRegex =
        RegExp(r'(\d{1,2}[./]\d{1,2}[./]\d{2,4}),? (\d{1,2}:\d{2}(?::\d{2})?) ?(?:[AP]M)? - ');
    final participantRegex = RegExp(r' - (.*?): ');

    for (final line in lines) {
      final dateMatch = dateRegex.firstMatch(line);
      if (dateMatch != null) {
        messageCount++;
        final dateStr = dateMatch.group(1);
        final timeStr = dateMatch.group(2);
        DateTime? date;
        if (dateStr != null && timeStr != null) {
          // Try different date formats
          date = _parseDateTime(dateStr, timeStr);
        }
        if (date != null) {
          startDate ??= date;
          endDate = date;
        }
      }

      final participantMatch = participantRegex.firstMatch(line);
      if (participantMatch != null && participantMatch.groupCount >= 1) {
        final participant = participantMatch.group(1);
        if (participant != null) {
          participants.add(participant);
        }
      }
    }

    return {
      'participants': participants.toList(),
      'participantCount': participants.length,
      'startDate': startDate?.toIso8601String(),
      'endDate': endDate?.toIso8601String(),
      'messageCount': messageCount,
      'mediaCount': mediaCount,
      'duration':
          endDate != null && startDate != null ? endDate.difference(startDate).inDays : null,
    };
  }

  DateTime? _parseDateTime(String dateStr, String timeStr) {
    // Try different date formats
    final formats = [
      (date) => DateTime.tryParse('$date $timeStr'),
      (date) => DateTime.tryParse(date.replaceAll('.', '/') + ' $timeStr'),
      (date) {
        final parts = date.split(RegExp(r'[./]'));
        if (parts.length == 3) {
          // Try both DD/MM/YY and MM/DD/YY formats
          final year = parts[2].length == 2 ? '20${parts[2]}' : parts[2];
          return DateTime.tryParse('${parts[0]}-${parts[1]}-$year $timeStr') ??
              DateTime.tryParse('${parts[1]}-${parts[0]}-$year $timeStr');
        }
        return null;
      },
    ];

    for (final format in formats) {
      final date = format(dateStr);
      if (date != null) return date;
    }
    return null;
  }
}
