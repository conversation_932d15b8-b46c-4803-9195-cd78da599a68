import 'dart:async';
import 'package:meta/meta.dart';

/// Base interface for handling specific file types (PDF, Text, etc.)
abstract class FileTypeService {
  /// Check if this service can handle the given file
  bool canHandle(String path, [String? mimeType]);

  /// Extract text content from the file
  Future<String?> extractContent(String path);

  /// Extract metadata specific to this file type
  Future<Map<String, dynamic>> extractMetadata(String path);

  /// Get the MIME type for this file type
  @mustCallSuper
  String getMimeType(String path) => 'application/octet-stream';

  /// Get a user-friendly name for this file type
  @mustCallSuper
  String getTypeName() => 'Unknown';
}
