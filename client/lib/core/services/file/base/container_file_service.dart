import 'dart:async';
import 'package:meta/meta.dart';
import 'package:promz/core/models/file/processed_file.dart';

/// Base interface for handling container formats (ZIP, RAR, etc.)
abstract class ContainerFileService {
  /// Check if this service can handle the given container file
  bool canHandle(String path, [String? mimeType]);

  /// Extract all files from the container
  Future<List<ProcessedFile>> extractFiles(String path);

  /// Extract metadata about the container itself
  Future<Map<String, dynamic>> extractMetadata(String path);

  /// Get the MIME type for this container type
  @mustCallSuper
  String getMimeType(String path) => 'application/octet-stream';

  /// Get a user-friendly name for this container type
  @mustCallSuper
  String getTypeName() => 'Unknown Container';
}
