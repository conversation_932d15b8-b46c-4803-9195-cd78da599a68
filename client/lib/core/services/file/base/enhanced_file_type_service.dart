import 'package:promz/core/services/file/base/file_type_service.dart';
import 'package:promz/generated/content_upload.pb.dart';

/// Extended interface for file type services that can enhance metadata
///
/// This interface adds methods for enhancing metadata in ProcessingResults,
/// allowing services to add type-specific metadata to processing results
/// without requiring service-specific logic in the attachment registry.
abstract class EnhancedFileTypeService extends FileTypeService {
  /// Check if this service can enhance the given processing result
  ///
  /// This method should return true if the service can enhance the metadata
  /// for the given content type, false otherwise.
  ///
  /// @param result The ProcessingResult to check
  /// @return True if this service can enhance the result's metadata
  bool canEnhanceMetadata(ProcessingResult result);

  /// Enhance a ProcessingResult with type-specific metadata
  ///
  /// This method should enrich the provided ProcessingResult with additional
  /// metadata specific to this file type, such as WhatsApp chat metadata,
  /// YouTube video details, etc.
  ///
  /// @param result The ProcessingResult to enhance
  /// @param extractedMetadata Optional pre-extracted metadata
  /// @return True if metadata was enhanced, false otherwise
  bool enhanceProcessingResult(ProcessingResult result, [Map<String, dynamic>? extractedMetadata]);
}
