import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

final connectivityServiceProvider = Provider<ConnectivityService>((ref) {
  return ConnectivityService();
});

final connectivityStatusProvider = StreamProvider<bool>((ref) {
  return ref.watch(connectivityServiceProvider).onConnectivityChanged;
});

class ConnectivityService {
  final Connectivity _connectivity = Connectivity();

  Future<bool> checkInternetConnection() async {
    final result = await _connectivity.checkConnectivity();
    final hasConnection = [
      ConnectivityResult.mobile,
      ConnectivityResult.wifi,
      ConnectivityResult.ethernet,
    ];

    // Fix: Use contains() instead of != operator
    return hasConnection.contains(result);
  }

  Stream<bool> get onConnectivityChanged => _connectivity.onConnectivityChanged.map(
        (result) {
          final hasConnection = [
            ConnectivityResult.mobile,
            ConnectivityResult.wifi,
            ConnectivityResult.ethernet,
          ];
          // Fix: Use contains() instead of != operator
          return hasConnection.contains(result);
        },
      );
}
