import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:grpc/grpc.dart';
import 'package:http/http.dart' as http;
import 'package:promz/core/services/license_manager_service.dart';
import 'package:promz/core/services/websocket_service.dart';
import 'package:promz_common/config/api_config.dart';
import 'package:promz_common/promz_common.dart';

/// This client leverages the existing ApiConfig for URL configuration
/// and integrates with SupabaseService for authentication.
class ApiClient {
  static const _logName = 'ApiClient';
  final http.Client _httpClient;
  final LicenseManagerService _licenseManager;

  /// WebSocket service for real-time communication
  WebSocketService? _webSocketService;

  /// Flag to track if WebSocket initialization has been attempted
  bool _webSocketInitialized = false;

  /// Flag to track if we're currently trying to initialize the WebSocket
  bool _initializingWebSocket = false;

  /// Queue of pending WebSocket requests that arrived before connection was established
  final List<_PendingWebSocketRequest> _pendingRequests = [];

  /// Stream controllers for different WebSocket topics
  final Map<String, StreamController<Map<String, dynamic>>> _topicControllers = {};

  /// Flag to enable lazy WebSocket initialization
  /// When true, WebSocket will only be initialized when actually needed
  final bool _lazyWebSocketInit;

  /// Flag to indicate if the app's UI has been rendered completely
  /// Will be set to true once the home page is fully rendered
  bool _isAppFullyRendered = false;

  /// Get the base URL from ApiConfig
  String get baseUrl => ApiConfig.baseUrl;

  ApiClient({
    required LicenseManagerService licenseManager,
    http.Client? httpClient,
    bool lazyWebSocketInit = true,
  })  : _licenseManager = licenseManager,
        _httpClient = httpClient ?? http.Client(),
        _lazyWebSocketInit = lazyWebSocketInit {
    // Listen for license changes to reinitialize WebSocket if needed
    _listenForLicenseChanges();
  }

  /// Set the flag indicating that app's UI is fully rendered and
  /// we can safely initialize resource-intensive connections
  void setAppFullyRendered() {
    _isAppFullyRendered = true;
    // Process any pending requests if we have them and WebSocket is ready
    if (_webSocketService != null &&
        _webSocketService!.isConnected &&
        _pendingRequests.isNotEmpty) {
      _processPendingRequests();
    }
  }

  /// Check if the WebSocket is currently connected
  bool isWebSocketConnected() {
    return _webSocketService != null && _webSocketService!.isConnected;
  }

  /// Process any pending WebSocket requests that were queued before connection was ready
  void _processPendingRequests() {
    if (_pendingRequests.isEmpty) return;

    appLog.info('Processing ${_pendingRequests.length} pending WebSocket requests', name: _logName);

    // Create a local copy to avoid concurrent modification issues
    final requestsToProcess = List<_PendingWebSocketRequest>.from(_pendingRequests);
    _pendingRequests.clear();

    // Process each pending request
    for (final request in requestsToProcess) {
      if (request.isSubscription) {
        // Handle subscription requests
        appLog.debug('Processing pending subscription for topic: ${request.topic}', name: _logName);
        final controller = request.controller as StreamController<Map<String, dynamic>>;

        try {
          if (_webSocketService != null) {
            final subscription = _webSocketService!
                .subscribe<Map<String, dynamic>>(request.topic, (data) => data)
                .listen((data) {
              if (!controller.isClosed) {
                controller.add(data);
              }
            });

            // Handle controller close
            controller.onCancel = () {
              subscription.cancel();
              _webSocketService?.unsubscribe(request.topic);
              _topicControllers.remove(request.topic);
            };
          }
        } catch (e, stack) {
          appLog.error('Error processing pending subscription',
              name: _logName, error: e, stackTrace: stack);
          if (!controller.isClosed) {
            controller.addError(e, stack);
          }
        }
      } else {
        // Handle send message requests
        appLog.debug('Processing pending message for topic: ${request.topic}', name: _logName);
        try {
          if (_webSocketService != null) {
            _webSocketService!.sendCustomMessage(request.message!);
          }
        } catch (e, stack) {
          appLog.error('Error processing pending message',
              name: _logName, error: e, stackTrace: stack);
        }
      }
    }
  }

  /// Listen for license changes to reconnect WebSocket when API key becomes available
  void _listenForLicenseChanges() {
    _licenseManager.addListener(() {
      if (_licenseManager.apiKey != null &&
          _licenseManager.apiKey!.isNotEmpty &&
          (_webSocketService == null || !_webSocketService!.isConnected)) {
        // Only log and attempt initialization if we don't already have a connected WebSocket
        appLog.debug('API key available, checking WebSocket connection status', name: _logName);

        // Only reinitialize immediately if not using lazy initialization or if the app is fully rendered
        if (!_lazyWebSocketInit || _isAppFullyRendered) {
          reinitializeWebSocketIfNeeded();
        }
      }
    });
  }

  /// Get the current API key from license manager
  Future<String?> getApiKey() async {
    try {
      final apiKey = _licenseManager.apiKey;
      if (apiKey != null && apiKey.isNotEmpty) {
        appLog.debug('Using API key from license manager', name: _logName);
        return apiKey;
      }

      return null;
    } catch (e, stackTrace) {
      appLog.error('Error getting API key', name: _logName, error: e, stackTrace: stackTrace);
      return null;
    }
  }

  /// Prepare authentication headers
  Future<Map<String, String>> getAuthHeaders({bool isProtected = false}) async {
    final headers = <String, String>{};

    // Add content type by default
    headers[HttpHeaders.contentTypeHeader] = 'application/json';

    // Add API key for protected endpoints
    if (isProtected) {
      final apiKey = await getApiKey();
      if (apiKey != null) {
        headers['X-API-Key'] = apiKey;
      }
    }

    return headers;
  }

  /// Send a GET request to the API
  Future<http.Response> get(
    String endpoint, {
    bool isProtected = false,
    Map<String, dynamic>? queryParameters,
    Map<String, String>? additionalHeaders,
  }) async {
    final uri = Uri.parse('$baseUrl$endpoint').replace(
      queryParameters: queryParameters,
    );

    final headers = await getAuthHeaders(isProtected: isProtected);

    // Add any additional headers
    if (additionalHeaders != null) {
      headers.addAll(additionalHeaders);
    }

    return _httpClient.get(
      uri,
      headers: headers,
    );
  }

  /// Send a POST request to the API
  Future<http.Response> post(
    String endpoint, {
    bool isProtected = false,
    Map<String, dynamic>? queryParameters,
    Map<String, String>? additionalHeaders,
    Object? body,
    Encoding? encoding,
  }) async {
    final uri = Uri.parse('$baseUrl$endpoint').replace(
      queryParameters: queryParameters,
    );

    final headers = await getAuthHeaders(isProtected: isProtected);

    // Add any additional headers
    if (additionalHeaders != null) {
      headers.addAll(additionalHeaders);
    }

    return _httpClient.post(
      uri,
      headers: headers,
      body: body,
      encoding: encoding,
    );
  }

  /// Send a PUT request to the API
  Future<http.Response> put(
    String endpoint, {
    bool isProtected = false,
    Map<String, dynamic>? queryParameters,
    Map<String, String>? additionalHeaders,
    Object? body,
    Encoding? encoding,
  }) async {
    final uri = Uri.parse('$baseUrl$endpoint').replace(
      queryParameters: queryParameters,
    );

    final headers = await getAuthHeaders(isProtected: isProtected);

    // Add any additional headers
    if (additionalHeaders != null) {
      headers.addAll(additionalHeaders);
    }

    return _httpClient.put(
      uri,
      headers: headers,
      body: body,
      encoding: encoding,
    );
  }

  /// Send a DELETE request to the API
  Future<http.Response> delete(
    String endpoint, {
    bool isProtected = false,
    Map<String, dynamic>? queryParameters,
    Map<String, String>? additionalHeaders,
    Object? body,
    Encoding? encoding,
  }) async {
    final uri = Uri.parse('$baseUrl$endpoint').replace(
      queryParameters: queryParameters,
    );

    final headers = await getAuthHeaders(isProtected: isProtected);

    // Add any additional headers
    if (additionalHeaders != null) {
      headers.addAll(additionalHeaders);
    }

    return _httpClient.delete(
      uri,
      headers: headers,
      body: body,
      encoding: encoding,
    );
  }

  /// Close the HTTP client and dispose WebSocket resources
  void dispose() {
    _httpClient.close();

    // Close all topic controllers
    for (final controller in _topicControllers.values) {
      controller.close();
    }
    _topicControllers.clear();

    // Dispose WebSocket service
    _webSocketService?.dispose();
    _webSocketService = null;
  }

  /// Initialize the WebSocket service
  /// Returns true if initialization was successful, false otherwise
  Future<bool> initializeWebSocket() async {
    // Don't initialize WebSocket connections during early app startup
    // if using lazy initialization and app hasn't fully rendered yet
    if (_lazyWebSocketInit && !_isAppFullyRendered) {
      appLog.info('App not fully rendered yet, delaying WebSocket initialization', name: _logName);
      return false;
    }

    // If already initialized and connected, return true
    if (_webSocketService != null && _webSocketService!.isConnected) {
      appLog.info('WebSocket service already initialized and connected', name: _logName);
      return true;
    }

    // Avoid multiple simultaneous initialization attempts
    if (_initializingWebSocket) {
      appLog.info('WebSocket initialization already in progress', name: _logName);
      return false;
    }

    _initializingWebSocket = true;

    // If initialization has been attempted but failed, mark as initialized to prevent repeated attempts
    if (_webSocketInitialized && _webSocketService == null) {
      appLog.info('WebSocket initialization already attempted', name: _logName);
      _initializingWebSocket = false;
      return false;
    }

    _webSocketInitialized = true;

    try {
      // Get API key for authentication
      final apiKey = await getApiKey();

      if (apiKey == null || apiKey.isEmpty) {
        appLog.info('API key not available, skipping WebSocket initialization', name: _logName);
        _initializingWebSocket = false;
        return false;
      }

      // Create WebSocket service with API key
      _webSocketService = WebSocketService(apiKey: apiKey);

      // Connect to WebSocket server
      await _webSocketService!.connect();

      appLog.info('WebSocket service initialized successfully', name: _logName);
      _initializingWebSocket = false;

      // Process any pending requests now that WebSocket is connected
      _processPendingRequests();

      return true;
    } catch (e, stackTrace) {
      appLog.error('Error initializing WebSocket service',
          name: _logName, error: e, stackTrace: stackTrace);
      _initializingWebSocket = false;
      return false;
    }
  }

  /// Reinitialize WebSocket if needed (when authentication state changes)
  Future<void> reinitializeWebSocketIfNeeded() async {
    // Don't reinitialize WebSocket connections during early app startup
    // if using lazy initialization and app hasn't fully rendered yet
    if (_lazyWebSocketInit && !_isAppFullyRendered) {
      appLog.info('App not fully rendered yet, delaying WebSocket reinitialization',
          name: _logName);
      return;
    }

    if (_webSocketInitialized && _webSocketService?.isConnected == true) {
      appLog.debug('WebSocket already connected, no need to reinitialize', name: _logName);
      return;
    }

    // Reset the initialization flag to allow for reconnection attempt
    _webSocketInitialized = false;

    // Dispose of the old WebSocket service if it exists
    _webSocketService?.dispose();
    _webSocketService = null;

    appLog.info('Reinitializing WebSocket connection', name: _logName);

    // Initialize WebSocket with the new API key
    final success = await initializeWebSocket();

    if (success) {
      appLog.info('WebSocket reinitialized successfully', name: _logName);

      // Resubscribe to all topics
      for (final topic in _topicControllers.keys) {
        appLog.debug('Resubscribing to topic: $topic', name: _logName);
        _webSocketService!.subscribe<Map<String, dynamic>>(topic, (data) => data).listen((data) {
          if (!_topicControllers[topic]!.isClosed) {
            _topicControllers[topic]!.add(data);
          }
        });
      }
    } else {
      appLog.warning('WebSocket reinitialization failed', name: _logName);
    }
  }

  /// Subscribe to a WebSocket topic
  /// Returns a stream of messages for the topic
  /// If the WebSocket is not connected, attempts to connect first
  Stream<Map<String, dynamic>> subscribeToTopic(String topic) async* {
    // Create a stream controller for this topic if it doesn't exist
    if (!_topicControllers.containsKey(topic)) {
      _topicControllers[topic] = StreamController<Map<String, dynamic>>.broadcast();

      // Initialize WebSocket if needed
      final initialized = await initializeWebSocket();

      if (initialized && _webSocketService != null) {
        // Subscribe to the topic
        final subscription = _webSocketService!
            .subscribe<Map<String, dynamic>>(topic, (data) => data)
            .listen((data) {
          // Forward the message to our controller
          if (!_topicControllers[topic]!.isClosed) {
            _topicControllers[topic]!.add(data);
          }
        });

        // Handle controller close
        _topicControllers[topic]!.onCancel = () {
          subscription.cancel();
          _webSocketService?.unsubscribe(topic);
          _topicControllers.remove(topic);
        };
      } else {
        // If WebSocket initialization failed or is delayed, queue this subscription request
        if (_lazyWebSocketInit) {
          appLog.info('WebSocket not connected yet, queuing subscription for topic: $topic',
              name: _logName);
          _pendingRequests.add(_PendingWebSocketRequest(
            topic: topic,
            controller: _topicControllers[topic]!,
            isSubscription: true,
          ));
        } else {
          // If not using lazy initialization, close the controller and throw an exception
          _topicControllers[topic]!.close();
          _topicControllers.remove(topic);
          throw Exception('Failed to initialize WebSocket connection');
        }
      }
    }

    yield* _topicControllers[topic]!.stream;
  }

  /// Send a request to a WebSocket topic
  /// Returns true if the request was sent successfully, false otherwise
  Future<bool> sendWebSocketRequest(String topic, Map<String, dynamic> payload) async {
    // Create message now so we can queue it if needed
    final message = {
      'type': 'request',
      'topic': topic,
      'payload': payload,
      'request_id': DateTime.now().millisecondsSinceEpoch.toString(),
    };

    final messageStr = jsonEncode(message);

    // Initialize WebSocket if needed
    final initialized = await initializeWebSocket();

    if (!initialized || _webSocketService == null) {
      // If using lazy initialization, queue the request for later
      if (_lazyWebSocketInit) {
        appLog.info('WebSocket not connected yet, queuing request for topic: $topic',
            name: _logName);
        _pendingRequests.add(_PendingWebSocketRequest(
          topic: topic,
          message: messageStr,
          isSubscription: false,
        ));
        // Return true since we've queued the request
        return true;
      } else {
        appLog.warning('Cannot send WebSocket request, not connected', name: _logName);
        return false;
      }
    }

    try {
      appLog.info('Sending WebSocket request for topic: $topic', name: _logName);

      // Send the message
      _webSocketService!.sendCustomMessage(messageStr);
      return true;
    } catch (e, stackTrace) {
      appLog.error('Error sending WebSocket request',
          name: _logName, error: e, stackTrace: stackTrace);
      return false;
    }
  }

  /// The current API key, exposed for status checking
  String? get apiKey => _licenseManager.apiKey;

  /// Refresh the WebSocket connection when API key becomes available
  /// This is called from the ContentProcessingStatusIndicator when license changes
  Future<bool> refreshWebSocketConnection() async {
    appLog.info('Manually refreshing WebSocket connection', name: _logName);

    // Reset initialization flags
    _webSocketInitialized = false;
    _initializingWebSocket = false;

    // Dispose the existing WebSocket service if it exists
    if (_webSocketService != null) {
      _webSocketService!.dispose();
      _webSocketService = null;
    }

    // Initialize WebSocket with the updated API key
    return await initializeWebSocket();
  }

  /// Get a gRPC channel for making API calls with fallback options
  Future<ClientChannel> getGrpcChannel() async {
    try {
      // Get the gRPC URL and port from ApiConfig
      final grpcUrl = ApiConfig.grpcUrl;
      final host = ApiConfig.baseHost;
      final configuredPort = ApiConfig.grpcPort;

      // Use the centralized method to determine if this is a local development environment
      final isLocalDevelopment = ApiConfig.isLocalDevelopmentEnvironment;

      // Use the centralized method to determine if secure credentials should be used
      final useSecure = ApiConfig.useSecureGrpc;

      appLog.debug(
          'Creating gRPC channel for $grpcUrl (secure: $useSecure, local: $isLocalDevelopment)',
          name: _logName);

      // Define fallback ports - only try alternatives for local development
      // All ports in this list are non-nullable integers
      final fallbackPorts =
          isLocalDevelopment ? [configuredPort, 9090, 50051, 8080] : [configuredPort];

      // Store connection errors for diagnostics
      final errors = <String>[];

      // Attempt connections with fallback
      for (final port in fallbackPorts) {
        try {
          final channel = ClientChannel(
            host,
            port: port,
            options: ChannelOptions(
              credentials: useSecure
                  ? const ChannelCredentials.secure()
                  : const ChannelCredentials.insecure(),
              // Short timeout for quick fallback
              connectTimeout: const Duration(seconds: 3),
            ),
          );

          // Test the channel - will throw if cannot connect
          await channel.getConnection().timeout(const Duration(seconds: 3));

          // If successful, log and return the channel
          appLog.info('Successfully connected to gRPC server at $host:$port', name: _logName);

          // Update the port in memory if different from configured
          if (port != configuredPort) {
            appLog.info(
                'Using fallback gRPC port: $port instead of configured port: $configuredPort',
                name: _logName);
          }

          return channel;
        } catch (e) {
          // Log and collect the error, then try next port
          final error = 'Failed to connect to gRPC server at $host:$port: $e';
          errors.add(error);
          appLog.warning(error, name: _logName);

          // Small delay before trying next port
          await Future.delayed(const Duration(milliseconds: 100));
        }
      }

      // If we get here, all connection attempts failed
      throw Exception('Failed to connect to gRPC server after trying all fallback ports: $errors');
    } catch (e) {
      appLog.error('Error creating gRPC channel: $e', name: _logName);
      rethrow;
    }
  }

  /// Test connectivity to the gRPC server
  /// Returns true if connection was successful, false otherwise
  Future<bool> testGrpcConnection() async {
    appLog.debug('Testing gRPC connectivity', name: _logName);

    // Get the host and port from ApiConfig
    final host = ApiConfig.baseHost;
    final port = ApiConfig.grpcPort;

    // Use the centralized method to determine if secure credentials should be used
    final useSecure = ApiConfig.useSecureGrpc;

    appLog.info('Testing gRPC connection to $host:$port (secure: $useSecure)', name: _logName);

    try {
      // Create a test channel
      final channel = ClientChannel(
        host,
        port: port,
        options: ChannelOptions(
          credentials:
              useSecure ? const ChannelCredentials.secure() : const ChannelCredentials.insecure(),
          connectTimeout: const Duration(seconds: 5),
        ),
      );

      // Attempt to connect
      await channel.getConnection();
      appLog.info('Successfully connected to gRPC server at $host:$port', name: _logName);

      // Gracefully shutdown the channel
      await channel.shutdown();
      return true;
    } catch (e) {
      appLog.error('Failed to connect to gRPC server at $host:$port: $e', name: _logName);
      return false;
    }
  }
}

/// Class representing a pending WebSocket request that couldn't be sent immediately
/// Used for lazy WebSocket initialization to queue requests until connection is established
class _PendingWebSocketRequest {
  final String topic;
  final StreamController? controller;
  final String? message;
  final bool isSubscription;

  _PendingWebSocketRequest({
    required this.topic,
    this.controller,
    this.message,
    required this.isSubscription,
  });
}
