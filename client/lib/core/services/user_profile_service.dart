import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:promz/core/models/license_model.dart';
import 'package:promz/core/models/supabase_auth_state.dart';
import 'package:promz/core/models/user_profile_model.dart';
import 'package:promz/core/providers/license_provider.dart';
import 'package:promz/core/providers/supabase_auth_provider.dart';
import 'package:promz/core/services/license_manager_service.dart';
import 'package:promz/core/services/secure_storage_service_provider.dart';
import 'package:promz_common/promz_common.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// A service for managing and providing user profile data across the application
///
/// This service acts as the central repository for user profile information,
/// including authentication state and license information. It provides consistent
/// access to user data from any part of the application, and handles caching
/// this data in secure storage for optimized app startup.
///
/// Usage:
///   - Access via ClientContextService: `ClientContextService().userProfile`
///   - Listen for changes: `userProfile.addListener(callback)`
class UserProfileService extends ChangeNotifier {
  static const _logName = 'UserProfileService';
  static const _profileCacheKey = 'user_profile_data_v2';

  /// The current user profile model
  UserProfileModel _profile = UserProfileModel.empty();

  /// Get the current user profile
  UserProfileModel get profile => _profile;

  /// Whether the profile data is currently loading
  bool _isLoading = false;
  bool get isLoading => _isLoading;

  /// The provider container to access providers
  final ProviderContainer _container = ProviderContainer();

  /// Subscription for auth state updates
  late final ProviderSubscription<AsyncValue<SupabaseAuthState>> _authSubscription;

  /// Subscription for license updates
  late final ProviderSubscription<AsyncValue<UserLicense>> _licenseSubscription;

  UserProfileService() {
    _initialize();
  }

  /// Initialize the service
  Future<void> _initialize() async {
    appLog.debug('START: Initializing UserProfileService', name: _logName);

    // First try to load from cache
    await _loadFromCache();

    // Then set up listeners
    _setupListeners();

    appLog.debug('END: UserProfileService initialized', name: _logName);
  }

  /// Set up listeners for auth state and license changes
  void _setupListeners() {
    // Listen to auth state changes using the correct type
    _authSubscription = _container.listen<AsyncValue<SupabaseAuthState>>(
      authStateProvider,
      (previous, next) {
        appLog.debug('Auth state changed', name: _logName);
        if (next.hasValue && next.value != null) {
          _updateProfileFromSupabaseAuthState(next.value);
        }
      },
      onError: (error, stackTrace) {
        appLog.error('Error in auth state provider',
            name: _logName, error: error, stackTrace: stackTrace);
      },
    );

    // Listen to license changes
    _licenseSubscription = _container.listen<AsyncValue<UserLicense>>(
      userLicenseProvider,
      (previous, next) {
        appLog.debug('License state changed', name: _logName);
        if (next.hasValue && next.value != null) {
          _updateProfileFromLicense(next.value);
        }
      },
      onError: (error, stackTrace) {
        appLog.error('Error in license provider',
            name: _logName, error: error, stackTrace: stackTrace);
      },
    );
  }

  /// Update profile when auth state changes
  void _updateProfileFromSupabaseAuthState(SupabaseAuthState? authState) {
    if (authState == null) return;

    try {
      final user = authState.user;
      final isAuthenticated = authState.isAuthenticated;

      // Log warning if authenticated but user is null
      if (isAuthenticated && user == null) {
        appLog.warning('Auth state indicates authenticated but user is null', name: _logName);
      }

      // Extract all needed user information
      final String? email = user?.email;
      final String? avatarUrl = user?.userMetadata?['avatar_url'] as String?;
      final String? fullName = user?.userMetadata?['full_name'] as String?;
      final String? userId = user?.id;

      // Update profile with auth data
      _profile = _profile.copyWith(
        isAuthenticated: isAuthenticated,
        email: email,
        avatarUrl: avatarUrl,
        fullName: fullName,
        userId: userId,
      );

      // Save to cache and notify listeners
      _saveToCache();
      notifyListeners();
    } catch (e, stack) {
      appLog.error('Error updating profile from auth state',
          name: _logName, error: e, stackTrace: stack);
      // Don't rethrow as we want to handle this gracefully
    }
  }

  /// Update profile when license state changes
  void _updateProfileFromLicense(UserLicense? license) {
    if (license == null) return;

    try {
      // Update profile with license data
      _profile = _profile.copyWith(
        hasLicense: license.hasLicense,
        licenseType: license.licenseType,
        licenseExpiresAt: license.expiresAt,
      );

      // Save to cache and notify listeners
      _saveToCache();
      notifyListeners();
    } catch (e, stack) {
      appLog.error('Error updating profile from license',
          name: _logName, error: e, stackTrace: stack);
      // Don't rethrow as we want to handle this gracefully
    }
  }

  /// Update profile when license state changes
  /// This method can be called directly from LicenseManagerService to ensure profile stays in sync
  void updateProfileFromLicense(UserLicense? license) {
    if (license == null) return;

    try {
      // Update profile with license data
      _profile = _profile.copyWith(
        hasLicense: license.hasLicense,
        licenseType: license.licenseType,
        licenseExpiresAt: license.expiresAt,
      );

      // Save to cache and notify listeners
      _saveToCache();
      notifyListeners();
    } catch (e, stack) {
      appLog.error('Error updating profile from license (direct call)',
          name: _logName, error: e, stackTrace: stack);
      // Don't rethrow as we want to handle this gracefully
    }
  }

  /// Refresh profile data from both auth and license states
  ///
  /// If forceFresh is false (default), will prioritize cached license data
  /// If forceFresh is true, will always fetch fresh license data
  Future<void> refreshProfile({bool forceFresh = false}) async {
    appLog.debug('START: Refreshing profile data (forceFresh: $forceFresh)', name: _logName);
    _isLoading = true;
    notifyListeners();

    try {
      // Get the latest auth state and license
      final authState = Supabase.instance.client.auth.currentSession;
      final user = authState != null ? Supabase.instance.client.auth.currentUser : null;

      // Log warning if session exists but user is null
      if (authState != null && user == null) {
        appLog.warning('Session exists but user is null - possible inconsistent state',
            name: _logName);
      }

      // Update immediately with the latest auth data
      if (user != null) {
        try {
          // Extract all needed user information
          final String? email = user.email;
          final String? avatarUrl = user.userMetadata?['avatar_url'] as String?;
          final String? fullName = user.userMetadata?['full_name'] as String?;
          final String userId = user.id;

          _profile = _profile.copyWith(
            isAuthenticated: true,
            email: email,
            avatarUrl: avatarUrl,
            fullName: fullName,
            userId: userId,
          );
        } catch (e) {
          appLog.error('Error updating profile with user data', name: _logName, error: e);
          // If we can't update with user data, set to empty but keep isAuthenticated true
          // This will show the unauthenticated button but still allow auth-only features
          _profile = UserProfileModel.empty().copyWith(isAuthenticated: true);
        }
      } else {
        _profile = UserProfileModel.empty();
      }

      // Get license information with forceFresh parameter
      if (user != null) {
        try {
          // Get the license manager directly instead of through a provider
          final licenseManager = LicenseManagerService();
          final license = await licenseManager.getUserLicense(forceFresh: forceFresh);

          if (license != null) {
            _updateProfileFromLicense(license);
          }
        } catch (e) {
          appLog.error('Error getting license information', name: _logName, error: e);
          // Continue with the existing license information
        }
      }

      // Save to cache
      _saveToCache();
    } catch (e) {
      appLog.error('Error refreshing profile', name: _logName, error: e);
    } finally {
      _isLoading = false;
      notifyListeners();
      appLog.debug('END: Profile refreshed', name: _logName);
    }
  }

  /// Load profile data from secure storage
  Future<void> _loadFromCache() async {
    appLog.debug('START: Loading profile from cache', name: _logName);
    try {
      final profileData = await SecureStorageServiceProvider.getValue(_profileCacheKey);
      if (profileData == null || profileData.isEmpty) {
        appLog.debug('No cached profile found', name: _logName);
        return;
      }

      // Parse the JSON data
      final Map<String, dynamic> data =
          Map<String, dynamic>.from(json.decode(profileData) as Map<String, dynamic>);

      // Create profile from cached data
      _profile = UserProfileModel.fromCachedData(data);

      appLog.debug('Profile loaded from cache: ${_profile.email}', name: _logName);
      notifyListeners();
    } catch (e) {
      appLog.error('Error loading profile from cache', name: _logName, error: e);
    }
    appLog.debug('END: Loading profile from cache', name: _logName);
  }

  /// Save profile data to secure storage
  Future<void> _saveToCache() async {
    try {
      // Convert profile to map and save as JSON
      final profileMap = _profile.toMap();
      await SecureStorageServiceProvider.saveValue(_profileCacheKey, json.encode(profileMap));
    } catch (e) {
      appLog.error('Error saving profile to cache', name: _logName, error: e);
    }
  }

  /// Clear cached profile data
  Future<void> clearCache() async {
    appLog.debug('START: Clearing profile cache', name: _logName);
    try {
      await SecureStorageServiceProvider.deleteValue(_profileCacheKey);

      appLog.debug('Profile cache cleared', name: _logName);
    } catch (e) {
      appLog.error('Error clearing profile cache', name: _logName, error: e);
    }
    appLog.debug('END: Clearing profile cache', name: _logName);
  }

  @override
  void dispose() {
    _authSubscription.close();
    _licenseSubscription.close();
    super.dispose();
  }
}
