/// Abstract interface for secure storage operations
///
/// This interface defines the contract for secure storage operations,
/// allowing for different implementations to be used in production and testing.
abstract class ISecureStorageService {
  /// Retrieve a value from secure storage
  static Future<String?> getValue(String key) async {
    throw UnimplementedError('getValue must be implemented by subclasses');
  }

  /// Save a value to secure storage
  static Future<void> saveValue(String key, String value) async {
    throw UnimplementedError('saveValue must be implemented by subclasses');
  }

  /// Check if a key exists in secure storage
  static Future<bool> containsKey(String key) async {
    throw UnimplementedError('containsKey must be implemented by subclasses');
  }

  /// Delete a value from secure storage
  static Future<void> deleteValue(String key) async {
    throw UnimplementedError('deleteValue must be implemented by subclasses');
  }

  /// Delete all values from secure storage
  static Future<void> deleteAll() async {
    throw UnimplementedError('deleteAll must be implemented by subclasses');
  }
}
