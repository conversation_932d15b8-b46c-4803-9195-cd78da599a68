import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:promz/core/models/content_processing_task.dart';
import 'package:promz/core/services/secure_storage_service.dart';
import 'package:promz/core/utils/hash_helper.dart';
import 'package:promz_common/promz_common.dart';
import 'package:rxdart/rxdart.dart';

/// Manages content processing tasks with persistent storage
class ContentProcessingTaskManager {
  static const _logName = 'ContentProcessingTaskManager';
  static const _storageKey = 'content_processing_tasks';

  /// Stream controller for task updates
  final _tasksSubject = BehaviorSubject<List<ContentProcessingTask>>();

  /// In-memory cache of tasks
  List<ContentProcessingTask> _tasks = [];

  /// Singleton instance
  static ContentProcessingTaskManager? _instance;

  /// Gets the singleton instance
  static ContentProcessingTaskManager get instance {
    _instance ??= ContentProcessingTaskManager._();
    return _instance!;
  }

  /// Private constructor
  ContentProcessingTaskManager._() {
    _init();
  }

  /// Initialize the manager and load tasks from storage
  Future<void> _init() async {
    appLog.debug('Initializing ContentProcessingTaskManager', name: _logName);
    await _loadTasks();
  }

  /// Load tasks from secure storage
  Future<void> _loadTasks() async {
    try {
      final tasksJson = await SecureStorageService.getValue(_storageKey);

      if (tasksJson == null || tasksJson.isEmpty) {
        _tasks = [];
        _tasksSubject.add(_tasks);
        appLog.debug('No tasks found in storage', name: _logName);
        return;
      }

      final Map<String, dynamic> tasksMap = jsonDecode(tasksJson);
      final List<dynamic> tasksList = tasksMap['content_processing_tasks'] ?? [];

      _tasks = tasksList.map((taskJson) => ContentProcessingTask.fromJson(taskJson)).toList();

      // Sort tasks by creation date (newest first)
      _tasks.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      _tasksSubject.add(_tasks);
      appLog.debug('Loaded ${_tasks.length} tasks from storage', name: _logName);
    } catch (e) {
      appLog.error('Failed to load tasks from storage', name: _logName, error: e);
      _tasks = [];
      _tasksSubject.add(_tasks);
    }
  }

  /// Save tasks to secure storage
  Future<void> _saveTasks() async {
    try {
      final tasksMap = {
        'content_processing_tasks': _tasks.map((task) => task.toJson()).toList(),
      };

      final tasksJson = jsonEncode(tasksMap);
      await SecureStorageService.saveValue(_storageKey, tasksJson);
      appLog.debug('Saved ${_tasks.length} tasks to storage', name: _logName);
    } catch (e) {
      appLog.error('Failed to save tasks to storage', name: _logName, error: e);
    }
  }

  /// Check if a file with the same hash already exists in the task list
  /// Returns the existing task if a duplicate is found, null otherwise
  Future<ContentProcessingTask?> findDuplicateTask(String filePath) async {
    try {
      // Calculate the hash of the file
      final fileHash = await HashHelper.calculateFileHash(filePath);

      if (fileHash.isEmpty) {
        appLog.debug('Could not calculate hash for file: $filePath', name: _logName);
        return null;
      }

      // Look for tasks with the same hash
      final duplicateTasks =
          _tasks.where((task) => task.fileHash != null && task.fileHash == fileHash).toList();

      if (duplicateTasks.isNotEmpty) {
        final duplicate = duplicateTasks.first;
        appLog.info(
          'Found duplicate task for file: $filePath, existing task ID: ${duplicate.id}',
          name: _logName,
        );
        return duplicate;
      }

      appLog.debug('No duplicate task found for file: $filePath', name: _logName);
      return null;
    } catch (e) {
      appLog.error(
        'Error checking for duplicate task',
        name: _logName,
        error: e,
      );
      return null;
    }
  }

  /// Add a new task for processing
  Future<ContentProcessingTask> addTask(String content, {String source = 'internal'}) async {
    appLog.debug('Adding new task for content: ${truncateForLogging(content)}', name: _logName);

    // Check for duplicates first
    final duplicateTask = await findDuplicateTask(content);
    if (duplicateTask != null) {
      appLog.info('Returning existing task for duplicate content', name: _logName);
      return duplicateTask;
    }

    // Create a new task
    var task = ContentProcessingTask.fromFilePath(content);

    // Update the source
    task = task.copyWith(source: source);

    // Calculate and add file hash if it's a file path
    try {
      // Check if the content is a file path
      final file = File(content);
      final isFile = await file.exists();

      if (isFile) {
        // It's a file, calculate hash and get size
        final fileHash = await HashHelper.calculateFileHash(content);
        if (fileHash.isNotEmpty) {
          task = task.copyWith(fileHash: fileHash);
        }

        // Update file size
        final fileSize = await file.length();
        task = task.copyWith(fileSize: fileSize);
      } else {
        // It's not a file, calculate hash from content string
        final contentHash = HashHelper.calculateStringHash(content);
        task = task.copyWith(fileHash: contentHash);
      }
    } catch (e) {
      appLog.warning(
        'Failed to process content for task: ${task.id}',
        name: _logName,
        error: e,
      );

      // If we can't determine if it's a file, treat it as text content
      final contentHash = HashHelper.calculateStringHash(content);
      task = task.copyWith(fileHash: contentHash);
    }

    // Add the task to the list and ensure it's fully saved before returning
    await _addTaskToList(task);

    // Double-check that the task was added successfully
    final taskIndex = _tasks.indexWhere((t) => t.id == task.id);
    if (taskIndex == -1) {
      appLog.error('Task was not added successfully: ${task.id}', name: _logName);

      // Try adding the task again if it wasn't found
      _tasks = [task, ..._tasks];
      _tasksSubject.add(_tasks);
      await _saveTasks();

      appLog.debug('Attempted to add task again after failure: ${task.id}', name: _logName);
    } else {
      appLog.debug('Task added successfully: ${task.id}', name: _logName);
    }

    return task;
  }

  /// Helper to add a task to the list and save
  Future<void> _addTaskToList(ContentProcessingTask task) async {
    // Add the task to the in-memory list
    _tasks = [task, ..._tasks];
    _tasksSubject.add(_tasks);

    // Save to storage and wait for completion
    await _saveTasks();

    // Log after successful save
    task.logTaskInfo(message: 'Task added');

    // Wait a small amount of time to ensure storage operations complete
    // This helps prevent race conditions with subsequent operations
    await Future.delayed(const Duration(milliseconds: 50));
  }

  /// Update the status of a task
  Future<ContentProcessingTask?> updateTaskStatus(
    String taskId,
    ContentProcessingTaskStatus status,
  ) async {
    return _updateTask(
      taskId,
      (task) => task.copyWith(
        status: status,
        updatedAt: DateTime.now(),
      ),
      'Status updated to ${status.label}',
    );
  }

  /// Update the progress of a task
  Future<ContentProcessingTask?> updateTaskProgress(
    String taskId,
    double progress,
  ) async {
    // Ensure progress is between 0 and 1
    final validProgress = progress.clamp(0.0, 1.0);

    return _updateTask(
      taskId,
      (task) => task.copyWith(
        progress: validProgress,
        updatedAt: DateTime.now(),
      ),
      'Progress updated to ${(validProgress * 100).toStringAsFixed(1)}%',
    );
  }

  /// Mark a task as completed with a result
  Future<ContentProcessingTask?> completeTask(
    String taskId,
    String resultId,
  ) async {
    return _updateTask(
      taskId,
      (task) => task.copyWith(
        status: ContentProcessingTaskStatus.completed,
        progress: 1.0,
        resultId: resultId,
        updatedAt: DateTime.now(),
      ),
      'Task completed with resultId: $resultId',
    );
  }

  /// Mark a task as failed with an error message
  Future<ContentProcessingTask?> failTask(
    String taskId,
    String errorMessage,
  ) async {
    return _updateTask(
      taskId,
      (task) => task.copyWith(
        status: ContentProcessingTaskStatus.failed,
        errorMessage: errorMessage,
        updatedAt: DateTime.now(),
      ),
      'Task failed: $errorMessage',
    );
  }

  /// Update a task with metadata from processing results
  Future<ContentProcessingTask?> updateTaskWithMetadata(
      String taskId, Map<String, dynamic> metadata,
      {String? displayName}) async {
    return _updateTask(
      taskId,
      (task) {
        // Update the display name if provided explicitly
        if (displayName != null && displayName.isNotEmpty) {
          return task.copyWith(
            displayName: displayName,
            updatedAt: DateTime.now(),
          );
        }

        // If no explicit display name is provided, try to extract from metadata
        String? newDisplayName;

        // Check for different content types and extract appropriate display names
        if (metadata.containsKey('title')) {
          // For YouTube videos
          if (task.filePath.contains('youtube.com') ||
              task.filePath.contains('youtu.be') ||
              metadata.containsKey('videoId')) {
            newDisplayName = metadata['title'] as String?;
            if (newDisplayName != null) {
              // Add YouTube prefix if not already included
              if (!newDisplayName.contains('YouTube')) {
                newDisplayName = 'YouTube: $newDisplayName';
              }
            }
          }
          // For news articles
          else if (task.filePath.startsWith('http') || metadata.containsKey('siteName')) {
            newDisplayName = metadata['title'] as String?;
            // Add site name if available
            final siteName = metadata['siteName'] as String?;
            if (siteName != null && siteName.isNotEmpty && newDisplayName != null) {
              newDisplayName = '$newDisplayName - $siteName';
            }
          }
          // For other content types with a title
          else {
            newDisplayName = metadata['title'] as String?;
          }
        }
        // For WhatsApp chats
        else if (metadata.containsKey('groupName') &&
            (task.fileName.contains('WhatsApp') || task.displayName.contains('WhatsApp'))) {
          newDisplayName = 'WhatsApp: ${metadata['groupName']}';
        }
        // For PDF documents
        else if (task.fileName.toLowerCase().endsWith('.pdf') &&
            metadata.containsKey('documentTitle')) {
          newDisplayName = metadata['documentTitle'] as String?;
        }

        // If we found a suitable display name, use it
        if (newDisplayName != null && newDisplayName.isNotEmpty) {
          appLog.debug('Updating display name from metadata: "$newDisplayName"', name: _logName);
          return task.copyWith(
            displayName: newDisplayName,
            updatedAt: DateTime.now(),
          );
        }

        // No suitable display name found in metadata
        return task.copyWith(updatedAt: DateTime.now());
      },
      'Task updated with metadata',
    );
  }

  /// Update a task with a custom update function
  Future<ContentProcessingTask?> updateTask(
    String taskId,
    ContentProcessingTask Function(ContentProcessingTask) updateFn,
  ) async {
    return _updateTask(
      taskId,
      updateFn,
      'Task updated with custom function',
    );
  }

  /// Helper to update a task and save changes
  Future<ContentProcessingTask?> _updateTask(
    String taskId,
    ContentProcessingTask Function(ContentProcessingTask) updateFn,
    String logMessage,
  ) async {
    final taskIndex = _tasks.indexWhere((task) => task.id == taskId);

    if (taskIndex == -1) {
      appLog.warning('Task not found: $taskId', name: _logName);
      return null;
    }

    final oldTask = _tasks[taskIndex];
    final updatedTask = updateFn(oldTask);

    _tasks[taskIndex] = updatedTask;
    _tasksSubject.add(_tasks);
    await _saveTasks();

    updatedTask.logTaskInfo(message: logMessage);
    return updatedTask;
  }

  /// Remove a task by ID
  Future<bool> removeTask(String taskId) async {
    final taskIndex = _tasks.indexWhere((task) => task.id == taskId);

    if (taskIndex == -1) {
      appLog.warning('Task not found for removal: $taskId', name: _logName);
      return false;
    }

    final task = _tasks[taskIndex];
    _tasks.removeAt(taskIndex);
    _tasksSubject.add(_tasks);
    await _saveTasks();

    task.logTaskInfo(message: 'Task removed');
    return true;
  }

  /// Clean up old completed or failed tasks
  /// Keeps only the most recent [maxTasks] tasks
  Future<void> cleanupOldTasks({int maxTasks = 20}) async {
    if (_tasks.length <= maxTasks) {
      return;
    }

    // Sort by date (newest first)
    _tasks.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));

    // Keep active tasks and up to maxTasks total
    final activeTasks = _tasks
        .where((task) =>
            task.status == ContentProcessingTaskStatus.pending ||
            task.status == ContentProcessingTaskStatus.processing)
        .toList();

    final completedTasks = _tasks
        .where((task) =>
            task.status == ContentProcessingTaskStatus.completed ||
            task.status == ContentProcessingTaskStatus.failed)
        .toList();

    final tasksToKeep = maxTasks - activeTasks.length;
    if (tasksToKeep <= 0) {
      _tasks = activeTasks;
    } else {
      _tasks = [
        ...activeTasks,
        ...completedTasks.take(tasksToKeep),
      ];
    }

    _tasksSubject.add(_tasks);
    await _saveTasks();

    appLog.debug(
      'Cleaned up tasks. Keeping ${_tasks.length} of ${activeTasks.length + completedTasks.length}',
      name: _logName,
    );
  }

  /// Process pending tasks
  Future<List<ContentProcessingTask>> processPendingTasks() async {
    appLog.debug('Processing pending tasks', name: _logName);

    final pendingTasks = _tasks
        .where(
          (task) => task.status == ContentProcessingTaskStatus.pending,
        )
        .toList();

    return pendingTasks;
  }

  /// Get all tasks
  List<ContentProcessingTask> getAllTasks() {
    return List.unmodifiable(_tasks);
  }

  ContentProcessingTask getTaskByFilePath(String filePath, String url) {
    return _tasks.firstWhere((task) => task.filePath == filePath || task.filePath == url);
  }

  /// Get tasks by status
  List<ContentProcessingTask> getTasksByStatus(ContentProcessingTaskStatus status) {
    return _tasks.where((task) => task.status == status).toList();
  }

  /// Get active tasks (pending, processing, or auth required)
  List<ContentProcessingTask> getActiveTasks() {
    return _tasks
        .where((task) =>
            task.status == ContentProcessingTaskStatus.pending ||
            task.status == ContentProcessingTaskStatus.processing)
        .toList();
  }

  /// Clear all tasks from storage
  Future<void> clearAllTasks() async {
    appLog.info('Clearing all tasks from storage', name: _logName);

    // Clear the tasks list
    _tasks = [];

    // Update the stream and save to storage
    _tasksSubject.add(_tasks);
    await _saveTasks();

    appLog.debug('All tasks cleared from storage', name: _logName);
  }

  /// Stream of task updates
  Stream<List<ContentProcessingTask>> get tasksStream => _tasksSubject.stream;

  /// Dispose resources
  void dispose() {
    _tasksSubject.close();
  }
}
