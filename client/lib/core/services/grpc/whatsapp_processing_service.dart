import 'package:grpc/grpc.dart';
import 'package:promz_common/promz_common.dart';
import 'package:promz_common/config/api_config.dart';
import 'package:promz/core/services/api/api_client.dart';
import 'package:promz/generated/whatsapp_metadata.pbgrpc.dart';

/// Service for processing WhatsApp content using gRPC
class WhatsAppProcessingService {
  static const _logName = 'WhatsAppProcessingService';

  final ApiClient _apiClient;
  late ClientChannel _channel;
  late WhatsAppProcessingServiceClient _client;

  WhatsAppProcessingService({
    required ApiClient apiClient,
  }) : _apiClient = apiClient {
    _initializeClient();
  }

  void _initializeClient() {
    final apiUrl = ApiConfig.baseUrl;
    final uri = Uri.parse(apiUrl);

    _channel = ClientChannel(
      uri.host,
      port: uri.port,
      options: const ChannelOptions(
        credentials: ChannelCredentials.insecure(),
      ),
    );

    _client = WhatsAppProcessingServiceClient(_channel);
  }

  /// Detect if the provided content is a WhatsApp chat
  Future<DetectWhatsAppResponse> detectWhatsAppContent(String content, String fileName) async {
    try {
      // Get auth token
      final token = await _apiClient.getApiKey();
      if (token == null) {
        throw Exception('Authentication token not available');
      }

      // Create metadata with auth token
      final metadata = {
        'authorization': 'Bearer $token',
      };

      // Create request
      final request = DetectWhatsAppRequest()
        ..content = content
        ..fileName = fileName;

      // Make gRPC call
      final response = await _client.detectWhatsAppContent(
        request,
        options: CallOptions(metadata: metadata),
      );

      appLog.debug(
        'WhatsApp detection result: ${response.isWhatsappChat}, '
        'confidence: ${response.confidenceScore}',
        name: _logName,
      );

      return response;
    } catch (e, stack) {
      appLog.error(
        'Error detecting WhatsApp content',
        name: _logName,
        error: e,
        stackTrace: stack,
      );
      rethrow;
    }
  }

  /// Process WhatsApp chat content
  Future<ProcessWhatsAppResponse> processWhatsAppContent(String content, String fileName) async {
    try {
      // Get auth token
      final token = await _apiClient.getApiKey();
      if (token == null) {
        throw Exception('Authentication token not available');
      }

      // Create metadata with auth token
      final metadata = {
        'authorization': 'Bearer $token',
      };

      // Create request
      final request = ProcessWhatsAppRequest()
        ..content = content
        ..fileName = fileName;

      // Make gRPC call
      final response = await _client.processWhatsAppContent(
        request,
        options: CallOptions(metadata: metadata),
      );

      appLog.debug(
        'WhatsApp processing completed, '
        'message count: ${response.metadata.messageCount}, '
        'participants: ${response.metadata.participants.length}',
        name: _logName,
      );

      return response;
    } catch (e, stack) {
      appLog.error(
        'Error processing WhatsApp content',
        name: _logName,
        error: e,
        stackTrace: stack,
      );
      rethrow;
    }
  }

  /// Close the gRPC channel
  void dispose() {
    _channel.shutdown();
  }
}
