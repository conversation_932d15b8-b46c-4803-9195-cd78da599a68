import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:promz_common/promz_common.dart';

final templateCacheServiceProvider = Provider<TemplateCacheService>((ref) {
  return TemplateCacheService();
});

/// Service for caching frequently used templates to improve performance
class TemplateCacheService {
  static const _logName = 'TemplateCacheService';
  static const _maxCacheSize = 100; // Maximum number of templates to cache

  // Cache for rendered templates
  final Map<String, String> _renderedTemplateCache = {};

  // Cache for template processing results
  final Map<String, Map<String, dynamic>> _processedTemplateCache = {};

  // Cache hit counter for LRU eviction
  final Map<String, int> _cacheHitCounter = {};

  /// Get a rendered template from the cache
  /// Returns null if the template is not in the cache
  String? getRenderedTemplate(String templateKey) {
    final cachedTemplate = _renderedTemplateCache[templateKey];
    if (cachedTemplate != null) {
      // Increment hit counter for this template
      _cacheHitCounter[templateKey] = (_cacheHitCounter[templateKey] ?? 0) + 1;
      appLog.debug('Cache hit for template: $templateKey', name: _logName);
    }
    return cachedTemplate;
  }

  /// Store a rendered template in the cache
  void storeRenderedTemplate(String templateKey, String renderedTemplate) {
    // Check if we need to evict an item from the cache
    if (_renderedTemplateCache.length >= _maxCacheSize) {
      _evictLeastRecentlyUsed();
    }

    _renderedTemplateCache[templateKey] = renderedTemplate;
    _cacheHitCounter[templateKey] = 1;
    appLog.debug('Stored template in cache: $templateKey', name: _logName);
  }

  /// Get processed template data from the cache
  /// Returns null if the template is not in the cache
  Map<String, dynamic>? getProcessedTemplate(String templateKey) {
    final cachedData = _processedTemplateCache[templateKey];
    if (cachedData != null) {
      // Increment hit counter for this template
      _cacheHitCounter[templateKey] = (_cacheHitCounter[templateKey] ?? 0) + 1;
      appLog.debug('Cache hit for processed template: $templateKey', name: _logName);
    }
    return cachedData;
  }

  /// Store processed template data in the cache
  void storeProcessedTemplate(String templateKey, Map<String, dynamic> processedData) {
    // Check if we need to evict an item from the cache
    if (_processedTemplateCache.length >= _maxCacheSize) {
      _evictLeastRecentlyUsed();
    }

    _processedTemplateCache[templateKey] = processedData;
    _cacheHitCounter[templateKey] = 1;
    appLog.debug('Stored processed template in cache: $templateKey', name: _logName);
  }

  /// Generate a cache key for a template with variables
  String generateCacheKey(String templateText, Map<String, dynamic> variables) {
    // Sort variable keys to ensure consistent cache keys
    final sortedKeys = variables.keys.toList()..sort();
    final variableString = sortedKeys.map((key) => '$key=${variables[key]}').join('|');

    // Combine template text and variables to create a unique key
    return '$templateText::$variableString';
  }

  /// Clear the entire cache
  void clearCache() {
    _renderedTemplateCache.clear();
    _processedTemplateCache.clear();
    _cacheHitCounter.clear();
    appLog.debug('Template cache cleared', name: _logName);
  }

  /// Remove a specific template from the cache
  void removeFromCache(String templateKey) {
    _renderedTemplateCache.remove(templateKey);
    _processedTemplateCache.remove(templateKey);
    _cacheHitCounter.remove(templateKey);
    appLog.debug('Removed template from cache: $templateKey', name: _logName);
  }

  /// Evict the least recently used item from the cache
  void _evictLeastRecentlyUsed() {
    if (_cacheHitCounter.isEmpty) return;

    // Find the key with the lowest hit count
    String? keyToEvict;
    int lowestHitCount = -1;

    for (final entry in _cacheHitCounter.entries) {
      if (lowestHitCount == -1 || entry.value < lowestHitCount) {
        lowestHitCount = entry.value;
        keyToEvict = entry.key;
      }
    }

    if (keyToEvict != null) {
      removeFromCache(keyToEvict);
      appLog.debug('Evicted least recently used template: $keyToEvict', name: _logName);
    }
  }

  /// Get cache statistics
  Map<String, dynamic> getCacheStats() {
    return {
      'renderedCacheSize': _renderedTemplateCache.length,
      'processedCacheSize': _processedTemplateCache.length,
      'totalCacheEntries': _cacheHitCounter.length,
    };
  }
}
