import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:promz/core/services/mock_secure_storage_service.dart';
import 'package:promz/core/services/secure_storage_service.dart';
import 'package:promz_common/promz_common.dart';

/// Provider for secure storage services that switches between real and mock implementations
///
/// This class acts as a facade for secure storage operations, directing calls to either
/// the real SecureStorageService (for production) or MockSecureStorageService (for testing).
/// It centralizes the decision of which implementation to use based on the current environment.
class SecureStorageServiceProvider {
  static const _logName = 'SecureStorageServiceProvider';

  // Flag to determine which implementation to use
  static bool _useTestImplementation = false;

  /// Switch to using the test implementation
  static void useTestImplementation() {
    appLog.debug('Switching to test implementation', name: _logName);
    _useTestImplementation = true;
    MockSecureStorageService.reset(); // Clear any existing test data
  }

  /// Switch to using the real implementation
  static void useRealImplementation() {
    appLog.debug('Switching to real implementation', name: _logName);
    _useTestImplementation = false;
  }

  /// Automatically use test implementation in test mode
  static void initialize() {
    if (kDebugMode) {
      // In Flutter tests, this will detect test environment
      // For simplicity, we'll just rely on explicit calls to useTestImplementation()
      // from the test setup, rather than trying to auto-detect
      appLog.debug('SecureStorageServiceProvider initialized in debug mode', name: _logName);
    }
  }

  /// Retrieve a value from secure storage
  static Future<String?> getValue(String key) async {
    try {
      if (_useTestImplementation) {
        return await MockSecureStorageService.getValue(key);
      } else {
        return await SecureStorageService.getValue(key);
      }
    } catch (e) {
      appLog.error('Error retrieving value for key: $key', name: _logName, error: e);
      return null;
    }
  }

  /// Save a value to secure storage
  static Future<void> saveValue(String key, String value) async {
    try {
      if (_useTestImplementation) {
        await MockSecureStorageService.saveValue(key, value);
      } else {
        // Check if key exists first
        final exists = await SecureStorageService.containsKey(key);
        if (exists) {
          // Delete the key first to avoid "already exists" errors
          appLog.debug('Key $key already exists, deleting before saving', name: _logName);
          await SecureStorageService.deleteValue(key);
        }

        // Now save the value
        await SecureStorageService.saveValue(key, value);
      }
    } catch (e) {
      appLog.error('Error saving value for key: $key', name: _logName, error: e);
      // Don't rethrow to avoid crashing the app, but log the error
    }
  }

  /// Check if a key exists in secure storage
  static Future<bool> containsKey(String key) async {
    try {
      if (_useTestImplementation) {
        return await MockSecureStorageService.containsKey(key);
      } else {
        return await SecureStorageService.containsKey(key);
      }
    } catch (e) {
      appLog.error('Error checking if key exists: $key', name: _logName, error: e);
      return false;
    }
  }

  /// Delete a value from secure storage
  static Future<void> deleteValue(String key) async {
    try {
      if (_useTestImplementation) {
        await MockSecureStorageService.deleteValue(key);
      } else {
        await SecureStorageService.deleteValue(key);
      }
    } catch (e) {
      appLog.error('Error deleting value for key: $key', name: _logName, error: e);
      // Don't rethrow to avoid crashing the app, but log the error
    }
  }

  /// Delete all values from secure storage
  static Future<void> deleteAll() async {
    try {
      if (_useTestImplementation) {
        await MockSecureStorageService.deleteAll();
      } else {
        await SecureStorageService.deleteAll();
      }
    } catch (e) {
      appLog.error('Error deleting all values from storage', name: _logName, error: e);
      // Don't rethrow to avoid crashing the app, but log the error
    }
  }
}
