import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:crypto/crypto.dart';
import 'package:promz_common/promz_common.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:url_launcher/url_launcher.dart';

/// Windows-specific OAuth service for handling authentication flows
///
/// This service provides desktop-appropriate OAuth flows for Windows,
/// using the system browser for authentication and handling redirects
/// back to the application.
class WindowsOAuthService {
  static const _logName = 'WindowsOAuthService';

  // Singleton instance
  static final WindowsOAuthService _instance = WindowsOAuthService._internal();
  factory WindowsOAuthService() => _instance;
  WindowsOAuthService._internal();

  static const String _redirectScheme = 'promz';
  static const String _redirectHost = 'auth-callback';
  static const String _redirectUri = '$_redirectScheme://$_redirectHost';

  // PKCE code verifier and challenge
  String _codeVerifier = '';
  String _codeChallenge = '';

  // Reference to the Supabase client
  late final SupabaseClient _supabaseClient;

  // Store the Supabase URL and key for auth URLs
  String _supabaseUrl = '';
  String _supabaseKey = '';

  // Completer for handling the OAuth flow completion
  Completer<User?>? _authCompleter;

  /// Initialize the Windows OAuth service
  void initialize(SupabaseClient supabaseClient, String supabaseUrl, String supabaseKey) {
    _supabaseClient = supabaseClient;
    _supabaseUrl = supabaseUrl;
    _supabaseKey = supabaseKey;

    appLog.debug('Initialized Windows OAuth service with URL: $_supabaseUrl', name: _logName);

    // Register protocol handler if not already registered
    _registerProtocolHandler().then((_) {
      appLog.debug('Protocol handler registration checked', name: _logName);
    }).catchError((error) {
      appLog.error('Failed to register protocol handler', name: _logName, error: error);
    });
  }

  /// Register the URL protocol handler for the app (promz://)
  Future<void> _registerProtocolHandler() async {
    if (Platform.isWindows) {
      try {
        // Check if we've already registered the protocol handler
        final result = await Process.run(
            'reg', ['query', 'HKCU\\Software\\Classes\\$_redirectScheme'],
            runInShell: true);

        // If protocol handler is not registered, register it
        if (result.exitCode != 0) {
          appLog.debug('Protocol handler not registered, registering now', name: _logName);

          // Get the path of the current executable
          final String exePath = Platform.resolvedExecutable;

          // Register the protocol handler
          final registerResult = await Process.run(
              'reg',
              [
                'add',
                'HKCU\\Software\\Classes\\$_redirectScheme',
                '/ve',
                '/t',
                'REG_SZ',
                '/d',
                'URL:Promz Protocol',
                '/f'
              ],
              runInShell: true);

          if (registerResult.exitCode == 0) {
            // Add URL Protocol
            await Process.run(
                'reg',
                [
                  'add',
                  'HKCU\\Software\\Classes\\$_redirectScheme',
                  '/v',
                  'URL Protocol',
                  '/t',
                  'REG_SZ',
                  '/d',
                  '',
                  '/f'
                ],
                runInShell: true);

            // Create command key
            await Process.run(
                'reg',
                [
                  'add',
                  'HKCU\\Software\\Classes\\$_redirectScheme\\shell\\open\\command',
                  '/ve',
                  '/t',
                  'REG_SZ',
                  '/d',
                  '"$exePath" "%1"',
                  '/f'
                ],
                runInShell: true);

            appLog.debug('Protocol handler registered successfully', name: _logName);
          } else {
            appLog.error('Failed to register protocol handler',
                name: _logName, error: registerResult.stderr);
          }
        } else {
          appLog.debug('Protocol handler already registered', name: _logName);
        }
      } catch (e) {
        appLog.error('Error registering protocol handler', name: _logName, error: e);
        // We'll continue even if registration fails
      }
    }
  }

  /// Sign in with Google using system browser on Windows
  Future<User?> signInWithGoogle() async {
    appLog.debug('Starting Windows-specific Google sign-in flow', name: _logName);

    // Generate PKCE codes
    _generatePkceCodeChallenge();

    // Get the authorization URL from Supabase
    final String authUrl = _buildGoogleAuthUrl();

    // Reset any previous authentication flow
    _resetAuthFlow();

    // Create a completer to wait for the authentication response
    _authCompleter = Completer<User?>();

    // Open the URL in system browser
    try {
      final uri = Uri.parse(authUrl);
      appLog.debug('Opening browser with URL: $authUrl', name: _logName);

      if (await canLaunchUrl(uri)) {
        await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );
      } else {
        appLog.error('Could not launch URL: $authUrl', name: _logName);
        return null;
      }

      // Wait for the authentication flow to complete
      return await _authCompleter!.future.timeout(
        const Duration(minutes: 5),
        onTimeout: () {
          appLog.error('Authentication flow timed out', name: _logName);
          return null;
        },
      );
    } catch (e) {
      appLog.error('Error launching browser', name: _logName, error: e);
      return null;
    }
  }

  /// Sign in with Microsoft using system browser on Windows
  Future<User?> signInWithMicrosoft() async {
    appLog.debug('Starting Windows-specific Microsoft sign-in flow', name: _logName);

    // Generate PKCE codes
    _generatePkceCodeChallenge();

    // Get the authorization URL from Supabase
    final String authUrl = _buildMicrosoftAuthUrl();

    // Reset any previous authentication flow
    _resetAuthFlow();

    // Create a completer to wait for the authentication response
    _authCompleter = Completer<User?>();

    // Open the URL in system browser
    try {
      final uri = Uri.parse(authUrl);
      appLog.debug('Opening browser with URL: $authUrl', name: _logName);

      if (await canLaunchUrl(uri)) {
        await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );
      } else {
        appLog.error('Could not launch URL: $authUrl', name: _logName);
        return null;
      }

      // Wait for the authentication flow to complete
      return await _authCompleter!.future.timeout(
        const Duration(minutes: 5),
        onTimeout: () {
          appLog.error('Authentication flow timed out', name: _logName);
          return null;
        },
      );
    } catch (e) {
      appLog.error('Error launching browser', name: _logName, error: e);
      return null;
    }
  }

  /// Handle the callback from the OAuth provider
  /// This should be called when the app receives a deep link
  Future<void> handleAuthCallback(Uri uri) async {
    appLog.debug('Received auth callback: $uri', name: _logName);

    if (_authCompleter == null || _authCompleter!.isCompleted) {
      appLog.warning('No active authentication flow to handle callback', name: _logName);
      return;
    }

    try {
      // Extract the authorization code from the URL
      final code = uri.queryParameters['code'];
      final error = uri.queryParameters['error'];

      if (error != null) {
        appLog.error('Authentication error: $error', name: _logName);
        _authCompleter!.complete(null);
        return;
      }

      if (code == null) {
        appLog.error('No authorization code in callback', name: _logName);
        _authCompleter!.complete(null);
        return;
      }

      appLog.debug('Exchanging code for token', name: _logName);

      // Exchange the code for a token
      final user = await _exchangeCodeForToken(code);

      // Complete the authentication flow
      _authCompleter!.complete(user);
    } catch (e) {
      appLog.error('Error handling authentication callback', name: _logName, error: e);
      if (_authCompleter != null && !_authCompleter!.isCompleted) {
        _authCompleter!.complete(null);
      }
    }
  }

  /// Exchange the authorization code for a token
  Future<User?> _exchangeCodeForToken(String code) async {
    try {
      if (_supabaseUrl.isEmpty || _supabaseKey.isEmpty) {
        appLog.error('Missing Supabase configuration', name: _logName);
        return null;
      }

      // Let Supabase handle the auth flow completion
      await _supabaseClient.auth.getSessionFromUrl(Uri.parse('$_redirectUri?code=$code'));

      // Return the authenticated user
      final user = _supabaseClient.auth.currentUser;
      return user;
    } catch (e) {
      appLog.error('Error exchanging code for token', name: _logName, error: e);
      return null;
    }
  }

  /// Build the Google OAuth authorization URL
  String _buildGoogleAuthUrl() {
    // Build the authorization URL with PKCE
    return '$_supabaseUrl/auth/v1/authorize'
        '?provider=google'
        '&redirect_to=$_redirectUri'
        '&response_type=code'
        '&code_challenge=$_codeChallenge'
        '&code_challenge_method=S256'
        '&prompt=select_account'
        '&scope=email profile'
        '&apikey=$_supabaseKey';
  }

  /// Build the Microsoft OAuth authorization URL
  String _buildMicrosoftAuthUrl() {
    // Build the authorization URL with PKCE
    return '$_supabaseUrl/auth/v1/authorize'
        '?provider=azure'
        '&redirect_to=$_redirectUri'
        '&response_type=code'
        '&code_challenge=$_codeChallenge'
        '&code_challenge_method=S256'
        '&prompt=select_account'
        '&scope=email profile User.Read offline_access'
        '&apikey=$_supabaseKey';
  }

  /// Generate a PKCE code challenge and verifier
  void _generatePkceCodeChallenge() {
    // Generate a random string for the code verifier
    final random = Random.secure();
    final List<int> values = List<int>.generate(32, (i) => random.nextInt(256));
    _codeVerifier =
        base64UrlEncode(values).replaceAll('+', '-').replaceAll('/', '_').replaceAll('=', '');

    // Generate the code challenge from the code verifier
    final List<int> bytes = utf8.encode(_codeVerifier);
    final Digest digest = sha256.convert(bytes);
    _codeChallenge =
        base64UrlEncode(digest.bytes).replaceAll('+', '-').replaceAll('/', '_').replaceAll('=', '');

    appLog.debug('Generated PKCE code verifier and challenge', name: _logName);
  }

  /// Reset the authentication flow
  void _resetAuthFlow() {
    if (_authCompleter != null && !_authCompleter!.isCompleted) {
      _authCompleter!.complete(null);
    }
    _authCompleter = null;
  }
}
