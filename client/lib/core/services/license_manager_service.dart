import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:http/http.dart' as http;
import 'package:promz/core/models/license_model.dart';
import 'package:promz/core/models/supabase_auth_state.dart';
import 'package:promz/core/providers/service_providers.dart';
import 'package:promz/core/services/secure_storage_service.dart';
import 'package:promz/core/services/supabase_service.dart';
import 'package:promz_common/config/api_config.dart';
import 'package:promz_common/promz_common.dart';

/// LicenseManagerService: Centralized License Management System
///
/// This service provides a single source of truth for all license-related operations
/// and state management in the application. It follows the singleton pattern for
/// consistent access across the application.
///
/// Key Responsibilities:
///   - Manage the current license state
///   - Automatically create free licenses for authenticated users
///   - Handle license validation and expiration
///   - Provide license upgrade paths (free to pro trial)
///   - Track license usage and prevent abuse
///
/// Usage:
///   - Call LicenseManagerService.initialize() during app startup
///   - Access the instance using LicenseManagerService()
///   - Listen to licenseStream for reactive updates to license state
class LicenseManagerService extends ChangeNotifier {
  static const _logName = 'LicenseManagerService';

  // Add cache-related fields
  DateTime? _lastLicenseCheckTime;
  Map<String, dynamic>? _cachedLicenseStatus;
  static const Duration _cacheDuration = Duration(minutes: 30); // 30 minutes for production
  static const Duration _debugCacheDuration = Duration(minutes: 5); // 5 minutes for debug mode

  // Singleton implementation
  static LicenseManagerService? _instance;

  /// Initialize the singleton instance
  static void initialize() {
    appLog.debug('Initializing LicenseManagerService', name: _logName);
    if (_instance != null) {
      appLog.debug('LicenseManagerService already initialized', name: _logName);
      return;
    }

    _instance = LicenseManagerService._();
    appLog.debug('LicenseManagerService initialized successfully', name: _logName);
  }

  /// Reset the singleton instance (for testing)
  @visibleForTesting
  static void reset() {
    _instance = null;
    appLog.debug('LicenseManagerService reset', name: _logName);
  }

  /// Factory constructor to access the singleton instance
  factory LicenseManagerService() {
    if (_instance == null) {
      throw StateError('LicenseManagerService not initialized. Call initialize() first.');
    }
    return _instance!;
  }

  // Private constructor
  LicenseManagerService._() {
    _supabaseService = SupabaseService();
    _initializeListeners();
  }

  // Dependencies
  late final SupabaseService _supabaseService;

  // License state
  UserLicense? _currentLicense;
  UserLicense? get currentLicense => _currentLicense;
  String? _apiKey;
  String? get apiKey => _apiKey;

  // Stream for license changes
  final _licenseController = StreamController<UserLicense?>.broadcast();
  Stream<UserLicense?> get licenseStream => _licenseController.stream;

  /// Initialize listeners for auth state changes
  void _initializeListeners() {
    appLog.debug('Setting up auth state listeners', name: _logName);

    // Listen for auth state changes
    _supabaseService.authStateStream.listen((authState) {
      _handleAuthStateChange(authState);
    });
  }

  /// Handle authentication state changes
  Future<void> _handleAuthStateChange(SupabaseAuthState? authState) async {
    appLog.debug('Auth state changed: ${authState?.isAuthenticated}', name: _logName);

    if (authState?.isAuthenticated == true) {
      // User authenticated, refresh license state asynchronously
      // Don't await this to prevent UI blocking
      _refreshLicenseState().catchError((error) {
        appLog.error('Error refreshing license state', name: _logName, error: error);
      });
    } else {
      // User logged out, clear license state
      _updateLicense(null);
      _apiKey = null;
    }
  }

  /// Refresh the current license state  // Track the last refresh time to minimize redundant API calls during startup
  static DateTime? _lastRefreshTime;
  static const Duration _minRefreshInterval = Duration(seconds: 5);

  Future<void> _refreshLicenseState() async {
    // Add a guard to prevent recursive calls
    if (_isProcessingLicense) {
      appLog.warning('License refresh already in progress, skipping duplicate call',
          name: _logName);
      return;
    }

    // Check if we've refreshed recently (within the minimum interval)
    final now = DateTime.now();
    if (_lastRefreshTime != null &&
        now.difference(_lastRefreshTime!).inSeconds < _minRefreshInterval.inSeconds) {
      appLog.debug(
          'License was refreshed ${now.difference(_lastRefreshTime!).inSeconds}s ago, skipping',
          name: _logName);
      return;
    }

    _lastRefreshTime = now;
    _isProcessingLicense = true;

    try {
      appLog.debug('Refreshing license state', name: _logName);

      // Use the enhanced license status check endpoint directly
      final licenseStatus = await checkLicenseStatusDirect();

      if (licenseStatus['has_license'] == true) {
        // User has a valid license
        appLog.debug('Valid license found: ${licenseStatus['license_type']}', name: _logName);

        // Create a UserLicense object from the response
        final license = UserLicense(
          hasLicense: true,
          licenseType: _getLicenseTypeFromString(licenseStatus['license_type']),
          expiresAt: licenseStatus['expiry_date'] != null
              ? DateTime.parse(licenseStatus['expiry_date'])
              : null,
        );

        _updateLicense(license);
      } else {
        // No valid license found, create a free license
        appLog.debug('No valid license found: ${licenseStatus['reason']}', name: _logName);

        // Auto-create free license if none exists
        final licenseData = await getOrCreateLicense('free');
        if (licenseData != null) {
          _processLicenseData(licenseData);
        } else {
          appLog.error('Failed to create free license', name: _logName);
          _updateLicense(null);
        }
      }
    } catch (e) {
      appLog.error('Error refreshing license state', name: _logName, error: e);
      // Don't update license state on error to avoid losing existing license
    } finally {
      _isProcessingLicense = false;
    }
  }

  /// Process license data from the database
  void _processLicenseData(Map<String, dynamic> licenseData) {
    try {
      // Extract license details
      final licenseType = _getLicenseTypeFromString(licenseData['license_type']);
      final expiresAt = DateTime.parse(licenseData['expiry_date']);
      final apiKey = licenseData['api_key'] as String?;

      // Check if license is valid
      final isFree = licenseData['license_type']?.toLowerCase() == 'free';
      final isValid = expiresAt.isAfter(DateTime.now()) || isFree;

      appLog.debug(
          'Processing license - type: ${licenseData['license_type']}, '
          'isFree: $isFree, expiresAt: $expiresAt, isValid: $isValid',
          name: _logName);

      // Update the current license state
      _updateLicense(UserLicense(
        hasLicense: isValid,
        licenseType: isValid ? licenseType : LicenseType.expired,
        expiresAt: expiresAt,
      ));

      // Store API key if available, but only if it's different from the current one
      // This prevents an infinite loop of storing the same key repeatedly
      if (apiKey != null && apiKey != _apiKey) {
        _apiKey = apiKey;
        // Store the API key without awaiting to prevent blocking
        _supabaseService.storeApiKey(apiKey);
      }
    } catch (e, stack) {
      appLog.error('Error processing license data', name: _logName, error: e, stackTrace: stack);
    }
  }

  /// Process license data and return a UserLicense object
  /// Similar to _processLicenseData but returns the license object
  UserLicense? _processLicenseDataAndGetLicense(Map<String, dynamic> licenseData) {
    try {
      // Extract license details
      final licenseType = _getLicenseTypeFromString(licenseData['license_type']);
      final expiresAt = licenseData['expiry_date'] != null
          ? DateTime.parse(licenseData['expiry_date'])
          : DateTime.now().add(const Duration(days: 365));
      final apiKey = licenseData['api_key'] as String?;

      // Check if license is valid
      final isFree = licenseData['license_type']?.toLowerCase() == 'free';
      final isValid = expiresAt.isAfter(DateTime.now()) || isFree;

      appLog.debug(
          'Processing license and returning - type: ${licenseData['license_type']}, '
          'isFree: $isFree, expiresAt: $expiresAt, isValid: $isValid',
          name: _logName);

      // Create license object
      final license = UserLicense(
        hasLicense: isValid,
        licenseType: isValid ? licenseType : LicenseType.expired,
        expiresAt: expiresAt,
      );

      // Update the current license state
      _updateLicense(license);

      // Store API key if available
      if (apiKey != null && apiKey != _apiKey) {
        _apiKey = apiKey;
        // Store the API key without awaiting to prevent blocking
        _supabaseService.storeApiKey(apiKey);
      }

      return license;
    } catch (e, stack) {
      appLog.error('Error processing license data', name: _logName, error: e, stackTrace: stack);
      return null;
    }
  }

  /// Update license and notify listeners
  void _updateLicense(UserLicense? license) {
    _currentLicense = license;
    _licenseController.add(license);
    notifyListeners();

    // Update license state through ClientContextService instead of directly accessing UserProfileService
    _updateLicenseStateViaClientContext(license);

    appLog.debug('License state updated: ${license?.licenseType}', name: _logName);
  }

  /// Update application license state via ClientContextService
  void _updateLicenseStateViaClientContext(UserLicense? license) {
    try {
      // Get ClientContextService from provider if available
      final providerContainer = ProviderContainer();
      try {
        if (providerContainer.exists(clientContextServiceProvider)) {
          final clientContextAsyncValue = providerContainer.read(clientContextServiceProvider);
          appLog.debug('Propagating license update via ClientContextService', name: _logName);
          clientContextAsyncValue.whenData((serviceInstance) {
            appLog.debug('ClientContextService available, updating license state.', name: _logName);
            serviceInstance.updateLicenseState(license);
          });
        } else {
          appLog.debug('ClientContextService provider not available yet, skipping license update',
              name: _logName);
        }
      } finally {
        // Always dispose the container to prevent memory leaks
        providerContainer.dispose();
      }
    } catch (e, stack) {
      // This is not critical, so just log and continue
      appLog.error('Error updating license state via ClientContextService',
          name: _logName, error: e, stackTrace: stack);
    }
  }

  /// Helper function to convert license type string to enum
  LicenseType _getLicenseTypeFromString(String? type) {
    switch (type?.toLowerCase()) {
      case 'free':
      case 'basic':
        return LicenseType.free;
      case 'pro':
        return LicenseType.pro;
      case 'trial':
        return LicenseType.trial;
      case 'enterprise':
        return LicenseType.enterprise;
      default:
        return LicenseType.none;
    }
  }

  /// Check if the current license is valid
  bool isLicenseValid() {
    if (_currentLicense == null) return false;

    // Free licenses are always valid
    if (_currentLicense!.licenseType == LicenseType.free) return true;

    // Check expiration for other license types
    // If expiresAt is null, consider the license valid (no expiration)
    // Otherwise, check if the license is still valid based on the expiration date
    return _currentLicense!.hasLicense &&
        (_currentLicense!.expiresAt == null || _currentLicense!.expiresAt!.isAfter(DateTime.now()));
  }

  /// Ensure the user has a free license (creates one if none exists)
  /// Returns true if the user has a valid license (existing or newly created), false otherwise
  Future<bool> ensureUserHasFreeLicense() async {
    // Add a guard to prevent recursive calls
    if (_isProcessingLicense) {
      appLog.warning('License creation already in progress, skipping duplicate call',
          name: _logName);
      return false;
    }

    _isProcessingLicense = true;
    bool result = false;

    try {
      appLog.debug('Ensuring user has a free license', name: _logName);

      // First check if the user already has a valid license using the enhanced endpoint
      final licenseStatus = await checkLicenseStatusDirect();

      if (licenseStatus['has_license'] == true) {
        // User already has a valid license
        final existingLicenseType = licenseStatus['license_type']?.toLowerCase();

        // Check if it's already a valid free license
        if (existingLicenseType == 'free' && licenseStatus['is_active'] == true) {
          appLog.debug('User already has a valid free license', name: _logName);

          // Create a UserLicense object from the response
          final license = UserLicense(
            hasLicense: true,
            licenseType: _getLicenseTypeFromString(existingLicenseType),
            expiresAt: licenseStatus['expiry_date'] != null
                ? DateTime.parse(licenseStatus['expiry_date'])
                : DateTime.now().add(const Duration(days: 36500)),
          );

          _updateLicense(license);
          result = true; // Changed from false to true - user has a valid license
        } else {
          // User has a license but it's not a free license or not active
          // We'll keep their existing license
          appLog.debug('User has a non-free license: $existingLicenseType', name: _logName);
          result = false;
        }
      } else {
        // User doesn't have a valid license, create a free one
        appLog.debug('No valid license found, creating free license', name: _logName);

        final licenseData = await getOrCreateLicense('free');
        if (licenseData != null) {
          _processLicenseData(licenseData);
          result = true; // New license created
        } else {
          appLog.error('Failed to create free license', name: _logName);
          result = false;
        }
      }
    } catch (e) {
      appLog.error('Error ensuring free license', name: _logName, error: e);
      result = false;
    } finally {
      _isProcessingLicense = false;
    }

    return result;
  }

  /// Generate a pro trial license for the current user
  Future<bool> generateProTrialLicense() async {
    // Add a guard to prevent recursive calls
    if (_isProcessingLicense) {
      appLog.warning('License creation already in progress, skipping duplicate call',
          name: _logName);
      return false;
    }

    _isProcessingLicense = true;
    bool result = false;

    try {
      appLog.debug('Generating pro trial license', name: _logName);

      // Create a trial license using the enhanced endpoint
      final licenseData = await getOrCreateLicense('trial');

      if (licenseData != null && licenseData['api_key'] != null) {
        // Process the new license data
        _processLicenseData(licenseData);

        appLog.debug('Created pro trial license for user', name: _logName);
        result = true;
      } else {
        appLog.warning('Failed to create pro trial license', name: _logName);
        result = false;
      }
    } catch (e) {
      appLog.error('Error generating pro trial license', name: _logName, error: e);
      result = false;
    } finally {
      _isProcessingLicense = false;
    }

    return result;
  }

  /// Check if the current user has an active pro trial license
  Future<bool> hasActiveProTrial() async {
    try {
      final licenseStatus = await checkLicenseStatusDirect();

      if (licenseStatus['has_license'] != true) {
        return false;
      }

      final licenseType = licenseStatus['license_type'] as String?;
      final isActive = licenseStatus['is_active'] as bool?;
      final expiryDate = licenseStatus['expiry_date'] != null
          ? DateTime.parse(licenseStatus['expiry_date'] as String)
          : null;

      return licenseType == 'trial' &&
          isActive == true &&
          expiryDate != null &&
          expiryDate.isAfter(DateTime.now());
    } catch (e, stack) {
      appLog.error('Error checking pro trial status', name: _logName, error: e, stackTrace: stack);
      return false;
    }
  }

  /// Track previously used accounts on this device to prevent multiple trials
  Future<void> trackAccountUsage(String userId, String email) async {
    try {
      // Implementation would store this information in secure local storage
      appLog.debug('Tracking account usage for $email', name: _logName);
    } catch (e, stack) {
      appLog.error('Error tracking account usage', name: _logName, error: e, stackTrace: stack);
    }
  }

  /// Initialize the license service
  /// Retrieves the stored API key or generates a new one if needed
  Future<String?> initializeLicense() async {
    try {
      appLog.debug('Initializing license', name: _logName);

      // Check if user is authenticated
      if (!_supabaseService.isAuthenticated) {
        appLog.debug('User not authenticated, skipping license initialization', name: _logName);
        return null;
      }

      // Try to get the stored API key
      _apiKey = await _supabaseService.getStoredApiKey();

      if (_apiKey != null) {
        appLog.debug('Retrieved stored API key', name: _logName);

        // Refresh license state to ensure it's up to date
        await _refreshLicenseState();

        return _apiKey;
      }

      // If no API key is stored, get or create a license
      final licenseData = await getOrCreateLicense('free');

      if (licenseData != null && licenseData['api_key'] != null) {
        // Process the license data
        _processLicenseData(licenseData);

        appLog.debug('Created new license and stored API key', name: _logName);
        return _apiKey;
      }

      appLog.warning('Failed to initialize license', name: _logName);
      return null;
    } catch (e, stack) {
      appLog.error('Error initializing license', name: _logName, error: e, stackTrace: stack);
      return null;
    }
  }

  /// Get user license directly
  /// Returns license data or null if no license is found
  ///
  /// If forceFresh is false (default), will return cached license if available
  /// If forceFresh is true, will always fetch fresh data from the server
  Future<UserLicense?> getUserLicense({bool forceFresh = false}) async {
    try {
      appLog.debug('Getting user license directly', name: _logName);

      // Return current license if we have it and not forcing fresh data
      if (!forceFresh && _currentLicense != null) {
        appLog.debug('Returning cached license', name: _logName);
        return _currentLicense;
      }

      // Check if we have a valid user session first
      if (_supabaseService.currentUser == null) {
        appLog.warning('Cannot get user license: No user is logged in', name: _logName);
        return null;
      }

      // Get license status first
      final licenseStatus = await checkLicenseStatusDirect();

      if (licenseStatus['has_license'] == true) {
        // Store the API key if available
        String? apiKey = licenseStatus['api_key'] as String?;
        if (apiKey != null) {
          SecureStorageService.saveApiKey(apiKey);
        }

        // Create a UserLicense object from the response
        final licenseType =
            _getLicenseTypeFromString(licenseStatus['license_type'] as String? ?? 'none');
        final expiresAt = licenseStatus['expiry_date'] != null
            ? DateTime.parse(licenseStatus['expiry_date'] as String)
            : null;

        final license = UserLicense(
          hasLicense: licenseStatus['has_license'] == true,
          licenseType: licenseType,
          expiresAt: expiresAt,
        );

        // Update the current license
        _updateLicense(license);

        return license;
      } else {
        // No license found, try to create one
        final licenseData = await getOrCreateLicense('free');
        if (licenseData != null) {
          // Process the license data to create a UserLicense object
          return _processLicenseDataAndGetLicense(licenseData);
        }
        return null;
      }
    } catch (e) {
      appLog.error('Error getting user license', name: _logName, error: e);
      return null;
    }
  }

  /// Dispose resources
  @override
  void dispose() {
    _licenseController.close();
    super.dispose();
  }

  /// Flag to prevent recursive calls to license creation
  bool _isProcessingLicense = false;

  // Add request deduplication field
  Completer<Map<String, dynamic>>? _pendingLicenseStatusRequest;

  /// Invalidate the license cache to force the next check to hit the server
  void invalidateLicenseCache() {
    _cachedLicenseStatus = null;
    _lastLicenseCheckTime = null;
    appLog.debug('License cache invalidated', name: _logName);
  }

  /// Force a refresh of the license status from the server
  Future<Map<String, dynamic>> refreshLicenseStatus() async {
    // Force cache invalidation
    invalidateLicenseCache();

    // Get fresh data
    return await checkLicenseStatusDirect();
  }

  /// Make an API request to the license endpoints
  /// Returns the parsed JSON response or null if the request failed
  Future<Map<String, dynamic>?> _makeLicenseApiRequest(
    String endpoint,
    Map<String, dynamic> params, {
    String? errorMessage,
  }) async {
    try {
      appLog.debug('Making API request to $endpoint', name: _logName);

      http.Response response;
      final Uri uri = Uri.parse(endpoint).replace(queryParameters: params);
      response = await http.get(
        uri,
        headers: {'Content-Type': 'application/json'},
      ).timeout(
        const Duration(seconds: 5),
        onTimeout: () {
          appLog.warning('GET request to $endpoint timed out after 5 seconds', name: _logName);
          return http.Response(
              jsonEncode({
                'error': 'timeout',
                'error_code': 'REQUEST_TIMEOUT',
              }),
              408);
        },
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body) as Map<String, dynamic>;
        appLog.debug('API request to $endpoint successful', name: _logName);
        return responseData;
      } else {
        appLog.error(
          'GET request to $endpoint failed with status ${response.statusCode}: ${response.body}',
          name: _logName,
        );
        return null;
      }
    } catch (e) {
      appLog.error(errorMessage ?? 'API request failed', name: _logName, error: e);
      return null;
    }
  }

  /// Verify API key directly with the server
  /// Returns a map with verification result
  Future<Map<String, dynamic>> verifyApiKey(String apiKey) async {
    try {
      // For development, assume keys are valid to prevent blocking the user experience
      if (kDebugMode) {
        appLog.debug('Debug mode: Assuming API key is valid without verification', name: _logName);
        return {
          'valid': true,
          'license_type': 'free',
          'is_free': true,
          'days_remaining': 36500,
        };
      }

      appLog.debug('Verifying API key via enhanced API endpoint', name: _logName);

      // Check if we have a valid user session first
      if (_supabaseService.currentUser == null) {
        appLog.warning('Cannot verify API key: No user is logged in', name: _logName);
        return {
          'valid': false,
          'reason': 'no_user_session',
          'error_code': 'NO_USER_SESSION',
        };
      }

      final result = await _makeLicenseApiRequest(
        ApiConfig.licenseVerifyEndpoint,
        {'api_key': apiKey},
        errorMessage: 'Error verifying API key',
      );

      if (result != null) {
        return result;
      }

      return {
        'valid': false,
        'reason': 'server_error',
        'error_code': 'SERVER_ERROR',
      };
    } catch (e) {
      appLog.error('Error verifying API key', name: _logName, error: e);
      return {
        'valid': false,
        'reason': 'exception',
        'error_code': 'VERIFICATION_EXCEPTION',
        'error_details': e.toString(),
      };
    }
  }

  /// Check license status directly with the server
  /// Returns a map with license status information
  ///
  /// If forceFresh is false (default), will return cached data if available and not expired
  /// If forceFresh is true, will always fetch fresh data from the server
  Future<Map<String, dynamic>> checkLicenseStatusDirect({bool forceFresh = false}) async {
    try {
      appLog.debug('Checking license status via enhanced API endpoint', name: _logName);

      // Check if we have a valid user session first
      if (_supabaseService.currentUser == null) {
        appLog.warning('Cannot check license status: No user is logged in', name: _logName);
        return {
          'has_license': false,
          'reason': 'no_user_session',
          'error_code': 'NO_USER_SESSION',
        };
      }

      // Check if we have a cached license status and it's still valid
      final now = DateTime.now();
      const cacheDuration = kDebugMode ? _debugCacheDuration : _cacheDuration;
      if (!forceFresh &&
          _cachedLicenseStatus != null &&
          _lastLicenseCheckTime != null &&
          now.difference(_lastLicenseCheckTime!).inSeconds < cacheDuration.inSeconds) {
        appLog.debug(
            'Using cached license status from ${now.difference(_lastLicenseCheckTime!).inSeconds}s ago',
            name: _logName);
        return _cachedLicenseStatus!;
      }

      // Check if there's a pending request
      if (_pendingLicenseStatusRequest != null) {
        appLog.debug('License status request already in progress, waiting for result',
            name: _logName);
        return await _pendingLicenseStatusRequest!.future;
      }

      // Create a new request
      _pendingLicenseStatusRequest = Completer<Map<String, dynamic>>();

      final result = await _makeLicenseApiRequest(
        ApiConfig.licenseStatusEndpoint,
        {'user_id': _supabaseService.currentUser!.id},
        errorMessage: 'Error checking license status',
      );

      if (result != null) {
        // Cache the result
        _cachedLicenseStatus = result;
        _lastLicenseCheckTime = now;
        _pendingLicenseStatusRequest!.complete(result);
        _pendingLicenseStatusRequest = null;
        return result;
      }

      final errorResult = {
        'has_license': false,
        'reason': 'server_error',
        'error_code': 'SERVER_ERROR',
      };
      _pendingLicenseStatusRequest!.complete(errorResult);
      _pendingLicenseStatusRequest = null;
      return errorResult;
    } catch (e) {
      appLog.error('Error checking license status', name: _logName, error: e);
      final errorResult = {
        'has_license': false,
        'reason': 'exception',
        'error_code': 'STATUS_CHECK_EXCEPTION',
        'error_details': e.toString(),
      };

      if (_pendingLicenseStatusRequest != null) {
        _pendingLicenseStatusRequest!.complete(errorResult);
        _pendingLicenseStatusRequest = null;
      }

      return errorResult;
    }
  }

  /// Get or create a free license key directly with the server
  /// Returns a map containing license information and API key
  Future<Map<String, dynamic>?> getOrCreateLicense(String licenseType) async {
    try {
      // Check if we have a valid user session first
      if (_supabaseService.currentUser == null) {
        appLog.warning('Cannot get/create license: No user is logged in', name: _logName);
        return null;
      }

      appLog.debug('Getting or creating license key via enhanced API endpoint', name: _logName);

      Map<String, dynamic> params = {
        'user_id': _supabaseService.currentUser!.id,
      };

      return await _makeLicenseApiRequest(
        licenseType == 'free'
            ? ApiConfig.licenseGetOrCreateFreeEndpoint
            : ApiConfig.licenseGetOrCreateTrialEndpoint,
        params,
        errorMessage: 'Error creating $licenseType license',
      );
    } catch (e) {
      appLog.error('Error creating license', name: _logName, error: e);
      return null;
    }
  }
}
