import 'package:flutter/material.dart';
import 'package:promz/core/models/license_model.dart';

/// A widget that displays a badge indicating the user's license type
class LicenseBadge extends StatelessWidget {
  /// The license type to display
  final LicenseType licenseType;

  /// The size of the badge
  final double size;

  /// Whether the user has a license
  final bool hasLicense;

  /// Creates a license badge
  const LicenseBadge({
    Key? key,
    required this.licenseType,
    required this.hasLicense,
    this.size = 22.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // If the user doesn't have a license, don't show a badge
    if (!hasLicense) {
      return const SizedBox.shrink();
    }

    // Get the badge configuration based on license type
    final badgeConfig = _getLicenseBadgeConfig(licenseType);

    // Using Material widget to ensure tooltip works properly
    return Material(
      type: MaterialType.transparency,
      child: Tooltip(
        message: badgeConfig.tooltip,
        waitDuration: const Duration(milliseconds: 100), // Much shorter wait time
        showDuration: const Duration(seconds: 3), // Longer display time
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.black87,
          borderRadius: BorderRadius.circular(4),
        ),
        textStyle: const TextStyle(color: Colors.white, fontSize: 14),
        preferBelow: true,
        verticalOffset: 20,
        child: Container(
          width: size,
          height: size,
          decoration: BoxDecoration(
            color: badgeConfig.color,
            shape: BoxShape.circle,
            border: Border.all(color: Colors.white, width: 2.0),
            // Add a subtle shadow for better visibility
            boxShadow: [
              BoxShadow(
                // ignore: deprecated_member_use
                color: Colors.black.withOpacity(0.2),
                blurRadius: 2,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Icon(
            badgeConfig.icon,
            size: size * 0.65, // Slightly larger icon relative to container
            color: Colors.white,
          ),
        ),
      ),
    );
  }

  /// Get the badge configuration for a license type
  _BadgeConfig _getLicenseBadgeConfig(LicenseType type) {
    switch (type) {
      case LicenseType.free:
        return _BadgeConfig(
          icon: Icons.card_giftcard,
          color: Colors.blue,
          tooltip: 'Free License',
        );
      case LicenseType.trial:
        return _BadgeConfig(
          icon: Icons.hourglass_empty,
          color: Colors.orange,
          tooltip: 'Trial License',
        );
      case LicenseType.pro:
        return _BadgeConfig(
          icon: Icons.star,
          color: Colors.purple,
          tooltip: 'Pro License',
        );
      case LicenseType.enterprise:
        return _BadgeConfig(
          icon: Icons.business,
          color: Colors.teal,
          tooltip: 'Enterprise License',
        );
      default:
        return _BadgeConfig(
          icon: Icons.help_outline,
          color: Colors.grey,
          tooltip: 'No License',
        );
    }
  }
}

/// Configuration for a license badge
class _BadgeConfig {
  final IconData icon;
  final Color color;
  final String tooltip;

  _BadgeConfig({
    required this.icon,
    required this.color,
    required this.tooltip,
  });
}
