import 'package:flutter/material.dart';
import 'package:promz/core/models/license_model.dart';
import 'package:promz/features/account/widgets/user_avatar.dart';

/// A widget that displays a user avatar with a license badge
///
/// This widget combines the UserAvatar with a license badge indicator
/// to provide a consistent representation of the user's profile and
/// license status throughout the app.
class ProfileAvatarWithBadge extends StatelessWidget {
  /// The user's email address
  final String email;

  /// The user's full name (optional)
  final String? fullName;

  /// The user's photo URL (optional)
  final String? photoUrl;

  /// Whether the user has a license
  final bool hasLicense;

  /// The type of license the user has
  final LicenseType licenseType;

  /// The size of the avatar (badge size is calculated proportionally)
  final double size;

  /// Creates a profile avatar with license badge
  const ProfileAvatarWithBadge({
    Key? key,
    required this.email,
    this.fullName,
    this.photoUrl,
    required this.hasLicense,
    this.licenseType = LicenseType.none,
    this.size = 40.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Calculate badge size based on avatar size
    final badgeSize = size * 0.5;

    // Get badge configuration based on license type
    final badgeConfig = _getLicenseBadgeConfig(licenseType);

    // Get avatar border color based on license type
    final borderColor = _getBorderColor(licenseType, hasLicense);
    final borderWidth = hasLicense ? 2.0 : 1.0;

    return Stack(
      children: [
        // User avatar with appropriate border color
        UserAvatar(
          email: email,
          fullName: fullName,
          photoUrl: photoUrl,
          size: size,
          borderWidth: borderWidth,
          borderColor: borderColor,
        ),

        // License badge (if user has a license)
        if (hasLicense)
          Positioned(
            right: -2,
            bottom: -2,
            child: Container(
              width: badgeSize,
              height: badgeSize,
              decoration: BoxDecoration(
                color: badgeConfig.color,
                shape: BoxShape.circle,
                border: Border.all(color: Colors.white, width: 2.0),
                boxShadow: [
                  BoxShadow(
                    // ignore: deprecated_member_use
                    color: Colors.black.withOpacity(0.2),
                    blurRadius: 2,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Icon(
                badgeConfig.icon,
                size: badgeSize * 0.65,
                color: Colors.white,
              ),
            ),
          ),
      ],
    );
  }

  /// Get the border color based on license type
  Color _getBorderColor(LicenseType type, bool hasLicense) {
    if (!hasLicense) return Colors.grey;

    switch (type) {
      case LicenseType.free:
        return Colors.blue;
      case LicenseType.trial:
        return Colors.orange;
      case LicenseType.pro:
        return Colors.purple;
      case LicenseType.enterprise:
        return Colors.teal;
      default:
        return Colors.grey;
    }
  }

  /// Get the badge configuration for a license type
  _BadgeConfig _getLicenseBadgeConfig(LicenseType type) {
    switch (type) {
      case LicenseType.free:
        return _BadgeConfig(
          icon: Icons.card_giftcard,
          color: Colors.blue,
          label: 'Free License',
        );
      case LicenseType.trial:
        return _BadgeConfig(
          icon: Icons.hourglass_empty,
          color: Colors.orange,
          label: 'Trial License',
        );
      case LicenseType.pro:
        return _BadgeConfig(
          icon: Icons.star,
          color: Colors.purple,
          label: 'Pro License',
        );
      case LicenseType.enterprise:
        return _BadgeConfig(
          icon: Icons.business,
          color: Colors.teal,
          label: 'Enterprise License',
        );
      default:
        return _BadgeConfig(
          icon: Icons.help_outline,
          color: Colors.grey,
          label: 'No License',
        );
    }
  }

  /// Get a text description of the license type
  String getLicenseTypeLabel() {
    return _getLicenseBadgeConfig(licenseType).label;
  }
}

/// Configuration for a license badge
class _BadgeConfig {
  final IconData icon;
  final Color color;
  final String label;

  _BadgeConfig({
    required this.icon,
    required this.color,
    required this.label,
  });
}
