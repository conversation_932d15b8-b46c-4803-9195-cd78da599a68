import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:promz/core/models/license_model.dart';
import 'package:promz/core/providers/license_manager_provider.dart';
import 'package:promz/core/providers/service_providers.dart';
import 'package:promz/core/services/license_manager_service.dart';
import 'package:promz/core/utils/platform_utils.dart';
import 'package:promz/core/widgets/content_processing_status_indicator.dart';
import 'package:promz/core/widgets/profile_avatar_with_badge.dart';
import 'package:promz/features/account/views/account_view.dart';
import 'package:promz/features/home/<USER>/home_viewmodel.dart';
import 'package:promz_common/promz_common.dart';

/// A custom app bar widget for the Promz application.
///
/// This widget encapsulates the app bar logic and UI, providing consistent
/// user profile display, authentication state handling, and navigation.
///
/// It supports displaying the user profile from either a cached source or
/// directly from the authentication state, with appropriate loading indicators.
class PromzAppBar extends ConsumerStatefulWidget implements PreferredSizeWidget {
  /// The title to display in the app bar
  final Widget title;

  /// Optional leading widget override
  final Widget? leading;

  /// Optional actions to display after the profile button
  final List<Widget>? actions;

  /// Whether to show a back button
  final bool showBackButton;

  /// Callback when the back button is pressed
  final VoidCallback? onBackPressed;

  const PromzAppBar({
    Key? key,
    required this.title,
    this.leading,
    this.actions,
    this.showBackButton = false,
    this.onBackPressed,
  }) : super(key: key);

  @override
  ConsumerState<PromzAppBar> createState() => _PromzAppBarState();

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

class _PromzAppBarState extends ConsumerState<PromzAppBar> {
  static const String _logName = 'PromzAppBar';

  @override
  void initState() {
    super.initState();

    // Schedule this to run after the first frame is rendered
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Get the license manager and add a listener
      final licenseManager = ref.read(licenseManagerProvider);
      licenseManager.addListener(_onLicenseChanged);
    });
  }

  @override
  void dispose() {
    // Safety check - store reference to licenseManager before potentially losing ref
    LicenseManagerService? licenseManager;
    try {
      // Only attempt to access ref if the widget is still mounted
      if (mounted) {
        licenseManager = ref.read(licenseManagerProvider);
      }
    } catch (e) {
      appLog.debug('Could not access ref in dispose: $e', name: _logName);
    }

    // Only remove the listener if we successfully got the licenseManager
    if (licenseManager != null) {
      licenseManager.removeListener(_onLicenseChanged);
    }

    super.dispose();
  }

  void _onLicenseChanged() {
    appLog.debug('License changed, rebuilding app bar', name: _logName);
    if (mounted) {
      // Force a rebuild of the widget
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return PlatformUtils.isApplePlatform
        ? CupertinoNavigationBar(
            middle: widget.title,
            leading: _buildLeadingWidget(context),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Status indicator for shared content processing
                const ContentProcessingStatusIndicator(),
                const SizedBox(width: 8),
                // User profile indicator using cached data first
                _buildProfileButton(context, ref),
                // Add any additional actions
                if (widget.actions != null) ...widget.actions!,
              ],
            ),
          )
        : AppBar(
            title: widget.title,
            leading: _buildLeadingWidget(context),
            actions: [
              // Status indicator for shared content processing
              const ContentProcessingStatusIndicator(),
              const SizedBox(width: 8),
              // User profile indicator using cached data first
              _buildProfileButton(context, ref),
              // Add any additional actions
              if (widget.actions != null) ...widget.actions!,
            ],
          );
  }

  Widget? _buildLeadingWidget(BuildContext context) {
    if (widget.leading != null) {
      return widget.leading;
    }

    if (widget.showBackButton) {
      return PlatformUtils.isApplePlatform
          ? CupertinoButton(
              padding: EdgeInsets.zero,
              onPressed: widget.onBackPressed ?? () => Navigator.of(context).pop(),
              child: const Icon(CupertinoIcons.back),
            )
          : IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: widget.onBackPressed ?? () => Navigator.of(context).pop(),
            );
    }

    return null;
  }

  Widget _buildProfileButton(BuildContext context, WidgetRef ref) {
    try {
      appLog.debug('Building profile button', name: _logName);

      // Watch the client context provider
      final clientContextAsync = ref.watch(clientContextServiceProvider);

      return clientContextAsync.when(
        data: (clientContext) {
          // Get the UserProfileService from the loaded client context
          final userProfileService = clientContext.userProfile;

          // Use ListenableBuilder to rebuild when the profile changes
          return ListenableBuilder(
            listenable: userProfileService,
            builder: (context, _) {
              final profile = userProfileService.profile;

              appLog.debug(
                  'Profile state: isAuthenticated=${profile.isAuthenticated}, hasLicense=${profile.hasLicense}',
                  name: _logName);

              // Authenticated with license
              if (profile.isAuthenticated && profile.hasLicense) {
                appLog.debug('END: Authenticated with license', name: _logName);
                return _buildAuthenticatedProfileButton(
                  context,
                  profile.email ?? '',
                  profile.fullName,
                  profile.avatarUrl,
                  hasLicense: true,
                  licenseType: profile.licenseType,
                );
              }

              // Authenticated but no license
              if (profile.isAuthenticated) {
                appLog.debug('END: Authenticated but no license', name: _logName);
                return _buildAuthenticatedProfileButton(
                  context,
                  profile.email ?? '',
                  profile.fullName,
                  profile.avatarUrl,
                );
              }

              // Not authenticated
              appLog.debug('END: Not authenticated', name: _logName);
              return _buildUnauthenticatedProfileButton(context);
            },
          );
        },
        loading: () => const CircularProgressIndicator(), // Show loading indicator
        error: (error, stackTrace) {
          appLog.error(
            'Error loading client context for profile button: $error',
            error: error,
            stackTrace: stackTrace,
            name: _logName,
          );
          // Optionally return an error indicator or placeholder
          return IconButton(
            icon: const Icon(Icons.error_outline),
            tooltip: 'Error loading profile', // Use hardcoded string
            onPressed: () {},
          );
        },
      );
    } catch (e, st) {
      appLog.error('Unexpected error building profile button: $e',
          name: _logName, error: e, stackTrace: st);
      return IconButton(
        icon: const Icon(Icons.error_outline),
        tooltip: 'Error loading profile', // Use hardcoded string
        onPressed: () {},
      );
    }
  }

  /// Build profile button for unauthenticated state with enhanced visual cue
  Widget _buildUnauthenticatedProfileButton(BuildContext context) {
    return Tooltip(
      message: 'Sign in',
      child: IconButton(
        icon: Stack(
          children: [
            const Icon(Icons.account_circle_outlined),
            Positioned(
              right: 0,
              bottom: 0,
              child: Container(
                padding: const EdgeInsets.all(2),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.error,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(
                  Icons.error_outline,
                  size: 10,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ),
        onPressed: () => _navigateToProfileSetup(context),
      ),
    );
  }

  Widget _buildAuthenticatedProfileButton(
    BuildContext context,
    String email,
    String? fullName,
    String? avatarUrl, {
    bool hasLicense = false,
    LicenseType licenseType = LicenseType.none,
  }) {
    return Material(
      type: MaterialType.transparency,
      child: InkWell(
        borderRadius: BorderRadius.circular(24),
        onTap: () => _navigateToProfileSetup(context),
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              ProfileAvatarWithBadge(
                email: email,
                fullName: fullName,
                photoUrl: avatarUrl,
                hasLicense: hasLicense,
                licenseType: licenseType,
                size: 32.0,
              ),
              const SizedBox(width: 8),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToProfileSetup(BuildContext context) {
    // Get the HomeViewModel to update the navigation index
    final provider = ProviderScope.containerOf(context);
    final homeViewModel = provider.read(homeViewModelProvider);

    // Update the current navigation index to reflect the account tab
    // This is typically the last index in the bottom nav bar
    homeViewModel.navigateToPage(context, 3); // Assuming 3 is the index for the Account tab

    // Use pushReplacement to update the current route with the profile page
    Navigator.of(context).pushReplacement(
      MaterialPageRoute<Widget>(
        builder: (context) => const AccountView(
          initialSection: 'profile',
        ),
        settings: const RouteSettings(name: '/account/profile'),
      ),
    );
  }
}
