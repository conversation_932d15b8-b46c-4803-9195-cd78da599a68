import 'package:flutter/material.dart';

/// A widget that wraps its child content in a styled box with consistent spacing
/// and background color to provide a distinct visual appearance.
///
/// This widget is used throughout the app to maintain a consistent look for
/// various content sections.
class ContentBox extends StatelessWidget {
  /// The widget to be displayed inside the content box.
  final Widget child;

  /// The padding to apply inside the content box.
  /// Defaults to slight padding.
  final EdgeInsetsGeometry padding;

  /// The margin to apply around the content box.
  /// Defaults to EdgeInsets.zero.
  final EdgeInsetsGeometry margin;

  /// The border radius of the content box.
  /// Defaults to BorderRadius.circular(8.0).
  final BorderRadiusGeometry borderRadius;

  /// The background color of the content box.
  /// If null, uses colorScheme.surfaceContainerLow from the current theme.
  final Color? backgroundColor;

  /// The elevation of the content box.
  /// Defaults to 4.0.
  final double elevation;

  /// Creates a ContentBox widget.
  ///
  /// The [child] parameter is required and represents the content to be displayed
  /// inside the styled box.
  const ContentBox({
    Key? key,
    required this.child,
    this.padding = const EdgeInsets.all(12.0),
    this.margin = EdgeInsets.zero,
    this.borderRadius = const BorderRadius.all(Radius.circular(8.0)),
    this.backgroundColor,
    this.elevation = 2.0,
  }) : super(key: key);

  /// A convenience factory that wraps any widget in a ContentBox.
  ///
  /// This makes it easy to apply the ContentBox styling to any widget
  /// without having to explicitly create a ContentBox instance.
  factory ContentBox.wrap(
    Widget child, {
    Key? key,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry margin = EdgeInsets.zero,
    BorderRadiusGeometry borderRadius = const BorderRadius.all(Radius.circular(8.0)),
    Color? backgroundColor,
    double? elevation,
  }) {
    return ContentBox(
      key: key,
      padding: padding ?? const EdgeInsets.all(12.0),
      margin: margin,
      borderRadius: borderRadius,
      backgroundColor: backgroundColor,
      elevation: elevation ?? 4.0,
      child: child,
    );
  }

  factory ContentBox.wrapChildren(Iterable<Widget> children) {
    return ContentBox(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: children.toList(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Container(
      margin: margin,
      decoration: BoxDecoration(
        color: backgroundColor ?? colorScheme.surfaceContainerLow,
        borderRadius: borderRadius,
        boxShadow: elevation > 0
            ? [
                BoxShadow(
                  color: colorScheme.shadow.withAlpha((0.1 * 255).round()),
                  blurRadius: elevation,
                  offset: Offset(0, elevation / 2),
                ),
              ]
            : null,
      ),
      child: Padding(
        padding: padding,
        child: child,
      ),
    );
  }
}
