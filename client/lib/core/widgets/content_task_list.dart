import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:promz/core/models/content_processing_task.dart';
import 'package:promz/core/providers/content_processing_task_provider.dart';
import 'package:promz/core/utils/format_utils.dart';
import 'package:promz/features/navigation/routes.dart';
import 'package:promz_common/promz_common.dart';

/// A widget that displays a list of shared content processing tasks
///
/// This widget is shown when the user taps on the status indicator
/// in the app header. It displays all tasks, both active and completed.
class ContentTaskList extends ConsumerWidget {
  /// Creates a shared content task list
  ///
  /// If [showUpgradeOption] is true, a banner will be shown at the top
  /// of the list with an option to upgrade the license.
  const ContentTaskList({
    Key? key,
    this.showUpgradeOption = false,
  }) : super(key: key);

  /// Whether to show an upgrade option banner
  final bool showUpgradeOption;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final tasks = ref.watch(contentProcessingTasksProvider);

    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildHeader(context, ref),
          Expanded(
            child: tasks.when(
              data: (taskList) {
                if (taskList.isEmpty) {
                  return const Center(
                    child: Text('No processing tasks'),
                  );
                }

                return _buildTaskList(context, taskList);
              },
              loading: () => const Center(
                child: CircularProgressIndicator(),
              ),
              error: (_, __) => const Center(
                child: Text('Failed to load tasks'),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context, WidgetRef ref) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            // ignore: deprecated_member_use
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Processing Tasks',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Row(
                children: [
                  TextButton(
                    onPressed: () => _clearAllTasks(context, ref),
                    child: const Text('Clear All'),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 4),
          const Text(
            'Files being processed are shown here',
            style: TextStyle(
              fontSize: 14,
              color: Colors.black54,
            ),
          ),
          if (showUpgradeOption)
            Padding(
              padding: const EdgeInsets.only(top: 12),
              child: _buildUpgradeBanner(context, ref),
            ),
        ],
      ),
    );
  }

  Widget _buildTaskList(BuildContext context, List<ContentProcessingTask> tasks) {
    // Sort tasks: active first, then by date (newest first)
    final sortedTasks = List<ContentProcessingTask>.from(tasks)
      ..sort((a, b) {
        // First by status (active first)
        final aIsActive = a.status == ContentProcessingTaskStatus.pending ||
            a.status == ContentProcessingTaskStatus.processing;
        final bIsActive = b.status == ContentProcessingTaskStatus.pending ||
            b.status == ContentProcessingTaskStatus.processing;

        if (aIsActive != bIsActive) {
          return aIsActive ? -1 : 1;
        }

        // Then by date (newest first)
        return b.updatedAt.compareTo(a.updatedAt);
      });

    return ListView.separated(
      padding: const EdgeInsets.all(16),
      itemCount: sortedTasks.length,
      separatorBuilder: (context, index) => const SizedBox(height: 12),
      itemBuilder: (context, index) => _TaskItem(task: sortedTasks[index]),
    );
  }

  /// Shows a confirmation dialog and clears all tasks if confirmed
  void _clearAllTasks(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Tasks'),
        content: const Text(
            'Are you sure you want to clear all processing tasks? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              // Get the task manager and clear all tasks
              final taskManager = ref.read(contentProcessingTaskManagerProvider);
              taskManager.clearAllTasks();

              // Close the dialog and the task list
              Navigator.of(context).pop();
              Navigator.of(context).pop();
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Clear All'),
          ),
        ],
      ),
    );
  }

  /// Builds a banner with an upgrade option for users with file size limit errors
  Widget _buildUpgradeBanner(BuildContext context, WidgetRef ref) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.amber.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.amber.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.warning_amber_rounded, color: Colors.amber.shade800, size: 20),
              const SizedBox(width: 8),
              const Expanded(
                child: Text(
                  'File size limit exceeded',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          const Text(
            'Some files exceed your current license tier limit. Upgrade your license to process larger files.',
            style: TextStyle(fontSize: 13),
          ),
          const SizedBox(height: 12),
          ElevatedButton(
            onPressed: () {
              // Close the task list and navigate to upgrade page
              Navigator.of(context).pop();
              Navigator.of(context).pushNamed(AppRoutes.subscriptionUpgrade);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              minimumSize: const Size(double.infinity, 36),
            ),
            child: const Text('Upgrade License'),
          ),
        ],
      ),
    );
  }
}

/// A widget that displays a single task item
class _TaskItem extends ConsumerWidget {
  final ContentProcessingTask task;

  const _TaskItem({
    required this.task,
  });

  /// Navigate to the license upgrade page
  void _navigateToUpgrade(BuildContext context, WidgetRef ref) {
    // Close the task list dialog
    Navigator.of(context).pop();

    // Navigate to the license upgrade page
    Navigator.of(context).pushNamed(AppRoutes.subscriptionUpgrade);
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            // ignore: deprecated_member_use
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              _buildStatusIcon(),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  task.displayName,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          _buildProgressIndicator(),
          const SizedBox(height: 8),
          _buildTaskDetails(),
          if (task.status == ContentProcessingTaskStatus.failed && task.errorMessage != null)
            _buildErrorMessage(context, ref),
        ],
      ),
    );
  }

  Widget _buildStatusIcon() {
    switch (task.status) {
      case ContentProcessingTaskStatus.pending:
        return const Icon(Icons.hourglass_empty, color: Colors.orange, size: 20);
      case ContentProcessingTaskStatus.processing:
        return const Icon(Icons.sync, color: Colors.blue, size: 20);
      case ContentProcessingTaskStatus.completed:
        return const Icon(Icons.check_circle, color: Colors.green, size: 20);
      case ContentProcessingTaskStatus.failed:
        return const Icon(Icons.error, color: Colors.red, size: 20);
      case ContentProcessingTaskStatus.sizeLimitExceeded:
        return const Icon(Icons.warning_amber_rounded, color: Colors.amber, size: 20);
    }
  }

  Widget _buildProgressIndicator() {
    if (task.status == ContentProcessingTaskStatus.processing) {
      return LinearProgressIndicator(
        value: task.progress,
        // ignore: deprecated_member_use
        backgroundColor: Colors.grey.withOpacity(0.2),
        valueColor: const AlwaysStoppedAnimation<Color>(AppColors.primary),
      );
    } else if (task.status == ContentProcessingTaskStatus.pending) {
      return LinearProgressIndicator(
        value: 0,
        // ignore: deprecated_member_use
        backgroundColor: Colors.grey.withOpacity(0.2),
        valueColor: const AlwaysStoppedAnimation<Color>(Colors.orange),
      );
    } else if (task.status == ContentProcessingTaskStatus.completed) {
      return LinearProgressIndicator(
        value: 1.0,
        // ignore: deprecated_member_use
        backgroundColor: Colors.grey.withOpacity(0.2),
        valueColor: const AlwaysStoppedAnimation<Color>(Colors.green),
      );
    } else if (task.status == ContentProcessingTaskStatus.sizeLimitExceeded) {
      return LinearProgressIndicator(
        value: 0,
        // ignore: deprecated_member_use
        backgroundColor: Colors.grey.withOpacity(0.2),
        valueColor: const AlwaysStoppedAnimation<Color>(Colors.amber),
      );
    } else {
      return LinearProgressIndicator(
        value: 0,
        // ignore: deprecated_member_use
        backgroundColor: Colors.grey.withOpacity(0.2),
        valueColor: const AlwaysStoppedAnimation<Color>(Colors.red),
      );
    }
  }

  Widget _buildTaskDetails() {
    final fileSizeStr =
        task.fileSize > 0 ? FormatUtils.formatFileSize(task.fileSize) : 'Unknown size';

    // Detect file type for better display
    String fileType = 'File';
    if (task.fileName.toLowerCase().contains('whatsapp')) {
      fileType = 'WhatsApp Chat';
    } else if (task.fileName.toLowerCase().endsWith('.zip')) {
      fileType = 'ZIP Archive';
    } else if (task.fileName.toLowerCase().endsWith('.pdf')) {
      fileType = 'PDF Document';
    } // Add more file type detection as needed

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              fileSizeStr,
              style: const TextStyle(
                fontSize: 12,
                color: Colors.black54,
              ),
            ),
            Text(
              task.status.label,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: _getStatusColor(),
              ),
            ),
            Text(
              FormatUtils.formatTimeAgo(task.updatedAt),
              style: const TextStyle(
                fontSize: 12,
                color: Colors.black54,
              ),
            ),
          ],
        ),
        if (task.status == ContentProcessingTaskStatus.sizeLimitExceeded)
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Text(
              fileType,
              style: const TextStyle(
                fontSize: 12,
                fontStyle: FontStyle.italic,
                color: Colors.black54,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildErrorMessage(BuildContext context, WidgetRef ref) {
    if (task.status == ContentProcessingTaskStatus.sizeLimitExceeded) {
      // Extract license tier information from error message
      final errorMsg = task.errorMessage ?? '';

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Text(
              errorMsg,
              style: TextStyle(
                fontSize: 12,
                color: Colors.amber.shade800,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const SizedBox(height: 8),
          ElevatedButton(
            onPressed: () => _navigateToUpgrade(context, ref),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              minimumSize: const Size(double.infinity, 36),
            ),
            child: const Text('Upgrade License'),
          ),
        ],
      );
    }

    return Padding(
      padding: const EdgeInsets.only(top: 8),
      child: Text(
        task.errorMessage!,
        style: const TextStyle(
          fontSize: 12,
          color: Colors.red,
        ),
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  Color _getStatusColor() {
    switch (task.status) {
      case ContentProcessingTaskStatus.pending:
        return Colors.orange;
      case ContentProcessingTaskStatus.processing:
        return Colors.blue;
      case ContentProcessingTaskStatus.completed:
        return Colors.green;
      case ContentProcessingTaskStatus.failed:
        return Colors.red;
      case ContentProcessingTaskStatus.sizeLimitExceeded:
        return Colors.amber;
    }
  }
}
