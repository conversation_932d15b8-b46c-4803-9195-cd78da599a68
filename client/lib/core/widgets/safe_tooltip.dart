import 'package:flutter/material.dart';
import 'package:promz/core/utils/tooltip_manager.dart';

/// A tooltip wrapper that integrates with the TooltipManager to prevent
/// animation controller conflicts.
///
/// This should be used instead of the default Flutter Tooltip widget
/// for consistent tooltip behavior across the app, especially before
/// navigation or state changes.
class SafeTooltip extends StatelessWidget {
  /// The child widget that the tooltip is provided for
  final Widget child;

  /// The message to show in the tooltip
  final String message;

  /// How long the tooltip should wait before showing
  final Duration? waitDuration;

  /// How long the tooltip should remain visible
  final Duration? showDuration;

  /// Additional styling for the tooltip
  final Decoration? decoration;

  /// Text style for the tooltip message
  final TextStyle? textStyle;

  /// Whether the tooltip should be enabled
  final bool enabled;

  const SafeTooltip({
    Key? key,
    required this.child,
    required this.message,
    this.waitDuration,
    this.showDuration,
    this.decoration,
    this.textStyle,
    this.enabled = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (!enabled || message.isEmpty) {
      return child;
    }

    // Use a standard Tooltip widget with consistent key for tracking
    return Tooltip(
      key: TooltipManager().tooltipKey,
      message: message,
      waitDuration: waitDuration,
      showDuration: showDuration,
      decoration: decoration,
      textStyle: textStyle,
      child: child,
    );
  }
}
