import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:promz/core/models/content_processing_task.dart';
import 'package:promz/core/providers/api_client_provider.dart';
import 'package:promz/core/providers/content_processing_task_provider.dart';
import 'package:promz/core/providers/license_manager_provider.dart';
import 'package:promz/core/services/license_manager_service.dart';
import 'package:promz/core/widgets/content_task_list.dart';
import 'package:promz_common/promz_common.dart';

/// A widget that displays the status of shared content processing tasks
///
/// This widget is designed to be placed in the app header and shows a small
/// indicator with the number of active tasks. When tapped, it expands to
/// show a list of all tasks.
class ContentProcessingStatusIndicator extends ConsumerStatefulWidget {
  /// Creates a shared content status indicator
  const ContentProcessingStatusIndicator({Key? key}) : super(key: key);

  @override
  ConsumerState<ContentProcessingStatusIndicator> createState() =>
      _ContentProcessingStatusIndicatorState();
}

class _ContentProcessingStatusIndicatorState
    extends ConsumerState<ContentProcessingStatusIndicator> {
  static const _logName = 'ContentStatusInd';

  @override
  void initState() {
    super.initState();

    // Check for license changes and force a rebuild when it happens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final licenseManager = ref.read(licenseManagerProvider);
      licenseManager.addListener(_onLicenseChanged);
    });
  }

  @override
  void dispose() {
    // Safety check - store reference to licenseManager before potentially losing ref
    LicenseManagerService? licenseManager;
    try {
      // Only attempt to access ref if the widget is still mounted
      if (mounted) {
        licenseManager = ref.read(licenseManagerProvider);
      }
    } catch (e) {
      appLog.debug('Could not access ref in dispose: $e', name: _logName);
    }

    // Only remove the listener if we successfully got the licenseManager
    if (licenseManager != null) {
      licenseManager.removeListener(_onLicenseChanged);
    }

    super.dispose();
  }

  void _onLicenseChanged() {
    appLog.debug('License changed, rebuilding status indicator', name: _logName);
    if (mounted) {
      // Force a rebuild of the widget
      setState(() {});

      // Also refresh the ApiClient to ensure WebSocket connections are updated
      final apiClient = ref.read(apiClientProvider);
      if (apiClient.apiKey != null && apiClient.apiKey!.isNotEmpty) {
        appLog.debug('API key is available, refreshing WebSocket connection', name: _logName);
        apiClient.refreshWebSocketConnection();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Check if any tasks have size limit errors
    final hasSizeLimitErrors = ref.watch(hasSizeLimitErrorsProvider);
    final activeTasksCount = ref.watch(activeTasksCountProvider);

    // First check for size limit errors
    if (hasSizeLimitErrors.valueOrNull == true) {
      // Show indicator with error styling
      return _buildIndicator(context, ref, 1, forceShow: true);
    }

    // Always show the indicator, even when there are no active tasks
    return activeTasksCount.when(
      data: (count) {
        // Always show the indicator with the count (even if it's 0)
        return _buildIndicator(context, ref, count, forceShow: true);
      },
      loading: () => _buildIndicator(context, ref, 0, forceShow: true),
      error: (_, __) => _buildIndicator(context, ref, 0, forceShow: true),
    );
  }

  Widget _buildIndicator(BuildContext context, WidgetRef ref, int count, {bool forceShow = false}) {
    // Get license validity to determine indicator appearance
    final apiKey = ref.watch(apiKeyProvider);
    final isLicenseValid = apiKey != null && apiKey.isNotEmpty;

    // Check if any tasks have size limit errors
    final hasSizeLimitErrors = ref.watch(hasSizeLimitErrorsProvider).valueOrNull ?? false;

    // Get task status information
    final tasksStream = ref.watch(contentProcessingTasksProvider);
    final pendingTasks = tasksStream.valueOrNull ?? [];
    final allTasks = pendingTasks.length; // Total number of tasks (including completed)

    // Determine status text based on task states
    String statusText = count.toString();
    String tooltipText = count > 0 ? 'Processing shared files...' : 'View uploaded files';

    if (hasSizeLimitErrors) {
      statusText = 'Size Limit';
      tooltipText = 'File size limit exceeded. Tap to upgrade.';
    } else if (!isLicenseValid) {
      tooltipText = 'Sign in required to process files';
    } else if (count == 0 && allTasks > 0) {
      // No active tasks but we have completed tasks
      statusText = 'Completed';
      tooltipText = 'View your completed tasks';
    } else if (count == 0) {
      // No tasks at all
      statusText = 'No Tasks';
      tooltipText = 'No files uploaded yet';
    } else {
      // Check if any tasks are in specific states for more detailed status
      final hasUploading = pendingTasks.any((task) =>
          task.status == ContentProcessingTaskStatus.pending &&
          task.progress > 0 &&
          task.progress < 0.1);
      final hasQueued = pendingTasks
          .any((task) => task.status == ContentProcessingTaskStatus.pending && task.progress == 0);
      final hasProcessing =
          pendingTasks.any((task) => task.status == ContentProcessingTaskStatus.processing);

      if (hasUploading) {
        statusText = 'Uploading';
        tooltipText = count == 1 ? 'Uploading file to server' : 'Uploading files to server';
      } else if (hasQueued) {
        statusText = 'Queued';
        tooltipText = count == 1 ? 'File queued for processing' : 'Files queued for processing';
      } else if (hasProcessing) {
        statusText = 'Processing';
        tooltipText = count == 1 ? 'Server processing file' : 'Server processing files';
      }
    }

    // Determine indicator color based on status
    final Color indicatorColor = hasSizeLimitErrors
        ? Colors.amber
        : (isLicenseValid ? AppColors.success : AppColors.primary);

    return Tooltip(
      message: tooltipText,
      child: GestureDetector(
        onTap: () => _showTaskList(context, hasSizeLimitErrors: hasSizeLimitErrors),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            // ignore: deprecated_member_use
            color: indicatorColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildAnimatedIcon(context, ref, isLicenseValid, indicatorColor, hasSizeLimitErrors),
              const SizedBox(width: 4),
              Text(
                statusText,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: indicatorColor,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAnimatedIcon(BuildContext context, WidgetRef ref, bool isLicenseValid, Color color,
      bool hasSizeLimitErrors) {
    if (hasSizeLimitErrors) {
      // Show warning icon for size limit errors
      return SizedBox(
        width: 16,
        height: 16,
        child: Icon(
          Icons.warning_amber_rounded,
          size: 14,
          color: color,
        ),
      );
    } else if (!isLicenseValid) {
      // Show lock icon for auth required tasks
      return SizedBox(
        width: 16,
        height: 16,
        child: Icon(
          Icons.lock_outline,
          size: 14,
          color: color,
        ),
      );
    } else {
      // Get active tasks count to determine icon
      final activeTasksCount = ref.watch(activeTasksCountProvider).valueOrNull ?? 0;

      if (activeTasksCount > 0) {
        // Show animated progress indicator for active tasks
        return SizedBox(
          width: 16,
          height: 16,
          child: _buildProgressIndicator(color),
        );
      } else {
        // Show file icon for no active tasks
        return SizedBox(
          width: 16,
          height: 16,
          child: Icon(
            Icons.file_copy_outlined,
            size: 14,
            color: color,
          ),
        );
      }
    }
  }

  /// Builds an animated progress indicator for processing tasks
  Widget _buildProgressIndicator(Color color) {
    // Use a stream builder to animate the progress indicator
    return StreamBuilder<int>(
      stream: Stream.periodic(const Duration(milliseconds: 750), (i) => i % 3),
      builder: (context, snapshot) {
        final phase = snapshot.data ?? 0;

        // Different phases show different animations
        switch (phase) {
          case 0:
            // Circular progress
            return CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(color),
            );
          case 1:
            // Cloud upload icon (indicates server processing)
            return Icon(
              Icons.cloud_upload_outlined,
              size: 14,
              color: color,
            );
          case 2:
            // Processing icon
            return Icon(
              Icons.sync,
              size: 14,
              color: color,
            );
          default:
            return CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(color),
            );
        }
      },
    );
  }

  void _showTaskList(BuildContext context, {bool hasSizeLimitErrors = false}) {
    // Always show the task list first
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => ContentTaskList(showUpgradeOption: hasSizeLimitErrors),
    );
  }
}
