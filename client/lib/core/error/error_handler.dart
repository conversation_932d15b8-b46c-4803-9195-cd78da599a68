import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:promz_common/strings.dart';

enum ErrorSeverity {
  /// Low severity, shows a brief Snackbar
  low,

  /// Medium severity, shows a Material Banner
  medium,

  /// High severity, shows a Bottom Sheet with actions
  high,

  /// Critical severity, shows a full-screen error
  critical,
}

class AppError {
  final String message;
  final String? details;
  final ErrorSeverity severity;
  final List<ErrorAction>? actions;
  final Exception? exception;

  const AppError({
    required this.message,
    this.details,
    required this.severity,
    this.actions,
    this.exception,
  });
}

class ErrorAction {
  final String label;
  final VoidCallback action;
  final bool isPrimary;

  const ErrorAction({
    required this.label,
    required this.action,
    this.isPrimary = false,
  });
}

final errorHandlerProvider = Provider((ref) => ErrorHandler());

class ErrorHandler {
  // Keep track of the most recent scaffold messenger
  static ScaffoldMessengerState? _globalScaffoldMessenger;

  // Method to store a reference to the scaffold messenger
  static void registerScaffoldMessenger(ScaffoldMessengerState scaffoldMessenger) {
    _globalScaffoldMessenger = scaffoldMessenger;
  }

  // Public getter to safely access the global scaffold messenger
  static ScaffoldMessengerState? get globalScaffoldMessenger => _globalScaffoldMessenger;

  // Safe accessor for ScaffoldMessenger
  ScaffoldMessengerState? _getScaffoldMessenger(BuildContext context) {
    // First try to use the context if it's still valid
    if (context.mounted) {
      try {
        final messenger = ScaffoldMessenger.of(context);
        _globalScaffoldMessenger = messenger; // Update the global reference
        return messenger;
      } catch (e) {
        // Context is mounted but ScaffoldMessenger access failed
        // This can happen during transitions or disposal
      }
    }

    // Fall back to the global reference if context is not usable
    return _globalScaffoldMessenger;
  }

  void handleError(BuildContext context, AppError error) {
    switch (error.severity) {
      case ErrorSeverity.low:
        _showSnackbar(context, error);
        break;
      case ErrorSeverity.medium:
        _showBanner(context, error);
        break;
      case ErrorSeverity.high:
        _showBottomSheet(context, error);
        break;
      case ErrorSeverity.critical:
        _showFullScreenError(context, error);
        break;
    }
  }

  void _showSnackbar(BuildContext context, AppError error) {
    final scaffoldMessenger = _getScaffoldMessenger(context);
    if (scaffoldMessenger == null) {
      // Cannot show snackbar - log this if needed
      return;
    }

    scaffoldMessenger.showSnackBar(
      SnackBar(
        content: Text(error.message),
        behavior: SnackBarBehavior.floating,
        action: error.actions?.firstOrNull != null
            ? SnackBarAction(
                label: error.actions!.first.label,
                onPressed: error.actions!.first.action,
              )
            : null,
      ),
    );
  }

  void _showBanner(BuildContext context, AppError error) {
    final scaffoldMessenger = _getScaffoldMessenger(context);
    if (scaffoldMessenger == null) {
      // Cannot show banner - log this if needed
      return;
    }

    scaffoldMessenger.showMaterialBanner(
      MaterialBanner(
        content: Text(error.message),
        leading: const Icon(Icons.error_outline),
        actions: [
          if (error.actions != null)
            ...error.actions!.map(
              (action) => TextButton(
                onPressed: () {
                  scaffoldMessenger.hideCurrentMaterialBanner();
                  action.action();
                },
                child: Text(action.label),
              ),
            ),
          TextButton(
            onPressed: () {
              scaffoldMessenger.hideCurrentMaterialBanner();
            },
            child: const Text('Dismiss'),
          ),
        ],
      ),
    );
  }

  void _showBottomSheet(BuildContext context, AppError error) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Icon(
              Icons.error_outline,
              size: 48,
              color: Colors.orange,
            ),
            const SizedBox(height: 16),
            Text(
              error.message,
              style: Theme.of(context).textTheme.titleLarge,
              textAlign: TextAlign.center,
            ),
            if (error.details != null) ...[
              const SizedBox(height: 8),
              Text(
                error.details!,
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
            ],
            const SizedBox(height: 24),
            if (error.actions != null) ...[
              ...error.actions!.map((action) => Padding(
                    padding: const EdgeInsets.only(bottom: 8.0),
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                        action.action();
                      },
                      style: action.isPrimary
                          ? ElevatedButton.styleFrom(
                              backgroundColor: Theme.of(context).colorScheme.primary,
                              foregroundColor: Theme.of(context).colorScheme.onPrimary,
                            )
                          : null,
                      child: Text(action.label),
                    ),
                  )),
            ],
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Dismiss'),
            ),
          ],
        ),
      ),
    );
  }

  void _showFullScreenError(BuildContext context, AppError error) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => Scaffold(
          appBar: AppBar(
            title: const Text(Strings.errorPageTitle),
          ),
          body: Center(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.red,
                  ),
                  const SizedBox(height: 24),
                  Text(
                    error.message,
                    style: Theme.of(context).textTheme.headlineSmall,
                    textAlign: TextAlign.center,
                  ),
                  if (error.details != null) ...[
                    const SizedBox(height: 16),
                    Text(
                      error.details!,
                      style: Theme.of(context).textTheme.bodyLarge,
                      textAlign: TextAlign.center,
                    ),
                  ],
                  const SizedBox(height: 32),
                  if (error.actions != null) ...[
                    ...error.actions!.map((action) => Padding(
                          padding: const EdgeInsets.only(bottom: 16.0),
                          child: SizedBox(
                            width: double.infinity,
                            child: ElevatedButton(
                              onPressed: () {
                                Navigator.pop(context);
                                action.action();
                              },
                              style: action.isPrimary
                                  ? ElevatedButton.styleFrom(
                                      backgroundColor: Theme.of(context).colorScheme.primary,
                                      foregroundColor: Theme.of(context).colorScheme.onPrimary,
                                    )
                                  : null,
                              child: Text(action.label),
                            ),
                          ),
                        )),
                  ],
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text(Strings.dismissButtonLabel),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
