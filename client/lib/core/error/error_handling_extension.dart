import 'package:flutter/material.dart';
import 'error_handler.dart';

extension ErrorHandlingExtension on BuildContext {
  void showError(AppError error) {
    ErrorHandler().handleError(this, error);
  }

  void showErrorMessage(
    String message, {
    ErrorSeverity severity = ErrorSeverity.low,
    String? details,
    List<ErrorAction>? actions,
    Exception? exception,
  }) {
    final error = AppError(
      message: message,
      severity: severity,
      details: details,
      actions: actions,
      exception: exception,
    );
    ErrorHandler().handleError(this, error);
  }
}
