import 'package:flutter/material.dart';
import 'package:promz/features/input_selection/models/input_source.dart';
import 'package:promz/generated/content_upload.pb.dart';
import 'package:promz_common/promz_common.dart';

/// Helper class for file and source type related UI elements
///
/// Provides methods for getting appropriate icons, descriptions,
/// and detecting source types based on metadata and file properties.
class FileIconHelper {
  static const _logName = 'FileIconHelper';

  static Icon getSourceIcon(
    InputSourceType type,
    BuildContext context, {
    String? fileName,
    String? mimeType,
  }) {
    // First determine based on InputSourceType
    switch (type) {
      // Content types
      case InputSourceType.text:
        return Icon(
          Icons.description,
          color: Colors.blue[700],
        );
      case InputSourceType.pdf:
        return Icon(
          Icons.picture_as_pdf,
          color: Colors.red[700],
        );
      case InputSourceType.zip:
        return Icon(
          Icons.folder_zip,
          color: Colors.amber[800],
        );
      case InputSourceType.file:
        // For generic files, examine the file name and mime type
        return getFileTypeIcon(fileName, mimeType, context);
      case InputSourceType.news:
        return Icon(
          Icons.article,
          color: Colors.teal[700],
        );
      case InputSourceType.youtubeVideo:
        return Icon(
          Icons.video_library,
          color: Colors.red[700],
        );

      // Origin types
      case InputSourceType.clipboard:
        return Icon(
          Icons.content_paste,
          color: Colors.purple[600],
        );
      case InputSourceType.shared:
        return Icon(
          Icons.share,
          color: Colors.green[600],
        );
      case InputSourceType.manual:
        return Icon(
          Icons.edit_note,
          color: Colors.indigo[600],
        );

      // Combined types
      case InputSourceType.clipboardText:
        return Icon(
          Icons.content_paste_go,
          color: Colors.purple[600],
        );
      case InputSourceType.sharedFile:
        return Icon(
          Icons.drive_file_move,
          color: Colors.green[600],
        );
      case InputSourceType.sharedText:
        return Icon(
          Icons.share,
          color: Colors.green[600],
        );
      case InputSourceType.manualFile:
        return Icon(
          Icons.upload_file,
          color: Colors.indigo[600],
        );
      case InputSourceType.manualText:
        return Icon(
          Icons.edit_note,
          color: Colors.indigo[600],
        );
    }
  }

  /// Gets an icon based on file extension or mime type
  static Icon getFileTypeIcon(String? fileName, String? mimeType, BuildContext context) {
    final lowerFileName = (fileName ?? '').toLowerCase();
    final lowerMimeType = (mimeType ?? '').toLowerCase();

    if (lowerFileName.endsWith('.pdf') || lowerMimeType == 'application/pdf') {
      return Icon(
        Icons.picture_as_pdf,
        color: Colors.red[700],
      );
    } else if (lowerFileName.endsWith('.zip') || lowerMimeType.contains('zip')) {
      return Icon(
        Icons.folder_zip,
        color: Colors.amber[800],
      );
    } else if (lowerMimeType.startsWith('text/') || lowerFileName.endsWith('.txt')) {
      return Icon(
        Icons.text_snippet,
        color: Colors.blue[700],
      );
    } else if (lowerFileName.endsWith('.doc') ||
        lowerFileName.endsWith('.docx') ||
        lowerMimeType.contains('word') ||
        lowerMimeType.contains('document')) {
      return Icon(
        Icons.article,
        color: Colors.blue[900],
      );
    } else if (lowerFileName.endsWith('.xls') ||
        lowerFileName.endsWith('.xlsx') ||
        lowerMimeType.contains('excel') ||
        lowerMimeType.contains('spreadsheet')) {
      return Icon(
        Icons.table_chart,
        color: Colors.green[800],
      );
    } else if (lowerFileName.endsWith('.ppt') ||
        lowerFileName.endsWith('.pptx') ||
        lowerMimeType.contains('powerpoint') ||
        lowerMimeType.contains('presentation')) {
      return Icon(
        Icons.slideshow,
        color: Colors.orange[800],
      );
    } else if (lowerFileName.endsWith('.jpg') ||
        lowerFileName.endsWith('.jpeg') ||
        lowerFileName.endsWith('.png') ||
        lowerFileName.endsWith('.gif') ||
        lowerMimeType.startsWith('image/')) {
      return Icon(
        Icons.image,
        color: Colors.purple[500],
      );
    } else if (lowerFileName.endsWith('.mp3') ||
        lowerFileName.endsWith('.wav') ||
        lowerFileName.endsWith('.ogg') ||
        lowerMimeType.startsWith('audio/')) {
      return Icon(
        Icons.audio_file,
        color: Colors.pink[500],
      );
    } else if (lowerFileName.endsWith('.mp4') ||
        lowerFileName.endsWith('.avi') ||
        lowerFileName.endsWith('.mov') ||
        lowerMimeType.startsWith('video/')) {
      return Icon(
        Icons.video_file,
        color: Colors.red[500],
      );
    } else if (lowerFileName.endsWith('.html') ||
        lowerFileName.endsWith('.htm') ||
        lowerMimeType.contains('html')) {
      return Icon(
        Icons.html,
        color: Colors.orange[600],
      );
    } else if (lowerFileName.endsWith('.json') ||
        lowerFileName.endsWith('.xml') ||
        lowerMimeType.contains('json') ||
        lowerMimeType.contains('xml')) {
      return Icon(
        Icons.data_object,
        color: Colors.teal[600],
      );
    } else if (lowerFileName.endsWith('.csv')) {
      return Icon(
        Icons.grid_on,
        color: Colors.green[600],
      );
    } else {
      return Icon(
        Icons.insert_drive_file,
        color: Colors.blueGrey[600],
      );
    }
  }

  /// Gets a descriptive text for a source type
  ///
  /// Returns a user-friendly description of the source type based on its
  /// properties like file extension, origin (clipboard, shared), etc.
  static String? getSourceTypeDescription(InputSource source) {
    if (source.type.isFileType) {
      if (source.fileName != null) {
        final extension = source.fileName!.split('.').last.toUpperCase();
        return '$extension file';
      } else {
        return 'File';
      }
    } else if (source.type.isClipboardType) {
      return 'From clipboard';
    } else if (source.type.isSharedType) {
      return source.sourceApp != null ? 'Shared from ${source.sourceApp}' : 'Shared content';
    } else if (source.type == InputSourceType.news) {
      return 'News article';
    } else if (source.type == InputSourceType.youtubeVideo) {
      return 'YouTube video';
    }

    return null;
  }

  /// Checks if a source appears to be a conversation based on metadata and file properties
  static bool isConversationSource(InputSource source) {
    final processingResult = source.processingResult;

    // First, always check if we have WhatsApp metadata
    if (processingResult != null && processingResult.hasWhatsappMetadata()) {
      appLog.debug('Detected conversation source from WhatsApp metadata object', name: _logName);
      return true;
    }

    // Check content type if available
    if (processingResult != null && processingResult.contentType == 'conversation') {
      appLog.debug('Detected conversation source from contentType=conversation', name: _logName);
      return true;
    }

    // Check for explicit WhatsApp/conversation markers
    if (processingResult != null && processingResult.sourceType == 'whatsapp') {
      appLog.debug('Detected conversation source from explicit WhatsApp metadata', name: _logName);
      return true;
    }

    // Check for standard platform identifiers
    if (processingResult != null &&
        (processingResult.source.toLowerCase().contains('whatsapp') ||
            processingResult.appName.toLowerCase().contains('whatsapp'))) {
      appLog.debug('Detected conversation source from platform metadata', name: _logName);
      return true;
    }

    // Check content sample for WhatsApp patterns
    if (processingResult != null &&
        processingResult.content.toLowerCase().contains('whatsapp chat with')) {
      appLog.debug('Detected conversation source from content sample', name: _logName);
      return true;
    }

    // Check filename patterns
    final fileName = source.fileName?.toLowerCase() ?? '';
    if (fileName.contains('chat') ||
        fileName.contains('conversation') ||
        fileName.contains('whatsapp') ||
        fileName.contains('telegram') ||
        fileName.contains('discord') ||
        fileName.contains('slack') ||
        fileName.contains('teams') ||
        fileName.contains('messenger')) {
      appLog.debug('Detected conversation source from filename pattern', name: _logName);
      return true;
    }

    return false;
  }

  /// Detects if a ProcessingResult has WhatsApp metadata or indicators
  /// This is a UI helper method to determine if WhatsApp-specific UI elements should be shown
  static bool hasWhatsAppMetadata(ProcessingResult result) {
    // Check if WhatsApp metadata exists
    if (result.hasWhatsappMetadata()) {
      appLog.debug('WhatsApp metadata found in result', name: _logName);
      return true;
    }

    // Check content type
    if (result.contentType == 'conversation') {
      appLog.debug('Conversation content type detected', name: _logName);
      return true;
    }

    // Check source type
    if (result.sourceType.toLowerCase() == 'whatsapp') {
      appLog.debug('WhatsApp source type detected', name: _logName);
      return true;
    }

    // Check filename patterns
    final fileName = result.fileName.toLowerCase();
    if (fileName.contains('whatsapp') || fileName.contains('chat')) {
      appLog.debug('WhatsApp or chat pattern found in filename', name: _logName);
      return true;
    }

    return false;
  }

  /// Checks if a news article source has valid metadata for rich display
  static bool hasValidNewsMetadata(InputSource source) {
    // First check if this is explicitly typed as news
    if (source.type == InputSourceType.news) {
      appLog.info('Source is explicitly typed as news', name: 'FileIconHelper');
      return true;
    }

    final processingResult = source.processingResult;
    if (processingResult == null) {
      appLog.info('hasValidNewsMetadata: No metadata available', name: 'FileIconHelper');
      return false;
    }

    // Check for other news-related metadata
    final hasTitle = processingResult.title.isNotEmpty;
    final hasAuthor = processingResult.author.isNotEmpty;

    // Check if filename suggests this is a news article
    final fileName = source.fileName?.toLowerCase() ?? '';
    final isLikelyNewsFileName = fileName.contains('article') ||
        fileName.contains('news') ||
        (fileName.length > 20 && !fileName.contains('.'));

    // Also consider it valid if it has a news-like filename and at least some metadata
    return (hasTitle) || (isLikelyNewsFileName && (hasAuthor));
  }

  /// Checks if a source is a YouTube video with valid metadata for rich display
  static bool hasValidYouTubeMetadata(InputSource source) {
    // First check if this is explicitly typed as YouTube video
    if (source.type == InputSourceType.youtubeVideo) {
      appLog.info('Source is explicitly typed as YouTube video', name: 'FileIconHelper');
      return true;
    }

    final processingResult = source.processingResult!;
    return processingResult.hasYoutubeMetadata();
  }
}
