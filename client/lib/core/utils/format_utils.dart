import 'package:intl/intl.dart';

/// Utility class for formatting various data types
class FormatUtils {
  /// Format a file size in bytes to a human-readable string
  ///
  /// Examples:
  /// - 1023 bytes -> "1023 B"
  /// - 1024 bytes -> "1.0 KB"
  /// - 1048576 bytes -> "1.0 MB"
  static String formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      final kb = bytes / 1024;
      return '${kb.toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      final mb = bytes / (1024 * 1024);
      return '${mb.toStringAsFixed(1)} MB';
    } else {
      final gb = bytes / (1024 * 1024 * 1024);
      return '${gb.toStringAsFixed(1)} GB';
    }
  }

  /// Format a DateTime to a relative time string (e.g., "2 minutes ago")
  static String formatTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inSeconds < 60) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes} ${difference.inMinutes == 1 ? 'minute' : 'minutes'} ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours} ${difference.inHours == 1 ? 'hour' : 'hours'} ago';
    } else if (difference.inDays < 30) {
      return '${difference.inDays} ${difference.inDays == 1 ? 'day' : 'days'} ago';
    } else {
      // For older dates, show the actual date
      return DateFormat('MMM d, yyyy').format(dateTime);
    }
  }

  /// Format a progress value (0.0 to 1.0) as a percentage string
  static String formatProgress(double progress) {
    // Ensure progress is between 0 and 1
    final clampedProgress = progress.clamp(0.0, 1.0);
    return '${(clampedProgress * 100).toStringAsFixed(0)}%';
  }
}
