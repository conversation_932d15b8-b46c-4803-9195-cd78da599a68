// Copyright (c) 2025 Promz AI Labs. All rights reserved.
// This code is the property of Promz AI Labs and may not be copied, modified,
// or distributed without express written permission.

import 'package:flutter/foundation.dart';
import 'package:promz_common/promz_common.dart';

/// A mixin that provides safe notification capabilities to ChangeNotifier classes
///
/// This mixin adds protection against calling notifyListeners() after a widget has been disposed,
/// which is a common cause of exceptions in Flutter applications, especially during navigation,
/// sign-out, or other major state changes.
///
/// Usage:
/// ```dart
/// class MyViewModel extends ChangeNotifier with SafeNotifier {
///   // Your ViewModel implementation
///
///   void updateSomething() {
///     // Make changes to state
///     // ...
///
///     // Safely notify listeners instead of directly calling notifyListeners()
///     safeNotify();
///   }
/// }
/// ```
mixin SafeNotifier on ChangeNotifier {
  static const _logName = 'SafeNotifier';

  /// Tracks if the notifier is still active and can notify listeners
  bool _isMounted = true;

  /// Whether the notifier is still active and can notify listeners
  bool get isMounted => _isMounted;

  /// Safely notifies listeners if the notifier is still mounted.
  ///
  /// This method wraps the standard notifyListeners() call with a check to
  /// prevent the "mounted widget was unmounted during build phase" exception.
  void safeNotify() {
    if (_isMounted) {
      try {
        notifyListeners();
      } catch (e, stack) {
        // This usually occurs during widget disposal race conditions
        appLog.debug(
          'Suppressed notifyListeners() exception: ${e.toString()}',
          name: _logName,
          error: e,
          stackTrace: stack,
        );
      }
    } else {
      appLog.debug(
        'Notification suppressed - notifier is no longer mounted',
        name: _logName,
      );
    }
  }

  /// Marks the notifier as unmounted to prevent further notifications
  void markUnmounted() {
    _isMounted = false;
  }

  @override
  void dispose() {
    // Mark as unmounted before official disposal
    _isMounted = false;
    super.dispose();
  }
}

/// An extension on ChangeNotifier that adds safe notification capabilities
///
/// This extension can be used with existing ChangeNotifier classes where
/// you can't add a mixin but want to perform safe notifications.
extension SafeNotifierExtension on ChangeNotifier {
  static const _logName = 'SafeNotifierExtension';

  /// Safely notifies listeners with a provided mounted flag.
  ///
  /// This method accepts a function to call notifyListeners() since the extension
  /// cannot directly call the protected notifyListeners() method.
  ///
  /// Example:
  /// ```dart
  /// // Inside a ViewModel class
  /// class MyViewModel extends ChangeNotifier {
  ///   bool _mounted = true;
  ///
  ///   void updateState() {
  ///     // Update state
  ///     // ...
  ///
  ///     // Safely notify if _mounted flag is true
  ///     if (_mounted) {
  ///       notifyListenersSafely(() => notifyListeners());
  ///     }
  ///   }
  /// }
  /// ```
  void notifyListenersSafely(VoidCallback notifier) {
    try {
      notifier();
    } catch (e, stack) {
      // This usually occurs during widget disposal race conditions
      appLog.debug(
        'Suppressed notifyListeners() exception: ${e.toString()}',
        name: _logName,
        error: e,
        stackTrace: stack,
      );
    }
  }
}
