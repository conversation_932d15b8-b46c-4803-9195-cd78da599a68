import 'package:flutter/material.dart';
import 'package:promz_common/promz_common.dart';

/// Manages tooltips throughout the application to prevent issues with animation controllers
///
/// This class provides methods to safely dismiss tooltips before major state changes
/// like navigation or sign-out, preventing animation controller conflicts.
class TooltipManager {
  static const _logName = 'TooltipManager';

  /// Singleton instance
  static final TooltipManager _instance = TooltipManager._();

  /// Factory constructor to return the singleton instance
  factory TooltipManager() => _instance;

  /// Private constructor for singleton pattern
  TooltipManager._();

  /// The last time tooltips were dismissed
  DateTime? _lastDismissTime;

  /// Global key used to access TooltipState
  final GlobalKey _tooltipKey = GlobalKey(debugLabel: 'global_tooltip_key');

  /// Get the tooltip key for widgets that want to register with this manager
  <PERSON><PERSON><PERSON> get tooltipKey => _tooltipKey;

  /// Dismisses all active tooltips in the application safely
  ///
  /// This should be called before major state changes like navigation or sign-out
  void dismissTooltips() {
    // We use a time-based approach to prevent excessive tooltip dismissals
    final now = DateTime.now();
    if (_lastDismissTime != null) {
      final timeSinceLastDismiss = now.difference(_lastDismissTime!);
      if (timeSinceLastDismiss.inMilliseconds < 500) {
        // Avoid dismissing tooltips too frequently
        return;
      }
    }

    // Remove focus from any focused widget, which will also dismiss tooltips
    WidgetsBinding.instance.focusManager.primaryFocus?.unfocus();

    // Update the last dismiss time
    _lastDismissTime = now;
    appLog.debug('Dismissed tooltips', name: _logName);
  }
}
