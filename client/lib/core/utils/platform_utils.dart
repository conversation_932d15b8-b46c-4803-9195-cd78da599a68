import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';

/// Utility class for platform-specific UI elements and behaviors
class PlatformUtils {
  /// Returns true if the current platform is iOS or macOS
  static bool get isApplePlatform => Platform.isIOS || Platform.isMacOS;

  /// Returns true if the current platform is Android
  static bool get isAndroid => Platform.isAndroid;

  /// Returns a platform-specific app bar
  static PreferredSizeWidget appBar({
    required BuildContext context,
    required Widget title,
    List<Widget>? actions,
    bool automaticallyImplyLeading = true,
    Widget? leading,
    Color? backgroundColor,
    Color? foregroundColor,
  }) {
    if (isApplePlatform) {
      return CupertinoNavigationBar(
        middle: title,
        trailing: actions != null && actions.isNotEmpty
            ? Row(
                mainAxisSize: MainAxisSize.min,
                children: actions,
              )
            : null,
        leading: leading,
        backgroundColor: backgroundColor,
      );
    } else {
      return AppBar(
        title: title,
        actions: actions,
        automaticallyImplyLeading: automaticallyImplyLeading,
        leading: leading,
        backgroundColor: backgroundColor,
        foregroundColor: foregroundColor,
      );
    }
  }

  /// Returns a platform-specific button
  static Widget button({
    required BuildContext context,
    required String label,
    required VoidCallback onPressed,
    bool isPrimary = false,
    bool isDestructive = false,
    IconData? icon,
  }) {
    if (isApplePlatform) {
      return CupertinoButton(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        color: isPrimary ? Theme.of(context).colorScheme.primary : null,
        onPressed: onPressed,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (icon != null) ...[
              Icon(
                icon,
                color: isPrimary
                    ? Colors.white
                    : isDestructive
                        ? CupertinoColors.destructiveRed
                        : null,
              ),
              const SizedBox(width: 8),
            ],
            Text(
              label,
              style: TextStyle(
                color: isPrimary
                    ? Colors.white
                    : isDestructive
                        ? CupertinoColors.destructiveRed
                        : null,
              ),
            ),
          ],
        ),
      );
    } else {
      if (isPrimary) {
        return ElevatedButton(
          onPressed: onPressed,
          style: ElevatedButton.styleFrom(
            backgroundColor: Theme.of(context).colorScheme.primary,
            foregroundColor: Colors.white,
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (icon != null) ...[
                Icon(icon),
                const SizedBox(width: 8),
              ],
              Text(label),
            ],
          ),
        );
      } else if (isDestructive) {
        return TextButton(
          onPressed: onPressed,
          style: TextButton.styleFrom(
            foregroundColor: Colors.red,
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (icon != null) ...[
                Icon(icon),
                const SizedBox(width: 8),
              ],
              Text(label),
            ],
          ),
        );
      } else {
        return TextButton(
          onPressed: onPressed,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (icon != null) ...[
                Icon(icon),
                const SizedBox(width: 8),
              ],
              Text(label),
            ],
          ),
        );
      }
    }
  }

  /// Returns a platform-specific dialog
  static Future<T?> showPlatformDialog<T>({
    required BuildContext context,
    required String title,
    required String message,
    String? cancelButtonLabel,
    String? confirmButtonLabel,
    bool barrierDismissible = true,
  }) {
    if (isApplePlatform) {
      return showCupertinoDialog<T>(
        context: context,
        barrierDismissible: barrierDismissible,
        builder: (context) => CupertinoAlertDialog(
          title: Text(title),
          content: Text(message),
          actions: [
            if (cancelButtonLabel != null)
              CupertinoDialogAction(
                onPressed: () => Navigator.of(context).pop(false as T),
                isDefaultAction: false,
                child: Text(cancelButtonLabel),
              ),
            if (confirmButtonLabel != null)
              CupertinoDialogAction(
                isDefaultAction: true,
                onPressed: () => Navigator.of(context).pop(true as T),
                child: Text(confirmButtonLabel),
              ),
          ],
        ),
      );
    } else {
      return showDialog<T>(
        context: context,
        barrierDismissible: barrierDismissible,
        builder: (context) => AlertDialog(
          title: Text(title),
          content: Text(message),
          actions: [
            if (cancelButtonLabel != null)
              TextButton(
                onPressed: () => Navigator.of(context).pop(false as T),
                child: Text(cancelButtonLabel),
              ),
            if (confirmButtonLabel != null)
              TextButton(
                onPressed: () => Navigator.of(context).pop(true as T),
                child: Text(confirmButtonLabel),
              ),
          ],
        ),
      );
    }
  }

  /// Returns a platform-specific text field
  static Widget textField({
    required BuildContext context,
    required TextEditingController controller,
    String? placeholder,
    bool obscureText = false,
    TextInputType? keyboardType,
    TextInputAction? textInputAction,
    ValueChanged<String>? onSubmitted,
    ValueChanged<String>? onChanged,
    FocusNode? focusNode,
    InputDecoration? decoration,
  }) {
    if (isApplePlatform) {
      return CupertinoTextField(
        controller: controller,
        placeholder: placeholder,
        obscureText: obscureText,
        keyboardType: keyboardType,
        textInputAction: textInputAction,
        onSubmitted: onSubmitted,
        onChanged: onChanged,
        focusNode: focusNode,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: CupertinoColors.systemBackground,
          border: Border.all(color: CupertinoColors.systemGrey4),
          borderRadius: BorderRadius.circular(8),
        ),
      );
    } else {
      return TextField(
        controller: controller,
        decoration: decoration ??
            InputDecoration(
              hintText: placeholder,
              border: const OutlineInputBorder(),
            ),
        obscureText: obscureText,
        keyboardType: keyboardType,
        textInputAction: textInputAction,
        onSubmitted: onSubmitted,
        onChanged: onChanged,
        focusNode: focusNode,
      );
    }
  }

  /// Returns a platform-specific switch
  static Widget switch_({
    required BuildContext context,
    required bool value,
    required ValueChanged<bool> onChanged,
    Color? activeColor,
  }) {
    if (isApplePlatform) {
      // CupertinoSwitch doesn't have activeTrackColor, so we'll use the standard CupertinoSwitch
      // with the default styling
      return CupertinoSwitch(
        value: value,
        onChanged: onChanged,
      );
    } else {
      return Switch(
        value: value,
        onChanged: onChanged,
        activeTrackColor: activeColor,
      );
    }
  }
}
