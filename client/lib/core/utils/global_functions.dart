import 'package:flutter/material.dart';
import 'package:promz/core/utils/tooltip_manager.dart';

/// Contains global functions that can be accessed from anywhere in the app
/// without requiring a BuildContext or other dependencies
class AppGlobalFunctions {
  /// Function to safely clean up any UI elements that might cause issues
  /// if they persist during major state changes (like sign-out)
  static VoidCallback? safeCleanupUI;

  /// Call this method before any major state change that could invalidate contexts
  /// such as sign-out, navigation to a different route stack, etc.
  static void cleanupUIBeforeStateChange() {
    // Dismiss any active tooltips to prevent animation controller errors
    TooltipManager().dismissTooltips();

    // Perform other cleanup tasks (dismiss snackbars, etc.)
    if (safeCleanupUI != null) {
      safeCleanupUI!();
    }
  }
}
