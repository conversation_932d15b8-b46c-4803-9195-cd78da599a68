import 'package:promz/core/utils/hash_helper.dart';
import 'package:promz_common/promz_common.dart';

/// A registry for tracking recently processed content hashes to prevent duplicates
class DuplicateDetectionRegistry {
  static const _logName = 'DuplicateDetectionRegistry';

  // Singleton instance
  static final DuplicateDetectionRegistry _instance = DuplicateDetectionRegistry._();

  // Private constructor
  DuplicateDetectionRegistry._();

  // Get the singleton instance
  static DuplicateDetectionRegistry get instance => _instance;

  // Set of recently processed content hashes with their timestamp
  final Map<String, DateTime> _recentContentHashes = {};

  // Cache expiration in minutes
  static const int _cacheExpirationMinutes = 30;

  /// Register a hash to the duplicate detection registry
  /// Returns true if the hash was newly added, false if it already exists
  bool registerHash(String hash) {
    if (hash.isEmpty) {
      return false;
    }

    final now = DateTime.now();

    // Clean expired entries first
    _cleanExpiredEntries();

    // Check if hash already exists
    if (_recentContentHashes.containsKey(hash)) {
      // Update timestamp and return false (already exists)
      _recentContentHashes[hash] = now;
      appLog.debug('Hash already exists in registry: $hash', name: _logName);
      return false;
    }

    // Add new hash with current timestamp
    _recentContentHashes[hash] = now;
    appLog.debug('Registered new hash: $hash', name: _logName);
    return true;
  }

  /// Check if a hash exists in the registry
  bool hashExists(String hash) {
    if (hash.isEmpty) {
      return false;
    }

    // Clean expired entries first
    _cleanExpiredEntries();

    // Check if hash exists
    return _recentContentHashes.containsKey(hash);
  }

  /// Register a file path and calculate its hash
  /// Returns true if the file was newly added, false if it already exists
  Future<bool> registerFile(String filePath) async {
    try {
      final hash = await HashHelper.calculateFileHash(filePath);
      return registerHash(hash);
    } catch (e) {
      appLog.error('Error calculating file hash', name: _logName, error: e);
      return true; // Assume not a duplicate if hash calculation fails
    }
  }

  /// Clean expired entries from the registry
  void _cleanExpiredEntries() {
    final now = DateTime.now();
    final expiredKeys = _recentContentHashes.entries
        .where((entry) => now.difference(entry.value).inMinutes > _cacheExpirationMinutes)
        .map((entry) => entry.key)
        .toList();

    for (final key in expiredKeys) {
      _recentContentHashes.remove(key);
    }

    if (expiredKeys.isNotEmpty) {
      appLog.debug('Cleaned ${expiredKeys.length} expired entries from registry', name: _logName);
    }
  }

  void reset() {
    _recentContentHashes.clear();
  }
}
