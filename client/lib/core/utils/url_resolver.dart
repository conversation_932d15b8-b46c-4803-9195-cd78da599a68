import 'package:http/http.dart' as http;
import 'package:promz_common/promz_common.dart';

/// Represents a resolved URL with both original and final destinations
class UrlInfo {
  final String originalUrl;
  final String finalUrl;
  final bool hasRedirect;

  UrlInfo({
    required this.originalUrl,
    required this.finalUrl,
  }) : hasRedirect = originalUrl != finalUrl;

  @override
  String toString() =>
      'UrlInfo{originalUrl: $originalUrl, finalUrl: $finalUrl, hasRedirect: $hasRedirect}';
}

/// Comprehensive information about a processed URL
///
/// This class contains all the information extracted from a URL,
/// including its type (YouTube, news), embedded URLs, and final destination
class ProcessedUrlInfo {
  /// The original URL that was processed
  final String originalUrl;

  /// The final URL after following redirects
  String finalUrl;

  /// Embedded URL if found in query parameters
  String? embeddedUrl;

  /// Whether the URL is a YouTube URL
  bool isYouTubeUrl;

  /// Whether the URL is a news article URL
  bool isNewsUrl;

  /// YouTube video ID if the URL is a YouTube URL
  String? youtubeVideoId;

  ProcessedUrlInfo({
    required this.originalUrl,
    required this.finalUrl,
    required this.embeddedUrl,
    required this.isYouTubeUrl,
    required this.isNewsUrl,
    required this.youtubeVideoId,
  });

  /// The most relevant URL to use for content extraction
  ///
  /// This is the embedded URL if available, otherwise the final URL
  String get bestUrl => embeddedUrl ?? finalUrl;

  @override
  String toString() => 'ProcessedUrlInfo{'
      'originalUrl: $originalUrl, '
      'finalUrl: $finalUrl, '
      'embeddedUrl: $embeddedUrl, '
      'isYouTubeUrl: $isYouTubeUrl, '
      'isNewsUrl: $isNewsUrl, '
      'youtubeVideoId: $youtubeVideoId'
      '}';
}

/// A utility class for resolving URLs, including following redirects
/// to determine the final destination of a URL.
class UrlResolver {
  static const _logName = 'UrlResolver';

  /// Store a default User-Agent for HTTP requests
  static const String defaultUserAgent =
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';

  /// Create an HTTP client
  /// This method is used to create an HTTP client for URL resolution
  /// It can be overridden in tests to provide a mock client
  static http.Client _createHttpClient() {
    try {
      // Check if we're in a test environment by looking for a global variable
      // This is a safer approach than using reflection which may not work in all environments
      // In the test, we'll set a global HTTP client via the UrlResolverTestUtils

      // First, try to access the test utilities via a global function
      // This is a simpler approach that doesn't rely on reflection
      final httpClient = _getTestHttpClient();
      if (httpClient != null) {
        return httpClient;
      }
    } catch (e) {
      // If the test utilities are not available, just create a new client
      // This will happen in production code
    }
    return http.Client();
  }

  /// Get a test HTTP client if available
  /// This is a separate method to make it easier to mock in tests
  static http.Client? _getTestHttpClient() {
    // This will be replaced in tests
    return null;
  }

  /// Follow redirects to get the final URL (useful for shorteners and tracking links)
  ///
  /// This method follows HTTP redirects up to maxRedirects times to find the
  /// final destination of a URL. It handles various redirect methods including
  /// 301, 302, 303, and 307 status codes.
  ///
  /// Parameters:
  /// - url: The starting URL to resolve
  /// - maxRedirects: Maximum number of redirects to follow (default: 5)
  /// - timeout: Maximum time to wait for each request (default: 5 seconds)
  ///
  /// Returns: The final URL after following all redirects, or the original URL if
  /// no redirects were found or in case of error.
  static Future<UrlInfo> resolveUrl(
    String url, {
    int maxRedirects = 3,
    Duration timeout = const Duration(seconds: 3),
  }) async {
    if (url.isEmpty) {
      appLog.warning('Empty URL provided to resolveUrl', name: _logName);
      return UrlInfo(originalUrl: url, finalUrl: url);
    }

    // Fast path: If it's already a YouTube URL, don't try to resolve it further
    if (YouTubeUrlHandling.isYouTubeUrl(url)) {
      appLog.debug('URL is already a YouTube URL, skipping resolution: $url', name: _logName);
      return UrlInfo(originalUrl: url, finalUrl: url);
    }

    String currentUrl = url;
    int redirectCount = 0;
    final httpClient = _createHttpClient();

    try {
      appLog.debug('Starting URL resolution for: $url', name: _logName);

      while (redirectCount < maxRedirects) {
        try {
          // Log before parsing
          appLog.debug('Parsing URL for HEAD request ($redirectCount): $currentUrl',
              name: _logName);
          final uri = Uri.parse(currentUrl);

          // Use HEAD request for efficiency when checking redirects
          appLog.debug('Attempting HEAD request ($redirectCount): $uri',
              name: _logName); // Log the parsed URI
          final response = await httpClient.head(
            uri, // Use the parsed URI
            headers: {'User-Agent': defaultUserAgent},
          ).timeout(timeout);
          appLog.debug('HEAD request completed. Status: ${response.statusCode}', name: _logName);

          // If we got a redirect status code and a location header
          if (response.statusCode >= 300 &&
              response.statusCode < 400 &&
              response.headers.containsKey('location')) {
            final location = response.headers['location']!;

            // Handle relative URLs
            if (location.startsWith('/')) {
              final uri = Uri.parse(currentUrl);
              currentUrl = '${uri.scheme}://${uri.host}$location';
            } else {
              currentUrl = location;
            }

            appLog.debug('Following redirect ($redirectCount): $currentUrl', name: _logName);
            redirectCount++;
          } else if (currentUrl.contains('search.app/') && response.statusCode == 200) {
            // Special handling for search.app URLs that don't respond to HEAD with redirects
            appLog.debug('search.app URL detected, trying GET request to find redirects',
                name: _logName);

            try {
              // Use a GET request to check for client-side redirects in search.app URLs
              // Use a shorter timeout to prevent hanging - use the main timeout now
              appLog.debug('Attempting GET request for search.app: $currentUrl', name: _logName);
              final getResponse = await httpClient.get(
                Uri.parse(currentUrl),
                headers: {'User-Agent': defaultUserAgent},
              ).timeout(timeout); // Use the passed-in timeout
              appLog.debug(
                  'GET request for search.app completed. Status: ${getResponse.statusCode}',
                  name: _logName);

              final body = getResponse.body.toLowerCase();
              // Log a snippet of the body
              final bodySnippet = body.length > 200 ? '${body.substring(0, 200)}...' : body;
              appLog.debug('search.app response body snippet: $bodySnippet', name: _logName);

              // Look for YouTube video ID in the page content
              if (body.contains('youtube.com')) {
                // First try to match watch?v= format
                final youtubeRegex = RegExp(r'youtube\.com/(?:watch\?v=|embed/)([a-zA-Z0-9_-]{11})',
                    caseSensitive: false);
                final youtubeMatch = youtubeRegex.firstMatch(body);

                if (youtubeMatch != null && youtubeMatch.groupCount >= 1) {
                  final videoId = youtubeMatch.group(1)!;
                  final youtubeUrl = 'https://www.youtube.com/watch?v=$videoId';
                  appLog.debug('Found YouTube URL in search.app content: $youtubeUrl',
                      name: _logName);
                  currentUrl = youtubeUrl;
                  redirectCount++;
                  continue; // Continue the main redirect loop
                }
                appLog.debug('YouTube URL regex (watch?v=) did not match.', name: _logName);

                // If we couldn't find a match with the first regex, try a more general approach
                // This will catch cases where the URL is in an href attribute
                final hrefRegex = RegExp(
                    r'''href=['"](https?://(?:www\.)?youtube\.com/[^'"]*)['"]''',
                    caseSensitive: false);
                final hrefMatch = hrefRegex.firstMatch(body);

                if (hrefMatch != null && hrefMatch.groupCount >= 1) {
                  final youtubeUrl = hrefMatch.group(1)!;
                  appLog.debug('Found YouTube URL in href attribute: $youtubeUrl', name: _logName);
                  currentUrl = youtubeUrl;
                  redirectCount++;
                  continue; // Continue the main redirect loop
                }
                appLog.debug('YouTube URL regex (href=) did not match.', name: _logName);
              } else {
                appLog.debug('Response body does not contain "youtube.com".', name: _logName);
              }
            } catch (e, s) {
              // Add stack trace
              appLog.warning('Error processing search.app URL with GET: $currentUrl',
                  name: _logName, error: e, stackTrace: s); // Log full error and stack trace
              // Don't break here - just continue with the current URL
              // This prevents the URL resolution from failing completely when there's an error
            }

            // If we couldn't find a redirect, just use the current URL
            appLog.debug('Reached final URL (after search.app check): $currentUrl', name: _logName);
            break;
          } else {
            appLog.debug('Reached final URL: $currentUrl', name: _logName);
            break;
          }
        } catch (e, s) {
          // Catch Object to include Errors
          // Handle timeouts and other errors
          appLog.warning(
              'Error during HEAD request or redirect following for $currentUrl, stopping resolution',
              name: _logName,
              error: e,
              stackTrace: s); // Log full error and stack trace
          break; // Stop resolution on error
        }
      }

      if (redirectCount >= maxRedirects) {
        appLog.warning('Maximum redirect limit reached for $url', name: _logName);
      }

      appLog.debug('URL resolution complete. Original: $url, Final: $currentUrl', name: _logName);
      return UrlInfo(originalUrl: url, finalUrl: currentUrl);
    } finally {
      httpClient.close();
    }
  }

  /// Extract a URL from text content and resolve it
  ///
  /// This method combines URL extraction and resolution in one step.
  /// It first extracts a URL from the provided content (if present),
  /// then resolves that URL to its final destination.
  ///
  /// Parameters:
  /// - content: Text content that may contain a URL
  /// - extractFn: Optional custom function to extract URL from content
  /// - maxRedirects: Maximum number of redirects to follow (default: 5)
  ///
  /// Returns: A UrlInfo object with both original and final URLs, or null if no URL was found
  static Future<UrlInfo?> extractAndResolveUrl(
    String content, {
    String? Function(String)? extractFn,
    int maxRedirects = 5,
  }) async {
    // Use provided extraction function or default to extractFirstUrl
    final extract = extractFn ?? extractFirstUrl;
    final url = extract(content);

    if (url == null || url.isEmpty) {
      return null;
    }

    return await resolveUrl(url, maxRedirects: maxRedirects);
  }

  /// Extract embedded URL from query parameters
  ///
  /// This method extracts URLs that are embedded in query parameters of wrapper URLs
  /// Common patterns include:
  /// - search.app/?link=https://example.com
  /// - example.com/redirect?url=https://target.com
  /// - shortener.com/r?u=https://destination.com
  ///
  /// Returns the extracted URL or null if none is found
  static String? extractEmbeddedUrl(String url) {
    try {
      appLog.debug('Extracting embedded URL from: $url', name: _logName);

      // Parse the URL
      final uri = Uri.parse(url);

      // Check for common wrapper patterns

      // Pattern 1: search.app/?link=URL format (used by Google)
      if (uri.host.contains('search.app')) {
        // Check for 'link' parameter which often contains the actual content URL
        final linkParam = uri.queryParameters['link'];
        if (linkParam != null && linkParam.isNotEmpty) {
          // URL decode the parameter
          String decodedUrl = Uri.decodeComponent(linkParam);

          // Further decode if it's still encoded (sometimes double encoding is used)
          if (decodedUrl.contains('%')) {
            try {
              decodedUrl = Uri.decodeComponent(decodedUrl);
            } catch (e) {
              // If this fails, just use the single-decoded version
            }
          }

          appLog.debug('Extracted embedded URL from link parameter: $decodedUrl', name: _logName);
          return decodedUrl;
        }
      }

      // Check common query parameters that might contain URLs
      final commonUrlParams = ['url', 'u', 'target', 'dest', 'destination', 'redirect', 'r'];

      for (final param in commonUrlParams) {
        final value = uri.queryParameters[param];
        if (value != null && value.isNotEmpty) {
          try {
            final decodedUrl = Uri.decodeComponent(value);
            if (isValidUrl(decodedUrl)) {
              appLog.debug('Extracted embedded URL from $param parameter: $decodedUrl',
                  name: _logName);
              return decodedUrl;
            }
          } catch (e) {
            // Continue to next parameter if decoding fails
          }
        }
      }

      // No embedded URL found
      return null;
    } catch (e) {
      appLog.warning('Error extracting embedded URL: $url', name: _logName, error: e);
      return null;
    }
  }

  /// Extract and resolve an embedded URL
  ///
  /// This method combines embedded URL extraction and resolution:
  /// 1. First checks if the URL contains an embedded URL in query parameters
  /// 2. If found, resolves that embedded URL to its final destination
  /// 3. If not found, resolves the original URL
  ///
  /// Returns a UrlInfo object with both original and final URLs
  static Future<UrlInfo> extractAndResolveEmbeddedUrl(
    String url, {
    int maxRedirects = 5,
  }) async {
    // First check if this URL contains an embedded URL
    final embeddedUrl = extractEmbeddedUrl(url);

    if (embeddedUrl != null && embeddedUrl.isNotEmpty) {
      appLog.debug('Found embedded URL, resolving: $embeddedUrl', name: _logName);
      // Resolve the embedded URL
      return await resolveUrl(embeddedUrl, maxRedirects: maxRedirects);
    }

    // If no embedded URL, resolve the original URL
    return await resolveUrl(url, maxRedirects: maxRedirects);
  }

  /// Comprehensive URL processing that combines all URL handling strategies
  ///
  /// This is the recommended entry point for all URL processing as it:
  /// 1. First tries to extract embedded URLs (fast path, no network requests)
  /// 2. If needed, follows redirects to handle URL shorteners
  /// 3. Returns detailed information about the URL including its type
  ///
  /// Parameters:
  /// - url: The URL to process
  /// - checkYouTube: Whether to check if the URL is a YouTube URL
  /// - checkNews: Whether to check if the URL is a news article URL
  /// - maxRedirects: Maximum number of redirects to follow
  /// - timeout: Maximum time to wait for each request (default: 3 seconds)
  ///
  /// Returns a ProcessedUrlInfo object with comprehensive information
  static Future<ProcessedUrlInfo> processUrl(
    String url, {
    bool checkYouTube = true,
    bool checkNews = true,
    int maxRedirects = 5,
    Duration timeout = const Duration(seconds: 3),
  }) async {
    try {
      appLog.debug('Processing URL with comprehensive strategy: $url', name: _logName);

      // Start building the result
      final result = ProcessedUrlInfo(
        originalUrl: url,
        finalUrl: url,
        embeddedUrl: null,
        isYouTubeUrl: false,
        isNewsUrl: false,
        youtubeVideoId: null,
      );

      // Step 1: Try to extract embedded URL first (fast path)
      final embeddedUrl = extractEmbeddedUrl(url);
      if (embeddedUrl != null && embeddedUrl.isNotEmpty) {
        appLog.debug('Found embedded URL: $embeddedUrl', name: _logName);
        result.embeddedUrl = embeddedUrl;

        // Use the embedded URL for further processing
        final urlToCheck = embeddedUrl;

        // Check if it's a YouTube URL
        if (checkYouTube && YouTubeUrlHandling.isYouTubeUrl(urlToCheck)) {
          result.isYouTubeUrl = true;
          result.youtubeVideoId = YouTubeUrlHandling.extractVideoId(urlToCheck);
          appLog.debug('Embedded URL is a YouTube URL with ID: ${result.youtubeVideoId}',
              name: _logName);
        }

        // Check if it's a news URL
        if (checkNews && NewsArticleUrlHandling.isLikelyNewsUrl(urlToCheck)) {
          result.isNewsUrl = true;
          appLog.debug('Embedded URL is a news article URL', name: _logName);
        }

        // Resolve the embedded URL to get the final URL
        final urlInfo = await resolveUrl(
          urlToCheck,
          maxRedirects: maxRedirects,
          timeout: timeout,
        );
        result.finalUrl = urlInfo.finalUrl;

        return result;
      }

      // Step 2: If no embedded URL, follow redirects
      final urlInfo = await resolveUrl(
        url,
        maxRedirects: maxRedirects,
        timeout: timeout,
      );
      result.finalUrl = urlInfo.finalUrl;

      // Check if the final URL is a YouTube URL
      if (checkYouTube && YouTubeUrlHandling.isYouTubeUrl(result.finalUrl)) {
        result.isYouTubeUrl = true;
        result.youtubeVideoId = YouTubeUrlHandling.extractVideoId(result.finalUrl);
        appLog.debug('Final URL is a YouTube URL with ID: ${result.youtubeVideoId}',
            name: _logName);
      }

      // Check if the final URL is a news URL
      if (checkNews && NewsArticleUrlHandling.isLikelyNewsUrl(result.finalUrl)) {
        result.isNewsUrl = true;
        appLog.debug('Final URL is a news article URL', name: _logName);
      }

      // Step 3: Check if the final URL has an embedded URL (sometimes redirects lead to wrapper URLs)
      final finalEmbeddedUrl = extractEmbeddedUrl(result.finalUrl);
      if (finalEmbeddedUrl != null && finalEmbeddedUrl != result.finalUrl) {
        appLog.debug('Final URL contains an embedded URL: $finalEmbeddedUrl', name: _logName);
        result.embeddedUrl = finalEmbeddedUrl;

        // Check the embedded URL type
        if (checkYouTube &&
            !result.isYouTubeUrl &&
            YouTubeUrlHandling.isYouTubeUrl(finalEmbeddedUrl)) {
          result.isYouTubeUrl = true;
          result.youtubeVideoId = YouTubeUrlHandling.extractVideoId(finalEmbeddedUrl);
          appLog.debug(
              'Embedded URL in final URL is a YouTube URL with ID: ${result.youtubeVideoId}',
              name: _logName);
        }

        if (checkNews &&
            !result.isNewsUrl &&
            NewsArticleUrlHandling.isLikelyNewsUrl(finalEmbeddedUrl)) {
          result.isNewsUrl = true;
          appLog.debug('Embedded URL in final URL is a news article URL', name: _logName);
        }
      }

      return result;
    } catch (e) {
      appLog.warning('Error processing URL: $url', name: _logName, error: e);
      // Return basic info in case of error
      return ProcessedUrlInfo(
        originalUrl: url,
        finalUrl: url,
        embeddedUrl: null,
        isYouTubeUrl: false,
        isNewsUrl: false,
        youtubeVideoId: null,
      );
    }
  }
}

/// Extension for YouTube-specific URL handling
extension YouTubeUrlHandling on UrlResolver {
  /// Check if a URL is a YouTube URL
  static bool isYouTubeUrl(String url) {
    try {
      final uri = Uri.parse(url.trim());
      final host = uri.host.toLowerCase();

      // Check for main YouTube domains
      if (host == 'youtube.com' || host == 'www.youtube.com' || host == 'youtu.be') {
        return true;
      }

      // Check for YouTube URLs with m. prefix (mobile)
      if (host == 'm.youtube.com') {
        return true;
      }

      // Check for YouTube URLs with youtube-nocookie.com (privacy-enhanced mode)
      if (host == 'youtube-nocookie.com' || host == 'www.youtube-nocookie.com') {
        return true;
      }

      return false;
    } catch (e) {
      appLog.warning('Error parsing URL: $url', name: 'UrlResolver');
      return false;
    }
  }

  /// Extract the video ID from a YouTube URL
  static String? extractVideoId(String url) {
    try {
      final uri = Uri.parse(url.trim());
      final host = uri.host.toLowerCase();
      final path = uri.path;

      String? videoId;

      // Short URL format: youtu.be/VIDEO_ID
      if (host == 'youtu.be') {
        videoId = path.startsWith('/') ? path.substring(1) : path;
        return videoId;
      }

      // YouTube Shorts
      if (path.contains('/shorts/')) {
        final segments = path.split('/');
        final shortsIndex = segments.indexOf('shorts');
        if (shortsIndex >= 0 && segments.length > shortsIndex + 1) {
          videoId = segments[shortsIndex + 1];
          return videoId;
        }
      }

      // Embedded player URL
      if (path.contains('/embed/')) {
        final segments = path.split('/');
        final embedIndex = segments.indexOf('embed');
        if (embedIndex >= 0 && segments.length > embedIndex + 1) {
          videoId = segments[embedIndex + 1];
          return videoId;
        }
      }

      // Standard watch URL
      if (uri.queryParameters.containsKey('v')) {
        videoId = uri.queryParameters['v'];
        return videoId;
      }

      return null;
    } catch (e) {
      appLog.warning('Error extracting YouTube video ID: $url', name: 'UrlResolver');
      return null;
    }
  }

  /// Extract and resolve a YouTube video ID from a URL
  ///
  /// This is the recommended entry point for YouTube URL processing as it:
  /// 1. First tries direct extraction (fast path for obvious YouTube URLs)
  /// 2. If that fails, follows redirects to handle URL shorteners
  /// 3. Checks for embedded YouTube URLs in query parameters
  static Future<String?> extractVideoIdWithRedirects(String url) async {
    try {
      // Fast path: First try direct extraction if it's already a YouTube URL
      if (isYouTubeUrl(url)) {
        final directVideoId = extractVideoId(url);
        if (directVideoId != null) {
          return directVideoId;
        }
      }

      // If not immediately recognizable as YouTube, resolve redirects and check for embedded URLs
      final urlInfo = await UrlResolver.extractAndResolveEmbeddedUrl(url);
      final resolvedUrl = urlInfo.finalUrl;

      // Check if the resolved URL is a YouTube URL
      if (isYouTubeUrl(resolvedUrl)) {
        return extractVideoId(resolvedUrl);
      }

      return null;
    } catch (e) {
      appLog.warning('Error extracting YouTube video ID with redirects: $url', name: 'UrlResolver');
      return null;
    }
  }

  /// Generate a thumbnail URL from a video ID
  static String getThumbnailUrl(String videoId, {String quality = 'mqdefault'}) {
    return 'https://img.youtube.com/vi/$videoId/$quality.jpg';
  }

  /// Generate a watch URL for a video ID
  static String getWatchUrl(String videoId) {
    return 'https://www.youtube.com/watch?v=$videoId';
  }

  /// Generate an embedded player URL for a video ID
  static String getEmbedUrl(String videoId) {
    return 'https://www.youtube.com/embed/$videoId';
  }
}

/// Extension for News article URL handling
extension NewsArticleUrlHandling on UrlResolver {
  /// Check if a URL is likely to be a news article
  static bool isLikelyNewsUrl(String url) {
    try {
      final uri = Uri.parse(url.trim());
      final host = uri.host.toLowerCase();
      final path = uri.path.toLowerCase();

      // Common news domains
      final newsDomains = [
        'nytimes.com',
        'washingtonpost.com',
        'bbc.com',
        'bbc.co.uk',
        'cnn.com',
        'reuters.com',
        'apnews.com',
        'npr.org',
        'theguardian.com',
        'wsj.com',
        'bloomberg.com',
        'ft.com',
        'aljazeera.com',
        'news.google.com',
        'news.yahoo.com'
      ];

      // Check if the domain is a known news site
      for (final domain in newsDomains) {
        if (host.endsWith(domain)) {
          return true;
        }
      }

      // Check for common news URL patterns
      final newsPatterns = [
        '/news/',
        '/article/',
        '/story/',
        '/world/',
        '/politics/',
        '/business/',
        '/technology/',
        '/science/',
        '/health/',
        '/sports/',
        '/entertainment/',
        '/opinion/',
        '/editorial/'
      ];

      for (final pattern in newsPatterns) {
        if (path.contains(pattern)) {
          return true;
        }
      }

      return false;
    } catch (e) {
      return false;
    }
  }
}
