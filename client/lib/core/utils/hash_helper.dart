import 'dart:convert';
import 'dart:io';
import 'package:crypto/crypto.dart';

class HashHelper {
  /// Calculate MD5 hash of a file's content
  static Future<String> calculateFileHash(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) return '';

      final bytes = await file.readAsBytes();
      return calculateBytesHash(bytes);
    } catch (e) {
      return '';
    }
  }

  /// Calculate MD5 hash of bytes
  static String calculateBytesHash(List<int> bytes) {
    return md5.convert(bytes).toString();
  }

  /// Calculate MD5 hash of a string
  static String calculateStringHash(String content) {
    return md5.convert(utf8.encode(content)).toString();
  }
}
