import 'dart:async';
import 'package:flutter/services.dart';
import 'package:promz_common/promz_common.dart';

const _logName = 'DeepLinkUtils';

/// Handles deep linking for the application.
///
/// This utility class manages incoming deep links from native platform channels
/// and provides a stream for other parts of the app to listen to.
class DeepLinkUtils {
  static const _channel = MethodChannel('ai.promz/deeplink');
  static final _controller = StreamController<Uri>.broadcast();

  /// Stream of incoming deep links.
  static Stream<Uri> get links => _controller.stream;

  static bool _isInitialized = false;

  /// Initializes the deep link handler.
  ///
  /// Sets up the method call handler to receive links from the native side.
  /// This should be called once at application startup.
  static void initialize() {
    if (_isInitialized) {
      appLog.debug('DeepLinkUtils already initialized.', name: _logName);
      return;
    }

    // Set up message handler for platform communication
    try {
      _channel.setMethodCallHandler(_handleMethod);
      appLog.info('DeepLinkUtils initialized and method call handler set.', name: _logName);
    } catch (e) {
      // This will happen on platforms that don't support the method channel
      appLog.debug('DeepLinkUtils initialized with limited functionality.', name: _logName);
    }

    _isInitialized = true;
  }

  /// Handles method calls from the native platform.
  ///
  /// Specifically listens for 'onLink' calls which carry the deep link URI.
  static Future<void> _handleMethod(MethodCall call) async {
    if (call.method == 'onLink') {
      final linkString = call.arguments as String?;
      if (linkString != null && linkString.isNotEmpty) {
        appLog.debug('Received link from native: $linkString', name: _logName);
        try {
          final uri = Uri.parse(linkString);
          // Add the parsed Uri directly
          _controller.add(uri);
        } catch (e) {
          appLog.error('Error parsing received link "$linkString": $e', name: _logName);
        }
      } else {
        appLog.warning('Received null or empty link from native.', name: _logName);
      }
    }
  }

  /// Manually notifies the stream of a received link.
  ///
  /// This can be used if a link is received through a mechanism other than
  /// the native method channel (e.g., from shared content).
  static void notifyLinkReceived(String linkString) {
    if (linkString.isNotEmpty) {
      appLog.info('Manually notifying link to DeepLinkUtils stream: $linkString', name: _logName);
      try {
        final uri = Uri.parse(linkString);
        // Add the parsed Uri directly
        _controller.add(uri);
      } catch (e) {
        appLog.error('Error parsing link for manual notification "$linkString": $e',
            name: _logName);
      }
    } else {
      appLog.warning('Attempted to notify a null or empty link.', name: _logName);
    }
  }

  /// Disposes the stream controller.
  ///
  /// Should be called when the application is shutting down to prevent memory leaks.
  static void dispose() {
    _controller.close();
    _isInitialized = false; // Reset initialization state if needed for app lifecycle
    appLog.info('DeepLinkUtils disposed.', name: _logName);
  }
}
