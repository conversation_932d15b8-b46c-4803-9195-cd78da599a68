import 'package:promz/core/utils/url_resolver.dart';
import 'package:promz_common/promz_common.dart';

/// Utility class for extracting URLs from text and processing them
///
/// This class serves as the primary entry point for URL extraction and processing,
/// using common URL utilities from promz_common while delegating network-dependent
/// operations to UrlResolver.
class UrlExtractor {
  static const _logName = 'UrlExtractor';

  /// Unified URL extraction and processing function
  ///
  /// This comprehensive function combines URL extraction from text,
  /// redirect following, embedded URL extraction, and URL type detection
  /// into a single call. It's the recommended entry point for all URL processing.
  ///
  /// Parameters:
  /// - input: The input text which may contain a URL or be a URL itself
  /// - maxRedirects: Maximum number of redirects to follow (default: 5)
  /// - timeout: Maximum time to wait for each request (default: 10 seconds)
  ///
  /// Returns: A ProcessedUrlInfo object with all URL details, or null if no URL found
  static Future<ProcessedUrlInfo?> extractUrl(
    String input, {
    int maxRedirects = 5,
    Duration timeout = const Duration(seconds: 10),
  }) async {
    try {
      appLog.debug(
          'Starting unified URL extraction for input: "${input.length > 50 ? '${input.substring(0, 50)}...' : input}"',
          name: _logName);

      // Step 1: Determine if input is already a URL or contains a URL
      String? url = input.trim();
      bool inputIsUrl = isValidUrl(url);

      if (!inputIsUrl) {
        // Try to extract URL from text
        url = extractFirstUrl(input);
        if (url == null) {
          appLog.debug('No URL found in input text', name: _logName);
          return null;
        }
        appLog.debug('Extracted URL from text: $url', name: _logName);
      }

      // Step 2: Process the URL comprehensively using URL resolver
      final result = await UrlResolver.processUrl(
        url,
        checkYouTube: true,
        checkNews: true,
        maxRedirects: maxRedirects,
        timeout: timeout,
      );

      appLog.debug('URL extraction complete: $result', name: _logName);
      return result;
    } catch (e) {
      appLog.warning('Error in extractUrl: ${e.toString()}', name: _logName);
      return null;
    }
  }

  /// Extract and resolve a YouTube video ID from a URL
  ///
  /// This is a convenience method that delegates to the URL resolver
  static Future<String?> extractYouTubeVideoId(String urlOrText) async {
    // First try to extract URL if the input is text
    final url = extractFirstUrl(urlOrText) ?? urlOrText;

    // Then use the resolver to extract the video ID
    return YouTubeUrlHandling.extractVideoIdWithRedirects(url);
  }
}
