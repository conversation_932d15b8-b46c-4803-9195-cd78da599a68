import 'dart:io';
import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:drift/drift.dart';
import 'package:drift/native.dart';
import 'package:promz_common/promz_common.dart';
import 'package:promz/database/database.dart' as db;

/// Database utility functions for schema and version management
class DatabaseUtils {
  /// Expected schema version
  static const int expectedSchemaVersion = 8;

  /// Schema version description
  static const String schemaVersionDescription =
      'Rename content field to subtitle in prompts table';

  /// Converts a database Prompt to the shared PromptModel
  static PromptModel createPromptModelFromDb(db.Prompt data) {
    List<String> variablesList = [];

    // Parse variables from the database if available
    if (data.variables.isNotEmpty) {
      try {
        variablesList = List<String>.from(json.decode(data.variables));
      } catch (e) {
        // If there's an error parsing the JSON, set to empty list
        variablesList = [];
      }
    }

    return PromptModel(
      id: data.id,
      title: data.title,
      subtitle: data.subtitle,
      keywords: List<String>.from(json.decode(data.keywords)),
      variables: variablesList,
      categoryId: data.categoryId,
      // Add any other fields that might be needed
    );
  }

  /// Checks if the database has the expected schema version
  /// Returns true if the database is valid, false if it needs recreation
  static Future<bool> checkSchemaVersion(File dbFile) async {
    if (!await dbFile.exists()) {
      return false;
    }

    NativeDatabase? tempDb;
    DatabaseConnection? connection;
    QueryExecutor? executor;

    try {
      // Create a temporary database connection
      tempDb = NativeDatabase(dbFile);
      executor = tempDb;
      connection = DatabaseConnection(executor);

      // First check if schema_version table exists
      final tables = await _queryTableNames(connection);
      if (!tables.contains('schema_version')) {
        appLog.debug('schema_version table not found, database needs recreation',
            name: 'DatabaseUtils');
        return false;
      }

      // Check schema version
      final results = await _querySchemaVersion(connection);
      if (results.isEmpty) {
        appLog.debug('No schema version found, database needs recreation', name: 'DatabaseUtils');
        return false;
      }

      final version = results.first['version'] as int?;
      if (version != expectedSchemaVersion) {
        appLog.debug(
            'Schema version $version does not match expected version $expectedSchemaVersion',
            name: 'DatabaseUtils');
        return false;
      }

      appLog.debug('Schema version $expectedSchemaVersion confirmed', name: 'DatabaseUtils');
      return true;
    } catch (e) {
      appLog.error('Error checking schema version', name: 'DatabaseUtils', error: e);
      return false;
    } finally {
      // Clean up resources
      await tempDb?.close();
    }
  }

  /// Recreates the database by copying it from assets
  static Future<void> recreateDatabase(File dbFile) async {
    try {
      // Delete existing file if it exists
      if (await dbFile.exists()) {
        await dbFile.delete();
      }

      // Create parent directories if they don't exist
      final dir = dbFile.parent;
      if (!await dir.exists()) {
        await dir.create(recursive: true);
      }

      // Copy fresh DB from assets
      appLog.debug('Copying database from assets:', name: 'DatabaseUtils');
      final bytes = await rootBundle.load('assets/promz.db');
      await dbFile.writeAsBytes(bytes.buffer.asUint8List());

      appLog.debug('Database recreated successfully', name: 'DatabaseUtils');
    } catch (e) {
      appLog.error('Failed to recreate database: ${dbFile.path}', name: 'DatabaseUtils', error: e);
      rethrow;
    }
  }

  /// Query table names from the database
  static Future<List<String>> _queryTableNames(DatabaseConnection connection) async {
    final results = await connection.executor.runSelect(
        'SELECT name FROM sqlite_master WHERE type=? AND name NOT LIKE ?', ['table', 'sqlite_%']);

    return results.map((row) => row['name'] as String).toList();
  }

  /// Query schema version from the database
  static Future<List<Map<String, dynamic>>> _querySchemaVersion(
      DatabaseConnection connection) async {
    try {
      final results = await connection.executor.runSelect(
          'SELECT version FROM schema_version WHERE version = ? LIMIT 1', [expectedSchemaVersion]);

      return results;
    } catch (e) {
      appLog.error('Error querying schema version', name: 'DatabaseUtils', error: e);
      return [];
    }
  }
}
