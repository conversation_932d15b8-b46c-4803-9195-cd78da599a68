// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'database.dart';

// ignore_for_file: type=lint
class $CategoriesTable extends Categories with drift.TableInfo<$CategoriesTable, Category> {
  @override
  final drift.GeneratedDatabase attachedDatabase;
  final String? _alias;
  $CategoriesTable(this.attachedDatabase, [this._alias]);
  @override
  late final drift.GeneratedColumnWithTypeConverter<String, String> id =
      drift.GeneratedColumn<String>('id', aliasedName, false,
              type: DriftSqlType.string, requiredDuringInsert: true)
          .withConverter<String>($CategoriesTable.$converterid);
  static const drift.VerificationMeta _titleMeta = const drift.VerificationMeta('title');
  @override
  late final drift.GeneratedColumn<String> title =
      drift.GeneratedColumn<String>('title', aliasedName, false,
          additionalChecks: GeneratedColumn.checkTextLength(
            minTextLength: 1,
          ),
          type: DriftSqlType.string,
          requiredDuringInsert: true);
  static const drift.VerificationMeta _subtitleMeta = const drift.VerificationMeta('subtitle');
  @override
  late final drift.GeneratedColumn<String> subtitle = drift.GeneratedColumn<String>(
      'subtitle', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      defaultValue: const drift.Constant(''));
  static const drift.VerificationMeta _iconMeta = const drift.VerificationMeta('icon');
  @override
  late final drift.GeneratedColumn<String> icon = drift.GeneratedColumn<String>(
      'icon', aliasedName, true,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      defaultValue: const drift.Constant('folder'));
  @override
  late final drift.GeneratedColumnWithTypeConverter<String, String> keywords =
      drift.GeneratedColumn<String>('keywords', aliasedName, false,
              type: DriftSqlType.string,
              requiredDuringInsert: false,
              defaultValue: const drift.Constant('[]'))
          .withConverter<String>($CategoriesTable.$converterkeywords);
  static const drift.VerificationMeta _createdAtMeta = const drift.VerificationMeta('createdAt');
  @override
  late final drift.GeneratedColumn<DateTime> createdAt = drift.GeneratedColumn<DateTime>(
      'created_at', aliasedName, false,
      type: DriftSqlType.dateTime,
      requiredDuringInsert: false,
      defaultValue: drift.Constant(DateTime.now()));
  static const drift.VerificationMeta _updatedAtMeta = const drift.VerificationMeta('updatedAt');
  @override
  late final drift.GeneratedColumn<DateTime> updatedAt = drift.GeneratedColumn<DateTime>(
      'updated_at', aliasedName, false,
      type: DriftSqlType.dateTime,
      requiredDuringInsert: false,
      defaultValue: drift.Constant(DateTime.now()));
  @override
  List<drift.GeneratedColumn> get $columns =>
      [id, title, subtitle, icon, keywords, createdAt, updatedAt];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'categories';
  @override
  drift.VerificationContext validateIntegrity(drift.Insertable<Category> instance,
      {bool isInserting = false}) {
    final context = drift.VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('title')) {
      context.handle(_titleMeta, title.isAcceptableOrUnknown(data['title']!, _titleMeta));
    } else if (isInserting) {
      context.missing(_titleMeta);
    }
    if (data.containsKey('subtitle')) {
      context.handle(
          _subtitleMeta, subtitle.isAcceptableOrUnknown(data['subtitle']!, _subtitleMeta));
    }
    if (data.containsKey('icon')) {
      context.handle(_iconMeta, icon.isAcceptableOrUnknown(data['icon']!, _iconMeta));
    }
    if (data.containsKey('created_at')) {
      context.handle(
          _createdAtMeta, createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta));
    }
    if (data.containsKey('updated_at')) {
      context.handle(
          _updatedAtMeta, updatedAt.isAcceptableOrUnknown(data['updated_at']!, _updatedAtMeta));
    }
    return context;
  }

  @override
  Set<drift.GeneratedColumn> get $primaryKey => {id};
  @override
  Category map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return Category(
      id: $CategoriesTable.$converterid.fromSql(
          attachedDatabase.typeMapping.read(DriftSqlType.string, data['${effectivePrefix}id'])!),
      title:
          attachedDatabase.typeMapping.read(DriftSqlType.string, data['${effectivePrefix}title'])!,
      subtitle: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}subtitle']),
      icon: attachedDatabase.typeMapping.read(DriftSqlType.string, data['${effectivePrefix}icon']),
      keywords: $CategoriesTable.$converterkeywords.fromSql(attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}keywords'])!),
      createdAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}created_at'])!,
      updatedAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}updated_at'])!,
    );
  }

  @override
  $CategoriesTable createAlias(String alias) {
    return $CategoriesTable(attachedDatabase, alias);
  }

  static drift.TypeConverter<String, String> $converterid = const UuidConverter();
  static drift.TypeConverter<String, String> $converterkeywords = const JsonConverter();
}

class Category extends drift.DataClass implements drift.Insertable<Category> {
  final String id;
  final String title;
  final String? subtitle;
  final String? icon;
  final String keywords;
  final DateTime createdAt;
  final DateTime updatedAt;
  const Category(
      {required this.id,
      required this.title,
      this.subtitle,
      this.icon,
      required this.keywords,
      required this.createdAt,
      required this.updatedAt});
  @override
  Map<String, drift.Expression> toColumns(bool nullToAbsent) {
    final map = <String, drift.Expression>{};
    {
      map['id'] = drift.Variable<String>($CategoriesTable.$converterid.toSql(id));
    }
    map['title'] = drift.Variable<String>(title);
    if (!nullToAbsent || subtitle != null) {
      map['subtitle'] = drift.Variable<String>(subtitle);
    }
    if (!nullToAbsent || icon != null) {
      map['icon'] = drift.Variable<String>(icon);
    }
    {
      map['keywords'] = drift.Variable<String>($CategoriesTable.$converterkeywords.toSql(keywords));
    }
    map['created_at'] = drift.Variable<DateTime>(createdAt);
    map['updated_at'] = drift.Variable<DateTime>(updatedAt);
    return map;
  }

  CategoriesCompanion toCompanion(bool nullToAbsent) {
    return CategoriesCompanion(
      id: drift.Value(id),
      title: drift.Value(title),
      subtitle:
          subtitle == null && nullToAbsent ? const drift.Value.absent() : drift.Value(subtitle),
      icon: icon == null && nullToAbsent ? const drift.Value.absent() : drift.Value(icon),
      keywords: drift.Value(keywords),
      createdAt: drift.Value(createdAt),
      updatedAt: drift.Value(updatedAt),
    );
  }

  factory Category.fromJson(Map<String, dynamic> json, {ValueSerializer? serializer}) {
    serializer ??= drift.driftRuntimeOptions.defaultSerializer;
    return Category(
      id: serializer.fromJson<String>(json['id']),
      title: serializer.fromJson<String>(json['title']),
      subtitle: serializer.fromJson<String?>(json['subtitle']),
      icon: serializer.fromJson<String?>(json['icon']),
      keywords: serializer.fromJson<String>(json['keywords']),
      createdAt: serializer.fromJson<DateTime>(json['createdAt']),
      updatedAt: serializer.fromJson<DateTime>(json['updatedAt']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= drift.driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'title': serializer.toJson<String>(title),
      'subtitle': serializer.toJson<String?>(subtitle),
      'icon': serializer.toJson<String?>(icon),
      'keywords': serializer.toJson<String>(keywords),
      'createdAt': serializer.toJson<DateTime>(createdAt),
      'updatedAt': serializer.toJson<DateTime>(updatedAt),
    };
  }

  Category copyWith(
          {String? id,
          String? title,
          drift.Value<String?> subtitle = const drift.Value.absent(),
          drift.Value<String?> icon = const drift.Value.absent(),
          String? keywords,
          DateTime? createdAt,
          DateTime? updatedAt}) =>
      Category(
        id: id ?? this.id,
        title: title ?? this.title,
        subtitle: subtitle.present ? subtitle.value : this.subtitle,
        icon: icon.present ? icon.value : this.icon,
        keywords: keywords ?? this.keywords,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
      );
  Category copyWithCompanion(CategoriesCompanion data) {
    return Category(
      id: data.id.present ? data.id.value : this.id,
      title: data.title.present ? data.title.value : this.title,
      subtitle: data.subtitle.present ? data.subtitle.value : this.subtitle,
      icon: data.icon.present ? data.icon.value : this.icon,
      keywords: data.keywords.present ? data.keywords.value : this.keywords,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
      updatedAt: data.updatedAt.present ? data.updatedAt.value : this.updatedAt,
    );
  }

  @override
  String toString() {
    return (StringBuffer('Category(')
          ..write('id: $id, ')
          ..write('title: $title, ')
          ..write('subtitle: $subtitle, ')
          ..write('icon: $icon, ')
          ..write('keywords: $keywords, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, title, subtitle, icon, keywords, createdAt, updatedAt);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is Category &&
          other.id == this.id &&
          other.title == this.title &&
          other.subtitle == this.subtitle &&
          other.icon == this.icon &&
          other.keywords == this.keywords &&
          other.createdAt == this.createdAt &&
          other.updatedAt == this.updatedAt);
}

class CategoriesCompanion extends drift.UpdateCompanion<Category> {
  final drift.Value<String> id;
  final drift.Value<String> title;
  final drift.Value<String?> subtitle;
  final drift.Value<String?> icon;
  final drift.Value<String> keywords;
  final drift.Value<DateTime> createdAt;
  final drift.Value<DateTime> updatedAt;
  final drift.Value<int> rowid;
  const CategoriesCompanion({
    this.id = const drift.Value.absent(),
    this.title = const drift.Value.absent(),
    this.subtitle = const drift.Value.absent(),
    this.icon = const drift.Value.absent(),
    this.keywords = const drift.Value.absent(),
    this.createdAt = const drift.Value.absent(),
    this.updatedAt = const drift.Value.absent(),
    this.rowid = const drift.Value.absent(),
  });
  CategoriesCompanion.insert({
    required String id,
    required String title,
    this.subtitle = const drift.Value.absent(),
    this.icon = const drift.Value.absent(),
    this.keywords = const drift.Value.absent(),
    this.createdAt = const drift.Value.absent(),
    this.updatedAt = const drift.Value.absent(),
    this.rowid = const drift.Value.absent(),
  })  : id = drift.Value(id),
        title = drift.Value(title);
  static drift.Insertable<Category> custom({
    drift.Expression<String>? id,
    drift.Expression<String>? title,
    drift.Expression<String>? subtitle,
    drift.Expression<String>? icon,
    drift.Expression<String>? keywords,
    drift.Expression<DateTime>? createdAt,
    drift.Expression<DateTime>? updatedAt,
    drift.Expression<int>? rowid,
  }) {
    return drift.RawValuesInsertable({
      if (id != null) 'id': id,
      if (title != null) 'title': title,
      if (subtitle != null) 'subtitle': subtitle,
      if (icon != null) 'icon': icon,
      if (keywords != null) 'keywords': keywords,
      if (createdAt != null) 'created_at': createdAt,
      if (updatedAt != null) 'updated_at': updatedAt,
      if (rowid != null) 'rowid': rowid,
    });
  }

  CategoriesCompanion copyWith(
      {drift.Value<String>? id,
      drift.Value<String>? title,
      drift.Value<String?>? subtitle,
      drift.Value<String?>? icon,
      drift.Value<String>? keywords,
      drift.Value<DateTime>? createdAt,
      drift.Value<DateTime>? updatedAt,
      drift.Value<int>? rowid}) {
    return CategoriesCompanion(
      id: id ?? this.id,
      title: title ?? this.title,
      subtitle: subtitle ?? this.subtitle,
      icon: icon ?? this.icon,
      keywords: keywords ?? this.keywords,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, drift.Expression> toColumns(bool nullToAbsent) {
    final map = <String, drift.Expression>{};
    if (id.present) {
      map['id'] = drift.Variable<String>($CategoriesTable.$converterid.toSql(id.value));
    }
    if (title.present) {
      map['title'] = drift.Variable<String>(title.value);
    }
    if (subtitle.present) {
      map['subtitle'] = drift.Variable<String>(subtitle.value);
    }
    if (icon.present) {
      map['icon'] = drift.Variable<String>(icon.value);
    }
    if (keywords.present) {
      map['keywords'] =
          drift.Variable<String>($CategoriesTable.$converterkeywords.toSql(keywords.value));
    }
    if (createdAt.present) {
      map['created_at'] = drift.Variable<DateTime>(createdAt.value);
    }
    if (updatedAt.present) {
      map['updated_at'] = drift.Variable<DateTime>(updatedAt.value);
    }
    if (rowid.present) {
      map['rowid'] = drift.Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('CategoriesCompanion(')
          ..write('id: $id, ')
          ..write('title: $title, ')
          ..write('subtitle: $subtitle, ')
          ..write('icon: $icon, ')
          ..write('keywords: $keywords, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $PromptsTable extends Prompts with drift.TableInfo<$PromptsTable, Prompt> {
  @override
  final drift.GeneratedDatabase attachedDatabase;
  final String? _alias;
  $PromptsTable(this.attachedDatabase, [this._alias]);
  @override
  late final drift.GeneratedColumnWithTypeConverter<String, String> id =
      drift.GeneratedColumn<String>('id', aliasedName, false,
              type: DriftSqlType.string, requiredDuringInsert: true)
          .withConverter<String>($PromptsTable.$converterid);
  static const drift.VerificationMeta _subtitleMeta = const drift.VerificationMeta('subtitle');
  @override
  late final drift.GeneratedColumn<String> subtitle = drift.GeneratedColumn<String>(
      'subtitle', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const drift.VerificationMeta _titleMeta = const drift.VerificationMeta('title');
  @override
  late final drift.GeneratedColumn<String> title =
      drift.GeneratedColumn<String>('title', aliasedName, false,
          additionalChecks: GeneratedColumn.checkTextLength(
            minTextLength: 1,
          ),
          type: DriftSqlType.string,
          requiredDuringInsert: true);
  static const drift.VerificationMeta _sourceMeta = const drift.VerificationMeta('source');
  @override
  late final drift.GeneratedColumn<String> source =
      drift.GeneratedColumn<String>('source', aliasedName, false,
          additionalChecks: GeneratedColumn.checkTextLength(
            minTextLength: 1,
          ),
          type: DriftSqlType.string,
          requiredDuringInsert: true,
          $customConstraints: 'NOT NULL CHECK (source IN (\'local\', \'cloud\'))');
  static const drift.VerificationMeta _createdAtMeta = const drift.VerificationMeta('createdAt');
  @override
  late final drift.GeneratedColumn<DateTime> createdAt = drift.GeneratedColumn<DateTime>(
      'created_at', aliasedName, false,
      type: DriftSqlType.dateTime,
      requiredDuringInsert: false,
      defaultValue: drift.Constant(DateTime.now()));
  static const drift.VerificationMeta _isSyncedMeta = const drift.VerificationMeta('isSynced');
  @override
  late final drift.GeneratedColumn<bool> isSynced = drift.GeneratedColumn<bool>(
      'is_synced', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints: GeneratedColumn.constraintIsAlways('CHECK ("is_synced" IN (0, 1))'),
      defaultValue: const drift.Constant(false));
  @override
  late final drift.GeneratedColumnWithTypeConverter<String, String> categoryId =
      drift.GeneratedColumn<String>('category_id', aliasedName, false,
              type: DriftSqlType.string,
              requiredDuringInsert: true,
              defaultConstraints: GeneratedColumn.constraintIsAlways('REFERENCES categories (id)'))
          .withConverter<String>($PromptsTable.$convertercategoryId);
  @override
  late final drift.GeneratedColumnWithTypeConverter<String, String> keywords =
      drift.GeneratedColumn<String>('keywords', aliasedName, false,
              type: DriftSqlType.string,
              requiredDuringInsert: false,
              defaultValue: const drift.Constant('[]'))
          .withConverter<String>($PromptsTable.$converterkeywords);
  @override
  late final drift.GeneratedColumnWithTypeConverter<String, String> variables =
      drift.GeneratedColumn<String>('variables', aliasedName, false,
              type: DriftSqlType.string,
              requiredDuringInsert: false,
              defaultValue: const drift.Constant('[]'))
          .withConverter<String>($PromptsTable.$convertervariables);
  static const drift.VerificationMeta _versionMeta = const drift.VerificationMeta('version');
  @override
  late final drift.GeneratedColumn<int> version = drift.GeneratedColumn<int>(
      'version', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: false, defaultValue: const drift.Constant(1));
  static const drift.VerificationMeta _popularityScoreMeta =
      const drift.VerificationMeta('popularityScore');
  @override
  late final drift.GeneratedColumn<int> popularityScore = drift.GeneratedColumn<int>(
      'popularity_score', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: false, defaultValue: const drift.Constant(0));
  static const drift.VerificationMeta _serverSyncAtMeta =
      const drift.VerificationMeta('serverSyncAt');
  @override
  late final drift.GeneratedColumn<DateTime> serverSyncAt = drift.GeneratedColumn<DateTime>(
      'server_sync_at', aliasedName, true,
      type: DriftSqlType.dateTime, requiredDuringInsert: false);
  static const drift.VerificationMeta _lastUsedAtMeta = const drift.VerificationMeta('lastUsedAt');
  @override
  late final drift.GeneratedColumn<DateTime> lastUsedAt = drift.GeneratedColumn<DateTime>(
      'last_used_at', aliasedName, true,
      type: DriftSqlType.dateTime, requiredDuringInsert: false);
  @override
  List<drift.GeneratedColumn> get $columns => [
        id,
        subtitle,
        title,
        source,
        createdAt,
        isSynced,
        categoryId,
        keywords,
        variables,
        version,
        popularityScore,
        serverSyncAt,
        lastUsedAt
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'prompts';
  @override
  drift.VerificationContext validateIntegrity(drift.Insertable<Prompt> instance,
      {bool isInserting = false}) {
    final context = drift.VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('subtitle')) {
      context.handle(
          _subtitleMeta, subtitle.isAcceptableOrUnknown(data['subtitle']!, _subtitleMeta));
    } else if (isInserting) {
      context.missing(_subtitleMeta);
    }
    if (data.containsKey('title')) {
      context.handle(_titleMeta, title.isAcceptableOrUnknown(data['title']!, _titleMeta));
    } else if (isInserting) {
      context.missing(_titleMeta);
    }
    if (data.containsKey('source')) {
      context.handle(_sourceMeta, source.isAcceptableOrUnknown(data['source']!, _sourceMeta));
    } else if (isInserting) {
      context.missing(_sourceMeta);
    }
    if (data.containsKey('created_at')) {
      context.handle(
          _createdAtMeta, createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta));
    }
    if (data.containsKey('is_synced')) {
      context.handle(
          _isSyncedMeta, isSynced.isAcceptableOrUnknown(data['is_synced']!, _isSyncedMeta));
    }
    if (data.containsKey('version')) {
      context.handle(_versionMeta, version.isAcceptableOrUnknown(data['version']!, _versionMeta));
    }
    if (data.containsKey('popularity_score')) {
      context.handle(_popularityScoreMeta,
          popularityScore.isAcceptableOrUnknown(data['popularity_score']!, _popularityScoreMeta));
    }
    if (data.containsKey('server_sync_at')) {
      context.handle(_serverSyncAtMeta,
          serverSyncAt.isAcceptableOrUnknown(data['server_sync_at']!, _serverSyncAtMeta));
    }
    if (data.containsKey('last_used_at')) {
      context.handle(_lastUsedAtMeta,
          lastUsedAt.isAcceptableOrUnknown(data['last_used_at']!, _lastUsedAtMeta));
    }
    return context;
  }

  @override
  Set<drift.GeneratedColumn> get $primaryKey => {id};
  @override
  Prompt map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return Prompt(
      id: $PromptsTable.$converterid.fromSql(
          attachedDatabase.typeMapping.read(DriftSqlType.string, data['${effectivePrefix}id'])!),
      subtitle: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}subtitle'])!,
      title:
          attachedDatabase.typeMapping.read(DriftSqlType.string, data['${effectivePrefix}title'])!,
      source:
          attachedDatabase.typeMapping.read(DriftSqlType.string, data['${effectivePrefix}source'])!,
      createdAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}created_at'])!,
      isSynced: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}is_synced'])!,
      categoryId: $PromptsTable.$convertercategoryId.fromSql(attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}category_id'])!),
      keywords: $PromptsTable.$converterkeywords.fromSql(attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}keywords'])!),
      variables: $PromptsTable.$convertervariables.fromSql(attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}variables'])!),
      version:
          attachedDatabase.typeMapping.read(DriftSqlType.int, data['${effectivePrefix}version'])!,
      popularityScore: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}popularity_score'])!,
      serverSyncAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}server_sync_at']),
      lastUsedAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}last_used_at']),
    );
  }

  @override
  $PromptsTable createAlias(String alias) {
    return $PromptsTable(attachedDatabase, alias);
  }

  static drift.TypeConverter<String, String> $converterid = const UuidConverter();
  static drift.TypeConverter<String, String> $convertercategoryId = const UuidConverter();
  static drift.TypeConverter<String, String> $converterkeywords = const JsonConverter();
  static drift.TypeConverter<String, String> $convertervariables = const JsonConverter();
}

class Prompt extends drift.DataClass implements drift.Insertable<Prompt> {
  final String id;
  final String subtitle;
  final String title;
  final String source;
  final DateTime createdAt;
  final bool isSynced;
  final String categoryId;
  final String keywords;
  final String variables;
  final int version;
  final int popularityScore;
  final DateTime? serverSyncAt;
  final DateTime? lastUsedAt;
  const Prompt(
      {required this.id,
      required this.subtitle,
      required this.title,
      required this.source,
      required this.createdAt,
      required this.isSynced,
      required this.categoryId,
      required this.keywords,
      required this.variables,
      required this.version,
      required this.popularityScore,
      this.serverSyncAt,
      this.lastUsedAt});
  @override
  Map<String, drift.Expression> toColumns(bool nullToAbsent) {
    final map = <String, drift.Expression>{};
    {
      map['id'] = drift.Variable<String>($PromptsTable.$converterid.toSql(id));
    }
    map['subtitle'] = drift.Variable<String>(subtitle);
    map['title'] = drift.Variable<String>(title);
    map['source'] = drift.Variable<String>(source);
    map['created_at'] = drift.Variable<DateTime>(createdAt);
    map['is_synced'] = drift.Variable<bool>(isSynced);
    {
      map['category_id'] =
          drift.Variable<String>($PromptsTable.$convertercategoryId.toSql(categoryId));
    }
    {
      map['keywords'] = drift.Variable<String>($PromptsTable.$converterkeywords.toSql(keywords));
    }
    {
      map['variables'] = drift.Variable<String>($PromptsTable.$convertervariables.toSql(variables));
    }
    map['version'] = drift.Variable<int>(version);
    map['popularity_score'] = drift.Variable<int>(popularityScore);
    if (!nullToAbsent || serverSyncAt != null) {
      map['server_sync_at'] = drift.Variable<DateTime>(serverSyncAt);
    }
    if (!nullToAbsent || lastUsedAt != null) {
      map['last_used_at'] = drift.Variable<DateTime>(lastUsedAt);
    }
    return map;
  }

  PromptsCompanion toCompanion(bool nullToAbsent) {
    return PromptsCompanion(
      id: drift.Value(id),
      subtitle: drift.Value(subtitle),
      title: drift.Value(title),
      source: drift.Value(source),
      createdAt: drift.Value(createdAt),
      isSynced: drift.Value(isSynced),
      categoryId: drift.Value(categoryId),
      keywords: drift.Value(keywords),
      variables: drift.Value(variables),
      version: drift.Value(version),
      popularityScore: drift.Value(popularityScore),
      serverSyncAt: serverSyncAt == null && nullToAbsent
          ? const drift.Value.absent()
          : drift.Value(serverSyncAt),
      lastUsedAt:
          lastUsedAt == null && nullToAbsent ? const drift.Value.absent() : drift.Value(lastUsedAt),
    );
  }

  factory Prompt.fromJson(Map<String, dynamic> json, {ValueSerializer? serializer}) {
    serializer ??= drift.driftRuntimeOptions.defaultSerializer;
    return Prompt(
      id: serializer.fromJson<String>(json['id']),
      subtitle: serializer.fromJson<String>(json['subtitle']),
      title: serializer.fromJson<String>(json['title']),
      source: serializer.fromJson<String>(json['source']),
      createdAt: serializer.fromJson<DateTime>(json['createdAt']),
      isSynced: serializer.fromJson<bool>(json['isSynced']),
      categoryId: serializer.fromJson<String>(json['categoryId']),
      keywords: serializer.fromJson<String>(json['keywords']),
      variables: serializer.fromJson<String>(json['variables']),
      version: serializer.fromJson<int>(json['version']),
      popularityScore: serializer.fromJson<int>(json['popularityScore']),
      serverSyncAt: serializer.fromJson<DateTime?>(json['serverSyncAt']),
      lastUsedAt: serializer.fromJson<DateTime?>(json['lastUsedAt']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= drift.driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'subtitle': serializer.toJson<String>(subtitle),
      'title': serializer.toJson<String>(title),
      'source': serializer.toJson<String>(source),
      'createdAt': serializer.toJson<DateTime>(createdAt),
      'isSynced': serializer.toJson<bool>(isSynced),
      'categoryId': serializer.toJson<String>(categoryId),
      'keywords': serializer.toJson<String>(keywords),
      'variables': serializer.toJson<String>(variables),
      'version': serializer.toJson<int>(version),
      'popularityScore': serializer.toJson<int>(popularityScore),
      'serverSyncAt': serializer.toJson<DateTime?>(serverSyncAt),
      'lastUsedAt': serializer.toJson<DateTime?>(lastUsedAt),
    };
  }

  Prompt copyWith(
          {String? id,
          String? subtitle,
          String? title,
          String? source,
          DateTime? createdAt,
          bool? isSynced,
          String? categoryId,
          String? keywords,
          String? variables,
          int? version,
          int? popularityScore,
          drift.Value<DateTime?> serverSyncAt = const drift.Value.absent(),
          drift.Value<DateTime?> lastUsedAt = const drift.Value.absent()}) =>
      Prompt(
        id: id ?? this.id,
        subtitle: subtitle ?? this.subtitle,
        title: title ?? this.title,
        source: source ?? this.source,
        createdAt: createdAt ?? this.createdAt,
        isSynced: isSynced ?? this.isSynced,
        categoryId: categoryId ?? this.categoryId,
        keywords: keywords ?? this.keywords,
        variables: variables ?? this.variables,
        version: version ?? this.version,
        popularityScore: popularityScore ?? this.popularityScore,
        serverSyncAt: serverSyncAt.present ? serverSyncAt.value : this.serverSyncAt,
        lastUsedAt: lastUsedAt.present ? lastUsedAt.value : this.lastUsedAt,
      );
  Prompt copyWithCompanion(PromptsCompanion data) {
    return Prompt(
      id: data.id.present ? data.id.value : this.id,
      subtitle: data.subtitle.present ? data.subtitle.value : this.subtitle,
      title: data.title.present ? data.title.value : this.title,
      source: data.source.present ? data.source.value : this.source,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
      isSynced: data.isSynced.present ? data.isSynced.value : this.isSynced,
      categoryId: data.categoryId.present ? data.categoryId.value : this.categoryId,
      keywords: data.keywords.present ? data.keywords.value : this.keywords,
      variables: data.variables.present ? data.variables.value : this.variables,
      version: data.version.present ? data.version.value : this.version,
      popularityScore:
          data.popularityScore.present ? data.popularityScore.value : this.popularityScore,
      serverSyncAt: data.serverSyncAt.present ? data.serverSyncAt.value : this.serverSyncAt,
      lastUsedAt: data.lastUsedAt.present ? data.lastUsedAt.value : this.lastUsedAt,
    );
  }

  @override
  String toString() {
    return (StringBuffer('Prompt(')
          ..write('id: $id, ')
          ..write('subtitle: $subtitle, ')
          ..write('title: $title, ')
          ..write('source: $source, ')
          ..write('createdAt: $createdAt, ')
          ..write('isSynced: $isSynced, ')
          ..write('categoryId: $categoryId, ')
          ..write('keywords: $keywords, ')
          ..write('variables: $variables, ')
          ..write('version: $version, ')
          ..write('popularityScore: $popularityScore, ')
          ..write('serverSyncAt: $serverSyncAt, ')
          ..write('lastUsedAt: $lastUsedAt')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, subtitle, title, source, createdAt, isSynced, categoryId,
      keywords, variables, version, popularityScore, serverSyncAt, lastUsedAt);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is Prompt &&
          other.id == this.id &&
          other.subtitle == this.subtitle &&
          other.title == this.title &&
          other.source == this.source &&
          other.createdAt == this.createdAt &&
          other.isSynced == this.isSynced &&
          other.categoryId == this.categoryId &&
          other.keywords == this.keywords &&
          other.variables == this.variables &&
          other.version == this.version &&
          other.popularityScore == this.popularityScore &&
          other.serverSyncAt == this.serverSyncAt &&
          other.lastUsedAt == this.lastUsedAt);
}

class PromptsCompanion extends drift.UpdateCompanion<Prompt> {
  final drift.Value<String> id;
  final drift.Value<String> subtitle;
  final drift.Value<String> title;
  final drift.Value<String> source;
  final drift.Value<DateTime> createdAt;
  final drift.Value<bool> isSynced;
  final drift.Value<String> categoryId;
  final drift.Value<String> keywords;
  final drift.Value<String> variables;
  final drift.Value<int> version;
  final drift.Value<int> popularityScore;
  final drift.Value<DateTime?> serverSyncAt;
  final drift.Value<DateTime?> lastUsedAt;
  final drift.Value<int> rowid;
  const PromptsCompanion({
    this.id = const drift.Value.absent(),
    this.subtitle = const drift.Value.absent(),
    this.title = const drift.Value.absent(),
    this.source = const drift.Value.absent(),
    this.createdAt = const drift.Value.absent(),
    this.isSynced = const drift.Value.absent(),
    this.categoryId = const drift.Value.absent(),
    this.keywords = const drift.Value.absent(),
    this.variables = const drift.Value.absent(),
    this.version = const drift.Value.absent(),
    this.popularityScore = const drift.Value.absent(),
    this.serverSyncAt = const drift.Value.absent(),
    this.lastUsedAt = const drift.Value.absent(),
    this.rowid = const drift.Value.absent(),
  });
  PromptsCompanion.insert({
    required String id,
    required String subtitle,
    required String title,
    required String source,
    this.createdAt = const drift.Value.absent(),
    this.isSynced = const drift.Value.absent(),
    required String categoryId,
    this.keywords = const drift.Value.absent(),
    this.variables = const drift.Value.absent(),
    this.version = const drift.Value.absent(),
    this.popularityScore = const drift.Value.absent(),
    this.serverSyncAt = const drift.Value.absent(),
    this.lastUsedAt = const drift.Value.absent(),
    this.rowid = const drift.Value.absent(),
  })  : id = drift.Value(id),
        subtitle = drift.Value(subtitle),
        title = drift.Value(title),
        source = drift.Value(source),
        categoryId = drift.Value(categoryId);
  static drift.Insertable<Prompt> custom({
    drift.Expression<String>? id,
    drift.Expression<String>? subtitle,
    drift.Expression<String>? title,
    drift.Expression<String>? source,
    drift.Expression<DateTime>? createdAt,
    drift.Expression<bool>? isSynced,
    drift.Expression<String>? categoryId,
    drift.Expression<String>? keywords,
    drift.Expression<String>? variables,
    drift.Expression<int>? version,
    drift.Expression<int>? popularityScore,
    drift.Expression<DateTime>? serverSyncAt,
    drift.Expression<DateTime>? lastUsedAt,
    drift.Expression<int>? rowid,
  }) {
    return drift.RawValuesInsertable({
      if (id != null) 'id': id,
      if (subtitle != null) 'subtitle': subtitle,
      if (title != null) 'title': title,
      if (source != null) 'source': source,
      if (createdAt != null) 'created_at': createdAt,
      if (isSynced != null) 'is_synced': isSynced,
      if (categoryId != null) 'category_id': categoryId,
      if (keywords != null) 'keywords': keywords,
      if (variables != null) 'variables': variables,
      if (version != null) 'version': version,
      if (popularityScore != null) 'popularity_score': popularityScore,
      if (serverSyncAt != null) 'server_sync_at': serverSyncAt,
      if (lastUsedAt != null) 'last_used_at': lastUsedAt,
      if (rowid != null) 'rowid': rowid,
    });
  }

  PromptsCompanion copyWith(
      {drift.Value<String>? id,
      drift.Value<String>? subtitle,
      drift.Value<String>? title,
      drift.Value<String>? source,
      drift.Value<DateTime>? createdAt,
      drift.Value<bool>? isSynced,
      drift.Value<String>? categoryId,
      drift.Value<String>? keywords,
      drift.Value<String>? variables,
      drift.Value<int>? version,
      drift.Value<int>? popularityScore,
      drift.Value<DateTime?>? serverSyncAt,
      drift.Value<DateTime?>? lastUsedAt,
      drift.Value<int>? rowid}) {
    return PromptsCompanion(
      id: id ?? this.id,
      subtitle: subtitle ?? this.subtitle,
      title: title ?? this.title,
      source: source ?? this.source,
      createdAt: createdAt ?? this.createdAt,
      isSynced: isSynced ?? this.isSynced,
      categoryId: categoryId ?? this.categoryId,
      keywords: keywords ?? this.keywords,
      variables: variables ?? this.variables,
      version: version ?? this.version,
      popularityScore: popularityScore ?? this.popularityScore,
      serverSyncAt: serverSyncAt ?? this.serverSyncAt,
      lastUsedAt: lastUsedAt ?? this.lastUsedAt,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, drift.Expression> toColumns(bool nullToAbsent) {
    final map = <String, drift.Expression>{};
    if (id.present) {
      map['id'] = drift.Variable<String>($PromptsTable.$converterid.toSql(id.value));
    }
    if (subtitle.present) {
      map['subtitle'] = drift.Variable<String>(subtitle.value);
    }
    if (title.present) {
      map['title'] = drift.Variable<String>(title.value);
    }
    if (source.present) {
      map['source'] = drift.Variable<String>(source.value);
    }
    if (createdAt.present) {
      map['created_at'] = drift.Variable<DateTime>(createdAt.value);
    }
    if (isSynced.present) {
      map['is_synced'] = drift.Variable<bool>(isSynced.value);
    }
    if (categoryId.present) {
      map['category_id'] =
          drift.Variable<String>($PromptsTable.$convertercategoryId.toSql(categoryId.value));
    }
    if (keywords.present) {
      map['keywords'] =
          drift.Variable<String>($PromptsTable.$converterkeywords.toSql(keywords.value));
    }
    if (variables.present) {
      map['variables'] =
          drift.Variable<String>($PromptsTable.$convertervariables.toSql(variables.value));
    }
    if (version.present) {
      map['version'] = drift.Variable<int>(version.value);
    }
    if (popularityScore.present) {
      map['popularity_score'] = drift.Variable<int>(popularityScore.value);
    }
    if (serverSyncAt.present) {
      map['server_sync_at'] = drift.Variable<DateTime>(serverSyncAt.value);
    }
    if (lastUsedAt.present) {
      map['last_used_at'] = drift.Variable<DateTime>(lastUsedAt.value);
    }
    if (rowid.present) {
      map['rowid'] = drift.Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('PromptsCompanion(')
          ..write('id: $id, ')
          ..write('subtitle: $subtitle, ')
          ..write('title: $title, ')
          ..write('source: $source, ')
          ..write('createdAt: $createdAt, ')
          ..write('isSynced: $isSynced, ')
          ..write('categoryId: $categoryId, ')
          ..write('keywords: $keywords, ')
          ..write('variables: $variables, ')
          ..write('version: $version, ')
          ..write('popularityScore: $popularityScore, ')
          ..write('serverSyncAt: $serverSyncAt, ')
          ..write('lastUsedAt: $lastUsedAt, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $KeywordMappingsTable extends KeywordMappings
    with drift.TableInfo<$KeywordMappingsTable, KeywordMapping> {
  @override
  final drift.GeneratedDatabase attachedDatabase;
  final String? _alias;
  $KeywordMappingsTable(this.attachedDatabase, [this._alias]);
  @override
  late final drift.GeneratedColumnWithTypeConverter<String, String> id =
      drift.GeneratedColumn<String>('id', aliasedName, false,
              type: DriftSqlType.string, requiredDuringInsert: true)
          .withConverter<String>($KeywordMappingsTable.$converterid);
  @override
  late final drift.GeneratedColumnWithTypeConverter<String, String> categoryId =
      drift.GeneratedColumn<String>('category_id', aliasedName, false,
              type: DriftSqlType.string,
              requiredDuringInsert: true,
              defaultConstraints: GeneratedColumn.constraintIsAlways('REFERENCES categories (id)'))
          .withConverter<String>($KeywordMappingsTable.$convertercategoryId);
  static const drift.VerificationMeta _keywordMeta = const drift.VerificationMeta('keyword');
  @override
  late final drift.GeneratedColumn<String> keyword = drift.GeneratedColumn<String>(
      'keyword', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const drift.VerificationMeta _weightMeta = const drift.VerificationMeta('weight');
  @override
  late final drift.GeneratedColumn<double> weight = drift.GeneratedColumn<double>(
      'weight', aliasedName, false,
      type: DriftSqlType.double,
      requiredDuringInsert: false,
      defaultValue: const drift.Constant(1.0));
  static const drift.VerificationMeta _createdAtMeta = const drift.VerificationMeta('createdAt');
  @override
  late final drift.GeneratedColumn<DateTime> createdAt = drift.GeneratedColumn<DateTime>(
      'created_at', aliasedName, false,
      type: DriftSqlType.dateTime,
      requiredDuringInsert: false,
      defaultValue: drift.Constant(DateTime.now()));
  @override
  List<drift.GeneratedColumn> get $columns => [id, categoryId, keyword, weight, createdAt];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'keyword_mappings';
  @override
  drift.VerificationContext validateIntegrity(drift.Insertable<KeywordMapping> instance,
      {bool isInserting = false}) {
    final context = drift.VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('keyword')) {
      context.handle(_keywordMeta, keyword.isAcceptableOrUnknown(data['keyword']!, _keywordMeta));
    } else if (isInserting) {
      context.missing(_keywordMeta);
    }
    if (data.containsKey('weight')) {
      context.handle(_weightMeta, weight.isAcceptableOrUnknown(data['weight']!, _weightMeta));
    }
    if (data.containsKey('created_at')) {
      context.handle(
          _createdAtMeta, createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta));
    }
    return context;
  }

  @override
  Set<drift.GeneratedColumn> get $primaryKey => {id};
  @override
  KeywordMapping map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return KeywordMapping(
      id: $KeywordMappingsTable.$converterid.fromSql(
          attachedDatabase.typeMapping.read(DriftSqlType.string, data['${effectivePrefix}id'])!),
      categoryId: $KeywordMappingsTable.$convertercategoryId.fromSql(attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}category_id'])!),
      keyword: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}keyword'])!,
      weight:
          attachedDatabase.typeMapping.read(DriftSqlType.double, data['${effectivePrefix}weight'])!,
      createdAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}created_at'])!,
    );
  }

  @override
  $KeywordMappingsTable createAlias(String alias) {
    return $KeywordMappingsTable(attachedDatabase, alias);
  }

  static drift.TypeConverter<String, String> $converterid = const UuidConverter();
  static drift.TypeConverter<String, String> $convertercategoryId = const UuidConverter();
}

class KeywordMapping extends drift.DataClass implements drift.Insertable<KeywordMapping> {
  final String id;
  final String categoryId;
  final String keyword;
  final double weight;
  final DateTime createdAt;
  const KeywordMapping(
      {required this.id,
      required this.categoryId,
      required this.keyword,
      required this.weight,
      required this.createdAt});
  @override
  Map<String, drift.Expression> toColumns(bool nullToAbsent) {
    final map = <String, drift.Expression>{};
    {
      map['id'] = drift.Variable<String>($KeywordMappingsTable.$converterid.toSql(id));
    }
    {
      map['category_id'] =
          drift.Variable<String>($KeywordMappingsTable.$convertercategoryId.toSql(categoryId));
    }
    map['keyword'] = drift.Variable<String>(keyword);
    map['weight'] = drift.Variable<double>(weight);
    map['created_at'] = drift.Variable<DateTime>(createdAt);
    return map;
  }

  KeywordMappingsCompanion toCompanion(bool nullToAbsent) {
    return KeywordMappingsCompanion(
      id: drift.Value(id),
      categoryId: drift.Value(categoryId),
      keyword: drift.Value(keyword),
      weight: drift.Value(weight),
      createdAt: drift.Value(createdAt),
    );
  }

  factory KeywordMapping.fromJson(Map<String, dynamic> json, {ValueSerializer? serializer}) {
    serializer ??= drift.driftRuntimeOptions.defaultSerializer;
    return KeywordMapping(
      id: serializer.fromJson<String>(json['id']),
      categoryId: serializer.fromJson<String>(json['categoryId']),
      keyword: serializer.fromJson<String>(json['keyword']),
      weight: serializer.fromJson<double>(json['weight']),
      createdAt: serializer.fromJson<DateTime>(json['createdAt']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= drift.driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'categoryId': serializer.toJson<String>(categoryId),
      'keyword': serializer.toJson<String>(keyword),
      'weight': serializer.toJson<double>(weight),
      'createdAt': serializer.toJson<DateTime>(createdAt),
    };
  }

  KeywordMapping copyWith(
          {String? id, String? categoryId, String? keyword, double? weight, DateTime? createdAt}) =>
      KeywordMapping(
        id: id ?? this.id,
        categoryId: categoryId ?? this.categoryId,
        keyword: keyword ?? this.keyword,
        weight: weight ?? this.weight,
        createdAt: createdAt ?? this.createdAt,
      );
  KeywordMapping copyWithCompanion(KeywordMappingsCompanion data) {
    return KeywordMapping(
      id: data.id.present ? data.id.value : this.id,
      categoryId: data.categoryId.present ? data.categoryId.value : this.categoryId,
      keyword: data.keyword.present ? data.keyword.value : this.keyword,
      weight: data.weight.present ? data.weight.value : this.weight,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
    );
  }

  @override
  String toString() {
    return (StringBuffer('KeywordMapping(')
          ..write('id: $id, ')
          ..write('categoryId: $categoryId, ')
          ..write('keyword: $keyword, ')
          ..write('weight: $weight, ')
          ..write('createdAt: $createdAt')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, categoryId, keyword, weight, createdAt);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is KeywordMapping &&
          other.id == this.id &&
          other.categoryId == this.categoryId &&
          other.keyword == this.keyword &&
          other.weight == this.weight &&
          other.createdAt == this.createdAt);
}

class KeywordMappingsCompanion extends drift.UpdateCompanion<KeywordMapping> {
  final drift.Value<String> id;
  final drift.Value<String> categoryId;
  final drift.Value<String> keyword;
  final drift.Value<double> weight;
  final drift.Value<DateTime> createdAt;
  final drift.Value<int> rowid;
  const KeywordMappingsCompanion({
    this.id = const drift.Value.absent(),
    this.categoryId = const drift.Value.absent(),
    this.keyword = const drift.Value.absent(),
    this.weight = const drift.Value.absent(),
    this.createdAt = const drift.Value.absent(),
    this.rowid = const drift.Value.absent(),
  });
  KeywordMappingsCompanion.insert({
    required String id,
    required String categoryId,
    required String keyword,
    this.weight = const drift.Value.absent(),
    this.createdAt = const drift.Value.absent(),
    this.rowid = const drift.Value.absent(),
  })  : id = drift.Value(id),
        categoryId = drift.Value(categoryId),
        keyword = drift.Value(keyword);
  static drift.Insertable<KeywordMapping> custom({
    drift.Expression<String>? id,
    drift.Expression<String>? categoryId,
    drift.Expression<String>? keyword,
    drift.Expression<double>? weight,
    drift.Expression<DateTime>? createdAt,
    drift.Expression<int>? rowid,
  }) {
    return drift.RawValuesInsertable({
      if (id != null) 'id': id,
      if (categoryId != null) 'category_id': categoryId,
      if (keyword != null) 'keyword': keyword,
      if (weight != null) 'weight': weight,
      if (createdAt != null) 'created_at': createdAt,
      if (rowid != null) 'rowid': rowid,
    });
  }

  KeywordMappingsCompanion copyWith(
      {drift.Value<String>? id,
      drift.Value<String>? categoryId,
      drift.Value<String>? keyword,
      drift.Value<double>? weight,
      drift.Value<DateTime>? createdAt,
      drift.Value<int>? rowid}) {
    return KeywordMappingsCompanion(
      id: id ?? this.id,
      categoryId: categoryId ?? this.categoryId,
      keyword: keyword ?? this.keyword,
      weight: weight ?? this.weight,
      createdAt: createdAt ?? this.createdAt,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, drift.Expression> toColumns(bool nullToAbsent) {
    final map = <String, drift.Expression>{};
    if (id.present) {
      map['id'] = drift.Variable<String>($KeywordMappingsTable.$converterid.toSql(id.value));
    }
    if (categoryId.present) {
      map['category_id'] = drift.Variable<String>(
          $KeywordMappingsTable.$convertercategoryId.toSql(categoryId.value));
    }
    if (keyword.present) {
      map['keyword'] = drift.Variable<String>(keyword.value);
    }
    if (weight.present) {
      map['weight'] = drift.Variable<double>(weight.value);
    }
    if (createdAt.present) {
      map['created_at'] = drift.Variable<DateTime>(createdAt.value);
    }
    if (rowid.present) {
      map['rowid'] = drift.Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('KeywordMappingsCompanion(')
          ..write('id: $id, ')
          ..write('categoryId: $categoryId, ')
          ..write('keyword: $keyword, ')
          ..write('weight: $weight, ')
          ..write('createdAt: $createdAt, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $Sp500TickersTable extends Sp500Tickers
    with drift.TableInfo<$Sp500TickersTable, Sp500Ticker> {
  @override
  final drift.GeneratedDatabase attachedDatabase;
  final String? _alias;
  $Sp500TickersTable(this.attachedDatabase, [this._alias]);
  static const drift.VerificationMeta _idMeta = const drift.VerificationMeta('id');
  @override
  late final drift.GeneratedColumn<int> id = drift.GeneratedColumn<int>('id', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultConstraints: GeneratedColumn.constraintIsAlways('PRIMARY KEY AUTOINCREMENT'));
  static const drift.VerificationMeta _symbolMeta = const drift.VerificationMeta('symbol');
  @override
  late final drift.GeneratedColumn<String> symbol = drift.GeneratedColumn<String>(
      'symbol', aliasedName, false,
      additionalChecks: GeneratedColumn.checkTextLength(minTextLength: 1, maxTextLength: 10),
      type: DriftSqlType.string,
      requiredDuringInsert: true);
  static const drift.VerificationMeta _companyNameMeta =
      const drift.VerificationMeta('companyName');
  @override
  late final drift.GeneratedColumn<String> companyName =
      drift.GeneratedColumn<String>('company_name', aliasedName, false,
          additionalChecks: GeneratedColumn.checkTextLength(
            minTextLength: 1,
          ),
          type: DriftSqlType.string,
          requiredDuringInsert: true);
  @override
  late final drift.GeneratedColumnWithTypeConverter<DateTime, String> lastUpdated =
      drift.GeneratedColumn<String>('last_updated', aliasedName, false,
              type: DriftSqlType.string,
              requiredDuringInsert: false,
              defaultValue: drift.Constant(DateTime.now().toIso8601String()))
          .withConverter<DateTime>($Sp500TickersTable.$converterlastUpdated);
  @override
  List<drift.GeneratedColumn> get $columns => [id, symbol, companyName, lastUpdated];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'sp500_tickers';
  @override
  drift.VerificationContext validateIntegrity(drift.Insertable<Sp500Ticker> instance,
      {bool isInserting = false}) {
    final context = drift.VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('symbol')) {
      context.handle(_symbolMeta, symbol.isAcceptableOrUnknown(data['symbol']!, _symbolMeta));
    } else if (isInserting) {
      context.missing(_symbolMeta);
    }
    if (data.containsKey('company_name')) {
      context.handle(_companyNameMeta,
          companyName.isAcceptableOrUnknown(data['company_name']!, _companyNameMeta));
    } else if (isInserting) {
      context.missing(_companyNameMeta);
    }
    return context;
  }

  @override
  Set<drift.GeneratedColumn> get $primaryKey => {id};
  @override
  Sp500Ticker map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return Sp500Ticker(
      id: attachedDatabase.typeMapping.read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      symbol:
          attachedDatabase.typeMapping.read(DriftSqlType.string, data['${effectivePrefix}symbol'])!,
      companyName: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}company_name'])!,
      lastUpdated: $Sp500TickersTable.$converterlastUpdated.fromSql(attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}last_updated'])!),
    );
  }

  @override
  $Sp500TickersTable createAlias(String alias) {
    return $Sp500TickersTable(attachedDatabase, alias);
  }

  static drift.TypeConverter<DateTime, String> $converterlastUpdated = const DateTimeConverter();
}

class Sp500Ticker extends drift.DataClass implements drift.Insertable<Sp500Ticker> {
  final int id;
  final String symbol;
  final String companyName;
  final DateTime lastUpdated;
  const Sp500Ticker(
      {required this.id,
      required this.symbol,
      required this.companyName,
      required this.lastUpdated});
  @override
  Map<String, drift.Expression> toColumns(bool nullToAbsent) {
    final map = <String, drift.Expression>{};
    map['id'] = drift.Variable<int>(id);
    map['symbol'] = drift.Variable<String>(symbol);
    map['company_name'] = drift.Variable<String>(companyName);
    {
      map['last_updated'] =
          drift.Variable<String>($Sp500TickersTable.$converterlastUpdated.toSql(lastUpdated));
    }
    return map;
  }

  Sp500TickersCompanion toCompanion(bool nullToAbsent) {
    return Sp500TickersCompanion(
      id: drift.Value(id),
      symbol: drift.Value(symbol),
      companyName: drift.Value(companyName),
      lastUpdated: drift.Value(lastUpdated),
    );
  }

  factory Sp500Ticker.fromJson(Map<String, dynamic> json, {ValueSerializer? serializer}) {
    serializer ??= drift.driftRuntimeOptions.defaultSerializer;
    return Sp500Ticker(
      id: serializer.fromJson<int>(json['id']),
      symbol: serializer.fromJson<String>(json['symbol']),
      companyName: serializer.fromJson<String>(json['companyName']),
      lastUpdated: serializer.fromJson<DateTime>(json['lastUpdated']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= drift.driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'symbol': serializer.toJson<String>(symbol),
      'companyName': serializer.toJson<String>(companyName),
      'lastUpdated': serializer.toJson<DateTime>(lastUpdated),
    };
  }

  Sp500Ticker copyWith({int? id, String? symbol, String? companyName, DateTime? lastUpdated}) =>
      Sp500Ticker(
        id: id ?? this.id,
        symbol: symbol ?? this.symbol,
        companyName: companyName ?? this.companyName,
        lastUpdated: lastUpdated ?? this.lastUpdated,
      );
  Sp500Ticker copyWithCompanion(Sp500TickersCompanion data) {
    return Sp500Ticker(
      id: data.id.present ? data.id.value : this.id,
      symbol: data.symbol.present ? data.symbol.value : this.symbol,
      companyName: data.companyName.present ? data.companyName.value : this.companyName,
      lastUpdated: data.lastUpdated.present ? data.lastUpdated.value : this.lastUpdated,
    );
  }

  @override
  String toString() {
    return (StringBuffer('Sp500Ticker(')
          ..write('id: $id, ')
          ..write('symbol: $symbol, ')
          ..write('companyName: $companyName, ')
          ..write('lastUpdated: $lastUpdated')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, symbol, companyName, lastUpdated);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is Sp500Ticker &&
          other.id == this.id &&
          other.symbol == this.symbol &&
          other.companyName == this.companyName &&
          other.lastUpdated == this.lastUpdated);
}

class Sp500TickersCompanion extends drift.UpdateCompanion<Sp500Ticker> {
  final drift.Value<int> id;
  final drift.Value<String> symbol;
  final drift.Value<String> companyName;
  final drift.Value<DateTime> lastUpdated;
  const Sp500TickersCompanion({
    this.id = const drift.Value.absent(),
    this.symbol = const drift.Value.absent(),
    this.companyName = const drift.Value.absent(),
    this.lastUpdated = const drift.Value.absent(),
  });
  Sp500TickersCompanion.insert({
    this.id = const drift.Value.absent(),
    required String symbol,
    required String companyName,
    this.lastUpdated = const drift.Value.absent(),
  })  : symbol = drift.Value(symbol),
        companyName = drift.Value(companyName);
  static drift.Insertable<Sp500Ticker> custom({
    drift.Expression<int>? id,
    drift.Expression<String>? symbol,
    drift.Expression<String>? companyName,
    drift.Expression<String>? lastUpdated,
  }) {
    return drift.RawValuesInsertable({
      if (id != null) 'id': id,
      if (symbol != null) 'symbol': symbol,
      if (companyName != null) 'company_name': companyName,
      if (lastUpdated != null) 'last_updated': lastUpdated,
    });
  }

  Sp500TickersCompanion copyWith(
      {drift.Value<int>? id,
      drift.Value<String>? symbol,
      drift.Value<String>? companyName,
      drift.Value<DateTime>? lastUpdated}) {
    return Sp500TickersCompanion(
      id: id ?? this.id,
      symbol: symbol ?? this.symbol,
      companyName: companyName ?? this.companyName,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  @override
  Map<String, drift.Expression> toColumns(bool nullToAbsent) {
    final map = <String, drift.Expression>{};
    if (id.present) {
      map['id'] = drift.Variable<int>(id.value);
    }
    if (symbol.present) {
      map['symbol'] = drift.Variable<String>(symbol.value);
    }
    if (companyName.present) {
      map['company_name'] = drift.Variable<String>(companyName.value);
    }
    if (lastUpdated.present) {
      map['last_updated'] =
          drift.Variable<String>($Sp500TickersTable.$converterlastUpdated.toSql(lastUpdated.value));
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('Sp500TickersCompanion(')
          ..write('id: $id, ')
          ..write('symbol: $symbol, ')
          ..write('companyName: $companyName, ')
          ..write('lastUpdated: $lastUpdated')
          ..write(')'))
        .toString();
  }
}

class $LocalPromptUsageTable extends LocalPromptUsage
    with drift.TableInfo<$LocalPromptUsageTable, LocalPromptUsageData> {
  @override
  final drift.GeneratedDatabase attachedDatabase;
  final String? _alias;
  $LocalPromptUsageTable(this.attachedDatabase, [this._alias]);
  static const drift.VerificationMeta _idMeta = const drift.VerificationMeta('id');
  @override
  late final drift.GeneratedColumn<int> id = drift.GeneratedColumn<int>('id', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultConstraints: GeneratedColumn.constraintIsAlways('PRIMARY KEY AUTOINCREMENT'));
  static const drift.VerificationMeta _promptIdMeta = const drift.VerificationMeta('promptId');
  @override
  late final drift.GeneratedColumn<String> promptId = drift.GeneratedColumn<String>(
      'prompt_id', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      defaultConstraints: GeneratedColumn.constraintIsAlways('REFERENCES prompts (id)'));
  static const drift.VerificationMeta _eventTypeMeta = const drift.VerificationMeta('eventType');
  @override
  late final drift.GeneratedColumn<String> eventType = drift.GeneratedColumn<String>(
      'event_type', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      $customConstraints: 'NOT NULL CHECK (event_type IN (\'selected\', \'executed\'))');
  static const drift.VerificationMeta _usedAtMeta = const drift.VerificationMeta('usedAt');
  @override
  late final drift.GeneratedColumn<int> usedAt = drift.GeneratedColumn<int>(
      'used_at', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const drift.VerificationMeta _countryCodeMeta =
      const drift.VerificationMeta('countryCode');
  @override
  late final drift.GeneratedColumn<String> countryCode = drift.GeneratedColumn<String>(
      'country_code', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  @override
  List<drift.GeneratedColumn> get $columns => [id, promptId, eventType, usedAt, countryCode];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'local_prompt_usage';
  @override
  drift.VerificationContext validateIntegrity(drift.Insertable<LocalPromptUsageData> instance,
      {bool isInserting = false}) {
    final context = drift.VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('prompt_id')) {
      context.handle(
          _promptIdMeta, promptId.isAcceptableOrUnknown(data['prompt_id']!, _promptIdMeta));
    } else if (isInserting) {
      context.missing(_promptIdMeta);
    }
    if (data.containsKey('event_type')) {
      context.handle(
          _eventTypeMeta, eventType.isAcceptableOrUnknown(data['event_type']!, _eventTypeMeta));
    } else if (isInserting) {
      context.missing(_eventTypeMeta);
    }
    if (data.containsKey('used_at')) {
      context.handle(_usedAtMeta, usedAt.isAcceptableOrUnknown(data['used_at']!, _usedAtMeta));
    } else if (isInserting) {
      context.missing(_usedAtMeta);
    }
    if (data.containsKey('country_code')) {
      context.handle(_countryCodeMeta,
          countryCode.isAcceptableOrUnknown(data['country_code']!, _countryCodeMeta));
    }
    return context;
  }

  @override
  Set<drift.GeneratedColumn> get $primaryKey => {id};
  @override
  LocalPromptUsageData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return LocalPromptUsageData(
      id: attachedDatabase.typeMapping.read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      promptId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}prompt_id'])!,
      eventType: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}event_type'])!,
      usedAt:
          attachedDatabase.typeMapping.read(DriftSqlType.int, data['${effectivePrefix}used_at'])!,
      countryCode: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}country_code']),
    );
  }

  @override
  $LocalPromptUsageTable createAlias(String alias) {
    return $LocalPromptUsageTable(attachedDatabase, alias);
  }
}

class LocalPromptUsageData extends drift.DataClass
    implements drift.Insertable<LocalPromptUsageData> {
  final int id;
  final String promptId;
  final String eventType;
  final int usedAt;
  final String? countryCode;
  const LocalPromptUsageData(
      {required this.id,
      required this.promptId,
      required this.eventType,
      required this.usedAt,
      this.countryCode});
  @override
  Map<String, drift.Expression> toColumns(bool nullToAbsent) {
    final map = <String, drift.Expression>{};
    map['id'] = drift.Variable<int>(id);
    map['prompt_id'] = drift.Variable<String>(promptId);
    map['event_type'] = drift.Variable<String>(eventType);
    map['used_at'] = drift.Variable<int>(usedAt);
    if (!nullToAbsent || countryCode != null) {
      map['country_code'] = drift.Variable<String>(countryCode);
    }
    return map;
  }

  LocalPromptUsageCompanion toCompanion(bool nullToAbsent) {
    return LocalPromptUsageCompanion(
      id: drift.Value(id),
      promptId: drift.Value(promptId),
      eventType: drift.Value(eventType),
      usedAt: drift.Value(usedAt),
      countryCode: countryCode == null && nullToAbsent
          ? const drift.Value.absent()
          : drift.Value(countryCode),
    );
  }

  factory LocalPromptUsageData.fromJson(Map<String, dynamic> json, {ValueSerializer? serializer}) {
    serializer ??= drift.driftRuntimeOptions.defaultSerializer;
    return LocalPromptUsageData(
      id: serializer.fromJson<int>(json['id']),
      promptId: serializer.fromJson<String>(json['promptId']),
      eventType: serializer.fromJson<String>(json['eventType']),
      usedAt: serializer.fromJson<int>(json['usedAt']),
      countryCode: serializer.fromJson<String?>(json['countryCode']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= drift.driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'promptId': serializer.toJson<String>(promptId),
      'eventType': serializer.toJson<String>(eventType),
      'usedAt': serializer.toJson<int>(usedAt),
      'countryCode': serializer.toJson<String?>(countryCode),
    };
  }

  LocalPromptUsageData copyWith(
          {int? id,
          String? promptId,
          String? eventType,
          int? usedAt,
          drift.Value<String?> countryCode = const drift.Value.absent()}) =>
      LocalPromptUsageData(
        id: id ?? this.id,
        promptId: promptId ?? this.promptId,
        eventType: eventType ?? this.eventType,
        usedAt: usedAt ?? this.usedAt,
        countryCode: countryCode.present ? countryCode.value : this.countryCode,
      );
  LocalPromptUsageData copyWithCompanion(LocalPromptUsageCompanion data) {
    return LocalPromptUsageData(
      id: data.id.present ? data.id.value : this.id,
      promptId: data.promptId.present ? data.promptId.value : this.promptId,
      eventType: data.eventType.present ? data.eventType.value : this.eventType,
      usedAt: data.usedAt.present ? data.usedAt.value : this.usedAt,
      countryCode: data.countryCode.present ? data.countryCode.value : this.countryCode,
    );
  }

  @override
  String toString() {
    return (StringBuffer('LocalPromptUsageData(')
          ..write('id: $id, ')
          ..write('promptId: $promptId, ')
          ..write('eventType: $eventType, ')
          ..write('usedAt: $usedAt, ')
          ..write('countryCode: $countryCode')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, promptId, eventType, usedAt, countryCode);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is LocalPromptUsageData &&
          other.id == this.id &&
          other.promptId == this.promptId &&
          other.eventType == this.eventType &&
          other.usedAt == this.usedAt &&
          other.countryCode == this.countryCode);
}

class LocalPromptUsageCompanion extends drift.UpdateCompanion<LocalPromptUsageData> {
  final drift.Value<int> id;
  final drift.Value<String> promptId;
  final drift.Value<String> eventType;
  final drift.Value<int> usedAt;
  final drift.Value<String?> countryCode;
  const LocalPromptUsageCompanion({
    this.id = const drift.Value.absent(),
    this.promptId = const drift.Value.absent(),
    this.eventType = const drift.Value.absent(),
    this.usedAt = const drift.Value.absent(),
    this.countryCode = const drift.Value.absent(),
  });
  LocalPromptUsageCompanion.insert({
    this.id = const drift.Value.absent(),
    required String promptId,
    required String eventType,
    required int usedAt,
    this.countryCode = const drift.Value.absent(),
  })  : promptId = drift.Value(promptId),
        eventType = drift.Value(eventType),
        usedAt = drift.Value(usedAt);
  static drift.Insertable<LocalPromptUsageData> custom({
    drift.Expression<int>? id,
    drift.Expression<String>? promptId,
    drift.Expression<String>? eventType,
    drift.Expression<int>? usedAt,
    drift.Expression<String>? countryCode,
  }) {
    return drift.RawValuesInsertable({
      if (id != null) 'id': id,
      if (promptId != null) 'prompt_id': promptId,
      if (eventType != null) 'event_type': eventType,
      if (usedAt != null) 'used_at': usedAt,
      if (countryCode != null) 'country_code': countryCode,
    });
  }

  LocalPromptUsageCompanion copyWith(
      {drift.Value<int>? id,
      drift.Value<String>? promptId,
      drift.Value<String>? eventType,
      drift.Value<int>? usedAt,
      drift.Value<String?>? countryCode}) {
    return LocalPromptUsageCompanion(
      id: id ?? this.id,
      promptId: promptId ?? this.promptId,
      eventType: eventType ?? this.eventType,
      usedAt: usedAt ?? this.usedAt,
      countryCode: countryCode ?? this.countryCode,
    );
  }

  @override
  Map<String, drift.Expression> toColumns(bool nullToAbsent) {
    final map = <String, drift.Expression>{};
    if (id.present) {
      map['id'] = drift.Variable<int>(id.value);
    }
    if (promptId.present) {
      map['prompt_id'] = drift.Variable<String>(promptId.value);
    }
    if (eventType.present) {
      map['event_type'] = drift.Variable<String>(eventType.value);
    }
    if (usedAt.present) {
      map['used_at'] = drift.Variable<int>(usedAt.value);
    }
    if (countryCode.present) {
      map['country_code'] = drift.Variable<String>(countryCode.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('LocalPromptUsageCompanion(')
          ..write('id: $id, ')
          ..write('promptId: $promptId, ')
          ..write('eventType: $eventType, ')
          ..write('usedAt: $usedAt, ')
          ..write('countryCode: $countryCode')
          ..write(')'))
        .toString();
  }
}

abstract class _$AppDatabase extends drift.GeneratedDatabase {
  _$AppDatabase(QueryExecutor e) : super(e);
  $AppDatabaseManager get managers => $AppDatabaseManager(this);
  late final $CategoriesTable categories = $CategoriesTable(this);
  late final $PromptsTable prompts = $PromptsTable(this);
  late final $KeywordMappingsTable keywordMappings = $KeywordMappingsTable(this);
  late final $Sp500TickersTable sp500Tickers = $Sp500TickersTable(this);
  late final $LocalPromptUsageTable localPromptUsage = $LocalPromptUsageTable(this);
  @override
  Iterable<drift.TableInfo<drift.Table, Object?>> get allTables =>
      allSchemaEntities.whereType<drift.TableInfo<drift.Table, Object?>>();
  @override
  List<drift.DatabaseSchemaEntity> get allSchemaEntities =>
      [categories, prompts, keywordMappings, sp500Tickers, localPromptUsage];
}

typedef $$CategoriesTableCreateCompanionBuilder = CategoriesCompanion Function({
  required String id,
  required String title,
  drift.Value<String?> subtitle,
  drift.Value<String?> icon,
  drift.Value<String> keywords,
  drift.Value<DateTime> createdAt,
  drift.Value<DateTime> updatedAt,
  drift.Value<int> rowid,
});
typedef $$CategoriesTableUpdateCompanionBuilder = CategoriesCompanion Function({
  drift.Value<String> id,
  drift.Value<String> title,
  drift.Value<String?> subtitle,
  drift.Value<String?> icon,
  drift.Value<String> keywords,
  drift.Value<DateTime> createdAt,
  drift.Value<DateTime> updatedAt,
  drift.Value<int> rowid,
});

final class $$CategoriesTableReferences
    extends drift.BaseReferences<_$AppDatabase, $CategoriesTable, Category> {
  $$CategoriesTableReferences(super.$_db, super.$_table, super.$_typedResult);

  static drift.MultiTypedResultKey<$PromptsTable, List<Prompt>> _promptsRefsTable(
          _$AppDatabase db) =>
      drift.MultiTypedResultKey.fromTable(db.prompts,
          aliasName: drift.$_aliasNameGenerator(db.categories.id, db.prompts.categoryId));

  $$PromptsTableProcessedTableManager get promptsRefs {
    final manager = $$PromptsTableTableManager($_db, $_db.prompts)
        .filter((f) => f.categoryId.id.sqlEquals($_itemColumn<String>('id')!));

    final cache = $_typedResult.readTableOrNull(_promptsRefsTable($_db));
    return drift.ProcessedTableManager(manager.$state.copyWith(prefetchedData: cache));
  }

  static drift.MultiTypedResultKey<$KeywordMappingsTable, List<KeywordMapping>>
      _keywordMappingsRefsTable(_$AppDatabase db) => drift.MultiTypedResultKey.fromTable(
          db.keywordMappings,
          aliasName: drift.$_aliasNameGenerator(db.categories.id, db.keywordMappings.categoryId));

  $$KeywordMappingsTableProcessedTableManager get keywordMappingsRefs {
    final manager = $$KeywordMappingsTableTableManager($_db, $_db.keywordMappings)
        .filter((f) => f.categoryId.id.sqlEquals($_itemColumn<String>('id')!));

    final cache = $_typedResult.readTableOrNull(_keywordMappingsRefsTable($_db));
    return drift.ProcessedTableManager(manager.$state.copyWith(prefetchedData: cache));
  }
}

class $$CategoriesTableFilterComposer extends drift.Composer<_$AppDatabase, $CategoriesTable> {
  $$CategoriesTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  drift.ColumnWithTypeConverterFilters<String, String, String> get id => $composableBuilder(
      column: $table.id, builder: (column) => drift.ColumnWithTypeConverterFilters(column));

  drift.ColumnFilters<String> get title =>
      $composableBuilder(column: $table.title, builder: (column) => drift.ColumnFilters(column));

  drift.ColumnFilters<String> get subtitle =>
      $composableBuilder(column: $table.subtitle, builder: (column) => drift.ColumnFilters(column));

  drift.ColumnFilters<String> get icon =>
      $composableBuilder(column: $table.icon, builder: (column) => drift.ColumnFilters(column));

  drift.ColumnWithTypeConverterFilters<String, String, String> get keywords => $composableBuilder(
      column: $table.keywords, builder: (column) => drift.ColumnWithTypeConverterFilters(column));

  drift.ColumnFilters<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => drift.ColumnFilters(column));

  drift.ColumnFilters<DateTime> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => drift.ColumnFilters(column));

  drift.Expression<bool> promptsRefs(
      drift.Expression<bool> Function($$PromptsTableFilterComposer f) f) {
    final $$PromptsTableFilterComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.id,
        referencedTable: $db.prompts,
        getReferencedColumn: (t) => t.categoryId,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer, $removeJoinBuilderFromRootComposer}) =>
            $$PromptsTableFilterComposer(
              $db: $db,
              $table: $db.prompts,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer: $removeJoinBuilderFromRootComposer,
            ));
    return f(composer);
  }

  drift.Expression<bool> keywordMappingsRefs(
      drift.Expression<bool> Function($$KeywordMappingsTableFilterComposer f) f) {
    final $$KeywordMappingsTableFilterComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.id,
        referencedTable: $db.keywordMappings,
        getReferencedColumn: (t) => t.categoryId,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer, $removeJoinBuilderFromRootComposer}) =>
            $$KeywordMappingsTableFilterComposer(
              $db: $db,
              $table: $db.keywordMappings,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer: $removeJoinBuilderFromRootComposer,
            ));
    return f(composer);
  }
}

class $$CategoriesTableOrderingComposer extends drift.Composer<_$AppDatabase, $CategoriesTable> {
  $$CategoriesTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  drift.ColumnOrderings<String> get id =>
      $composableBuilder(column: $table.id, builder: (column) => drift.ColumnOrderings(column));

  drift.ColumnOrderings<String> get title =>
      $composableBuilder(column: $table.title, builder: (column) => drift.ColumnOrderings(column));

  drift.ColumnOrderings<String> get subtitle => $composableBuilder(
      column: $table.subtitle, builder: (column) => drift.ColumnOrderings(column));

  drift.ColumnOrderings<String> get icon =>
      $composableBuilder(column: $table.icon, builder: (column) => drift.ColumnOrderings(column));

  drift.ColumnOrderings<String> get keywords => $composableBuilder(
      column: $table.keywords, builder: (column) => drift.ColumnOrderings(column));

  drift.ColumnOrderings<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => drift.ColumnOrderings(column));

  drift.ColumnOrderings<DateTime> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => drift.ColumnOrderings(column));
}

class $$CategoriesTableAnnotationComposer extends drift.Composer<_$AppDatabase, $CategoriesTable> {
  $$CategoriesTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  drift.GeneratedColumnWithTypeConverter<String, String> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  drift.GeneratedColumn<String> get title =>
      $composableBuilder(column: $table.title, builder: (column) => column);

  drift.GeneratedColumn<String> get subtitle =>
      $composableBuilder(column: $table.subtitle, builder: (column) => column);

  drift.GeneratedColumn<String> get icon =>
      $composableBuilder(column: $table.icon, builder: (column) => column);

  drift.GeneratedColumnWithTypeConverter<String, String> get keywords =>
      $composableBuilder(column: $table.keywords, builder: (column) => column);

  drift.GeneratedColumn<DateTime> get createdAt =>
      $composableBuilder(column: $table.createdAt, builder: (column) => column);

  drift.GeneratedColumn<DateTime> get updatedAt =>
      $composableBuilder(column: $table.updatedAt, builder: (column) => column);

  drift.Expression<T> promptsRefs<T extends Object>(
      drift.Expression<T> Function($$PromptsTableAnnotationComposer a) f) {
    final $$PromptsTableAnnotationComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.id,
        referencedTable: $db.prompts,
        getReferencedColumn: (t) => t.categoryId,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer, $removeJoinBuilderFromRootComposer}) =>
            $$PromptsTableAnnotationComposer(
              $db: $db,
              $table: $db.prompts,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer: $removeJoinBuilderFromRootComposer,
            ));
    return f(composer);
  }

  drift.Expression<T> keywordMappingsRefs<T extends Object>(
      drift.Expression<T> Function($$KeywordMappingsTableAnnotationComposer a) f) {
    final $$KeywordMappingsTableAnnotationComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.id,
        referencedTable: $db.keywordMappings,
        getReferencedColumn: (t) => t.categoryId,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer, $removeJoinBuilderFromRootComposer}) =>
            $$KeywordMappingsTableAnnotationComposer(
              $db: $db,
              $table: $db.keywordMappings,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer: $removeJoinBuilderFromRootComposer,
            ));
    return f(composer);
  }
}

class $$CategoriesTableTableManager extends drift.RootTableManager<
    _$AppDatabase,
    $CategoriesTable,
    Category,
    $$CategoriesTableFilterComposer,
    $$CategoriesTableOrderingComposer,
    $$CategoriesTableAnnotationComposer,
    $$CategoriesTableCreateCompanionBuilder,
    $$CategoriesTableUpdateCompanionBuilder,
    (Category, $$CategoriesTableReferences),
    Category,
    drift.PrefetchHooks Function({bool promptsRefs, bool keywordMappingsRefs})> {
  $$CategoriesTableTableManager(_$AppDatabase db, $CategoriesTable table)
      : super(drift.TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () => $$CategoriesTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () => $$CategoriesTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$CategoriesTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            drift.Value<String> id = const drift.Value.absent(),
            drift.Value<String> title = const drift.Value.absent(),
            drift.Value<String?> subtitle = const drift.Value.absent(),
            drift.Value<String?> icon = const drift.Value.absent(),
            drift.Value<String> keywords = const drift.Value.absent(),
            drift.Value<DateTime> createdAt = const drift.Value.absent(),
            drift.Value<DateTime> updatedAt = const drift.Value.absent(),
            drift.Value<int> rowid = const drift.Value.absent(),
          }) =>
              CategoriesCompanion(
            id: id,
            title: title,
            subtitle: subtitle,
            icon: icon,
            keywords: keywords,
            createdAt: createdAt,
            updatedAt: updatedAt,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String id,
            required String title,
            drift.Value<String?> subtitle = const drift.Value.absent(),
            drift.Value<String?> icon = const drift.Value.absent(),
            drift.Value<String> keywords = const drift.Value.absent(),
            drift.Value<DateTime> createdAt = const drift.Value.absent(),
            drift.Value<DateTime> updatedAt = const drift.Value.absent(),
            drift.Value<int> rowid = const drift.Value.absent(),
          }) =>
              CategoriesCompanion.insert(
            id: id,
            title: title,
            subtitle: subtitle,
            icon: icon,
            keywords: keywords,
            createdAt: createdAt,
            updatedAt: updatedAt,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), $$CategoriesTableReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: ({promptsRefs = false, keywordMappingsRefs = false}) {
            return drift.PrefetchHooks(
              db: db,
              explicitlyWatchedTables: [
                if (promptsRefs) db.prompts,
                if (keywordMappingsRefs) db.keywordMappings
              ],
              addJoins: null,
              getPrefetchedDataCallback: (items) async {
                return [
                  if (promptsRefs)
                    await drift.$_getPrefetchedData<Category, $CategoriesTable, Prompt>(
                        currentTable: table,
                        referencedTable: $$CategoriesTableReferences._promptsRefsTable(db),
                        managerFromTypedResult: (p0) =>
                            $$CategoriesTableReferences(db, table, p0).promptsRefs,
                        referencedItemsForCurrentItem: (item, referencedItems) =>
                            referencedItems.where((e) => e.categoryId == item.id),
                        typedResults: items),
                  if (keywordMappingsRefs)
                    await drift.$_getPrefetchedData<Category, $CategoriesTable, KeywordMapping>(
                        currentTable: table,
                        referencedTable: $$CategoriesTableReferences._keywordMappingsRefsTable(db),
                        managerFromTypedResult: (p0) =>
                            $$CategoriesTableReferences(db, table, p0).keywordMappingsRefs,
                        referencedItemsForCurrentItem: (item, referencedItems) =>
                            referencedItems.where((e) => e.categoryId == item.id),
                        typedResults: items)
                ];
              },
            );
          },
        ));
}

typedef $$CategoriesTableProcessedTableManager = drift.ProcessedTableManager<
    _$AppDatabase,
    $CategoriesTable,
    Category,
    $$CategoriesTableFilterComposer,
    $$CategoriesTableOrderingComposer,
    $$CategoriesTableAnnotationComposer,
    $$CategoriesTableCreateCompanionBuilder,
    $$CategoriesTableUpdateCompanionBuilder,
    (Category, $$CategoriesTableReferences),
    Category,
    drift.PrefetchHooks Function({bool promptsRefs, bool keywordMappingsRefs})>;
typedef $$PromptsTableCreateCompanionBuilder = PromptsCompanion Function({
  required String id,
  required String subtitle,
  required String title,
  required String source,
  drift.Value<DateTime> createdAt,
  drift.Value<bool> isSynced,
  required String categoryId,
  drift.Value<String> keywords,
  drift.Value<String> variables,
  drift.Value<int> version,
  drift.Value<int> popularityScore,
  drift.Value<DateTime?> serverSyncAt,
  drift.Value<DateTime?> lastUsedAt,
  drift.Value<int> rowid,
});
typedef $$PromptsTableUpdateCompanionBuilder = PromptsCompanion Function({
  drift.Value<String> id,
  drift.Value<String> subtitle,
  drift.Value<String> title,
  drift.Value<String> source,
  drift.Value<DateTime> createdAt,
  drift.Value<bool> isSynced,
  drift.Value<String> categoryId,
  drift.Value<String> keywords,
  drift.Value<String> variables,
  drift.Value<int> version,
  drift.Value<int> popularityScore,
  drift.Value<DateTime?> serverSyncAt,
  drift.Value<DateTime?> lastUsedAt,
  drift.Value<int> rowid,
});

final class $$PromptsTableReferences
    extends drift.BaseReferences<_$AppDatabase, $PromptsTable, Prompt> {
  $$PromptsTableReferences(super.$_db, super.$_table, super.$_typedResult);

  static $CategoriesTable _categoryIdTable(_$AppDatabase db) => db.categories
      .createAlias(drift.$_aliasNameGenerator(db.prompts.categoryId, db.categories.id));

  $$CategoriesTableProcessedTableManager get categoryId {
    final $_column = $_itemColumn<String>('category_id')!;

    final manager = $$CategoriesTableTableManager($_db, $_db.categories)
        .filter((f) => f.id.sqlEquals($_column));
    final item = $_typedResult.readTableOrNull(_categoryIdTable($_db));
    if (item == null) return manager;
    return drift.ProcessedTableManager(manager.$state.copyWith(prefetchedData: [item]));
  }

  static drift.MultiTypedResultKey<$LocalPromptUsageTable, List<LocalPromptUsageData>>
      _localPromptUsageRefsTable(_$AppDatabase db) =>
          drift.MultiTypedResultKey.fromTable(db.localPromptUsage,
              aliasName: drift.$_aliasNameGenerator(db.prompts.id, db.localPromptUsage.promptId));

  $$LocalPromptUsageTableProcessedTableManager get localPromptUsageRefs {
    final manager = $$LocalPromptUsageTableTableManager($_db, $_db.localPromptUsage)
        .filter((f) => f.promptId.id.sqlEquals($_itemColumn<String>('id')!));

    final cache = $_typedResult.readTableOrNull(_localPromptUsageRefsTable($_db));
    return drift.ProcessedTableManager(manager.$state.copyWith(prefetchedData: cache));
  }
}

class $$PromptsTableFilterComposer extends drift.Composer<_$AppDatabase, $PromptsTable> {
  $$PromptsTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  drift.ColumnWithTypeConverterFilters<String, String, String> get id => $composableBuilder(
      column: $table.id, builder: (column) => drift.ColumnWithTypeConverterFilters(column));

  drift.ColumnFilters<String> get subtitle =>
      $composableBuilder(column: $table.subtitle, builder: (column) => drift.ColumnFilters(column));

  drift.ColumnFilters<String> get title =>
      $composableBuilder(column: $table.title, builder: (column) => drift.ColumnFilters(column));

  drift.ColumnFilters<String> get source =>
      $composableBuilder(column: $table.source, builder: (column) => drift.ColumnFilters(column));

  drift.ColumnFilters<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => drift.ColumnFilters(column));

  drift.ColumnFilters<bool> get isSynced =>
      $composableBuilder(column: $table.isSynced, builder: (column) => drift.ColumnFilters(column));

  drift.ColumnWithTypeConverterFilters<String, String, String> get keywords => $composableBuilder(
      column: $table.keywords, builder: (column) => drift.ColumnWithTypeConverterFilters(column));

  drift.ColumnWithTypeConverterFilters<String, String, String> get variables => $composableBuilder(
      column: $table.variables, builder: (column) => drift.ColumnWithTypeConverterFilters(column));

  drift.ColumnFilters<int> get version =>
      $composableBuilder(column: $table.version, builder: (column) => drift.ColumnFilters(column));

  drift.ColumnFilters<int> get popularityScore => $composableBuilder(
      column: $table.popularityScore, builder: (column) => drift.ColumnFilters(column));

  drift.ColumnFilters<DateTime> get serverSyncAt => $composableBuilder(
      column: $table.serverSyncAt, builder: (column) => drift.ColumnFilters(column));

  drift.ColumnFilters<DateTime> get lastUsedAt => $composableBuilder(
      column: $table.lastUsedAt, builder: (column) => drift.ColumnFilters(column));

  $$CategoriesTableFilterComposer get categoryId {
    final $$CategoriesTableFilterComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.categoryId,
        referencedTable: $db.categories,
        getReferencedColumn: (t) => t.id,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer, $removeJoinBuilderFromRootComposer}) =>
            $$CategoriesTableFilterComposer(
              $db: $db,
              $table: $db.categories,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer: $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }

  drift.Expression<bool> localPromptUsageRefs(
      drift.Expression<bool> Function($$LocalPromptUsageTableFilterComposer f) f) {
    final $$LocalPromptUsageTableFilterComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.id,
        referencedTable: $db.localPromptUsage,
        getReferencedColumn: (t) => t.promptId,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer, $removeJoinBuilderFromRootComposer}) =>
            $$LocalPromptUsageTableFilterComposer(
              $db: $db,
              $table: $db.localPromptUsage,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer: $removeJoinBuilderFromRootComposer,
            ));
    return f(composer);
  }
}

class $$PromptsTableOrderingComposer extends drift.Composer<_$AppDatabase, $PromptsTable> {
  $$PromptsTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  drift.ColumnOrderings<String> get id =>
      $composableBuilder(column: $table.id, builder: (column) => drift.ColumnOrderings(column));

  drift.ColumnOrderings<String> get subtitle => $composableBuilder(
      column: $table.subtitle, builder: (column) => drift.ColumnOrderings(column));

  drift.ColumnOrderings<String> get title =>
      $composableBuilder(column: $table.title, builder: (column) => drift.ColumnOrderings(column));

  drift.ColumnOrderings<String> get source =>
      $composableBuilder(column: $table.source, builder: (column) => drift.ColumnOrderings(column));

  drift.ColumnOrderings<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => drift.ColumnOrderings(column));

  drift.ColumnOrderings<bool> get isSynced => $composableBuilder(
      column: $table.isSynced, builder: (column) => drift.ColumnOrderings(column));

  drift.ColumnOrderings<String> get keywords => $composableBuilder(
      column: $table.keywords, builder: (column) => drift.ColumnOrderings(column));

  drift.ColumnOrderings<String> get variables => $composableBuilder(
      column: $table.variables, builder: (column) => drift.ColumnOrderings(column));

  drift.ColumnOrderings<int> get version => $composableBuilder(
      column: $table.version, builder: (column) => drift.ColumnOrderings(column));

  drift.ColumnOrderings<int> get popularityScore => $composableBuilder(
      column: $table.popularityScore, builder: (column) => drift.ColumnOrderings(column));

  drift.ColumnOrderings<DateTime> get serverSyncAt => $composableBuilder(
      column: $table.serverSyncAt, builder: (column) => drift.ColumnOrderings(column));

  drift.ColumnOrderings<DateTime> get lastUsedAt => $composableBuilder(
      column: $table.lastUsedAt, builder: (column) => drift.ColumnOrderings(column));

  $$CategoriesTableOrderingComposer get categoryId {
    final $$CategoriesTableOrderingComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.categoryId,
        referencedTable: $db.categories,
        getReferencedColumn: (t) => t.id,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer, $removeJoinBuilderFromRootComposer}) =>
            $$CategoriesTableOrderingComposer(
              $db: $db,
              $table: $db.categories,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer: $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }
}

class $$PromptsTableAnnotationComposer extends drift.Composer<_$AppDatabase, $PromptsTable> {
  $$PromptsTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  drift.GeneratedColumnWithTypeConverter<String, String> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  drift.GeneratedColumn<String> get subtitle =>
      $composableBuilder(column: $table.subtitle, builder: (column) => column);

  drift.GeneratedColumn<String> get title =>
      $composableBuilder(column: $table.title, builder: (column) => column);

  drift.GeneratedColumn<String> get source =>
      $composableBuilder(column: $table.source, builder: (column) => column);

  drift.GeneratedColumn<DateTime> get createdAt =>
      $composableBuilder(column: $table.createdAt, builder: (column) => column);

  drift.GeneratedColumn<bool> get isSynced =>
      $composableBuilder(column: $table.isSynced, builder: (column) => column);

  drift.GeneratedColumnWithTypeConverter<String, String> get keywords =>
      $composableBuilder(column: $table.keywords, builder: (column) => column);

  drift.GeneratedColumnWithTypeConverter<String, String> get variables =>
      $composableBuilder(column: $table.variables, builder: (column) => column);

  drift.GeneratedColumn<int> get version =>
      $composableBuilder(column: $table.version, builder: (column) => column);

  drift.GeneratedColumn<int> get popularityScore =>
      $composableBuilder(column: $table.popularityScore, builder: (column) => column);

  drift.GeneratedColumn<DateTime> get serverSyncAt =>
      $composableBuilder(column: $table.serverSyncAt, builder: (column) => column);

  drift.GeneratedColumn<DateTime> get lastUsedAt =>
      $composableBuilder(column: $table.lastUsedAt, builder: (column) => column);

  $$CategoriesTableAnnotationComposer get categoryId {
    final $$CategoriesTableAnnotationComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.categoryId,
        referencedTable: $db.categories,
        getReferencedColumn: (t) => t.id,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer, $removeJoinBuilderFromRootComposer}) =>
            $$CategoriesTableAnnotationComposer(
              $db: $db,
              $table: $db.categories,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer: $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }

  drift.Expression<T> localPromptUsageRefs<T extends Object>(
      drift.Expression<T> Function($$LocalPromptUsageTableAnnotationComposer a) f) {
    final $$LocalPromptUsageTableAnnotationComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.id,
        referencedTable: $db.localPromptUsage,
        getReferencedColumn: (t) => t.promptId,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer, $removeJoinBuilderFromRootComposer}) =>
            $$LocalPromptUsageTableAnnotationComposer(
              $db: $db,
              $table: $db.localPromptUsage,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer: $removeJoinBuilderFromRootComposer,
            ));
    return f(composer);
  }
}

class $$PromptsTableTableManager extends drift.RootTableManager<
    _$AppDatabase,
    $PromptsTable,
    Prompt,
    $$PromptsTableFilterComposer,
    $$PromptsTableOrderingComposer,
    $$PromptsTableAnnotationComposer,
    $$PromptsTableCreateCompanionBuilder,
    $$PromptsTableUpdateCompanionBuilder,
    (Prompt, $$PromptsTableReferences),
    Prompt,
    drift.PrefetchHooks Function({bool categoryId, bool localPromptUsageRefs})> {
  $$PromptsTableTableManager(_$AppDatabase db, $PromptsTable table)
      : super(drift.TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () => $$PromptsTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () => $$PromptsTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$PromptsTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            drift.Value<String> id = const drift.Value.absent(),
            drift.Value<String> subtitle = const drift.Value.absent(),
            drift.Value<String> title = const drift.Value.absent(),
            drift.Value<String> source = const drift.Value.absent(),
            drift.Value<DateTime> createdAt = const drift.Value.absent(),
            drift.Value<bool> isSynced = const drift.Value.absent(),
            drift.Value<String> categoryId = const drift.Value.absent(),
            drift.Value<String> keywords = const drift.Value.absent(),
            drift.Value<String> variables = const drift.Value.absent(),
            drift.Value<int> version = const drift.Value.absent(),
            drift.Value<int> popularityScore = const drift.Value.absent(),
            drift.Value<DateTime?> serverSyncAt = const drift.Value.absent(),
            drift.Value<DateTime?> lastUsedAt = const drift.Value.absent(),
            drift.Value<int> rowid = const drift.Value.absent(),
          }) =>
              PromptsCompanion(
            id: id,
            subtitle: subtitle,
            title: title,
            source: source,
            createdAt: createdAt,
            isSynced: isSynced,
            categoryId: categoryId,
            keywords: keywords,
            variables: variables,
            version: version,
            popularityScore: popularityScore,
            serverSyncAt: serverSyncAt,
            lastUsedAt: lastUsedAt,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String id,
            required String subtitle,
            required String title,
            required String source,
            drift.Value<DateTime> createdAt = const drift.Value.absent(),
            drift.Value<bool> isSynced = const drift.Value.absent(),
            required String categoryId,
            drift.Value<String> keywords = const drift.Value.absent(),
            drift.Value<String> variables = const drift.Value.absent(),
            drift.Value<int> version = const drift.Value.absent(),
            drift.Value<int> popularityScore = const drift.Value.absent(),
            drift.Value<DateTime?> serverSyncAt = const drift.Value.absent(),
            drift.Value<DateTime?> lastUsedAt = const drift.Value.absent(),
            drift.Value<int> rowid = const drift.Value.absent(),
          }) =>
              PromptsCompanion.insert(
            id: id,
            subtitle: subtitle,
            title: title,
            source: source,
            createdAt: createdAt,
            isSynced: isSynced,
            categoryId: categoryId,
            keywords: keywords,
            variables: variables,
            version: version,
            popularityScore: popularityScore,
            serverSyncAt: serverSyncAt,
            lastUsedAt: lastUsedAt,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) =>
              p0.map((e) => (e.readTable(table), $$PromptsTableReferences(db, table, e))).toList(),
          prefetchHooksCallback: ({categoryId = false, localPromptUsageRefs = false}) {
            return drift.PrefetchHooks(
              db: db,
              explicitlyWatchedTables: [if (localPromptUsageRefs) db.localPromptUsage],
              addJoins: <
                  T extends drift.TableManagerState<dynamic, dynamic, dynamic, dynamic, dynamic,
                      dynamic, dynamic, dynamic, dynamic, dynamic, dynamic>>(state) {
                if (categoryId) {
                  state = state.withJoin(
                    currentTable: table,
                    currentColumn: table.categoryId,
                    referencedTable: $$PromptsTableReferences._categoryIdTable(db),
                    referencedColumn: $$PromptsTableReferences._categoryIdTable(db).id,
                  ) as T;
                }

                return state;
              },
              getPrefetchedDataCallback: (items) async {
                return [
                  if (localPromptUsageRefs)
                    await drift.$_getPrefetchedData<Prompt, $PromptsTable, LocalPromptUsageData>(
                        currentTable: table,
                        referencedTable: $$PromptsTableReferences._localPromptUsageRefsTable(db),
                        managerFromTypedResult: (p0) =>
                            $$PromptsTableReferences(db, table, p0).localPromptUsageRefs,
                        referencedItemsForCurrentItem: (item, referencedItems) =>
                            referencedItems.where((e) => e.promptId == item.id),
                        typedResults: items)
                ];
              },
            );
          },
        ));
}

typedef $$PromptsTableProcessedTableManager = drift.ProcessedTableManager<
    _$AppDatabase,
    $PromptsTable,
    Prompt,
    $$PromptsTableFilterComposer,
    $$PromptsTableOrderingComposer,
    $$PromptsTableAnnotationComposer,
    $$PromptsTableCreateCompanionBuilder,
    $$PromptsTableUpdateCompanionBuilder,
    (Prompt, $$PromptsTableReferences),
    Prompt,
    drift.PrefetchHooks Function({bool categoryId, bool localPromptUsageRefs})>;
typedef $$KeywordMappingsTableCreateCompanionBuilder = KeywordMappingsCompanion Function({
  required String id,
  required String categoryId,
  required String keyword,
  drift.Value<double> weight,
  drift.Value<DateTime> createdAt,
  drift.Value<int> rowid,
});
typedef $$KeywordMappingsTableUpdateCompanionBuilder = KeywordMappingsCompanion Function({
  drift.Value<String> id,
  drift.Value<String> categoryId,
  drift.Value<String> keyword,
  drift.Value<double> weight,
  drift.Value<DateTime> createdAt,
  drift.Value<int> rowid,
});

final class $$KeywordMappingsTableReferences
    extends drift.BaseReferences<_$AppDatabase, $KeywordMappingsTable, KeywordMapping> {
  $$KeywordMappingsTableReferences(super.$_db, super.$_table, super.$_typedResult);

  static $CategoriesTable _categoryIdTable(_$AppDatabase db) => db.categories
      .createAlias(drift.$_aliasNameGenerator(db.keywordMappings.categoryId, db.categories.id));

  $$CategoriesTableProcessedTableManager get categoryId {
    final $_column = $_itemColumn<String>('category_id')!;

    final manager = $$CategoriesTableTableManager($_db, $_db.categories)
        .filter((f) => f.id.sqlEquals($_column));
    final item = $_typedResult.readTableOrNull(_categoryIdTable($_db));
    if (item == null) return manager;
    return drift.ProcessedTableManager(manager.$state.copyWith(prefetchedData: [item]));
  }
}

class $$KeywordMappingsTableFilterComposer
    extends drift.Composer<_$AppDatabase, $KeywordMappingsTable> {
  $$KeywordMappingsTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  drift.ColumnWithTypeConverterFilters<String, String, String> get id => $composableBuilder(
      column: $table.id, builder: (column) => drift.ColumnWithTypeConverterFilters(column));

  drift.ColumnFilters<String> get keyword =>
      $composableBuilder(column: $table.keyword, builder: (column) => drift.ColumnFilters(column));

  drift.ColumnFilters<double> get weight =>
      $composableBuilder(column: $table.weight, builder: (column) => drift.ColumnFilters(column));

  drift.ColumnFilters<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => drift.ColumnFilters(column));

  $$CategoriesTableFilterComposer get categoryId {
    final $$CategoriesTableFilterComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.categoryId,
        referencedTable: $db.categories,
        getReferencedColumn: (t) => t.id,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer, $removeJoinBuilderFromRootComposer}) =>
            $$CategoriesTableFilterComposer(
              $db: $db,
              $table: $db.categories,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer: $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }
}

class $$KeywordMappingsTableOrderingComposer
    extends drift.Composer<_$AppDatabase, $KeywordMappingsTable> {
  $$KeywordMappingsTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  drift.ColumnOrderings<String> get id =>
      $composableBuilder(column: $table.id, builder: (column) => drift.ColumnOrderings(column));

  drift.ColumnOrderings<String> get keyword => $composableBuilder(
      column: $table.keyword, builder: (column) => drift.ColumnOrderings(column));

  drift.ColumnOrderings<double> get weight =>
      $composableBuilder(column: $table.weight, builder: (column) => drift.ColumnOrderings(column));

  drift.ColumnOrderings<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => drift.ColumnOrderings(column));

  $$CategoriesTableOrderingComposer get categoryId {
    final $$CategoriesTableOrderingComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.categoryId,
        referencedTable: $db.categories,
        getReferencedColumn: (t) => t.id,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer, $removeJoinBuilderFromRootComposer}) =>
            $$CategoriesTableOrderingComposer(
              $db: $db,
              $table: $db.categories,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer: $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }
}

class $$KeywordMappingsTableAnnotationComposer
    extends drift.Composer<_$AppDatabase, $KeywordMappingsTable> {
  $$KeywordMappingsTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  drift.GeneratedColumnWithTypeConverter<String, String> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  drift.GeneratedColumn<String> get keyword =>
      $composableBuilder(column: $table.keyword, builder: (column) => column);

  drift.GeneratedColumn<double> get weight =>
      $composableBuilder(column: $table.weight, builder: (column) => column);

  drift.GeneratedColumn<DateTime> get createdAt =>
      $composableBuilder(column: $table.createdAt, builder: (column) => column);

  $$CategoriesTableAnnotationComposer get categoryId {
    final $$CategoriesTableAnnotationComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.categoryId,
        referencedTable: $db.categories,
        getReferencedColumn: (t) => t.id,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer, $removeJoinBuilderFromRootComposer}) =>
            $$CategoriesTableAnnotationComposer(
              $db: $db,
              $table: $db.categories,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer: $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }
}

class $$KeywordMappingsTableTableManager extends drift.RootTableManager<
    _$AppDatabase,
    $KeywordMappingsTable,
    KeywordMapping,
    $$KeywordMappingsTableFilterComposer,
    $$KeywordMappingsTableOrderingComposer,
    $$KeywordMappingsTableAnnotationComposer,
    $$KeywordMappingsTableCreateCompanionBuilder,
    $$KeywordMappingsTableUpdateCompanionBuilder,
    (KeywordMapping, $$KeywordMappingsTableReferences),
    KeywordMapping,
    drift.PrefetchHooks Function({bool categoryId})> {
  $$KeywordMappingsTableTableManager(_$AppDatabase db, $KeywordMappingsTable table)
      : super(drift.TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$KeywordMappingsTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$KeywordMappingsTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$KeywordMappingsTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            drift.Value<String> id = const drift.Value.absent(),
            drift.Value<String> categoryId = const drift.Value.absent(),
            drift.Value<String> keyword = const drift.Value.absent(),
            drift.Value<double> weight = const drift.Value.absent(),
            drift.Value<DateTime> createdAt = const drift.Value.absent(),
            drift.Value<int> rowid = const drift.Value.absent(),
          }) =>
              KeywordMappingsCompanion(
            id: id,
            categoryId: categoryId,
            keyword: keyword,
            weight: weight,
            createdAt: createdAt,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String id,
            required String categoryId,
            required String keyword,
            drift.Value<double> weight = const drift.Value.absent(),
            drift.Value<DateTime> createdAt = const drift.Value.absent(),
            drift.Value<int> rowid = const drift.Value.absent(),
          }) =>
              KeywordMappingsCompanion.insert(
            id: id,
            categoryId: categoryId,
            keyword: keyword,
            weight: weight,
            createdAt: createdAt,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), $$KeywordMappingsTableReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: ({categoryId = false}) {
            return drift.PrefetchHooks(
              db: db,
              explicitlyWatchedTables: [],
              addJoins: <
                  T extends drift.TableManagerState<dynamic, dynamic, dynamic, dynamic, dynamic,
                      dynamic, dynamic, dynamic, dynamic, dynamic, dynamic>>(state) {
                if (categoryId) {
                  state = state.withJoin(
                    currentTable: table,
                    currentColumn: table.categoryId,
                    referencedTable: $$KeywordMappingsTableReferences._categoryIdTable(db),
                    referencedColumn: $$KeywordMappingsTableReferences._categoryIdTable(db).id,
                  ) as T;
                }

                return state;
              },
              getPrefetchedDataCallback: (items) async {
                return [];
              },
            );
          },
        ));
}

typedef $$KeywordMappingsTableProcessedTableManager = drift.ProcessedTableManager<
    _$AppDatabase,
    $KeywordMappingsTable,
    KeywordMapping,
    $$KeywordMappingsTableFilterComposer,
    $$KeywordMappingsTableOrderingComposer,
    $$KeywordMappingsTableAnnotationComposer,
    $$KeywordMappingsTableCreateCompanionBuilder,
    $$KeywordMappingsTableUpdateCompanionBuilder,
    (KeywordMapping, $$KeywordMappingsTableReferences),
    KeywordMapping,
    drift.PrefetchHooks Function({bool categoryId})>;
typedef $$Sp500TickersTableCreateCompanionBuilder = Sp500TickersCompanion Function({
  drift.Value<int> id,
  required String symbol,
  required String companyName,
  drift.Value<DateTime> lastUpdated,
});
typedef $$Sp500TickersTableUpdateCompanionBuilder = Sp500TickersCompanion Function({
  drift.Value<int> id,
  drift.Value<String> symbol,
  drift.Value<String> companyName,
  drift.Value<DateTime> lastUpdated,
});

class $$Sp500TickersTableFilterComposer extends drift.Composer<_$AppDatabase, $Sp500TickersTable> {
  $$Sp500TickersTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  drift.ColumnFilters<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => drift.ColumnFilters(column));

  drift.ColumnFilters<String> get symbol =>
      $composableBuilder(column: $table.symbol, builder: (column) => drift.ColumnFilters(column));

  drift.ColumnFilters<String> get companyName => $composableBuilder(
      column: $table.companyName, builder: (column) => drift.ColumnFilters(column));

  drift.ColumnWithTypeConverterFilters<DateTime, DateTime, String> get lastUpdated =>
      $composableBuilder(
          column: $table.lastUpdated,
          builder: (column) => drift.ColumnWithTypeConverterFilters(column));
}

class $$Sp500TickersTableOrderingComposer
    extends drift.Composer<_$AppDatabase, $Sp500TickersTable> {
  $$Sp500TickersTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  drift.ColumnOrderings<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => drift.ColumnOrderings(column));

  drift.ColumnOrderings<String> get symbol =>
      $composableBuilder(column: $table.symbol, builder: (column) => drift.ColumnOrderings(column));

  drift.ColumnOrderings<String> get companyName => $composableBuilder(
      column: $table.companyName, builder: (column) => drift.ColumnOrderings(column));

  drift.ColumnOrderings<String> get lastUpdated => $composableBuilder(
      column: $table.lastUpdated, builder: (column) => drift.ColumnOrderings(column));
}

class $$Sp500TickersTableAnnotationComposer
    extends drift.Composer<_$AppDatabase, $Sp500TickersTable> {
  $$Sp500TickersTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  drift.GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  drift.GeneratedColumn<String> get symbol =>
      $composableBuilder(column: $table.symbol, builder: (column) => column);

  drift.GeneratedColumn<String> get companyName =>
      $composableBuilder(column: $table.companyName, builder: (column) => column);

  drift.GeneratedColumnWithTypeConverter<DateTime, String> get lastUpdated =>
      $composableBuilder(column: $table.lastUpdated, builder: (column) => column);
}

class $$Sp500TickersTableTableManager extends drift.RootTableManager<
    _$AppDatabase,
    $Sp500TickersTable,
    Sp500Ticker,
    $$Sp500TickersTableFilterComposer,
    $$Sp500TickersTableOrderingComposer,
    $$Sp500TickersTableAnnotationComposer,
    $$Sp500TickersTableCreateCompanionBuilder,
    $$Sp500TickersTableUpdateCompanionBuilder,
    (Sp500Ticker, drift.BaseReferences<_$AppDatabase, $Sp500TickersTable, Sp500Ticker>),
    Sp500Ticker,
    drift.PrefetchHooks Function()> {
  $$Sp500TickersTableTableManager(_$AppDatabase db, $Sp500TickersTable table)
      : super(drift.TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () => $$Sp500TickersTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () => $$Sp500TickersTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$Sp500TickersTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            drift.Value<int> id = const drift.Value.absent(),
            drift.Value<String> symbol = const drift.Value.absent(),
            drift.Value<String> companyName = const drift.Value.absent(),
            drift.Value<DateTime> lastUpdated = const drift.Value.absent(),
          }) =>
              Sp500TickersCompanion(
            id: id,
            symbol: symbol,
            companyName: companyName,
            lastUpdated: lastUpdated,
          ),
          createCompanionCallback: ({
            drift.Value<int> id = const drift.Value.absent(),
            required String symbol,
            required String companyName,
            drift.Value<DateTime> lastUpdated = const drift.Value.absent(),
          }) =>
              Sp500TickersCompanion.insert(
            id: id,
            symbol: symbol,
            companyName: companyName,
            lastUpdated: lastUpdated,
          ),
          withReferenceMapper: (p0) =>
              p0.map((e) => (e.readTable(table), drift.BaseReferences(db, table, e))).toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$Sp500TickersTableProcessedTableManager = drift.ProcessedTableManager<
    _$AppDatabase,
    $Sp500TickersTable,
    Sp500Ticker,
    $$Sp500TickersTableFilterComposer,
    $$Sp500TickersTableOrderingComposer,
    $$Sp500TickersTableAnnotationComposer,
    $$Sp500TickersTableCreateCompanionBuilder,
    $$Sp500TickersTableUpdateCompanionBuilder,
    (Sp500Ticker, drift.BaseReferences<_$AppDatabase, $Sp500TickersTable, Sp500Ticker>),
    Sp500Ticker,
    drift.PrefetchHooks Function()>;
typedef $$LocalPromptUsageTableCreateCompanionBuilder = LocalPromptUsageCompanion Function({
  drift.Value<int> id,
  required String promptId,
  required String eventType,
  required int usedAt,
  drift.Value<String?> countryCode,
});
typedef $$LocalPromptUsageTableUpdateCompanionBuilder = LocalPromptUsageCompanion Function({
  drift.Value<int> id,
  drift.Value<String> promptId,
  drift.Value<String> eventType,
  drift.Value<int> usedAt,
  drift.Value<String?> countryCode,
});

final class $$LocalPromptUsageTableReferences
    extends drift.BaseReferences<_$AppDatabase, $LocalPromptUsageTable, LocalPromptUsageData> {
  $$LocalPromptUsageTableReferences(super.$_db, super.$_table, super.$_typedResult);

  static $PromptsTable _promptIdTable(_$AppDatabase db) => db.prompts
      .createAlias(drift.$_aliasNameGenerator(db.localPromptUsage.promptId, db.prompts.id));

  $$PromptsTableProcessedTableManager get promptId {
    final $_column = $_itemColumn<String>('prompt_id')!;

    final manager =
        $$PromptsTableTableManager($_db, $_db.prompts).filter((f) => f.id.sqlEquals($_column));
    final item = $_typedResult.readTableOrNull(_promptIdTable($_db));
    if (item == null) return manager;
    return drift.ProcessedTableManager(manager.$state.copyWith(prefetchedData: [item]));
  }
}

class $$LocalPromptUsageTableFilterComposer
    extends drift.Composer<_$AppDatabase, $LocalPromptUsageTable> {
  $$LocalPromptUsageTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  drift.ColumnFilters<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => drift.ColumnFilters(column));

  drift.ColumnFilters<String> get eventType => $composableBuilder(
      column: $table.eventType, builder: (column) => drift.ColumnFilters(column));

  drift.ColumnFilters<int> get usedAt =>
      $composableBuilder(column: $table.usedAt, builder: (column) => drift.ColumnFilters(column));

  drift.ColumnFilters<String> get countryCode => $composableBuilder(
      column: $table.countryCode, builder: (column) => drift.ColumnFilters(column));

  $$PromptsTableFilterComposer get promptId {
    final $$PromptsTableFilterComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.promptId,
        referencedTable: $db.prompts,
        getReferencedColumn: (t) => t.id,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer, $removeJoinBuilderFromRootComposer}) =>
            $$PromptsTableFilterComposer(
              $db: $db,
              $table: $db.prompts,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer: $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }
}

class $$LocalPromptUsageTableOrderingComposer
    extends drift.Composer<_$AppDatabase, $LocalPromptUsageTable> {
  $$LocalPromptUsageTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  drift.ColumnOrderings<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => drift.ColumnOrderings(column));

  drift.ColumnOrderings<String> get eventType => $composableBuilder(
      column: $table.eventType, builder: (column) => drift.ColumnOrderings(column));

  drift.ColumnOrderings<int> get usedAt =>
      $composableBuilder(column: $table.usedAt, builder: (column) => drift.ColumnOrderings(column));

  drift.ColumnOrderings<String> get countryCode => $composableBuilder(
      column: $table.countryCode, builder: (column) => drift.ColumnOrderings(column));

  $$PromptsTableOrderingComposer get promptId {
    final $$PromptsTableOrderingComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.promptId,
        referencedTable: $db.prompts,
        getReferencedColumn: (t) => t.id,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer, $removeJoinBuilderFromRootComposer}) =>
            $$PromptsTableOrderingComposer(
              $db: $db,
              $table: $db.prompts,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer: $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }
}

class $$LocalPromptUsageTableAnnotationComposer
    extends drift.Composer<_$AppDatabase, $LocalPromptUsageTable> {
  $$LocalPromptUsageTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  drift.GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  drift.GeneratedColumn<String> get eventType =>
      $composableBuilder(column: $table.eventType, builder: (column) => column);

  drift.GeneratedColumn<int> get usedAt =>
      $composableBuilder(column: $table.usedAt, builder: (column) => column);

  drift.GeneratedColumn<String> get countryCode =>
      $composableBuilder(column: $table.countryCode, builder: (column) => column);

  $$PromptsTableAnnotationComposer get promptId {
    final $$PromptsTableAnnotationComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.promptId,
        referencedTable: $db.prompts,
        getReferencedColumn: (t) => t.id,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer, $removeJoinBuilderFromRootComposer}) =>
            $$PromptsTableAnnotationComposer(
              $db: $db,
              $table: $db.prompts,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer: $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }
}

class $$LocalPromptUsageTableTableManager extends drift.RootTableManager<
    _$AppDatabase,
    $LocalPromptUsageTable,
    LocalPromptUsageData,
    $$LocalPromptUsageTableFilterComposer,
    $$LocalPromptUsageTableOrderingComposer,
    $$LocalPromptUsageTableAnnotationComposer,
    $$LocalPromptUsageTableCreateCompanionBuilder,
    $$LocalPromptUsageTableUpdateCompanionBuilder,
    (LocalPromptUsageData, $$LocalPromptUsageTableReferences),
    LocalPromptUsageData,
    drift.PrefetchHooks Function({bool promptId})> {
  $$LocalPromptUsageTableTableManager(_$AppDatabase db, $LocalPromptUsageTable table)
      : super(drift.TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$LocalPromptUsageTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$LocalPromptUsageTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$LocalPromptUsageTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            drift.Value<int> id = const drift.Value.absent(),
            drift.Value<String> promptId = const drift.Value.absent(),
            drift.Value<String> eventType = const drift.Value.absent(),
            drift.Value<int> usedAt = const drift.Value.absent(),
            drift.Value<String?> countryCode = const drift.Value.absent(),
          }) =>
              LocalPromptUsageCompanion(
            id: id,
            promptId: promptId,
            eventType: eventType,
            usedAt: usedAt,
            countryCode: countryCode,
          ),
          createCompanionCallback: ({
            drift.Value<int> id = const drift.Value.absent(),
            required String promptId,
            required String eventType,
            required int usedAt,
            drift.Value<String?> countryCode = const drift.Value.absent(),
          }) =>
              LocalPromptUsageCompanion.insert(
            id: id,
            promptId: promptId,
            eventType: eventType,
            usedAt: usedAt,
            countryCode: countryCode,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), $$LocalPromptUsageTableReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: ({promptId = false}) {
            return drift.PrefetchHooks(
              db: db,
              explicitlyWatchedTables: [],
              addJoins: <
                  T extends drift.TableManagerState<dynamic, dynamic, dynamic, dynamic, dynamic,
                      dynamic, dynamic, dynamic, dynamic, dynamic, dynamic>>(state) {
                if (promptId) {
                  state = state.withJoin(
                    currentTable: table,
                    currentColumn: table.promptId,
                    referencedTable: $$LocalPromptUsageTableReferences._promptIdTable(db),
                    referencedColumn: $$LocalPromptUsageTableReferences._promptIdTable(db).id,
                  ) as T;
                }

                return state;
              },
              getPrefetchedDataCallback: (items) async {
                return [];
              },
            );
          },
        ));
}

typedef $$LocalPromptUsageTableProcessedTableManager = drift.ProcessedTableManager<
    _$AppDatabase,
    $LocalPromptUsageTable,
    LocalPromptUsageData,
    $$LocalPromptUsageTableFilterComposer,
    $$LocalPromptUsageTableOrderingComposer,
    $$LocalPromptUsageTableAnnotationComposer,
    $$LocalPromptUsageTableCreateCompanionBuilder,
    $$LocalPromptUsageTableUpdateCompanionBuilder,
    (LocalPromptUsageData, $$LocalPromptUsageTableReferences),
    LocalPromptUsageData,
    drift.PrefetchHooks Function({bool promptId})>;

class $AppDatabaseManager {
  final _$AppDatabase _db;
  $AppDatabaseManager(this._db);
  $$CategoriesTableTableManager get categories =>
      $$CategoriesTableTableManager(_db, _db.categories);
  $$PromptsTableTableManager get prompts => $$PromptsTableTableManager(_db, _db.prompts);
  $$KeywordMappingsTableTableManager get keywordMappings =>
      $$KeywordMappingsTableTableManager(_db, _db.keywordMappings);
  $$Sp500TickersTableTableManager get sp500Tickers =>
      $$Sp500TickersTableTableManager(_db, _db.sp500Tickers);
  $$LocalPromptUsageTableTableManager get localPromptUsage =>
      $$LocalPromptUsageTableTableManager(_db, _db.localPromptUsage);
}
