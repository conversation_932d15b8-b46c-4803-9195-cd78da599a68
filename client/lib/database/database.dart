import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:drift/drift.dart';
import 'package:drift/drift.dart' as drift;
import 'package:drift/native.dart' show NativeDatabase;
import 'package:intl/intl.dart';
import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';
import 'package:promz/database/db_utils.dart';
import 'package:promz/features/home/<USER>/local_prompt_usage_repository.dart';
import 'package:promz_common/promz_common.dart';
import 'package:sqflite/sqflite.dart';
import 'package:synchronized/synchronized.dart';

part 'database.g.dart';

// Custom type converter for UUID
class UuidConverter extends TypeConverter<String, String> {
  const UuidConverter();
  @override
  String fromSql(String fromDb) => fromDb;
  @override
  String toSql(String value) => value;
}

// Custom type converter for JSON
class JsonConverter extends TypeConverter<String, String> {
  const JsonConverter();
  @override
  String fromSql(String fromDb) => fromDb;
  @override
  String toSql(String value) {
    try {
      // Validate that the string is valid JSON
      json.decode(value);
      return value;
    } catch (e) {
      // If not valid JSON, try to encode it
      try {
        return json.encode(value);
      } catch (e) {
        // If all fails, return an empty JSON array/object
        return value.startsWith('[') ? '[]' : '{}';
      }
    }
  }
}

class DateTimeConverter extends TypeConverter<DateTime, String> {
  const DateTimeConverter();

  @override
  DateTime fromSql(String fromDb) {
    try {
      return DateTime.parse(fromDb);
    } catch (e) {
      appLog.error('Error parsing DateTime from database: $fromDb');
      return DateTime.now(); // Provide a default value in case of parsing errors
    }
  }

  @override
  String toSql(DateTime value) {
    return value.toIso8601String();
  }
}

@DataClassName('Sp500Ticker')
class Sp500Tickers extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get symbol => text().withLength(min: 1, max: 10)();
  TextColumn get companyName => text().withLength(min: 1)();
  TextColumn get lastUpdated => text()
      .map(const DateTimeConverter())
      .withDefault(Constant(DateTime.now().toIso8601String()))();
}

@DataClassName('Prompt')
class Prompts extends Table {
  TextColumn get id => text().map(const UuidConverter())();
  TextColumn get subtitle => text()(); // Renamed from content to subtitle
  TextColumn get title => text().withLength(min: 1)();
  TextColumn get source =>
      text().withLength(min: 1).customConstraint("NOT NULL CHECK (source IN ('local', 'cloud'))")();
  DateTimeColumn get createdAt => dateTime().withDefault(Constant(DateTime.now()))();
  BoolColumn get isSynced => boolean().withDefault(const Constant(false))();
  TextColumn get categoryId => text().map(const UuidConverter()).references(Categories, #id)();
  TextColumn get keywords => text().map(const JsonConverter()).withDefault(const Constant('[]'))();
  TextColumn get variables => text().map(const JsonConverter()).withDefault(const Constant('[]'))();
  IntColumn get version => integer().withDefault(const Constant(1))();
  IntColumn get popularityScore => integer().withDefault(const Constant(0))();
  DateTimeColumn get serverSyncAt => dateTime().nullable()();
  DateTimeColumn get lastUsedAt => dateTime().nullable()();

  @override
  Set<Column> get primaryKey => {id};
}

@DataClassName('KeywordMapping')
class KeywordMappings extends Table {
  TextColumn get id => text().map(const UuidConverter())();
  TextColumn get categoryId => text().map(const UuidConverter()).references(Categories, #id)();
  TextColumn get keyword => text()();
  RealColumn get weight => real().withDefault(const Constant(1.0))();
  DateTimeColumn get createdAt => dateTime().withDefault(Constant(DateTime.now()))();

  @override
  Set<Column> get primaryKey => {id};
}

@DataClassName('Category')
class Categories extends Table {
  TextColumn get id => text().map(const UuidConverter())();
  TextColumn get title => text().withLength(min: 1)();
  TextColumn get subtitle => text().nullable().withDefault(const Constant(''))();
  TextColumn get icon => text().nullable().withDefault(const Constant('folder'))();
  TextColumn get keywords => text().map(const JsonConverter()).withDefault(const Constant('[]'))();
  DateTimeColumn get createdAt => dateTime().withDefault(Constant(DateTime.now()))();
  DateTimeColumn get updatedAt => dateTime().withDefault(Constant(DateTime.now()))();

  @override
  Set<Column> get primaryKey => {id};
}

// New table for local prompt usage
class LocalPromptUsage extends Table {
  IntColumn get id => integer().autoIncrement()();
  TextColumn get promptId => text().references(Prompts, #id)();
  TextColumn get eventType =>
      text().customConstraint("NOT NULL CHECK (event_type IN ('selected', 'executed'))")();
  IntColumn get usedAt => integer().named('used_at')();
  TextColumn get countryCode => text().nullable()();
}

@DriftDatabase(tables: [Prompts, Categories, KeywordMappings, Sp500Tickers, LocalPromptUsage])
class AppDatabase extends _$AppDatabase {
  static const String _logName = 'AppDatabase';

  // Private constructor
  AppDatabase._(QueryExecutor e) : super(e);

  // For singleton pattern
  static AppDatabase? _instance;
  static final _lock = Lock();
  static bool _forTesting = false;

  // Track if database is being closed
  bool _isClosing = false;

  // Version number - match server schema version
  @override
  int get schemaVersion => DatabaseUtils.expectedSchemaVersion;

  @override
  MigrationStrategy get migration => MigrationStrategy(
        beforeOpen: (details) async {
          await customStatement('PRAGMA foreign_keys = ON');
          appLog.debug('Database opened', name: _logName);

          // We specifically expect schema version 2 for the "subtitle" field
          if (details.versionBefore != null &&
              details.versionBefore! < DatabaseUtils.expectedSchemaVersion) {
            appLog.warning('Found outdated schema version. Database may need to be recreated.',
                name: _logName);
          }
        },
        onCreate: (m) async {
          appLog.debug('Creating database schema', name: _logName);
          await m.createAll();
          // Create local_prompt_usage table
          await m.createTable(localPromptUsage);
        },
        // No need for complex migrations during development
        // If schema changes, we can simply delete the database and recreate
      );

  bool get isClosing => _isClosing;

  static Future<AppDatabase> getInstanceForTesting() async {
    _forTesting = true;
    return getInstance();
  }

  /// Gets the singleton instance of AppDatabase
  static Future<AppDatabase> getInstance() async {
    appLog.debug('getInstance() START', name: _logName);

    if (_instance == null) {
      appLog.debug('No existing instance, creating new one', name: _logName);

      await _lock.synchronized(() async {
        if (_instance == null) {
          appLog.debug('Creating database connection', name: _logName);

          final executor = _openConnection(_forTesting);
          _instance = AppDatabase._(executor);

          try {
            await _instance!.executor.ensureOpen(_instance!);
            appLog.debug('Database connection verified', name: _logName);

            await _instance!.customSelect('SELECT 1').get();
            appLog.debug('Database connection is working', name: _logName);
          } catch (e, stack) {
            appLog.error('Error verifying database connection',
                name: _logName, error: e, stackTrace: stack);
            rethrow;
          }
        }
      });
    }

    appLog.debug('getInstance() END - success', name: _logName);
    return _instance!;
  }

  /// Closes the database connection
  @override
  Future<void> close() async {
    appLog.debug('close() START', name: _logName);
    _isClosing = true;
    await _lock.synchronized(() async {
      if (_instance != null) {
        await super.close();
        _instance = null;
        appLog.debug('close() END - Database connection closed', name: _logName);
      }
    });
  }

  /// Ensures the database is open and ready
  Future<void> ensureOpen() async {
    if (_instance == null) {
      await getInstance();
    }

    try {
      await ensureOpenConnection();
    } catch (e, stackTrace) {
      appLog.error('ensureOpen() END - Error', name: _logName, error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  List<String> splitSqlStatements(String sql) {
    return sql.split(';').map((s) => s.trim()).where((s) => s.isNotEmpty).toList();
  }

  Future<void> ensureOpenConnection() async {
    try {
      await executor.ensureOpen(this);
      await customSelect('SELECT 1').get();
    } catch (e, stackTrace) {
      appLog.error('Failed to open database', name: _logName, error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// Verify that the database has the required seed data
  Future<void> seedDatabase() async {
    try {
      appLog.debug('Verifying database seed data...', name: _logName);

      // Check categories
      final categories = await select(this.categories).get();
      if (categories.isEmpty) {
        throw Exception('Categories table is empty. Database must be pre-seeded with categories.');
      }

      // Check prompts
      final prompts = await select(this.prompts).get();
      if (prompts.isEmpty) {
        throw Exception('Prompts table is empty. Database must be pre-seeded with prompts.');
      }

      appLog.debug('Database seed data verification complete', name: _logName);
    } catch (e, stack) {
      appLog.error('Database seed data verification failed',
          name: _logName, error: e, stackTrace: stack);
      rethrow;
    }
  }

  // Updated query methods to use String IDs
  Future<List<Prompt>> getAllPrompts() {
    if (_isClosing) {
      appLog.debug('Database is closing, cannot execute query', name: _logName);
      return Future.value([]);
    }
    return select(prompts).get();
  }

  Future<Prompt?> getPrompt(String id) =>
      (select(prompts)..where((p) => p.id.equals(id))).getSingleOrNull();

  Future<List<Prompt>> getPromptsByCategory(String categoryId) {
    return (select(prompts)..where((p) => p.categoryId.equals(categoryId))).get();
  }

  Future<String> insertPrompt(PromptsCompanion prompt) async {
    // Validate required fields
    if (!prompt.id.present || prompt.id.value.isEmpty) {
      throw Exception('ID is required for a prompt');
    }
    if (!prompt.title.present || prompt.title.value.isEmpty) {
      throw Exception('Title is required for a prompt');
    }
    if (!prompt.subtitle.present || prompt.subtitle.value.isEmpty) {
      throw Exception('Subtitle is required for a prompt');
    }
    if (!prompt.keywords.present || prompt.keywords.value.isEmpty) {
      throw Exception('Keywords are required for a prompt');
    }

    // Set default values for optional fields
    if (!prompt.version.present) {
      prompt = prompt.copyWith(version: const Value(1));
    }
    if (!prompt.createdAt.present) {
      prompt = prompt.copyWith(createdAt: Value(DateTime.now()));
    }
    if (!prompt.isSynced.present) {
      prompt = prompt.copyWith(isSynced: const Value(false));
    }

    // Generate new UUID if not present
    await into(prompts).insert(prompt);
    return prompt.id.value;
  }

  Future<bool> updatePrompt(Prompt prompt) => update(prompts).replace(prompt);

  Future<int> deletePrompt(String id) => (delete(prompts)..where((p) => p.id.equals(id))).go();

  // Method to get all SP500 tickers with pagination support
  Future<List<Sp500Ticker>> getAllSP500Tickers({int limit = 0, int offset = 0}) async {
    try {
      await ensureOpen();
      if (_isClosing) {
        appLog.warning('Database is closing, cannot execute query', name: _logName);
        return [];
      }

      // Build the query with optional pagination
      final query = select(sp500Tickers);

      // Apply pagination if limit is specified
      if (limit > 0) {
        query.limit(limit, offset: offset);
      }

      // Order by symbol for consistency
      query.orderBy([(t) => OrderingTerm.asc(t.symbol)]);

      final result = await query.get();
      appLog.debug('Retrieved ${result.length} SP500 tickers, offset: $offset', name: _logName);
      return result;
    } catch (e, stack) {
      appLog.error('Error fetching SP500 tickers', name: _logName, error: e, stackTrace: stack);
      return [];
    }
  }

  // Method to get count of SP500 tickers
  Future<int> getSP500TickerCount() async {
    try {
      await ensureOpen();
      if (_isClosing) {
        appLog.warning('Database is closing, cannot execute query', name: _logName);
        return 0;
      }

      final result = await customSelect('SELECT COUNT(*) as count FROM sp500_tickers').getSingle();
      final count = result.data['count'] as int;
      appLog.debug('SP500 ticker count: $count', name: _logName);
      return count;
    } catch (e, stack) {
      appLog.error('Error counting SP500 tickers', name: _logName, error: e, stackTrace: stack);
      return 0;
    }
  }

  // Implement getRecentPrompts method using last_used_at approach
  Future<List<Prompt>> getRecentPrompts({int limit = 5}) async {
    try {
      final query = select(prompts)
        ..orderBy([(t) => OrderingTerm.desc(t.lastUsedAt)])
        ..limit(limit);

      final results = await query.get();
      appLog.debug('Found ${results.length} recent prompts', name: _logName);
      return results;
    } catch (e, stack) {
      appLog.error('Error getting recent prompts', name: _logName, error: e, stackTrace: stack);
      return [];
    }
  }

  // Simplified getPopularPrompts method that uses popularity_score column in prompts table
  Future<List<Prompt>> getPopularPrompts({int limit = 5}) async {
    try {
      appLog.debug('getPopularPrompts() START with limit=$limit', name: _logName);

      // Query prompts table directly using the popularity_score column
      final query = select(prompts)
        ..where((p) => p.popularityScore.isBiggerThan(const Constant(0)))
        ..orderBy([(t) => OrderingTerm.desc(t.popularityScore)])
        ..limit(limit);

      final results = await query.get();

      appLog.debug('Found ${results.length} popular prompts', name: _logName);
      return results;
    } catch (e, stack) {
      appLog.error('Error getting popular prompts', name: _logName, error: e, stackTrace: stack);
      return [];
    }
  }

  // Method to update last_used_at timestamp when a prompt is used
  Future<void> updatePromptLastUsed(String promptId) async {
    try {
      await (update(prompts)..where((p) => p.id.equals(promptId))).write(PromptsCompanion(
        lastUsedAt: Value(DateTime.now()),
      ));
      appLog.debug('Updated last_used_at for prompt $promptId', name: _logName);
    } catch (e, stack) {
      appLog.error('Error updating last_used_at for prompt',
          name: _logName, error: e, stackTrace: stack);
    }
  }

  // Method to sync popular prompts with server data
  Future<void> syncPopularPrompts(List<Map<String, dynamic>> serverData) async {
    try {
      await ensureOpen();
      if (_isClosing) {
        appLog.warning('Database is closing, cannot execute query', name: _logName);
        return;
      }

      appLog.debug('Syncing ${serverData.length} popular prompts', name: _logName);

      // Begin transaction
      await transaction(() async {
        for (final promptData in serverData) {
          // Fix: Safely handle null value for prompt_id
          final promptId = promptData['prompt_id']?.toString();
          if (promptId == null || promptId.isEmpty) {
            appLog.warning('Skipping prompt data with missing prompt_id', name: _logName);
            continue;
          }

          final score = promptData['score'] as int? ?? 0;

          // Check if prompt exists in the database
          final existingPrompt =
              await (select(prompts)..where((p) => p.id.equals(promptId))).getSingleOrNull();

          if (existingPrompt != null) {
            // Update existing prompt's popularity score and server sync time
            await (update(prompts)..where((p) => p.id.equals(promptId))).write(
              PromptsCompanion(
                popularityScore: Value(score),
                serverSyncAt: Value(DateTime.now()),
              ),
            );
          }
        }
      });

      appLog.debug('Synced ${serverData.length} popular prompts', name: _logName);
    } catch (e, stack) {
      appLog.error('Error syncing popular prompts', name: _logName, error: e, stackTrace: stack);
    }
  }

  // Method to store popular prompts with complete data
  Future<void> storePopularPrompts(List<Map<String, dynamic>> promptsData) async {
    try {
      await ensureOpen();
      if (_isClosing) {
        appLog.warning('Database is closing, cannot execute query', name: _logName);
        return;
      }

      appLog.debug('Storing ${promptsData.length} popular prompts', name: _logName);

      // Begin transaction
      await transaction(() async {
        // For each prompt, store it in the prompts table with popularity_score
        for (final promptData in promptsData) {
          final promptId = promptData['id']?.toString() ?? '';
          if (promptId.isEmpty) {
            appLog.warning('Skipping prompt with missing ID', name: _logName);
            continue;
          }

          // Store the base prompt data
          await insertOrUpdatePrompt(promptData);

          // Get and store the popularity score
          final popularityScore = promptData['popularity_score'] ?? 0;
          final rating = popularityScore is num ? popularityScore.toInt() : 0;

          // Update rating directly in prompts table
          await (update(prompts)..where((p) => p.id.equals(promptId))).write(
            PromptsCompanion(
              popularityScore: Value(rating),
              serverSyncAt: Value(DateTime.now()),
            ),
          );

          appLog.debug('Stored popular prompt: $promptId with popularity score $rating',
              name: _logName);
        }
      });

      appLog.debug('Successfully stored all popular prompts', name: _logName);
    } catch (e, stack) {
      appLog.error('Error storing popular prompts', name: _logName, error: e, stackTrace: stack);
    }
  }

  /// Sync prompts from the server
  Future<void> syncPrompts(List<Map<String, dynamic>> serverData, DateTime syncTime) async {
    try {
      await ensureOpen();
      if (_isClosing) {
        appLog.warning('Database is closing, cannot execute query', name: _logName);
        return;
      }

      int updatedCount = 0;
      int insertedCount = 0;
      int unchangedCount = 0;

      // Begin transaction
      await transaction(() async {
        for (final promptData in serverData) {
          final promptId = promptData['id'] as String;

          // Check if prompt already exists
          final existingPrompt =
              await (select(prompts)..where((p) => p.id.equals(promptId))).getSingleOrNull();

          // Extract keywords and variables from the JSON data
          final keywordsJson =
              promptData['keywords'] != null ? jsonEncode(promptData['keywords']) : '[]';
          final variablesJson =
              promptData['variables'] != null ? jsonEncode(promptData['variables']) : '[]';

          // Parse the server's updated_at timestamp
          DateTime? serverUpdatedAt;
          if (promptData['updated_at'] != null) {
            try {
              serverUpdatedAt = DateTime.parse(promptData['updated_at'] as String);
            } catch (e) {
              appLog.warning('Failed to parse updated_at: ${promptData['updated_at']}',
                  name: _logName);
            }
          }

          // Create a companion object for the prompt
          final companion = PromptsCompanion(
            id: Value(promptId),
            title: Value(promptData['title'] as String? ?? ''),
            subtitle: Value(promptData['subtitle'] as String? ?? ''),
            source: const Value('cloud'), // Mark as cloud source
            categoryId: Value(promptData['category_id'] as String? ?? ''),
            keywords: Value(keywordsJson),
            variables: Value(variablesJson),
            createdAt: Value(DateTime.parse(
                promptData['created_at'] as String? ?? DateTime.now().toIso8601String())),
            isSynced: const Value(true),
            serverSyncAt: Value(syncTime),
          );

          if (existingPrompt != null) {
            // Check if there are actual changes to update
            bool hasChanges = false;

            // Check title and subtitle changes
            if (existingPrompt.title != (promptData['title'] as String? ?? '') ||
                existingPrompt.subtitle != (promptData['subtitle'] as String? ?? '')) {
              hasChanges = true;
              appLog.debug('Title or subtitle changed for prompt: $promptId', name: _logName);
            }

            // Check if server data is newer based on updated_at
            if (serverUpdatedAt != null && existingPrompt.serverSyncAt != null) {
              if (serverUpdatedAt.isAfter(existingPrompt.serverSyncAt!)) {
                hasChanges = true;
                appLog.debug('Server has newer data for prompt: $promptId', name: _logName);
              }
            } else if (serverUpdatedAt != null) {
              hasChanges = true;
              appLog.debug('Server has updated_at; local has no serverSyncAt: $promptId',
                  name: _logName);
            }

            // Check for changes in other fields
            if (!hasChanges) {
              // Compare keywords
              try {
                final existingKeywords = json.decode(existingPrompt.keywords);
                final newKeywords = promptData['keywords'] ?? [];
                if (!_areListsEqual(existingKeywords, newKeywords)) {
                  hasChanges = true;
                  appLog.debug('Keywords changed for prompt: $promptId', name: _logName);
                }
              } catch (e) {
                appLog.warning('Error comparing keywords for prompt: $promptId',
                    name: _logName, error: e);
                // Assume changes if we can't compare
                hasChanges = true;
              }
            }

            if (hasChanges) {
              // Update existing prompt because there are changes
              await (update(prompts)..where((p) => p.id.equals(promptId))).write(companion);
              appLog.debug('Updated existing prompt from server: $promptId', name: _logName);
              updatedCount++;
            } else {
              unchangedCount++;
            }
          } else {
            // Insert new prompt
            await into(prompts).insert(companion);
            appLog.debug('Inserted new prompt from server: $promptId', name: _logName);
            insertedCount++;
          }
        }
      });

      appLog.debug(
          'Synced ${serverData.length} prompts from server: $insertedCount inserted, $updatedCount updated, $unchangedCount unchanged',
          name: _logName);
    } catch (e, stack) {
      appLog.error('Error syncing prompts from server',
          name: _logName, error: e, stackTrace: stack);
    }
  }

  /// Helper method to compare two lists for equality
  bool _areListsEqual(dynamic list1, dynamic list2) {
    if (list1 is! List || list2 is! List) return false;
    if (list1.length != list2.length) return false;

    // Sort both lists if possible to ensure consistent comparison
    try {
      final sortedList1 = List.from(list1)..sort();
      final sortedList2 = List.from(list2)..sort();

      for (int i = 0; i < sortedList1.length; i++) {
        if (sortedList1[i] != sortedList2[i]) return false;
      }
      return true;
    } catch (e) {
      // If we can't sort (e.g., mixed types), do a simple comparison
      for (final item in list1) {
        if (!list2.contains(item)) return false;
      }
      for (final item in list2) {
        if (!list1.contains(item)) return false;
      }
      return true;
    }
  }

  // New methods for local_prompt_usage table

  // Method to insert a prompt usage event
  Future<int> insertLocalPromptUsage(LocalPromptUsageCompanion entry) async {
    return into(localPromptUsage).insert(entry);
  }

  // Method to get all prompt usage events
  Future<List<LocalPromptUsageData>> getAllLocalPromptUsage() async {
    return select(localPromptUsage).get();
  }

  // Method to clear the local_prompt_usage table
  Future<int> clearLocalPromptUsage() async {
    return delete(localPromptUsage).go();
  }

  // Method to get the device's country code
  String getDeviceCountryCode() {
    try {
      // Use intl package to get the current locale
      final locale = Intl.getCurrentLocale();
      // Extract the country code from the locale (e.g., 'US' from 'en_US')
      return locale.split('_').last.toUpperCase();
    } catch (e) {
      appLog.error('Error getting device country code: \$e', name: _logName);
      return ''; // Return empty string if unable to get the country code
    }
  }

  // Add getter for LocalPromptUsageRepository
  LocalPromptUsageRepository get localPromptUsageRepository => LocalPromptUsageRepository(this);

  // Method to check if a prompt exists in the database
  Future<bool> promptExists(String promptId) async {
    try {
      await ensureOpen();
      if (_isClosing) {
        appLog.warning('Database is closing, cannot execute query', name: _logName);
        return false;
      }

      final result = await (select(prompts)..where((p) => p.id.equals(promptId))).getSingleOrNull();
      return result != null;
    } catch (e, stack) {
      appLog.error('Error checking if prompt exists', name: _logName, error: e, stackTrace: stack);
      return false;
    }
  }

  // Method to insert or update a prompt
  Future<void> insertOrUpdatePrompt(Map<String, dynamic> promptData) async {
    try {
      await ensureOpen();
      if (_isClosing) {
        appLog.warning('Database is closing, cannot execute query', name: _logName);
        return;
      }

      final promptId = promptData['id'] as String;

      // Check if prompt already exists
      final exists = await promptExists(promptId);

      // Create a companion object for the prompt
      final companion = PromptsCompanion(
        id: Value(promptId),
        title: Value(promptData['title'] as String? ?? ''),
        subtitle: Value(promptData['subtitle'] as String? ?? ''),
        source: Value(promptData['source'] as String? ?? ''),
        categoryId: Value(promptData['category_id'] as String? ?? ''),
        keywords: Value(promptData['keywords'] != null ? jsonEncode(promptData['keywords']) : '[]'),
        createdAt: Value(DateTime.parse(
            promptData['created_at'] as String? ?? DateTime.now().toIso8601String())),
        lastUsedAt: Value(DateTime.now()),
      );

      if (exists) {
        // Update existing prompt
        await (update(prompts)..where((p) => p.id.equals(promptId))).write(companion);
        appLog.debug('Updated existing prompt: $promptId', name: _logName);
      } else {
        // Insert new prompt
        await into(prompts).insert(companion);
        appLog.debug('Inserted new prompt: $promptId', name: _logName);
      }
    } catch (e, stack) {
      appLog.error('Error inserting/updating prompt', name: _logName, error: e, stackTrace: stack);
    }
  }

  // Method to get a prompt by ID
  Future<Prompt?> getPromptById(String promptId) async {
    try {
      await ensureOpen();
      if (_isClosing) {
        appLog.warning('Database is closing, cannot execute query', name: _logName);
        return null;
      }

      final result = await (select(prompts)..where((p) => p.id.equals(promptId))).getSingleOrNull();
      if (result != null) {
        appLog.debug('Found prompt with ID $promptId: ${result.title}', name: _logName);
      } else {
        appLog.debug('No prompt found with ID $promptId', name: _logName);
      }
      return result;
    } catch (e, stack) {
      appLog.error('Error getting prompt by ID', name: _logName, error: e, stackTrace: stack);
      return null;
    }
  }
}

Future<File> _getDbFileForTesting() async {
  // Get application documents directory
  final dir = await getApplicationDocumentsDirectory();

  // Check if directory exists
  if (!await dir.exists()) {
    await dir.create(recursive: true);
  }

  final dbFolder = dir.path;
  return File(p.join(dbFolder, 'promz.db'));
}

Future<File> _getDbFileForApp() async {
  final dir = await getDatabasesPath();
  final path = p.join(dir, 'promz.db');
  return File(path);
}

LazyDatabase _openConnection(bool forTesting) {
  const String logName = 'AppDatabase:_openConnection';

  return LazyDatabase(() async {
    try {
      appLog.debug('Opening database connection...', name: logName);

      final dbFile = forTesting ? await _getDbFileForTesting() : await _getDbFileForApp();

      // If db file doesn't exist, copy from assets
      if (!await dbFile.exists()) {
        appLog.debug('Database file not found, copying from assets...', name: logName);
        await DatabaseUtils.recreateDatabase(dbFile);
      }

      final database = NativeDatabase.createInBackground(dbFile);
      appLog.debug('Database opened successfully', name: logName);
      return database;
    } catch (e, stackTrace) {
      appLog.error('Failed to open database connection',
          name: logName, error: e, stackTrace: stackTrace);
      rethrow;
    }
  });
}
