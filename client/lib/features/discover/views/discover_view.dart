import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:promz_common/promz_common.dart';
import 'package:promz/core/models/category.dart';
import 'package:promz/core/widgets/promz_app_bar.dart';
import 'package:promz/features/discover/viewmodels/discover_viewmodel.dart';
import 'package:promz/features/discover/views/prompt_list_view.dart';
import 'package:promz/features/home/<USER>/home_viewmodel.dart';
import 'package:promz/features/home/<USER>/widgets/bottom_nav_bar.dart';

class DiscoverView extends ConsumerWidget {
  const DiscoverView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final homeViewModel = ref.watch(homeViewModelProvider);

    return PopScope(
      canPop: false,
      // ignore: deprecated_member_use
      onPopInvoked: (_) {
        homeViewModel.navigateToPage(context, 0);
      },
      child: Scaffold(
        appBar: PromzAppBar(
          title: const Text(Strings.discoverPageTitle),
          showBackButton: true,
          onBackPressed: () => homeViewModel.navigateToPage(context, 0),
        ),
        body: const PlatformCategoryListView(),
        bottomNavigationBar: BottomNavBar(
          currentIndex: homeViewModel.currentNavIndex,
          onTap: (index) => homeViewModel.navigateToPage(context, index),
        ),
      ),
    );
  }
}

class PlatformCategoryListView extends ConsumerWidget {
  const PlatformCategoryListView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final viewModel = ref.watch(categoryListViewModelProvider.notifier);
    final state = ref.watch(categoryListViewModelProvider);

    // We'll now use all categories instead of just the current page
    final categories = state.categories;

    // Show loading indicator while categories are being loaded
    if (state.isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const PromzProgressIndicator(size: 16),
            const SizedBox(height: 16),
            Text(
              Strings.loadingCategories,
              style: theme.textTheme.titleMedium,
            ),
          ],
        ),
      );
    }

    // Show error state if loading failed
    if (state.hasError) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 48,
                color: theme.colorScheme.error,
              ),
              const SizedBox(height: 16),
              Text(
                Strings.failedToLoadCategories,
                style: theme.textTheme.titleMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              PromzButton(
                label: Strings.retryButtonLabel,
                onPressed: viewModel.loadCategories,
                icon: Icons.refresh,
              ),
            ],
          ),
        ),
      );
    }

    // Create category list with full scrolling support
    final categoryList = ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(), // Disable scrolling on inner list
      padding: EdgeInsets.zero,
      itemCount: categories.length,
      itemBuilder: (context, index) {
        final category = categories[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 2.0),
          child: PromzCard(
            icon: category.icon,
            title: category.title,
            description: category.subtitle ?? '',
            onTap: () => _navigateToCategory(context, category),
            customColor:
                state.isCategorySelected(index) ? theme.colorScheme.primaryContainer : null,
          ),
        );
      },
    );

    // Return simplified PromzPageView without actions
    return PromzPageView(
      title: Strings.categoryListTitle,
      content: categoryList,
      scrollable: true,
    );
  }

  // Navigate directly to the category's prompt list
  void _navigateToCategory(BuildContext context, Category category) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => PromptListView(
          categoryTitle: category.title,
        ),
      ),
    );
  }
}
