import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:promz/core/providers/service_providers.dart';
import 'package:promz/core/services/share_service.dart';
import 'package:promz/core/widgets/promz_app_bar.dart';
import 'package:promz/features/discover/viewmodels/prompt_list_viewmodel.dart';
import 'package:promz/features/home/<USER>/home_viewmodel.dart';
import 'package:promz/features/home/<USER>/widgets/bottom_nav_bar.dart';
import 'package:promz/features/shared/widgets/execution_results_manager.dart';
import 'package:promz_common/promz_common.dart';

final promptListViewModelProvider =
    FutureProvider.family<PromptListViewModel, String>((ref, categoryTitle) async {
  final viewModel = PromptListViewModel(categoryTitle: categoryTitle);
  await viewModel.loadPrompts();
  return viewModel;
});

class PromptListView extends ConsumerWidget {
  final String categoryTitle;

  const PromptListView({
    super.key,
    required this.categoryTitle,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final viewModelAsyncValue = ref.watch(promptListViewModelProvider(categoryTitle));

    return viewModelAsyncValue.when(
      data: (viewModel) => PlatformPromptListView(viewModel: viewModel),
      loading: () => const Scaffold(body: Center(child: CircularProgressIndicator())),
      error: (error, stackTrace) => Scaffold(body: Center(child: Text('Error: $error'))),
    );
  }
}

class PlatformPromptListView extends ConsumerWidget {
  final PromptListViewModel viewModel;

  const PlatformPromptListView({super.key, required this.viewModel});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final homeViewModel = ref.watch(homeViewModelProvider);

    // Use prompts from the current page
    final prompts = viewModel.promptsForCurrentPage;

    return Scaffold(
      appBar: PromzAppBar(
        title: Text(viewModel.categoryTitle),
        showBackButton: true,
        onBackPressed: () => Navigator.of(context).pop(),
      ),
      body: Stack(
        children: [
          buildExecutionResultsView(
            context,
            homeViewModel,
            defaultContent: PromzPageView(
              title: Strings.promptListTitle,
              content: _buildPromptList(context, prompts, theme, homeViewModel, ref),
              scrollable: true,
            ),
          ),
          if (homeViewModel.isExecutingLlm) _buildLoadingOverlay(context),
        ],
      ),
      bottomNavigationBar: BottomNavBar(
        currentIndex: homeViewModel.currentNavIndex,
        onTap: (index) => homeViewModel.navigateToPage(context, index),
      ),
    );
  }

  Widget _buildLoadingOverlay(BuildContext context) {
    return Container(
      color: Colors.black54,
      child: Center(
        child: Card(
          elevation: 8,
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CircularProgressIndicator(),
                const SizedBox(height: 16),
                Text(
                  Strings.executingPromptText,
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPromptList(
    BuildContext context,
    List<PromptModel> prompts,
    ThemeData theme,
    HomeViewModel homeViewModel,
    WidgetRef ref,
  ) {
    // Create empty state widget
    final emptyStateWidget = Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 48,
              color: theme.colorScheme.tertiary,
            ),
            const SizedBox(height: 16),
            Text(
              'No prompts found in this category.',
              style: theme.textTheme.titleMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );

    // Get the share service
    final shareService = ref.read(shareServiceProvider);

    // Watch the context service provider
    final clientContextAsync = ref.watch(clientContextServiceProvider);

    // Handle AsyncValue states
    return clientContextAsync.when(
      data: (service) {
        // Build the prompt list once service is available
        return PromptList.fromPromptModels(
          title: Strings.promptListTitle,
          prompts: prompts,
          isLoading: false,
          selectedIndex: viewModel.selectedPromptIndex,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          emptyStateWidget: emptyStateWidget,
          onPromptSelected: (displayItem) {
            homeViewModel.onPromptSelected(context, displayItem);
          },
          onPromptShare: (displayItem) {
            _sharePrompt(context, displayItem, shareService);
          },
          contextService: service, // Pass the resolved service instance
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (err, stack) => Center(
        child: Text('Error loading context: ${err.toString()}'),
      ),
    );
  }

  /// Share a prompt with other apps
  void _sharePrompt(BuildContext context, DisplayItem displayItem, ShareService shareService) {
    // Check if we have a valid prompt with ID
    if (displayItem.prompt == null || displayItem.prompt!.id.isEmpty) {
      // Fallback to regular sharing if no prompt ID is available
      final promptText = "${displayItem.displayText}\n\n${displayItem.subtitle ?? ''}";
      shareService.sharePrompt(promptText, title: displayItem.displayText);
      return;
    }

    // Create a formatted string with the prompt title and content
    final promptText = "${displayItem.displayText}\n\n${displayItem.subtitle ?? ''}";

    // Get the prompt ID
    final promptId = displayItem.prompt!.id;

    // Share the prompt with a shareable URL using the ShareService
    shareService.sharePromptWithUrl(promptId, promptText, title: displayItem.displayText);
  }
}
