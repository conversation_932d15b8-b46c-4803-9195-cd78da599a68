import 'package:flutter/material.dart';
import 'package:promz/database/database.dart';
import 'package:promz/database/db_utils.dart';
import 'package:promz_common/promz_common.dart';

class PromptListViewModel extends ChangeNotifier {
  final String categoryTitle;
  final List<PromptModel> _prompts = [];
  int? _selectedIndex;
  int _currentPage = 0;
  int _itemsPerPage = 5;

  PromptListViewModel({required this.categoryTitle});

  List<PromptModel> get promptsForCurrentPage {
    final startIndex = _currentPage * _itemsPerPage;
    final endIndex = startIndex + _itemsPerPage;
    if (startIndex >= _prompts.length) {
      return [];
    }
    return _prompts.sublist(startIndex, endIndex.clamp(0, _prompts.length));
  }

  int get currentPage => _currentPage;
  int get totalPages => (_prompts.length / _itemsPerPage).ceil();
  int get itemsPerPage => _itemsPerPage;
  bool get isContinueEnabled => _selectedIndex != null;
  int? get selectedPromptIndex => _selectedIndex;

  Future<void> loadPrompts() async {
    final database = await AppDatabase.getInstance();
    final category = await (database.select(database.categories)
          ..where((c) => c.title.equals(categoryTitle)))
        .getSingle();

    final dbPrompts = await (database.select(database.prompts)
          ..where((p) => p.categoryId.equals(category.id)))
        .get();

    _prompts.clear();
    _prompts.addAll(dbPrompts.map((dbPrompt) => DatabaseUtils.createPromptModelFromDb(dbPrompt)));
    notifyListeners();
  }

  void updateItemsPerPage(int count) {
    _itemsPerPage = count;
    notifyListeners();
  }

  void nextPage() {
    if (_currentPage < totalPages - 1) {
      _currentPage++;
      notifyListeners();
    }
  }

  void previousPage() {
    if (_currentPage > 0) {
      _currentPage--;
      notifyListeners();
    }
  }

  bool isPromptSelected(int index) {
    final globalIndex = _currentPage * _itemsPerPage + index;
    return _selectedIndex == globalIndex;
  }

  void togglePromptSelection(int index) {
    final globalIndex = _currentPage * _itemsPerPage + index;
    if (_selectedIndex == globalIndex) {
      _selectedIndex = null;
    } else {
      _selectedIndex = globalIndex;
    }
    notifyListeners();
  }
}
