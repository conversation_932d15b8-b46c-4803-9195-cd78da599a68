import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:promz/core/models/category.dart' as model;
import 'package:promz/database/database.dart';

class CategoryListState {
  final bool isLoading;
  final bool hasError;
  final List<model.Category> categories;
  final int currentPage;
  final int itemsPerPage;
  final Set<int> selectedCategoryIndices;

  CategoryListState({
    this.isLoading = false,
    this.hasError = false,
    this.categories = const [],
    this.currentPage = 0,
    this.itemsPerPage = 5,
    this.selectedCategoryIndices = const {},
  });

  CategoryListState copyWith({
    bool? isLoading,
    bool? hasError,
    List<model.Category>? categories,
    int? currentPage,
    int? itemsPerPage,
    Set<int>? selectedCategoryIndices,
  }) {
    return CategoryListState(
      isLoading: isLoading ?? this.isLoading,
      hasError: hasError ?? this.hasError,
      categories: categories ?? this.categories,
      currentPage: currentPage ?? this.currentPage,
      itemsPerPage: itemsPerPage ?? this.itemsPerPage,
      selectedCategoryIndices: selectedCategoryIndices ?? this.selectedCategoryIndices,
    );
  }

  List<model.Category> get categoriesForCurrentPage {
    final startIndex = currentPage * itemsPerPage;
    final endIndex = startIndex + itemsPerPage;
    if (startIndex >= categories.length) return [];
    return categories.sublist(startIndex, endIndex.clamp(0, categories.length));
  }

  int get totalPages => (categories.length / itemsPerPage).ceil();

  bool get isContinueEnabled => selectedCategoryIndices.isNotEmpty;

  bool isCategorySelected(int index) => selectedCategoryIndices.contains(index);
}

class DiscoverViewModel extends StateNotifier<CategoryListState> {
  DiscoverViewModel() : super(CategoryListState());

  Future<void> loadCategories() async {
    state = state.copyWith(isLoading: true, hasError: false);
    try {
      final database = await AppDatabase.getInstance();
      final categories = await database.select(database.categories).get();
      state = state.copyWith(
        categories: categories
            .map((dbCat) => model.Category(
                title: dbCat.title,
                subtitle: dbCat.subtitle ?? '',
                icon: Icons.folder // Default icon
                ))
            .toList(),
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        hasError: true,
      );
    }
  }

  void updateItemsPerPage(int itemsPerPage) {
    state = state.copyWith(itemsPerPage: itemsPerPage);
  }

  void toggleCategorySelection(int index) {
    final newSelectedIndices = Set<int>.from(state.selectedCategoryIndices);
    if (newSelectedIndices.contains(index)) {
      newSelectedIndices.remove(index);
    } else {
      newSelectedIndices.clear(); // Single selection mode
      newSelectedIndices.add(index);
    }
    state = state.copyWith(selectedCategoryIndices: newSelectedIndices);
  }

  void nextPage() {
    if (state.currentPage < state.totalPages - 1) {
      state = state.copyWith(currentPage: state.currentPage + 1);
    }
  }

  void previousPage() {
    if (state.currentPage > 0) {
      state = state.copyWith(currentPage: state.currentPage - 1);
    }
  }
}

final categoryListViewModelProvider =
    StateNotifierProvider<DiscoverViewModel, CategoryListState>((ref) {
  final viewModel = DiscoverViewModel();
  viewModel.loadCategories();
  return viewModel;
});
