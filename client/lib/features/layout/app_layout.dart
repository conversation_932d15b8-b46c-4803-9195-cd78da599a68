import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:promz/features/home/<USER>/home_viewmodel.dart';
import 'package:promz/features/home/<USER>/home_page.dart';

/// The main layout for the application
/// Handles bottom navigation and main screen container
class AppLayout extends ConsumerStatefulWidget {
  /// Creates an instance of AppLayout
  const AppLayout({super.key});

  @override
  ConsumerState<AppLayout> createState() => _AppLayoutState();
}

class _AppLayoutState extends ConsumerState<AppLayout> {
  @override
  Widget build(BuildContext context) {
    // Watch the current page index from the home view model
    final currentIndex = ref.watch(homeViewModelProvider.select((value) => value.currentNavIndex));

    // Select the appropriate page based on the current index
    Widget currentPage = const HomePage();

    switch (currentIndex) {
      case 0:
        currentPage = const HomePage();
        break;
      // Add cases for other navigation items as they are implemented
      default:
        currentPage = const HomePage();
    }

    return Scaffold(
      body: currentPage,
      // Bottom navigation is handled by each page individually to allow for page-specific customizations
    );
  }
}
