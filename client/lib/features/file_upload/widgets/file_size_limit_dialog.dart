import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:promz/core/models/license_model.dart';
import 'package:promz/core/providers/license_provider.dart';
import 'package:promz/core/services/file/file_upload_service.dart';
import 'package:promz/features/navigation/routes.dart';
import 'package:promz_common/promz_common.dart';

/// Dialog that shows when a file exceeds the allowed size for the user's license tier
class FileSizeLimitDialog extends ConsumerWidget {
  final FileSizeLimitExceededException error;

  const FileSizeLimitDialog({
    Key? key,
    required this.error,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    // Access the license data using Riverpod
    final licenseState = ref.watch(userLicenseProvider);
    final license = licenseState.valueOrNull;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Header with icon
            Center(
              child: Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: colorScheme.errorContainer,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.file_copy_rounded,
                  size: 40,
                  color: colorScheme.error,
                ),
              ),
            ),
            const SizedBox(height: 24),

            // Title
            Text(
              'File Size Limit Exceeded',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),

            // Description
            Text(
              error.message,
              style: theme.textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),

            // File size details
            _buildInfoSection(
              context,
              'Your File',
              '${error.fileSizeMB} MB',
              Icons.description,
              colorScheme.error,
            ),
            _buildInfoSection(
              context,
              '${error.currentTier} Tier Limit',
              '${error.maxSizeMB} MB',
              Icons.info_outline,
              colorScheme.primary,
            ),
            const SizedBox(height: 24),

            // Only show upgrade button if there's a next tier that would support the file size
            if (error.nextTier != null) ...[
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: colorScheme.primary,
                  foregroundColor: colorScheme.onPrimary,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                onPressed: () => _handleUpgrade(context, ref, license),
                child: Text(
                  'Upgrade to ${error.nextTier}',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              const SizedBox(height: 12),
            ],

            // Close button
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoSection(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color iconColor,
  ) {
    final theme = Theme.of(context);
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(icon, color: iconColor),
          const SizedBox(width: 16),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                value,
                style: theme.textTheme.bodyLarge,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _handleUpgrade(BuildContext context, WidgetRef ref, UserLicense? license) async {
    try {
      // Close the dialog first
      Navigator.of(context).pop();

      // Log the user's interest in upgrading
      appLog.info('User initiated upgrade flow from file size limit dialog',
          name: 'FileSizeLimitDialog');

      // Navigate to the appropriate screen based on license state
      if (license == null || license.licenseType == LicenseType.none) {
        // User has no license - navigate to profile setup first
        if (context.mounted) {
          Navigator.of(context).pushNamed(AppRoutes.userProfileSetup);
        }
      } else if (license.licenseType == LicenseType.free) {
        // User has free license - navigate to upgrade screen
        _navigateToUpgradeScreen(context);
      } else if (license.licenseType == LicenseType.trial && _isTrialExpiring(license)) {
        // Trial about to expire - navigate to upgrade screen
        _navigateToUpgradeScreen(context);
      } else {
        // User already has Pro or valid trial
        _showAlreadySubscribedMessage(context, license);
      }
    } catch (e) {
      appLog.error('Failed to start upgrade process', name: 'FileSizeLimitDialog', error: e);

      // Show a simple error message
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Unable to load subscription options. Please try again later.'),
          ),
        );
      }
    }
  }

  // Check if trial is expiring soon (within 3 days)
  bool _isTrialExpiring(UserLicense license) {
    if (license.expiresAt == null) return false;
    final daysRemaining = license.expiresAt!.difference(DateTime.now()).inDays;
    return daysRemaining <= 3;
  }

  // Navigate to upgrade screen
  void _navigateToUpgradeScreen(BuildContext context) {
    if (!context.mounted) return;
    Navigator.of(context).pushNamed(AppRoutes.subscriptionUpgrade);
  }

  // Show message when user already has subscription
  void _showAlreadySubscribedMessage(BuildContext context, UserLicense license) {
    if (!context.mounted) return;

    String message;
    if (license.licenseType == LicenseType.pro) {
      message =
          'You already have a Pro subscription but are still trying to upload a file that exceeds your plan\'s limits. Please try with a smaller file.';
    } else {
      // Must be an active trial
      message =
          'You have an active trial but the file exceeds your plan\'s limits. Please try with a smaller file or upgrade to a higher tier.';
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }
}
