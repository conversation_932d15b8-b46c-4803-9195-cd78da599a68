import 'package:flutter/material.dart';
import 'package:promz/core/services/file/file_upload_service.dart';
import 'package:promz/features/file_upload/widgets/file_size_limit_dialog.dart';
import 'package:promz_common/promz_common.dart';

/// Helper class to handle file size limit exceptions and showing the appropriate dialog
class FileSizeLimitHandler {
  static const _logName = 'FileSizeLimitHandler';

  /// Show the file size limit dialog for the given exception
  ///
  /// Returns a Future that completes when the dialog is dismissed
  static Future<void> showFileSizeLimitDialog(
    BuildContext context,
    FileSizeLimitExceededException exception,
  ) async {
    appLog.info(
        'Showing file size limit dialog for ${exception.fileSizeMB} MB file '
        '(limit: ${exception.maxSizeMB} MB for ${exception.currentTier} tier)',
        name: _logName);

    if (!context.mounted) {
      appLog.warning('Context is not mounted, cannot show dialog', name: _logName);
      return;
    }

    return showDialog<void>(
      context: context,
      barrierDismissible: false, // User must tap a button to close dialog
      builder: (BuildContext context) {
        return FileSizeLimitDialog(error: exception);
      },
    );
  }

  /// Handle a file upload operation with proper error handling for file size limits
  ///
  /// This function will automatically show the FileSizeLimitDialog if a FileSizeLimitExceededException
  /// is thrown during the upload operation.
  static Future<T?> handleFileUpload<T>(
    BuildContext context,
    Future<T> Function() uploadOperation,
  ) async {
    try {
      return await uploadOperation();
    } on FileSizeLimitExceededException catch (e) {
      await showFileSizeLimitDialog(context, e);
      return null;
    } catch (e, stack) {
      appLog.error('Error during file upload', name: _logName, error: e, stackTrace: stack);

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Upload error: ${e.toString()}')),
        );
      }
      return null;
    }
  }
}
