import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:promz/core/providers/service_providers.dart';
import 'package:promz/core/services/client_context_service.dart';
import 'package:promz/core/utils/safe_notifier.dart';
import 'package:promz/database/database.dart';
import 'package:promz/database/db_utils.dart';
import 'package:promz_common/promz_common.dart';

class SourceInputViewModel extends ChangeNotifier with SafeNotifier {
  static const _logName = 'SourceInputViewModel';

  // Data sources
  List<PromptModel> _prompts = [];
  List<Category> _categories = [];
  List<Sp500Ticker> _stockTickers = [];
  Set<String> _uniqueWords = {};
  final Map<String, List<String>> _promptKeywords = {};
  final Map<String, List<String>> _categoryKeywords = {};

  // Suggestions and state
  List<DisplayItem> _suggestions = [];
  String? _errorMessage;
  bool _isLoading = false;
  String _currentQuery = '';

  // Services
  final Ref _ref;
  late final AsyncValue<ClientContextService> _clientContextService;

  // Configuration
  final int _maxEntitySuggestions = 3;

  // Getters
  List<PromptModel> get prompts => _prompts;
  List<DisplayItem> get suggestions => _suggestions;
  String? get errorMessage => _errorMessage;
  bool get isLoading => _isLoading;
  bool get mounted => isMounted; // Use SafeNotifier's isMounted property instead

  SourceInputViewModel(Ref ref) : _ref = ref {
    // Watch the provider to get the AsyncValue and trigger rebuilds on change
    _clientContextService = _ref.watch(clientContextServiceProvider);
    loadData();
  }

  Future<void> loadData() async {
    if (!isMounted) return;

    _isLoading = true;
    _errorMessage = null; // Reset error message at the start
    safeNotify();

    try {
      final database = await AppDatabase.getInstance();

      // Load prompts first
      try {
        _prompts = (await database.getAllPrompts())
            .map((data) => DatabaseUtils.createPromptModelFromDb(data))
            .toList();
        appLog.debug('Loaded ${_prompts.length} prompts', name: _logName);
      } catch (e, stack) {
        appLog.error('Error loading prompts', name: _logName, error: e, stackTrace: stack);
        // Don't set error message yet, try to continue with other data
        _prompts = [];
      }

      if (!isMounted) return;

      // Load categories
      try {
        _categories = await database.select(database.categories).get();
        appLog.debug('Loaded ${_categories.length} categories', name: _logName);
      } catch (e, stack) {
        appLog.error('Error loading categories', name: _logName, error: e, stackTrace: stack);
        _categories = [];
      }

      if (!isMounted) return;

      // Load stock tickers
      try {
        _stockTickers = await database.getAllSP500Tickers();
        appLog.debug('Loaded ${_stockTickers.length} stock tickers', name: _logName);
      } catch (e, stack) {
        appLog.error('Error loading stock tickers', name: _logName, error: e, stackTrace: stack);
        _stockTickers = [];
      }

      if (!isMounted) return;

      // Only extract keywords and build word list if we have some data
      if (_prompts.isNotEmpty || _categories.isNotEmpty) {
        _extractKeywords();
        _buildUniqueWordsList();
      }

      if (!isMounted) return;

      // Initialize entity service dictionaries
      if (_clientContextService.hasValue) {
        await _clientContextService.value!.entityDetection.loadEntityDictionaries();
        if (!isMounted) return;
      } else {
        appLog.warning('Cannot load entity dictionaries: ClientContextService not ready.',
            name: _logName);
      }
    } catch (e, stackTrace) {
      appLog.error('Error loading data', name: _logName, error: e, stackTrace: stackTrace);
      _errorMessage = 'Failed to load prompts.';
    } finally {
      if (isMounted) {
        _isLoading = false;
        safeNotify();
      }
    }
  }

  void _extractKeywords() {
    // Extract keywords from prompts
    for (final prompt in _prompts) {
      try {
        _promptKeywords[prompt.id] = List<String>.from(prompt.keywords);
      } catch (e) {
        appLog.error('Error parsing prompt keywords for ${prompt.id}', name: _logName, error: e);
        _promptKeywords[prompt.id] = [];
      }
    }

    // Extract keywords from categories
    for (final category in _categories) {
      try {
        final List<dynamic> keywords = json.decode(category.keywords);
        _categoryKeywords[category.id] = List<String>.from(keywords);
      } catch (e) {
        appLog.error('Error parsing category keywords for ${category.id}',
            name: _logName, error: e);
        _categoryKeywords[category.id] = [];
      }
    }
  }

  void _buildUniqueWordsList() {
    _uniqueWords = {};
    final Set<String> keywordWords = {}; // Track words that are specifically keywords

    // Add words from categories and their keywords
    for (final category in _categories) {
      // Add words from title and subtitle
      _addWordsFromText(category.title);
      if (category.subtitle != null && category.subtitle!.isNotEmpty) {
        _addWordsFromText(category.subtitle!);
      }

      // Add category keywords - mark them as keyword words
      final keywords = _categoryKeywords[category.id] ?? [];
      for (final keyword in keywords) {
        _uniqueWords.add(keyword.toLowerCase());
        keywordWords.add(keyword.toLowerCase());
      }
    }

    // Add words from prompt titles and their keywords
    for (final prompt in _prompts) {
      // Add words from title
      _addWordsFromText(prompt.title);

      // Add prompt keywords - mark them as keyword words
      final keywords = _promptKeywords[prompt.id] ?? [];
      for (final keyword in keywords) {
        _uniqueWords.add(keyword.toLowerCase());
        keywordWords.add(keyword.toLowerCase());
      }
    }

    // Now store the keywordWords set for later use
    _keywordWords = keywordWords;

    appLog.debug(
        'Built unique words list with ${_uniqueWords.length} words, ${_keywordWords.length} are keywords, ${_stockTickers.length} stock tickers',
        name: _logName);
  }

  // Add this field to store the set of keywords
  Set<String> _keywordWords = {};

  void _addWordsFromText(String text) {
    final words = text.toLowerCase().split(RegExp(r'[^\w]+')); // Split on non-word characters
    for (final word in words) {
      if (word.length > 2) {
        // Skip very short words
        _uniqueWords.add(word);
      }
    }
  }

  /// Find the current word at cursor position
  String _getCurrentWord(String text, int cursorPosition) {
    if (text.isEmpty || cursorPosition < 0 || cursorPosition > text.length) {
      return '';
    }

    // Find the start of the word (search backwards for a space)
    var startPos = cursorPosition;
    while (startPos > 0 && text[startPos - 1] != ' ') {
      startPos--;
    }

    // Find the end of the word (search forwards for a space)
    var endPos = cursorPosition;
    while (endPos < text.length && text[endPos] != ' ') {
      endPos++;
    }

    return text.substring(startPos, endPos).toLowerCase();
  }

  /// Get autocomplete suggestions based on current query
  Future<void> getAutoCompleteSuggestions(WidgetRef ref, String query,
      [int? cursorPosition]) async {
    if (!isMounted) return;

    _currentQuery = query.toLowerCase();

    // Clear suggestions if query is empty
    if (_currentQuery.isEmpty) {
      _suggestions = [];
      safeNotify();
      return;
    }

    // If we have an error and no data, try to reload
    if (_errorMessage != null &&
        (_prompts.isEmpty && _categories.isEmpty && _stockTickers.isEmpty)) {
      await loadData();
      if (!isMounted) return;
      if (_errorMessage != null) {
        _suggestions = [];
        safeNotify();
        return;
      }
    }

    // Get the current word if cursor position is provided
    final currentWord =
        cursorPosition != null ? _getCurrentWord(_currentQuery, cursorPosition) : '';
    final bool hasCurrentWord = currentWord.isNotEmpty;

    // Start building suggestions
    final currentWordMatches = <DisplayItem>[];
    final fullQueryMatches = <DisplayItem>[];

    // 1. Add word suggestions from our unique words set (for auto-completion)
    if (hasCurrentWord) {
      for (final word in _uniqueWords) {
        if (word.startsWith(currentWord)) {
          final isKeyword = _keywordWords.contains(word);
          currentWordMatches.add(DisplayItem(
            text: word,
            displayText: word,
            type: isKeyword ? DisplayItemType.keywordWord : DisplayItemType.promptWord,
          ));
        }
      }
    }

    // 2. Add exact matches for prompt titles
    for (final prompt in _prompts) {
      final title = prompt.title.toLowerCase();

      // Transform the title for display by replacing templates with human-readable text
      final displayTitle = _formatPromptTitleForDisplay(prompt.title);

      if (title.contains(_currentQuery)) {
        fullQueryMatches.add(DisplayItem(
          text: prompt.title,
          displayText: displayTitle, // Use formatted display title
          type: DisplayItemType.promptTitle,
          prompt: prompt,
        ));
      }
    }

    // 3. Add detected entities instead of direct stock/company matches
    try {
      // Use either the current word or full query depending on context
      final textToProcess = hasCurrentWord ? currentWord : _currentQuery;

      // Only process if we have text and if ClientContextService is available
      if (textToProcess.isNotEmpty) {
        // Get all possible entities
        List<Entity> allEntities = [];

        // Use hasValue to safely check for the service
        if (_clientContextService.hasValue && _clientContextService.value != null) {
          // Now safe to use value and call entityDetection
          final service = _clientContextService.value!;
          allEntities = service.entityDetection.getAllEntities();
        } else {
          if (_clientContextService.isLoading) {
            appLog.debug('Waiting for ClientContextService to become available', name: _logName);
          } else if (_clientContextService.hasError) {
            appLog.error('ClientContextService has error',
                name: _logName, error: _clientContextService.error);
          } else {
            appLog.warning('ClientContextService is not available', name: _logName);
          }
        }

        // Filter entities based on the current word
        final entitySuggestions = allEntities
            .where((entity) =>
                entity.text.toLowerCase().startsWith(textToProcess.toLowerCase()) &&
                entity.confidence >= 0.6) // Apply confidence threshold
            .take(_maxEntitySuggestions)
            .toList();

        // Update ClientContextService with detected entities
        if (_clientContextService.hasValue && _clientContextService.value != null) {
          final service = _clientContextService.value!;
          service.updateEntities(entitySuggestions);
          appLog.debug('Updated client context with ${entitySuggestions.length} entity suggestions',
              name: _logName);
        } else if (_clientContextService.hasError) {
          appLog.error('Could not update entities, ClientContextService has error',
              name: _logName, error: _clientContextService.error);
        } else {
          appLog.warning('Could not update entities: ClientContextService not ready.',
              name: _logName);
        }

        final displayItems = entitySuggestions
            .map((entity) => EntityUtils.createDisplayItemFromEntity(entity))
            .toList();

        // Add to the appropriate list
        if (hasCurrentWord) {
          currentWordMatches.addAll(displayItems);
        } else {
          fullQueryMatches.addAll(displayItems);
        }

        appLog.debug(
          'Detected ${entitySuggestions.length} entities in "$textToProcess"',
          name: _logName,
        );
      }
    } catch (e, stack) {
      appLog.error('Error detecting entities', name: _logName, error: e, stackTrace: stack);
    }

    // 4. Add category words
    for (final category in _categories) {
      if (category.title.toLowerCase().contains(_currentQuery)) {
        fullQueryMatches.add(DisplayItem(
          text: category.title,
          displayText: category.title,
          type: DisplayItemType.categoryWord,
        ));
      }
    }

    // 5. If we have fewer than 2 direct matches, use semantic matching
    if (fullQueryMatches.where((s) => s.type == DisplayItemType.promptTitle).length < 2) {
      try {
        List<DisplayItem> semanticSuggestions = [];

        // Check if service is available
        if (_clientContextService.hasValue && _clientContextService.value != null) {
          // Check if promptSuggestion is available
          final keywords = _currentQuery.split(RegExp(r'\s+'));
          // Await the async call
          semanticSuggestions =
              await _clientContextService.value!.promptSuggestion.getPromptSuggestions(keywords);
        } else {
          if (_clientContextService.isLoading) {
            appLog.debug('Waiting for ClientContextService to load', name: _logName);
          } else {
            appLog.warning('Could not get semantic suggestions: ClientContextService not ready.',
                name: _logName);
          }
        }

        // Add semantic matches that aren't already in our suggestions
        for (final suggestion in semanticSuggestions) {
          if (!fullQueryMatches.any((s) => s.text == suggestion.text)) {
            fullQueryMatches.add(suggestion);
          }
        }
      } catch (e, stack) {
        appLog.error('Error getting semantic suggestions',
            name: _logName, error: e, stackTrace: stack);
      }
    }

    // Sort suggestions within their groups
    _sortSuggestions(currentWordMatches);
    _sortSuggestions(fullQueryMatches);

    // Combine current word matches and full query matches
    final allSuggestions = [...currentWordMatches, ...fullQueryMatches];

    // Limit total suggestions
    _suggestions = allSuggestions.take(15).toList();
    safeNotify();
  }

  void _sortSuggestions(List<DisplayItem> suggestions) {
    suggestions.sort((a, b) {
      // First by type
      if (a.type != b.type) {
        return a.type.index - b.type.index;
      }

      // Then by relevance score for keyword matches and entities
      if (a.type == DisplayItemType.keywordMatch || a.type == DisplayItemType.entity) {
        return (b.relevanceScore ?? 0).compareTo(a.relevanceScore ?? 0);
      }

      // Otherwise alphabetically
      return a.text.compareTo(b.text);
    });
  }

  /// Format a prompt title by replacing template variables with human-readable text
  String _formatPromptTitleForDisplay(String title) {
    // Regular expressions for template detection
    final fullTemplateRegex = RegExp(r'\{\{([A-Za-z_]+):([^}]+)\}\}');
    final shortTemplateRegex = RegExp(r'\{\{([A-Za-z0-9_]+)\}\}');
    String result = title;

    // Replace full templates {{TYPE:NAME}}
    result = result.replaceAllMapped(fullTemplateRegex, (match) {
      if (match.groupCount >= 2) {
        final typeStr = match.group(1)?.toUpperCase();
        final name = match.group(2);

        if (typeStr != null && name != null) {
          try {
            // Try to map string to EntityType
            EntityType.values.firstWhere((t) => t.name.toUpperCase() == typeStr);

            // Get display text from variableManager
            final displayText = EntityUtils.getDisplayName(variableName: name);
            return displayText;
          } catch (e) {
            appLog.warning('Unknown entity type in template: $typeStr', name: _logName, error: e);
          }
        }
      }
      return match.group(0) ?? '';
    });

    // Replace short templates {{NAME}}
    result = result.replaceAllMapped(shortTemplateRegex, (match) {
      if (match.groupCount >= 1) {
        final name = match.group(1);

        if (name != null) {
          // Get display text from variableManager
          final displayText = EntityUtils.getDisplayName(variableName: name);
          return displayText;
        }
      }
      return match.group(0) ?? '';
    });

    return result;
  }

  /// Clears all current suggestions
  void clearSuggestions() {
    if (!isMounted) return;

    _suggestions = [];
    safeNotify();
    appLog.debug('Cleared all suggestions', name: _logName);
  }

  /// Handle a suggestion selection and update text with transformed values
  /// Returns the transformed text that should be displayed in the input field
  String handleSuggestionSelection(DisplayItem suggestion, String currentText) {
    appLog.debug('Handling suggestion selection: "${suggestion.text}" (type: ${suggestion.type})',
        name: _logName);

    // Clear suggestions immediately
    clearSuggestions();

    // For entity-based suggestions, set the variable values
    if (suggestion.type == DisplayItemType.entity) {
      final entity = suggestion.entity!;

      // Ensure service is ready before setting variables
      if (_clientContextService.hasValue) {
        final service = _clientContextService.value!;
        final canonicalName = EntityUtils.getCanonicalName(entity.canonicalText);
        service.setVariableValue(canonicalName, entity.text);

        // Handle finance entities specially
        if (entity.type == EntityType.finance) {
          if (entity.stockSymbol != null) {
            service.setVariableValue('FINANCE:TICKER', entity.stockSymbol!);
          }

          if (entity.companyName != null) {
            service.setVariableValue('FINANCE:COMPANY_NAME', entity.companyName!);
          }
        }
      } else {
        appLog.warning('Cannot set entity variable: ClientContextService not ready.',
            name: _logName);
      }
    }

    // Get all variable values
    Map<String, String> allValues = {};
    if (_clientContextService.hasValue) {
      allValues = _clientContextService.value!.getAllVariableValues();
    } else {
      appLog.warning('Cannot get all variables: ClientContextService not ready.', name: _logName);
    }

    // Transform the text with all available values
    String transformedText = suggestion.text; // Default to original text
    if (_clientContextService.hasValue) {
      // Pass the resolved map
      transformedText =
          _clientContextService.value!.transformTitleWithValues(suggestion.text, allValues);
    } else {
      appLog.warning('Cannot transform title: ClientContextService not ready.', name: _logName);
    }

    appLog.debug('Text transformed: "${suggestion.text}" -> "$transformedText"', name: _logName);

    // Return the resolved string
    return transformedText;
  }
}
