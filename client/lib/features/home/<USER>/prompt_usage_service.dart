import 'dart:convert';
import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:http/http.dart' as http;
import 'package:promz_common/config/api_config.dart';
import 'package:promz/core/services/api/api_client.dart';
import 'package:promz/database/database.dart';
import 'package:promz/features/home/<USER>/prompt_usage_event.dart';
import 'package:promz/features/home/<USER>/local_prompt_usage_repository.dart';
import 'package:promz_common/promz_common.dart';

/// Defines the interface for the prompt usage service.
/// This service is responsible for tracking and syncing prompt usage data
/// with the server, as well as fetching popular prompts.
abstract class PromptUsage {
  /// Records a usage event (selected or executed) for a prompt
  Future<void> recordUsage(String promptId, PromptUsageEventType eventType);

  /// Syncs local usage data with the server and clears local data on success
  Future<void> syncUsageData();

  /// Fetches popular prompts from the server and updates local storage
  Future<void> fetchPopularPrompts([bool forceRefresh = false]);

  /// Checks if enough time has passed to sync data with the server
  bool shouldSyncData();
}

/// Implementation of the PromptUsageService interface
class PromptUsageService implements PromptUsage {
  static const _logName = 'PromptUsageService';

  /// Repository for local prompt usage data
  final LocalPromptUsageRepository _repository;

  /// Database instance for direct access to PopularPrompts table
  final AppDatabase _database;

  /// Time when data was last synced with the server (nullable)
  DateTime? _lastSyncTime;

  /// Time when popular prompts were last fetched (nullable)
  DateTime? _lastPopularPromptsFetchTime;

  /// HTTP client for API requests
  final http.Client _httpClient;

  /// API client for WebSocket communication
  final ApiClient _apiClient;

  /// Subscription to popular prompts updates
  StreamSubscription<Map<String, dynamic>>? _popularPromptsSubscription;

  /// Creates a new instance of the DefaultPromptUsageService.
  PromptUsageService({
    required LocalPromptUsageRepository repository,
    required AppDatabase database,
    http.Client? httpClient,
    required ApiClient apiClient,
    Ref? ref,
  })  : _repository = repository,
        _database = database,
        _httpClient = httpClient ?? http.Client(),
        _apiClient = apiClient {
    // Initialize WebSocket subscription for popular prompts
    _subscribeToPopularPrompts();
  }

  /// Dispose resources
  void dispose() {
    // Cancel WebSocket subscription
    _popularPromptsSubscription?.cancel();
  }

  /// The time interval before syncing usage data (24 hours)
  static const Duration syncInterval = Duration(hours: 24);

  @override
  Future<void> recordUsage(String promptId, PromptUsageEventType eventType) async {
    try {
      appLog.debug('Recording prompt usage: $promptId, ${eventType.name}', name: _logName);

      // Get country code from the device
      final countryCode = _database.getDeviceCountryCode();

      // Create usage event
      final event = PromptUsageEvent(
        promptId: promptId,
        eventType: eventType,
        usedAt: DateTime.now(),
        countryCode: countryCode.isNotEmpty ? countryCode : null,
      );

      // Save to local storage
      await _repository.insertPromptUsageEvent(event);
      appLog.debug('Recorded prompt usage event: $event', name: _logName);

      // Check if we should sync data
      if (shouldSyncData()) {
        appLog.debug('Time to sync data with server', name: _logName);
        await syncUsageData();
      }
    } catch (e, stackTrace) {
      appLog.error('Error recording prompt usage',
          name: _logName, error: e, stackTrace: stackTrace);
    }
  }

  @override
  Future<void> syncUsageData() async {
    try {
      appLog.debug('Syncing usage data with server', name: _logName);

      // Get all local usage events
      final events = await _repository.getAllPromptUsageEvents();

      // If no events, nothing to sync
      if (events.isEmpty) {
        appLog.debug('No usage events to sync', name: _logName);
        _lastSyncTime = DateTime.now();
        return;
      }

      // Convert database model to domain model
      final usageEvents = events
          .map((event) => PromptUsageEvent(
                id: event.id,
                promptId: event.promptId,
                eventType: PromptUsageEventType.values.firstWhere(
                  (e) => e.name == event.eventType,
                  orElse: () => PromptUsageEventType.selected,
                ),
                usedAt: DateTime.fromMillisecondsSinceEpoch(event.usedAt),
                countryCode: event.countryCode,
              ))
          .toList();

      // Prepare payload - direct array of events as expected by the server
      final payload = usageEvents
          .map((e) => {
                'prompt_id': e.promptId,
                'event_type': e.eventType.name,
                'used_at': e.usedAt.millisecondsSinceEpoch,
                'country_code': e.countryCode,
              })
          .toList();

      // Get API key from API client
      final apiKey = await _apiClient.getApiKey();
      if (apiKey == null || apiKey.isEmpty) {
        appLog.warning('No API key available for syncing usage data', name: _logName);
        return;
      }

      // Send to server
      final response = await _httpClient.post(
        Uri.parse(ApiConfig.promptUsageEndpoint),
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': apiKey,
        },
        body: jsonEncode(payload),
      );

      // Handle response
      if (response.statusCode >= 200 && response.statusCode < 300) {
        appLog.debug('Successfully synced ${usageEvents.length} usage events', name: _logName);

        // Clear local data on success
        await _repository.clearAllPromptUsageEvents();

        // Update last sync time
        _lastSyncTime = DateTime.now();
      } else {
        // Try again once more immediately
        appLog.warning('Failed to sync usage data (${response.statusCode}), trying again...',
            name: _logName);

        // Small delay before retry
        await Future.delayed(const Duration(seconds: 1));

        final retryResponse = await _httpClient.post(
          Uri.parse(ApiConfig.promptUsageEndpoint),
          headers: {
            'Content-Type': 'application/json',
            'X-API-Key': apiKey,
          },
          body: jsonEncode(payload),
        );

        if (retryResponse.statusCode >= 200 && retryResponse.statusCode < 300) {
          appLog.debug('Successfully synced ${usageEvents.length} usage events on retry',
              name: _logName);

          // Clear local data on success
          await _repository.clearAllPromptUsageEvents();

          // Update last sync time
          _lastSyncTime = DateTime.now();
        } else {
          appLog.error('Failed to sync usage data after retry: ${retryResponse.statusCode}',
              name: _logName);
          // Keep data for next sync attempt
        }
      }
    } catch (e, stackTrace) {
      appLog.error('Error syncing usage data with server',
          name: _logName, error: e, stackTrace: stackTrace);
    }
  }

  @override
  Future<void> fetchPopularPrompts([bool forceRefresh = false]) async {
    try {
      // Skip if we recently fetched and not forcing refresh
      if (!forceRefresh && _lastPopularPromptsFetchTime != null && !shouldRefreshPopularPrompts()) {
        appLog.debug('Skipping popular prompts fetch, not needed at this time', name: _logName);
        return;
      }

      // Request popular prompts via WebSocket
      final success = await _apiClient.sendWebSocketRequest('popular_prompts', {
        'limit': 10,
        'days': 30,
      });

      if (success) {
        // Update fetch time
        _lastPopularPromptsFetchTime = DateTime.now();
        appLog.debug('Popular prompts request sent via WebSocket', name: _logName);
      } else {
        appLog.warning('Failed to send popular prompts request via WebSocket', name: _logName);
      }
    } catch (e, stackTrace) {
      appLog.error('Error fetching popular prompts',
          name: _logName, error: e, stackTrace: stackTrace);
    }
  }

  /// Process and store popular prompts data
  Future<void> _processPopularPrompts(List<dynamic> popularPrompts) async {
    if (popularPrompts.isEmpty) return;

    // Make sure each item is a Map<String, dynamic>
    final validPrompts = <Map<String, dynamic>>[];
    for (final item in popularPrompts) {
      if (item is Map<String, dynamic>) {
        validPrompts.add(item);
      } else {
        appLog.warning('Skipping invalid prompt item: $item', name: _logName);
      }
    }

    // Store the complete prompts in the database
    if (validPrompts.isNotEmpty) {
      await _database.storePopularPrompts(validPrompts);
      appLog.debug('Stored ${validPrompts.length} popular prompts with data', name: _logName);
    } else {
      appLog.warning('No valid prompts to store', name: _logName);
    }
  }

  @override
  bool shouldSyncData() {
    // If never synced before, or it's been more than syncInterval since last sync
    if (_lastSyncTime == null) {
      return true;
    }

    final now = DateTime.now();
    final diff = now.difference(_lastSyncTime!);
    return diff >= syncInterval;
  }

  /// Checks if we should refresh popular prompts from the server
  bool shouldRefreshPopularPrompts() {
    // Always refresh if we've never fetched before
    if (_lastPopularPromptsFetchTime == null) {
      return true;
    }

    // Check time since last fetch
    final now = DateTime.now();
    final diff = now.difference(_lastPopularPromptsFetchTime!);

    // Use a shorter interval (4 hours) for popular prompts refresh
    const refreshInterval = Duration(hours: 4);
    return diff >= refreshInterval;
  }

  /// Subscribe to popular prompts updates via ApiClient's WebSocket
  Future<void> _subscribeToPopularPrompts() async {
    try {
      // Cancel any existing subscription
      _popularPromptsSubscription?.cancel();

      // First ensure WebSocket is initialized
      final initialized = await _apiClient.initializeWebSocket();
      if (!initialized) {
        appLog.warning(
            'Could not initialize WebSocket, popular prompts updates will not be available',
            name: _logName);
        return;
      }

      // Now subscribe to popular prompts topic
      _popularPromptsSubscription =
          _apiClient.subscribeToTopic('popular_prompts').listen(_handlePopularPromptsUpdate);

      appLog.debug('Subscribed to popular prompts via ApiClient', name: _logName);
    } catch (e, stackTrace) {
      appLog.error('Error subscribing to popular prompts',
          name: _logName, error: e, stackTrace: stackTrace);
    }
  }

  /// Handle popular prompts update from WebSocket
  Future<void> _handlePopularPromptsUpdate(Map<String, dynamic> data) async {
    try {
      appLog.debug('Received popular prompts update via WebSocket: ${data.keys}', name: _logName);

      // Check for different possible data structures
      List<dynamic>? popularPrompts;

      // Case 1: Data contains 'popular_prompts' key directly
      if (data.containsKey('popular_prompts')) {
        popularPrompts = data['popular_prompts'] as List<dynamic>?;
      }
      // Case 2: Data contains 'payload' key with popular prompts inside
      else if (data.containsKey('payload')) {
        final payload = data['payload'];
        if (payload is Map<String, dynamic> && payload.containsKey('popular_prompts')) {
          popularPrompts = payload['popular_prompts'] as List<dynamic>?;
        } else if (payload is List) {
          // Direct list in payload
          popularPrompts = payload;
        }
      }
      // Case 3: Data itself is the list (though this shouldn't happen with our structure)
      else if (data.containsKey('data')) {
        popularPrompts = data['data'] as List<dynamic>?;
      }

      // Process the prompts if we found them
      if (popularPrompts != null) {
        appLog.debug('Processing ${popularPrompts.length} popular prompts', name: _logName);
        await _processPopularPrompts(popularPrompts);
        _lastPopularPromptsFetchTime = DateTime.now();
      } else {
        appLog.warning('No popular prompts found in WebSocket data', name: _logName);
      }
    } catch (e, stackTrace) {
      appLog.error('Error handling popular prompts update',
          name: _logName, error: e, stackTrace: stackTrace);
    }
  }
}

/// Mock implementation of the PromptUsageService for testing purposes
class MockPromptUsageService implements PromptUsage {
  @override
  Future<void> fetchPopularPrompts([bool forceRefresh = false]) async {}

  @override
  Future<void> recordUsage(String promptId, PromptUsageEventType eventType) async {}

  @override
  bool shouldSyncData() {
    return false;
  }

  @override
  Future<void> syncUsageData() async {}

  /// Dispose resources
  void dispose() {
    // No resources to dispose in mock implementation
  }
}
