import 'dart:math' as math;
import 'package:promz/database/database.dart';
import 'package:promz/core/utils/url_resolver.dart' as url_resolver;
import 'package:promz/generated/content_upload.pb.dart';
import 'package:promz_common/promz_common.dart';

/// Service for detecting entities in text input
class EntityDetectionService {
  static const _logName = 'EntityDetectionService';

  // Dictionary maps by entity type
  final Map<EntityType, Map<String, Entity>> _entityDictionaries = {};
  final Map<String, Entity> _symbolToEntity = {};
  final Map<String, Entity> _companyNameToEntity = {};
  final Map<String, Entity> _urlToNewsEntity = {};
  final Map<String, Entity> _videoIdToYouTubeEntity = {};

  // Fuzzy matching configuration
  final double _minFuzzyMatchScore = 0.75;

  /// Database connection
  final Future<AppDatabase> _db;

  // Flag to track if dictionaries have been loaded
  bool _dictionariesLoaded = false;

  /// Create an instance with database
  EntityDetectionService({required Future<AppDatabase> db}) : _db = db;

  /// Initialize the entity dictionaries
  Future<void> loadEntityDictionaries() async {
    if (_dictionariesLoaded) return;

    appLog.debug('Loading entity dictionaries', name: _logName);

    try {
      await _loadFinancialEntities();
      // Additional entity types can be loaded in the future

      _dictionariesLoaded = true;
      appLog.debug('Entity dictionaries loaded successfully', name: _logName);
    } catch (e, stack) {
      appLog.error('Error loading entity dictionaries',
          name: _logName, error: e, stackTrace: stack);
    }
  }

  /// Load financial entities (stocks) from the database
  Future<void> _loadFinancialEntities() async {
    try {
      final database = await _db;
      final stockTickers = await database.getAllSP500Tickers();

      final financeDict = <String, Entity>{};
      _symbolToEntity.clear();
      _companyNameToEntity.clear();

      // Process stock tickers
      for (final ticker in stockTickers) {
        // Create stock ticker entity
        final symbol = ticker.symbol;
        final cleanSymbol = EntityUtils.cleanStockSymbol(symbol);
        final fullCompanyName = ticker.companyName;
        final cleanCompanyName = EntityUtils.cleanCompanyName(fullCompanyName);

        // Create stock entity
        final stockEntity = Entity.stockTicker(
          symbol: cleanSymbol,
          companyName: fullCompanyName,
        );

        // Create company entity
        final companyEntity = Entity(
          text: cleanCompanyName,
          canonicalText: fullCompanyName,
          displayText: fullCompanyName,
          type: EntityType.finance,
          confidence: 1.0,
          metadata: {
            MetadataKeys.symbol: cleanSymbol,
            MetadataKeys.companyName: fullCompanyName,
            MetadataKeys.cleanCompanyName: cleanCompanyName,
            MetadataKeys.entitySubtype: 'company',
          },
        );

        // Add stock entity to dictionaries but only when it has a minimum of 3 characters
        _symbolToEntity[symbol.toLowerCase()] = stockEntity;
        _symbolToEntity[cleanSymbol.toLowerCase()] = stockEntity;
        if (cleanSymbol.length >= 3) {
          financeDict[symbol.toLowerCase()] = stockEntity;
          financeDict[cleanSymbol.toLowerCase()] = stockEntity;
        }

        // Add company entity to dictionaries but only when it has a minimum of 3 characters
        _companyNameToEntity[fullCompanyName.toLowerCase()] = companyEntity;
        _companyNameToEntity[cleanCompanyName.toLowerCase()] = companyEntity;
        _companyNameToEntity[normalizeText(fullCompanyName)] = companyEntity;
        if (cleanCompanyName.length >= 3) {
          financeDict[fullCompanyName.toLowerCase()] = companyEntity;
          financeDict[cleanCompanyName.toLowerCase()] = companyEntity;
          financeDict[normalizeText(fullCompanyName)] = companyEntity;
        }
      }

      // Store the finance dictionary
      _entityDictionaries[EntityType.finance] = financeDict;

      appLog.debug(
          'Loaded ${stockTickers.length} financial entities (${financeDict.length} variations)',
          name: _logName);

      // Log the first few stock symbols to aid in debugging
      final symbols = _symbolToEntity.keys.take(5).join(', ');
      appLog.debug('Sample stock symbols: $symbols', name: _logName);
    } catch (e, stack) {
      appLog.error('Error loading financial entities', name: _logName, error: e, stackTrace: stack);
      _entityDictionaries[EntityType.finance] = {};
      _symbolToEntity.clear();
      _companyNameToEntity.clear();
    }
  }

  /// Find a stock entity by ticker symbol (case insensitive)
  Entity? getStockBySymbol(String symbol) {
    // Normalize symbol (ensure lowercase for lookup)
    final normalizedSymbol = symbol.toLowerCase();
    return _symbolToEntity[normalizedSymbol];
  }

  /// Find a stock entity by company name (case insensitive)
  Entity? getStockByCompanyName(String companyName) {
    // Normalize company name (ensure lowercase for lookup)
    final normalizedName = companyName.toLowerCase();
    return _companyNameToEntity[normalizedName];
  }

  /// Find a news article entity by URL (case insensitive)
  Entity? getNewsEntityByUrl(String url) {
    // Normalize URL (ensure lowercase for lookup)
    final normalizedUrl = url.toLowerCase();
    return _urlToNewsEntity[normalizedUrl];
  }

  /// Find a YouTube video entity by video ID (case insensitive)
  Entity? getYouTubeEntityByVideoId(String videoId) {
    // Normalize videoId (ensure lowercase for lookup)
    final normalizedVideoId = videoId.toLowerCase();
    return _videoIdToYouTubeEntity[normalizedVideoId];
  }

  /// Register a news article manually
  /// This allows adding news articles without requiring database storage
  /// Stores raw metadata which can be used later to generate variables.
  Entity registerNewsArticle({
    required String url,
    required String contents,
    String? title,
    String? author,
    String? excerpt,
    String? siteName,
    String? finalUrl,
  }) {
    // Create the base news entity
    final newsEntity = Entity.newsArticle(
      url: url,
      contents: contents,
    );

    // Create a new metadata map that includes the original metadata plus our additions
    final enhancedMetadata = Map<String, dynamic>.from(newsEntity.metadata ?? {});

    // Add provided raw metadata using constants
    enhancedMetadata[MetadataKeys.url] = url;
    enhancedMetadata[MetadataKeys.finalUrl] = finalUrl ?? url;
    enhancedMetadata[MetadataKeys.content] = contents;
    if (title != null) {
      enhancedMetadata[MetadataKeys.title] = title;
    }
    if (author != null) {
      enhancedMetadata[MetadataKeys.author] = author;
    }
    if (excerpt != null) {
      enhancedMetadata[MetadataKeys.excerpt] = excerpt;
    }
    if (siteName != null) {
      enhancedMetadata[MetadataKeys.siteName] = siteName;
    }

    // Create a new entity with the enhanced metadata
    final enhancedEntity = newsEntity.copyWith(metadata: enhancedMetadata);

    // Add to dictionaries
    final newsDict = _entityDictionaries[EntityType.news] ?? <String, Entity>{};
    final lookupUrl = (finalUrl ?? url).toLowerCase();
    newsDict[lookupUrl] = enhancedEntity;
    _urlToNewsEntity[lookupUrl] = enhancedEntity;

    // Ensure the dictionary is stored
    _entityDictionaries[EntityType.news] = newsDict;

    appLog.debug('Registered news article with URL: ${finalUrl ?? url}', name: _logName);

    return enhancedEntity;
  }

  /// Register a YouTube video manually
  /// This allows adding YouTube videos without requiring database storage
  /// Stores raw metadata which can be used later to generate variables.
  Entity registerYouTubeVideo({
    required String videoId,
    required String url,
    required String title,
    required String channelName,
    String? thumbnailUrl,
    String? description,
  }) {
    // Create the base YouTube entity
    final youtubeEntity = Entity(
      text: url,
      canonicalText: videoId,
      displayText: title,
      type: EntityType.youtubeVideo,
      confidence: 1.0,
      metadata: {
        MetadataKeys.videoId: videoId,
        MetadataKeys.url: url,
        MetadataKeys.title: title,
        MetadataKeys.channelName: channelName,
      },
    );

    // Create a new metadata map that includes additional information
    final enhancedMetadata = Map<String, dynamic>.from(youtubeEntity.metadata ?? {});

    // Add additional metadata using constants
    if (thumbnailUrl != null) {
      enhancedMetadata[MetadataKeys.thumbnailUrl] = thumbnailUrl;
    }
    if (description != null) {
      enhancedMetadata[MetadataKeys.description] = description;
    }

    // Create a new entity with the enhanced metadata
    final enhancedEntity = youtubeEntity.copyWith(metadata: enhancedMetadata);

    // Add to dictionaries
    final youtubeDict = _entityDictionaries[EntityType.youtubeVideo] ?? <String, Entity>{};
    final lookupVideoId = videoId.toLowerCase();
    youtubeDict[lookupVideoId] = enhancedEntity;
    _videoIdToYouTubeEntity[lookupVideoId] = enhancedEntity;

    // Ensure the dictionary is stored
    _entityDictionaries[EntityType.youtubeVideo] = youtubeDict;

    appLog.debug('Registered YouTube video with ID: $videoId, title: $title', name: _logName);

    return enhancedEntity;
  }

  /// Add a new entity to the appropriate dictionary
  void addEntity({
    required String id,
    required String text,
    required String type,
    required ProcessingResult? processingResult,
    Map<String, dynamic>? metadata,
  }) {
    appLog.debug('Adding entity: id=$id, type=$type, text=$text', name: _logName);

    // Determine the entity type
    EntityType entityType;
    try {
      // Try to parse the type string into an EntityType enum
      entityType = EntityType.values.firstWhere(
        (t) => t.name.toLowerCase() == type.toLowerCase(),
        orElse: () => EntityType.generic,
      );
    } catch (e) {
      appLog.warning('Unknown entity type: $type, defaulting to custom', name: _logName, error: e);
      entityType = EntityType.generic;
    }

    // Create entity with appropriate type-specific handling
    switch (entityType) {
      case EntityType.finance:
        // For finance entities, we need to determine if it's a stock ticker or company name
        final isStockSymbol = id.length <= 5 && id.toUpperCase() == id;

        if (isStockSymbol) {
          // It's a ticker symbol
          final stockEntity = Entity.stockTicker(
            symbol: id,
            companyName: text,
          );

          // Add to symbol dictionary
          final financeDict = _entityDictionaries[EntityType.finance] ?? <String, Entity>{};
          financeDict[id.toLowerCase()] = stockEntity;
          _symbolToEntity[id.toLowerCase()] = stockEntity;

          // Ensure the dictionary is stored
          _entityDictionaries[EntityType.finance] = financeDict;
        } else {
          // It's a company name
          final companyEntity = Entity(
            text: text,
            canonicalText: id,
            displayText: text,
            type: EntityType.finance,
            confidence: 1.0,
            metadata: {
              MetadataKeys.companyName: text,
              MetadataKeys.cleanCompanyName: EntityUtils.cleanCompanyName(text),
              MetadataKeys.entitySubtype: 'company',
              ...?metadata,
            },
          );

          // Add to company name dictionary
          final financeDict = _entityDictionaries[EntityType.finance] ?? <String, Entity>{};
          financeDict[text.toLowerCase()] = companyEntity;
          _companyNameToEntity[text.toLowerCase()] = companyEntity;

          // Ensure the dictionary is stored
          _entityDictionaries[EntityType.finance] = financeDict;
        }
        break;

      case EntityType.youtubeVideo:
        // For YouTube videos, register using the video ID
        final youtubeEntity = Entity(
          text: text,
          canonicalText: id, // VideoID
          displayText: text, // Title
          type: EntityType.youtubeVideo,
          confidence: 1.0,
          metadata: metadata,
        );

        // Add to YouTube dictionary
        final youtubeDict = _entityDictionaries[EntityType.youtubeVideo] ?? <String, Entity>{};
        youtubeDict[id.toLowerCase()] = youtubeEntity;
        _videoIdToYouTubeEntity[id.toLowerCase()] = youtubeEntity;

        // Ensure the dictionary is stored
        _entityDictionaries[EntityType.youtubeVideo] = youtubeDict;
        break;

      case EntityType.news:
        // For news articles, register using the URL
        final newsEntity = Entity(
          text: text,
          canonicalText: id, // URL
          displayText: text, // Title
          type: EntityType.news,
          confidence: 1.0,
          metadata: metadata,
        );

        // Add to news dictionary
        final newsDict = _entityDictionaries[EntityType.news] ?? <String, Entity>{};
        newsDict[id.toLowerCase()] = newsEntity;
        _urlToNewsEntity[id.toLowerCase()] = newsEntity;

        // Ensure the dictionary is stored
        _entityDictionaries[EntityType.news] = newsDict;
        break;

      default:
        // For other entity types, create a generic entity and store it
        final entity = Entity(
          text: text,
          canonicalText: id,
          displayText: text,
          type: entityType,
          confidence: 1.0,
          metadata: metadata,
        );

        // Add to appropriate dictionary
        final dict = _entityDictionaries[entityType] ?? <String, Entity>{};
        dict[id.toLowerCase()] = entity;

        // Ensure the dictionary is stored
        _entityDictionaries[entityType] = dict;
        break;
    }

    appLog.debug('Entity added successfully: id=$id, type=$type', name: _logName);
  }

  /// Register a new entity (alias for addEntity for backward compatibility)
  void registerEntity({
    required String id,
    required String text,
    required String type,
    required ProcessingResult? processingResult,
    Map<String, dynamic>? metadata,
  }) {
    addEntity(
        id: id, text: text, type: type, processingResult: processingResult, metadata: metadata);
  }

  /// Detect entities in the provided text
  /// Returns a list of entities found in the text
  Future<List<Entity>> detectEntities(String text) async {
    if (text.isEmpty) return [];

    await loadEntityDictionaries(); // Ensure dictionaries are loaded

    // Process text
    final normalizedText = normalizeText(text);
    final tokens = _tokenizeText(normalizedText);

    // Collect results
    final List<Entity> detectedEntities = [];

    // First check for template pattern matches
    _findTemplateEntities(text, detectedEntities);

    // Then check for direct and fuzzy matches in tokens
    for (final type in _entityDictionaries.keys) {
      _findEntitiesForType(type, tokens, text, detectedEntities);
    }

    return detectedEntities;
  }

  /// Get variable values for a financial entity
  Map<String, dynamic> getValuesBySymbol(String symbol) {
    final variables = <String, dynamic>{};
    final entity = getStockBySymbol(symbol);
    if (entity != null) {
      variables[EntityUtils.getCanonicalName('FINANCE:TICKER')] = entity.stockSymbol!;
      variables[EntityUtils.getCanonicalName('FINANCE:COMPANY_NAME')] = entity.companyName!;
    }
    return variables;
  }

  /// Get variable values for a YouTube video by video ID
  /// Constructs the variable map on demand from the entity's raw metadata.
  Map<String, dynamic> getValuesByVideoId(String videoId) {
    final variables = <String, dynamic>{};
    final youtubeEntity = getYouTubeEntityByVideoId(videoId);

    if (youtubeEntity != null && youtubeEntity.metadata != null) {
      final metadata = youtubeEntity.metadata!;
      // Convert video ID to proper YouTube URL using the utility method
      final youtubeUrl = youtubeEntity.metadata?[MetadataKeys.url] ??
          url_resolver.YouTubeUrlHandling.getWatchUrl(videoId);

      // Store the full YouTube URL in the variable
      variables[EntityUtils.getCanonicalName('MEDIA:YOUTUBE_URL')] = youtubeUrl;
      variables[EntityUtils.getCanonicalName('MEDIA:YOUTUBE_TITLE')] =
          metadata[MetadataKeys.title] ?? '';
      variables[EntityUtils.getCanonicalName('MEDIA:YOUTUBE_CHANNEL')] =
          metadata[MetadataKeys.channelName] ?? '';
      variables[EntityUtils.getCanonicalName('MEDIA:YOUTUBE_DESCRIPTION')] =
          metadata[MetadataKeys.description] ?? '';
      variables[EntityUtils.getCanonicalName('MEDIA:YOUTUBE_THUMBNAIL')] =
          metadata[MetadataKeys.thumbnailUrl] ?? '';
      appLog.debug('Added YouTube variables for video ID: $videoId, with URL: $youtubeUrl',
          name: _logName);
    } else {
      // Create a standard YouTube URL even when we don't have the entity
      final youtubeUrl = url_resolver.YouTubeUrlHandling.getWatchUrl(videoId);
      variables[EntityUtils.getCanonicalName('MEDIA:YOUTUBE_URL')] = youtubeUrl;
    }

    return variables;
  }

  /// Get variable values for a conversation by attachment ID
  /// Constructs the variable map on demand from the attachment metadata
  Map<String, dynamic> getValuesByConversationId(
      String attachmentId, Map<String, dynamic>? attachment) {
    final variables = <String, dynamic>{};

    if (attachment != null && attachment['type'] == 'conversation') {
      final metadata = attachment['metadata'] as Map<String, dynamic>? ?? {};
      final content = attachment['content'] as String? ?? '';

      // Add conversation content
      variables[EntityUtils.getCanonicalName('CONVERSATION:CONTENTS')] = content;

      // Add metadata fields if available
      if (metadata.containsKey('platform')) {
        variables[EntityUtils.getCanonicalName('CONVERSATION:PLATFORM')] =
            metadata['platform'] ?? '';
      }

      if (metadata.containsKey('groupName')) {
        variables[EntityUtils.getCanonicalName('CONVERSATION:GROUP_NAME')] =
            metadata['groupName'] ?? '';
      }

      if (metadata.containsKey('participants')) {
        final participants = metadata['participants'];
        if (participants is List) {
          variables[EntityUtils.getCanonicalName('CONVERSATION:PARTICIPANTS')] =
              participants.join(', ');
        } else if (participants is String) {
          variables[EntityUtils.getCanonicalName('CONVERSATION:PARTICIPANTS')] = participants;
        }
      }

      if (metadata.containsKey('messageCount')) {
        variables[EntityUtils.getCanonicalName('CONVERSATION:MESSAGE_COUNT')] =
            metadata['messageCount']?.toString() ?? '';
      }

      appLog.debug('Added conversation variables for ID: $attachmentId', name: _logName);
    } else {
      // Set empty values for required fields
      variables[EntityUtils.getCanonicalName('CONVERSATION:CONTENTS')] = '';
      variables[EntityUtils.getCanonicalName('CONVERSATION:PARTICIPANTS')] = '';
    }

    return variables;
  }

  /// Get variable values for any entity type based on a primary identifier
  Map<String, dynamic> getVariablePackage(EntityType type, String primaryId,
      {Map<String, dynamic>? additionalData}) {
    switch (type) {
      case EntityType.finance:
        return getValuesBySymbol(primaryId);
      case EntityType.conversation:
        // For conversation entities, we need the attachment data
        if (additionalData != null) {
          return getValuesByConversationId(primaryId, additionalData);
        }
        return {};
      default:
        return {};
    }
  }

  /// Find entities that follow the template pattern {{TYPE:ENTITY}}
  void _findTemplateEntities(String text, List<Entity> results) {
    final templateRegex = RegExp(r'\{\{([A-Z]+):([^}]+)\}\}');
    final matches = templateRegex.allMatches(text);

    for (final match in matches) {
      if (match.groupCount >= 2) {
        final typeString = match.group(1)?.toLowerCase();
        final entityText = match.group(2);

        if (typeString != null && entityText != null) {
          EntityType? type;
          try {
            type = EntityType.values
                .firstWhere((t) => t.name.toLowerCase() == typeString.toLowerCase());
          } catch (e) {
            appLog.warning('Unknown entity type in template: $typeString',
                name: _logName, error: e);
            continue;
          }

          final entity = Entity(
            text: match.group(0) ?? '',
            canonicalText: entityText,
            displayText: _getDisplayTextForEntity(type, entityText),
            type: type,
            startPosition: match.start,
            endPosition: match.end,
          );

          results.add(entity);
        }
      }
    }
  }

  /// Find entities of the given type in tokens
  void _findEntitiesForType(
      EntityType type, List<String> tokens, String originalText, List<Entity> results) {
    final dictionary = _entityDictionaries[type];
    if (dictionary == null || dictionary.isEmpty) return;

    if (type == EntityType.finance) {
      _findFinancialEntities(tokens, originalText, results);
    }

    if (type == EntityType.news) {
      _findNewsEntities(tokens, originalText, results);
    }

    if (type == EntityType.youtubeVideo) {
      _findYouTubeEntities(tokens, originalText, results);
    }

    for (int i = 0; i < tokens.length - 1; i++) {
      for (int j = i + 1; j < math.min(i + 5, tokens.length); j++) {
        final phrase = tokens.sublist(i, j + 1).join(' ');
        final normalizedPhrase = normalizeText(phrase);

        if (dictionary.containsKey(normalizedPhrase)) {
          final entity = dictionary[normalizedPhrase]!;

          final startPos = _findPositionInOriginalText(originalText, tokens[i], i, tokens);
          final endToken = tokens[j];
          final endPos = originalText.indexOf(endToken, startPos) + endToken.length;

          if (startPos >= 0 && endPos > startPos) {
            final entityWithPosition = Entity(
              text: entity.text,
              canonicalText: entity.canonicalText,
              displayText: entity.displayText,
              type: entity.type,
              startPosition: startPos,
              endPosition: endPos,
              confidence: entity.confidence,
              metadata: entity.metadata,
            );
            results.add(entityWithPosition);
          } else {
            results.add(entity);
          }
          continue;
        } else {
          _findFuzzyMatches(normalizedPhrase, type, dictionary, results);
        }
      }
    }

    for (int i = 0; i < tokens.length; i++) {
      final token = tokens[i];
      final normalizedToken = normalizeText(token);

      if (dictionary.containsKey(normalizedToken)) {
        final entity = dictionary[normalizedToken]!;

        final entityWithPosition = Entity(
          text: entity.text,
          canonicalText: entity.canonicalText,
          displayText: entity.displayText,
          type: entity.type,
          startPosition: _findPositionInOriginalText(originalText, token, i, tokens),
          endPosition: _findPositionInOriginalText(originalText, token, i, tokens) + token.length,
          confidence: entity.confidence,
          metadata: entity.metadata,
        );

        results.add(entityWithPosition);
        continue;
      }

      _findFuzzyMatches(normalizedToken, type, dictionary, results);
    }
  }

  int _findPositionInOriginalText(
      String originalText, String token, int tokenIndex, List<String> allTokens) {
    final directMatchPos = originalText.toLowerCase().indexOf(token);
    if (directMatchPos >= 0) return directMatchPos;

    int tokenCount = 0;

    for (int i = 0; i < originalText.length; i++) {
      if (i > 0 && _isWhitespace(originalText[i - 1]) && !_isWhitespace(originalText[i])) {
        tokenCount++;
      }

      if (tokenCount == tokenIndex) {
        return i;
      }
    }

    return -1;
  }

  bool _isWhitespace(String char) {
    return char == ' ' || char == '\t' || char == '\n';
  }

  void _findFinancialEntities(List<String> tokens, String originalText, List<Entity> results) {
    for (int i = 0; i < tokens.length - 1; i++) {
      for (int j = i + 1; j < math.min(i + 8, tokens.length); j++) {
        final phrase = tokens.sublist(i, j + 1).join(' ');
        final companyEntity = getStockByCompanyName(phrase);
        if (companyEntity != null) {
          final startPos = _findPositionInOriginalText(originalText, tokens[i], i, tokens);
          final endToken = tokens[j];
          final endPos = originalText.indexOf(endToken, startPos) + endToken.length;

          if (startPos >= 0 && endPos > startPos) {
            final entityWithPosition = Entity(
              text: companyEntity.text,
              canonicalText: companyEntity.canonicalText,
              displayText: companyEntity.displayText,
              type: EntityType.finance,
              startPosition: startPos,
              endPosition: endPos,
              confidence: 1.0,
              metadata: companyEntity.metadata,
            );
            results.add(entityWithPosition);
          } else {
            results.add(companyEntity);
          }
        }
      }
    }

    for (int i = 0; i < tokens.length; i++) {
      final token = tokens[i];
      final stockEntity = getStockBySymbol(token);
      if (stockEntity != null) {
        final entityWithPosition = Entity(
          text: stockEntity.text,
          canonicalText: stockEntity.canonicalText,
          displayText: stockEntity.displayText,
          type: EntityType.finance,
          startPosition: _findPositionInOriginalText(originalText, token, i, tokens),
          endPosition: _findPositionInOriginalText(originalText, token, i, tokens) + token.length,
          confidence: 1.0,
          metadata: stockEntity.metadata,
        );

        results.add(entityWithPosition);
      }
    }
  }

  void _findNewsEntities(List<String> tokens, String originalText, List<Entity> results) {
    for (int i = 0; i < tokens.length; i++) {
      final token = tokens[i];
      final newsEntity = getNewsEntityByUrl(token);
      if (newsEntity != null) {
        final entityWithPosition = Entity(
          text: newsEntity.text,
          canonicalText: newsEntity.canonicalText,
          displayText: newsEntity.displayText,
          type: EntityType.news,
          startPosition: _findPositionInOriginalText(originalText, token, i, tokens),
          endPosition: _findPositionInOriginalText(originalText, token, i, tokens) + token.length,
          confidence: 1.0,
          metadata: newsEntity.metadata,
        );

        results.add(entityWithPosition);
      }
    }
  }

  void _findYouTubeEntities(List<String> tokens, String originalText, List<Entity> results) {
    for (int i = 0; i < tokens.length; i++) {
      final token = tokens[i];
      final youtubeEntity = getYouTubeEntityByVideoId(token);
      if (youtubeEntity != null) {
        final entityWithPosition = Entity(
          text: youtubeEntity.text,
          canonicalText: youtubeEntity.canonicalText,
          displayText: youtubeEntity.displayText,
          type: EntityType.youtubeVideo,
          startPosition: _findPositionInOriginalText(originalText, token, i, tokens),
          endPosition: _findPositionInOriginalText(originalText, token, i, tokens) + token.length,
          confidence: 1.0,
          metadata: youtubeEntity.metadata,
        );

        results.add(entityWithPosition);
      }
    }
  }

  void _findFuzzyMatches(
      String token, EntityType type, Map<String, Entity> dictionary, List<Entity> results) {
    if (token.length < 3) return;

    String? bestMatch;
    double bestScore = _minFuzzyMatchScore;

    for (final key in dictionary.keys) {
      if (key.length > 2) {
        final keyTokens = _tokenizeText(key);
        final tokenizedKey = keyTokens.join(' ');

        if (tokenizedKey.toLowerCase() == token.toLowerCase()) {
          bestMatch = key;
          bestScore = 1.0;
          break;
        }
        final score = _calculateFuzzyMatchScore(token, key);
        if (score > bestScore) {
          bestScore = score;
          bestMatch = key;
        }
      }
    }

    if (bestMatch != null) {
      final baseEntity = dictionary[bestMatch]!;
      final fuzzyEntity = Entity(
        text: token,
        canonicalText: baseEntity.canonicalText,
        displayText: baseEntity.displayText,
        type: type,
        confidence: bestScore,
        metadata: baseEntity.metadata,
      );
      appLog.debug('  Best match found: bestMatch=$bestMatch, bestScore=$bestScore',
          name: _logName);
      results.add(fuzzyEntity);
    } else {
      appLog.debug('  No match found for token=$token', name: _logName);
    }
  }

  double _calculateFuzzyMatchScore(String input, String target) {
    final normalizedInput = input.toLowerCase();
    final normalizedTarget = target.toLowerCase();

    if (normalizedInput == normalizedTarget) return 1.0;

    final lengthDiff = (normalizedInput.length - normalizedTarget.length).abs();
    if (lengthDiff > normalizedInput.length / 2) return 0.0;

    final distance = _levenshteinDistance(normalizedInput, normalizedTarget);
    final maxLength = math.max(normalizedInput.length, normalizedTarget.length);

    return 1.0 - (distance / maxLength);
  }

  int _levenshteinDistance(String s1, String s2) {
    if (s1 == s2) return 0;
    if (s1.isEmpty) return s2.length;
    if (s2.isEmpty) return s1.length;

    List<int> v0 = List<int>.generate(s2.length + 1, (i) => i);
    List<int> v1 = List<int>.filled(s2.length + 1, 0);

    for (int i = 0; i < s1.length; i++) {
      v1[0] = i + 1;

      for (int j = 0; j < s2.length; j++) {
        final cost = s1[i] == s2[j] ? 0 : 1;
        v1[j + 1] = math.min(v1[j] + 1, math.min(v0[j + 1] + 1, v0[j] + cost));
      }

      final temp = v0;
      v0 = v1;
      v1 = temp;
    }

    return v0[s2.length];
  }

  List<String> _tokenizeText(String text) {
    final normalized = normalizeText(text);
    final tokens = normalized.split(RegExp(r'\s+'));

    return tokens.where((t) => t.isNotEmpty).toList();
  }

  String _getDisplayTextForEntity(EntityType type, String canonicalText) {
    switch (type) {
      case EntityType.finance:
        final stockEntity = getStockBySymbol(canonicalText);
        if (stockEntity != null) {
          return stockEntity.displayText;
        }

        final companyEntity = getStockByCompanyName(canonicalText);
        if (companyEntity != null) {
          return companyEntity.displayText;
        }

        return 'Financial entity: $canonicalText';

      case EntityType.news:
        final newsEntity = getNewsEntityByUrl(canonicalText);
        if (newsEntity != null) {
          return newsEntity.displayText;
        }

        return 'News article: $canonicalText';

      case EntityType.conversation:
        // For conversations, we use a more descriptive format
        // The canonicalText is typically the attachment ID
        return 'WhatsApp Chat';

      case EntityType.youtubeVideo:
        final youtubeEntity = getYouTubeEntityByVideoId(canonicalText);
        if (youtubeEntity != null) {
          return youtubeEntity.displayText;
        }
        return 'YouTube video: $canonicalText';

      default:
        return canonicalText;
    }
  }

  List<Entity> getAllEntities() {
    final uniqueEntities = <String, Entity>{};

    for (final entity in _symbolToEntity.values) {
      final symbol = entity.stockSymbol;
      final entitySubtype = entity.entitySubtype;

      if (symbol != null && entitySubtype == 'stock') {
        final key = symbol.toLowerCase();
        if (!uniqueEntities.containsKey(key)) {
          final cleanSymbol = EntityUtils.cleanStockSymbol(symbol);
          uniqueEntities[key] = entity.copyWith(
            text: cleanSymbol,
            displayText: entity.displayText,
            canonicalText: entity.canonicalText,
          );
        }
      }
    }

    for (final entity in _companyNameToEntity.values) {
      final symbol = entity.stockSymbol;

      if (symbol != null && entity.metadata?[MetadataKeys.entitySubtype] == 'company') {
        final key = symbol.toLowerCase();
        if (!uniqueEntities.containsKey('${key}_company')) {
          final cleanName = entity.companyName ?? EntityUtils.cleanCompanyName(entity.text);
          uniqueEntities['${key}_company'] = entity.copyWith(
            text: cleanName,
            displayText: entity.displayText,
            canonicalText: entity.canonicalText,
          );
        }
      }
    }

    for (final entity in _urlToNewsEntity.values) {
      final url = entity.canonicalText;
      if (!uniqueEntities.containsKey(url)) {
        uniqueEntities[url] = entity;
      }
    }

    for (final entity in _videoIdToYouTubeEntity.values) {
      final videoId = entity.canonicalText;
      if (!uniqueEntities.containsKey(videoId)) {
        uniqueEntities[videoId] = entity;
      }
    }

    appLog.debug(
        'Retrieved ${uniqueEntities.length} unique entities '
        'from ${_symbolToEntity.length} symbol entities, '
        '${_companyNameToEntity.length} company entities, '
        '${_urlToNewsEntity.length} news article entities, and '
        '${_videoIdToYouTubeEntity.length} YouTube video entities',
        name: _logName);

    return uniqueEntities.values.toList();
  }

  String? getDisplayTextForFinance(String nameOrTicker) {
    if (nameOrTicker.isEmpty) {
      return null;
    }

    appLog.debug('START: Getting display text for: $nameOrTicker', name: _logName);

    final stockByName = getStockByCompanyName(nameOrTicker);

    final stockBySymbol = stockByName == null ? getStockBySymbol(nameOrTicker) : null;

    final stock = stockByName ?? stockBySymbol;
    if (stock == null) {
      appLog.debug('END: No stock found: $nameOrTicker', name: _logName);
      return nameOrTicker;
    }

    final isTicker = stockByName == null;

    final String companyName =
        (isTicker ? (stock.companyName) : stock.displayText) ?? 'Unknown Company';
    final String symbol = (stock.stockSymbol) ?? '';

    final formattedText = isTicker ? '$symbol ($companyName)' : '$companyName ($symbol)';

    appLog.debug('END: Display text for company: $formattedText', name: _logName);
    return formattedText;
  }
}
