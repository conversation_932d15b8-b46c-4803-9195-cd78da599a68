import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:promz/core/providers/service_providers.dart';
import 'package:promz/core/services/client_context_service.dart';
import 'package:promz_common/promz_common.dart';

class AutoCompleteSuggestionList extends ConsumerWidget {
  static const _logName = 'AutoCompleteSuggestionList';
  final Iterable<DisplayItem> options;
  final void Function(DisplayItem) onSelected;
  final String logName;

  const AutoCompleteSuggestionList({
    Key? key,
    required this.options,
    required this.onSelected,
    this.logName = _logName,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (options.isEmpty) {
      return const SizedBox.shrink();
    }
    return Platform.isIOS ? _buildCupertinoList(context, ref) : _buildMaterialList(context, ref);
  }

  Widget _buildMaterialList(BuildContext context, WidgetRef ref) {
    return Material(
      type: MaterialType.transparency,
      child: ListView.builder(
        shrinkWrap: true,
        padding: EdgeInsets.zero,
        itemCount: options.length,
        itemBuilder: (context, index) {
          final option = options.elementAt(index);
          return _buildMaterialItem(context, ref, option);
        },
      ),
    );
  }

  Widget _buildCupertinoList(BuildContext context, WidgetRef ref) {
    return CupertinoScrollbar(
      child: ListView.builder(
        shrinkWrap: true,
        padding: EdgeInsets.zero,
        itemCount: options.length,
        itemBuilder: (context, index) {
          final option = options.elementAt(index);
          return _buildCupertinoItem(context, ref, option);
        },
      ),
    );
  }

  Widget _buildMaterialItem(BuildContext context, WidgetRef ref, DisplayItem suggestion) {
    final theme = Theme.of(context);
    final (icon, color) = _getIconAndColor(suggestion, ref);

    // For entity suggestions, we want to display what will be inserted
    final displayText = suggestion.displayText;

    return InkWell(
      onTap: () => onSelected(suggestion),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
        child: ListTile(
          leading: Icon(icon, color: color),
          title: Text(
            displayText,
            style: TextStyle(
              fontWeight: _shouldBoldText(suggestion.type) ? FontWeight.bold : FontWeight.normal,
              color: theme.colorScheme.onSurface,
              fontFamily: suggestion.type == DisplayItemType.entity ? 'monospace' : null,
            ),
          ),
          subtitle: suggestion.subtitle != null
              ? Text(
                  suggestion.subtitle!,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                )
              : null,
          dense: true,
          visualDensity: VisualDensity.compact,
          trailing: suggestion.type == DisplayItemType.entity && suggestion.relevanceScore != null
              ? _buildConfidenceIndicator(suggestion.relevanceScore!)
              : null,
        ),
      ),
    );
  }

  Widget _buildCupertinoItem(BuildContext context, WidgetRef ref, DisplayItem suggestion) {
    final (icon, color) = _getIconAndColor(suggestion, ref);

    // For entity suggestions, we want to display what will be inserted
    final displayText = suggestion.displayText;

    return GestureDetector(
      onTap: () => onSelected(suggestion),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: CupertinoColors.separator.resolveFrom(context),
              width: 0.0,
            ),
          ),
        ),
        child: Row(
          children: [
            Icon(icon, color: color, size: 20),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    displayText,
                    style: TextStyle(
                      fontWeight:
                          _shouldBoldText(suggestion.type) ? FontWeight.bold : FontWeight.normal,
                      fontFamily: suggestion.type == DisplayItemType.entity ? 'monospace' : null,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    suggestion.subtitle ?? '',
                    style: TextStyle(
                      fontSize: Theme.of(context).textTheme.bodySmall?.fontSize,
                      color: CupertinoColors.secondaryLabel,
                    ),
                  ),
                ],
              ),
            ),
            if (suggestion.type == DisplayItemType.entity && suggestion.relevanceScore != null)
              _buildConfidenceIndicator(suggestion.relevanceScore!),
          ],
        ),
      ),
    );
  }

  Widget _buildConfidenceIndicator(double confidence) {
    // Color the indicator based on confidence score
    final color = confidence > 0.9
        ? Colors.green
        : confidence > 0.75
            ? Colors.lightGreen
            : confidence > 0.6
                ? Colors.amber
                : Colors.orange;

    return Container(
      width: 8,
      height: 8,
      margin: const EdgeInsets.only(right: 8),
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: color,
      ),
    );
  }

  (IconData, Color) _getIconAndColor(DisplayItem suggestion, WidgetRef ref) {
    // Default financial status to false
    bool hasFinancial = false;

    // Check if prompt exists before checking financial variables
    if (suggestion.type == DisplayItemType.promptTitle && suggestion.prompt != null) {
      // Check service availability before checking for financial variables
      final clientContextAsync = ref.read(clientContextServiceProvider);
      if (clientContextAsync.hasValue) {
        final service = clientContextAsync.value!;
        hasFinancial = service.hasFinancialVariables(suggestion.prompt!);
      } else {
        // Log if service is not ready, could be loading or error
        if (clientContextAsync.isLoading) {
          appLog.debug('ClientContextService loading, cannot check financial variables.',
              name: logName);
        } else if (clientContextAsync.hasError) {
          appLog.error('ClientContextService error, cannot check financial variables.',
              name: logName, error: clientContextAsync.error);
        }
      }
    }

    // Check if this is a prompt with financial variables
    if (suggestion.type == DisplayItemType.promptTitle &&
        suggestion.prompt != null &&
        hasFinancial) {
      return (Icons.trending_up, Colors.green);
    }

    // Standard icon based on type
    return switch (suggestion.type) {
      DisplayItemType.promptTitle => (Icons.lightbulb, Colors.amber),
      DisplayItemType.keywordMatch => (Icons.auto_awesome, Colors.orange),
      DisplayItemType.stockSymbol => (Icons.trending_up, Colors.green),
      DisplayItemType.companyName => (Icons.business, Colors.blue),
      DisplayItemType.keywordWord => (Icons.tag, Colors.purple),
      DisplayItemType.categoryWord => (Icons.category, Colors.teal),
      DisplayItemType.promptWord => (Icons.description, Colors.indigo),
      DisplayItemType.entity => (Icons.smart_button, Colors.deepPurple),
    };
  }

  bool _shouldBoldText(DisplayItemType type) {
    return type == DisplayItemType.keywordWord ||
        type == DisplayItemType.promptTitle ||
        type == DisplayItemType.keywordMatch ||
        type == DisplayItemType.stockSymbol ||
        type == DisplayItemType.entity;
  }
}
