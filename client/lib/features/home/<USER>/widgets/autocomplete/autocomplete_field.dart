import 'dart:async';
import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:promz/core/providers/service_providers.dart';
import 'package:promz/features/home/<USER>/variable_manager.dart';
import 'package:promz/features/home/<USER>/home_viewmodel.dart';
import 'package:promz/features/home/<USER>/source_input_viewmodel.dart';
import 'package:promz/features/home/<USER>/widgets/autocomplete/autocomplete_suggestion_list.dart';
import 'package:promz_common/promz_common.dart';

/// AutoCompleteField: Advanced text input with intelligent suggestion capabilities
///
/// This widget provides a rich text input experience with context-aware suggestions,
/// variable resolution, and platform-specific behaviors. It serves as the primary
/// interface for users to enter prompts and queries.
///
/// Key Responsibilities:
///   - Displays and manages an input field with real-time suggestions
///   - Handles suggestion selection and variable resolution
///   - Manages overlay UI for displaying suggestions
///   - Provides platform-specific animations and behaviors
///   - Coordinates with the VariableManager for template variable processing
///   - Supports both entity-based and prompt-based suggestions
///
/// The component uses a layered architecture with specialized handlers for different
/// aspects of text input and ensures that template variables are properly resolved
/// to their actual values when suggestions are selected.
class AutoCompleteField extends ConsumerStatefulWidget {
  final TextEditingController controller;
  final FocusNode focusNode;
  final Function(String) onTextChanged;
  final SourceInputViewModel viewModel;
  final HomeViewModel homeViewModel;
  final Function(DisplayItem, WidgetRef) onSuggestionSelected;
  final Function(DisplayItem) onPromptSelected;
  final VoidCallback onSubmitted;

  const AutoCompleteField({
    Key? key,
    required this.controller,
    required this.focusNode,
    required this.onTextChanged,
    required this.viewModel,
    required this.homeViewModel,
    required this.onSuggestionSelected,
    required this.onPromptSelected,
    required this.onSubmitted,
  }) : super(key: key);

  @override
  ConsumerState<AutoCompleteField> createState() => _AutoCompleteFieldState();
}

class _AutoCompleteFieldState extends ConsumerState<AutoCompleteField>
    with SingleTickerProviderStateMixin {
  static const _logName = 'AutoCompleteField';

  // State management
  bool _showSuggestions = false;
  OverlayEntry? _overlayEntry;
  final LayerLink _layerLink = LayerLink();

  // Animation controller for platform-specific animations
  late AnimationController _animationController;
  late Animation<double> _animation;

  // Timers
  Timer? _debounceTimer;
  Timer? _longInactivityTimer;

  @override
  void initState() {
    super.initState();
    widget.focusNode.addListener(_handleFocusChange);

    // Initialize animation controller
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    );
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    _longInactivityTimer?.cancel();
    _removeOverlay();
    widget.focusNode.removeListener(_handleFocusChange);
    _animationController.dispose();
    super.dispose();
  }

  void _handleFocusChange() {
    if (!widget.focusNode.hasFocus) {
      _hideSuggestions();
    }
  }

  void _hideSuggestions() {
    if (_showSuggestions) {
      _animationController.reverse().then((_) {
        if (mounted) {
          setState(() => _showSuggestions = false);
          _removeOverlay();
          widget.viewModel.clearSuggestions();
        }
      });
    }
  }

  void _updateOverlay() {
    _removeOverlay();

    if (!_showSuggestions || !widget.focusNode.hasFocus) return;

    // Wait for the next frame to ensure we have the correct layout
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted || !_showSuggestions) return;

      // Create the overlay entry
      _overlayEntry = OverlayEntry(
        builder: (context) => _buildSuggestionsOverlay(),
      );

      // Add the overlay to the screen
      Overlay.of(context).insert(_overlayEntry!);

      // Start the animation
      _animationController.forward();
    });
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  void _handleTextChanged(String text) {
    // Reset timers
    _debounceTimer?.cancel();
    _longInactivityTimer?.cancel();

    // Update parent
    widget.onTextChanged(text);

    if (text.isEmpty) {
      _hideSuggestions();
      return;
    }

    // Set up debounce timer for suggestions
    _debounceTimer = Timer(const Duration(milliseconds: 300), () {
      if (!mounted) return;

      // Update suggestions with current cursor position
      widget.viewModel.getAutoCompleteSuggestions(
        ref,
        text,
        widget.controller.selection.baseOffset,
      );

      // Show suggestions if we have any, regardless of current state
      if (widget.viewModel.suggestions.isNotEmpty && widget.focusNode.hasFocus) {
        if (!_showSuggestions) {
          setState(() => _showSuggestions = true);
          _updateOverlay();
          _animationController.forward();
        } else {
          // If already showing, just update the overlay
          _updateOverlay();
        }
      } else if (_showSuggestions && widget.viewModel.suggestions.isEmpty) {
        // Hide if we have no suggestions
        _hideSuggestions();
      }
    });

    // Set up long inactivity timer (15 seconds)
    _longInactivityTimer = Timer(const Duration(seconds: 15), () {
      if (!mounted) return;
      _hideSuggestions();
    });
  }

  void _handleSuggestionSelected(DisplayItem suggestion) {
    appLog.debug(
      'START: Suggestion selected: ${suggestion.text}',
      name: _logName,
    );

    // Hide suggestions immediately
    _hideSuggestions();

    // Check if this is a prompt selection
    final promptModel = suggestion.prompt;
    if (promptModel != null) {
      // This is a prompt selection, need VariableManager
      final clientContextAsync = ref.read(clientContextServiceProvider);
      clientContextAsync.when(
        data: (service) {
          // Service is available, process the prompt selection
          _processPromptSelection(promptModel, suggestion, service.variableManager);
        },
        loading: () {
          // Handle loading state - maybe show a brief indicator or log
          appLog.warning('ClientContextService is loading, cannot process prompt selection yet.',
              name: _logName);
          // Optionally show feedback to user
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Initializing context... Please wait.')),
          );
        },
        error: (error, stack) {
          // Handle error state
          appLog.error('Error accessing ClientContextService for prompt selection.',
              name: _logName, error: error, stackTrace: stack);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error processing selection: $error')),
          );
        },
      );
    } else {
      // Not a prompt model, just a regular suggestion (e.g., entity)
      // This likely still needs the SourceInputViewModel to handle it,
      // which internally uses ClientContextService if needed.
      widget.onSuggestionSelected(suggestion, ref);
    }

    // Request focus back to the text field after a short delay
    // This ensures focus is set after any text processing completes
    Future.delayed(const Duration(milliseconds: 50), () {
      if (mounted) {
        widget.focusNode.requestFocus();

        // Optionally position cursor at the end of text for better UX
        widget.controller.selection = TextSelection.fromPosition(
          TextPosition(offset: widget.controller.text.length),
        );
      }
    });

    appLog.debug('END: Suggestion selected', name: _logName);
  }

  /// Handle suggestion selection with template variables
  void _processPromptSelection(
      PromptModel promptModel, DisplayItem suggestion, VariableManager variableManager) {
    appLog.debug('START: Processing prompt selection with template variables', name: _logName);

    // Initialize the variable manager with the prompt
    variableManager.initWithPrompt(promptModel);

    // Update the input text with the template
    widget.controller.text = variableManager.displayText;
    widget.onTextChanged(variableManager.displayText);
    widget.onPromptSelected(suggestion);

    appLog.debug('END: Processing prompt selection with template variables', name: _logName);
  }

  Widget _buildSuggestionsOverlay() {
    final RenderBox textFieldRenderBox = context.findRenderObject() as RenderBox;
    final textFieldSize = textFieldRenderBox.size;
    final screenSize = MediaQuery.of(context).size;

    return Stack(
      children: [
        // Add modal barrier to capture taps outside
        Positioned.fill(
          child: GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: _hideSuggestions,
            child: Container(
              color: Colors.transparent,
            ),
          ),
        ),
        CompositedTransformFollower(
          link: _layerLink,
          showWhenUnlinked: false,
          offset: Offset(0.0, textFieldSize.height + 5.0),
          child: AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return Transform.translate(
                offset: Offset(
                  0,
                  Platform.isIOS
                      ? (1 - _animation.value) * 20 // Slide up on iOS
                      : 0, // No slide on Android
                ),
                child: Opacity(
                  opacity: _animation.value,
                  child: child,
                ),
              );
            },
            child: Material(
              elevation: 8,
              borderRadius: BorderRadius.circular(8),
              color: Theme.of(context).colorScheme.surface,
              clipBehavior: Clip.antiAlias,
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  maxHeight: screenSize.height * 0.4, // 40% of screen height
                  minWidth: textFieldSize.width,
                ),
                child: Platform.isIOS
                    ? _buildCupertinoSuggestionList()
                    : _buildMaterialSuggestionList(),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCupertinoSuggestionList() {
    return CupertinoTheme(
      data: CupertinoThemeData(
        primaryColor: Theme.of(context).primaryColor,
      ),
      child: AutoCompleteSuggestionList(
        options: widget.viewModel.suggestions,
        onSelected: _handleSuggestionSelected,
        logName: _logName,
      ),
    );
  }

  Widget _buildMaterialSuggestionList() {
    return AutoCompleteSuggestionList(
      options: widget.viewModel.suggestions,
      onSelected: _handleSuggestionSelected,
      logName: _logName,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Stack(
          children: [
            CompositedTransformTarget(
              link: _layerLink,
              child: Stack(
                children: [
                  Platform.isIOS ? _buildCupertinoTextField() : _buildMaterialTextField(),
                  Positioned(
                    right: 8,
                    top: 0,
                    bottom: 0,
                    child: Center(
                      child: IconButton(
                        icon: const Icon(Icons.send),
                        onPressed: widget.onSubmitted,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildMaterialTextField() {
    return TextField(
      controller: widget.controller,
      focusNode: widget.focusNode,
      maxLines: 2,
      decoration: const InputDecoration(
        hintText: Strings.sourceInputFieldHint,
        border: OutlineInputBorder(),
        contentPadding: EdgeInsets.fromLTRB(12, 12, 48, 12),
      ),
      onChanged: _handleTextChanged,
      onSubmitted: (_) => widget.onSubmitted(),
    );
  }

  Widget _buildCupertinoTextField() {
    return CupertinoTextField(
      controller: widget.controller,
      focusNode: widget.focusNode,
      maxLines: 2,
      placeholder: Strings.sourceInputFieldHint,
      padding: const EdgeInsets.fromLTRB(12, 12, 48, 12),
      onChanged: _handleTextChanged,
      onSubmitted: (_) => widget.onSubmitted(),
    );
  }
}
