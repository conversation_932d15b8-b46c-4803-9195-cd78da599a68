import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:promz/core/providers/service_providers.dart';
import 'package:promz/core/services/attachment/attachment_registry_service.dart';
import 'package:promz/core/widgets/content_box.dart';
import 'package:promz/features/home/<USER>/home_viewmodel.dart';
import 'package:promz/features/home/<USER>/source_input_viewmodel.dart';
import 'package:promz/features/home/<USER>/widgets/autocomplete/autocomplete_field.dart';
import 'package:promz/features/home/<USER>/widgets/clipboard_handler.dart';
import 'package:promz/features/home/<USER>/widgets/source_list.dart';
import 'package:promz/features/home/<USER>/widgets/text_handling/hashtag_handler.dart';
import 'package:promz/features/home/<USER>/widgets/text_handling/suggestion_handler.dart';
import 'package:promz/features/home/<USER>/widgets/text_handling/text_analysis_handler.dart';
import 'package:promz/features/home/<USER>/widgets/variables/variables_section.dart';
import 'package:promz/features/input_selection/models/input_source.dart';
import 'package:promz_common/promz_common.dart';

// Create a provider for SourceInputViewModel that can be overridden in tests
final sourceInputViewModelProvider =
    ChangeNotifierProvider.autoDispose<SourceInputViewModel>((ref) {
  return SourceInputViewModel(ref);
});

// Create a global key for accessing the SourceInputSection state
final sourceInputSectionKey = GlobalKey<SourceInputSectionState>();

/// SourceInputSection: Main text input component for user prompts and queries
///
/// This widget serves as the primary input interface for users to enter prompts,
/// queries, and interact with the autocomplete system. It manages text input,
/// suggestion handling, and integration with various services.
///
/// Key Responsibilities:
///   - Provides a rich text input field with autocomplete capabilities
///   - Handles text analysis and entity detection as users type
///   - Manages suggestion selection and variable replacement in the input text
///   - Coordinates with the clipboard system for copy/paste operations
///   - Supports hashtag detection and special formatting
///   - Maintains the input state and communicates with parent components
///
/// The component uses several specialized handlers (SuggestionHandler, TextAnalysisHandler,
/// HashtagHandler) to process different aspects of text input and ensures that
/// template variables are properly resolved to their actual values when suggestions
/// are selected.
class SourceInputSection extends ConsumerStatefulWidget {
  final String initialText;
  final List<InputSource> sources;
  final Function(String) onTextChanged;
  final Function(InputSource) onSourceDeleted;
  final Function(InputSource) onSourceAdded;
  final bool isProcessing;
  final List<DisplayItem> suggestedPrompts;
  final bool isLoadingSuggestions;
  final Function(DisplayItem) onPromptSelected;
  final Function(String) onKeywordSelected;

  const SourceInputSection({
    super.key,
    required this.initialText,
    required this.sources,
    required this.onTextChanged,
    required this.onSourceDeleted,
    required this.onSourceAdded,
    this.isProcessing = false,
    required this.suggestedPrompts,
    required this.isLoadingSuggestions,
    required this.onPromptSelected,
    required this.onKeywordSelected,
  });

  @override
  ConsumerState<SourceInputSection> createState() => SourceInputSectionState();
}

class SourceInputSectionState extends ConsumerState<SourceInputSection> {
  static const String _logName = 'SourceInputSection';

  Timer? _analysisDebounce;
  bool _isAddingHashtag = false;
  late SourceInputViewModel _viewModel;

  // Store a reference to the attachment registry service
  late final AttachmentRegistryService attachmentRegistry;

  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  final ClipboardHandler _clipboardHandler = ClipboardHandler();

  @override
  void initState() {
    super.initState();
    _controller.text = widget.initialText;
    _clipboardHandler.startClipboardCheck(() => setState(() {}));

    // Store the attachment registry reference during initialization
    attachmentRegistry = ref.read(attachmentRegistryServiceProvider);

    // Set up a listener for attachment registry updates
    _setupAttachmentRegistryListener();
  }

  // Set up a listener for the attachment registry
  void _setupAttachmentRegistryListener() {
    appLog.debug('Setting up attachment registry listener in SourceInputSection', name: _logName);
    // Use the stored reference to attachment registry
    attachmentRegistry.addListener(_onAttachmentRegistryUpdated);
  }

  // Callback for attachment registry updates
  void _onAttachmentRegistryUpdated() {
    appLog.debug('Attachment registry updated in SourceInputSection, forcing rebuild',
        name: _logName);
    if (mounted) {
      setState(() {});
    }
  }

  @override
  void didUpdateWidget(SourceInputSection oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Update controller text if initialText changed
    if (widget.initialText != oldWidget.initialText && widget.initialText != _controller.text) {
      _controller.text = widget.initialText;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    _analysisDebounce?.cancel();
    _clipboardHandler.stopClipboardCheck();

    // Clean up the attachment registry listener using the stored reference
    try {
      // Use the instance variable we stored during initialization
      attachmentRegistry.removeListener(_onAttachmentRegistryUpdated);
      appLog.debug('Removed attachment registry listener in SourceInputSection', name: _logName);
    } catch (e) {
      // Catch and log any errors during disposal but don't throw
      appLog.debug('Error cleaning up attachment registry listener: $e', name: _logName);
    }

    super.dispose();
  }

  // Method to set the hashtag mode from outside this class
  void setHashtagMode(WidgetRef ref, bool isAdding) {
    final state = sourceInputSectionKey.currentState;
    if (state != null) {
      state._isAddingHashtag = isAdding;
    }
  }

  void _onTextChanged(String text, WidgetRef ref) {
    // Notify parent of text change
    widget.onTextChanged(text);

    // Log the text change
    appLog.debug('Text changed: ${text.length} characters', name: _logName);

    // Set up debounce timer for analysis
    _analysisDebounce?.cancel();
    _analysisDebounce = TextAnalysisHandler.setupAnalysisDebounce(
      text: text,
      homeViewModel: ref.read(homeViewModelProvider),
      isAddingHashtag: _isAddingHashtag,
      existingTimer: _analysisDebounce,
    );

    // If text is empty, clear suggestions immediately
    if (text.isEmpty) {
      appLog.debug('Text is empty, clearing suggestions immediately', name: _logName);
      _viewModel.clearSuggestions();
    }
    _viewModel.getAutoCompleteSuggestions(ref, text);
  }

  void addHashtag(String hashtag) {
    appLog.info('Adding hashtag: $hashtag', name: _logName);
    final homeViewModel = ref.read(homeViewModelProvider);
    HashtagHandler.addHashtag(
      hashtag: hashtag,
      controller: _controller,
      homeViewModel: homeViewModel,
      viewModel: _viewModel,
      focusNode: _focusNode,
      updateUI: () => setState(() {}),
    );
  }

  void _triggerAnalysis() {
    appLog.info('Triggering analysis', name: _logName);
    final homeViewModel = ref.read(homeViewModelProvider);
    TextAnalysisHandler.triggerAnalysis(
      homeViewModel: homeViewModel,
      focusNode: _focusNode,
      controller: _controller,
      onTextChanged: widget.onTextChanged,
    );
  }

  void _onSuggestionSelected(DisplayItem suggestion, WidgetRef ref) {
    final homeViewModel = ref.read(homeViewModelProvider);
    SuggestionHandler.handleSuggestionSelected(
      suggestion: suggestion,
      controller: _controller,
      homeViewModel: homeViewModel,
      viewModel: _viewModel,
      focusNode: _focusNode,
      updateUI: () => setState(() {}),
      ref: ref,
    );
  }

  FocusNode get focusNode => _focusNode;

  void triggerAnalysis() {
    _triggerAnalysis();
  }

  /// Handle when variables are changed
  void _handleVariablesChanged() {
    appLog.debug('Handling variable changes', name: _logName);

    // Read the provider and handle AsyncValue state
    final clientContextAsync = ref.read(clientContextServiceProvider);
    clientContextAsync.whenData((service) {
      final variableManager = service.variableManager;
      // Check if the prompt has variables
      if (variableManager.hasVariables) {
        // Update the input text with resolved text
        final resolvedText = variableManager.resolvedText;
        _controller.text = resolvedText;
        widget.onTextChanged(resolvedText);
        appLog.debug('Updated text field with resolved variables: $resolvedText', name: _logName);
      }
    });

    // Log if service isn't ready
    if (clientContextAsync.isLoading) {
      appLog.warning('Cannot handle variable change: ClientContextService loading.',
          name: _logName);
    } else if (clientContextAsync.hasError) {
      appLog.error('Cannot handle variable change: ClientContextService error.',
          name: _logName, error: clientContextAsync.error);
    }
  }

  @override
  Widget build(BuildContext context) {
    // Use watch instead of read to rebuild when homeViewModel changes
    final homeViewModel = ref.watch(homeViewModelProvider);
    _viewModel = ref.watch(sourceInputViewModelProvider);

    // Log viewModel state
    appLog.debug(
        'ViewModel loaded: ${_viewModel.prompts.length} prompts, '
        '${_viewModel.suggestions.length} suggestions, '
        '${homeViewModel.popularPrompts.length} popular prompts',
        name: _logName);

    if (_viewModel.errorMessage != null) {
      appLog.error('Error in ViewModel: ${_viewModel.errorMessage}', name: _logName);
      // Don't show the error directly to the user, just log it
      // If we have no data at all, we'll try to reload in updateSuggestions
    }

    // Watch the client context provider to react to its state changes
    final clientContextAsync = ref.watch(clientContextServiceProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ContentBox.wrapChildren(
          [
            Text(
              Strings.sourceInputSectionTitle,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Theme.of(context).colorScheme.primary,
                  ),
            ),
            const SizedBox(height: 12),

            // Use a custom built autocomplete field.
            Consumer(
              builder: (context, ref, child) => AutoCompleteField(
                controller: _controller,
                focusNode: _focusNode,
                onTextChanged: (text) => _onTextChanged(text, ref),
                viewModel: _viewModel,
                homeViewModel: homeViewModel,
                onSuggestionSelected: _onSuggestionSelected,
                onPromptSelected: widget.onPromptSelected,
                onSubmitted: _triggerAnalysis,
              ),
            ),

            // Variables section will handle its own visibility based on whether there are variable values
            VariablesSection(
              onVariablesChanged: _handleVariablesChanged,
              onPromptSelected: widget.onPromptSelected,
            ),

            // Source list
            if (widget.sources.isNotEmpty) ...[
              const SizedBox(height: 12),
              SourceList(
                sources: widget.sources,
                onSourceDeleted: widget.onSourceDeleted,
                isSourceLoading: homeViewModel.sourceLoadingStates,
              ),
            ],
          ],
        ),

        // Use clientContextAsync.when to build dependent widgets only when data is ready
        clientContextAsync.when(
          data: (clientContextService) {
            // Build widgets that depend on the clientContextService
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Suggested prompts section
                if (widget.suggestedPrompts.isNotEmpty) ...[
                  const SizedBox(height: 12),
                  ContentBox.wrapChildren(
                    [
                      PromptList(
                        title: 'Suggested Prompts',
                        suggestedItems: widget.suggestedPrompts,
                        isLoading: widget.isLoadingSuggestions,
                        onPromptSelected: widget.onPromptSelected,
                        onKeywordSelected: widget.onKeywordSelected,
                        contextService: clientContextService, // Pass the resolved service
                      ),
                    ],
                  ),
                ],

                // Popular prompts section
                if (homeViewModel.popularPrompts.isNotEmpty) ...[
                  const SizedBox(height: 12),
                  ContentBox.wrapChildren(
                    [
                      PromptList(
                        title: 'Popular Prompts',
                        suggestedItems: homeViewModel.popularPrompts,
                        isLoading: homeViewModel.isLoadingPopularPrompts,
                        onPromptSelected: widget.onPromptSelected,
                        onKeywordSelected: widget.onKeywordSelected,
                        contextService: clientContextService, // Pass the resolved service
                      ),
                    ],
                  ),
                ],
              ],
            );
          },
          loading: () => const Center(child: CircularProgressIndicator()), // Show loading indicator
          error: (error, stack) {
            // Show error message
            appLog.error('Error loading ClientContextService for PromptLists',
                name: _logName, error: error, stackTrace: stack);
            return Center(child: Text('Error loading prompts: $error'));
          },
        ),
      ],
    );
  }
}
