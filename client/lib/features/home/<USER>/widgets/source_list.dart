import 'package:flutter/material.dart';
import 'package:promz/features/input_selection/models/input_source.dart';
import 'package:promz/features/input_selection/widgets/source_card_factory.dart';
import 'package:promz_common/promz_common.dart';
import 'package:url_launcher/url_launcher.dart'; // Import url_launcher

/// Widget that displays a list of input sources
class SourceList extends StatelessWidget {
  static const _logName = 'SourceList';

  final List<InputSource> sources;
  final Map<String, bool> isSourceLoading; // Track loading state by source ID
  final Function(InputSource) onSourceDeleted;

  const SourceList({
    Key? key,
    required this.sources,
    required this.onSourceDeleted,
    this.isSourceLoading = const {},
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (sources.isEmpty) {
      return const SizedBox.shrink();
    }

    // Create a key based on the sources to force rebuild when they change
    final sourcesKey = sources.map((s) => s.processingResult?.status.toString() ?? '').join('-');
    appLog.debug('Building SourceList with key: $sourcesKey', name: _logName);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min, // Use minimum vertical space
      children: [
        Text(
          Strings.sourcesTitle,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
                color: Theme.of(context).colorScheme.secondary,
              ),
        ),
        const SizedBox(height: 8),
        // Replace ListView.builder with a more efficient solution
        ...sources.asMap().entries.map((entry) {
          final index = entry.key;
          final source = entry.value;
          final sourceId = source.filePath ?? source.contentHash ?? index.toString();
          final isLoading = isSourceLoading[sourceId] ?? false;
          final String? url = _getUrlFromSource(source);

          if (isLoading) {
            appLog.info('Showing loading skeleton for source: ${source.fileName}', name: _logName);
            return SourceCardFactory.createSkeletonCard(source);
          }

          // Create a unique key based on the source ID and metadata version
          final metadataVersion = source.processingResult?.status.toString() ?? '';
          final messageCount = source.processingResult?.hasWhatsappMetadata() == true
              ? source.processingResult!.whatsappMetadata.messageCount.toString()
              : '';
          final participantCount = source.processingResult?.hasWhatsappMetadata() == true
              ? source.processingResult!.whatsappMetadata.participantCount.toString()
              : '';

          // Create a key that changes when the metadata changes
          final keyString = '$sourceId-$metadataVersion-$messageCount-$participantCount';
          appLog.debug('Creating source card with key: $keyString', name: _logName);

          return SourceCardFactory.createCardForSource(
            source,
            onTap: url != null ? () => _launchUrl(url, context) : null,
            onDelete: () => onSourceDeleted(source),
            context: context,
            index: index,
            key: ValueKey(keyString), // Add a key that changes when the source metadata changes
          );
        }).toList(),
      ],
    );
  }

  /// Extracts the URL from the InputSource processingResult, prioritizing finalUrl
  String? _getUrlFromSource(InputSource source) {
    appLog.info('Getting URL for source: ${source.fileName}', name: _logName);
    if (source.type == InputSourceType.news && source.processingResult != null) {
      // Check for sourceUrl in the processingResult
      if (source.processingResult!.sourceUrl.isNotEmpty) {
        return source.processingResult!.sourceUrl;
      }
      // Check for URL in article metadata if available
      if (source.processingResult!.hasArticleMetadata() &&
          source.processingResult!.articleMetadata.url.isNotEmpty) {
        return source.processingResult!.articleMetadata.url;
      }
    }
    return null;
  }

  /// Launches the given URL
  Future<void> _launchUrl(String urlString, BuildContext context) async {
    appLog.info('Launching URL: $urlString', name: _logName);
    final Uri? uri = Uri.tryParse(urlString);
    if (uri != null && await canLaunchUrl(uri)) {
      try {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } catch (e) {
        appLog.error('Could not launch URL $urlString: $e', name: _logName, error: e);
        // Optionally show a snackbar or dialog to the user
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Could not open link: $urlString')),
          );
        }
      }
    } else {
      appLog.warning('Cannot launch URL: $urlString', name: _logName, error: 'Invalid URL');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Cannot open link: $urlString')),
        );
      }
    }
  }
}
