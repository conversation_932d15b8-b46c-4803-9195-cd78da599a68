import 'package:flutter/material.dart';
import 'package:promz/core/services/clipboard_service.dart';
import 'package:promz_common/promz_common.dart';

/// Class responsible for handling clipboard operations
class ClipboardHandler {
  static const String _logName = 'ClipboardHandler';
  final ClipboardService _clipboardService = ClipboardService();
  bool _hasClipboardContent = false;
  VoidCallback? _updateUICallback;

  /// Registers the callback for UI updates but doesn't start polling
  /// This preserves the same API signature while removing the automatic polling
  void startClipboardCheck(VoidCallback updateUI) {
    _updateUICallback = updateUI;
    appLog.debug('Clipboard handler initialized without automatic checking', name: _logName);
  }

  /// Clears the UI callback - maintains API for backward compatibility
  void stopClipboardCheck() {
    _updateUICallback = null;
    appLog.debug('Clipboard handler stopped', name: _logName);
  }

  /// Explicitly check clipboard content - should be called only on user action
  /// Returns true if the content state has changed
  Future<bool> checkClipboardContent() async {
    try {
      final hasContent = await _clipboardService.hasClipboardText();
      final hasChanged = hasContent != _hasClipboardContent;

      if (hasChanged) {
        _hasClipboardContent = hasContent;
        if (_updateUICallback != null) {
          _updateUICallback!();
        }
      }

      return hasChanged;
    } catch (e) {
      appLog.error('Error checking clipboard: $e', name: _logName);
      return false;
    }
  }

  /// Returns whether the clipboard has content
  bool get hasClipboardContent => _hasClipboardContent;
}
