import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:promz/features/home/<USER>/widgets/source_list.dart';
import 'package:promz/features/input_selection/models/input_source.dart';
import 'package:promz_common/promz_common.dart';

/// Mode for the prompt dialog
enum PromptDialogMode {
  /// Show execution confirmation with sources
  execution,

  /// Show prompt details with category and share option
  details
}

/// A versatile dialog for prompt execution confirmation or prompt details.
class PromptExecutionDialog extends ConsumerWidget {
  /// The display item containing the prompt to execute.
  final DisplayItem displayItem;

  /// The list of sources that will be included in the prompt execution.
  final List<InputSource> sources;

  /// How the dialog should be displayed
  final PromptDialogMode mode;

  const PromptExecutionDialog({
    Key? key,
    required this.displayItem,
    this.sources = const [],
    this.mode = PromptDialogMode.execution,
  }) : super(key: key);

  static const _logName = 'PromptExecutionDialog';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    // Get category information from the display item
    final hasCategory =
        displayItem.prompt?.categoryName != null && displayItem.prompt!.categoryName!.isNotEmpty;
    final categoryName = displayItem.prompt?.categoryName;
    final IconData categoryIcon = PromzDialog.getCategoryIcon(categoryName);

    // Create the dialog content
    final content = Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Prompt subtitle
        if (displayItem.subtitle != null && displayItem.subtitle!.isNotEmpty) ...[
          Text(
            displayItem.subtitle!,
            style: theme.textTheme.bodyMedium,
          ),
          const SizedBox(height: 12),
        ],

        // Category chip
        if (hasCategory) ...[
          Wrap(
            children: [
              Chip(
                label: Text(categoryName!),
                backgroundColor: theme.colorScheme.primaryContainer,
                labelStyle: TextStyle(
                  color: theme.colorScheme.onPrimaryContainer,
                  fontSize: 12,
                ),
                visualDensity: VisualDensity.compact,
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
            ],
          ),
          const SizedBox(height: 16),
        ],

        // Execution confirmation text (only in execution mode)
        if (mode == PromptDialogMode.execution) ...[
          const Text(Strings.areYouSureExecutePrompt),
          const SizedBox(height: 16),
        ],

        // Source list (only in execution mode with sources)
        if (mode == PromptDialogMode.execution && sources.isNotEmpty) ...[
          const Divider(),
          Padding(
            padding: const EdgeInsets.only(top: 8, bottom: 4),
            child: Row(
              children: [
                const Icon(Icons.source, size: 16),
                const SizedBox(width: 8),
                Text(
                  'Sources',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
          SourceList(
            sources: sources,
            onSourceDeleted: (_) {}, // Read-only in confirmation dialog
          ),
        ],
      ],
    );

    // Create the action buttons
    final actions = <Widget>[
      TextButton(
        onPressed: () {
          Navigator.of(context).pop(false);
        },
        child: const Text('Cancel'),
      ),
      FilledButton(
        onPressed: () => Navigator.of(context).pop(true),
        child: const Text('Execute'),
      ),
    ];

    // Use the reusable PromzDialog for consistent styling
    return PromzDialog(
      title: displayItem.displayText,
      headerIcon: categoryIcon,
      content: content,
      actions: actions,
      onClose: () => Navigator.of(context).pop(false),
    );
  }

  /// Shows a dialog asking for confirmation to execute a prompt.
  /// Returns true if user confirms, false otherwise.
  static Future<bool> show(
    BuildContext context,
    DisplayItem displayItem,
    List<InputSource> sources, {
    PromptDialogMode mode = PromptDialogMode.execution,
  }) async {
    appLog.debug(
      'Showing prompt dialog for: ${displayItem.displayText} in ${mode.name} mode',
      name: _logName,
    );

    return await showDialog<bool>(
          context: context,
          builder: (context) => PromptExecutionDialog(
            displayItem: displayItem,
            sources: sources,
            mode: mode,
          ),
        ) ??
        false;
  }
}
