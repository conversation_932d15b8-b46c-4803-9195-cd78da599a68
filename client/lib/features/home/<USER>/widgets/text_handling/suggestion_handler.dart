import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:promz/features/home/<USER>/home_viewmodel.dart';
import 'package:promz/features/home/<USER>/source_input_viewmodel.dart';
import 'package:promz_common/promz_common.dart';

/// SuggestionHandler: Manages suggestion selection and text replacement in input fields
///
/// This utility class provides methods for handling user selection of suggestions
/// and updating the input field text accordingly. It supports different types of
/// suggestions (prompts, entities, etc.) and handles text replacement strategies
/// based on the suggestion type and context.
class SuggestionHandler {
  static const _logName = 'SuggestionHandler';

  /// Handles the selection of a suggestion item
  ///
  /// This method processes the selected suggestion and updates the input field text accordingly.
  /// It determines the appropriate replacement strategy based on the suggestion type and context,
  /// and transforms any template variables into their actual values before insertion.
  static void handleSuggestionSelected({
    required DisplayItem suggestion,
    required TextEditingController controller,
    required HomeViewModel homeViewModel,
    required SourceInputViewModel viewModel,
    required FocusNode focusNode,
    required VoidCallback updateUI,
    required WidgetRef ref,
  }) {
    appLog.debug(
        'START: Suggestion selected: "${suggestion.text}" (type: ${suggestion.type}, '
        'displayText: "${suggestion.displayText}")',
        name: _logName);

    // Get current text and selection
    final currentText = controller.text;
    final selection = controller.selection;

    // Get the transformed text from the ViewModel
    final replacementText = viewModel.handleSuggestionSelection(suggestion, currentText);

    // Determine how to handle replacement based on suggestion type
    String newText;

    if (_shouldReplaceWholeText(suggestion, currentText)) {
      // Replace entire text
      newText = replacementText;
      controller.value = TextEditingValue(
        text: newText,
        selection: TextSelection.collapsed(offset: newText.length),
      );
      appLog.debug('Replaced whole text with: "$newText"', name: _logName);
    } else {
      // Replace current word only
      final (wordStart, wordEnd) = _findWordBoundaries(currentText, selection);
      if (wordStart < wordEnd) {
        final wordBeingReplaced = currentText.substring(wordStart, wordEnd);
        newText = currentText.replaceRange(wordStart, wordEnd, replacementText);
        appLog.debug(
            'Replaced word "$wordBeingReplaced" at positions $wordStart-$wordEnd with: "$replacementText"',
            name: _logName);
        controller.value = TextEditingValue(
          text: newText,
          selection: TextSelection.collapsed(offset: wordStart + replacementText.length),
        );
      } else {
        // Fallback: append to end if no valid word boundaries
        newText = currentText + (currentText.isEmpty ? '' : ' ') + replacementText;
        appLog.debug('Appended text because no valid word boundaries: "$newText"', name: _logName);
        controller.value = TextEditingValue(
          text: newText,
          selection: TextSelection.collapsed(offset: newText.length),
        );
      }
    }

    // Update view model with new text
    homeViewModel.updateInputText(newText);

    // Trigger analysis if needed
    if (_shouldReplaceWholeText(suggestion, currentText)) {
      Future.microtask(() {
        if (newText.isNotEmpty) {
          homeViewModel.analyzeText();
        }
      });
    }

    // Unfocus to dismiss the autocomplete dropdown
    focusNode.unfocus();

    // Force UI update
    updateUI();
  }

  /// Determine if we should replace the whole text based on suggestion type and context
  static bool _shouldReplaceWholeText(DisplayItem suggestion, String currentText) {
    // Always replace entire text with prompt titles and keyword matches
    if (suggestion.type == DisplayItemType.promptTitle ||
        suggestion.type == DisplayItemType.keywordMatch) {
      return true;
    }

    // For company names and stock symbols, always replace current word only
    if (suggestion.type == DisplayItemType.companyName ||
        suggestion.type == DisplayItemType.stockSymbol) {
      return false;
    }

    // For other types, replace whole text if multi-word suggestion
    final isMultiWord = suggestion.text.trim().contains(' ');

    // Check if text is too long for whole replacement (more than 10 words)
    final textIsTooLong = currentText.split(' ').length > 10;

    // Replace whole text if multi-word and text isn't too long
    return isMultiWord && !textIsTooLong;
  }

  /// Find the boundaries of the current word at the cursor position
  static (int, int) _findWordBoundaries(String text, TextSelection selection) {
    if (text.isEmpty) {
      return (0, 0);
    }

    // Get cursor position
    final cursorPos = selection.baseOffset;
    if (cursorPos < 0 || cursorPos > text.length) {
      return (0, text.length);
    }

    // Find word start
    int wordStart = cursorPos;
    while (wordStart > 0 && text[wordStart - 1] != ' ' && text[wordStart - 1] != '\n') {
      wordStart--;
    }

    // Find word end
    int wordEnd = cursorPos;
    while (wordEnd < text.length && text[wordEnd] != ' ' && text[wordEnd] != '\n') {
      wordEnd++;
    }

    return (wordStart, wordEnd);
  }
}
