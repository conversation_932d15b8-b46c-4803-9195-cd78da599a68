import 'package:flutter/material.dart';
import 'package:promz_common/promz_common.dart';
import 'package:promz/features/home/<USER>/home_viewmodel.dart';
import 'package:promz/features/home/<USER>/source_input_viewmodel.dart';

/// Class responsible for handling hashtag operations
class HashtagHandler {
  static const String _logName = 'HashtagHandler';

  /// Adds a hashtag to the text, either by replacing an existing word or appending
  static void addHashtag({
    required String hashtag,
    required TextEditingController controller,
    required HomeViewModel homeViewModel,
    required SourceInputViewModel viewModel,
    required FocusNode focusNode,
    required VoidCallback updateUI,
  }) {
    // Remove the # if it exists at the beginning
    final tag = hashtag.startsWith('#') ? hashtag.substring(1) : hashtag;
    final hashtagWithHash = '#$tag';

    // Get current text and check if the hashtag already exists
    final currentText = controller.text;

    // Check if the hashtag already exists in the text
    if (currentText.contains(hashtagWithHash)) {
      appLog.debug('Hashtag $hashtagWithHash already exists in the text', name: _logName);
      return; // Don't add duplicate hashtags
    }

    // Check if the word without hashtag exists in the text
    final regex = RegExp(r'\b' + RegExp.escape(tag) + r'\b', caseSensitive: false);
    if (regex.hasMatch(currentText)) {
      // Replace the word with the hashtag version
      final newText = currentText.replaceAll(regex, hashtagWithHash);

      // Update the text controller
      controller.text = newText;
      controller.selection = TextSelection.collapsed(offset: newText.length);

      // Update the text in the view model
      homeViewModel.updateInputText(newText);

      appLog.debug('Replaced word $tag with hashtag $hashtagWithHash', name: _logName);
    } else {
      // Append hashtag to input with a preceding space if needed
      final suffix = currentText.endsWith(' ') ? hashtagWithHash : ' $hashtagWithHash';
      final newText = currentText + suffix;

      // Update the text controller
      controller.text = newText;
      controller.selection = TextSelection.collapsed(offset: newText.length);

      // Update the text in the view model
      homeViewModel.updateInputText(newText);

      appLog.debug('Added hashtag $hashtagWithHash to text', name: _logName);
    }

    // Clear suggestions to close the dropdown
    viewModel.clearSuggestions();

    // Unfocus to dismiss the autocomplete dropdown
    if (focusNode.hasFocus) {
      focusNode.unfocus();
    }

    // Update UI
    updateUI();
  }
}
