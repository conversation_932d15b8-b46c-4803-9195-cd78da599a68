import 'dart:async';
import 'package:flutter/material.dart';
import 'package:promz_common/promz_common.dart';
import 'package:promz/features/home/<USER>/home_viewmodel.dart';

/// Class responsible for handling text analysis operations
class TextAnalysisHandler {
  static const String _logName = 'TextAnalysisHandler';

  /// Triggers text analysis in the HomeViewModel
  static void triggerAnalysis({
    required HomeViewModel homeViewModel,
    required FocusNode focusNode,
    required TextEditingController controller,
    required Function(String) onTextChanged,
  }) {
    // Notify parent of text change
    onTextChanged(controller.text);

    // Log that analysis was triggered
    appLog.debug('Triggered analysis', name: _logName);

    // Remove focus from the text field to dismiss the autocomplete
    // Only unfocus if we currently have focus to avoid unnecessary UI changes
    if (focusNode.hasFocus) {
      focusNode.unfocus();
    }

    // Trigger analysis with extra debug logging
    appLog.debug('Calling homeViewModel.analyzeText() directly', name: _logName);
    homeViewModel.analyzeText();
  }

  /// Sets up a debounce timer for text analysis
  static Timer? setupAnalysisDebounce({
    required String text,
    required HomeViewModel homeViewModel,
    required bool isAddingHashtag,
    required Timer? existingTimer,
  }) {
    // Cancel any existing debounce timer
    existingTimer?.cancel();

    // Don't analyze if text is too short
    if (text.trim().length <= 3) {
      return null;
    }

    // Set debounce time based on whether we're adding a hashtag
    final debounceTime = isAddingHashtag
        ? const Duration(milliseconds: 1000) // Longer debounce for hashtags
        : const Duration(milliseconds: 500); // Increased debounce for regular text

    // Log the debounce time
    appLog.debug(
        'Setting analysis debounce: ${debounceTime.inMilliseconds}ms, hashtag mode: $isAddingHashtag',
        name: _logName);

    // Set a new debounce timer
    return Timer(debounceTime, () {
      // Only trigger analysis if we're not already analyzing
      if (!homeViewModel.isAnalyzing) {
        appLog.debug('Debounce timer expired, updating input text in view model', name: _logName);

        // Update the text in the view model which will trigger analysis
        appLog.debug('Updating input text in view model with: $text', name: _logName);
        homeViewModel.updateInputText(text);

        // Force analysis even if the text hasn't changed
        if (homeViewModel.inputText == text) {
          appLog.debug('Text unchanged, forcing analysis', name: _logName);
          homeViewModel.analyzeText();
        }
      } else {
        appLog.debug('Skipping analysis trigger - already analyzing', name: _logName);
      }
    });
  }
}
