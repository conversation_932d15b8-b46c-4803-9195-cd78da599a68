import 'package:flutter/material.dart';

class IntentAnalysisSection extends StatelessWidget {
  final Function(String) onTextSubmitted;
  final bool isAnalyzing;
  final List<String> extractedKeywords;

  const IntentAnalysisSection({
    super.key,
    required this.onTextSubmitted,
    required this.isAnalyzing,
    required this.extractedKeywords,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextField(
              maxLines: 3,
              decoration: const InputDecoration(
                hintText: 'Enter your text here...',
                border: OutlineInputBorder(),
              ),
              onSubmitted: onTextSubmitted,
            ),
            if (isAnalyzing)
              const Padding(
                padding: EdgeInsets.all(8.0),
                child: CircularProgressIndicator(),
              )
            else if (extractedKeywords.isNotEmpty)
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Wrap(
                    spacing: 8.0,
                    runSpacing: 4.0,
                    children:
                        extractedKeywords.map((keyword) => Chip(label: Text(keyword))).toList(),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
