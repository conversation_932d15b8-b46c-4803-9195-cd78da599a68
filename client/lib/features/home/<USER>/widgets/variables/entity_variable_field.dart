import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:promz/core/providers/service_providers.dart';
import 'package:promz/features/home/<USER>/variable.dart';
import 'package:promz/features/home/<USER>/widgets/variables/news_article_input.dart';
import 'package:promz/features/home/<USER>/widgets/variables/stock_symbol_autocomplete.dart';
import 'package:promz/features/youtube/widgets/youtube_video_input.dart';
import 'package:promz_common/promz_common.dart';

/// Field for editing entity-based variables (like stocks, locations)
class EntityVariableField extends ConsumerWidget {
  static const _logName = 'EntityVariableField';
  final Variable variable;
  final String? initialValue;
  final void Function(Variable) onChanged;

  const EntityVariableField({
    Key? key,
    required this.variable,
    this.initialValue,
    required this.onChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final entityType = variable.entityType;

    // WATCH the provider instead of reading
    final clientContextAsync = ref.watch(clientContextServiceProvider);

    // Use .when to handle the state of the AsyncValue
    return clientContextAsync.when(
      data: (service) {
        // 'service' is the resolved ClientContextService
        // --- Existing logic starts here, using 'service' ---
        if (entityType == EntityType.news) {
          // Get newsArticleService from the RESOLVED service
          final newsArticleService = service.newsArticle; // Use 'service'

          // Check if this is a news article URL variable and if there's a corresponding contents variable
          Variable? contentsVariable;

          // If this is a news article URL variable, look for a corresponding contents variable
          if (variable.name.toLowerCase().contains('url')) {
            // Construct the expected name for the contents variable
            final contentsVarName = variable.name.toLowerCase().replaceAll('url', 'contents');
            appLog.debug('Looking for corresponding contents variable: $contentsVarName',
                name: _logName);

            // Use the RESOLVED service to get the value
            final currentContentsValue =
                service.getVariableValue(contentsVarName) ?? ''; // Use 'service'

            // Create a Variable object for the contents
            contentsVariable = Variable(
              name: contentsVarName,
              displayName: variable.displayName.replaceAll('URL', 'Contents'),
              type: VariableType.customVariable,
              entityType: EntityType.news,
              originalDisplay: contentsVarName,
              fullTemplate: '{{$contentsVarName}}',
              value: currentContentsValue, // Use existing value if available
            );
          }

          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(
                      Icons.article,
                      size: 18,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      variable.prefixedDisplayName,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                NewsArticleInput(
                  initialValue: initialValue ?? '',
                  newsService: newsArticleService, // Pass the sub-service
                  variable: variable,
                  contentsVariable: contentsVariable,
                  onSelected: (updatedVariable) {
                    onChanged(updatedVariable);
                  },
                ),
              ],
            ),
          );
        } else if (entityType == EntityType.finance) {
          // Get entityDetectionService from the RESOLVED service
          final entityDetectionService = service.entityDetection; // Use 'service'

          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(
                      Icons.trending_up,
                      size: 18,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      variable.prefixedDisplayName,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                StockSymbolAutocomplete(
                  initialValue: initialValue ?? '',
                  entityService: entityDetectionService, // Pass the sub-service
                  // Pass the variable to be updated
                  variable: variable,
                  // Now receives an updated Variable directly
                  onSelected: (updatedVariable) {
                    onChanged(updatedVariable);
                  },
                ),
              ],
            ),
          );
        } else if (entityType == EntityType.youtubeVideo) {
          // Get youtubeService from the RESOLVED service
          final youtubeService = service.youtube; // Use 'service'

          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(
                      Icons.video_library,
                      size: 18,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      variable.prefixedDisplayName,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                YouTubeVideoInput(
                  initialValue: initialValue ?? '',
                  youtubeService: youtubeService, // Pass the sub-service
                  variable: variable,
                  onSelected: (updatedVariable) {
                    onChanged(updatedVariable);
                  },
                ),
              ],
            ),
          );
        } else {
          // Default field for other entity types
          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  variable.prefixedDisplayName,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const SizedBox(height: 8),
                TextFormField(
                  initialValue: initialValue,
                  decoration: InputDecoration(
                    hintText: 'Enter ${variable.displayName}',
                    border: const OutlineInputBorder(),
                  ),
                  onChanged: (value) {
                    appLog.debug('Entity variable value changed: $value', name: _logName);
                    if (value.isNotEmpty && entityType != null) {
                      onChanged(variable.copyWithValue(value));
                    }
                  },
                ),
              ],
            ),
          );
        }
        // --- End of existing logic ---
      },
      loading: () => const Center(child: CircularProgressIndicator()), // Loading state
      error: (error, stackTrace) {
        // Error state
        appLog.error('Error loading ClientContextService in EntityVariableField',
            name: _logName, error: error, stackTrace: stackTrace);
        return Center(child: Text('Error loading variable field: $error'));
      },
    );
  }
}
