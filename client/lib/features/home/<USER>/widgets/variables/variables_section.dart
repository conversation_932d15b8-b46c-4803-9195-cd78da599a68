import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:promz/core/providers/service_providers.dart';
import 'package:promz/core/services/client_context_service.dart';
import 'package:promz/features/home/<USER>/variable.dart';
import 'package:promz/features/home/<USER>/widgets/variables/variables_dialog.dart';
import 'package:promz_common/promz_common.dart';

/// Widget that displays read-only information about prompt variables in a compact chip format
class VariablesSection extends ConsumerWidget {
  static const _logName = 'VariablesSection';

  final VoidCallback? onVariablesChanged;
  final Function(DisplayItem) onPromptSelected;

  const VariablesSection({
    Key? key,
    this.onVariablesChanged,
    required this.onPromptSelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Use watch instead of read to rebuild when clientContextService changes
    final clientContextAsync = ref.watch(clientContextServiceProvider);

    return clientContextAsync.when(
      data: (service) {
        final variableManager = service.variableManager;
        final variables = variableManager.variables;
        final variableValues = variableManager.variableValues;

        // Filter out input source variables
        final filteredVariables =
            variables.where((v) => !EntityUtils.isInputSourceVariable(v.name)).toList();

        // Only show the section if we have non-input source variables with values
        final variablesWithValues = filteredVariables
            .where((v) =>
                variableValues.containsKey(v.name) && variableValues[v.name]?.isNotEmpty == true)
            .toList();

        // Hide the section if we have no non-input source variables with values
        if (variablesWithValues.isEmpty) {
          return const SizedBox.shrink();
        }

        appLog.debug('Building variables section with ${variablesWithValues.length} variables',
            name: _logName);

        return Padding(
          padding: const EdgeInsets.only(top: 4, bottom: 8),
          child: Stack(
            children: [
              // Scrollable area for variable chips with padding for the edit button
              Padding(
                padding: const EdgeInsets.only(right: 40),
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      // We no longer show attachment chips since they're input source variables
                      // Show regular variable chips
                      ...variablesWithValues
                          .map((variable) => _buildVariableChip(
                                context,
                                variable,
                                variableValues[variable.name] ?? '',
                                service,
                              ))
                          .toList(),
                    ],
                  ),
                ),
              ),

              // Overlay edit button with semi-transparent background
              Positioned(
                right: 0,
                top: 0,
                bottom: 0,
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.centerRight,
                      end: Alignment.centerLeft,
                      colors: [
                        Theme.of(context).colorScheme.surface,
                        Theme.of(context).colorScheme.surface.withAlpha(0),
                      ],
                      stops: const [0.7, 1.0],
                    ),
                  ),
                  child: Center(
                    child: IconButton(
                      visualDensity: VisualDensity.compact,
                      icon: Icon(
                        Icons.edit_outlined,
                        size: 16,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      tooltip: 'Edit variables',
                      onPressed: () {
                        _showVariablesDialog(context, service);
                      },
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
      loading: () => const Center(
        child: Padding(
          padding: EdgeInsets.all(8.0),
          child: SizedBox(width: 16, height: 16, child: CircularProgressIndicator(strokeWidth: 2)),
        ),
      ),
      error: (err, stack) {
        appLog.error('Error loading client context in VariablesSection',
            name: _logName, error: err, stackTrace: stack);
        // Optionally return a more user-friendly error widget
        return const SizedBox.shrink(); // Or a small error indicator
      },
    );
  }

  /// Build a chip showing variable name and value with a clear button
  Widget _buildVariableChip(
    BuildContext context,
    Variable variable,
    String value,
    ClientContextService clientContextService,
  ) {
    final theme = Theme.of(context);

    // Format the display value based on variable type
    String displayValue = value;

    // Special formatting for finance variables
    if (variable.entityType == EntityType.finance) {
      final formattedValue = clientContextService.entityDetection.getDisplayTextForFinance(value);
      if (formattedValue != null) {
        displayValue = formattedValue;
      }
    }

    return Padding(
      padding: const EdgeInsets.only(right: 4),
      child: Tooltip(
        message: '${variable.displayName}: $displayValue',
        child: Chip(
          visualDensity: VisualDensity.compact,
          label: ConstrainedBox(
            constraints: const BoxConstraints(maxWidth: 300),
            child: Text(
              '${variable.displayName}: $displayValue',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ),
          backgroundColor: theme.colorScheme.surfaceContainerLow,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
            side: BorderSide(color: theme.colorScheme.outlineVariant, width: 0.5),
          ),
          deleteIcon: Icon(
            Icons.clear,
            size: 14,
            color: theme.colorScheme.onSurfaceVariant,
          ),
          onDeleted: () {
            appLog.debug('Clearing variable ${variable.name} from chip', name: _logName);
            // Clear the variable value
            clientContextService.clearVariableValue(variable.name);
            // Notify listeners that variables have changed
            onVariablesChanged?.call();
          },
        ),
      ),
    );
  }

  /// Show the variables dialog for editing
  void _showVariablesDialog(BuildContext context, ClientContextService clientContextService) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (context.mounted) {
        VariablesDialog.show(
          context,
          clientContextService, // Pass the service itself
          clientContextService.variableManager, // Pass the manager
          onVariablesChanged: onVariablesChanged,
          onProceed: onVariablesChanged,
        );
      }
    });
  }
}
