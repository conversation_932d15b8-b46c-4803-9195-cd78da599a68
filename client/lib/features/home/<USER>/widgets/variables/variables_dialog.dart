import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:promz_common/promz_common.dart';
import 'package:promz/core/providers/service_providers.dart';
import 'package:promz/core/services/client_context_service.dart';
import 'package:promz/features/home/<USER>/variable.dart';
import 'package:promz/features/home/<USER>/variables_dialog_result.dart';
import 'package:promz/features/home/<USER>/variable_manager.dart';
import 'package:promz/features/home/<USER>/widgets/variables/custom_variable_field.dart';
import 'package:promz/features/home/<USER>/widgets/variables/entity_variable_field.dart';

/// Dialog that displays and collects values for prompt variables
class VariablesDialog extends ConsumerWidget {
  static const _logName = 'VariablesDialog';

  final VariableManager variableManager;
  final VoidCallback? onVariablesChanged;
  final VoidCallback? onAllVariablesFilled;
  final VoidCallback? onProceed;
  final Map<String, String> originalVariableValues;
  final DisplayItem? promptDisplayItem; // Added DisplayItem for context

  const VariablesDialog({
    Key? key,
    required this.variableManager,
    this.onVariablesChanged,
    this.onAllVariablesFilled,
    this.onProceed,
    required this.originalVariableValues,
    this.promptDisplayItem,
  }) : super(key: key);

  /// Shows the variables dialog
  static Future<VariablesDialogResult?> show(
    BuildContext context,
    ClientContextService clientContextService,
    VariableManager variableManager, {
    VoidCallback? onVariablesChanged,
    VoidCallback? onAllVariablesFilled,
    VoidCallback? onProceed,
    DisplayItem? promptDisplayItem,
  }) async {
    appLog.debug('Showing variables dialog with ${variableManager.variables.length} variables',
        name: 'VariablesDialog.show');

    // Capture original variable values to support cancel functionality
    final originalVariableValues =
        Map<String, String>.from(clientContextService.variableManager.variableValues);

    // Get category icon if display item is available
    IconData? headerIcon;
    String? title;
    if (promptDisplayItem != null) {
      headerIcon = PromzDialog.getCategoryIcon(promptDisplayItem.prompt?.categoryName);
      title = promptDisplayItem.displayText;
    }

    // Create dialog content with variables and outer 'context', which is fine for Theme lookups
    // as it's done before the dialog is shown.
    final dialogContent = Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Prompt subtitle (if available)
        if (promptDisplayItem?.subtitle != null && promptDisplayItem!.subtitle!.isNotEmpty) ...[
          Text(
            promptDisplayItem.subtitle!,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  // ignore: deprecated_member_use
                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                ),
          ),
          const SizedBox(height: 16),
        ],

        // Category chip (if available)
        if (promptDisplayItem?.prompt?.categoryName != null &&
            promptDisplayItem!.prompt!.categoryName!.isNotEmpty) ...[
          Wrap(
            children: [
              Chip(
                label: Text(promptDisplayItem.prompt!.categoryName!),
                backgroundColor: Theme.of(context).colorScheme.primaryContainer,
                labelStyle: TextStyle(
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                  fontSize: 12,
                ),
                visualDensity: VisualDensity.compact,
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
            ],
          ),
          const SizedBox(height: 16),
        ],

        // Variables section title
        Text(
          'Choose or enter values',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 16),

        // Variable fields
        VariablesDialog(
          variableManager: variableManager,
          onVariablesChanged: onVariablesChanged,
          onAllVariablesFilled: onAllVariablesFilled,
          onProceed: onProceed,
          originalVariableValues: originalVariableValues,
          promptDisplayItem: promptDisplayItem,
        ),
      ],
    );

    // Use the standard PromzDialog for consistent styling
    return await showDialog<VariablesDialogResult>(
      context: context, // Outer context is used to launch the dialog
      barrierDismissible: true,
      builder: (BuildContext dialogContext) {
        // This is the crucial dialogContext
        // Define action buttons INSIDE the builder to use dialogContext for Navigator.pop
        final actions = <Widget>[
          TextButton(
            onPressed: () {
              // Restore original values when Cancel is clicked
              _restoreOriginalValues(context, originalVariableValues, clientContextService);
              Navigator.of(dialogContext).pop(null); // Use dialogContext
            },
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              // Don't restore values when Save is clicked
              Navigator.of(dialogContext).pop(VariablesDialogResult.saved()); // Use dialogContext
            },
            child: const Text('Save'),
          ),
          FilledButton(
            onPressed: () {
              // Don't restore values when Execute is clicked
              Navigator.of(dialogContext).pop(VariablesDialogResult.execute()); // Use dialogContext
              // Proceed to execute the prompt if callback provided
              if (onProceed != null) {
                onProceed();
              }
            },
            child: const Text('Execute'),
          ),
        ];

        return PopScope(
          canPop: true,
          onPopInvokedWithResult: (didPop, result) {
            if (didPop && result == null) {
              // Restore original values when popped without explicit result
              // The 'context' for _restoreOriginalValues is the outer one, which is fine
              // as it doesn't perform Navigator operations.
              _restoreOriginalValues(context, originalVariableValues, clientContextService);
            }
          },
          child: PromzDialog(
            title: title ?? 'Enter Variables',
            headerIcon: headerIcon,
            content: dialogContent,
            actions: actions, // Actions now correctly use dialogContext
            onClose: () {
              _restoreOriginalValues(context, originalVariableValues, clientContextService);
              Navigator.of(dialogContext).pop(null); // Use dialogContext for onClose as well
            },
          ),
        );
      },
    );
  }

  /// Restores the original variable values when canceling
  static void _restoreOriginalValues(
      BuildContext context, Map<String, String> originalValues, ClientContextService service) {
    appLog.debug('Restoring original variable values', name: _logName);

    // Restore each variable value
    originalValues.forEach((key, value) {
      service.setVariableValue(key, value);
    });

    // Clear any variables that were added but not originally present
    final currentVariableNames = service.variableManager.variableValues.keys.toSet();
    final originalVariableNames = originalValues.keys.toSet();
    final addedVariableNames = currentVariableNames.difference(originalVariableNames);

    for (final name in addedVariableNames) {
      service.clearVariableValue(name);
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Get all variables
    final allVariables = variableManager.variables;

    // Filter out content variables when a corresponding URL variable exists
    final filteredVariables = _filterNewsContentVariables(allVariables);

    if (filteredVariables.isEmpty) {
      return const Center(child: Text('No variables required for this prompt.'));
    }

    appLog.debug(
        'Building variables dialog with ${filteredVariables.length} variables (filtered from ${allVariables.length})',
        name: _logName);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Use .when to handle the service state
        ref.watch(clientContextServiceProvider).when(
              data: (service) => Column(
                // Build the fields only when service is available
                children: filteredVariables
                    .map((variable) =>
                        _buildVariableField(context, variable, service)) // Pass resolved service
                    .toList(),
              ),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) {
                appLog.error('Error loading ClientContextService for Variable Fields',
                    name: _logName, error: error, stackTrace: stack);
                return Center(child: Text('Error loading variable fields: $error'));
              },
            ),
      ],
    );
  }

  /// Filter out news article content variables when a corresponding URL variable exists
  List<Variable> _filterNewsContentVariables(List<Variable> variables) {
    // First, identify all news article URL variables
    final newsUrlVariables = variables
        .where((v) => v.entityType == EntityType.news && v.name.toLowerCase().contains('url'))
        .toList();

    // Create a set of content variable names to filter out
    final contentVarNamesToFilter = <String>{};

    // For each URL variable, find the corresponding content variable name
    for (final urlVar in newsUrlVariables) {
      final contentVarName = urlVar.name.toLowerCase().replaceAll('url', 'contents');
      contentVarNamesToFilter.add(contentVarName);
      appLog.debug(
          'Will filter out content variable: $contentVarName for URL variable: ${urlVar.name}',
          name: _logName);
    }

    // Filter out content variables that have a corresponding URL variable
    return variables
        .where((v) => !(v.entityType == EntityType.news &&
            contentVarNamesToFilter.contains(v.name.toLowerCase())))
        .toList();
  }

  /// Build the appropriate input field based on variable type
  Widget _buildVariableField(
      BuildContext context, Variable variable, ClientContextService clientContextService) {
    appLog.debug('START: Building variable field for ${variable.fullName}, ${variable.type}',
        name: _logName);

    // Get the current value from the centralized store
    final currentValue = clientContextService.variableManager.variableValues[variable.fullName];

    // Choose the appropriate input field based on variable type
    if (variable.isEntityVariable) {
      appLog.debug('END: Building entity variable field for ${variable.fullName}', name: _logName);
      return EntityVariableField(
        variable: variable,
        initialValue: currentValue,
        onChanged: (Variable variable) {
          appLog.debug('Value changed for ${variable.fullName}: ${variable.value}', name: _logName);
          clientContextService.setVariableValue(variable.fullName, variable.value ?? '');
          onVariablesChanged?.call();

          // Check if all variables are filled
          if (variableManager.allVariablesResolved) {
            onAllVariablesFilled?.call();
          }
          // Trigger the general callback AFTER checking resolution
          onVariablesChanged?.call();
        },
      );
    } else {
      appLog.debug('END: Building custom variable field for ${variable.fullName}', name: _logName);
      return CustomVariableField(
        variable: variable,
        initialValue: currentValue,
        onChanged: (value) {
          // Important: Only update the value when it's non-empty
          // Don't clear values from clientContextService here
          if (value.isNotEmpty) {
            // Use centralized value setting with correct variable name
            clientContextService.setVariableValue(variable.fullName, value);
            onVariablesChanged?.call();

            // Check if all variables are filled
            if (variableManager.allVariablesResolved) {
              onAllVariablesFilled?.call();
            }
            // Trigger the general callback AFTER checking resolution
            onVariablesChanged?.call();
          }
          // Empty values are ignored - clearing is done via the chip X button
        },
      );
    }
  }
}
