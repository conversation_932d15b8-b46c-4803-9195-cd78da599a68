import 'package:flutter/material.dart';
import 'package:promz/features/home/<USER>/variable.dart';
import 'package:promz_common/promz_common.dart';

/// Input field for custom variables
class CustomVariableField extends StatefulWidget {
  final Variable variable;
  final String? initialValue;
  final ValueChanged<String> onChanged;

  const CustomVariableField({
    Key? key,
    required this.variable,
    this.initialValue,
    required this.onChanged,
  }) : super(key: key);

  @override
  State<CustomVariableField> createState() => _CustomVariableFieldState();
}

class _CustomVariableFieldState extends State<CustomVariableField> {
  static const _logName = 'CustomVariableField';
  late final TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialValue ?? '');
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(CustomVariableField oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.initialValue != widget.initialValue) {
      _controller.text = widget.initialValue ?? '';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.variable.displayName,
            style: Theme.of(context).textTheme.labelLarge,
          ),
          const SizedBox(height: 4),
          TextField(
            controller: _controller,
            decoration: InputDecoration(
              // Display prefixed name for label
              labelText: widget.variable.prefixedDisplayName,
              hintText: 'Enter value',
              border: const OutlineInputBorder(),
              isDense: true,
              suffixIcon: _controller.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        setState(() {
                          _controller.clear();
                        });
                      },
                    )
                  : null,
            ),
            onChanged: (value) {
              appLog.debug('Custom variable value changed: $value', name: _logName);
              if (value.isNotEmpty) {
                widget.onChanged(value);
              }
            },
          ),
          if (widget.variable.description != null) ...[
            const SizedBox(height: 4),
            Text(
              widget.variable.description!,
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ],
      ),
    );
  }
}
