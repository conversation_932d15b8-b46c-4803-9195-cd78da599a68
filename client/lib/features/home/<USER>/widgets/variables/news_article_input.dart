import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:promz/core/utils/url_extractor.dart';
import 'package:promz/features/home/<USER>/variable.dart';
import 'package:promz/features/news/services/news_article_service.dart';
import 'package:promz/features/news/widgets/news_link_preview.dart';
import 'package:promz/generated/content_upload.pb.dart';
import 'package:promz_common/promz_common.dart';

/// A specialized input widget for news article variables
/// Allows users to enter a URL and see a preview of the article
class NewsArticleInput extends StatefulWidget {
  final String initialValue;
  final void Function(Variable) onSelected;
  final NewsArticleService newsService;
  final Variable variable;

  /// Optional variable for article contents that will be updated alongside the URL
  final Variable? contentsVariable;

  const NewsArticleInput({
    Key? key,
    required this.initialValue,
    required this.onSelected,
    required this.newsService,
    required this.variable,
    this.contentsVariable,
  }) : super(key: key);

  @override
  State<NewsArticleInput> createState() => _NewsArticleInputState();
}

class _NewsArticleInputState extends State<NewsArticleInput> {
  static const _logName = 'NewsArticleInput';

  late TextEditingController _urlController;
  bool _isLoading = false;
  bool _isValidUrl = false;
  bool _isProcessingInput = false;
  ProcessingResult? _previewResult;
  String? _selectedArticleId;
  String? _extractedUrl;

  @override
  void initState() {
    super.initState();
    _urlController = TextEditingController(text: widget.initialValue);

    // If we have an initial value, try to validate it
    if (widget.initialValue.isNotEmpty) {
      if (_isUrlValid(widget.initialValue)) {
        _validateInput(widget.initialValue);
      } else {
        // It might be an article ID, not a URL
        _selectedArticleId = widget.initialValue;
        // Here we would ideally fetch the article details by ID
      }
    }
  }

  @override
  void dispose() {
    _urlController.dispose();
    super.dispose();
  }

  Future<void> _pasteFromClipboard() async {
    final data = await Clipboard.getData(Clipboard.kTextPlain);
    if (data != null && data.text != null) {
      final text = data.text!;
      setState(() {
        _urlController.text = text;
        _isProcessingInput = true;
      });
      _validateInput(text);
    }
  }

  void _validateInput(String input) async {
    setState(() {
      _isProcessingInput = true;
    });

    try {
      // First, try to extract a URL from the input text using the UrlExtractor
      final processedUrlInfo = await UrlExtractor.extractUrl(input);

      if (processedUrlInfo != null) {
        // We found a URL in the input
        final bestUrl = processedUrlInfo.bestUrl;
        appLog.debug('Extracted URL from input: $bestUrl', name: _logName);

        // Update the URL field with the extracted URL
        if (_urlController.text != bestUrl) {
          setState(() {
            _urlController.text = bestUrl;
          });
        }

        _extractedUrl = bestUrl;
        setState(() {
          _isValidUrl = true;
          _isProcessingInput = false;
        });

        // Fetch preview for the extracted URL
        _fetchPreview(bestUrl);
      } else {
        // No valid URL found in the input
        final directUrlValid = _isUrlValid(input);
        setState(() {
          _isValidUrl = directUrlValid;
          _isProcessingInput = false;
          if (!directUrlValid) {
            _previewResult = null;
            _extractedUrl = null;
          }
        });

        if (directUrlValid) {
          _fetchPreview(input);
        }
      }
    } catch (e, stack) {
      appLog.error('Error validating input', name: _logName, error: e, stackTrace: stack);
      setState(() {
        _isValidUrl = _isUrlValid(input);
        _isProcessingInput = false;
      });
    }
  }

  bool _isUrlValid(String url) {
    if (url.isEmpty) return false;

    try {
      final uri = Uri.parse(url);
      return uri.scheme == 'http' || uri.scheme == 'https';
    } catch (e) {
      return false;
    }
  }

  Future<void> _fetchPreview(String url) async {
    if (!_isValidUrl) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Use the lightweight preview method
      final metadata = await widget.newsService.fetchArticlePreview(url);

      setState(() {
        _previewResult = metadata;
        _isLoading = false;
      });

      // Automatically select the article once preview is fetched successfully
      await _selectArticle();
    } catch (e, stack) {
      appLog.error('Error fetching article preview', name: _logName, error: e, stackTrace: stack);
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _selectArticle() async {
    if (_previewResult == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Fetch and register the full article
      appLog.debug('Fetching and registering article with metadata', name: _logName);
      final articleMetadata = _previewResult!.articleMetadata;
      final url = articleMetadata.url.isNotEmpty ? articleMetadata.url : _urlController.text;
      final fullMetadata = await widget.newsService.fetchAndRegisterArticleWithMetadata(
        url: url,
        title: articleMetadata.title,
        description: articleMetadata.excerpt,
      );

      // Update the variable with the article ID
      final articleId = fullMetadata.jobId; // Using jobId as the article ID
      if (articleId.isNotEmpty) {
        _selectedArticleId = articleId;
        widget.onSelected(widget.variable.copyWithValue(articleId));

        // If we have a contents variable, update it with the article content
        if (widget.contentsVariable != null && fullMetadata.content.isNotEmpty) {
          final articleContent = fullMetadata.content;
          widget.onSelected(widget.contentsVariable!.copyWithValue(articleContent));
          appLog.debug('Updated article contents variable with ${articleContent.length} characters',
              name: _logName);
        }
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e, stack) {
      appLog.error('Error selecting article', name: _logName, error: e, stackTrace: stack);
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _clearSelection() {
    setState(() {
      _urlController.clear();
      _previewResult = null;
      _selectedArticleId = null;
      _isValidUrl = false;
      _extractedUrl = null;
    });

    // Update the variable with empty value
    widget.onSelected(widget.variable.copyWithValue(''));

    // Also clear the contents variable if it exists
    if (widget.contentsVariable != null) {
      widget.onSelected(widget.contentsVariable!.copyWithValue(''));
    }
  }

  @override
  Widget build(BuildContext context) {
    // If we already have a selected article, show its details
    if (_selectedArticleId != null && _previewResult != null) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          NewsLinkPreview(
            processingResult: _previewResult!,
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: Text(
                  'Article selected',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.green,
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ),
              TextButton.icon(
                icon: const Icon(Icons.edit),
                label: const Text('Change'),
                onPressed: _clearSelection,
              ),
            ],
          ),
        ],
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // URL Input Field
        TextField(
          controller: _urlController,
          decoration: InputDecoration(
            hintText: 'Enter news article URL or paste text containing a URL',
            border: const OutlineInputBorder(),
            prefixIcon: const Icon(Icons.link),
            suffixIcon: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (_urlController.text.isNotEmpty)
                  IconButton(
                    icon: const Icon(Icons.clear),
                    onPressed: _clearSelection,
                  ),
                IconButton(
                  icon: const Icon(Icons.content_paste),
                  onPressed: _pasteFromClipboard,
                ),
              ],
            ),
          ),
          onChanged: _validateInput,
        ),

        const SizedBox(height: 8),

        // Input processing indicator
        if (_isProcessingInput) const Center(child: Text('Extracting URL from input...')),

        // Show the extracted URL if different from input
        if (_extractedUrl != null && _extractedUrl != _urlController.text && !_isProcessingInput)
          Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: Text(
              'Using URL: $_extractedUrl',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontStyle: FontStyle.italic,
                    color: Colors.blue,
                  ),
            ),
          ), // Loading Indicator
        if (_isLoading) const Center(child: CircularProgressIndicator(strokeWidth: 2.0)),

        // Preview
        if (_previewResult != null && !_isLoading)
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              NewsLinkPreview(
                processingResult: _previewResult!,
              ),
            ],
          ),
      ],
    );
  }
}
