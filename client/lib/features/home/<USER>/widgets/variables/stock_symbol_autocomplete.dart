import 'package:flutter/material.dart';
import 'package:promz/features/home/<USER>/variable.dart';
import 'package:promz_common/promz_common.dart';
import 'package:promz/features/home/<USER>/entity_detection_service.dart';

/// An enhanced autocomplete widget for stock symbol selection
/// This provides a searchable dropdown of stock symbols with company names
class StockSymbolAutocomplete extends StatefulWidget {
  final String initialValue;
  // Now receives and returns a Variable instead of creating one
  final void Function(Variable) onSelected;
  final FocusNode? focusNode;
  final EntityDetectionService entityService;
  final Variable variable;

  const StockSymbolAutocomplete({
    Key? key,
    this.initialValue = '',
    required this.onSelected,
    this.focusNode,
    required this.entityService,
    required this.variable,
  }) : super(key: key);

  @override
  State<StockSymbolAutocomplete> createState() => _StockSymbolAutocompleteState();
}

class _StockSymbolAutocompleteState extends State<StockSymbolAutocomplete> {
  static const _logName = 'StockSymbolAutocomplete';

  late TextEditingController _controller;
  late FocusNode _focusNode;
  List<Entity> _allStocks = [];
  bool _isLoading = true;
  bool _shouldShowOptions = false;
  String? _selectedSymbol;

  @override
  void initState() {
    super.initState();
    _selectedSymbol = widget.initialValue;
    _controller = TextEditingController(text: widget.initialValue);
    _focusNode = widget.focusNode ?? FocusNode();

    // Add listener to show options when focused
    _focusNode.addListener(_onFocusChange);

    _loadStockSymbols();
  }

  void _onFocusChange() {
    if (_focusNode.hasFocus && !_shouldShowOptions) {
      setState(() {
        _shouldShowOptions = true;
      });
    }
  }

  Future<void> _loadStockSymbols() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Ensure entity dictionaries are loaded
      await widget.entityService.loadEntityDictionaries();

      // Get all finance entities
      final entities = widget.entityService.getAllEntities();

      // Filter for stock symbols only
      _allStocks = entities
          .where((entity) => entity.type == EntityType.finance && entity.entitySubtype == 'stock')
          .toList();

      // Sort alphabetically by symbol
      _allStocks.sort((a, b) {
        final symbolA = a.stockSymbol ?? '';
        final symbolB = b.stockSymbol ?? '';
        return symbolA.compareTo(symbolB);
      });

      // Try to find initial entity that matches our initial value
      if (widget.initialValue.isNotEmpty) {
        final matchingEntities = _allStocks.where((entity) {
          final symbol = entity.stockSymbol ?? '';
          return symbol.toUpperCase() == widget.initialValue.toUpperCase();
        }).toList();

        if (matchingEntities.isNotEmpty) {
          // Found a matching entity
          final matchingEntity = matchingEntities.first;
          final symbol = matchingEntity.stockSymbol ?? '';
          final companyName = matchingEntity.companyName ?? '';
          _controller.text = '$symbol - $companyName';
          _selectedSymbol = symbol;
        } else {
          // No matching entity found - keep the initial value as is
          _controller.text = widget.initialValue;
          _selectedSymbol = widget.initialValue;
        }
      }

      appLog.debug('Loaded ${_allStocks.length} stock symbols', name: _logName);
    } catch (e, stack) {
      appLog.error('Error loading stock symbols', name: _logName, error: e, stackTrace: stack);
      _allStocks = [];
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    // Remove focus listener
    _focusNode.removeListener(_onFocusChange);

    // Only dispose the focus node if we created it
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    _controller.dispose();
    super.dispose();
  }

  // Filter stocks based on user input
  Iterable<Entity> _filterStocks(TextEditingValue textEditingValue) {
    if (_shouldShowOptions && textEditingValue.text.isEmpty) {
      // When field is focused but empty, show popular/common stocks
      return _allStocks.take(50); // Show first 50 stocks
    }

    final query = textEditingValue.text.toLowerCase();
    final results = <Entity>[];

    // First, add exact symbol matches (highest priority)
    results.addAll(_allStocks.where((entity) {
      final symbol = entity.stockSymbol ?? '';
      return symbol.toLowerCase() == query;
    }));

    // Then add symbols that start with the query (high priority)
    if (results.length < 50) {
      results.addAll(_allStocks.where((entity) {
        final symbol = entity.stockSymbol ?? '';
        return symbol.toLowerCase().startsWith(query) && !results.contains(entity);
      }).take(50 - results.length));
    }

    // Then add symbols that contain the query (medium priority)
    if (results.length < 50) {
      results.addAll(_allStocks.where((entity) {
        final symbol = entity.stockSymbol ?? '';
        return symbol.toLowerCase().contains(query) && !results.contains(entity);
      }).take(50 - results.length));
    }

    // Finally, search company names (lower priority)
    if (results.length < 50) {
      results.addAll(_allStocks.where((entity) {
        final companyName = entity.companyName ?? '';
        return companyName.toLowerCase().contains(query) && !results.contains(entity);
      }).take(50 - results.length));
    }

    return results;
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(strokeWidth: 2.0),
      );
    }

    return Autocomplete<Entity>(
      initialValue: TextEditingValue(text: _controller.text),
      optionsBuilder: (TextEditingValue textEditingValue) {
        // Enable suggestions as soon as field is focused or text is entered
        return _filterStocks(textEditingValue);
      },
      displayStringForOption: (Entity option) {
        final symbol = option.stockSymbol ?? '';
        final companyName = option.companyName ?? '';
        return '$symbol - $companyName';
      },
      fieldViewBuilder: (
        BuildContext context,
        TextEditingController fieldController,
        FocusNode fieldFocusNode,
        VoidCallback onFieldSubmitted,
      ) {
        // Use the controller and focus node provided by Autocomplete
        // This ensures proper connections between text input and options
        return TextFormField(
          controller: fieldController,
          focusNode: fieldFocusNode,
          decoration: InputDecoration(
            hintText: 'Search by symbol or company name',
            prefixIcon: const Icon(Icons.search),
            border: const OutlineInputBorder(),
            // Add magnifying glass icon to indicate searchable field
            suffixIcon: _selectedSymbol != null && _selectedSymbol!.isNotEmpty
                ? IconButton(
                    icon: const Icon(Icons.clear),
                    onPressed: () {
                      fieldController.clear();
                      setState(() {
                        _selectedSymbol = null;
                      });
                      // Clear the variable value
                      widget.onSelected(widget.variable.copyWithValue(''));
                    },
                  )
                : const Icon(Icons.expand_more),
          ),
          onChanged: (text) {
            // Ensure options display is enabled when typing
            if (!_shouldShowOptions) {
              setState(() => _shouldShowOptions = true);
            }
          },
          onFieldSubmitted: (_) => onFieldSubmitted(),
        );
      },
      optionsViewBuilder: (
        BuildContext context,
        AutocompleteOnSelected<Entity> onSelected,
        Iterable<Entity> options,
      ) {
        return Align(
          alignment: Alignment.topLeft,
          child: Material(
            elevation: 4.0,
            child: ConstrainedBox(
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.4,
                maxWidth: MediaQuery.of(context).size.width * 0.9,
              ),
              child: ListView.builder(
                padding: const EdgeInsets.all(8.0),
                itemCount: options.length,
                itemBuilder: (BuildContext context, int index) {
                  final Entity option = options.elementAt(index);
                  final symbol = option.stockSymbol ?? '';
                  final companyName = option.companyName ?? '';

                  return ListTile(
                    leading: const Icon(Icons.trending_up, color: Colors.green),
                    title: Text(
                      '$symbol - $companyName',
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    onTap: () {
                      onSelected(option);
                    },
                  );
                },
              ),
            ),
          ),
        );
      },
      onSelected: (Entity selection) {
        final symbol = selection.stockSymbol ?? '';
        appLog.debug('Selected stock: $symbol', name: _logName);
        _selectedSymbol = symbol;

        // Make sure to update the internal state before calling callback
        setState(() {});

        // Update the variable with the selected symbol
        widget.onSelected(widget.variable.copyWithValue(symbol));
      },
    );
  }
}
