import 'package:flutter/material.dart';
import 'package:promz/features/home/<USER>/extracted_keyword.dart';
import 'package:promz_common/strings.dart';

class KeywordExtractionSection extends StatelessWidget {
  final List<ExtractedKeyword> keywords;
  final Function(ExtractedKeyword) onKeywordDeleted;
  final bool isExpanded;
  final VoidCallback onToggleExpanded;
  final bool isLoading; // Add isLoading parameter

  const KeywordExtractionSection({
    super.key,
    required this.keywords,
    required this.onKeywordDeleted,
    required this.isExpanded,
    required this.onToggleExpanded,
    this.isLoading = false, // Make it optional with default value
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        ListTile(
          title: Row(
            children: [
              Text(
                Strings.extractedKeywordsTitle,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                    ),
              ),
              if (keywords.isNotEmpty)
                Text(
                  ' (${keywords.length})',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                      ),
                ),
            ],
          ),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (isLoading)
                const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              const SizedBox(width: 8),
              IconButton(
                icon: Icon(
                  isExpanded ? Icons.expand_less : Icons.expand_more,
                ),
                onPressed: onToggleExpanded,
              ),
            ],
          ),
        ),
        if (isExpanded)
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: keywords.map((keyword) {
              return Chip(
                label: Text(keyword.text),
                deleteIcon: const Icon(Icons.close, size: 18),
                onDeleted: () => onKeywordDeleted(keyword),
                avatar: Icon(
                  keyword.relevance > 0.7 ? Icons.arrow_upward : Icons.arrow_downward,
                  size: 18,
                  color: keyword.relevance > 0.7 ? Colors.green : Colors.orange,
                ),
              );
            }).toList(),
          ),
      ],
    );
  }
}
