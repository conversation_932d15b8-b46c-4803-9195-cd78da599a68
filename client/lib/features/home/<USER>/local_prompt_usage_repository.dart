import 'package:drift/drift.dart';
import 'package:promz/database/database.dart';
import 'package:promz/features/home/<USER>/prompt_usage_event.dart';
import 'package:promz_common/promz_common.dart';

/// Repository for local prompt usage data
class LocalPromptUsageRepository {
  static const _logName = 'LocalPromptUsageRepository';

  final AppDatabase _database;

  LocalPromptUsageRepository(this._database);

  /// Inserts a prompt usage event into the local database
  Future<void> insertPromptUsageEvent(PromptUsageEvent event) async {
    try {
      final entry = LocalPromptUsageCompanion.insert(
        promptId: event.promptId,
        eventType: event.eventType.name,
        usedAt: event.usedAt.millisecondsSinceEpoch,
        countryCode: Value(event.countryCode),
      );
      await _database.insertLocalPromptUsage(entry);
      appLog.debug('Inserted prompt usage event into local database', name: _logName);
    } catch (e, stackTrace) {
      appLog.error('Error inserting prompt usage event into local database',
          name: _logName, error: e, stackTrace: stackTrace);
    }
  }

  /// Gets all prompt usage events from the local database
  Future<List<LocalPromptUsageData>> getAllPromptUsageEvents() async {
    try {
      final events = await _database.getAllLocalPromptUsage();
      appLog.debug('Retrieved ${events.length} prompt usage events from local database',
          name: _logName);
      return events;
    } catch (e) {
      appLog.error('Error getting all prompt usage events from local database',
          name: _logName, error: e);
      return [];
    }
  }

  /// Clears all prompt usage events from the local database
  Future<void> clearAllPromptUsageEvents() async {
    try {
      await _database.clearLocalPromptUsage();
      appLog.debug('Cleared all prompt usage events from local database', name: _logName);
    } catch (e) {
      appLog.error('Error clearing all prompt usage events from local database',
          name: _logName, error: e);
    }
  }
}
