import 'package:flutter/foundation.dart';

/// Represents the type of usage event being recorded for a prompt.
enum PromptUsageEventType {
  selected,
  executed,
}

/// Represents a single usage event for a prompt, intended for local storage
/// before being uploaded to the server.
@immutable
class PromptUsageEvent {
  final int? id; // Nullable ID, as it's assigned by the local DB
  final String promptId;
  final PromptUsageEventType eventType;
  final DateTime usedAt;
  final String? countryCode; // Nullable country code

  const PromptUsageEvent({
    this.id,
    required this.promptId,
    required this.eventType,
    required this.usedAt,
    this.countryCode,
  });

  // --- Serialization ---

  /// Converts the event to a Map suitable for JSON encoding or DB insertion.
  /// Note: Enum is converted to its string representation.
  /// Note: DateTime is converted to millisecondsSinceEpoch for DB compatibility.
  Map<String, dynamic> toJson() {
    return {
      'id': id, // Include id if it exists
      'prompt_id': promptId,
      'event_type': eventType.name, // Use enum name (e.g., 'selected')
      'used_at': usedAt.millisecondsSinceEpoch,
      'country_code': countryCode,
    };
  }

  /// Creates an event from a Map (e.g., from JSON or DB).
  /// Note: Enum is parsed from its string representation.
  /// Note: DateTime is parsed from millisecondsSinceEpoch.
  factory PromptUsageEvent.fromJson(Map<String, dynamic> json) {
    final eventTypeName = json['event_type'] as String?;
    final timestampMillis = json['used_at'] as int?;

    if (eventTypeName == null || timestampMillis == null) {
      throw FormatException("Missing required fields 'event_type' or 'used_at' in JSON: $json");
    }

    return PromptUsageEvent(
      id: json['id'] as int?,
      promptId: json['prompt_id'] as String? ?? '', // Handle potential null
      eventType: PromptUsageEventType.values.firstWhere(
        (e) => e.name == eventTypeName,
        orElse: () => throw FormatException('Invalid event_type: $eventTypeName'),
      ),
      usedAt: DateTime.fromMillisecondsSinceEpoch(timestampMillis),
      countryCode: json['country_code'] as String?,
    );
  }

  // --- Immutability & Equality ---

  PromptUsageEvent copyWith({
    int? id,
    String? promptId,
    PromptUsageEventType? eventType,
    DateTime? usedAt,
    String? countryCode,
    bool includeNullCountryCode = false, // Flag to explicitly set countryCode to null
  }) {
    return PromptUsageEvent(
      id: id ?? this.id,
      promptId: promptId ?? this.promptId,
      eventType: eventType ?? this.eventType,
      usedAt: usedAt ?? this.usedAt,
      countryCode: includeNullCountryCode ? null : (countryCode ?? this.countryCode),
    );
  }

  @override
  String toString() {
    return 'PromptUsageEvent(id: $id, promptId: $promptId, eventType: ${eventType.name}, usedAt: $usedAt, countryCode: $countryCode)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is PromptUsageEvent &&
        other.id == id &&
        other.promptId == promptId &&
        other.eventType == eventType &&
        other.usedAt == usedAt &&
        other.countryCode == countryCode;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        promptId.hashCode ^
        eventType.hashCode ^
        usedAt.hashCode ^
        countryCode.hashCode;
  }
}
