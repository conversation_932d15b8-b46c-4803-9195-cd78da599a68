import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/scheduler.dart';
import 'package:promz/database/database.dart';
import 'package:promz_common/promz_common.dart';

/// Service to handle background loading of SP500 tickers
class TickerLoadingService {
  static const _logName = 'TickerLoadingService';

  /// The batch size for loading tickers
  static const int _batchSize = 100;

  /// Initial number of tickers to load immediately
  static const int _initialLoadCount = 100;

  /// Delay between loading batches (milliseconds)
  static const int _batchDelayMs = 200;

  /// Delay before starting background loading (seconds)
  static const int _backgroundLoadDelaySeconds = 5;

  /// Database connection
  final Future<AppDatabase> _db;

  /// Loading status
  final ValueNotifier<TickerLoadingStatus> _loadingStatus =
      ValueNotifier(TickerLoadingStatus.notStarted);

  /// Total number of tickers to load
  int _totalTickerCount = 0;

  /// Number of tickers loaded so far
  int _loadedTickerCount = 0;

  /// Timer for background loading
  Timer? _loadingTimer;

  /// Flag to prevent multiple simultaneous loading operations
  bool _isLoading = false;

  /// Create an instance with database
  TickerLoadingService({required Future<AppDatabase> db}) : _db = db;

  /// Get the current loading status
  ValueNotifier<TickerLoadingStatus> get loadingStatus => _loadingStatus;

  /// Get the loading progress (0.0 to 1.0)
  double get loadingProgress =>
      _totalTickerCount > 0 ? _loadedTickerCount / _totalTickerCount : 0.0;

  /// Start loading tickers
  ///
  /// First loads an initial batch immediately, then loads the rest in the background
  Future<void> startLoading() async {
    if (_isLoading) {
      appLog.debug('Ticker loading already in progress', name: _logName);
      return;
    }

    _isLoading = true;
    _loadingStatus.value = TickerLoadingStatus.loading;

    try {
      // Get the total count of tickers
      final db = await _db;
      _totalTickerCount = await db.getSP500TickerCount();

      if (_totalTickerCount == 0) {
        appLog.debug('No tickers found in database', name: _logName);
        _loadingStatus.value = TickerLoadingStatus.completed;
        _isLoading = false;
        return;
      }

      // Load initial batch immediately
      await _loadInitialBatch();

      // If all tickers are loaded in the initial batch, we're done
      if (_loadedTickerCount >= _totalTickerCount) {
        _loadingStatus.value = TickerLoadingStatus.completed;
        _isLoading = false;
        return;
      }

      // Schedule background loading for remaining tickers after a delay
      _scheduleBackgroundLoading();
    } catch (e, stack) {
      appLog.error('Error starting ticker loading', name: _logName, error: e, stackTrace: stack);
      _loadingStatus.value = TickerLoadingStatus.error;
      _isLoading = false;
    }
  }

  /// Schedule background loading after a delay to ensure UI responsiveness
  void _scheduleBackgroundLoading() {
    appLog.debug('Scheduling background ticker loading with a delay', name: _logName);

    // Use a post-frame callback to ensure the app is rendered before starting background loading
    SchedulerBinding.instance.addPostFrameCallback((_) {
      // Add a delay to ensure the UI is responsive
      Timer(const Duration(seconds: _backgroundLoadDelaySeconds), () {
        _startBackgroundLoading();
      });
    });
  }

  /// Stop background loading
  void stopLoading() {
    _loadingTimer?.cancel();
    _loadingTimer = null;
    _isLoading = false;
    appLog.debug('Ticker loading stopped', name: _logName);
  }

  /// Load the initial batch of tickers
  Future<void> _loadInitialBatch() async {
    appLog.debug('Loading initial batch of $_initialLoadCount tickers', name: _logName);

    final db = await _db;
    final tickers = await db.getAllSP500Tickers(limit: _initialLoadCount);

    _loadedTickerCount = tickers.length;
    appLog.debug('Loaded $_loadedTickerCount tickers initially', name: _logName);
  }

  /// Start background loading of remaining tickers
  void _startBackgroundLoading() {
    appLog.debug('Starting background loading of remaining tickers', name: _logName);

    // Use a periodic timer to load batches
    _loadingTimer = Timer.periodic(const Duration(milliseconds: _batchDelayMs), (timer) {
      _loadNextBatch();
    });
  }

  /// Load the next batch of tickers
  Future<void> _loadNextBatch() async {
    if (!_isLoading || _loadedTickerCount >= _totalTickerCount) {
      stopLoading();
      _loadingStatus.value = TickerLoadingStatus.completed;
      return;
    }

    try {
      final db = await _db;
      final tickers = await db.getAllSP500Tickers(limit: _batchSize, offset: _loadedTickerCount);

      if (tickers.isEmpty) {
        // No more tickers to load
        stopLoading();
        _loadingStatus.value = TickerLoadingStatus.completed;
        return;
      }

      _loadedTickerCount += tickers.length;

      // Update loading status
      appLog.debug('Loaded $_loadedTickerCount/$_totalTickerCount tickers', name: _logName);

      // If we've loaded all tickers, we're done
      if (_loadedTickerCount >= _totalTickerCount) {
        stopLoading();
        _loadingStatus.value = TickerLoadingStatus.completed;
      }
    } catch (e, stack) {
      appLog.error('Error loading next batch of tickers',
          name: _logName, error: e, stackTrace: stack);
      // Continue loading despite errors
    }
  }

  /// Dispose resources
  void dispose() {
    stopLoading();
    _loadingStatus.dispose();
  }
}

/// Status of ticker loading
enum TickerLoadingStatus {
  /// Loading has not started
  notStarted,

  /// Loading is in progress
  loading,

  /// Loading completed successfully
  completed,

  /// Error occurred during loading
  error
}
