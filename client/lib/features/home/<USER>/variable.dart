import 'package:promz_common/promz_common.dart';

/// Types of prompt variables
enum VariableType {
  /// Known entity from the registry (like stocks)
  entityVariable,

  /// Custom user-defined variable
  customVariable,
}

/// Represents a variable in a prompt template
class Variable {
  /// The unique name of this variable
  final String name;

  /// The type of variable (entity or custom)
  final VariableType type;

  /// Display name shown to users in the variable UI
  final String displayName;

  /// Description or hint for this variable
  final String? description;

  /// Current value of the variable (null if not filled)
  final String? value;

  /// Original display text for the template before replacement
  final String originalDisplay;

  /// Template pattern like {{FINANCE:SP500_SYMBOL}}
  final String fullTemplate;

  /// Associated Entity for entity variables
  /// For entity variables, this is required and serves as the source of truth
  final Entity? entity;

  /// Creates a new variable
  const Variable._internal({
    required this.name,
    required this.type,
    required this.displayName,
    required this.originalDisplay,
    required this.fullTemplate,
    this.description,
    this.value,
    this.entity,
  });

  /// Create a variable from an entity
  factory Variable.fromEntity(Entity entity, {String? value}) {
    return Variable._internal(
      name: entity.canonicalText,
      type: VariableType.entityVariable,
      displayName: EntityUtils.getDisplayName(entity: entity),
      originalDisplay: entity.displayText,
      fullTemplate: entity.templateText,
      value: value,
      entity: entity,
    );
  }

  /// Create a custom variable (non-entity based)
  factory Variable({
    required String name,
    required VariableType type,
    required String displayName,
    required String originalDisplay,
    required String fullTemplate,
    EntityType? entityType,
    String? value,
    String? description,
    int? startPosition,
    int? endPosition,
    Entity? entity,
    String? prefix,
  }) {
    // For entity variables, ensure we have an entity
    if (type == VariableType.entityVariable && entity == null && entityType != null) {
      // Create a basic entity if one wasn't provided
      entity = Entity(
        text: name,
        canonicalText: name,
        displayText: displayName,
        type: entityType,
        startPosition: startPosition,
        endPosition: endPosition,
        prefix: prefix,
      );
    }

    return Variable._internal(
      name: name,
      type: type,
      displayName: displayName,
      originalDisplay: originalDisplay,
      fullTemplate: fullTemplate,
      value: value,
      description: description,
      entity: entity,
    );
  }

  /// Create a copy of this variable with a new value
  Variable copyWithValue(String? newValue) {
    return Variable._internal(
      name: name,
      type: type,
      displayName: displayName,
      description: description,
      value: newValue,
      originalDisplay: originalDisplay,
      fullTemplate: fullTemplate,
      entity: entity,
    );
  }

  /// Check if this variable has a valid value
  bool get hasValue => value != null && value!.isNotEmpty;

  /// Check if this variable is an entity variable
  bool get isEntityVariable => type == VariableType.entityVariable;

  /// Check if this variable is a custom variable
  bool get isCustomVariable => type == VariableType.customVariable;

  /// The entity type if this is an entity variable
  EntityType? get entityType => entity?.type;

  /// Start position of template in original text
  int? get startPosition => entity?.startPosition;

  /// End position of template in original text
  int? get endPosition => entity?.endPosition;

  /// Optional prefix for distinguishing multiple instances of same variable
  String? get prefix => entity?.prefix;

  /// Get the full name including prefix if present
  String get fullName => prefix != null ? '{#$prefix}$name' : name;

  /// Get the display name including prefix if present
  String get prefixedDisplayName => prefix != null ? '$prefix. $displayName' : displayName;

  /// Get the canonical identity of this variable, which uniquely identifies it
  String get canonicalIdentity {
    return EntityUtils.getCanonicalName(fullName);
  }

  /// Check if this variable has the same identity as another variable
  bool hasSameIdentityAs(Variable other) {
    return canonicalIdentity == other.canonicalIdentity;
  }

  /// Factory constructor to create a variable from a name and optional parameters
  factory Variable.createFromName(
    String name, {
    String? explicitType,
    String? fullTemplate,
    int? startPosition,
    int? endPosition,
    String? prefix,
  }) {
    // Get the canonical name (this properly handles prefixes already)
    final canonicalName = EntityUtils.getCanonicalName(name);

    // Parse the canonical name to extract components
    String actualName;
    String? actualType = explicitType;
    EntityType? entityType;

    // Check if the canonical name has an entity type prefix (e.g. FINANCE:TICKER)
    final bool hasEntityTypePrefix = canonicalName.contains(':');

    if (hasEntityTypePrefix) {
      final parts = canonicalName.split(':');
      if (parts.length >= 2) {
        // If canonical name has entity type, use it
        actualType = actualType ?? parts[0];
        actualName = parts[1];

        try {
          // Try to get entity type from the prefix
          entityType = EntityType.values
              .firstWhere((t) => t.name.toUpperCase() == actualType!.toUpperCase());
        } catch (e) {
          // Use null if entity type not found
        }
      } else {
        // Fallback if splitting fails
        actualName = name;
      }
    } else {
      // For canonical names without entity type
      actualName = canonicalName;
    }

    // If no explicit type or it wasn't found, try to determine from the name
    entityType ??= EntityUtils.getEntityTypeForVariable(canonicalName);

    // Create the display name
    final displayName = EntityUtils.getDisplayName(variableName: canonicalName);

    // Create entity if this is an entity variable
    Entity? entity;
    String template;

    if (entityType != null) {
      entity = Entity(
        text: actualName,
        canonicalText: actualName,
        displayText: displayName,
        type: entityType,
        startPosition: startPosition,
        endPosition: endPosition,
        prefix: prefix,
      );
      template = fullTemplate ?? entity.templateText;
    } else {
      // For custom variables, use the static helper method
      template = fullTemplate ?? Entity.generateCustomTemplate(actualName, prefix: prefix);
    }

    // Create and return the variable
    return Variable(
      name: canonicalName,
      type: entityType != null ? VariableType.entityVariable : VariableType.customVariable,
      displayName: displayName,
      originalDisplay: displayName,
      fullTemplate: template,
      entityType: entityType,
      entity: entity,
    );
  }

  // Override equals operator for proper equality checks
  @override
  bool operator ==(Object other) =>
      identical(this, other) || (other is Variable && canonicalIdentity == other.canonicalIdentity);

  // Override hashCode to be consistent with equals
  @override
  int get hashCode => canonicalIdentity.hashCode;
}
