import 'dart:io';
import 'dart:developer' as dev;
import 'package:archive/archive.dart';
import 'package:file_picker/file_picker.dart';
import 'package:mime/mime.dart';
import 'package:path/path.dart' as path;
import 'package:syncfusion_flutter_pdf/pdf.dart';

class UnsupportedFileException implements Exception {
  final String fileName;
  final String extension;
  final List<String> supportedExtensions;

  UnsupportedFileException(this.fileName, this.extension, this.supportedExtensions);

  @override
  String toString() {
    return 'Cannot process "$fileName". Only the following file types are supported: ${supportedExtensions.join(", ")}';
  }
}

class FilePickerService {
  static const List<String> _supportedTextExtensions = ['.txt', '.md'];
  static const List<String> _supportedImageExtensions = ['.jpg', '.jpeg', '.png'];
  static const List<String> _supportedDocumentExtensions = ['.pdf'];
  static const List<String> _supportedArchiveExtensions = ['.zip'];

  static List<String> get supportedExtensions => [
        ..._supportedTextExtensions,
        ..._supportedImageExtensions,
        ..._supportedDocumentExtensions,
        ..._supportedArchiveExtensions,
      ];

  Future<List<FileResult>> pickFiles() async {
    final result = await FilePicker.platform.pickFiles(
      type: FileType.any,
      allowMultiple: true,
      withData: true,
    );

    if (result == null) return [];

    List<FileResult> files = [];
    List<String> unsupportedFiles = [];

    for (var file in result.files) {
      final extension = path.extension(file.name).toLowerCase();
      dev.log('Processing file: ${file.name}', name: 'FilePickerService');

      try {
        if (extension == '.zip') {
          if (file.bytes != null) {
            files.addAll(await _processArchiveBytes(file.bytes!, file.name));
          } else if (file.path != null) {
            files.addAll(await _processArchive(file.path!));
          }
        } else if (_supportedTextExtensions.contains(extension)) {
          files.add(await _processTextFile(file));
        } else if (_supportedImageExtensions.contains(extension)) {
          files.add(await _processImageFile(file));
        } else if (_supportedDocumentExtensions.contains(extension)) {
          files.add(await _processDocumentFile(file));
        } else {
          throw UnsupportedFileException(file.name, extension, supportedExtensions);
        }
      } on UnsupportedFileException catch (e) {
        dev.log(e.toString(), name: 'FilePickerService', level: 900);
        unsupportedFiles.add(file.name);
      }
    }

    if (unsupportedFiles.isNotEmpty) {
      throw UnsupportedFileException(unsupportedFiles.join(', '), 'multiple', supportedExtensions);
    }

    return files;
  }

  Future<List<FileResult>> _processArchiveBytes(List<int> bytes, String fileName) async {
    List<FileResult> results = [];
    final archive = ZipDecoder().decodeBytes(bytes);

    for (final file in archive) {
      if (!file.isFile) continue;

      final extension = path.extension(file.name).toLowerCase();
      final mimeType = lookupMimeType(file.name) ?? 'application/octet-stream';

      if (_supportedTextExtensions.contains(extension)) {
        results.add(FileResult(
          path: fileName, // Use the zip file name as the path
          fileName: file.name,
          content: String.fromCharCodes(file.content as List<int>),
          mimeType: 'text/plain',
          fileType: FileType.custom,
          sourceArchive: fileName,
        ));
      } else if (_supportedImageExtensions.contains(extension)) {
        results.add(FileResult(
          path: fileName,
          fileName: file.name,
          content: null,
          mimeType: mimeType,
          fileType: FileType.image,
          sourceArchive: fileName,
          fileSize: file.content.length,
        ));
      } else if (_supportedDocumentExtensions.contains(extension)) {
        results.add(FileResult(
          path: fileName,
          fileName: file.name,
          content: null,
          mimeType: 'application/pdf',
          fileType: FileType.custom,
          sourceArchive: fileName,
          fileSize: file.content.length,
        ));
      }
    }

    return results;
  }

  Future<FileResult> _processTextFile(PlatformFile file) async {
    String content;
    if (file.bytes != null) {
      // Handle content URI case
      content = String.fromCharCodes(file.bytes!);
    } else {
      // Handle direct file path case
      content = await File(file.path!).readAsString();
    }

    return FileResult(
      path: file.path ?? file.name,
      fileName: file.name,
      content: content,
      mimeType: 'text/plain',
      fileType: FileType.custom,
    );
  }

  Future<FileResult> _processImageFile(PlatformFile file) async {
    return FileResult(
      path: file.path!,
      fileName: file.name,
      content: null, // Raw content not needed for images
      mimeType: lookupMimeType(file.path!) ?? 'image/unknown',
      fileType: FileType.image,
      fileSize: file.size,
    );
  }

  Future<FileResult> _processDocumentFile(PlatformFile file) async {
    if (file.path == null && file.bytes == null) {
      throw Exception('PDF file must have either a path or bytes available');
    }

    try {
      // Load the PDF document
      PdfDocument? pdfDoc;
      if (file.bytes != null) {
        pdfDoc = PdfDocument(inputBytes: file.bytes!);
      } else {
        pdfDoc = PdfDocument(inputBytes: await File(file.path!).readAsBytes());
      }

      // Extract text from all pages
      final PdfTextExtractor extractor = PdfTextExtractor(pdfDoc);
      final StringBuilder textBuilder = StringBuilder();

      for (int i = 1; i <= pdfDoc.pages.count; i++) {
        final String pageText = extractor.extractText(startPageIndex: i - 1, endPageIndex: i - 1);
        if (pageText.isNotEmpty) {
          if (textBuilder.length > 0) {
            textBuilder.appendLine();
          }
          textBuilder.append(pageText);
        }
      }

      final String content = textBuilder.toString();

      dev.log(
        'Successfully extracted text from PDF: ${file.name}',
        name: 'FilePickerService',
      );

      return FileResult(
        path: file.path ?? file.name,
        fileName: file.name,
        content: content,
        mimeType: 'application/pdf',
        fileType: FileType.custom,
        fileSize: file.size,
        metadata: {
          'pageCount': pdfDoc.pages.count,
          'isEncrypted': pdfDoc.security.userPassword.isNotEmpty,
          'hasDigitalSignature': pdfDoc.form.fields.count > 0,
        },
      );
    } catch (e) {
      dev.log(
        'Failed to extract text from PDF: ${file.name}. Error: $e',
        name: 'FilePickerService',
        level: 900,
      );
      rethrow;
    }
  }

  Future<List<FileResult>> _processArchive(String archivePath) async {
    List<FileResult> results = [];
    final bytes = await File(archivePath).readAsBytes();
    final archive = ZipDecoder().decodeBytes(bytes);

    for (final file in archive) {
      if (!file.isFile) continue;

      final extension = path.extension(file.name).toLowerCase();
      final mimeType = lookupMimeType(file.name) ?? 'application/octet-stream';

      if (_supportedTextExtensions.contains(extension)) {
        results.add(FileResult(
          path: archivePath,
          fileName: file.name,
          content: String.fromCharCodes(file.content as List<int>),
          mimeType: 'text/plain',
          fileType: FileType.custom,
          sourceArchive: path.basename(archivePath),
        ));
      } else if (_supportedImageExtensions.contains(extension)) {
        results.add(FileResult(
          path: archivePath,
          fileName: file.name,
          content: null,
          mimeType: mimeType,
          fileType: FileType.image,
          sourceArchive: path.basename(archivePath),
          fileSize: file.content.length,
        ));
      } else if (_supportedDocumentExtensions.contains(extension)) {
        results.add(FileResult(
          path: archivePath,
          fileName: file.name,
          content: null,
          mimeType: 'application/pdf',
          fileType: FileType.custom,
          sourceArchive: path.basename(archivePath),
          fileSize: file.content.length,
        ));
      }
    }

    return results;
  }
}

class FileResult {
  final String path;
  final String fileName;
  final String? content;
  final String mimeType;
  final FileType fileType;
  final String? sourceArchive;
  final int? fileSize;
  final Map<String, dynamic>? metadata;

  FileResult({
    required this.path,
    required this.fileName,
    required this.mimeType,
    required this.fileType,
    this.content,
    this.sourceArchive,
    this.fileSize,
    this.metadata,
  });
}

class StringBuilder {
  final StringBuffer _buffer = StringBuffer();

  void append(String str) {
    _buffer.write(str);
  }

  void appendLine([String? str]) {
    if (str != null) {
      _buffer.writeln(str);
    } else {
      _buffer.writeln();
    }
  }

  int get length => _buffer.length;

  @override
  String toString() => _buffer.toString();
}
