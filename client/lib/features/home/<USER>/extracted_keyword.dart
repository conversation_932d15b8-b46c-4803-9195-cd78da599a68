class ExtractedKeyword {
  final String text;
  final double relevance;
  final String? source; // Add source tracking

  const ExtractedKeyword({
    required this.text,
    required this.relevance,
    this.source,
  });

  factory ExtractedKeyword.fromJson(Map<String, dynamic> json) {
    return ExtractedKeyword(
      text: json['text'] as String,
      relevance: json['relevance'] as double,
    );
  }

  Map<String, dynamic> toJson() => {
        'text': text,
        'relevance': relevance,
      };
}
