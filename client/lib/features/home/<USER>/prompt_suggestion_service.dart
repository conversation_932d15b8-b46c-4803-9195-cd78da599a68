import 'dart:convert';
import 'package:promz/core/services/client_context_service.dart';
import 'package:promz/database/database.dart';
import 'package:promz/database/db_utils.dart';
import 'package:promz_common/promz_common.dart';

/// Defines the modes for prompt suggestions
enum PromptSuggestionMode {
  /// Semantic matching mode for source input autocomplete
  semantic,

  /// Combined mode for home screen (recent + relevance)
  combined
}

class PromptSuggestionService {
  static const _logName = 'PromptSuggestions';
  final Future<AppDatabase> _db;
  final ClientContextService _clientContextService;

  /// Constructor that takes required dependencies
  PromptSuggestionService({
    required Future<AppDatabase> db,
    required ClientContextService clientContextService,
  })  : _db = db,
        _clientContextService = clientContextService;

  Future<List<DisplayItem>> getPromptSuggestions(
    List<String> keywords, {
    PromptSuggestionMode mode = PromptSuggestionMode.semantic,
  }) async {
    appLog.debug('START: getPromptSuggestions(mode: $mode)', name: _logName);

    try {
      final database = await _db;
      if (database.isClosing) {
        appLog.debug('END: Database is closed (isClosing)', name: _logName);
        return [];
      }

      // For empty keywords in semantic mode, return empty list
      if (keywords.isEmpty && mode == PromptSuggestionMode.semantic) {
        appLog.debug('END: No keywords provided in semantic mode', name: _logName);
        return [];
      }

      // Get prompts based on mode
      final recentPrompts = mode == PromptSuggestionMode.combined
          ? await database.getRecentPrompts(limit: 3)
          : <Prompt>[];

      // If no keywords and in combined mode, just return recent prompts
      if (keywords.isEmpty && mode == PromptSuggestionMode.combined) {
        final items = recentPrompts.map((prompt) {
          final promptModel = DatabaseUtils.createPromptModelFromDb(prompt)
              .copyWith(relevanceScore: 0.5); // Base score for recent items
          return DisplayItem.fromPromptModel(promptModel, _clientContextService);
        }).toList();
        return items;
      }

      // Continue with normal scoring for non-empty keywords
      final allPrompts = await database.getAllPrompts();
      final categories = await database.select(database.categories).get();

      if (categories.isEmpty) {
        appLog.debug('END: No categories found in database', name: _logName);
        return [];
      }

      // Create a set of recent prompt IDs for quick lookup
      final recentPromptIds = recentPrompts.map((p) => p.id).toSet();

      // Score all prompts
      final scoredPrompts = await Future.wait(allPrompts.map((prompt) async {
        try {
          final category = categories.cast<Category?>().firstWhere(
                (c) => c?.id == prompt.categoryId,
                orElse: () => null,
              );

          var promptModel = await _scorePrompt(prompt, keywords, category);

          // Add boost score for recent prompts in combined mode
          if (mode == PromptSuggestionMode.combined && recentPromptIds.contains(prompt.id)) {
            promptModel = promptModel.copyWith(
              relevanceScore: (promptModel.score + 0.5).clamp(0.0, 1.0),
            );
          }

          return promptModel;
        } catch (e, stack) {
          if (!e.toString().contains('No element')) {
            appLog.error('Error scoring prompt', name: _logName, error: e, stackTrace: stack);
          }
          return DatabaseUtils.createPromptModelFromDb(prompt);
        }
      }));

      scoredPrompts.sort((a, b) => b.score.compareTo(a.score));

      // Adjust minimum score based on mode
      final minScore = mode == PromptSuggestionMode.semantic ? 0.1 : 0.0;
      final topMatches = scoredPrompts.where((sp) => sp.score >= minScore).take(3).map((sp) async {
        return DisplayItem.fromPromptModel(sp, _clientContextService);
      }).toList();

      final displayItems = await Future.wait(topMatches);

      appLog.debug('END: Found ${displayItems.length} matching suggestions', name: _logName);
      return displayItems;
    } catch (e, stack) {
      appLog.error('END: Error getting suggestions', name: _logName, error: e, stackTrace: stack);
      return [];
    }
  }

  // Legacy method for backward compatibility
  Future<List<DisplayItem>> getSuggestedPrompts(List<String> keywords) async {
    return getPromptSuggestions(keywords, mode: PromptSuggestionMode.semantic);
  }

  Future<List<DisplayItem>> getRecentPrompts() async {
    appLog.debug('getRecentPrompts() START', name: _logName);
    try {
      final database = await _db;

      // Get recent prompts from the database using last_used_at field
      final prompts = await database.getRecentPrompts(limit: 3);

      // Convert database Prompt objects to DisplayItems
      final items = prompts.map((prompt) {
        final promptModel = DatabaseUtils.createPromptModelFromDb(prompt);
        return DisplayItem.fromPromptModel(promptModel, _clientContextService);
      }).toList();

      appLog.debug('Found ${items.length} recent prompts', name: _logName);
      return items;
    } catch (e, stack) {
      appLog.error('Error getting recent prompts', name: _logName, error: e, stackTrace: stack);
      return [];
    }
  }

  Future<List<DisplayItem>> getPopularPrompts() async {
    appLog.debug('getPopularPrompts() START', name: _logName);
    try {
      final database = await _db;

      // Get popular prompts from the database using the PopularPrompts table
      final prompts = await database.getPopularPrompts(limit: 3);

      // Convert database Prompt objects to DisplayItems
      final items = prompts.map((prompt) {
        final promptModel = DatabaseUtils.createPromptModelFromDb(prompt);
        return DisplayItem.fromPromptModel(promptModel, _clientContextService);
      }).toList();

      appLog.debug('Found ${items.length} popular prompts', name: _logName);
      return items;
    } catch (e, stack) {
      appLog.error('Error getting popular prompts', name: _logName, error: e, stackTrace: stack);
      return [];
    }
  }

  Future<PromptModel> _scorePrompt(
    Prompt prompt,
    List<String> keywords,
    Category? category,
  ) async {
    try {
      final promptKeywords = _parseKeywords(prompt.keywords, 'prompt');
      final categoryKeywords =
          category != null ? _parseKeywords(category.keywords, 'category') : <String>[];

      final score = _calculateRelevanceScore(
        inputKeywords: keywords,
        promptKeywords: promptKeywords,
        categoryKeywords: categoryKeywords,
        prompt: prompt,
        category: category,
      );

      final matchedKeywords =
          _findMatchedKeywords(keywords, [...promptKeywords, ...categoryKeywords]);

      return DatabaseUtils.createPromptModelFromDb(prompt).copyWith(
        relevanceScore: score,
        matchedKeywords: matchedKeywords,
        categoryId: category?.id,
      );
    } catch (e, stack) {
      appLog.error('Error scoring prompt', name: _logName, error: e, stackTrace: stack);
      return DatabaseUtils.createPromptModelFromDb(prompt);
    }
  }

  List<String> _parseKeywords(String jsonKeywords, String source) {
    try {
      final List<dynamic> decoded = json.decode(jsonKeywords) as List<dynamic>;
      return List<String>.from(decoded);
    } catch (e) {
      appLog.error('Error parsing $source keywords', name: _logName, error: e);
      return <String>[];
    }
  }

  double _calculateRelevanceScore({
    required List<String> inputKeywords,
    required List<String> promptKeywords,
    required List<String> categoryKeywords,
    required Prompt prompt,
    Category? category,
  }) {
    try {
      // Check for complete match with prompt title
      final promptTitleKeywords = prompt.title.toLowerCase().split(' ');
      if (inputKeywords.every((ik) => promptTitleKeywords.contains(ik.toLowerCase()))) {
        return 1.0;
      }

      // Check for complete match with category title
      if (category != null) {
        final categoryTitleKeywords = category.title.toLowerCase().split(' ');
        if (inputKeywords.every((ik) => categoryTitleKeywords.contains(ik.toLowerCase()))) {
          return 0.8;
        }
      }

      final matchedPromptKeywords =
          promptKeywords.where((pk) => inputKeywords.any((ik) => _isKeywordMatch(ik, pk))).toList();
      final matchedCategoryKeywords = categoryKeywords
          .where((ck) => inputKeywords.any((ik) => _isKeywordMatch(ik, ck)))
          .toList();

      var score = 0.0;

      if (promptKeywords.isNotEmpty) {
        final promptScore = (matchedPromptKeywords.length / inputKeywords.length) * 0.9;
        score += promptScore;
      }
      if (categoryKeywords.isNotEmpty) {
        final categoryScore = (matchedCategoryKeywords.length / inputKeywords.length) * 0.7;
        score += categoryScore;
      }

      return score.clamp(0.0, 1.0);
    } catch (e, stackTrace) {
      appLog.error('Error calculating relevance score',
          name: _logName, error: e, stackTrace: stackTrace);
      return 0.0;
    }
  }

  bool _isKeywordMatch(String input, String keyword) {
    final normalizedInput = input.toLowerCase().trim();
    final normalizedKeyword = keyword.toLowerCase().trim();

    return normalizedInput == normalizedKeyword ||
        normalizedKeyword.contains(normalizedInput) ||
        normalizedInput.contains(normalizedKeyword);
  }

  List<String> _findMatchedKeywords(List<String> inputKeywords, List<String> targetKeywords) {
    final matchedKeywords = <String>{};
    for (final ik in inputKeywords) {
      for (final tk in targetKeywords) {
        if (_isKeywordMatch(ik, tk)) {
          matchedKeywords.add(tk);
        }
      }
    }
    return matchedKeywords.toList();
  }
}
