import 'dart:developer' as dev;
import 'dart:math';

enum ContentType {
  general,
  conversation,
  financial,
  technical,
}

class TextAnalysisService {
  static const int _maxKeywords = 10;
  static const double _minScore = 0.4;

  // Common compound terms to detect
  static const _compoundTerms = {
    // AI/ML Terms (highest weight)
    'artificial intelligence': 1.5,
    'machine learning': 1.5,
    'deep learning': 1.5,
    'neural network': 1.5,
    'natural language': 1.5,
    'computer vision': 1.5,

    // Financial Terms (high weight)
    'data center': 1.3,
    'stock market': 1.3,
    'market analysis': 1.3,
    'quarterly results': 1.3,
    'revenue growth': 1.3,
    'earnings report': 1.3,
    'market share': 1.3,
    'profit margin': 1.3,
    'cash flow': 1.3,
    'balance sheet': 1.3,
    'income statement': 1.3,
    'financial report': 1.3,
    'trading volume': 1.3,
    'market cap': 1.3,

    // Technical/Industry Terms (medium-high weight)
    'supply chain': 1.2,
    'cloud computing': 1.2,
    'edge computing': 1.2,
    'semiconductor industry': 1.2,
    'chip manufacturing': 1.2,
    'data processing': 1.2,
    'real time': 1.2,
    'high performance': 1.2,

    // Business Terms (medium weight)
    'year over year': 1.1,
    'quarter over quarter': 1.1,
    'competitive advantage': 1.1,
    'market position': 1.1,
    'business model': 1.1,
    'growth strategy': 1.1,
  };

  // Technical terms to boost
  static const _technicalTerms = {
    // Company Tickers/Names (highest weight)
    'nvda': 1.5,
    'nvidia': 1.5,
    'amd': 1.5,
    'intel': 1.5,
    'tsmc': 1.5,
    'qualcomm': 1.5,

    // Technical Terms (high weight)
    'ai': 1.4,
    'ml': 1.4,
    'gpu': 1.4,
    'cpu': 1.4,
    'tpu': 1.4,
    'asic': 1.4,
    'fpga': 1.4,

    // Financial Metrics (medium-high weight)
    'revenue': 1.3,
    'earnings': 1.3,
    'ebitda': 1.3,
    'eps': 1.3,
    'margin': 1.3,
    'profit': 1.3,
    'growth': 1.3,

    // Industry Terms (medium weight)
    'chip': 1.2,
    'semiconductor': 1.2,
    'processor': 1.2,
    'hardware': 1.2,
    'software': 1.2,
    'cloud': 1.2,
    'server': 1.2,
    'datacenter': 1.2,

    // Market Terms (medium-low weight)
    'market': 1.1,
    'stock': 1.1,
    'shares': 1.1,
    'trading': 1.1,
    'investment': 1.1,
  };

  static Future<TextAnalysisService> initialize() async {
    return TextAnalysisService();
  }

  Future<List<String>> extractKeywords(
    String text, {
    String? title,
    String? subtitle,
    bool collectMetrics = false,
  }) async {
    dev.log('Extracting keywords from text: ${text.substring(0, min(100, text.length))}...');
    if (title != null) {
      dev.log('Title: $title');
    }
    if (subtitle != null) {
      dev.log('Subtitle: $subtitle');
    }

    if (text.isEmpty) return [];

    try {
      // Test case for debugging
      if (text.contains('NVDA')) {
        dev.log('Found NVDA in text');
        dev.log('Text: $text');
        dev.log('Title: $title');
      }

      final scores = await getKeywordScores(text, title: title, subtitle: subtitle);

      // Sort and filter results
      final sortedTerms = scores.entries.toList()..sort((a, b) => b.value.compareTo(a.value));

      final keywords = sortedTerms
          .where((e) => e.value >= _minScore)
          .take(_maxKeywords)
          .map((e) => e.key)
          .toList();

      dev.log('Extracted keywords: ${keywords.join(', ')}');
      return keywords;
    } catch (e) {
      dev.log('Error extracting keywords', error: e);
      return [];
    }
  }

  Future<Map<String, double>> getKeywordScores(
    String text, {
    String? title,
    String? subtitle,
  }) async {
    if (text.isEmpty) return {};

    try {
      // Process text to extract terms and frequencies
      final terms = _processText(text, ContentType.conversation);
      final frequencies = _calculateFrequencies(terms);

      dev.log('Processed terms: ${terms.join(', ')}');
      dev.log('Term frequencies: ${frequencies.toString()}');

      // Also process title terms if present
      final titleTerms = title != null ? _processText(title, ContentType.conversation) : <String>[];
      final titleFrequencies = _calculateFrequencies(titleTerms);

      if (title != null) {
        dev.log('Title terms: ${titleTerms.join(', ')}');
        dev.log('Title frequencies: ${titleFrequencies.toString()}');
      }

      // Find max frequency for normalization
      final maxFrequency = frequencies.values.fold(0.0, (max, freq) => freq > max ? freq : max);

      dev.log('Max frequency: $maxFrequency');

      // Score terms
      final scores = <String, double>{};

      // First score terms from main text
      for (var entry in frequencies.entries) {
        if (_isValidTerm(entry.key)) {
          // Base score from frequency (normalized by max frequency)
          var score = entry.value.toDouble() / maxFrequency;
          score = 0.4 + (score * 0.6); // Increase base score for valid terms

          // Apply compound term boost if applicable
          if (_compoundTerms.containsKey(entry.key.toLowerCase())) {
            score *= _compoundTerms[entry.key.toLowerCase()]!;
          }

          // Apply technical term boost if applicable
          if (_technicalTerms.containsKey(entry.key.toLowerCase())) {
            score *= _technicalTerms[entry.key.toLowerCase()]!;
          }

          scores[entry.key.toLowerCase()] = score.clamp(0.0, 1.0);
        }
      }

      dev.log('Base scores: ${scores.toString()}');

      // Then process title terms with higher base importance
      for (var entry in titleFrequencies.entries) {
        final term = entry.key.toLowerCase();
        if (_isValidTerm(term)) {
          // Title terms get a higher base score
          var titleScore = 0.8 + (0.2 * entry.value / titleTerms.length);

          // Apply compound term boost if applicable
          if (_compoundTerms.containsKey(term)) {
            titleScore *= _compoundTerms[term]!;
          }

          // Apply technical term boost if applicable
          if (_technicalTerms.containsKey(term)) {
            titleScore *= _technicalTerms[term]!;
          }

          // If term exists in main text, boost its score
          if (scores.containsKey(term)) {
            scores[term] = (scores[term]! + titleScore).clamp(0.0, 1.0);
          } else {
            // Term only appears in title, give it the title score
            scores[term] = titleScore.clamp(0.0, 1.0);
          }
        }
      }

      dev.log('Final scores: ${scores.toString()}');
      return scores;
    } catch (e) {
      dev.log('Error calculating keyword scores', error: e);
      return {};
    }
  }

  List<String> _processText(String text, ContentType contentType) {
    dev.log('Processing text: ${text.substring(0, min(100, text.length))}...');

    // Normalize to lowercase
    text = text.toLowerCase();
    dev.log('After lowercase: $text');

    // Apply content type specific preprocessing
    switch (contentType) {
      case ContentType.conversation:
        text = _preprocessChatContent(text);
        break;
      case ContentType.financial:
        text = _preprocessFinancialContent(text);
        break;
      case ContentType.technical:
        text = _preprocessTechnicalContent(text);
        break;
      default:
        break;
    }
    dev.log('After content type preprocessing: $text');

    // Clean text (remove HTML tags, special chars)
    text = text.replaceAll('<', ' ');
    text = text.replaceAll('>', ' ');
    text = text.replaceAll(RegExp(r'[^\w\s]'), ' ');
    dev.log('After cleaning: $text');

    // Handle apostrophes in contractions
    text = text.replaceAll("'s ", ' ');
    text = text.replaceAll("'", ' ');
    dev.log('After handling apostrophes: $text');

    // Find compound terms first
    var terms = <String>[];
    var compoundTermsFound = 0;
    for (var entry in _compoundTerms.entries) {
      final pattern = RegExp(entry.key, caseSensitive: false);
      if (pattern.hasMatch(text)) {
        terms.add(entry.key);
        compoundTermsFound++;
        // Remove matched compound terms to avoid double counting
        text = text.replaceAll(pattern, ' ');
      }
    }
    dev.log('Found $compoundTermsFound compound terms: $terms');

    // Find technical terms next
    var technicalTermsFound = 0;
    for (var entry in _technicalTerms.entries) {
      final pattern = RegExp(r'\b' + entry.key + r'\b', caseSensitive: false);
      if (pattern.hasMatch(text)) {
        terms.add(entry.key);
        technicalTermsFound++;
        // Remove matched technical terms to avoid double counting
        text = text.replaceAll(pattern, ' ');
      }
    }
    dev.log('Found $technicalTermsFound technical terms: $terms');

    // Split remaining text into terms
    var remainingTerms = text
        .split(RegExp(r'\s+'))
        .where((term) =>
            term.isNotEmpty &&
            term.length > 3 &&
            !commonWords.contains(term) &&
            !RegExp(r'^\d+$').hasMatch(term) &&
            !RegExp(r'^[a-z]$').hasMatch(term))
        .toList();
    dev.log('Remaining terms: $remainingTerms');

    terms.addAll(remainingTerms);
    dev.log('Final processed terms: $terms');
    return terms;
  }

  String _preprocessChatContent(String text) {
    // Remove timestamps and usernames
    text = text.replaceAll(
        RegExp(r'\[\d{4}-\d{2}-\d{2},?\s+\d{1,2}:\d{2}(?::\d{2})?\s*(?:AM|PM)?\]'), ' ');
    text = text.replaceAll(RegExp(r'^\s*\w+:\s*', multiLine: true), ' ');
    return text;
  }

  String _preprocessFinancialContent(String text) {
    // Remove currency symbols and normalize numbers
    text = text.replaceAll(RegExp(r'[$€£¥]'), ' ');
    text = text.replaceAll(RegExp(r'\d+\.\d+|\d+'), ' number ');
    return text;
  }

  String _preprocessTechnicalContent(String text) {
    // Remove code blocks and special technical characters
    text = text.replaceAll(RegExp(r'```[\s\S]*?```'), ' ');
    text = text.replaceAll(RegExp(r'`[^`]+`'), ' ');
    return text;
  }

  Map<String, double> _calculateFrequencies(List<String> terms) {
    final frequencies = <String, double>{};
    final total = terms.length.toDouble();

    for (final term in terms) {
      frequencies.update(
        term,
        (value) => value + (1.0 / total),
        ifAbsent: () => 1.0 / total,
      );
    }

    return frequencies;
  }

  bool _isValidTerm(String term) {
    // Allow compound terms and technical terms regardless of common word status
    if (_compoundTerms.containsKey(term) || _technicalTerms.containsKey(term)) {
      return true;
    }

    return term.length > 2 &&
        !commonWords.contains(term.toLowerCase()) &&
        !RegExp(r'^\d+$').hasMatch(term);
  }

  // Common words to filter out
  static const commonWords = {
    'the',
    'is',
    'at',
    'which',
    'on',
    'for',
    'and',
    'to',
    'in',
    'of',
    'com',
    'org',
    'net',
    'http',
    'https',
    'www',
    'example',
    'using',
    'used',
    'use',
    'need',
    'needs',
    'want',
    'wants',
    'get',
    'gets',
    'got',
    'take',
    'takes',
    'make',
    'makes',
    'made',
    'like',
    'likes',
    'way',
    'ways',
    'new',
    'thing',
    'things',
    'good',
    'best',
    'better',
    'different',
    'various',
    'etc',
    'look',
    'looks',
    'help',
    'helps',
    'helped',
    'helping',
    'what',
    'when',
    'where',
    'who',
    'why',
    'how',
    'yes',
    'no',
    'not',
    'about',
    'from',
    'with',
    'their',
    'they',
    'this',
    'that',
    'these',
    'those',
    'then',
    'than',
    'but',
    'now',
    'some',
    'such',
    'could',
    'would',
    'should',
    'shall',
    'will',
    'may',
    'might',
    'must',
    'can',
    'cannot',
    'cant',
    'wont',
    'am',
    'are',
    'was',
    'were',
    'been',
    'being',
    'have',
    'has',
    'had',
    'having',
    'do',
    'does',
    'did',
    'doing',
    'done',
    'going',
    'gone',
    'went',
  };
}
