import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:promz/core/providers/service_providers.dart';
import 'package:promz/core/widgets/promz_app_bar.dart';
import 'package:promz/features/home/<USER>/home_viewmodel.dart';
import 'package:promz/features/home/<USER>/widgets/bottom_nav_bar.dart';
import 'package:promz/features/home/<USER>/widgets/source_input_section.dart';
import 'package:promz/features/shared/widgets/execution_results_manager.dart';
import 'package:promz_common/promz_common.dart';

class HomePage extends ConsumerStatefulWidget {
  final VoidCallback? onRendered;

  const HomePage({
    Key? key,
    this.onRendered,
  }) : super(key: key);

  @override
  ConsumerState<HomePage> createState() => _HomePageState();
}

class _HomePageState extends ConsumerState<HomePage> {
  static const _logName = 'HomePage';
  // Add a class instance key instead of using a global key
  final GlobalKey<SourceInputSectionState> _sourceInputSectionKey =
      GlobalKey<SourceInputSectionState>();

  @override
  void initState() {
    super.initState();
    _initViewModel();

    // Call the onRendered callback after the first frame is rendered
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.onRendered != null) {
        appLog.debug('First frame rendered, calling onRendered callback', name: _logName);
        widget.onRendered!();
      }
    });
  }

  Future<void> _initViewModel() async {
    // First check if ClientContextService is ready before attempting to load data
    await Future.microtask(() async {
      final clientContextAsync = ref.read(clientContextServiceProvider);

      // If ClientContextService is still loading, wait for it to complete
      if (clientContextAsync is AsyncLoading) {
        appLog.debug('Waiting for ClientContextService to be ready before loading prompt lists',
            name: _logName);

        // Wait for ClientContextService to be ready
        await ref.read(clientContextServiceProvider.future);
        appLog.debug('ClientContextService now ready, proceeding with initialization',
            name: _logName);
      }

      final viewModel = ref.read(homeViewModelProvider);

      // Load all prompt lists
      await viewModel.loadAllPromptLists();

      // Load user profile data
      await viewModel.loadProfileData();

      // Start background authentication check without blocking the UI
      if (mounted) {
        // Use a microtask instead of a post-frame callback to avoid race conditions
        // but still don't block the current execution
        Future.microtask(() {
          if (mounted) {
            viewModel.runBackgroundAuthCheck(context);
          }
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final viewModel = ref.watch(homeViewModelProvider);

    return Scaffold(
      appBar: const PromzAppBar(
        title: Text(Strings.appBarTitle),
      ),
      body: Column(
        children: [
          if (viewModel.isProcessingSources) const LinearProgressIndicator(),
          Expanded(
            child: Stack(
              children: [
                _buildMainContent(context, viewModel),
                if (viewModel.isExecutingLlm) _buildLlmContainer(context),
              ],
            ),
          ),
          BottomNavBar(
            currentIndex: viewModel.currentNavIndex,
            onTap: (index) {
              appLog.debug('Navigating to page: $index', name: _logName);
              viewModel.navigateToPage(context, index);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildLlmContainer(BuildContext context) {
    return Container(
      color: Colors.black54,
      child: Center(
        child: Card(
          elevation: 8,
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CircularProgressIndicator(),
                const SizedBox(height: 16),
                Text(
                  Strings.executingPromptText,
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMainContent(BuildContext context, HomeViewModel viewModel) {
    // Use the shared utility function for results display
    return buildExecutionResultsView(
      context,
      viewModel,
      defaultContent: ConstrainedBox(
        constraints: BoxConstraints(
          minHeight: MediaQuery.of(context).size.height - 200, // Adjust as needed
        ),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              SourceInputSection(
                key: _sourceInputSectionKey,
                initialText: viewModel.inputText,
                sources: viewModel.sources,
                onTextChanged: viewModel.updateInputText,
                onSourceDeleted: viewModel.removeSource,
                onSourceAdded: viewModel.addClipboardSource,
                isProcessing: viewModel.isAnalyzing || viewModel.isProcessingSources,
                suggestedPrompts: viewModel.suggestedPrompts,
                isLoadingSuggestions: viewModel.isLoadingSuggestions,
                onPromptSelected: (displayItem) => viewModel.onPromptSelected(context, displayItem),
                onKeywordSelected: (keyword) {
                  // Delay modification to avoid layout phase conflicts
                  Future.microtask(() {
                    if (_sourceInputSectionKey.currentState != null) {
                      _sourceInputSectionKey.currentState!.addHashtag(keyword);
                    }
                  });
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
