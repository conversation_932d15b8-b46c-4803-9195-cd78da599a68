import 'package:promz_common/promz_common.dart';

/// Enum representing the different states of the variables dialog
enum VariablesDialogState {
  /// Dialog was canceled (closed without saving)
  canceled,

  /// Variables were saved but not executed
  saved,

  /// Variables were saved and execution was requested
  execute,

  /// Dialog was not shown (no variables needed)
  notShown,
}

/// Result of the variables dialog interaction
class VariablesDialogResult {
  static const _logName = 'VariablesDialogResult';

  /// The state of the dialog result
  final VariablesDialogState state;

  /// Whether all variables were resolved (convenience getter)
  bool get resolved => state != VariablesDialogState.canceled;

  /// Whether the user wants to execute the prompt immediately (convenience getter)
  bool get execute => state == VariablesDialogState.execute;

  const VariablesDialogResult({
    required this.state,
  });

  /// Factory constructor for a canceled dialog
  factory VariablesDialogResult.canceled() {
    appLog.debug('Creating canceled result', name: _logName);
    return const VariablesDialogResult(state: VariablesDialogState.canceled);
  }

  /// Factory constructor for save-only result
  factory VariablesDialogResult.saved() {
    appLog.debug('Creating saved result', name: _logName);
    return const VariablesDialogResult(state: VariablesDialogState.saved);
  }

  /// Factory constructor for execute result
  factory VariablesDialogResult.execute() {
    appLog.debug('Creating execute result', name: _logName);
    return const VariablesDialogResult(state: VariablesDialogState.execute);
  }

  /// Factory constructor for result when dialog is not shown
  factory VariablesDialogResult.notShown() {
    appLog.debug('Creating not shown result', name: _logName);
    return const VariablesDialogResult(state: VariablesDialogState.notShown);
  }

  @override
  String toString() => 'VariablesDialogResult(state: $state)';
}
