import 'package:flutter/material.dart';
import 'package:promz/core/services/client_context_service.dart';
import 'package:promz/features/home/<USER>/variable.dart';
import 'package:promz_common/promz_common.dart';

/// Class responsible for managing variables in prompt templates
///
/// This class handles the extraction, display, and resolution of variables
/// within prompt templates. It supports both entity variables (linked to
/// known entities like stocks or locations) and custom user-defined variables.
class VariableManager extends ChangeNotifier {
  static const _logName = 'VariableManager';

  // The original prompt text with templates
  String _originalText = '';
  String _displayText = '';
  PromptModel? _promptModel;
  final List<Variable> _variables = [];
  final ClientContextService _clientContextService;

  // Constructor that accepts a ClientContextService instance
  VariableManager({required ClientContextService clientContextService})
      : _clientContextService = clientContextService;

  // Getters
  String get originalText => _originalText;
  String get displayText => _displayText;
  String get resolvedText => _generateResolvedText();
  PromptModel? get promptModel => _promptModel;
  List<Variable> get variables => List.unmodifiable(_variables);

  /// Get all variable values with proper normalization
  Map<String, String> get variableValues {
    // Create a map view that automatically normalizes keys during lookup
    return _NormalizedVariableMap(_clientContextService);
  }

  bool get hasVariables => _variables.isNotEmpty;
  int get variableCount => _variables.length;
  int get resolvedVariableCount => variableValues.length;
  bool get allVariablesResolved =>
      _variables.every((variable) => variableValues.containsKey(variable.name));

  /// Initialize with a prompt model
  void initWithPrompt(PromptModel? promptModel, [String? initialText]) async {
    appLog.debug('START: Initializing with prompt model: ${promptModel?.title}, text: $initialText',
        name: _logName);
    _promptModel = promptModel;
    String text = initialText ?? promptModel?.title ?? '';
    List<String> textParts = [];
    if (text.isNotEmpty) textParts.add(text);
    if (promptModel?.subtitle != null) textParts.add(promptModel!.subtitle!);
    _initialize(textParts: textParts);
    appLog.debug('END: Initialized with prompt model: ${promptModel?.title}, text: $text',
        name: _logName);
  }

  /// Initialize with text
  void initWithText(String text) {
    _promptModel = null;
    _initialize(textParts: [text]);
    appLog.debug('END: Initialized with text: $text', name: _logName);
  }

  /// Reset the manager
  void reset() {
    _originalText = '';
    _displayText = '';
    _promptModel = null;
    _variables.clear();
    notifyListeners();
  }

  /// Initialize the manager with array of text parts
  void _initialize({required List<String> textParts}) {
    // Reset state
    _originalText = textParts.isNotEmpty ? textParts.first : '';
    _variables.clear();

    // Extract variables from definition if available
    _extractDefinedVariables();

    // Extract template variables from all text parts
    _extractTemplateVariables(textParts);

    // Generate display text
    _generateDisplayText();

    // Generate resolved text
    _generateResolvedText();

    // Notify listeners of changes
    notifyListeners();
  }

  /// Extract variables defined in the prompt.variables field
  void _extractDefinedVariables() {
    if (_promptModel == null || !_promptModel!.hasTemplateVariables) return;

    final variablesList = _promptModel!.variables;

    try {
      // Extract variables from the list of variable names
      for (final variableName in variablesList) {
        _addVariableFromName(variableName);
      }

      appLog.debug('Extracted defined variables from prompt', name: _logName);
    } catch (e, stack) {
      appLog.error('Error extracting defined variables',
          name: _logName, error: e, stackTrace: stack);
    }
  }

  /// Extract template variables from text using regex
  void _extractTemplateVariables(List<String> textParts) {
    for (final text in textParts) {
      // Extract all templates from the text
      final templates = TemplateUtils.extractTemplates(text);

      for (final template in templates) {
        // Add the variable using template information
        _addVariableFromTemplate(template);
      }
    }
  }

  /// Add a variable from a template match
  void _addVariableFromTemplate(TemplateMatch template) {
    // Create a variable using Variable.createFromName which now handles entity creation
    final variable = Variable.createFromName(
      template.name,
      explicitType: template.type,
      fullTemplate: template.fullMatch,
      startPosition: template.startPosition,
      endPosition: template.endPosition,
      prefix: template.prefix,
    );

    _addVariableIfNotDuplicate(variable);
  }

  /// Add a variable from just a name (used for predefined variables)
  void _addVariableFromName(String name) {
    // Create a variable from name - will create entity if needed
    final variable = Variable.createFromName(name);

    _addVariableIfNotDuplicate(variable);
  }

  /// Helper to check for duplicates and add a variable if it's unique
  void _addVariableIfNotDuplicate(Variable variable) {
    // Check if we already have this variable (by canonical identity)
    bool isDuplicate = _variables.any((existing) => existing.hasSameIdentityAs(variable));

    if (isDuplicate) {
      appLog.debug(
          'Skipping duplicate variable: ${variable.name}${variable.prefix != null ? ' with prefix ${variable.prefix}' : ''}',
          name: _logName);
      return;
    }

    // Add the variable if it's not a duplicate
    _variables.add(variable);
    appLog.debug(
      'Added variable: ${variable.name}${variable.prefix != null ? ' with prefix ${variable.prefix}' : ''} '
      '(${variable.fullTemplate}) with canonical identity: "${variable.canonicalIdentity}"',
      name: _logName,
    );
  }

  /// Generate display text with human-readable placeholders
  void _generateDisplayText() {
    String result = _originalText;
    var textLength = result.length;

    appLog.debug('Generating display text from original text (length: $textLength)',
        name: _logName);

    // Sort variables by position in reverse order to avoid position shifts
    final sortedVariables = List.of(_variables)
      ..sort((a, b) {
        final posA = a.startPosition ?? 0;
        final posB = b.startPosition ?? 0;
        return posB.compareTo(posA);
      });

    // Replace templates with display text
    for (final variable in sortedVariables) {
      if (variable.startPosition != null && variable.endPosition != null) {
        // Validate positions are within bounds
        final start = variable.startPosition!;
        final end = variable.endPosition!;

        if (start < 0 || end > textLength || start >= end) {
          appLog.error(
            'Invalid template position for ${variable.name}: start=$start, end=$end, textLength=$textLength',
            name: _logName,
          );
          // Skip this variable to avoid range errors
          continue;
        }

        try {
          // Use prefixedDisplayName to include prefix in display
          result = TemplateUtils.replaceTemplateAtPosition(
            result,
            start,
            end,
            variable.prefixedDisplayName,
          );
          // Update text length after replacement
          textLength = result.length;
        } catch (e) {
          appLog.error(
            'Error replacing template ${variable.name}: $e',
            name: _logName,
          );
          // Continue with next variable
        }
      } else {
        // If no position info, try pattern replacement
        try {
          // Use prefixedDisplayName to include prefix in display
          result = result.replaceAll(variable.fullTemplate, variable.prefixedDisplayName);
          // Update text length after replacement
          textLength = result.length;
        } catch (e) {
          appLog.error(
            'Error replacing template pattern ${variable.fullTemplate}: $e',
            name: _logName,
          );
        }
      }
    }

    _displayText = result;
    appLog.debug('Generated display text: $_displayText', name: _logName);
  }

  /// Method called by ClientContextService when variable values change
  void notifyVariableValueChanged() {
    // Check if we need to add any variables that exist in ClientContextService but not in our list
    _syncVariablesWithClientContext();
    notifyListeners();
  }

  /// Synchronize variables with ClientContextService
  void _syncVariablesWithClientContext() {
    final variableValues = _clientContextService.getAllVariableValues();

    // Add any variables that exist in ClientContextService but not in our list
    for (final entry in variableValues.entries) {
      final variableName = entry.key;
      if (!_variables.any((v) => v.canonicalIdentity.toUpperCase() == variableName.toUpperCase())) {
        appLog.debug('Adding variable from ClientContextService: $variableName', name: _logName);
        _addVariableFromName(variableName);
      }
    }
  }

  /// Generate the resolved text by replacing variables with their values
  String _generateResolvedText() {
    appLog.debug('START: Generating resolved text', name: _logName);

    if (_originalText.isEmpty) {
      appLog.debug('Original text is empty, resolved text set to empty', name: _logName);
      return '';
    }

    String result = _clientContextService.transformTitleWithAllValues(_originalText);
    appLog.debug('END: Transformed text: $result', name: _logName);
    return result;
  }

  /// Method to set the value for a variable and update the resolved text
  /// This is a more streamlined approach than using setEntityVariableAndTransform directly
  String setVariableAndTransform(Variable variable, String value, String templateText) {
    appLog.debug('Setting variable value and transforming text: ${variable.name} = $value',
        name: _logName);

    // For custom variables, use the standard variable value setting
    _clientContextService.setVariableValue(variable.name, value);

    // Then transform the text with all values
    final transformedText = _clientContextService.transformTitleWithAllValues(templateText);
    return transformedText;
  }
}

/// A custom map implementation that normalizes variable names during lookup
class _NormalizedVariableMap implements Map<String, String> {
  final ClientContextService _clientContextService;

  _NormalizedVariableMap(this._clientContextService);

  @override
  String? operator [](Object? key) {
    if (key is String) {
      return _clientContextService.getVariableValue(key);
    }
    return null;
  }

  @override
  void operator []=(String key, String value) {
    // This map is read-only
    throw UnsupportedError('Cannot modify variables through this map view');
  }

  @override
  void clear() {
    // This map is read-only
    throw UnsupportedError('Cannot modify variables through this map view');
  }

  @override
  Iterable<String> get keys {
    // Return the keys from the underlying map
    // This is an approximation since we don't know all possible keys
    return _clientContextService.variableValues.keys;
  }

  @override
  String? remove(Object? key) {
    // This map is read-only
    throw UnsupportedError('Cannot modify variables through this map view');
  }

  @override
  bool containsKey(Object? key) {
    if (key is String) {
      return _clientContextService.variableValues[key] != null;
    }
    return false;
  }

  @override
  bool containsValue(Object? value) {
    return _clientContextService.variableValues.containsValue(value);
  }

  @override
  void forEach(void Function(String key, String value) action) {
    _clientContextService.variableValues.forEach(action);
  }

  @override
  bool get isEmpty => _clientContextService.variableValues.isEmpty;

  @override
  bool get isNotEmpty => _clientContextService.variableValues.isNotEmpty;

  @override
  int get length => _clientContextService.variableValues.length;

  @override
  Iterable<String> get values => _clientContextService.variableValues.values;

  @override
  Map<K2, V2> cast<K2, V2>() {
    throw UnsupportedError('Cannot cast this map view');
  }

  @override
  void addAll(Map<String, String> other) {
    // This map is read-only
    throw UnsupportedError('Cannot modify variables through this map view');
  }

  @override
  void addEntries(Iterable<MapEntry<String, String>> newEntries) {
    // This map is read-only
    throw UnsupportedError('Cannot modify variables through this map view');
  }

  @override
  Map<RK, RV> map<RK, RV>(MapEntry<RK, RV> Function(String key, String value) convert) {
    return _clientContextService.variableValues.map(convert);
  }

  @override
  String putIfAbsent(String key, String Function() ifAbsent) {
    // This map is read-only
    throw UnsupportedError('Cannot modify variables through this map view');
  }

  @override
  void removeWhere(bool Function(String key, String value) test) {
    // This map is read-only
    throw UnsupportedError('Cannot modify variables through this map view');
  }

  @override
  String update(String key, String Function(String value) update, {String Function()? ifAbsent}) {
    // This map is read-only
    throw UnsupportedError('Cannot modify variables through this map view');
  }

  @override
  void updateAll(String Function(String key, String value) update) {
    // This map is read-only
    throw UnsupportedError('Cannot modify variables through this map view');
  }

  @override
  Iterable<MapEntry<String, String>> get entries => _clientContextService.variableValues.entries;
}
