import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:promz/core/providers/database_provider.dart';
import 'package:promz/core/providers/license_provider.dart';
import 'package:promz/core/providers/service_providers.dart';
import 'package:promz/core/providers/shared_content_provider.dart';
import 'package:promz/core/providers/supabase_auth_provider.dart';
import 'package:promz/core/services/client_context_service.dart';
import 'package:promz/core/services/secure_storage_service.dart';
import 'package:promz/core/utils/safe_notifier.dart';
import 'package:promz/database/database.dart';
import 'package:promz/features/account/providers/settings_provider.dart';
import 'package:promz/features/account/views/account_view.dart';
import 'package:promz/features/account/widgets/authentication_dialog.dart';
import 'package:promz/features/account/widgets/upgrade_dialog.dart';
import 'package:promz/features/discover/views/discover_view.dart';
import 'package:promz/features/home/<USER>/extracted_keyword.dart';
import 'package:promz/features/home/<USER>/variables_dialog_result.dart';
import 'package:promz/features/home/<USER>/entity_detection_service.dart';
import 'package:promz/features/home/<USER>/prompt_suggestion_service.dart';
import 'package:promz/features/home/<USER>/text_analysis_service.dart';
import 'package:promz/features/home/<USER>/home_page.dart';
import 'package:promz/features/home/<USER>/widgets/variables/variables_dialog.dart';
import 'package:promz/features/home/<USER>/widgets/prompt_execution_dialog.dart';
import 'package:promz/features/input_selection/models/input_source.dart';
import 'package:promz/features/llm/services/llm_execution_service.dart';
import 'package:promz/features/portfolio/views/portfolio_view.dart';
import 'package:promz_common/promz_common.dart';

final homeViewModelProvider = ChangeNotifierProvider<HomeViewModel>((ref) {
  return HomeViewModel(
    ref: ref,
    database: ref.watch(databaseProvider.future),
  );
});

class HomeViewModel extends ChangeNotifier with SafeNotifier {
  static const _logName = 'HomeViewModel';
  Timer? _popularPromptsTimer;
  ProviderSubscription? _sharedContentSubscription;
  StreamSubscription? _sourceProcessedSubscription;
  final bool _enableAttachmentRegistryListener;

  final Ref _ref;
  final Future<AppDatabase> _database;

  // Services that depend on ClientContextService (can be null if init fails)
  ClientContextService? _clientContextService;
  PromptSuggestionService? _promptSuggestionService;
  EntityDetectionService? _entityDetectionService;
  final _textAnalysisService = TextAnalysisService();
  final _llmExecutionService = LlmExecutionService();

  HomeViewModel({
    required Ref ref,
    required Future<AppDatabase> database,
    bool enableAttachmentRegistryListener = true,
  })  : _ref = ref,
        _database = database,
        _enableAttachmentRegistryListener = enableAttachmentRegistryListener {
    _initializeClientContextServices();

    // Load popular prompts immediately if ClientContextService is available
    if (_promptSuggestionService != null) {
      getPopularPrompts();
      _startPeriodicPopularPromptsFetch();
    }

    // Set up listeners that don't depend on ClientContextService
    _listenToSharedContent();
  }

  /// Initialize services from ClientContextService when it becomes available
  Future<void> _initializeClientContextServices() async {
    // First try to get ClientContextService synchronously
    final clientContextAsync = _ref.read(clientContextServiceProvider);

    if (clientContextAsync.hasValue) {
      _setupWithClientContext(clientContextAsync.value!);
    } else {
      appLog.warning(
        'HomeViewModel initialized before ClientContextService was ready. State: $clientContextAsync',
        name: _logName,
      );

      try {
        // Wait for ClientContextService to be ready
        final clientContext = await _ref.read(clientContextServiceProvider.future);
        _setupWithClientContext(clientContext);

        // Now that services are initialized, load prompts
        getPopularPrompts();
        _startPeriodicPopularPromptsFetch();

        appLog.debug('ClientContextService became available, services initialized', name: _logName);
      } catch (e) {
        appLog.error('Failed to initialize with ClientContextService', name: _logName, error: e);
      }
    }
  }

  /// Set up services with initialized ClientContextService
  void _setupWithClientContext(ClientContextService clientContext) {
    _clientContextService = clientContext;
    _promptSuggestionService = _clientContextService?.promptSuggestion;
    _entityDetectionService = _clientContextService?.entityDetection;

    // Initialize listeners that depend on ClientContextService
    if (_enableAttachmentRegistryListener) {
      _listenToAttachmentRegistry();
    }
    _listenToSourceProcessed();
  }

  void _startPeriodicPopularPromptsFetch() {
    // Fetch popular prompts every 24 hours (adjust as needed)
    _popularPromptsTimer = Timer.periodic(const Duration(hours: 24), (timer) {
      getPopularPrompts();
    });
  }

  @override
  void dispose() {
    _popularPromptsTimer?.cancel();
    _sharedContentSubscription?.close();
    _sourceProcessedSubscription?.cancel();
    _clientContextService?.attachmentRegistry.removeListener(_onAttachmentRegistryUpdated);
    super.dispose();
  }

  /// Listen to attachment registry updates to add processed content to sources
  void _listenToAttachmentRegistry() {
    appLog.debug('Setting up attachment registry listener', name: _logName);

    // Set up a listener for the attachment registry
    _clientContextService?.attachmentRegistry.addListener(_onAttachmentRegistryUpdated);

    // Force an initial update to ensure any existing attachments are processed
    _onAttachmentRegistryUpdated();
  }

  /// Listen to source processed events from ContentProcessingService
  void _listenToSourceProcessed() {
    appLog.debug('Setting up source processed event listener', name: _logName);
    _sourceProcessedSubscription =
        _clientContextService?.contentProcessingService.sourceProcessed.listen(_onSourceProcessed);
  }

  /// Handle source processed events
  void _onSourceProcessed(InputSource source) {
    appLog.debug('Source processed event received: ${source.fileName}', name: _logName);

    // For non-news sources, use the standard duplicate check
    bool isDuplicate = _clientContextService?.contentProcessingService
            .checkForDuplicate(source.filePath, source.contentHash, _sources) ??
        false;

    // Handle nullable bool
    if (!isDuplicate) {
      _sources.add(source);
      safeNotify();

      // Analyze text if there's content
      if (source.content != null && source.content!.isNotEmpty) {
        analyzeText();
      }
    } else {
      appLog.debug('Duplicate source detected, not adding: ${source.fileName}', name: _logName);
    }
  }

  // Callback for attachment registry updates
  void _onAttachmentRegistryUpdated() {
    appLog.debug('Attachment registry updated', name: _logName);

    // Check if any sources need to be updated with new metadata
    final attachmentRegistry = _clientContextService?.attachmentRegistry;
    if (attachmentRegistry == null) return;

    bool sourcesUpdated = false;

    // Get all attachments from the registry
    final allAttachments = attachmentRegistry.getAllServerAttachments();
    appLog.debug('Found ${allAttachments.length} attachments and ${_sources.length} sources',
        name: _logName);

    // Update sources with the latest metadata from the attachment registry
    for (int i = 0; i < _sources.length; i++) {
      final source = _sources[i];

      // Skip sources without processing results
      if (source.processingResult == null) continue;

      // Look for this source in the attachment registry by job ID
      final jobId = source.processingResult!.jobId;
      if (jobId.isEmpty) continue;

      // Find the matching attachment in the registry
      final latestResult = allAttachments.firstWhere(
        (attachment) => attachment.jobId == jobId,
        orElse: () => source.processingResult!,
      );

      // Check if the status or metadata has changed
      final currentStatus = source.processingResult!.status;
      final latestStatus = latestResult.status;

      // Check if WhatsApp metadata has been updated
      bool whatsAppMetadataChanged = false;
      if (latestResult.hasWhatsappMetadata()) {
        if (!source.processingResult!.hasWhatsappMetadata()) {
          whatsAppMetadataChanged = true;
        } else {
          final currentMetadata = source.processingResult!.whatsappMetadata;
          final latestMetadata = latestResult.whatsappMetadata;

          whatsAppMetadataChanged = currentMetadata.messageCount != latestMetadata.messageCount ||
              currentMetadata.participantCount != latestMetadata.participantCount ||
              currentMetadata.isGroupChat != latestMetadata.isGroupChat;
        }
      }

      // Update source if status or metadata has changed
      if (currentStatus != latestStatus || whatsAppMetadataChanged) {
        appLog.debug('Updating source ${source.fileName} with latest metadata', name: _logName);
        appLog.debug('Status changed from $currentStatus to $latestStatus', name: _logName);

        // Update the source with the latest processing result
        _sources[i] = source.copyWith(processingResult: latestResult);
        sourcesUpdated = true;

        // Force a rebuild of the UI
        appLog.debug('Forcing UI update for source ${source.fileName}', name: _logName);
        safeNotify();
      }
    }

    // Notify listeners if any sources were updated
    if (sourcesUpdated) {
      appLog.debug('Sources updated with latest metadata, notifying listeners', name: _logName);
      safeNotify();
    }
  }

  /// Listen to shared content events from the SharedContentHandler
  void _listenToSharedContent() {
    appLog.debug('Setting up shared content listener', name: _logName);

    // Listen to the shared content provider directly
    _sharedContentSubscription = _ref.listen<AsyncValue<InputSource>>(
      sharedContentStreamProvider,
      (previous, next) {
        // Only process when we have data (not loading or error)
        next.whenData((receivedSource) {
          appLog.debug('Received shared content notification for: ${receivedSource.filePath}',
              name: _logName);

          // Create a new InputSource with additional metadata
          final source = InputSource(
            filePath: receivedSource.filePath,
            fileName: receivedSource.fileName,
            content: receivedSource.content,
            mimeType: receivedSource.mimeType,
            sourceApp: 'External app',
            // Use the type from receivedSource
            type: receivedSource.type,
            contentHash: receivedSource.contentHash,
            // Preserve the full metadata if available
            processingResult: receivedSource.processingResult,
          );

          // Log the source type and metadata for debugging
          appLog.debug('Created source with type: ${source.type}, fileName: ${source.fileName}',
              name: _logName);
          if (source.processingResult != null) {
            appLog.debug('Source processingResult: ${source.processingResult!.toString()}',
                name: _logName);
          }

          // Add the source directly without reprocessing
          _sources.add(source);
          safeNotify();

          // Analyze the new content
          analyzeText();

          appLog.debug('Successfully added shared content from stream', name: _logName);
        });

        // Handle errors
        next.whenOrNull(error: (error, stackTrace) {
          appLog.error('Error in shared content stream',
              name: _logName, error: error, stackTrace: stackTrace);
        });
      },
    );
  }

  String _inputText = '';
  final List<InputSource> _sources = [];
  List<ExtractedKeyword> _extractedKeywords = [];

  // State variables
  bool _isKeywordSectionExpanded = false;
  bool _isAnalyzing = false;
  bool _isLoadingSuggestions = false;
  bool _isLoadingPopularPrompts = false;
  int _currentNavIndex = 0;
  final bool _isProcessingSources = false;

  bool _isExecutingLlm = false;
  String? _executionError;
  LlmExecuteResponse? _llmResponse;

  // Prompt suggestion lists
  List<DisplayItem> _suggestedPrompts = [];
  List<DisplayItem> _popularPrompts = [];

  // Background authentication check state
  bool _isRunningBackgroundAuthCheck = false;
  bool get isRunningBackgroundAuthCheck => _isRunningBackgroundAuthCheck;

  // Sources for the current session
  List<InputSource> get sources => _sources;

  // Track loading state by source ID (file path or content hash)
  final Map<String, bool> _sourceLoadingStates = {};
  Map<String, bool> get sourceLoadingStates => _sourceLoadingStates;

  // Getters
  String get inputText => _inputText;
  List<ExtractedKeyword> get extractedKeywords => _extractedKeywords;
  bool get isKeywordSectionExpanded => _isKeywordSectionExpanded;
  bool get isAnalyzing => _isAnalyzing;
  bool get isLoadingSuggestions => _isLoadingSuggestions;
  int get currentNavIndex => _currentNavIndex;
  bool get isProcessingSources => _isProcessingSources;
  bool get canAnalyze => _inputText.isNotEmpty || _sources.isNotEmpty;
  bool get isExecutingLlm => _isExecutingLlm;
  String? get executionError => _executionError;
  LlmExecuteResponse? get llmResponse => _llmResponse;
  ClientContextService? get clientContextService => _clientContextService;
  bool get hasExecutionResult => _llmResponse != null || _executionError != null;

  // Getters for multiple prompt lists
  List<DisplayItem> get suggestedPrompts => _suggestedPrompts;
  List<DisplayItem> get popularPrompts => _popularPrompts;
  bool get isLoadingPopularPrompts => _isLoadingPopularPrompts;

  // Methods for state updates
  void updateInputText(String text) {
    appLog.debug('updateInputText() START - length: ${text.length}', name: _logName);
    _inputText = text;
    safeNotify();

    // Don't automatically trigger analysis for very short text
    if (text.trim().length > 3) {
      appLog.debug('Text length > 3, triggering analysis from updateInputText', name: _logName);
      // Schedule analysis on the next frame to ensure UI updates first
      Future.microtask(() => analyzeText());
    }
  }

  void removeSource(InputSource source) {
    appLog.debug('removeSource() START - source: ${source.fileName}', name: _logName);
    try {
      _sources.remove(source);

      if (_sources.isEmpty && _inputText.isEmpty) {
        _extractedKeywords = [];
        _suggestedPrompts = [];
        _isKeywordSectionExpanded = false;
      } else {
        analyzeText();
      }

      safeNotify();
      appLog.debug('removeSource() END - success', name: _logName);
    } catch (e, stackTrace) {
      appLog.error('removeSource() END - Error', name: _logName, error: e, stackTrace: stackTrace);
    }
  }

  Future<void> analyzeText() async {
    appLog.debug('analyzeText() START', name: _logName);
    if (_inputText.isEmpty && _sources.isEmpty) {
      appLog.debug('analyzeText() END - No content to analyze', name: _logName);
      return;
    }

    _isAnalyzing = true;
    _isLoadingSuggestions = true;
    safeNotify();

    try {
      List<ExtractedKeyword> allKeywords = [];

      if (_inputText.isNotEmpty) {
        appLog.debug('Processing manual input', name: _logName);
        final manualKeywords = await _textAnalysisService.extractKeywords(_inputText);
        final scores = await _textAnalysisService.getKeywordScores(_inputText);
        allKeywords.addAll(
          manualKeywords.map((text) => ExtractedKeyword(
                text: text,
                relevance: scores[text] ?? 0.5,
                source: 'manual input',
              )),
        );
      }

      // Create a copy of the sources list to avoid concurrent modification
      final sourcesToProcess = List<InputSource>.from(_sources);
      for (final source in sourcesToProcess) {
        if (source.content == null || source.content!.isEmpty) {
          appLog.debug('Skipping empty source: ${source.fileName}', name: _logName);
          continue;
        }

        appLog.debug('Processing source: ${source.fileName}', name: _logName);

        final sourceKeywords = await _textAnalysisService.extractKeywords(source.content!);
        final scores = await _textAnalysisService.getKeywordScores(source.content!);

        allKeywords.addAll(
          sourceKeywords.map((text) => ExtractedKeyword(
                text: text,
                relevance: scores[text] ?? 0.5,
                source: source.fileName ?? source.sourceApp ?? 'unknown source',
              )),
        );
      }

      _extractedKeywords = _removeDuplicateKeywords(allKeywords);
      _isKeywordSectionExpanded = false;

      if (_extractedKeywords.isNotEmpty) {
        await _updatePromptSuggestions();
      }

      appLog.debug('analyzeText() END - Found ${_extractedKeywords.length} keywords',
          name: _logName);
    } catch (e, stackTrace) {
      appLog.error('Error analyzing content', name: _logName, error: e, stackTrace: stackTrace);
      _extractedKeywords = [];
      _suggestedPrompts = [];
    } finally {
      _isAnalyzing = false;
      _isLoadingSuggestions = false;
      safeNotify();
    }
  }

  List<ExtractedKeyword> _removeDuplicateKeywords(List<ExtractedKeyword> keywords) {
    final keywordScores = <String, double>{};
    final keywordSources = <String, Set<String>>{};
    final originalScores = <String, double>{};

    // First pass: collect scores and sources
    for (var keyword in keywords) {
      final key = keyword.text.toLowerCase();
      // Keep track of the highest original score
      originalScores[key] = (originalScores[key] ?? 0.0).clamp(0.0, 1.0);
      if (keyword.relevance > (originalScores[key] ?? 0.0)) {
        originalScores[key] = keyword.relevance;
      }
      // Accumulate scores
      keywordScores[key] = (keywordScores[key] ?? 0.0) + keyword.relevance;
      keywordSources.putIfAbsent(key, () => {}).add(keyword.source ?? 'unknown');
    }

    // Second pass: create combined keywords with adjusted scores
    final combinedKeywords = keywordScores.entries.map((entry) {
      final sourceCount = keywordSources[entry.key]?.length ?? 1;
      // Base the score on both original relevance and source count
      final baseScore = originalScores[entry.key] ?? 0.5;
      final adjustedScore = (baseScore + (sourceCount - 1) * 0.2).clamp(0.0, 1.0);

      return ExtractedKeyword(
        text: keywords.firstWhere((k) => k.text.toLowerCase() == entry.key).text,
        relevance: adjustedScore,
        source: keywordSources[entry.key]?.join(', '),
      );
    }).toList();

    // Sort by score and take top 10
    combinedKeywords.sort((a, b) => b.relevance.compareTo(a.relevance));
    return combinedKeywords.take(10).toList();
  }

  Future<void> _updatePromptSuggestions() async {
    try {
      appLog.debug('START: Prompt suggestions update', name: _logName);
      _isLoadingSuggestions = true;
      safeNotify();

      final keywords = _extractedKeywords.map((k) => k.text).toList();
      appLog.debug('Processing keywords: ${keywords.join(", ")}', name: _logName);

      appLog.debug('Fetching prompt suggestions from service...', name: _logName);
      _suggestedPrompts = await _promptSuggestionService?.getPromptSuggestions(keywords) ?? [];
      appLog.debug('Received ${_suggestedPrompts.length} prompt suggestions', name: _logName);

      // Log each suggestion for debugging
      for (var suggestedItem in _suggestedPrompts) {
        appLog.debug(
            'PromptSuggestion: ${suggestedItem.displayText} (Score: ${suggestedItem.relevanceScore})',
            name: _logName);
      }
    } catch (e, stack) {
      appLog.error('Error updating prompt suggestions',
          name: _logName, error: e, stackTrace: stack);
      _suggestedPrompts = [];
    } finally {
      _isLoadingSuggestions = false;
      safeNotify();
    }
  }

  void removeKeyword(ExtractedKeyword keyword) {
    _extractedKeywords.remove(keyword);
    safeNotify();
    _updatePromptSuggestions();
  }

  void toggleKeywordSection() {
    _isKeywordSectionExpanded = !_isKeywordSectionExpanded;
    safeNotify();
  }

  void updateNavIndex(int index) {
    _currentNavIndex = index;
    safeNotify();
  }

  void navigateToPage(BuildContext context, int index) {
    // If we're already on this page, do nothing
    if (index == _currentNavIndex) {
      return;
    }

    // Update the current index
    _currentNavIndex = index;
    safeNotify();

    // Navigate to the appropriate page
    Widget page;
    switch (index) {
      case 0:
        page = const HomePage();
        break;
      case 1:
        page = const DiscoverView();
        break;
      case 2:
        page = const PortfolioView();
        break;
      case 3:
        page = const AccountView();
        break;
      default:
        page = const HomePage();
    }

    // Use pushAndRemoveUntil to clear the navigation stack
    // This ensures we don't have multiple instances of the same page
    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(
        builder: (context) => page,
      ),
      (route) => false, // Remove all previous routes
    );
  }

  Future<bool> confirmPromptExecution(BuildContext context, DisplayItem displayItem) async {
    // Use the new PromptExecutionDialog instead of inline dialog implementation
    return await PromptExecutionDialog.show(context, displayItem, _sources);
  }

  Future<void> loadProfileData() async {
    await _clientContextService?.userProfile.refreshProfile(forceFresh: true);
  }

  /// Check if user is authenticated and has a valid license
  Future<bool> _checkApiKeyAndPrompt(BuildContext context) async {
    return await AuthenticationDialog.checkAuthAndShowDialogIfNeeded(
      context,
      clientContextService: _clientContextService,
    );
  }

  /// Check for confirmation based on settings
  Future<bool> _checkConfirmation(
    BuildContext context,
    DisplayItem displayItem, {
    bool skipConfirmation = false,
  }) async {
    // If skipConfirmation is true, bypass the confirmation check
    if (skipConfirmation) {
      appLog.debug('Skipping confirmation dialog as requested', name: _logName);
      return true;
    }

    final settings = _ref.read(settingsProvider);
    if (settings.confirmPromptExecution) {
      return await confirmPromptExecution(context, displayItem);
    }
    return true;
  }

  /// Check for variables and entities in the prompt
  Future<VariablesDialogResult> _checkVariablesAndEntities(
      BuildContext context, DisplayItem displayItem,
      {VoidCallback? onDialogShown}) async {
    appLog.debug('START: Checking for variables and entities in prompt', name: _logName);

    // Collect all texts to check for entities
    final List<String> textsToCheck = [
      _inputText,
      displayItem.displayText,
      displayItem.subtitle ?? '',
      displayItem.prompt?.title ?? '',
      displayItem.prompt?.subtitle ?? '',
    ].where((text) => text.isNotEmpty).toList();

    // Detect entities in all texts
    final allDetectedEntities = <Entity>[];
    for (final text in textsToCheck) {
      final entities = await _entityDetectionService?.detectEntities(text) ?? [];
      allDetectedEntities.addAll(entities);
    }

    // Update client context with all detected entities
    _clientContextService?.updateEntities(allDetectedEntities);
    appLog.debug('Updated client context with entities: $allDetectedEntities', name: _logName);

    // Initialize variable manager with the prompt model
    final variableManager = _clientContextService?.variableManager;
    variableManager?.initWithPrompt(displayItem.prompt);

    // Check if we have variables that need values; show the dialog until all variables are resolved
    // or user cancels but not more than maxAttempts times to prevent infinite loops
    int attemptCount = 0;
    const maxAttempts = 3;

    // Check variableManager existence before loop condition
    while (variableManager != null &&
        variableManager.hasVariables &&
        !variableManager.allVariablesResolved &&
        attemptCount < maxAttempts) {
      appLog.debug(
          'Variables need values: ${variableManager.variables.length} variables, ${variableManager.resolvedVariableCount} resolved',
          name: _logName);

      // Store context in local variable to avoid using it after async gap
      final currentContext = context;

      // Check if context is still valid (widget is mounted)
      if (!currentContext.mounted) {
        appLog.warning('END: Context is no longer valid, canceling variables dialog',
            name: _logName);
        return VariablesDialogResult.canceled();
      }

      // Notify that we're showing the dialog
      onDialogShown?.call();

      // Show variables dialog with displayItem for context
      final result = await VariablesDialog.show(
        currentContext,
        _clientContextService!, // Pass the service (assert non-null)
        variableManager, // Pass the manager (guaranteed non-null by loop)
        promptDisplayItem: displayItem, // Pass display item for context
        onVariablesChanged: () {
          // Update the input text with resolved variables
          _inputText = variableManager.resolvedText;
          safeNotify();
        },
      );

      // If user cancelled (result is null), return canceled result
      if (result == null) {
        appLog.debug('END: User cancelled variables dialog', name: _logName);
        return VariablesDialogResult.canceled();
      }

      // Check if all variables are now resolved
      if (variableManager.allVariablesResolved) {
        appLog.debug('END: All variables resolved successfully', name: _logName);
        return result; // Return the result with execute flag as set by dialog
      }

      // If we get here, variables are still not resolved
      attemptCount++;

      // If we've reached max attempts, show a warning
      if (attemptCount >= maxAttempts) {
        appLog.debug('END: Max attempts reached for variable resolution', name: _logName);

        // Show a warning to the user
        if (context.mounted) {
          await showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('Missing Variables'),
              content: const Text(
                  'Some variables are still missing values. Please fill in all required variables.'),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('OK'),
                ),
              ],
            ),
          );
        }
        return VariablesDialogResult.canceled();
      }
    }

    // If we don't have variables or they're all resolved, return a default "proceed" result
    appLog.debug('END: No variables or all variables resolved', name: _logName);
    return VariablesDialogResult.notShown();
  }

  /// Execute the prompt with the LLM service
  Future<void> _executePrompt(BuildContext context, DisplayItem displayItem) async {
    appLog.debug('START: Executing prompt: ${displayItem.displayText}', name: _logName);

    try {
      // Prepare source content from _sources
      final List<SourceContent> sourceContentList = [];
      if (_sources.isNotEmpty) {
        for (var source in _sources) {
          sourceContentList.add(SourceContent(
            type: source.type == InputSourceType.file ? 'file' : 'text',
            content: source.content ?? '',
            fileName: source.fileName ?? '',
            contentType: source.mimeType ?? '',
          ));
        }
      }

      // Process variables if available
      Map<String, dynamic>? variables;
      appLog.debug('Processing variables for prompt execution', name: _logName);

      // Get all variable values, including attachment variables
      final values = _clientContextService?.getAllVariableValues();

      // Check for null before accessing properties
      if (values != null && values.isNotEmpty) {
        variables = <String, dynamic>{};

        // Use null-aware access for entries
        for (final entry in values.entries) {
          variables[entry.key] = entry.value;
        }
        appLog.debug('Sending ${variables.length} variables to LLM: ${variables.keys.join(", ")}',
            name: _logName);
      }

      // Create the request with variables
      final request = LlmExecuteRequest(
        promptId: displayItem.prompt?.id,
        promptContent: _inputText, // Use the processed content with replaced variables
        sourceContent: sourceContentList, // List of source content from _sources
        variables: variables, // Variables including attachment variables
      );

      // Execute the request
      final response = await _llmExecutionService.executePrompt(request);
      _llmResponse = response;

      // Update prompt usage stats
      if (displayItem.prompt != null) {
        // Update the last_used_at timestamp in the local database
        updatePromptUsage(displayItem.prompt!.id);

        // Record that this prompt was executed for popularity tracking
        try {
          // Use null-aware access
          await _clientContextService?.recordPromptExecuted(displayItem.prompt!.id);
        } catch (e) {
          // Log but continue - this shouldn't block the main flow
          appLog.warning('Failed to record prompt execution: $e', name: _logName);
        }
      }

      appLog.debug('END: Prompt execution completed successfully', name: _logName);
    } catch (e, stack) {
      appLog.error('Error executing prompt', name: _logName, error: e, stackTrace: stack);

      // Handle specific error types for proper UI feedback
      if (e is NoEligibleModelException && context.mounted) {
        // Show upgrade dialog for model tier restrictions
        appLog.debug('Showing upgrade dialog for NoEligibleModelException', name: _logName);
        _showUpgradeDialog(context, e);
      } else {
        // Store general error message
        _executionError = e.toString();
      }
    }
  }

  // Add this new method to show the upgrade dialog
  void _showUpgradeDialog(BuildContext context, NoEligibleModelException error) {
    showDialog(
      context: context,
      builder: (context) => UpgradeDialog(error: error),
    );
  }

  Future<void> onPromptSelected(BuildContext context, DisplayItem displayItem) async {
    appLog.debug('START: onPromptSelected() prompt: ${displayItem.displayText}', name: _logName);

    // Record that this prompt was selected for popularity tracking
    if (displayItem.prompt != null) {
      try {
        // Use null-aware access
        await _clientContextService?.recordPromptSelected(displayItem.prompt!.id);
      } catch (e) {
        // Log but continue - this shouldn't block the main flow
        appLog.warning('Failed to record prompt selection: $e', name: _logName);
      }
    }

    // Check if context is still mounted
    if (!context.mounted) {
      appLog.debug('1. Context no longer mounted, skipping prompt execution', name: _logName);
      return;
    }

    // Step 1: Check authentication
    final hasApiKey = await _checkApiKeyAndPrompt(context);
    if (!hasApiKey) {
      return;
    }

    // Check if context is still mounted
    if (!context.mounted) {
      appLog.debug('2. Context no longer mounted, skipping prompt execution', name: _logName);
      return;
    }

    // Step 2: Check variables and entities
    final variablesResult = await _checkVariablesAndEntities(context, displayItem);
    if (!variablesResult.resolved || !context.mounted) {
      appLog.debug('Variables not resolved or context no longer mounted', name: _logName);
      return;
    }

    // Handle the different dialog states explicitly
    switch (variablesResult.state) {
      case VariablesDialogState.canceled:
        appLog.debug('Variables dialog was canceled', name: _logName);
        return;

      case VariablesDialogState.saved:
        appLog.debug('Variables were saved but execution not requested', name: _logName);
        return;

      case VariablesDialogState.notShown:
        appLog.debug('Variables dialog was not shown (no variables or all resolved)',
            name: _logName);
        // Continue to confirmation dialog
        break;

      case VariablesDialogState.execute:
        appLog.debug('Variables were saved and execution requested', name: _logName);
        // Continue to confirmation dialog with skipConfirmation=true
        break;
    }

    // Step 3: Ask for confirmation if needed
    final shouldProceed = await _checkConfirmation(
      context,
      displayItem,
      // Skip confirmation if we came from the Variables Dialog with Execute button
      skipConfirmation: variablesResult.execute,
    );

    if (!shouldProceed) {
      appLog.debug('User cancelled prompt execution', name: _logName);
      return;
    }

    // Step 4: Show loading indicator
    _isExecutingLlm = true;
    _executionError = null;
    _llmResponse = null;
    safeNotify();

    // Check if context is still mounted
    if (!context.mounted) {
      appLog.debug('5. Context no longer mounted, skipping prompt execution', name: _logName);
      return;
    }

    // Step 5: Execute the prompt with context parameter
    await _executePrompt(context, displayItem);

    // Step 6: Update UI state
    _isExecutingLlm = false;
    safeNotify();
  }

  Future<void> updatePromptUsage(String promptId) async {
    try {
      final database = await _database;
      await database.updatePromptLastUsed(promptId);
    } catch (e, stack) {
      appLog.error('Error updating prompt last_used_at',
          name: _logName, error: e, stackTrace: stack);
    }
  }

  void clearExecutionResults() {
    _llmResponse = null;
    _executionError = null;
    safeNotify();
  }

  @visibleForTesting
  bool bypassDuplicateChecking = false;

  set isAnalyzing(bool value) {
    _isAnalyzing = value;
    safeNotify();
  }

  Future<void> addClipboardSource(InputSource clipboardSource) async {
    appLog.debug('addClipboardSource() START - source: ${clipboardSource.fileName}',
        name: _logName);
    try {
      // If the source already has content, process it through the event-driven approach
      if (clipboardSource.content != null && clipboardSource.content!.isNotEmpty) {
        final isDuplicate = _clientContextService?.contentProcessingService.checkForDuplicate(
            clipboardSource.filePath ?? '', clipboardSource.contentHash ?? '', _sources);

        // Handle nullable bool
        if (!(isDuplicate ?? false)) {
          // Create a new source with the clipboard type
          final updatedSource = clipboardSource.copyWith(type: InputSourceType.clipboardText);

          // Ensure listeners are notified even if the source is processed internally
          // This keeps the UI consistent
          _clientContextService?.contentProcessingService.notifySourceProcessed(updatedSource);

          appLog.debug('addClipboardSource() END - emitted source processed event', name: _logName);
        } else {
          appLog.debug('addClipboardSource() END - duplicate detected', name: _logName);
        }
      }
      // Otherwise process it through the central method
      else if (clipboardSource.filePath != null) {
        final result = await _clientContextService?.contentProcessingService
            .processInput(clipboardSource.filePath!);
        if (result != null) {
          appLog.debug(
              'addClipboardSource() END - processed through central method: ${result.fileName}',
              name: _logName);
        } else {
          appLog.debug('addClipboardSource() END - processing failed', name: _logName);
        }
      } else {
        appLog.debug('addClipboardSource() END - no content or file path', name: _logName);
      }
    } catch (e, stackTrace) {
      appLog.error('addClipboardSource() END - Error',
          name: _logName, error: e, stackTrace: stackTrace);
    }
  }

  Future<bool> checkUserAuthentication(BuildContext context) async {
    final authState = _ref.read(authStateProvider).value;
    final userLicense = await _ref.read(userLicenseProvider.future);

    if (authState == null || !authState.isAuthenticated || !userLicense.hasLicense) {
      if (context.mounted) {
        Navigator.of(context).push(
          MaterialPageRoute(builder: (context) => const AccountView()),
        );
      }
      return false;
    }
    return true;
  }

  Future<void> loadAllPromptLists() async {
    await Future.wait([
      getSuggestedPrompts(),
      getPopularPrompts(),
    ]);
  }

  Future<void> getSuggestedPrompts() async {
    _isLoadingSuggestions = true;
    safeNotify();

    try {
      final keywords = extractKeywords(_inputText);
      _suggestedPrompts = await _promptSuggestionService?.getPromptSuggestions(keywords) ?? [];
    } catch (e, stack) {
      appLog.error('Error getting suggested prompts', name: _logName, error: e, stackTrace: stack);
      _suggestedPrompts = [];
    } finally {
      _isLoadingSuggestions = false;
      safeNotify();
    }
  }

  Future<void> getPopularPrompts() async {
    appLog.debug('getPopularPrompts() START in HomeViewModel', name: _logName);
    _isLoadingPopularPrompts = true;
    safeNotify();

    try {
      _popularPrompts = await _promptSuggestionService?.getPopularPrompts() ?? [];
      appLog.debug('Retrieved ${_popularPrompts.length} popular prompts in HomeViewModel',
          name: _logName);

      // Log the details of each popular prompt for debugging
      for (int i = 0; i < _popularPrompts.length; i++) {
        final prompt = _popularPrompts[i];
        appLog.debug('Popular prompt $i: ${prompt.displayText}', name: _logName);
      }
    } catch (e, stack) {
      appLog.error('Error getting popular prompts', name: _logName, error: e, stackTrace: stack);
      _popularPrompts = [];
    } finally {
      _isLoadingPopularPrompts = false;
      safeNotify();
    }
  }

  // Helper method to extract keywords for suggested prompts
  List<String> extractKeywords(String text) {
    // Simple implementation - split by space and take words longer than 3 chars
    return text.split(RegExp(r'[\s,.]')).where((word) => word.trim().length > 3).toList();
  }

  /// Run authentication check in background
  Future<void> runBackgroundAuthCheck(BuildContext context) async {
    if (_isRunningBackgroundAuthCheck) {
      appLog.debug('Background auth check already running, skipping', name: _logName);
      return;
    }

    appLog.debug('Starting background authentication check', name: _logName);
    _isRunningBackgroundAuthCheck = true;
    safeNotify();

    try {
      final supabaseService = _ref.read(supabaseServiceProvider);

      // Attempt to refresh session if needed
      final sessionRefreshed = await supabaseService.checkAndRefreshSession();

      if (sessionRefreshed) {
        appLog.debug('Session refreshed successfully', name: _logName);

        // Refresh auth state with the new session and await its completion to ensure consistency
        final _ = await _ref.refresh(authStateProvider.future);

        // Cache the current user profile information
        await supabaseService.cacheUserProfileInfo();

        // Important: Read the auth state AFTER the refresh has completed
        final refreshedAuthState = await _ref.read(authStateProvider.future);

        // Check for license validity - also await this to ensure we have latest data
        final userLicense = await _ref.read(userLicenseProvider.future);
        final authenticated = refreshedAuthState.isAuthenticated;

        appLog.debug(
            'Auth check result after refresh - isAuthenticated: $authenticated, hasLicense: ${userLicense.hasLicense}',
            name: _logName);

        // Update UI state based on the refreshed values
        if (!authenticated || !userLicense.hasLicense) {
          appLog.debug('Auth check failed after refresh, updating UI state only', name: _logName);
          await _incrementAuthFailureCount();
        }
      } else {
        appLog.debug('Session not refreshed - invalid or expired', name: _logName);

        // For non-refreshed sessions, use the current auth state
        final currentAuthState = await _ref.read(authStateProvider.future);
        final userLicense = await _ref.read(userLicenseProvider.future);
        final authenticated = currentAuthState.isAuthenticated;

        appLog.debug(
            'Auth check result - isAuthenticated: $authenticated, hasLicense: ${userLicense.hasLicense}',
            name: _logName);

        // Update UI state based on current values
        if (!authenticated || !userLicense.hasLicense) {
          appLog.debug('Auth check failed, updating UI state only', name: _logName);
          await _incrementAuthFailureCount();
        }
      }
    } catch (e) {
      appLog.error('Error in background auth check', name: _logName, error: e);
    } finally {
      _isRunningBackgroundAuthCheck = false;
      safeNotify();
    }
  }

  /// Track authentication failure count to potentially show reminder in the future
  Future<void> _incrementAuthFailureCount() async {
    try {
      // Store authentication failure count using the generic storage methods
      const countKey = 'auth_failure_count';
      const timestampKey = 'last_auth_failure_time';

      // Get current count
      final countString = await SecureStorageService.getValue(countKey) ?? '0';
      final count = int.tryParse(countString) ?? 0;

      // Save incremented count
      await SecureStorageService.saveValue(countKey, (count + 1).toString());

      // Also store the last time we had an auth failure
      await SecureStorageService.saveValue(
          timestampKey, DateTime.now().millisecondsSinceEpoch.toString());
    } catch (e) {
      appLog.error('Error tracking auth failure count', name: _logName, error: e);
    }
  }
}
