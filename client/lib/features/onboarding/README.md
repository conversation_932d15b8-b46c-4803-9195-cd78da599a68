# Onboarding Feature

This directory contains the implementation of the Promz app's onboarding experience.

## Overview

The onboarding feature provides new users with a guided introduction to the app's key features and benefits. It consists
of a series of screens that highlight the app's value proposition and unique capabilities.

## Components

- **OnboardingView**: The main widget that displays the onboarding screens using a PageView.
- **OnboardingPage**: A reusable widget for individual onboarding screens.
- **OnboardingService**: A service that manages the onboarding state and navigation logic.

## Usage

The onboarding flow is automatically shown to users on their first app launch. The app checks if the user has completed
onboarding using the `OnboardingService.hasCompletedOnboarding()` method.

```dart
// Check if the user has completed onboarding
final hasCompletedOnboarding = await OnboardingService.hasCompletedOnboarding();

// Show onboarding if not completed
if (!hasCompletedOnboarding) {
  Navigator.of(context).pushReplacement(
    MaterialPageRoute(builder: (context) => const OnboardingView()),
  );
}
```

## Customization

To modify the onboarding content:

1. Edit the individual page builder methods in `OnboardingView` (e.g., `_buildWelcomePage()`)
2. Update the images in `assets/images/onboarding/`
3. Adjust the total number of pages by changing the `_totalPages` constant

## Assets

The onboarding screens use the following image assets:

- `assets/images/onboarding/welcome.png`
- `assets/images/onboarding/curated_prompts.png`
- `assets/images/onboarding/community.png`
- `assets/images/onboarding/organize.png`

## Future Improvements

- Add animations to enhance the visual experience
- Implement A/B testing for different content variations
- Add analytics to track onboarding completion rates
- Support for tablet and desktop form factors
