import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:promz/core/utils/platform_utils.dart';
import 'package:promz/features/home/<USER>/home_page.dart';
import 'package:promz_common/promz_common.dart';

/// A view that displays the onboarding screens for first-time users.
///
/// This widget shows a series of screens that introduce the app's key features
/// and benefits, with options to navigate between screens or skip the onboarding
/// process entirely.
class OnboardingView extends ConsumerStatefulWidget {
  const OnboardingView({Key? key}) : super(key: key);

  @override
  ConsumerState<OnboardingView> createState() => _OnboardingViewState();
}

class _OnboardingViewState extends ConsumerState<OnboardingView> {
  static const String _logName = 'OnboardingView';
  final PageController _pageController = PageController();
  int _currentPage = 0;
  final int _totalPages = 2;

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  /// Mark onboarding as completed in SharedPreferences
  Future<void> _markOnboardingComplete() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('has_seen_onboarding', true);
      appLog.debug('Onboarding marked as complete', name: _logName);
    } catch (e) {
      appLog.error('Failed to mark onboarding as complete', name: _logName, error: e);
    }
  }

  /// Navigate to the next page or finish onboarding if on the last page
  void _nextPage() {
    if (_currentPage < _totalPages - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _finishOnboarding();
    }
  }

  /// Navigate to the previous page
  void _previousPage() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  /// Skip the onboarding process and go directly to the home page
  void _skipOnboarding() async {
    await _markOnboardingComplete();
    if (mounted) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(
          builder: (context) => const HomePage(),
          settings: const RouteSettings(name: '/home'),
        ),
      );
    }
  }

  /// Complete the onboarding process and navigate to the home page
  void _finishOnboarding() async {
    await _markOnboardingComplete();
    if (mounted) {
      // Navigate directly to the HomePage with a clean navigation stack
      Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(
          builder: (context) => const HomePage(),
          settings: const RouteSettings(name: '/home'),
        ),
        (route) => false, // Remove all previous routes
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            // Navigation buttons at the top
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Back button (only show on pages after the first)
                  _currentPage > 0
                      ? TextButton.icon(
                          onPressed: _previousPage,
                          icon: const Icon(Icons.arrow_back),
                          label: const Text(Strings.onboardingBack),
                        )
                      : const SizedBox(width: 80), // Empty space for alignment

                  // Skip button
                  TextButton(
                    onPressed: _skipOnboarding,
                    child: const Text(Strings.onboardingSkip),
                  ),
                ],
              ),
            ),

            // Page indicator
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 8.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(
                  _totalPages,
                  (index) => _buildPageIndicator(index == _currentPage),
                ),
              ),
            ),

            // Main content - wrap in SizedBox with defined height constraints
            Expanded(
              child: PageView(
                controller: _pageController,
                onPageChanged: (int page) {
                  setState(() {
                    _currentPage = page;
                  });
                },
                children: [
                  _buildWelcomePage(),
                  _buildCuratedPromptsPage(),
                ],
              ),
            ),

            // Navigation buttons with fixed height
            Padding(
              padding: const EdgeInsets.all(24.0),
              child: SizedBox(
                height: 50,
                child: _buildNavigationButton(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build a page indicator dot
  Widget _buildPageIndicator(bool isActive) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4.0),
      height: 8.0,
      width: isActive ? 24.0 : 8.0,
      decoration: BoxDecoration(
        color: isActive
            ? Theme.of(context).colorScheme.primary
            // ignore: deprecated_member_use
            : Theme.of(context).colorScheme.primary.withOpacity(0.3),
        borderRadius: BorderRadius.circular(4.0),
      ),
    );
  }

  /// Build the platform-specific navigation button
  Widget _buildNavigationButton() {
    final isLastPage = _currentPage == _totalPages - 1;
    final buttonText = isLastPage ? Strings.onboardingGetStarted : Strings.onboardingNext;
    final buttonWidth = MediaQuery.of(context).size.width * 0.8;

    return PlatformUtils.isApplePlatform
        ? CupertinoButton.filled(
            onPressed: _nextPage,
            padding: const EdgeInsets.symmetric(horizontal: 32.0),
            child: Text(buttonText),
          )
        : ElevatedButton(
            onPressed: _nextPage,
            style: ElevatedButton.styleFrom(
              minimumSize: Size(buttonWidth, 50),
            ),
            child: Text(buttonText),
          );
  }

  /// Build the welcome page (first screen)
  Widget _buildWelcomePage() {
    return const OnboardingPage(
      title: Strings.onboardingTitle1,
      description: Strings.onboardingDesc1,
      bullets: Strings.onboardingBullets1,
      imagePath: 'assets/images/onboarding/get-more-from-ai.png',
    );
  }

  /// Build the curated prompts page (second screen)
  Widget _buildCuratedPromptsPage() {
    return const OnboardingPage(
      title: Strings.onboardingTitle2,
      description: Strings.onboardingDesc2,
      bullets: Strings.onboardingBullets2,
      imagePath: 'assets/images/onboarding/sharing.png',
    );
  }
}

/// A reusable widget for individual onboarding pages
class OnboardingPage extends StatelessWidget {
  final String title;
  final String? subtitle;
  final String description;
  final List<String>? bullets;
  final String imagePath;

  const OnboardingPage({
    Key? key,
    required this.title,
    this.subtitle,
    required this.description,
    this.bullets,
    required this.imagePath,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Image with proper constraints
          ConstrainedBox(
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.3,
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 16.0),
              child: Image.asset(
                imagePath,
                fit: BoxFit.contain,
                errorBuilder: (context, error, stackTrace) {
                  // Fallback if image is not found
                  return Container(
                    height: MediaQuery.of(context).size.height * 0.2,
                    decoration: BoxDecoration(
                      color: theme.colorScheme.surfaceContainerHighest,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Center(
                      child: Icon(
                        Icons.image,
                        size: 80,
                        color: theme.colorScheme.primary,
                      ),
                    ),
                  );
                },
              ),
            ),
          ),

          // Text content - use fixed height constraints
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    title,
                    style: theme.textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.primary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  if (subtitle != null) ...[
                    const SizedBox(height: 8),
                    Text(
                      subtitle!,
                      style: theme.textTheme.titleLarge,
                      textAlign: TextAlign.center,
                    ),
                  ],
                  const SizedBox(height: 16),
                  Text(
                    description,
                    style: theme.textTheme.bodyLarge?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  if (bullets != null) ...[
                    const SizedBox(height: 16),
                    ...bullets!.map((bullet) => Padding(
                          padding: const EdgeInsets.only(bottom: 8.0),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const SizedBox(width: 16),
                              const Text('•',
                                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  bullet,
                                  style: theme.textTheme.bodyMedium,
                                ),
                              ),
                            ],
                          ),
                        )),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
