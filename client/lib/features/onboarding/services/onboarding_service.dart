import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:promz/features/onboarding/views/onboarding_view.dart';
import 'package:promz/features/home/<USER>/home_page.dart';
import 'package:promz_common/promz_common.dart';

/// Service responsible for managing the onboarding experience
class OnboardingService {
  static const String _logName = 'OnboardingService';
  static const String _onboardingCompletedKey = 'has_seen_onboarding';

  /// Check if the user has completed onboarding
  static Future<bool> hasCompletedOnboarding() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_onboardingCompletedKey) ?? false;
    } catch (e) {
      appLog.error('Error checking onboarding status', name: _logName, error: e);
      // Default to false if there's an error, showing onboarding again
      return false;
    }
  }

  /// Mark onboarding as completed
  static Future<void> markOnboardingComplete() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_onboardingCompletedKey, true);
      appLog.debug('Onboarding marked as complete', name: _logName);
    } catch (e) {
      appLog.error('Error marking onboarding as complete', name: _logName, error: e);
    }
  }

  /// Reset onboarding status (for testing or user-requested reset)
  static Future<void> resetOnboardingStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_onboardingCompletedKey, false);
      appLog.debug('Onboarding status reset', name: _logName);
    } catch (e) {
      appLog.error('Error resetting onboarding status', name: _logName, error: e);
    }
  }

  /// Check onboarding status and navigate to the appropriate screen
  static Future<void> checkAndShowOnboarding(BuildContext context) async {
    final hasCompleted = await hasCompletedOnboarding();

    if (!hasCompleted) {
      if (context.mounted) {
        appLog.debug('Showing onboarding screens', name: _logName);
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const OnboardingView()),
        );
      }
    } else {
      if (context.mounted) {
        appLog.debug('Onboarding already completed, proceeding to home', name: _logName);
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const HomePage()),
        );
      }
    }
  }
}
