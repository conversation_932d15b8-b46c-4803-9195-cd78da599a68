import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:promz/core/models/license_model.dart';
import 'package:promz/core/providers/license_provider.dart';
import 'package:promz/core/providers/service_providers.dart';
import 'package:promz/features/navigation/routes.dart';
import 'package:promz_common/promz_common.dart';

/// Service responsible for handling subscription and license-related operations
class SubscriptionService {
  static const _logName = 'SubscriptionService';

  /// Shows the upgrade options UI
  /// This will navigate to the appropriate screen based on the user's authentication and license state
  Future<void> showUpgradeOptions(BuildContext context, [WidgetRef? ref]) async {
    appLog.debug('Showing upgrade options', name: _logName);

    // If ref is provided, we can access the license state directly
    if (ref != null) {
      final licenseState = ref.read(userLicenseProvider);
      final clientContextAsync = ref.read(clientContextServiceProvider);

      if (clientContextAsync.hasValue) {
        final clientContextService = clientContextAsync.value!;
        final userProfile = clientContextService.userProfile.profile;

        if (!userProfile.isAuthenticated) {
          // User is not authenticated, redirect to profile setup
          if (context.mounted) {
            appLog.debug('User not authenticated, navigating to profile setup', name: _logName);
            Navigator.of(context).pushNamed(AppRoutes.userProfileSetup);
          }
          return;
        }

        // User is authenticated, check license and navigate accordingly
        if (licenseState.hasValue && licenseState.value != null) {
          _handleNavigationBasedOnLicense(context, licenseState.value!);
        } else {
          // License state not available, navigate to upgrade screen anyway
          if (context.mounted) {
            appLog.debug('License state not available, navigating to upgrade screen',
                name: _logName);
            Navigator.of(context).pushNamed(AppRoutes.subscriptionUpgrade);
          }
        }
        return;
      } else {
        // Handle loading/error state - ClientContextService not ready
        appLog.warning(
          'ClientContextService not available when showing upgrade options. State: $clientContextAsync',
          name: _logName,
        );
        // Optionally show a message to the user? For now, just return.
        return;
      }
    } else {
      // If ref is not provided, just navigate to the upgrade screen
      // The actual subscription view will handle checking auth/license state
      if (context.mounted) {
        appLog.debug('Navigating to upgrade screen', name: _logName);
        Navigator.of(context).pushNamed(AppRoutes.subscriptionUpgrade);
      }
    }
  }

  /// Navigates to the appropriate screen based on the user's license state
  void _handleNavigationBasedOnLicense(BuildContext context, UserLicense license) {
    if (!context.mounted) return;

    if (license.licenseType == LicenseType.none || !license.hasLicense) {
      // User has no license, navigate to profile setup
      appLog.debug('User has no license, navigating to profile setup', name: _logName);
      Navigator.of(context).pushNamed(AppRoutes.userProfileSetup);
    } else if (license.licenseType == LicenseType.free) {
      // User has free license, navigate to subscription upgrade
      appLog.debug('User has free license, navigating to subscription upgrade', name: _logName);
      Navigator.of(context).pushNamed(AppRoutes.subscriptionUpgrade);
    } else if (license.licenseType == LicenseType.pro) {
      // User already has pro license, show message
      appLog.debug('User already has pro license', name: _logName);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('You already have a Pro subscription.'),
        ),
      );
    } else if (license.licenseType == LicenseType.trial) {
      // User has trial license, check if it's expiring soon
      final daysRemaining =
          license.expiresAt != null ? license.expiresAt!.difference(DateTime.now()).inDays : 0;

      if (daysRemaining <= 3) {
        // Trial expiring soon, navigate to subscription upgrade
        appLog.debug('Trial expiring soon, navigating to subscription upgrade', name: _logName);
        Navigator.of(context).pushNamed(AppRoutes.subscriptionUpgrade);
      } else {
        // Trial still active, show message
        appLog.debug('Trial still active', name: _logName);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('You have an active trial with $daysRemaining days remaining.'),
          ),
        );
      }
    }
  }
}

/// Provider for the SubscriptionService
final subscriptionServiceProvider = Provider<SubscriptionService>((ref) {
  return SubscriptionService();
});
