import 'dart:io';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:promz/core/providers/service_providers.dart';
import 'package:promz/core/services/supabase_service.dart';
import 'package:promz_common/promz_common.dart';

final topicsServiceProvider = Provider<TopicsService>((ref) {
  final supabaseService = ref.watch(supabaseServiceProvider);
  return TopicsService(supabaseService);
});

/// Service for managing topics (collections of saved LLM outputs)
class TopicsService {
  static const _logName = 'TopicsService';
  final SupabaseService _supabaseService;

  TopicsService(this._supabaseService);

  /// Get all topics for the current user
  Future<List<Topic>> getTopics() async {
    try {
      appLog.debug('Getting topics for user', name: _logName);

      // Check if user is authenticated
      if (!_supabaseService.isAuthenticated) {
        appLog.warning('User not authenticated, cannot get topics', name: _logName);
        return [];
      }

      // Call the get_topics function
      final response = await _supabaseService.client.rpc('get_topics', params: {
        'p_user_id': _supabaseService.currentUser!.id,
      });

      // Convert response to Topic objects
      if (response == null) {
        appLog.warning('Null response from get_topics RPC', name: _logName);
        return [];
      }

      // Handle different response formats
      List<dynamic> data;
      if (response is List) {
        data = response;
      } else if (response.data is List) {
        data = response.data as List<dynamic>;
      } else {
        appLog.warning('Unexpected response format from get_topics RPC', name: _logName);
        return [];
      }

      return data.map((json) => Topic.fromJson(json)).toList();
    } catch (e, stack) {
      appLog.error('Error getting topics', name: _logName, error: e, stackTrace: stack);
      return [];
    }
  }

  /// Create a new topic
  Future<Topic?> createTopic({
    required String name,
    String? description,
    StorageType storageType = StorageType.local,
    String? driveRootPath,
  }) async {
    try {
      appLog.debug('Creating topic: $name', name: _logName);

      // Check if user is authenticated
      if (!_supabaseService.isAuthenticated) {
        appLog.warning('User not authenticated, cannot create topic', name: _logName);
        return null;
      }

      // Check if a topic with this name already exists
      final exists = await topicExists(name);
      if (exists) {
        appLog.warning('Topic with name "$name" already exists', name: _logName);
        return null;
      }

      // Call the create_topic function
      final response = await _supabaseService.client.rpc('create_topic', params: {
        'p_user_id': _supabaseService.currentUser!.id,
        'p_name': name,
        'p_description': description ?? '',
        'p_storage_type': storageType.name,
        'p_drive_root_path':
            storageType == StorageType.googleDrive ? (driveRootPath ?? 'promz/') : null,
      });

      // Create local folder if storage type is local
      if (storageType == StorageType.local) {
        await _createLocalFolder(name);
      }

      // Convert response to Topic object
      if (response == null) {
        appLog.warning('Null response from create_topic RPC', name: _logName);
        return null;
      }

      // Handle different response formats
      List<dynamic> data;
      if (response is List) {
        data = response;
      } else if (response.data is List) {
        data = response.data as List<dynamic>;
      } else {
        appLog.warning('Unexpected response format from create_topic RPC', name: _logName);
        return null;
      }

      if (data.isNotEmpty) {
        return Topic.fromJson(data.first);
      }

      return null;
    } catch (e, stack) {
      appLog.error('Error creating topic', name: _logName, error: e, stackTrace: stack);
      return null;
    }
  }

  /// Check if a topic with the given name exists
  Future<bool> topicExists(String name) async {
    try {
      appLog.debug('Checking if topic exists: $name', name: _logName);

      // Check if user is authenticated
      if (!_supabaseService.isAuthenticated) {
        appLog.warning('User not authenticated, cannot check topic existence', name: _logName);
        return false;
      }

      // Call the topic_exists function
      final response = await _supabaseService.client.rpc('topic_exists', params: {
        'p_user_id': _supabaseService.currentUser!.id,
        'p_name': name,
      });

      if (response == null) {
        appLog.warning('Null response from topic_exists RPC', name: _logName);
        return false;
      }

      // Handle different response formats
      if (response is bool) {
        return response;
      } else if (response.data is bool) {
        return response.data as bool;
      } else {
        appLog.warning('Unexpected response format from topic_exists RPC', name: _logName);
        return false;
      }
    } catch (e, stack) {
      appLog.error('Error checking topic existence', name: _logName, error: e, stackTrace: stack);
      return false;
    }
  }

  /// Delete a topic
  Future<bool> deleteTopic(String topicId) async {
    try {
      appLog.debug('Deleting topic: $topicId', name: _logName);

      // Check if user is authenticated
      if (!_supabaseService.isAuthenticated) {
        appLog.warning('User not authenticated, cannot delete topic', name: _logName);
        return false;
      }

      // Get the topic to check storage type and name
      final topics = await getTopics();
      final topic = topics.firstWhere((t) => t.id == topicId, orElse: () => Topic.empty());

      if (topic.id.isEmpty) {
        appLog.warning('Topic not found: $topicId', name: _logName);
        return false;
      }

      // Call the delete_topic function
      final response = await _supabaseService.client.rpc('delete_topic', params: {
        'p_user_id': _supabaseService.currentUser!.id,
        'p_topic_id': topicId,
      });

      // Delete local folder if storage type is local
      if (topic.storageType == StorageType.local) {
        await _deleteLocalFolder(topic.name);
      }

      if (response == null) {
        appLog.warning('Null response from delete_topic RPC', name: _logName);
        return false;
      }

      // Handle different response formats
      if (response is bool) {
        return response;
      } else if (response.data is bool) {
        return response.data as bool;
      } else {
        appLog.warning('Unexpected response format from delete_topic RPC', name: _logName);
        return false;
      }
    } catch (e, stack) {
      appLog.error('Error deleting topic', name: _logName, error: e, stackTrace: stack);
      return false;
    }
  }

  /// Save LLM output to a topic
  Future<bool> saveLlmOutput({
    required String topicId,
    required String content,
    required String title,
    String? description,
  }) async {
    try {
      appLog.debug('Saving LLM output to topic: $topicId', name: _logName);

      // Get the topic to check storage type and name
      final topics = await getTopics();
      final topic = topics.firstWhere((t) => t.id == topicId, orElse: () => Topic.empty());

      if (topic.id.isEmpty) {
        appLog.warning('Topic not found: $topicId', name: _logName);
        return false;
      }

      // Generate a filename based on the title
      final filename = _generateFilename(title);

      // Save based on storage type
      if (topic.storageType == StorageType.local) {
        return await _saveToLocalStorage(topic.name, filename, content);
      } else {
        // Google Drive integration is postponed
        appLog.warning('Google Drive integration is not implemented yet', name: _logName);
        return false;
      }
    } catch (e, stack) {
      appLog.error('Error saving LLM output', name: _logName, error: e, stackTrace: stack);
      return false;
    }
  }

  /// Create a local folder for a topic
  Future<bool> _createLocalFolder(String topicName) async {
    try {
      final directory = await _getTopicsDirectory();
      final topicDir = Directory(path.join(directory.path, topicName));

      if (!await topicDir.exists()) {
        await topicDir.create(recursive: true);
      }

      return true;
    } catch (e, stack) {
      appLog.error('Error creating local folder', name: _logName, error: e, stackTrace: stack);
      return false;
    }
  }

  /// Delete a local folder for a topic
  Future<bool> _deleteLocalFolder(String topicName) async {
    try {
      final directory = await _getTopicsDirectory();
      final topicDir = Directory(path.join(directory.path, topicName));

      if (await topicDir.exists()) {
        await topicDir.delete(recursive: true);
      }

      return true;
    } catch (e, stack) {
      appLog.error('Error deleting local folder', name: _logName, error: e, stackTrace: stack);
      return false;
    }
  }

  /// Save content to local storage
  Future<bool> _saveToLocalStorage(String topicName, String filename, String content) async {
    try {
      final directory = await _getTopicsDirectory();
      final topicDir = Directory(path.join(directory.path, topicName));

      if (!await topicDir.exists()) {
        await topicDir.create(recursive: true);
      }

      final file = File(path.join(topicDir.path, filename));
      await file.writeAsString(content);

      return true;
    } catch (e, stack) {
      appLog.error('Error saving to local storage', name: _logName, error: e, stackTrace: stack);
      return false;
    }
  }

  /// Get the topics directory
  Future<Directory> _getTopicsDirectory() async {
    final appDir = await getApplicationDocumentsDirectory();
    final topicsDir = Directory(path.join(appDir.path, 'topics'));

    if (!await topicsDir.exists()) {
      await topicsDir.create(recursive: true);
    }

    return topicsDir;
  }

  /// Generate a filename based on the title
  String _generateFilename(String title) {
    // Remove special characters and spaces
    final sanitized =
        title.replaceAll(RegExp(r'[^\w\s]'), '').replaceAll(RegExp(r'\s+'), '_').toLowerCase();

    // Add timestamp to ensure uniqueness
    final timestamp = DateTime.now().millisecondsSinceEpoch;

    return '${sanitized}_$timestamp.txt';
  }
}

/// Topic model
class Topic {
  final String id;
  final String name;
  final String description;
  final StorageType storageType;
  final String? driveRootPath;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? deletedAt;
  final int version;

  Topic({
    required this.id,
    required this.name,
    required this.description,
    required this.storageType,
    this.driveRootPath,
    required this.createdAt,
    required this.updatedAt,
    this.deletedAt,
    required this.version,
  });

  factory Topic.fromJson(Map<String, dynamic> json) {
    return Topic(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String? ?? '',
      storageType: _storageTypeFromString(json['storage_type'] as String),
      driveRootPath: json['drive_root_path'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      deletedAt: json['deleted_at'] != null ? DateTime.parse(json['deleted_at'] as String) : null,
      version: json['version'] as int,
    );
  }

  factory Topic.empty() {
    return Topic(
      id: '',
      name: '',
      description: '',
      storageType: StorageType.local,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      version: 0,
    );
  }

  static StorageType _storageTypeFromString(String value) {
    switch (value.toLowerCase()) {
      case 'google_drive':
        return StorageType.googleDrive;
      case 'local':
      default:
        return StorageType.local;
    }
  }
}

/// Storage type enum
enum StorageType {
  local,
  googleDrive,
}

extension StorageTypeExtension on StorageType {
  String get name {
    switch (this) {
      case StorageType.local:
        return 'local';
      case StorageType.googleDrive:
        return 'google_drive';
    }
  }
}
