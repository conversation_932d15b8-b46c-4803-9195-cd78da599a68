import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:promz/core/widgets/promz_app_bar.dart';
import 'package:promz/features/home/<USER>/home_viewmodel.dart';
import 'package:promz/features/home/<USER>/widgets/bottom_nav_bar.dart';
import 'package:promz/features/portfolio/viewmodels/portfolio_viewmodel.dart';
import 'package:promz/features/portfolio/services/topics_service.dart';
import 'package:promz_common/promz_common.dart';

class PortfolioView extends ConsumerStatefulWidget {
  const PortfolioView({super.key});

  @override
  ConsumerState<PortfolioView> createState() => _PortfolioViewState();
}

class _PortfolioViewState extends ConsumerState<PortfolioView> {
  static const _logName = 'PortfolioView';

  @override
  void initState() {
    super.initState();
    // Load topics when the view is first created
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(portfolioViewModelProvider).loadTopics();
    });
  }

  @override
  Widget build(BuildContext context) {
    final homeViewModel = ref.watch(homeViewModelProvider);
    final portfolioViewModel = ref.watch(portfolioViewModelProvider);
    final theme = Theme.of(context);

    return PopScope(
      canPop: false,
      // ignore: deprecated_member_use
      onPopInvoked: (didPop) {
        if (!didPop) {
          homeViewModel.navigateToPage(context, 0);
        }
      },
      child: Scaffold(
        appBar: PromzAppBar(
          title: const Text(Strings.portfolioPageTitle),
          showBackButton: true,
          onBackPressed: () => homeViewModel.navigateToPage(context, 0),
          actions: [
            IconButton(
              icon: const Icon(Icons.add),
              tooltip: 'Create new topic',
              onPressed: () => _showCreateTopicDialog(context),
            ),
          ],
        ),
        body: portfolioViewModel.isLoading
            ? const Center(child: CircularProgressIndicator())
            : portfolioViewModel.hasError
                ? _buildErrorView(portfolioViewModel.errorMessage ?? 'Failed to load topics')
                : portfolioViewModel.topicsForCurrentPage.isEmpty
                    ? _buildEmptyView(theme)
                    : _buildTopicsList(portfolioViewModel),
        bottomNavigationBar: BottomNavBar(
          currentIndex: homeViewModel.currentNavIndex,
          onTap: (index) => homeViewModel.navigateToPage(context, index),
        ),
      ),
    );
  }

  Widget _buildErrorView(String errorMessage) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 48, color: Colors.red),
            const SizedBox(height: 16),
            Text(
              'Error',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              errorMessage,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                ref.read(portfolioViewModelProvider).loadTopics();
              },
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyView(ThemeData theme) {
    return PromzPageView(
      title: 'No Topics Yet',
      emoji: '📁',
      description: 'Create your first topic to start organizing your AI interactions:',
      content: _buildFeatureList(theme),
      actions: ElevatedButton.icon(
        icon: const Icon(Icons.add),
        label: const Text('Create Topic'),
        onPressed: () => _showCreateTopicDialog(context),
      ),
    );
  }

  Widget _buildTopicsList(PortfolioViewModel viewModel) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Your Topics',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 16),
          Expanded(
            child: ListView.builder(
              itemCount: viewModel.topicsForCurrentPage.length,
              itemBuilder: (context, index) {
                final topic = viewModel.topicsForCurrentPage[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 12),
                  child: ListTile(
                    leading: Icon(
                      topic.storageType == StorageType.local ? Icons.folder : Icons.cloud,
                      color: Theme.of(context).primaryColor,
                    ),
                    title: Text(topic.name),
                    subtitle: Text(
                      topic.description.isNotEmpty
                          ? topic.description
                          : 'Created ${_formatDate(topic.createdAt)}',
                    ),
                    trailing: IconButton(
                      icon: const Icon(Icons.delete_outline),
                      onPressed: () => _confirmDeleteTopic(context, topic),
                    ),
                    onTap: () {
                      viewModel.selectTopic(topic);
                      _showTopicDetails(context, topic);
                    },
                  ),
                );
              },
            ),
          ),
          if (viewModel.totalPages > 1)
            Padding(
              padding: const EdgeInsets.only(top: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  IconButton(
                    icon: const Icon(Icons.arrow_back),
                    onPressed: viewModel.currentPage > 0 ? () => viewModel.previousPage() : null,
                  ),
                  Text('Page ${viewModel.currentPage + 1} of ${viewModel.totalPages}'),
                  IconButton(
                    icon: const Icon(Icons.arrow_forward),
                    onPressed: viewModel.currentPage < viewModel.totalPages - 1
                        ? () => viewModel.nextPage()
                        : null,
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  void _showCreateTopicDialog(BuildContext context) {
    final formKey = GlobalKey<FormState>();
    String name = '';
    String description = '';
    StorageType storageType = StorageType.local;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Create New Topic'),
        content: Form(
          key: formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                decoration: const InputDecoration(labelText: 'Topic Name'),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a topic name';
                  }
                  return null;
                },
                onSaved: (value) => name = value!,
              ),
              TextFormField(
                decoration: const InputDecoration(labelText: 'Description (Optional)'),
                onSaved: (value) => description = value ?? '',
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<StorageType>(
                decoration: const InputDecoration(labelText: 'Storage Type'),
                value: storageType,
                items: const [
                  DropdownMenuItem(
                    value: StorageType.local,
                    child: Text('Local Storage'),
                  ),
                  DropdownMenuItem(
                    value: StorageType.googleDrive,
                    enabled: false,
                    child: Text('Google Drive (Coming Soon)'),
                  ),
                ],
                onChanged: (value) {
                  if (value != null) {
                    storageType = value;
                  }
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (formKey.currentState!.validate()) {
                formKey.currentState!.save();
                Navigator.of(context).pop();

                // Create the topic
                ref.read(portfolioViewModelProvider).createTopic(
                      name: name,
                      description: description,
                      storageType: storageType,
                    );
              }
            },
            child: const Text('Create'),
          ),
        ],
      ),
    );
  }

  void _confirmDeleteTopic(BuildContext context, Topic topic) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Topic'),
        content:
            Text('Are you sure you want to delete "${topic.name}"? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref.read(portfolioViewModelProvider).deleteTopic(topic.id);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _showTopicDetails(BuildContext context, Topic topic) {
    appLog.debug('Showing details for topic: ${topic.name}', name: _logName);

    // For now, just show a simple dialog with topic details
    // In the future, this could navigate to a detailed view of the topic
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(topic.name),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (topic.description.isNotEmpty) ...[
              Text(topic.description),
              const SizedBox(height: 16),
            ],
            Text('Storage: ${topic.storageType == StorageType.local ? 'Local' : 'Google Drive'}'),
            Text('Created: ${_formatDate(topic.createdAt)}'),
            Text('Last Updated: ${_formatDate(topic.updatedAt)}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  Widget _buildFeatureList(ThemeData theme) {
    final features = [
      {
        'icon': Icons.folder_special,
        'title': 'Organized Storage',
        'description': 'Create topics to group related AI conversations and outputs',
      },
      {
        'icon': Icons.cloud_sync,
        'title': 'Cloud Integration',
        'description': 'Seamlessly sync with Google Drive for backup and sharing',
      },
      {
        'icon': Icons.picture_as_pdf,
        'title': 'PDF Management',
        'description': 'Automatically save and organize AI outputs as PDFs',
      },
      {
        'icon': Icons.folder_shared,
        'title': 'Easy Navigation',
        'description': 'Quick access to all your saved conversations and files',
      },
      {
        'icon': Icons.search,
        'title': 'Smart Search',
        'description': 'Find content across all your topics and files',
      },
    ];

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: features.length,
      itemBuilder: (context, index) {
        final feature = features[index];
        return PromzCard(
          icon: feature['icon'] as IconData,
          title: feature['title'] as String,
          description: feature['description'] as String,
        );
      },
    );
  }
}
