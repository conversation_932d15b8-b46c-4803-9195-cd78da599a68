import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:promz/features/portfolio/services/topics_service.dart';
import 'package:promz_common/promz_common.dart';

final portfolioViewModelProvider = ChangeNotifierProvider<PortfolioViewModel>((ref) {
  final topicsService = ref.watch(topicsServiceProvider);
  return PortfolioViewModel(topicsService);
});

class PortfolioViewModel extends ChangeNotifier {
  static const _logName = 'PortfolioViewModel';
  final TopicsService _topicsService;

  final List<Topic> _topics = [];
  int _currentPage = 0;
  int _itemsPerPage = 10;
  bool _isLoading = false;
  bool _hasError = false;
  String? _errorMessage;
  Topic? _selectedTopic;
  bool _isCreatingTopic = false;

  PortfolioViewModel(this._topicsService);

  bool get isLoading => _isLoading;
  bool get hasError => _hasError;
  String? get errorMessage => _errorMessage;
  int get currentPage => _currentPage;
  int get itemsPerPage => _itemsPerPage;
  int get totalPages => (_topics.length / _itemsPerPage).ceil();
  bool get isCreatingTopic => _isCreatingTopic;
  Topic? get selectedTopic => _selectedTopic;

  List<Topic> get topicsForCurrentPage {
    if (_topics.isEmpty) return [];

    final startIndex = _currentPage * _itemsPerPage;
    final endIndex = startIndex + _itemsPerPage;
    if (startIndex >= _topics.length) {
      return [];
    }
    return _topics.sublist(startIndex, endIndex.clamp(0, _topics.length));
  }

  Future<void> loadTopics() async {
    _isLoading = true;
    _hasError = false;
    _errorMessage = null;
    notifyListeners();

    try {
      final topics = await _topicsService.getTopics();
      _topics.clear();
      _topics.addAll(topics);
      _hasError = false;

      appLog.debug('Loaded ${_topics.length} topics', name: _logName);
    } catch (e, stack) {
      _hasError = true;
      _errorMessage = 'Failed to load topics: $e';
      appLog.error('Error loading topics', name: _logName, error: e, stackTrace: stack);
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<Topic?> createTopic({
    required String name,
    String? description,
    StorageType storageType = StorageType.local,
  }) async {
    _isCreatingTopic = true;
    _hasError = false;
    _errorMessage = null;
    notifyListeners();

    try {
      final topic = await _topicsService.createTopic(
        name: name,
        description: description,
        storageType: storageType,
      );

      if (topic != null) {
        _topics.add(topic);
        _selectedTopic = topic;
        appLog.debug('Created topic: ${topic.name}', name: _logName);
      } else {
        _hasError = true;
        _errorMessage = 'Failed to create topic. It may already exist.';
      }

      return topic;
    } catch (e, stack) {
      _hasError = true;
      _errorMessage = 'Failed to create topic: $e';
      appLog.error('Error creating topic', name: _logName, error: e, stackTrace: stack);
      return null;
    } finally {
      _isCreatingTopic = false;
      notifyListeners();
    }
  }

  Future<bool> deleteTopic(String topicId) async {
    _isLoading = true;
    _hasError = false;
    _errorMessage = null;
    notifyListeners();

    try {
      final success = await _topicsService.deleteTopic(topicId);

      if (success) {
        _topics.removeWhere((topic) => topic.id == topicId);
        if (_selectedTopic?.id == topicId) {
          _selectedTopic = null;
        }
        appLog.debug('Deleted topic: $topicId', name: _logName);
      } else {
        _hasError = true;
        _errorMessage = 'Failed to delete topic';
      }

      return success;
    } catch (e, stack) {
      _hasError = true;
      _errorMessage = 'Failed to delete topic: $e';
      appLog.error('Error deleting topic', name: _logName, error: e, stackTrace: stack);
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<bool> saveLlmOutput({
    required String content,
    required String title,
    String? description,
  }) async {
    if (_selectedTopic == null) {
      _hasError = true;
      _errorMessage = 'No topic selected';
      notifyListeners();
      return false;
    }

    _isLoading = true;
    _hasError = false;
    _errorMessage = null;
    notifyListeners();

    try {
      final success = await _topicsService.saveLlmOutput(
        topicId: _selectedTopic!.id,
        content: content,
        title: title,
        description: description,
      );

      if (!success) {
        _hasError = true;
        _errorMessage = 'Failed to save LLM output';
      } else {
        appLog.debug('Saved LLM output to topic: ${_selectedTopic!.name}', name: _logName);
      }

      return success;
    } catch (e, stack) {
      _hasError = true;
      _errorMessage = 'Failed to save LLM output: $e';
      appLog.error('Error saving LLM output', name: _logName, error: e, stackTrace: stack);
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  void selectTopic(Topic topic) {
    _selectedTopic = topic;
    notifyListeners();
  }

  void clearSelectedTopic() {
    _selectedTopic = null;
    notifyListeners();
  }

  void nextPage() {
    if (_currentPage < totalPages - 1) {
      _currentPage++;
      notifyListeners();
    }
  }

  void previousPage() {
    if (_currentPage > 0) {
      _currentPage--;
      notifyListeners();
    }
  }

  void updateItemsPerPage(int count) {
    if (_itemsPerPage != count) {
      _itemsPerPage = count;
      _currentPage = 0;
      notifyListeners();
    }
  }

  void clearError() {
    _hasError = false;
    _errorMessage = null;
    notifyListeners();
  }
}
