import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:promz_common/promz_common.dart';
import 'package:promz/features/home/<USER>/home_viewmodel.dart';
import 'package:promz/features/input_selection/widgets/source_card_factory.dart';
import 'package:promz/features/input_selection/models/input_source.dart';
import 'package:promz/core/utils/file_icon_helper.dart';
import 'dart:io';
import 'package:pdf/widgets.dart' as pw;
import 'package:pdf/pdf.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';

/// A widget that displays execution results, either in markdown or regular text format
class ExecutionResultsSection extends StatelessWidget {
  static const String _logName = 'ExecutionResultsSection';

  final HomeViewModel viewModel;

  const ExecutionResultsSection({
    super.key,
    required this.viewModel,
  });

  @override
  Widget build(BuildContext context) {
    if (viewModel.executionError != null) {
      return _buildErrorView(context);
    }

    if (viewModel.llmResponse != null) {
      return _buildResponseView(context);
    }

    return const Center(child: Text(Strings.noResultsText));
  }

  /// Builds the view for displaying error messages
  Widget _buildErrorView(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 24),
            Text(
              Strings.errorText,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Theme.of(context).colorScheme.error,
                  ),
            ),
            const SizedBox(height: 16),
            Text(
              viewModel.executionError!,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyLarge,
            ),
            const SizedBox(height: 32),
            FilledButton.icon(
              onPressed: () => viewModel.clearExecutionResults(),
              icon: const Icon(Icons.refresh),
              label: const Text(Strings.retryText),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds the view for displaying LLM responses
  Widget _buildResponseView(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  Strings.resultText,
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
              ),
              IconButton(
                icon: const Icon(Icons.close),
                onPressed: () {
                  // Use Future.microtask to avoid UI update race conditions
                  Future.microtask(() => viewModel.clearExecutionResults());
                },
                tooltip: 'Close results',
              ),
            ],
          ),
          const Divider(),
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Display source cards if there are sources
                  if (viewModel.sources.isNotEmpty) _buildSourceCardsSection(context),
                  _buildContentContainer(context),
                  const SizedBox(height: 16),
                  _buildActionButtons(context),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Builds the container that displays the response content
  Widget _buildContentContainer(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (viewModel.llmResponse!.isMarkdown)
            _buildMarkdownVersion(context)
          else
            _buildRegularVersion(context),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Text(
                'Provider: ${viewModel.llmResponse!.provider}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Builds the markdown version of the response
  Widget _buildMarkdownVersion(BuildContext context) {
    final service = viewModel.clientContextService;
    final processedText = service != null
        ? viewModel.llmResponse!.processResponseText(service)
        : viewModel.llmResponse!.text; // Use raw text if service is null

    return MarkdownBody(
      data: processedText,
      selectable: true,
      styleSheet: MarkdownStyleSheet.fromTheme(Theme.of(context)).copyWith(
        p: Theme.of(context).textTheme.bodyLarge,
        h1: Theme.of(context).textTheme.headlineMedium,
        h2: Theme.of(context).textTheme.headlineSmall,
        h3: Theme.of(context).textTheme.titleLarge,
        h4: Theme.of(context).textTheme.titleMedium,
        h5: Theme.of(context).textTheme.titleSmall,
        h6: Theme.of(context).textTheme.labelLarge,
        code: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontFamily: 'monospace',
              backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
            ),
        codeblockDecoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(4),
        ),
      ),
    );
  }

  /// Builds the regular text version of the response
  Widget _buildRegularVersion(BuildContext context) {
    final service = viewModel.clientContextService;
    final processedText = service != null
        ? viewModel.llmResponse!.processResponseText(service)
        : viewModel.llmResponse!.text; // Use raw text if service is null

    return Text(
      processedText,
      style: Theme.of(context).textTheme.bodyLarge,
    );
  }

  /// Builds the action buttons for the response
  Widget _buildActionButtons(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        FilledButton.icon(
          onPressed: () {
            Clipboard.setData(
              ClipboardData(text: viewModel.llmResponse!.text),
            );
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text(Strings.copiedToClipboardText)),
            );
          },
          icon: const Icon(Icons.copy),
          label: const Text(Strings.copyText),
        ),
        FilledButton.icon(
          onPressed: () => _saveToPdf(context),
          icon: const Icon(Icons.picture_as_pdf),
          label: const Text(Strings.saveText),
        ),
        FilledButton.icon(
          onPressed: () => _shareContent(context),
          icon: const Icon(Icons.share),
          label: const Text(Strings.shareText),
        ),
      ],
    );
  }

  /// Shares the content using the system share dialog
  Future<void> _shareContent(BuildContext context) async {
    try {
      final service = viewModel.clientContextService;
      final processedText = service != null
          ? viewModel.llmResponse!.processResponseText(service)
          : viewModel.llmResponse!.text; // Use raw text if service is null

      // Show loading indicator
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Preparing to share...')),
        );
      }

      // Share the text content directly
      await Share.share(
        processedText,
        subject: 'Promz Result',
      );

      if (context.mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
      }
    } catch (e, stackTrace) {
      appLog.error('Error sharing content: $e', name: _logName, error: e, stackTrace: stackTrace);

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error sharing content: ${e.toString()}'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  /// Builds a section displaying source cards
  Widget _buildSourceCardsSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(bottom: 4.0),
          child: Text(
            'Sources:',
            style: Theme.of(context).textTheme.titleMedium,
          ),
        ),
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surfaceContainerLow,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: [
              for (final source in viewModel.sources)
                Padding(
                  padding: const EdgeInsets.only(bottom: 4.0),
                  child: _buildSourceCardHtml(source),
                ),
            ],
          ),
        ),
        const SizedBox(height: 8),
      ],
    );
  }

  /// Builds an HTML widget for a source card
  Widget _buildSourceCardHtml(InputSource source) {
    final isLoading = viewModel.sourceLoadingStates[source.contentHash ?? source.filePath] ?? false;

    final html = isLoading
        ? SourceCardFactory.generateSkeletonHtml()
        : SourceCardFactory.generateHtmlForSource(source);

    return Html(
      data: '<style>${SourceCardFactory.getSourceCardCss()}</style>$html',
      style: {
        'body': Style(
          margin: Margins.zero,
          padding: HtmlPaddings.zero,
        ),
        '.source-card': Style(
          margin: Margins.only(bottom: 4),
        ),
      },
    );
  }

  /// Saves the content to a PDF file and shares it
  Future<void> _saveToPdf(BuildContext context) async {
    try {
      final service = viewModel.clientContextService;
      final processedText = service != null
          ? viewModel.llmResponse!.processResponseText(service)
          : viewModel.llmResponse!.text; // Use raw text if service is null

      // Show loading indicator
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Generating PDF...')),
        );
      }

      // Create PDF document
      final pdf = pw.Document();

      // Define a theme for the document
      final theme = pw.ThemeData.withFont(
        base: pw.Font.helvetica(),
        bold: pw.Font.helveticaBold(),
      );

      // Add page with content that can span multiple pages
      pdf.addPage(
        pw.MultiPage(
          theme: theme,
          pageFormat: PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(32),
          maxPages: 100,
          build: (pw.Context context) {
            final List<pw.Widget> content = [];

            // Add title
            content.add(
              pw.Header(
                level: 0,
                child: pw.Text('Promz Results'),
              ),
            );

            // Add sources section if there are sources
            if (viewModel.sources.isNotEmpty) {
              content.add(pw.SizedBox(height: 10));
              content.add(
                pw.Header(
                  level: 1,
                  text: 'Sources',
                ),
              );

              // Add each source as a separate item
              for (final source in viewModel.sources) {
                final sourceName = source.fileName ?? 'Source';
                final sourceType = FileIconHelper.getSourceTypeDescription(source) ?? '';

                content.add(
                  pw.Container(
                    margin: const pw.EdgeInsets.only(bottom: 5),
                    padding: const pw.EdgeInsets.all(8),
                    decoration: pw.BoxDecoration(
                      color: PdfColors.grey200,
                      border: pw.Border.all(color: PdfColors.grey400),
                      borderRadius: const pw.BorderRadius.all(pw.Radius.circular(4)),
                    ),
                    child: pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                        pw.Text(
                          sourceName,
                          style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                        ),
                        if (sourceType.isNotEmpty)
                          pw.Text(sourceType, style: const pw.TextStyle(fontSize: 9)),
                      ],
                    ),
                  ),
                );
              }

              content.add(pw.SizedBox(height: 10));
              content.add(pw.Divider());
            }

            // Add content section
            content.add(pw.SizedBox(height: 10));
            content.add(
              pw.Header(
                level: 1,
                text: 'Content',
              ),
            );

            // Add the main content
            final paragraphs = processedText.split('\n\n');
            for (final paragraph in paragraphs) {
              if (paragraph.trim().isEmpty) continue;

              // Check if paragraph is a header (starts with #)
              if (paragraph.trim().startsWith('#')) {
                final headerLevel = paragraph.indexOf(' ');
                if (headerLevel > 0 && headerLevel <= 6) {
                  final headerText = paragraph.substring(headerLevel).trim();
                  content.add(
                    pw.Header(
                      level: headerLevel,
                      text: headerText,
                    ),
                  );
                  continue;
                }
              }

              // Regular paragraph
              content.add(pw.Paragraph(text: paragraph.trim()));
            }

            return content;
          },
          footer: (context) => pw.Container(
            alignment: pw.Alignment.centerRight,
            margin: const pw.EdgeInsets.only(top: 10),
            child: pw.Text(
              'Page ${context.pageNumber} of ${context.pagesCount}',
              style: const pw.TextStyle(fontSize: 9),
            ),
          ),
        ),
      );

      // Get temporary directory for saving the file
      final tempDir = await getTemporaryDirectory();

      // Use the suggested file name from the server response if available
      final fileName = viewModel.llmResponse!.getFullFileName();
      appLog.debug('Got file name: $fileName', name: _logName);

      // Save PDF to temporary file
      final tempFile = File('${tempDir.path}/$fileName');
      await tempFile.writeAsBytes(await pdf.save());
      appLog.debug('Saved PDF to temporary file: ${tempFile.path}', name: _logName);

      // Share the file with other apps
      if (context.mounted) {
        // Clear previous snackbar
        ScaffoldMessenger.of(context).hideCurrentSnackBar();

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('PDF generated successfully')),
        );

        // Share the file
        await Share.shareXFiles(
          [XFile(tempFile.path)],
          text: 'Promz Result',
          subject: fileName.replaceAll('.pdf', ''), // Use file name without extension as subject
        );
      }

      appLog.info('PDF generated successfully: ${tempFile.path}', name: _logName);
    } catch (e, stackTrace) {
      appLog.error('Error saving PDF: $e', name: _logName, error: e, stackTrace: stackTrace);

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving PDF: ${e.toString()}'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }
}
