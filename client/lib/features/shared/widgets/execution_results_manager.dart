import 'package:flutter/material.dart';
import 'package:promz/features/home/<USER>/home_viewmodel.dart';
import 'package:promz/features/shared/widgets/execution_results_section.dart';

/// A utility function that conditionally displays execution results based on the HomeViewModel state.
///
/// If the HomeViewModel has execution results (success or error), it returns the ExecutionResultsSection.
/// Otherwise, it returns the provided defaultContent.
///
/// @param context The BuildContext
/// @param viewModel The HomeViewModel containing execution state
/// @param defaultContent The content to display when there are no execution results
Widget buildExecutionResultsView(
  BuildContext context,
  HomeViewModel viewModel, {
  required Widget defaultContent,
}) {
  if (viewModel.hasExecutionResult) {
    return ExecutionResultsSection(viewModel: viewModel);
  }
  return defaultContent;
}
