import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:promz/core/services/secure_storage_service.dart';
import 'package:promz/database/database.dart';
import 'package:promz_common/config/api_config.dart';
import 'package:promz_common/promz_common.dart';

/// Service to handle synchronization of prompts with the server
class PromptSyncService {
  static const _logName = 'PromptSyncService';

  /// HTTP client for API requests
  final http.Client _httpClient;

  /// Database instance
  late AppDatabase _db;

  /// Flag to prevent multiple simultaneous sync operations
  bool _isSyncing = false;

  /// Flag to track if the service is initialized
  bool _isInitialized = false;

  /// Timeout duration for sync operations - increased from default API timeout
  static const int _syncTimeoutSeconds = 60; // Double the default timeout

  /// Maximum number of retries for failed requests
  static const int _maxRetries = 3;

  /// Delay between retries in milliseconds
  static const int _retryDelayMs = 1000;

  /// Constructor
  PromptSyncService({
    http.Client? httpClient,
    AppDatabase? db,
  }) : _httpClient = httpClient ?? http.Client() {
    if (db != null) {
      _db = db;
      _isInitialized = true;
    }
  }

  /// Initialize the service
  /// This must be called before using the service if a database instance was not provided in the constructor
  Future<void> initialize() async {
    if (_isInitialized) return;

    _db = await AppDatabase.getInstance();
    _isInitialized = true;
    appLog.debug('PromptSyncService initialized', name: _logName);
  }

  /// Sync prompts with the server
  /// Returns true if sync was successful or partially successful, false otherwise
  Future<bool> syncPrompts() async {
    // Ensure the service is initialized
    if (!_isInitialized) {
      await initialize();
    }

    // Prevent multiple simultaneous sync operations
    if (_isSyncing) {
      appLog.debug('Prompt sync already in progress, skipping', name: _logName);
      return false;
    }

    _isSyncing = true;
    appLog.debug('Starting prompt sync', name: _logName);

    try {
      // Get API key from secure storage
      final apiKey = await SecureStorageService.getApiKey();
      if (apiKey == null || apiKey.isEmpty) {
        appLog.warning('No API key available for syncing prompts', name: _logName);
        return false;
      }

      // Initialize pagination variables
      int page = 1;
      const int pageSize = 50;
      bool hasMoreData = true;
      bool anyPageSynced = false; // Track if any page was successfully synced

      // Track the last sync time
      final syncTime = DateTime.now();

      // Fetch prompts with pagination
      while (hasMoreData) {
        // Construct the API endpoint with pagination parameters
        final endpoint = '${ApiConfig.baseUrl}/prompts?page=$page&limit=$pageSize';

        bool pageSuccess = false;
        int retries = 0;

        // Try with retries for each page
        while (!pageSuccess && retries < _maxRetries) {
          try {
            // Make the HTTP request to fetch prompts with extended timeout
            final response = await _httpClient.get(
              Uri.parse(endpoint),
              headers: {
                'Content-Type': 'application/json',
                'X-API-Key': apiKey,
              },
            ).timeout(const Duration(seconds: _syncTimeoutSeconds));

            if (response.statusCode == 200) {
              // Parse the response JSON
              final data = json.decode(response.body);

              if (data.containsKey('prompts') && data['prompts'] is List) {
                final prompts = List<Map<String, dynamic>>.from(data['prompts']);

                if (prompts.isEmpty) {
                  // No more prompts to fetch
                  hasMoreData = false;
                } else {
                  // Process and store the fetched prompts
                  await _db.syncPrompts(prompts, syncTime);
                  appLog.debug('Synced ${prompts.length} prompts from page $page', name: _logName);
                  anyPageSynced = true;

                  // Move to the next page
                  page++;
                }
                pageSuccess = true;
              } else {
                appLog.warning('Invalid response format from prompts endpoint', name: _logName);
                hasMoreData = false;
                break;
              }
            } else if (response.statusCode == 429) {
              // Rate limiting - wait longer before retry
              appLog.warning('Rate limited when fetching prompts: HTTP 429', name: _logName);
              await Future.delayed(const Duration(milliseconds: _retryDelayMs * 2));
              retries++;
            } else {
              appLog.warning('Failed to fetch prompts: HTTP ${response.statusCode}',
                  name: _logName);

              if (response.statusCode >= 500) {
                // Server error, might be temporary
                retries++;
                await Future.delayed(const Duration(milliseconds: _retryDelayMs));
              } else {
                // Client error or other, likely won't succeed with retry
                hasMoreData = false;
                break;
              }
            }
          } catch (e, stackTrace) {
            // Handle timeouts and connection errors with retries
            if (retries < _maxRetries - 1) {
              appLog.warning('Error fetching prompts page $page, retrying: $e',
                  name: _logName, error: e, stackTrace: stackTrace);
              retries++;
              await Future.delayed(const Duration(milliseconds: _retryDelayMs));
            } else {
              appLog.error('Failed to fetch prompts page $page after $retries retries',
                  name: _logName, error: e, stackTrace: stackTrace);
              // Continue with next page if we've synced at least one page already
              // This allows for partial syncs rather than complete failures
              if (anyPageSynced) {
                hasMoreData = false;
              } else {
                // If first page fails completely, propagate the error
                rethrow;
              }
              break;
            }
          }
        }
      }

      appLog.debug(anyPageSynced ? 'Prompt sync completed successfully' : 'No prompts synced',
          name: _logName);
      return anyPageSynced; // Return true if any page was synced successfully
    } catch (e, stackTrace) {
      appLog.error('Error in prompt sync task', name: _logName, error: e, stackTrace: stackTrace);
      return false;
    } finally {
      _isSyncing = false;
    }
  }

  /// Dispose resources
  void dispose() {
    _httpClient.close();
  }
}
