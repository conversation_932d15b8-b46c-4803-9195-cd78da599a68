import 'dart:async';
import 'package:promz/core/services/secure_storage_service.dart';
import 'package:promz_common/config/api_config.dart';
import 'package:promz_common/promz_common.dart';

/// Possible connection states for LLM service
enum ConnectionStatus {
  unknown,
  connected,
  disconnected,
  timeout,
}

class LlmExecutionService {
  final String _logName = 'LlmExecutionService';

  LlmExecutionService();

  Future<LlmExecuteResponse> executePrompt(
    LlmExecuteRequest request,
  ) async {
    // Get API key from secure storage
    final apiKey = await SecureStorageService.getApiKey();
    if (apiKey == null || apiKey.isEmpty) {
      appLog.warning('No API key available for prompt execution', name: _logName);
      throw Exception('No API key available');
    }

    // Create request with API key included in body
    final requestWithApiKey = LlmExecuteRequest(
      promptContent: request.promptContent,
      promptId: request.promptId,
      categoryId: request.categoryId,
      sourceContent: request.sourceContent,
      maxTokens: request.maxTokens,
      temperature: request.temperature,
      provider: request.provider,
      options: request.options,
      variables: request.variables,
      apiKey: apiKey,
    );

    // Execute prompt using Promz LLM service
    // Use ApiConfig.baseUrl which already includes the protocol
    final promzLlmService = PromzLlmExecutionService(
      apiBaseUrl: ApiConfig.baseUrl,
      promzApiKey: apiKey,
    );

    return promzLlmService.execute(requestWithApiKey);
  }
}
