// HTTP import removed as we now use url_resolver for HTTP operations
import 'package:promz/core/services/content_processing_service.dart';
import 'package:promz/core/utils/url_resolver.dart' as url_resolver;
import 'package:promz_common/promz_common.dart';

/// Utility class for YouTube URL operations
class YouTubeUrlUtils {
  static const _logName = 'YouTubeUrlUtils';

  // User agent for HTTP requests
  static const String defaultUserAgent =
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';

  /// Check if a URL is a YouTube URL (supports both youtube.com and youtu.be)
  static bool isYouTubeUrl(String url) {
    try {
      final uri = Uri.parse(url.trim());
      final host = uri.host.toLowerCase();

      // Check for main YouTube domains
      if (host == 'youtube.com' || host == 'www.youtube.com' || host == 'youtu.be') {
        return true;
      }

      // Check for YouTube URLs with m. prefix (mobile)
      if (host == 'm.youtube.com') {
        return true;
      }

      // Check for YouTube URLs with youtube-nocookie.com (privacy-enhanced mode)
      if (host == 'youtube-nocookie.com' || host == 'www.youtube-nocookie.com') {
        return true;
      }

      return false;
    } catch (e, stack) {
      appLog.warning('Error parsing URL: $url', name: _logName, error: e, stackTrace: stack);
      return false;
    }
  }

  /// Extract the video ID from a YouTube URL
  ///
  /// Supports various YouTube URL formats:
  /// - https://youtube.com/watch?v=VIDEO_ID
  /// - https://www.youtube.com/watch?v=VIDEO_ID
  /// - https://youtu.be/VIDEO_ID
  /// - https://www.youtube.com/embed/VIDEO_ID
  /// - https://m.youtube.com/watch?v=VIDEO_ID
  /// - https://youtube.com/shorts/VIDEO_ID
  static String? extractVideoId(String url) {
    try {
      final uri = Uri.parse(url.trim());
      final host = uri.host.toLowerCase();
      final path = uri.path;

      String? videoId;

      // Short URL format: youtu.be/VIDEO_ID
      if (host == 'youtu.be') {
        videoId = path.startsWith('/') ? path.substring(1) : path;
        appLog.debug('Extracted video ID from youtu.be URL: $videoId', name: _logName);
        return videoId;
      }

      // YouTube Shorts
      if (path.contains('/shorts/')) {
        final segments = path.split('/');
        final shortsIndex = segments.indexOf('shorts');
        if (shortsIndex >= 0 && segments.length > shortsIndex + 1) {
          videoId = segments[shortsIndex + 1];
          appLog.debug('Extracted video ID from YouTube shorts URL: $videoId', name: _logName);
          return videoId;
        }
      }

      // Embedded player URL
      if (path.contains('/embed/')) {
        final segments = path.split('/');
        final embedIndex = segments.indexOf('embed');
        if (embedIndex >= 0 && segments.length > embedIndex + 1) {
          videoId = segments[embedIndex + 1];
          appLog.debug('Extracted video ID from embed URL: $videoId', name: _logName);
          return videoId;
        }
      }

      // Standard watch URL
      if (uri.queryParameters.containsKey('v')) {
        videoId = uri.queryParameters['v'];
        appLog.debug('Extracted video ID from query parameter: $videoId', name: _logName);
        return videoId;
      }

      // If we get here, we couldn't extract the video ID
      appLog.warning('Could not extract video ID from URL: $url', name: _logName);
      return null;
    } catch (e, stack) {
      appLog.warning('Error extracting video ID: $url',
          name: _logName, error: e, stackTrace: stack);
      return null;
    }
  }

  /// Extract the video ID from a URL that might redirect to YouTube
  ///
  /// This is the recommended entry point for YouTube URL processing as it:
  /// 1. First tries direct extraction (fast path for obvious YouTube URLs)
  /// 2. If that fails, follows redirects to handle URL shorteners
  /// 3. Checks for embedded YouTube URLs in query parameters
  /// 4. Returns null if all methods fail
  ///
  /// @deprecated Use url_resolver.YouTubeUrlHandling.extractVideoIdWithRedirects instead
  static Future<String?> extractVideoIdWithRedirects(String url) async {
    try {
      appLog.debug('Processing URL for YouTube video ID: $url', name: _logName);

      // Use the consolidated URL processing approach
      final processedUrl = await url_resolver.UrlResolver.processUrl(
        url,
        checkYouTube: true,
        checkNews: false,
      );

      return processedUrl.youtubeVideoId;
    } catch (e, stack) {
      appLog.warning('Error extracting video ID with redirects: $url',
          name: _logName, error: e, stackTrace: stack);
      return null;
    }
  }

  /// Generate a thumbnail URL from a video ID
  ///
  /// Quality options: default, mqdefault, hqdefault, sddefault, maxresdefault
  static String getThumbnailUrl(String videoId, {String quality = 'mqdefault'}) {
    return 'https://img.youtube.com/vi/$videoId/$quality.jpg';
  }

  /// Generate a watch URL for a video ID (alias for getStandardUrl)
  static String getWatchUrl(String videoId) {
    return 'https://www.youtube.com/watch?v=$videoId';
  }

  /// Generate an embedded player URL for a video ID
  static String getEmbedUrl(String videoId) {
    return 'https://www.youtube.com/embed/$videoId';
  }
}

/// Extension for ContentProcessingService to detect YouTube links
extension ContentProcessingServiceYouTube on ContentProcessingService {
  /// Check if content appears to be a YouTube link
  bool isYouTubeLink(String content) {
    // Clean up content by trimming
    final trimmedContent = content.trim();

    // Check if this looks like a URL
    if (trimmedContent.startsWith('http://') ||
        trimmedContent.startsWith('https://') ||
        trimmedContent.startsWith('www.')) {
      return url_resolver.YouTubeUrlHandling.isYouTubeUrl(trimmedContent);
    }

    // Not a URL
    return false;
  }
}
