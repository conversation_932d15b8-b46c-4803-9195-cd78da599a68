import 'package:flutter/material.dart';
import 'package:promz/features/input_selection/models/input_source.dart';
import 'package:promz/features/input_selection/utils/source_card_html_generator.dart';
import 'package:promz_common/promz_common.dart';

/// A compact card for displaying YouTube videos in the input sources list
///
/// This widget is designed to be used in the SourceList to provide a more
/// visually appealing representation of YouTube video sources.
class YouTubeSourceCard extends StatelessWidget {
  static const _logName = 'YouTubeSourceCard';

  final InputSource source;
  final VoidCallback? onTap;
  final VoidCallback? onDelete;

  const YouTubeSourceCard({
    Key? key,
    required this.source,
    this.onTap,
    this.onDelete,
  }) : super(key: key);

  /// Generates HTML representation of this YouTube card
  String toHtml() {
    appLog.info('Generating HTML for YouTube source: ${source.fileName}', name: _logName);
    return SourceCardHtmlGenerator.generateForSource(source);
  }

  @override
  Widget build(BuildContext context) {
    // Extract metadata from the source
    final processingResult = source.processingResult;
    if (processingResult == null) {
      return _buildFallbackCard(context);
    }

    // Extract video details from metadata
    final youtubeMetadata = processingResult.youtubeMetadata;
    final title = youtubeMetadata.title;
    final channelName = youtubeMetadata.channelName;
    final thumbnailUrl = youtubeMetadata.thumbnailUrl;

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4.0),
      clipBehavior: Clip.antiAlias,
      elevation: 1.0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.0),
        side: BorderSide(color: Colors.grey.shade300, width: 1.0),
      ),
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Thumbnail with play button overlay
              _buildThumbnail(context, thumbnailUrl),

              const SizedBox(width: 12.0),

              // Text content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Title
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                    // Channel name with icon
                    Row(
                      children: [
                        Icon(
                          Icons.account_circle,
                          size: 12,
                          color: Colors.grey.shade600,
                        ),
                        const SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            channelName,
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  color: Colors.grey.shade600,
                                ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Delete button
              if (onDelete != null)
                IconButton(
                  icon: const Icon(Icons.close, size: 18),
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  onPressed: onDelete,
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds the thumbnail with a play button overlay
  Widget _buildThumbnail(BuildContext context, String? thumbnailUrl) {
    return Stack(
      alignment: Alignment.center,
      children: [
        // Thumbnail image
        ClipRRect(
          borderRadius: BorderRadius.circular(4.0),
          child: SizedBox(
            width: 120,
            height: 68, // 16:9 aspect ratio
            child: thumbnailUrl != null
                ? Image.network(
                    thumbnailUrl,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => _buildFallbackThumbnail(context),
                  )
                : _buildFallbackThumbnail(context),
          ),
        ),

        // Play button overlay
        Container(
          width: 36,
          height: 36,
          decoration: const BoxDecoration(
            color: Colors.red,
            shape: BoxShape.circle,
          ),
          child: const Icon(
            Icons.play_arrow,
            color: Colors.white,
            size: 24,
          ),
        ),
      ],
    );
  }

  /// Builds a fallback thumbnail when no image URL is available
  Widget _buildFallbackThumbnail(BuildContext context) {
    return Container(
      width: 120,
      height: 68,
      decoration: BoxDecoration(
        color: Colors.grey.shade200,
        borderRadius: BorderRadius.circular(4.0),
      ),
      child: Icon(
        Icons.video_library,
        size: 32,
        color: Colors.red.shade700,
      ),
    );
  }

  /// Builds a fallback card when no metadata is available
  Widget _buildFallbackCard(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4.0),
      child: ListTile(
        leading: Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: Colors.grey.shade200,
            borderRadius: BorderRadius.circular(4.0),
          ),
          child: Icon(
            Icons.video_library,
            size: 24,
            color: Colors.red.shade700,
          ),
        ),
        title: Text(
          source.fileName ?? 'YouTube Video',
          style: TextStyle(
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
        trailing: onDelete != null
            ? IconButton(
                icon: const Icon(Icons.close),
                onPressed: onDelete,
              )
            : null,
        onTap: onTap,
      ),
    );
  }
}
