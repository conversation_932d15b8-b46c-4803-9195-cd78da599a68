import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:promz/features/home/<USER>/variable.dart';
import 'package:promz/features/youtube/services/youtube_service.dart';
import 'package:promz/core/utils/url_resolver.dart' as url_resolver;
import 'package:promz/features/youtube/widgets/youtube_link_preview.dart';
import 'package:promz/generated/content_upload.pb.dart';
import 'package:promz_common/promz_common.dart';
import 'package:promz/core/utils/url_extractor.dart';

/// Widget for inputting and selecting YouTube videos as variables
class YouTubeVideoInput extends StatefulWidget {
  final String initialValue;
  final void Function(Variable) onSelected;
  final YouTubeService youtubeService;
  final Variable variable;

  const YouTubeVideoInput({
    Key? key,
    required this.initialValue,
    required this.onSelected,
    required this.youtubeService,
    required this.variable,
  }) : super(key: key);

  @override
  State<YouTubeVideoInput> createState() => _YouTubeVideoInputState();
}

class _YouTubeVideoInputState extends State<YouTubeVideoInput> {
  static const _logName = 'YouTubeVideoInput';

  late TextEditingController _urlController;
  bool _isLoading = false;
  bool _isValidUrl = false;
  ProcessingResult? _previewMetadata;
  String? _selectedVideoId;

  @override
  void initState() {
    super.initState();
    _urlController = TextEditingController(text: widget.initialValue);

    // If we have an initial value, try to validate it
    if (widget.initialValue.isNotEmpty) {
      if (_isUrlValid(widget.initialValue)) {
        _validateUrl(widget.initialValue);
      } else {
        // It might be a variable ID, not a URL
        final parts = widget.initialValue.split(':');
        if (parts.length >= 3 && parts[0] == 'MEDIA' && parts[1] == 'YOUTUBE_URL') {
          _selectedVideoId = parts[2];
          // Fetch the video details by ID
          _fetchVideoById(_selectedVideoId!);
        }
      }
    }
  }

  @override
  void dispose() {
    _urlController.dispose();
    super.dispose();
  }

  Future<void> _pasteFromClipboard() async {
    final data = await Clipboard.getData(Clipboard.kTextPlain);
    if (data != null && data.text != null) {
      final text = data.text!;
      setState(() {
        _urlController.text = text;
      });

      // First try to extract a URL from the input text if it's not a direct URL
      if (!_isUrlValid(text)) {
        try {
          final processedUrlInfo = await UrlExtractor.extractUrl(text);

          if (processedUrlInfo != null) {
            // We found a URL in the input
            final bestUrl = processedUrlInfo.bestUrl;
            appLog.debug('Extracted URL from pasted text: $bestUrl', name: _logName);

            // Update the URL field with the extracted URL
            setState(() {
              _urlController.text = bestUrl;
            });

            // Validate the extracted URL
            _validateUrl(bestUrl);
            return;
          }
        } catch (e, stack) {
          appLog.error('Error extracting URL from pasted text',
              name: _logName, error: e, stackTrace: stack);
        }
      }

      // If URL extraction failed or wasn't needed, validate the text directly
      _validateUrl(text);
    }
  }

  void _validateUrl(String input) async {
    // First check if the input is directly a valid URL
    var isValid = _isUrlValid(input);
    var urlToUse = input;

    // If not directly valid, try extracting a URL from the text
    if (!isValid && input.isNotEmpty) {
      try {
        final processedUrlInfo = await UrlExtractor.extractUrl(input);

        if (processedUrlInfo != null) {
          // We found a URL in the input
          urlToUse = processedUrlInfo.bestUrl;
          appLog.debug('Extracted URL from input: $urlToUse', name: _logName);

          // Check if the extracted URL is a valid YouTube URL
          isValid = _isUrlValid(urlToUse);

          // Update the text field with the extracted URL if it's valid and different
          if (isValid && _urlController.text != urlToUse) {
            setState(() {
              _urlController.text = urlToUse;
            });
          }
        }
      } catch (e, stack) {
        appLog.error('Error validating input', name: _logName, error: e, stackTrace: stack);
      }
    }

    setState(() {
      _isValidUrl = isValid;
      if (!isValid) {
        _previewMetadata = null;

        // If URL is invalid or empty, clear the selection
        if (input.isEmpty) {
          _clearSelection();
        }
      }
    });

    if (isValid) {
      _fetchPreview(urlToUse);
    }
  }

  bool _isUrlValid(String url) {
    if (url.isEmpty) return false;

    try {
      return url_resolver.YouTubeUrlHandling.isYouTubeUrl(url);
    } catch (e) {
      return false;
    }
  }

  Future<void> _fetchPreview(String url) async {
    if (!_isValidUrl) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Fetch video metadata
      final metadata = await widget.youtubeService.fetchVideoMetadata(url);

      setState(() {
        _previewMetadata = metadata;
        _isLoading = false;
      });

      // Auto-select the video as soon as we have the metadata
      _selectVideo();
    } catch (e, stack) {
      appLog.error('Error fetching YouTube video preview',
          name: _logName, error: e, stackTrace: stack);
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _fetchVideoById(String videoId) async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Construct a URL from the video ID
      final url = url_resolver.YouTubeUrlHandling.getWatchUrl(videoId);

      // Fetch video metadata
      final metadata = await widget.youtubeService.fetchVideoMetadata(url);

      setState(() {
        _previewMetadata = metadata;
        _isLoading = false;
      });
    } catch (e, stack) {
      appLog.error('Error fetching YouTube video by ID',
          name: _logName, error: e, stackTrace: stack);
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _selectVideo() async {
    if (_previewMetadata == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Register the video and get its metadata
      final url = _previewMetadata!.youtubeMetadata.url;
      final metadata = await widget.youtubeService.registerYouTubeVideo(url);

      // Get the video ID from the metadata
      final videoId = metadata[MetadataKeys.videoId] as String? ?? '';

      // Create a variable ID in the format MEDIA:YOUTUBE_URL:videoId
      final variableId = 'MEDIA:YOUTUBE_URL:$videoId';

      // Update the variable with the video ID
      if (videoId.isNotEmpty) {
        _selectedVideoId = videoId;
        widget.onSelected(widget.variable.copyWithValue(variableId));
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e, stack) {
      appLog.error('Error selecting YouTube video', name: _logName, error: e, stackTrace: stack);
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _clearSelection() {
    setState(() {
      _urlController.clear();
      _previewMetadata = null;
      _selectedVideoId = null;
      _isValidUrl = false;
    });

    // Update the variable with empty value
    widget.onSelected(widget.variable.copyWithValue(''));
  }

  @override
  Widget build(BuildContext context) {
    // If we already have a selected video, show its details
    if (_selectedVideoId != null && _previewMetadata != null) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          YouTubeLinkPreview(
            metadata: _previewMetadata!,
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: Text(
                  'YouTube video selected',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.green,
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ),
              TextButton.icon(
                icon: const Icon(Icons.edit),
                label: const Text('Change'),
                onPressed: _clearSelection,
              ),
            ],
          ),
        ],
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // URL Input Field
        TextField(
          controller: _urlController,
          decoration: InputDecoration(
            hintText: 'Enter YouTube video URL',
            border: const OutlineInputBorder(),
            prefixIcon: const Icon(Icons.video_library),
            suffixIcon: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (_urlController.text.isNotEmpty)
                  IconButton(
                    icon: const Icon(Icons.clear),
                    onPressed: _clearSelection,
                  ),
                IconButton(
                  icon: const Icon(Icons.content_paste),
                  onPressed: _pasteFromClipboard,
                ),
              ],
            ),
          ),
          onChanged: _validateUrl,
        ),

        const SizedBox(height: 8),

        // Loading Indicator
        if (_isLoading) const Center(child: CircularProgressIndicator(strokeWidth: 2.0)), // Preview
        if (_previewMetadata != null && !_isLoading)
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              YouTubeLinkPreview(
                metadata: _previewMetadata!,
              ),
            ],
          ),
      ],
    );
  }
}
