import 'dart:async';
import 'package:fixnum/fixnum.dart';
import 'package:html/dom.dart' as html_dom;
import 'package:html/parser.dart' as html_parser;
import 'package:http/http.dart' as http;
import 'package:promz/core/services/attachment/attachment_registry_service.dart';
import 'package:promz/core/utils/url_resolver.dart';
import 'package:promz/features/home/<USER>/entity_detection_service.dart';
import 'package:promz/generated/content_upload.pb.dart';
import 'package:promz_common/promz_common.dart';

/// Service for fetching and managing YouTube video metadata
class YouTubeService {
  static const _logName = 'YouTubeService';

  final http.Client _httpClient;
  final AttachmentRegistryService? _attachmentRegistry;
  final EntityDetectionService? _entityDetectionService;

  // In-memory cache for video metadata
  final Map<String, ProcessingResult> _videoCache = {};

  /// Creates a new instance of the YouTubeService
  ///
  /// If attachmentRegistry is provided, videos will be registered as attachments
  /// for variable resolution. If entityDetectionService is provided, videos will also
  /// be registered as entities for detection in text.
  YouTubeService({
    http.Client? httpClient,
    AttachmentRegistryService? attachmentRegistry,
    EntityDetectionService? entityDetectionService,
  })  : _httpClient = httpClient ?? http.Client(),
        _attachmentRegistry = attachmentRegistry,
        _entityDetectionService = entityDetectionService;

  /// Fetch video metadata from a URL
  /// Returns the video metadata or null if there was an error
  Future<ProcessingResult> fetchVideoMetadata(String url) async {
    try {
      appLog.debug('Fetching YouTube metadata from URL: $url', name: _logName);

      // Extract video ID with redirect handling
      final processedUrl = await UrlResolver.processUrl(url, checkYouTube: true, checkNews: false);
      final videoId = processedUrl.youtubeVideoId;
      if (videoId == null) {
        throw Exception('Could not extract video ID from URL: $url');
      }

      // Check cache first
      if (_videoCache.containsKey(videoId)) {
        appLog.debug('Using cached metadata for video ID: $videoId', name: _logName);
        return _videoCache[videoId]!.deepCopy();
      }

      // Generate standardized URL
      final standardUrl = YouTubeUrlHandling.getWatchUrl(videoId);

      // Fetch the HTML page to extract metadata
      final response = await _httpClient.get(
        Uri.parse(standardUrl),
        headers: {'User-Agent': UrlResolver.defaultUserAgent},
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode != 200) {
        throw Exception('Failed to fetch YouTube page: ${response.statusCode}');
      }

      // Parse the HTML
      final document = html_parser.parse(response.body);

      // Extract metadata
      final processingResult = _extractMetadataFromHtml(document, videoId, standardUrl);

      // Cache the result
      processingResult
        ..contentType = EntityType.youtubeVideo.name
        ..expiresAt = DateTime.now().add(const Duration(hours: 24)).toIso8601String()
        ..youtubeMetadata.url = standardUrl;
      _videoCache[videoId] = processingResult.deepCopy();
      return processingResult;
    } catch (e, stack) {
      appLog.error('Error fetching YouTube metadata', name: _logName, error: e, stackTrace: stack);

      // Return minimal metadata based on the URL
      return await _createMinimalMetadata(url);
    }
  }

  /// Extract metadata from YouTube page HTML
  ProcessingResult _extractMetadataFromHtml(
      html_dom.Document document, String videoId, String url) {
    // Extract title from meta tags
    String title = _extractMetadata(document, [
      'og:title',
      'twitter:title',
      'title',
    ]);

    // Extract description from meta tags
    String description = _extractMetadata(document, [
      'og:description',
      'twitter:description',
      'description',
    ]);

    // Extract channel name
    String channelName = _extractMetadata(document, [
      'og:site_name',
    ]);

    // If channel name is just "YouTube", try to extract the actual channel
    if (channelName == 'YouTube') {
      // Try to extract channel from the page
      final channelElements = document.querySelectorAll('.owner-name a');
      if (channelElements.isNotEmpty) {
        channelName = channelElements.first.text.trim();
      }
    }

    // Extract thumbnail URL
    String thumbnailUrl = _extractMetadata(document, [
      'og:image',
      'twitter:image',
    ]);

    // If no thumbnail found, generate one from the video ID
    if (thumbnailUrl.isEmpty) {
      thumbnailUrl = YouTubeUrlHandling.getThumbnailUrl(videoId);
    }

    // Create content summary
    final content =
        'YouTube Video: $title\nChannel: $channelName\nDescription: $description\nLink: $url';

    // Create YouTube metadata using the Protocol Buffer message
    final youtubeMetadata = YouTubeMetadata()
      ..videoId = videoId
      ..title = title
      ..description = description
      ..channelName = channelName
      ..thumbnailUrl = thumbnailUrl
      ..imageUrl = thumbnailUrl;

    // Create the ProcessingResult with YouTubeMetadata
    final processingResult = ProcessingResult()
      ..contentType = EntityType.youtubeVideo.name
      ..content = content
      ..expiresAt = DateTime.now().add(const Duration(days: 7)).toIso8601String();

    // Set the YouTube metadata
    processingResult.youtubeMetadata = youtubeMetadata;

    // Add additional metadata using explicit fields
    processingResult.sourceUrl = url;
    processingResult.timestamp = Int64(DateTime.now().millisecondsSinceEpoch);

    return processingResult;
  }

  /// Extract metadata from meta tags
  String _extractMetadata(html_dom.Document document, List<String> propertyNames) {
    for (final property in propertyNames) {
      // Try meta[name]
      final metaByName = document.querySelector('meta[name="$property"]');
      if (metaByName != null && metaByName.attributes['content'] != null) {
        return metaByName.attributes['content']!;
      }

      // Try meta[property]
      final metaByProperty = document.querySelector('meta[property="$property"]');
      if (metaByProperty != null && metaByProperty.attributes['content'] != null) {
        return metaByProperty.attributes['content']!;
      }
    }
    return '';
  }

  /// Create minimal metadata from a YouTube URL
  Future<ProcessingResult> _createMinimalMetadata(String url) async {
    // Try to extract video ID with redirect handling first
    String? videoId = await UrlResolver.processUrl(url, checkYouTube: true, checkNews: false)
        .then((value) => value.youtubeVideoId);

    // Fall back to direct extraction if redirect-aware method fails
    videoId ??= YouTubeUrlHandling.extractVideoId(url) ?? '';

    final thumbnailUrl = videoId.isNotEmpty ? YouTubeUrlHandling.getThumbnailUrl(videoId) : '';
    final content = 'YouTube Video\nLink: $url';

    // Create YouTube metadata using the Protocol Buffer message
    final youtubeMetadata = YouTubeMetadata()
      ..videoId = videoId
      ..title = 'YouTube Video'
      ..channelName = 'YouTube Channel'
      ..thumbnailUrl = thumbnailUrl
      ..imageUrl = thumbnailUrl;

    // Create the ProcessingResult with YouTubeMetadata
    final processingResult = ProcessingResult()
      ..contentType = EntityType.youtubeVideo.name
      ..content = content
      ..expiresAt = DateTime.now().add(const Duration(days: 7)).toIso8601String();

    // Set the YouTube metadata
    processingResult.youtubeMetadata = youtubeMetadata;

    // Add additional metadata using explicit fields
    processingResult.sourceUrl = url;
    processingResult.timestamp = Int64(DateTime.now().millisecondsSinceEpoch);

    return processingResult;
  }

  /// Convert ProcessingResult to legacy metadata format for backward compatibility
  Map<String, dynamic> _processingResultToLegacyMetadata(
      {required ProcessingResult processingResult}) {
    return {
      MetadataKeys.videoId: processingResult.youtubeMetadata.videoId,
      MetadataKeys.url: processingResult.sourceUrl,
      MetadataKeys.title: processingResult.youtubeMetadata.title,
      MetadataKeys.excerpt: processingResult.youtubeMetadata.description,
      MetadataKeys.channelName: processingResult.youtubeMetadata.channelName,
      MetadataKeys.thumbnailUrl: processingResult.youtubeMetadata.thumbnailUrl,
      MetadataKeys.type: 'youtube',
    };
  }

  /// Fetch and register a YouTube video as an attachment
  ///
  /// This method fetches video metadata and registers it with the attachment registry
  /// to make it available through variables
  Future<Map<String, dynamic>> fetchAndRegisterVideo(String url) async {
    try {
      final processingResult = await fetchVideoMetadata(url);
      final videoId = processingResult.youtubeMetadata.videoId;

      // Register as attachment if registry is available
      String? attachmentId;
      if (_attachmentRegistry != null && videoId.isNotEmpty) {
        attachmentId = 'youtube_${DateTime.now().millisecondsSinceEpoch}';

        // Clone the result so we can modify it
        final resultToRegister = processingResult.deepCopy();
        resultToRegister.jobId = attachmentId;

        // Register with the new protobuf-based method
        _attachmentRegistry?.registerAttachment(
          id: attachmentId,
          result: resultToRegister,
        );
      }

      // Also register as an entity if entity detection service is available
      if (_entityDetectionService != null && videoId.isNotEmpty) {
        // Convert to legacy format for entity detection
        final legacyMetadata =
            _processingResultToLegacyMetadata(processingResult: processingResult);

        _entityDetectionService?.registerEntity(
          id: videoId,
          text: processingResult.youtubeMetadata.title,
          type: 'youtube',
          processingResult: processingResult,
          metadata: legacyMetadata,
        );
      }

      // Return legacy format for backward compatibility
      return _processingResultToLegacyMetadata(processingResult: processingResult);
    } catch (e, stack) {
      appLog.error('Error registering YouTube video', name: _logName, error: e, stackTrace: stack);
      rethrow;
    }
  }

  /// Register a YouTube video using its URL
  /// This is an alias for fetchAndRegisterVideo with a more consistent name
  Future<Map<String, dynamic>> registerYouTubeVideo(String url) async {
    return fetchAndRegisterVideo(url);
  }

  /// Dispose of resources
  void dispose() {
    _httpClient.close();
    _videoCache.clear();
  }
}

/// Mock implementation of YouTubeService for testing
class MockYouTubeService implements YouTubeService {
  @override
  Future<ProcessingResult> fetchVideoMetadata(String url) async {
    // Try redirect-aware extraction first
    final processedUrl = await UrlResolver.processUrl(url, checkYouTube: true, checkNews: false);
    String? videoId = processedUrl.youtubeVideoId;

    // Fall back to direct extraction or default ID if needed
    videoId ??= YouTubeUrlHandling.extractVideoId(url) ?? 'dQw4w9WgXcQ'; // Default video ID

    // Create a summary of the video for content
    final content =
        'YouTube Video: Mock YouTube Video\nChannel: Mock Channel\nDescription: This is a mock YouTube video for testing\nLink: ${YouTubeUrlHandling.getWatchUrl(videoId)}';

    // Create YouTube metadata using the Protocol Buffer message
    final youtubeMetadata = YouTubeMetadata()
      ..videoId = videoId
      ..title = 'Mock YouTube Video'
      ..description = 'This is a mock YouTube video for testing'
      ..channelName = 'Mock Channel'
      ..thumbnailUrl = YouTubeUrlHandling.getThumbnailUrl(videoId)
      ..imageUrl = YouTubeUrlHandling.getThumbnailUrl(videoId);

    // Create the ProcessingResult with YouTubeMetadata
    final processingResult = ProcessingResult()
      ..contentType = EntityType.youtubeVideo.name
      ..content = content
      ..expiresAt = DateTime.now().add(const Duration(days: 7)).toIso8601String();

    // Set the YouTube metadata
    processingResult.youtubeMetadata = youtubeMetadata;

    // Add additional metadata using explicit fields
    processingResult.sourceUrl = url;
    processingResult.timestamp = Int64(DateTime.now().millisecondsSinceEpoch);

    return processingResult;
  }

  @override
  Future<Map<String, dynamic>> fetchAndRegisterVideo(String url) async {
    final processingResult = await fetchVideoMetadata(url);
    processingResult.jobId = 'mock_youtube_video_id';

    // Convert to legacy format for return
    return {
      MetadataKeys.videoId: processingResult.youtubeMetadata.videoId,
      MetadataKeys.url: url,
      MetadataKeys.title: processingResult.youtubeMetadata.title,
      MetadataKeys.excerpt: processingResult.youtubeMetadata.description,
      MetadataKeys.channelName: processingResult.youtubeMetadata.channelName,
      MetadataKeys.thumbnailUrl: processingResult.youtubeMetadata.thumbnailUrl,
      MetadataKeys.type: 'youtube',
      MetadataKeys.id: 'mock_youtube_video_id',
    };
  }

  @override
  Future<Map<String, dynamic>> registerYouTubeVideo(String url) async {
    return fetchAndRegisterVideo(url);
  }

  @override
  void dispose() {}

  @override
  Map<String, ProcessingResult> get _videoCache => {};

  @override
  AttachmentRegistryService? get _attachmentRegistry => null;

  @override
  EntityDetectionService? get _entityDetectionService => null;

  @override
  http.Client get _httpClient => http.Client();

  @override
  Future<ProcessingResult> _createMinimalMetadata(String url) {
    throw UnimplementedError();
  }

  @override
  String _extractMetadata(html_dom.Document document, List<String> propertyNames) {
    throw UnimplementedError();
  }

  @override
  ProcessingResult _extractMetadataFromHtml(
      html_dom.Document document, String videoId, String url) {
    throw UnimplementedError();
  }

  @override
  Map<String, dynamic> _processingResultToLegacyMetadata(
      {required ProcessingResult processingResult}) {
    throw UnimplementedError();
  }
}
