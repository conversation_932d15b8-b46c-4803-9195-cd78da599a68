import 'package:flutter/material.dart';
import 'package:promz/features/input_selection/models/input_source.dart';
import 'package:promz/features/input_selection/utils/source_card_html_generator.dart';
import 'package:promz_common/promz_common.dart';

/// A compact card for displaying news articles in the input sources list
///
/// This widget is designed to be used in the SourceList to provide a more
/// visually appealing representation of news article sources.
class NewsArticleSourceCard extends StatelessWidget {
  static const _logName = 'NewsArticleSourceCard';

  final InputSource source;
  final VoidCallback? onTap;
  final VoidCallback? onDelete;

  const NewsArticleSourceCard({
    Key? key,
    required this.source,
    this.onTap,
    this.onDelete,
  }) : super(key: key);

  /// Generates HTML representation of this news article card
  String toHtml() {
    appLog.info('Generating HTML for news article source: ${source.fileName}', name: _logName);
    return SourceCardHtmlGenerator.generateForSource(source);
  }

  @override
  Widget build(BuildContext context) {
    final processingResult = source.processingResult;
    if (processingResult == null) {
      return _buildFallbackCard(context);
    }

    final articleMetadata = processingResult.articleMetadata;
    final title = articleMetadata.title;
    final siteName = articleMetadata.siteName;
    final excerpt = articleMetadata.excerpt;
    final imageUrl = articleMetadata.imageUrl;

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4.0),
      clipBehavior: Clip.antiAlias,
      elevation: 1.0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.0),
        side: BorderSide(color: Colors.grey.shade300, width: 1.0),
      ),
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Image or icon
              _buildLeadingImage(context, imageUrl),

              const SizedBox(width: 12.0),

              // Text content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Title
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),

                    // Source name with icon
                    Row(
                      children: [
                        Icon(
                          Icons.public,
                          size: 12,
                          color: Colors.grey.shade600,
                        ),
                        const SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            siteName,
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  color: Colors.grey.shade600,
                                ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),

                    // Excerpt
                    if (excerpt.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Text(
                        excerpt,
                        style: Theme.of(context).textTheme.bodySmall,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ],
                ),
              ),

              // Delete button
              if (onDelete != null)
                IconButton(
                  icon: const Icon(Icons.close, size: 18),
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  onPressed: onDelete,
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds the leading image or icon for the card
  Widget _buildLeadingImage(BuildContext context, String? imageUrl) {
    appLog.info('Building leading image for source: ${source.fileName}', name: _logName);
    if (imageUrl != null && imageUrl.isNotEmpty) {
      appLog.info('Using image URL: $imageUrl', name: _logName);
      return ClipRRect(
        borderRadius: BorderRadius.circular(4.0),
        child: SizedBox(
          width: 48,
          height: 48,
          child: Image.network(
            imageUrl,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) => _buildFallbackIcon(context),
          ),
        ),
      );
    } else {
      appLog.info('Using fallback icon', name: _logName);
      return _buildFallbackIcon(context);
    }
  }

  /// Builds a fallback icon when no image is available
  Widget _buildFallbackIcon(BuildContext context) {
    appLog.info('Building fallback icon', name: _logName);
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: Colors.grey.shade200,
        borderRadius: BorderRadius.circular(4.0),
      ),
      child: Icon(
        Icons.article,
        size: 24,
        color: Colors.grey.shade600,
      ),
    );
  }

  /// Builds a fallback card when no metadata is available
  Widget _buildFallbackCard(BuildContext context) {
    appLog.info('Building fallback card for source: ${source.fileName}', name: _logName);
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4.0),
      child: ListTile(
        leading: _buildFallbackIcon(context),
        title: Text(
          source.fileName ?? 'News Article',
          style: TextStyle(
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
        trailing: onDelete != null
            ? IconButton(
                icon: const Icon(Icons.close),
                onPressed: onDelete,
              )
            : null,
        onTap: onTap,
      ),
    );
  }
}
