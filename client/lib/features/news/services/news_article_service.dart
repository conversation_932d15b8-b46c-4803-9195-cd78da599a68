import 'dart:async';
import 'dart:convert';
import 'package:html/dom.dart' as html_dom;
import 'package:html/parser.dart' as html_parser;
import 'package:http/http.dart' as http;
import 'package:promz/core/services/attachment/attachment_registry_service.dart';
import 'package:promz/features/home/<USER>/entity_detection_service.dart';
import 'package:promz/generated/content_upload.pb.dart';
import 'package:promz_common/promz_common.dart';
import 'package:readability/readability.dart' as readability;

/// Service for fetching and processing news articles
class NewsArticleService {
  static const _logName = 'NewsArticleService';

  final http.Client _httpClient;
  final AttachmentRegistryService? _attachmentRegistry;
  final EntityDetectionService? _entityDetectionService;

  /// Creates a new instance of the NewsArticleService
  ///
  /// If attachmentRegistry is provided, articles will be registered as attachments
  /// for variable resolution. If entityDetectionService is provided, articles will also
  /// be registered as entities for detection in text.
  NewsArticleService({
    http.Client? httpClient,
    AttachmentRegistryService? attachmentRegistry,
    EntityDetectionService? entityDetectionService,
  })  : _httpClient = httpClient ?? http.Client(),
        _attachmentRegistry = attachmentRegistry,
        _entityDetectionService = entityDetectionService;

  /// Fetch article content from a direct, clean URL (not embedded in text)
  /// Returns a ProcessingResult with ArticleMetadata
  Future<ProcessingResult> fetchArticleContent(String url) async {
    // Create the ProcessingResult with ArticleMetadata that we'll populate below
    final processingResult = ProcessingResult()
      ..contentType = EntityType.news.name
      ..expiresAt = DateTime.now().add(const Duration(hours: 24)).toIso8601String();

    final articleMetadata = ArticleMetadata()
      ..finalUrl = url
      ..title = extractSiteName(url);

    processingResult.articleMetadata = articleMetadata;

    try {
      appLog.debug('Fetching article content from URL: $url', name: _logName);

      // Try the readability package first for better content extraction
      try {
        // Validate URL format before passing to readability
        if (isValidUrl(url)) {
          try {
            final article = await readability.parseAsync(url);

            appLog.debug(
                'Successfully extracted article with readability (${article.content?.length ?? 0} chars)',
                name: _logName);

            // Set content in the ProcessingResult
            processingResult.content = article.textContent ?? '';

            // Populate the ArticleMetadata
            articleMetadata.title = article.title ?? '';
            articleMetadata.author = article.author ?? '';
            articleMetadata.excerpt = article.excerpt ?? '';
            articleMetadata.siteName = article.siteName ?? '';
            articleMetadata.htmlContent = article.content ?? '';

            // Set the ArticleMetadata in the ProcessingResult
            processingResult.articleMetadata = articleMetadata;

            return processingResult;
          } catch (e, stack) {
            appLog.warning('Readability extraction failed, will try manual HTTP request',
                name: _logName, error: e, stackTrace: stack);
            // Continue to HTTP fallback
          }
        }
      } catch (e, stack) {
        appLog.warning('Error during readability attempt, will try manual HTTP request',
            name: _logName, error: e, stackTrace: stack);
        processingResult.errorMessage = e.toString();
      }
    } catch (e, stack) {
      appLog.error('Error fetching article content', name: _logName, error: e, stackTrace: stack);
      processingResult.errorMessage = e.toString();
    }

    return processingResult;
  }

  /// Fetch and register an article as an attachment
  ///
  /// This method fetches an article and registers it with the attachment registry
  /// to make its content available through variables
  Future<ProcessingResult> fetchAndRegisterArticle(String url) async {
    return fetchAndRegisterArticleWithMetadata(url: url);
  }

  /// Fetch and register an article with pre-parsed metadata
  ///
  /// This method handles cases where the article URL is provided with optional metadata
  /// It fetches the full content and registers everything as an attachment
  /// and with the entity detection service, using ArticleMetadata.
  Future<ProcessingResult> fetchAndRegisterArticleWithMetadata({
    required String url,
    String? title,
    String? description,
    bool fetchFullContent = true,
  }) async {
    try {
      appLog.debug('Processing article with metadata: $url', name: _logName);

      // Create the ProcessingResult with ArticleMetadata
      final processingResult = ProcessingResult()
        ..contentType = EntityType.news.name
        ..expiresAt = DateTime.now().add(const Duration(hours: 24)).toIso8601String();

      final articleMetadata = ArticleMetadata()
        ..url = url
        ..finalUrl = url // Will be updated if redirects occur
        ..title = title ?? ''
        ..excerpt = description ?? '';

      processingResult.articleMetadata = articleMetadata;

      // Optionally fetch the full content
      if (fetchFullContent) {
        try {
          final fetchedResult = await fetchArticleContent(url);

          // Update our processing result with content from the fetched result
          processingResult.content = fetchedResult.content;

          // If article metadata was successfully retrieved, use it
          if (fetchedResult.hasArticleMetadata()) {
            final fetchedMetadata = fetchedResult.articleMetadata;

            // Use title from article if available, otherwise use provided title
            if (fetchedMetadata.title.isNotEmpty) {
              articleMetadata.title = fetchedMetadata.title;
            } else if (title != null && title.isNotEmpty) {
              articleMetadata.title = title;
            }

            // Use excerpt from article if available, otherwise use provided description
            if (fetchedMetadata.excerpt.isNotEmpty) {
              articleMetadata.excerpt = fetchedMetadata.excerpt;
            } else if (description != null && description.isNotEmpty) {
              articleMetadata.excerpt = description;
            }

            // Copy other metadata fields
            articleMetadata.siteName = fetchedMetadata.siteName;
            articleMetadata.author = fetchedMetadata.author;
            articleMetadata.imageUrl = fetchedMetadata.imageUrl;
            articleMetadata.language = fetchedMetadata.language;
            articleMetadata.publishDate = fetchedMetadata.publishDate;

            // Update the final URL if available
            if (fetchedMetadata.finalUrl.isNotEmpty) {
              articleMetadata.finalUrl = fetchedMetadata.finalUrl;
            }
          }
        } catch (e, stack) {
          // If fetching fails, use provided metadata only
          processingResult.content = description ?? '';
          appLog.warning('Failed to fetch full article content, using provided metadata only',
              name: _logName, error: e, stackTrace: stack);
        }
      } else {
        // Use provided metadata only
        processingResult.content = description ?? '';
      }

      // Ensure required fields have default values
      if (processingResult.content.isEmpty) {
        processingResult.content = '';
      }

      if (articleMetadata.title.isEmpty) {
        articleMetadata.title = 'Article from ${Uri.parse(url).host}';
      }

      // Register as attachment if registry is available
      if (_attachmentRegistry != null) {
        String attachmentId = 'news_${DateTime.now().millisecondsSinceEpoch}';

        // Register the ProcessingResult directly with the new protobuf-based method
        _attachmentRegistry?.registerAttachment(
          id: attachmentId,
          result: processingResult,
        );

        // Add the attachment ID to the ProcessingResult
        processingResult.jobId = attachmentId;
      }

      // Also register as an entity if entity detection service is available
      if (_entityDetectionService != null) {
        _entityDetectionService?.registerNewsArticle(
          url: articleMetadata.url,
          finalUrl: articleMetadata.finalUrl,
          contents: processingResult.content,
          title: articleMetadata.title,
          author: articleMetadata.author,
          excerpt: articleMetadata.excerpt,
          siteName: articleMetadata.siteName,
        );

        appLog.debug('Entity: ${articleMetadata.title}, ${articleMetadata.finalUrl}',
            name: _logName);
      }

      // Return the consolidated ProcessingResult with ArticleMetadata
      return processingResult;
    } catch (e, stack) {
      appLog.error('Error registering article with metadata',
          name: _logName, error: e, stackTrace: stack);

      // Create a basic ProcessingResult with error information
      final processingResult = ProcessingResult()
        ..contentType = EntityType.news.name
        ..expiresAt = DateTime.now().add(const Duration(hours: 24)).toIso8601String()
        ..errorMessage = e.toString();

      final articleMetadata = ArticleMetadata()
        ..url = url
        ..finalUrl = url
        ..title = title ?? 'Article from ${Uri.parse(url).host}'
        ..excerpt = description ?? '';

      processingResult.articleMetadata = articleMetadata;

      return processingResult;
    }
  }

  /// Fetch just the metadata for an article without the full content
  /// This is a lightweight operation suitable for previews
  Future<ProcessingResult> fetchArticlePreview(String url) async {
    try {
      appLog.debug('Fetching article preview from URL: $url', name: _logName);

      // Create the ProcessingResult with ArticleMetadata
      final processingResult = ProcessingResult()
        ..contentType = EntityType.news.name
        ..expiresAt = DateTime.now().add(const Duration(hours: 24)).toIso8601String();

      final articleMetadata = ArticleMetadata()
        ..url = url
        ..finalUrl = url;

      // Make a GET request to fetch HTML (with a short timeout)
      final response = await _httpClient.get(
        Uri.parse(url),
        headers: {'User-Agent': defaultUserAgent},
      ).timeout(const Duration(seconds: 5));

      // Get the final URL after redirects
      final finalUrl = response.request?.url.toString() ?? url;
      articleMetadata.finalUrl = finalUrl;

      // Parse the HTML
      final document = html_parser.parse(response.body);

      // Extract metadata from Open Graph tags
      final title = _extractMetadata(document, ['og:title', 'twitter:title']);
      articleMetadata.title = title;

      final description =
          _extractMetadata(document, ['og:description', 'twitter:description', 'description']);
      articleMetadata.excerpt = description;

      final imageUrl = _extractMetadata(document, ['og:image', 'twitter:image']);
      articleMetadata.imageUrl = imageUrl;

      final siteName = _extractMetadata(document, ['og:site_name']);
      articleMetadata.siteName = siteName;

      final language = _extractMetadata(document, ['language', 'content-language']);
      articleMetadata.language = language;

      final publishDate = _extractMetadata(document, ['article:published_time', 'pubdate', 'date']);
      articleMetadata.publishDate = publishDate;

      // Set the ArticleMetadata in the ProcessingResult
      processingResult.articleMetadata = articleMetadata;

      return processingResult;
    } catch (e, stack) {
      appLog.error('Error fetching article preview', name: _logName, error: e, stackTrace: stack);

      // Create a basic ProcessingResult with error information
      final processingResult = ProcessingResult()
        ..contentType = EntityType.news.name
        ..expiresAt = DateTime.now().add(const Duration(hours: 24)).toIso8601String()
        ..errorMessage = e.toString();

      final articleMetadata = ArticleMetadata()
        ..url = url
        ..finalUrl = url
        ..title = 'Article from ${Uri.parse(url).host}'
        ..siteName = Uri.parse(url).host;

      processingResult.articleMetadata = articleMetadata;

      return processingResult;
    }
  }

  /// Dispose of resources
  void dispose() {
    _httpClient.close();
  }

  /// Extract metadata from HTML document using property names
  /// Checks meta tags, Open Graph tags, and other common metadata locations
  String _extractMetadata(html_dom.Document document, List<String> propertyNames) {
    try {
      // Check for meta tags with name attribute
      for (final name in propertyNames) {
        final metaTag = document.querySelector('meta[name="$name"]');
        if (metaTag != null && metaTag.attributes.containsKey('content')) {
          final content = metaTag.attributes['content'];
          if (content != null && content.isNotEmpty) {
            return content;
          }
        }
      }

      // Check for Open Graph meta tags (property attribute)
      for (final name in propertyNames) {
        final metaTag = document.querySelector('meta[property="$name"]');
        if (metaTag != null && metaTag.attributes.containsKey('content')) {
          final content = metaTag.attributes['content'];
          if (content != null && content.isNotEmpty) {
            return content;
          }
        }
      }

      // Check for Twitter Card meta tags
      for (final name in propertyNames) {
        final metaTag = document.querySelector('meta[name="twitter:$name"]');
        if (metaTag != null && metaTag.attributes.containsKey('content')) {
          final content = metaTag.attributes['content'];
          if (content != null && content.isNotEmpty) {
            return content;
          }
        }
      }

      // Check for Dublin Core meta tags
      for (final name in propertyNames) {
        final metaTag = document.querySelector('meta[name="dc.$name"]');
        if (metaTag != null && metaTag.attributes.containsKey('content')) {
          final content = metaTag.attributes['content'];
          if (content != null && content.isNotEmpty) {
            return content;
          }
        }
      }

      // Check for JSON-LD script tag
      final jsonLdScript = document.querySelector('script[type="application/ld+json"]');
      if (jsonLdScript != null) {
        try {
          final jsonData = jsonDecode(jsonLdScript.text);
          for (final name in propertyNames) {
            final value = _extractJsonValue(jsonData, name);
            if (value != null && value.isNotEmpty) {
              return value;
            }
          }
        } catch (e) {
          // Ignore JSON parsing errors
        }
      }

      // Check for schema.org microdata
      for (final name in propertyNames) {
        final element = document.querySelector('[itemprop="$name"]');
        if (element != null) {
          final content = element.attributes['content'];
          if (content != null && content.isNotEmpty) {
            return content;
          }
          if (element.text.isNotEmpty) {
            return element.text.trim();
          }
        }
      }

      // For specific elements based on property name
      if (propertyNames.contains('title') && document.querySelector('title') != null) {
        return document.querySelector('title')!.text.trim();
      }

      if (propertyNames.contains('description')) {
        final description = document.querySelector('meta[name="description"]');
        if (description != null && description.attributes.containsKey('content')) {
          return description.attributes['content']!;
        }
      }
    } catch (e) {
      appLog.warning('Error extracting metadata for properties: $propertyNames',
          name: _logName, error: e);
    }

    // Return empty string if not found
    return '';
  }

  /// Extract a value from a JSON object with potential nesting
  String? _extractJsonValue(dynamic json, String key) {
    if (json is Map) {
      if (json.containsKey(key)) {
        final value = json[key];
        if (value is String) {
          return value;
        } else if (value != null) {
          return value.toString();
        }
      }

      // Check for nested objects
      for (final entry in json.entries) {
        if (entry.value is Map || entry.value is List) {
          final nestedValue = _extractJsonValue(entry.value, key);
          if (nestedValue != null) {
            return nestedValue;
          }
        }
      }
    } else if (json is List) {
      // Check each item in the list
      for (final item in json) {
        final nestedValue = _extractJsonValue(item, key);
        if (nestedValue != null) {
          return nestedValue;
        }
      }
    }
    return null;
  }
}

/// Mock implementation of NewsArticleService for testing
/// This mock is used to provide test implementations of the NewsArticleService methods
class MockNewsArticleService implements NewsArticleService {
  @override
  Future<ProcessingResult> fetchArticleContent(String url) async {
    final processingResult = ProcessingResult()
      ..contentType = EntityType.news.name
      ..content = 'Mock article content'
      ..expiresAt = DateTime.now().add(const Duration(hours: 24)).toIso8601String();

    final articleMetadata = ArticleMetadata()
      ..url = url
      ..finalUrl = url
      ..title = 'Mock article title'
      ..excerpt = 'Mock article excerpt'
      ..siteName = 'Mock Site'
      ..author = 'Mock Author';

    processingResult.articleMetadata = articleMetadata;

    return processingResult;
  }

  @override
  Future<ProcessingResult> fetchArticlePreview(String url) async {
    final processingResult = ProcessingResult()
      ..contentType = EntityType.news.name
      ..expiresAt = DateTime.now().add(const Duration(hours: 24)).toIso8601String();

    final articleMetadata = ArticleMetadata()
      ..url = url
      ..finalUrl = url
      ..title = 'Mock article title'
      ..excerpt = 'Mock article excerpt'
      ..siteName = 'Mock Site'
      ..imageUrl = 'https://example.com/image.jpg';

    processingResult.articleMetadata = articleMetadata;

    return processingResult;
  }

  @override
  Future<ProcessingResult> fetchAndRegisterArticle(String url) async {
    final processingResult = ProcessingResult()
      ..contentType = EntityType.news.name
      ..content = 'Mock article content'
      ..expiresAt = DateTime.now().add(const Duration(hours: 24)).toIso8601String();

    final articleMetadata = ArticleMetadata()
      ..url = url
      ..finalUrl = url
      ..title = 'Mock article title';

    processingResult.articleMetadata = articleMetadata;
    processingResult.jobId = 'mock_article_id';

    return processingResult;
  }

  @override
  Future<ProcessingResult> fetchAndRegisterArticleWithMetadata({
    required String url,
    String? title,
    String? description,
    bool fetchFullContent = true,
  }) async {
    final processingResult = ProcessingResult()
      ..contentType = EntityType.news.name
      ..content = 'Mock article content'
      ..expiresAt = DateTime.now().add(const Duration(hours: 24)).toIso8601String();

    final articleMetadata = ArticleMetadata()
      ..url = url
      ..finalUrl = url
      ..title = title ?? 'Mock article title'
      ..excerpt = description ?? 'Mock article excerpt'
      ..siteName = 'Mock Site'
      ..author = 'Mock Author';

    processingResult.articleMetadata = articleMetadata;
    processingResult.jobId = 'mock_article_id';

    return processingResult;
  }

  // Private methods need to be implemented because they're part of the class interface

  @override
  void dispose() {}

  @override
  AttachmentRegistryService? get _attachmentRegistry => throw UnimplementedError();

  @override
  EntityDetectionService? get _entityDetectionService => throw UnimplementedError();

  @override
  String _extractMetadata(html_dom.Document document, List<String> propertyNames) {
    return 'Mock metadata';
  }

  @override
  String? _extractJsonValue(dynamic json, String key) {
    return 'Mock JSON value';
  }

  @override
  http.Client get _httpClient => throw UnimplementedError();
}
