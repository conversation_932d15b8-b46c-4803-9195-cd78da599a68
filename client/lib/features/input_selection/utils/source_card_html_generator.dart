import 'package:promz/core/utils/file_icon_helper.dart';
import 'package:promz/features/input_selection/models/input_source.dart';
import 'package:promz_common/promz_common.dart';

/// Class responsible for generating HTML representations of source cards
class SourceCardHtmlGenerator {
  static const _logName = 'SourceCardHtmlGenerator';

  /// Generates HTML representation for a given source
  static String generateForSource(InputSource source) {
    appLog.info('Generating HTML for source: ${source.fileName}', name: _logName);

    if (source.type == InputSourceType.youtubeVideo &&
        source.processingResult != null &&
        source.processingResult!.hasYoutubeMetadata()) {
      return _generateYouTubeSourceHtml(source);
    } else if (source.type == InputSourceType.news &&
        source.processingResult != null &&
        source.processingResult!.hasArticleMetadata()) {
      return _generateNewsSourceHtml(source);
    } else {
      return _generateStandardSourceHtml(source);
    }
  }

  /// Generates HTML for YouTube source cards
  static String _generateYouTubeSourceHtml(InputSource source) {
    final processingResult = source.processingResult!;
    final youtubeMetadata = processingResult.youtubeMetadata;
    final title = processingResult.title.isNotEmpty ? processingResult.title : 'YouTube Video';
    final channelName =
        youtubeMetadata.channelName.isNotEmpty ? youtubeMetadata.channelName : 'Unknown Channel';
    final videoId = youtubeMetadata.videoId;
    final thumbnailUrl = youtubeMetadata.thumbnailUrl;

    return '''
    <div class="source-card youtube-card">
      <div class="thumbnail">
        ${thumbnailUrl.isNotEmpty ? '<img src="$thumbnailUrl" alt="$title" />' : '<div class="placeholder-thumbnail"><i class="icon-youtube"></i></div>'}
      </div>
      <div class="content">
        <h3 class="title">$title</h3>
        <p class="channel"><i class="icon-user"></i> $channelName</p>
        ${videoId.isNotEmpty ? '<p class="link">https://youtu.be/$videoId</p>' : ''}
      </div>
    </div>
    ''';
  }

  /// Generates HTML for news article source cards
  static String _generateNewsSourceHtml(InputSource source) {
    final processingResult = source.processingResult!;
    final articleMetadata = processingResult.articleMetadata;
    final title = processingResult.title.isNotEmpty ? processingResult.title : 'News Article';
    final siteName = articleMetadata.siteName.isNotEmpty
        ? articleMetadata.siteName
        : _extractDomainFromUrl(articleMetadata.url);
    final url = articleMetadata.url;
    final imageUrl = articleMetadata.imageUrl;

    return '''
    <div class="source-card news-card">
      <div class="thumbnail">
        ${imageUrl.isNotEmpty ? '<img src="$imageUrl" alt="$title" />' : '<div class="placeholder-thumbnail"><i class="icon-newspaper"></i></div>'}
      </div>
      <div class="content">
        <h3 class="title">$title</h3>
        <p class="site"><i class="icon-globe"></i> $siteName</p>
        ${url.isNotEmpty ? '<p class="link">$url</p>' : ''}
      </div>
    </div>
    ''';
  }

  /// Generates HTML for default source cards
  static String _generateStandardSourceHtml(InputSource source) {
    final title = source.fileName ?? 'Source';
    final typeDescription = FileIconHelper.getSourceTypeDescription(source) ?? '';
    final iconClass = _getIconClassForSourceType(source.type);

    return '''
    <div class="source-card standard-card">
      <div class="icon">
        <i class="$iconClass"></i>
      </div>
      <div class="content">
        <h3 class="title">$title</h3>
        ${typeDescription.isNotEmpty ? '<p class="type">$typeDescription</p>' : ''}
      </div>
    </div>
    ''';
  }

  /// Creates a skeleton HTML for loading state
  static String generateSkeletonHtml() {
    return '''
    <div class="source-card skeleton-card">
      <div class="skeleton-thumbnail"></div>
      <div class="content">
        <div class="skeleton-title"></div>
        <div class="skeleton-subtitle"></div>
        <div class="skeleton-text"></div>
      </div>
    </div>
    ''';
  }

  /// Gets the appropriate icon class for a source type
  static String _getIconClassForSourceType(InputSourceType type) {
    switch (type) {
      case InputSourceType.file:
        return 'icon-file';
      case InputSourceType.text:
        return 'icon-text';
      case InputSourceType.clipboard:
        return 'icon-clipboard';
      case InputSourceType.youtubeVideo:
        return 'icon-youtube';
      case InputSourceType.news:
        return 'icon-newspaper';
      default:
        return 'icon-document';
    }
  }

  /// Extracts the domain from a URL
  static String _extractDomainFromUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.host;
    } catch (_) {
      return 'Unknown Source';
    }
  }

  /// Gets the CSS styles for source cards
  static String getSourceCardCss() {
    return '''
    .source-card {
      display: flex;
      padding: 8px;
      margin-bottom: 8px;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      background-color: #f8f8f8;
    }
    
    .source-card .thumbnail {
      width: 80px;
      height: 60px;
      margin-right: 12px;
      border-radius: 4px;
      overflow: hidden;
      background-color: #e0e0e0;
    }
    
    .source-card .thumbnail img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    .source-card .placeholder-thumbnail {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #e0e0e0;
    }
    
    .source-card .icon {
      width: 48px;
      height: 48px;
      margin-right: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #e0e0e0;
      border-radius: 4px;
    }
    
    .source-card .content {
      flex: 1;
    }
    
    .source-card .title {
      margin: 0 0 4px 0;
      font-size: 16px;
      font-weight: bold;
      color: #1976d2;
    }
    
    .source-card .channel,
    .source-card .site,
    .source-card .type {
      margin: 0 0 2px 0;
      font-size: 12px;
      color: #666;
    }
    
    .source-card .link {
      margin: 4px 0 0 0;
      font-size: 12px;
      color: #1976d2;
      word-break: break-all;
    }
    
    /* Icon styles */
    .icon-youtube, .icon-newspaper, .icon-file, 
    .icon-text, .icon-clipboard, .icon-document,
    .icon-user, .icon-globe {
      font-family: 'Material Icons';
      font-size: 24px;
      color: #666;
    }
    
    .icon-youtube:before { content: '\\e064'; }
    .icon-newspaper:before { content: '\\e873'; }
    .icon-file:before { content: '\\e24d'; }
    .icon-text:before { content: '\\e264'; }
    .icon-clipboard:before { content: '\\e14d'; }
    .icon-document:before { content: '\\e873'; }
    .icon-user:before { content: '\\e7fd'; }
    .icon-globe:before { content: '\\e894'; }
    
    /* Skeleton card styles */
    .skeleton-card {
      display: flex;
      padding: 8px;
      margin-bottom: 8px;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      background-color: #f8f8f8;
    }
    
    .skeleton-thumbnail {
      width: 80px;
      height: 60px;
      margin-right: 12px;
      border-radius: 4px;
      background-color: #e0e0e0;
      animation: shimmer 1.5s infinite;
    }
    
    .skeleton-title {
      width: 100%;
      height: 18px;
      margin-bottom: 8px;
      border-radius: 2px;
      background-color: #e0e0e0;
      animation: shimmer 1.5s infinite;
    }
    
    .skeleton-subtitle {
      width: 60%;
      height: 14px;
      margin-bottom: 6px;
      border-radius: 2px;
      background-color: #e0e0e0;
      animation: shimmer 1.5s infinite;
    }
    
    .skeleton-text {
      width: 80%;
      height: 14px;
      border-radius: 2px;
      background-color: #e0e0e0;
      animation: shimmer 1.5s infinite;
    }
    
    @keyframes shimmer {
      0% {
        opacity: 0.5;
      }
      50% {
        opacity: 1.0;
      }
      100% {
        opacity: 0.5;
      }
    }
    ''';
  }
}
