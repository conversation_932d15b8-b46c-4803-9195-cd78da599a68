import 'package:flutter/material.dart';
import 'base_source_tab.dart';

class ClipboardTab extends BaseSourceTab {
  const ClipboardTab({
    super.key,
    required super.source,
  });

  @override
  Widget buildContent(BuildContext context) {
    if (source.content == null) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.content_paste_off,
              size: 48,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'No clipboard content available',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (source.sourceApp != null) ...[
          Text(
            'Source: ${source.sourceApp}',
            style: Theme.of(context).textTheme.bodySmall,
          ),
          const Divider(),
        ],
        Expanded(
          child: SingleChildScrollView(
            child: Text(
              source.content!,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ),
      ],
    );
  }
}
