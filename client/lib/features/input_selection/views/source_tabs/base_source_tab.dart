import 'package:flutter/material.dart';
import '../../models/input_source.dart';

abstract class BaseSourceTab extends StatelessWidget {
  final InputSource source;

  const BaseSourceTab({
    Key? key,
    required this.source,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: buildContent(context),
    );
  }

  Widget buildContent(BuildContext context);
}
