import 'package:equatable/equatable.dart';

class FileInfo extends Equatable {
  final String path;
  final String fileName;
  final String? mimeType;
  final String? content;
  final int? size;

  const FileInfo({
    required this.path,
    required this.fileName,
    this.mimeType,
    this.content,
    this.size,
  });

  @override
  List<Object?> get props => [path, fileName, mimeType, content, size];
}
