import 'package:equatable/equatable.dart';
import 'package:promz/generated/content_upload.pb.dart';

/// Enum representing both the type of input source and its origin.
///
/// This enum combines information about what the source is (text, PDF, etc.)
/// and how it was added to the app (manually, shared, clipboard, etc.).
enum InputSourceType {
  // Content types
  text,
  pdf,
  zip,
  file,
  news,

  // Origin types - how the content was added to the app
  clipboard, // Content added from clipboard
  shared, // Content shared from another app
  manual, // Content manually entered by user

  // Combined types (for more specific categorization if needed)
  clipboardText,
  sharedFile,
  sharedText,
  manualFile,
  manualText,
  youtubeVideo,
}

extension InputSourceTypeExtension on InputSourceType {
  /// Returns true if this type represents a file-based source
  bool get isFileType =>
      this == InputSourceType.pdf ||
      this == InputSourceType.text ||
      this == InputSourceType.zip ||
      this == InputSourceType.file ||
      this == InputSourceType.news ||
      this == InputSourceType.sharedFile ||
      this == InputSourceType.manualFile;

  /// Returns true if this type represents a clipboard source
  bool get isClipboardType =>
      this == InputSourceType.clipboard || this == InputSourceType.clipboardText;

  /// Returns true if this type represents a shared source
  bool get isSharedType =>
      this == InputSourceType.shared ||
      this == InputSourceType.sharedFile ||
      this == InputSourceType.sharedText;

  /// Returns true if this type represents a manual source
  bool get isManualType =>
      this == InputSourceType.manual ||
      this == InputSourceType.manualFile ||
      this == InputSourceType.manualText;
}

class InputSource extends Equatable {
  final String? id;
  final InputSourceType type;
  final String? filePath;
  final String? fileName;
  final String? content;
  final String? mimeType;
  final ProcessingResult? processingResult;
  final String? contentHash;
  final String? sourceApp;
  final bool isSelected;

  const InputSource({
    this.id,
    required this.type,
    this.filePath,
    this.fileName,
    this.content,
    this.mimeType,
    this.processingResult,
    this.contentHash,
    this.sourceApp,
    this.isSelected = false,
  });

  InputSource copyWith({
    String? id,
    InputSourceType? type,
    String? filePath,
    String? fileName,
    String? content,
    String? mimeType,
    ProcessingResult? processingResult,
    String? contentHash,
    String? sourceApp,
    bool? isSelected,
  }) {
    return InputSource(
      id: id ?? this.id,
      type: type ?? this.type,
      filePath: filePath ?? this.filePath,
      fileName: fileName ?? this.fileName,
      content: content ?? this.content,
      mimeType: mimeType ?? this.mimeType,
      processingResult: processingResult ?? this.processingResult,
      contentHash: contentHash ?? this.contentHash,
      sourceApp: sourceApp ?? this.sourceApp,
      isSelected: isSelected ?? this.isSelected,
    );
  }

  bool get isFile => type.isFileType;

  bool get isClipboard => type.isClipboardType;

  bool get isZipFile {
    if (mimeType?.toLowerCase().contains('zip') ?? false) return true;
    return fileName?.toLowerCase().endsWith('.zip') ?? false;
  }

  @override
  List<Object?> get props => [
        id,
        type,
        filePath,
        fileName,
        content,
        mimeType,
        processingResult,
        contentHash,
        sourceApp,
        isSelected
      ];
}
