import 'package:flutter/material.dart';
import 'package:promz/core/utils/file_icon_helper.dart';
import 'package:promz/features/conversation/widgets/chat_source_card.dart';
import 'package:promz/features/input_selection/models/input_source.dart';
import 'package:promz/features/input_selection/utils/source_card_html_generator.dart';
import 'package:promz/features/input_selection/widgets/source_card_skeleton.dart';
import 'package:promz/features/news/widgets/news_article_source_card.dart';
import 'package:promz/features/youtube/widgets/youtube_source_card.dart';
import 'package:promz/generated/common.pbenum.dart';
import 'package:promz_common/promz_common.dart';
import 'package:sprintf/sprintf.dart';

/// A factory class for creating source cards based on input source type and metadata
class SourceCardFactory {
  static const _logName = 'SourceCardFactory';

  /// Determines if a source is still loading its metadata
  static bool isLoadingMetadata(InputSource source) {
    // Check if the source has a processing status indicating it's still loading
    if (source.processingResult != null) {
      final status = source.processingResult!.status;
      return status == UploadStatus.UPLOAD_STATUS_PROCESSING ||
          status == UploadStatus.UPLOAD_STATUS_QUEUED;
    }

    // If no processing result is available, check for essential metadata
    // based on source type
    if (source.type == InputSourceType.youtubeVideo) {
      // For YouTube videos, check if necessary metadata is present
      return source.processingResult == null ||
          !source.processingResult!.hasYoutubeMetadata() ||
          source.processingResult!.title.isEmpty;
    } else if (source.type == InputSourceType.news) {
      // For news articles, check if necessary metadata is present
      return source.processingResult == null ||
          !source.processingResult!.hasArticleMetadata() ||
          source.processingResult!.title.isEmpty;
    } else if (source.type == InputSourceType.zip) {
      // For zip files, check if zip metadata is available
      return source.processingResult == null || !source.processingResult!.hasZipMetadata();
    } else if (source.type == InputSourceType.file) {
      // For files, check if content extraction is complete
      return source.processingResult == null || source.processingResult!.contentType.isEmpty;
    }

    // For other sources, check if content is null or empty
    return source.content == null || source.content!.isEmpty;
  }

  /// Creates a skeleton card for a loading source
  static Widget createSkeletonCard(InputSource source) {
    SourceCardType skeletonType;

    switch (source.type) {
      case InputSourceType.youtubeVideo:
        skeletonType = SourceCardType.youtube;
        break;
      case InputSourceType.news:
        skeletonType = SourceCardType.news;
        break;
      default:
        skeletonType = SourceCardType.standard;
    }

    return SourceCardSkeleton(type: skeletonType);
  }

  /// Generates HTML representation for a source
  static String generateHtmlForSource(InputSource source) {
    return SourceCardHtmlGenerator.generateForSource(source);
  }

  /// Generates skeleton HTML for a loading source
  static String generateSkeletonHtml() {
    return SourceCardHtmlGenerator.generateSkeletonHtml();
  }

  /// Gets the CSS styles for source cards
  static String getSourceCardCss() {
    return SourceCardHtmlGenerator.getSourceCardCss();
  }

  /// Creates the appropriate card widget for a given input source
  static Widget createCardForSource(
    InputSource source, {
    VoidCallback? onTap,
    VoidCallback? onDelete,
    BuildContext? context,
    int? index,
    Key? key,
  }) {
    // Debug logging for source type and processing result
    appLog.info('Source type: ${source.type}, fileName: ${source.fileName}', name: _logName);
    if (source.processingResult != null) {
      appLog.info('Processing result available with status: ${source.processingResult!.status}',
          name: _logName);
    } else {
      appLog.info('No processing result available', name: _logName);
    }

    // First check if this is a YouTube video
    final isYouTubeType = source.type == InputSourceType.youtubeVideo;
    final hasYouTubeMetadata =
        source.processingResult != null && source.processingResult!.hasYoutubeMetadata();

    if (isYouTubeType && hasYouTubeMetadata) {
      appLog.info('Creating YouTubeSourceCard for source: ${source.fileName}', name: _logName);
      return YouTubeSourceCard(
        key: key,
        source: source,
        onTap: onTap,
        onDelete: onDelete,
      );
    }

    // Then check if this is a news article with proper metadata
    final isNewsType = source.type == InputSourceType.news;
    final hasValidMetadata = FileIconHelper.hasValidNewsMetadata(source);
    appLog.info('Is news type: $isNewsType, Has valid metadata: $hasValidMetadata', name: _logName);

    if (isNewsType && hasValidMetadata) {
      appLog.info('Creating NewsArticleSourceCard for source: ${source.fileName}', name: _logName);
      return NewsArticleSourceCard(
        key: key,
        source: source,
        onTap: onTap,
        onDelete: onDelete,
      );
    }

    // Check for WhatsApp metadata first (highest priority)
    if (source.processingResult != null && source.processingResult!.hasWhatsappMetadata()) {
      appLog.info('Creating ChatSourceCard for WhatsApp source: ${source.fileName}',
          name: _logName);

      // The ChatSourceCard will handle displaying the appropriate name
      // based on WhatsApp metadata internally
      return ChatSourceCard(
        key: key,
        source: source,
        onTap: onTap,
        onDelete: onDelete,
      );
    }

    // Then check for other conversation sources
    if (FileIconHelper.isConversationSource(source)) {
      appLog.info('Creating ChatSourceCard for source: ${source.fileName}', name: _logName);
      return ChatSourceCard(
        key: key,
        source: source,
        onTap: onTap,
        onDelete: onDelete,
      );
    }

    // Fallback to default card with appropriate styling
    return _createDefaultCard(source, onTap, onDelete, context, index, key);
  }

  /// Creates a default card for sources that don't have a specialized card
  static Widget _createDefaultCard(
    InputSource source,
    VoidCallback? onTap,
    VoidCallback? onDelete,
    BuildContext? context,
    int? index,
    Key? key,
  ) {
    appLog.info('Creating default card for source: ${source.fileName}', name: _logName);
    if (context == null) {
      // Fallback for when context is not provided
      return Card(
        key: key,
        margin: const EdgeInsets.symmetric(vertical: 4.0),
        child: ListTile(
          title: Text(source.fileName ?? 'Source'),
          trailing: onDelete != null
              ? IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: onDelete,
                )
              : null,
          onTap: onTap,
        ),
      );
    }

    // Format the title with appropriate fallbacks
    final title = source.fileName ??
        (index != null ? sprintf(Strings.sourceItemFormat, [index + 1]) : 'Source');

    return Card(
      key: key,
      margin: const EdgeInsets.symmetric(vertical: 4.0),
      child: ListTile(
        leading: FileIconHelper.getSourceIcon(
          source.type,
          context,
          fileName: source.fileName,
          mimeType: source.mimeType,
        ),
        title: onTap != null
            ? InkWell(
                onTap: onTap,
                child: Text(
                  title,
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.primary,
                    decoration: TextDecoration.underline,
                  ),
                ),
              )
            : Text(
                title,
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
        subtitle: FileIconHelper.getSourceTypeDescription(source) != null
            ? Text(FileIconHelper.getSourceTypeDescription(source)!,
                style: const TextStyle(fontSize: 12))
            : null,
        trailing: onDelete != null
            ? IconButton(
                icon: const Icon(Icons.close),
                onPressed: onDelete,
              )
            : null,
        onTap: onTap,
      ),
    );
  }
}
