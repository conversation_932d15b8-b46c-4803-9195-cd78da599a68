import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

/// Enum for different types of source cards
enum SourceCardType {
  standard,
  youtube,
  news,
}

/// A widget that displays a loading state while a source card is being prepared
class SourceCardSkeleton extends StatelessWidget {
  final SourceCardType type;

  const SourceCardSkeleton({
    Key? key,
    this.type = SourceCardType.standard,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4.0),
      child: Shimmer.fromColors(
        baseColor: Colors.grey[300]!,
        highlightColor: Colors.grey[100]!,
        child: _buildSkeletonByType(),
      ),
    );
  }

  Widget _buildSkeletonByType() {
    switch (type) {
      case SourceCardType.youtube:
        return _buildYoutubeCardSkeleton();
      case SourceCardType.news:
        return _buildNewsCardSkeleton();
      case SourceCardType.standard:
        return _buildDefaultCardSkeleton();
    }
  }

  Widget _buildYoutubeCardSkeleton() {
    return Card(
      clipBehavior: Clip.antiAlias,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Thumbnail skeleton
            Container(
              width: 120,
              height: 68,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(4),
              ),
            ),

            const SizedBox(width: 12),

            // Content skeleton
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title skeleton
                  Container(
                    width: double.infinity,
                    height: 18,
                    color: Colors.white,
                  ),

                  const SizedBox(height: 8),

                  // Channel skeleton
                  Container(
                    width: 120,
                    height: 14,
                    color: Colors.white,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNewsCardSkeleton() {
    return Card(
      clipBehavior: Clip.antiAlias,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Thumbnail skeleton
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(4),
              ),
            ),

            const SizedBox(width: 12),

            // Content skeleton
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title skeleton
                  Container(
                    width: double.infinity,
                    height: 18,
                    color: Colors.white,
                  ),

                  const SizedBox(height: 8),

                  // Source site skeleton
                  Container(
                    width: 80,
                    height: 14,
                    color: Colors.white,
                  ),

                  const SizedBox(height: 6),

                  // Excerpt skeleton
                  Container(
                    width: double.infinity,
                    height: 14,
                    color: Colors.white,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDefaultCardSkeleton() {
    return Card(
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(4),
          ),
        ),
        title: Container(
          width: double.infinity,
          height: 18,
          color: Colors.white,
        ),
        subtitle: Container(
          width: 100,
          height: 14,
          margin: const EdgeInsets.only(top: 8),
          color: Colors.white,
        ),
      ),
    );
  }
}
