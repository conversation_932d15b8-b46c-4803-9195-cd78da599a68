import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:promz/core/providers/app_restart_provider.dart';
import 'package:promz_common/promz_common.dart';
import 'package:promz_common/config/api_config.dart';

/// A widget that displays server connection settings
class ServerConnectionCard extends ConsumerStatefulWidget {
  const ServerConnectionCard({Key? key}) : super(key: key);

  @override
  ConsumerState<ServerConnectionCard> createState() => _ServerConnectionCardState();
}

class _ServerConnectionCardState extends ConsumerState<ServerConnectionCard> {
  static const String _logName = 'ServerConnectionCard';

  // Local state to track the current setting
  late bool _useLocalServer;

  @override
  void initState() {
    super.initState();
    // Initialize with the current API config setting
    _useLocalServer = ApiConfig.forceLocalDevelopmentUrl;
  }

  @override
  Widget build(BuildContext context) {
    // Only show this card if we're running on an emulator
    final isEmulator = DeviceUtils.isEmulatorOrDefault();

    if (!isEmulator) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Text(
            'Server connection settings are only available when running on an emulator.',
            style: TextStyle(fontStyle: FontStyle.italic),
          ),
        ),
      );
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Server Connection',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            const Text(
              'Configure which server the app connects to when running in an emulator.',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('Use Local Development Server'),
              subtitle: Text(
                _useLocalServer
                    ? 'Connected to: ${ApiConfig.getBaseUrl()}'
                    : 'Connected to: ${ApiConfig.getProductionUrl()}',
                style: TextStyle(
                  fontSize: 12,
                  color: Theme.of(context).colorScheme.secondary,
                ),
              ),
              value: _useLocalServer,
              onChanged: (value) async {
                // Show a confirmation dialog
                final bool? confirmed = await showDialog<bool>(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: const Text('Change Server Connection'),
                    content: Text('Changing the server connection will restart the app. '
                        'You will be connected to the ${value ? 'local development' : 'production'} server.\n\n'
                        'Are you sure you want to continue?'),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(false),
                        child: const Text('Cancel'),
                      ),
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(true),
                        child: const Text('Continue'),
                      ),
                    ],
                  ),
                );

                if (confirmed != true) return;

                // Update the local state first
                setState(() {
                  _useLocalServer = value;
                });

                // Change the API configuration
                // This will trigger the setter which handles URL reconfiguration
                ApiConfig.forceLocalDevelopmentUrl = value;

                appLog.info(
                  'API server connection changed to: ${value ? 'local development' : 'production'}',
                  name: _logName,
                );

                // Force a connectivity check to ensure we're using the correct URLs
                await ApiConfig.checkConnectivity();

                // Log the actual base URL being used after the change
                appLog.info(
                  'Server connection changed. API base URL is now: ${ApiConfig.baseUrl}',
                  name: _logName,
                );

                // Add a small delay to ensure logs are flushed before restart
                await Future.delayed(const Duration(milliseconds: 100));

                // Restart the app to ensure all services are properly reinitialized
                ref.read(appRestartProvider.notifier).restartApp();
              },
            ),
            const SizedBox(height: 8),
            const Divider(),
            const SizedBox(height: 8),
            Text(
              'Current Configuration:',
              style: Theme.of(context).textTheme.titleSmall,
            ),
            const SizedBox(height: 8),
            Text('Base URL: ${ApiConfig.getBaseUrl()}'),
            const SizedBox(height: 4),
            Text('WebSocket URL: ${ApiConfig.getWebSocketUrl()}'),
            const SizedBox(height: 4),
            Text('gRPC URL: ${ApiConfig.getGrpcUrl()}'),
          ],
        ),
      ),
    );
  }
}
