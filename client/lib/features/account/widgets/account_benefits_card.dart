import 'package:flutter/material.dart';
import 'package:promz_common/promz_common.dart';

/// A widget that displays the benefits of creating an account
/// Used in the sign-in flow to explain why users should create an account
class AccountBenefitsCard extends StatelessWidget {
  const AccountBenefitsCard({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return const Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              Strings.whyCreateAccount,
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              Strings.accountBenefitsText,
              style: TextStyle(fontSize: 16),
            ),
          ],
        ),
      ),
    );
  }
}
