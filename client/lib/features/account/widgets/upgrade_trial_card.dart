import 'package:flutter/material.dart';
import 'package:promz_common/promz_common.dart';

/// A widget that displays the upgrade to trial option
/// Used when a user already has a free license but can upgrade to a trial
class UpgradeTrialCard extends StatelessWidget {
  /// Callback when the upgrade button is tapped
  final VoidCallback onUpgrade;

  const UpgradeTrialCard({
    Key? key,
    required this.onUpgrade,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final accentColor = Colors.deepOrange[400];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Center(
          child: ElevatedButton.icon(
            onPressed: onUpgrade,
            icon: const Icon(Icons.star),
            label: const Text(Strings.upgradeToPro),
            style: ElevatedButton.styleFrom(
              minimumSize: const Size.fromHeight(40),
              backgroundColor: accentColor,
              foregroundColor: Colors.white,
              elevation: 6,
              shadowColor: accentColor?.withAlpha((0.6 * 255).round()),
            ),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          Strings.proLicenseFeatures,
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w400,
            height: 1.3,
            letterSpacing: 0.2,
          ),
        ),
      ],
    );
  }
}
