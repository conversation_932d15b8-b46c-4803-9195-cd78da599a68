import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:promz/features/account/providers/settings_provider.dart';
import 'package:promz_common/promz_common.dart';

/// A widget that displays prompt execution settings
class PromptExecutionCard extends ConsumerWidget {
  const PromptExecutionCard({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final settingsNotifier = ref.read(settingsProvider.notifier);
    final settings = ref.watch(settingsProvider);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              Strings.promptExecutionSettingsTitle,
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text(Strings.confirmPromptExecutionLabel),
              subtitle: const Text(Strings.confirmPromptExecutionDescription),
              value: settings.confirmPromptExecution,
              onChanged: (bool value) {
                settingsNotifier.setConfirmPromptExecution(value);
              },
            ),
          ],
        ),
      ),
    );
  }
}
