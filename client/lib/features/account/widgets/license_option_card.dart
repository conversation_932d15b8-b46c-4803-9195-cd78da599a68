import 'package:flutter/material.dart';
import 'package:promz/core/models/license_model.dart';
import 'package:promz_common/promz_common.dart';

/// A reusable widget that displays license option information
/// Used for Free and Pro license options
class LicenseOptionCard extends StatelessWidget {
  /// The type of license this card represents
  final LicenseType licenseType;

  /// Callback when the license option is selected
  final VoidCallback onSelected;

  /// Text for the action button
  final String buttonText;

  /// Text describing the features of this license
  final String features;

  /// Primary color for this license type
  final Color color;

  /// Background color for the card
  final Color backgroundColor;

  /// Icon to display with the license title
  final IconData icon;

  /// Whether this is a promoted option (e.g., Pro Trial)
  final bool isPromoted;

  const LicenseOptionCard({
    Key? key,
    required this.licenseType,
    required this.onSelected,
    required this.buttonText,
    required this.features,
    required this.color,
    required this.backgroundColor,
    required this.icon,
    this.isPromoted = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final String licenseTitle =
        licenseType == LicenseType.free ? Strings.freeLicenseTitle : Strings.proTrialTitle;

    return Card(
      elevation: 1,
      color: backgroundColor,
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color),
                const SizedBox(width: 8),
                Text(
                  licenseTitle,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              features,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 12),
            ElevatedButton(
              onPressed: onSelected,
              style: ElevatedButton.styleFrom(
                minimumSize: const Size.fromHeight(40),
                backgroundColor: color,
                foregroundColor: Colors.white,
              ),
              child: Text(buttonText),
            ),
          ],
        ),
      ),
    );
  }
}
