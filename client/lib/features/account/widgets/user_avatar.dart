import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:promz_common/promz_common.dart';

/// A reusable widget that displays a user's avatar
///
/// If a photo URL is available, it will display that image.
/// Otherwise, it will display the first letter of the user's email or name in a circle.
///
/// This widget can be used in two ways:
/// 1. By providing a Supabase User object (legacy mode)
/// 2. By providing individual properties (email, fullName, photoUrl) directly
class UserAvatar extends StatelessWidget {
  static const String _logName = 'UserAvatar';

  /// The Supabase user (optional if email is provided)
  final User? user;

  /// Email address (used if user is null)
  final String? email;

  /// Full name (used if user is null)
  final String? fullName;

  /// Photo URL (used if user is null)
  final String? photoUrl;

  /// The size of the avatar
  final double size;

  /// The border color of the avatar
  final Color? borderColor;

  /// The border width of the avatar
  final double borderWidth;

  /// The background color when showing initials
  final Color? backgroundColor;

  /// The text style for the initials
  final TextStyle? textStyle;

  const UserAvatar({
    Key? key,
    this.user,
    this.email,
    this.fullName,
    this.photoUrl,
    this.size = 40.0,
    this.borderColor,
    this.borderWidth = 0.0,
    this.backgroundColor,
    this.textStyle,
  })  : assert(user != null || email != null, 'Either user or email must be provided'),
        super(key: key);

  @override
  Widget build(BuildContext context) {
    // Get the photo URL (either directly provided or from user metadata)
    final avatarUrl = _getPhotoUrl();

    // Default background color is the primary color
    final bgColor = backgroundColor ?? Theme.of(context).primaryColor;

    // Default text style
    final defaultTextStyle = TextStyle(
      color: Colors.white,
      fontSize: Theme.of(context).textTheme.bodyMedium?.fontSize ?? (size / 2.5),
      fontWeight: FontWeight.bold,
    );

    // Create a border if specified
    final border =
        borderWidth > 0 ? Border.all(color: borderColor ?? Colors.grey, width: borderWidth) : null;

    if (avatarUrl != null) {
      // If we have a photo URL, display the image
      appLog.debug('Using photo URL for avatar: $avatarUrl', name: _logName);
      return Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: border,
          image: DecorationImage(
            image: NetworkImage(avatarUrl),
            fit: BoxFit.cover,
          ),
        ),
      );
    } else {
      // Otherwise, display the first letter of the email or name
      final initial = _getInitial();
      appLog.debug('Using initial for avatar: $initial', name: _logName);

      return Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: bgColor,
          shape: BoxShape.circle,
          border: border,
        ),
        child: Center(
          child: Text(
            initial,
            style: textStyle ?? defaultTextStyle,
          ),
        ),
      );
    }
  }

  /// Get the photo URL from direct property or user metadata
  String? _getPhotoUrl() {
    // If photoUrl is directly provided, use it
    if (photoUrl != null) return photoUrl;

    // If no user is provided, return null
    if (user == null) return null;

    // Check for photo_url or avatar_url in user metadata
    final metadata = user!.userMetadata;
    if (metadata == null) return null;

    // Try different keys that might contain the avatar URL
    return metadata['avatar_url'] as String? ??
        metadata['photo_url'] as String? ??
        metadata['picture'] as String?;
  }

  /// Get the initial letter from the user's email or name
  String _getInitial() {
    // If fullName is directly provided, use it
    if (fullName != null && fullName!.isNotEmpty) {
      return fullName!.substring(0, 1).toUpperCase();
    }

    // If email is directly provided, use it
    if (email != null && email!.isNotEmpty) {
      return email!.substring(0, 1).toUpperCase();
    }

    // If no user is provided, return 'U'
    if (user == null) return 'U';

    // Try to get a display name from metadata
    final metadata = user!.userMetadata;
    String? displayName;

    if (metadata != null) {
      displayName = metadata['full_name'] as String? ??
          metadata['display_name'] as String? ??
          metadata['name'] as String?;
    }

    // If we have a display name, use its first letter
    if (displayName != null && displayName.isNotEmpty) {
      return displayName.substring(0, 1).toUpperCase();
    }

    // Otherwise use the first letter of the email
    if (user!.email != null && user!.email!.isNotEmpty) {
      return user!.email!.substring(0, 1).toUpperCase();
    }

    // Fallback to 'U' for unknown
    return 'U';
  }
}
