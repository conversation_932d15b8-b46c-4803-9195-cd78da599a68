import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:promz/core/services/client_context_service.dart';
import 'package:promz/features/account/views/account_view.dart';
import 'package:promz_common/promz_common.dart';

/// A dialog that prompts users to sign in when authentication is required
/// for a specific feature.
class AuthenticationDialog extends ConsumerWidget {
  final String? featureName;

  const AuthenticationDialog({
    Key? key,
    this.featureName,
  }) : super(key: key);

  static const _logName = 'AuthenticationDialog';

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    // Generate appropriate title and message based on the feature
    const title = 'Authentication Required';
    final message = featureName != null
        ? 'You need to sign in to use ${featureName!}. Sign in now?'
        : 'You need to sign in to use this feature. Sign in now?';

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Header with icon
            Center(
              child: Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: colorScheme.primaryContainer,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.account_circle,
                  size: 40,
                  color: colorScheme.primary,
                ),
              ),
            ),
            const SizedBox(height: 24),

            // Title
            Text(
              title,
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),

            // Description
            Text(
              message,
              style: theme.textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),

            // Sign in button
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: colorScheme.primary,
                foregroundColor: colorScheme.onPrimary,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              onPressed: () => _handleSignIn(context),
              child: const Text(
                'Sign In',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
            const SizedBox(height: 12),

            // Cancel button
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Cancel'),
            ),
          ],
        ),
      ),
    );
  }

  /// Navigates to the account view for sign in
  void _handleSignIn(BuildContext context) {
    appLog.debug('User initiated sign in flow from authentication dialog', name: _logName);

    // Close the dialog with true result (user wants to sign in)
    Navigator.of(context).pop(true);

    // Navigate to account view for authentication
    if (context.mounted) {
      Navigator.of(context).push(
        MaterialPageRoute(builder: (context) => const AccountView()),
      );
    }
  }

  /// Static method to show the authentication dialog
  /// Returns true if user chooses to sign in, false otherwise
  static Future<bool> show(
    BuildContext context, {
    String? featureName,
  }) async {
    appLog.debug(
      'Showing authentication dialog${featureName != null ? " for $featureName" : ""}',
      name: _logName,
    );

    return await showDialog<bool>(
          context: context,
          barrierDismissible: false,
          builder: (context) => AuthenticationDialog(featureName: featureName),
        ) ??
        false;
  }

  /// Helper method to check if the user is authenticated and has a valid license
  /// Shows the authentication dialog if needed
  /// Returns true if authenticated with license, false otherwise
  static Future<bool> checkAuthAndShowDialogIfNeeded(
    BuildContext context, {
    ClientContextService? clientContextService,
    String? featureName,
  }) async {
    final userProfileService = clientContextService?.userProfile;

    // Refresh profile to ensure we have latest auth state
    await userProfileService?.refreshProfile(forceFresh: true);

    final isAuthenticated = userProfileService?.profile.isAuthenticated ?? false;
    final hasLicense = userProfileService?.profile.hasLicense ?? false;

    // Log the current state
    appLog.debug(
      'Auth check - isAuthenticated: $isAuthenticated, hasValidLicense: $hasLicense',
      name: _logName,
    );

    // Check if user is authenticated and has a valid license
    if (!isAuthenticated || !hasLicense) {
      // Check if context is still valid
      if (!context.mounted) return false;

      // Show authentication dialog and return its result
      return await show(context, featureName: featureName);
    }

    // User is authenticated and has a license
    return true;
  }
}
