import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:promz/core/models/license_model.dart';
import 'package:promz/core/providers/license_provider.dart';
import 'package:promz/features/navigation/routes.dart';
import 'package:promz_common/promz_common.dart';

class UpgradeDialog extends ConsumerWidget {
  final NoEligibleModelException error;

  const UpgradeDialog({
    Key? key,
    required this.error,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    // Access the license data using Riverpod
    final licenseState = ref.watch(userLicenseProvider);
    final license = licenseState.valueOrNull;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Header with icon
            Center(
              child: Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: colorScheme.primaryContainer,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.upgrade_rounded,
                  size: 40,
                  color: colorScheme.primary,
                ),
              ),
            ),
            const SizedBox(height: 24),

            // Title
            Text(
              'Advanced Features Required',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),

            // Description
            Text(
              _getDescriptionText(),
              style: theme.textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),

            // Feature list
            ..._buildFeatureList(theme),
            const SizedBox(height: 24),

            // Upgrade button
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: colorScheme.primary,
                foregroundColor: colorScheme.onPrimary,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              onPressed: () => _handleUpgrade(context, ref, license),
              child: const Text(
                'Upgrade to Pro',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
            const SizedBox(height: 12),

            // Maybe later button
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Maybe Later'),
            ),
          ],
        ),
      ),
    );
  }

  String _getDescriptionText() {
    // Get provider name if available
    final providerName = error.getProviderName();

    if (error.isProviderError) {
      return '$providerName models are only available on our Pro plan. '
          'Upgrade now to access advanced AI capabilities.';
    } else if (error.isModelError) {
      return 'This advanced model is only available on our Pro plan. '
          'Upgrade now to leverage its full capabilities.';
    } else {
      return 'Your current plan doesn\'t have access to the requested models. '
          'Upgrade to Pro for unlimited access to all models and features.';
    }
  }

  List<Widget> _buildFeatureList(ThemeData theme) {
    final features = [
      {'icon': Icons.verified, 'text': 'Access to all premium models'},
      {'icon': Icons.speed, 'text': 'Faster processing and priority queue'},
      {'icon': Icons.token, 'text': '5 million tokens monthly allowance'},
      {'icon': Icons.support_agent, 'text': 'Priority support'},
    ];

    return features.map((feature) {
      return Padding(
        padding: const EdgeInsets.only(bottom: 12),
        child: Row(
          children: [
            Icon(
              feature['icon'] as IconData,
              color: theme.colorScheme.primary,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                feature['text'] as String,
                style: theme.textTheme.bodyMedium,
              ),
            ),
          ],
        ),
      );
    }).toList();
  }

  Future<void> _handleUpgrade(BuildContext context, WidgetRef ref, UserLicense? license) async {
    try {
      // Close the dialog first
      Navigator.of(context).pop();

      // Log the user's interest in upgrading
      appLog.info('User initiated upgrade flow from model restriction dialog',
          name: 'UpgradeDialog');

      // Navigate to the appropriate screen based on license state
      if (license == null || license.licenseType == LicenseType.none) {
        // User has no license - navigate to profile setup first
        if (context.mounted) {
          Navigator.of(context).pushNamed(AppRoutes.userProfileSetup);
        }
      } else if (license.licenseType == LicenseType.free) {
        // User has free license - navigate to upgrade screen
        _navigateToUpgradeScreen(context);
      } else if (license.licenseType == LicenseType.trial && _isTrialExpiring(license)) {
        // Trial about to expire - navigate to upgrade screen
        _navigateToUpgradeScreen(context);
      } else {
        // User already has Pro or valid trial
        _showAlreadySubscribedMessage(context, license);
      }
    } catch (e) {
      appLog.error('Failed to start upgrade process', name: 'UpgradeDialog', error: e);

      // Show a simple error message
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Unable to load subscription options. Please try again later.'),
          ),
        );
      }
    }
  }

  // Check if trial is expiring soon (within 3 days)
  bool _isTrialExpiring(UserLicense license) {
    if (license.expiresAt == null) return false;
    final daysRemaining = license.expiresAt!.difference(DateTime.now()).inDays;
    return daysRemaining <= 3;
  }

  // Navigate to upgrade screen
  void _navigateToUpgradeScreen(BuildContext context) {
    if (!context.mounted) return;
    Navigator.of(context).pushNamed(AppRoutes.subscriptionUpgrade);
  }

  // Show message when user already has subscription
  void _showAlreadySubscribedMessage(BuildContext context, UserLicense license) {
    if (!context.mounted) return;

    String message;
    if (license.licenseType == LicenseType.pro) {
      message =
          'You already have a Pro subscription. Please contact support if you\'re having issues accessing premium features.';
    } else {
      // Must be an active trial
      message =
          'You have an active trial. Please try using a different model or contact support if you\'re having issues.';
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }
}
