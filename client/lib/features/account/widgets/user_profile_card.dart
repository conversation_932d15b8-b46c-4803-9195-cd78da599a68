import 'package:flutter/material.dart';
import 'package:promz/core/models/license_model.dart';
import 'package:promz/features/account/widgets/account_widgets.dart';
import 'package:promz_common/promz_common.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// A reusable widget that displays the user's profile information
/// including avatar, email, and license status
class UserProfileCard extends StatelessWidget {
  /// The Supabase user
  final User user;

  /// The user's license information
  final UserLicense? license;

  /// Function to get remaining days for trial licenses
  final int Function(DateTime expiresAt) getRemainingDays;

  /// Function to handle sign out
  final VoidCallback onSignOut;

  /// Function to handle creating a free license
  final VoidCallback? onCreateFreeLicense;

  /// Function to handle generating a pro trial
  final VoidCallback? onGenerateProTrial;

  const UserProfileCard({
    Key? key,
    required this.user,
    required this.license,
    required this.getRemainingDays,
    required this.onSignOut,
    this.onCreateFreeLicense,
    this.onGenerateProTrial,
  }) : super(key: key);

  /// Get the user's display name from metadata, or fall back to email
  String _getUserDisplayName() {
    final metadata = user.userMetadata;
    if (metadata != null) {
      final displayName = metadata['full_name'] as String? ??
          metadata['display_name'] as String? ??
          metadata['name'] as String?;
      if (displayName != null && displayName.isNotEmpty) {
        return displayName;
      }
    }
    return user.email ?? 'Unknown User';
  }

  @override
  Widget build(BuildContext context) {
    final bool hasLicense = license?.hasLicense == true;
    final bool isTrial = license?.licenseType == LicenseType.trial && hasLicense;
    final bool canStartTrial = !isTrial && license?.licenseType == LicenseType.free;
    final bool noLicense = license?.licenseType == LicenseType.none || license?.licenseType == null;

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                UserAvatar(
                  user: user,
                  size: 48.0,
                  borderWidth: 1.0,
                  borderColor: Theme.of(context).dividerColor,
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Flexible(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Display user's name if available, otherwise email
                                Text(
                                  _getUserDisplayName(),
                                  style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                                  overflow: TextOverflow.ellipsis,
                                ),
                                // Display email as secondary information if name is available
                                if (_getUserDisplayName() != user.email && user.email != null) ...[
                                  const SizedBox(height: 2),
                                  Text(
                                    user.email!,
                                    style: TextStyle(
                                      fontSize: 14,
                                      // ignore: deprecated_member_use
                                      color: Theme.of(context).colorScheme.onSurface.withOpacity(
                                          0.7), // Use onSurface with opacity for better dark mode visibility
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ],
                              ],
                            ),
                          ),
                          if (hasLicense) ...[
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                              decoration: BoxDecoration(
                                color: isTrial ? Colors.amber[700] : Colors.green,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                UserLicense.formatLicenseType(license?.licenseType,
                                    hasLicense: hasLicense),
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                      // Show trial days remaining if applicable
                      if (isTrial && license?.expiresAt != null) ...[
                        const SizedBox(height: 4),
                        Text(
                          Strings.daysRemaining
                              .replaceFirst('%d', getRemainingDays(license!.expiresAt!).toString()),
                          style: TextStyle(
                            color: Colors.amber[700],
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Show license selection options if user has no license
            if (noLicense && onCreateFreeLicense != null && onGenerateProTrial != null) ...[
              const Text(
                Strings.chooseLicenseType,
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 12),
              const Text(
                Strings.selectLicenseType,
                style: TextStyle(fontSize: 14),
              ),
              const SizedBox(height: 16),

              // Free License Option
              LicenseOptionCard(
                licenseType: LicenseType.free,
                onSelected: onCreateFreeLicense!,
                buttonText: Strings.continueWithFree,
                features: Strings.freeLicenseFeatures,
                color: Colors.green[700]!,
                backgroundColor: Colors.grey[50]!,
                icon: Icons.check_circle_outline,
              ),
            ],

            // Add trial license button if eligible and already has a free license
            if (!noLicense && canStartTrial && onGenerateProTrial != null) ...[
              UpgradeTrialCard(
                onUpgrade: onGenerateProTrial!,
              ),
            ],

            const SizedBox(height: 8),
            Center(
              child: PromzButton(
                  label: Strings.signOut,
                  onPressed: onSignOut,
                  icon: Icons.logout,
                  height: 44.0, // Slightly taller for better touch target
                  width: 200.0, // Fixed width for consistent appearance
                  backgroundColor: Theme.of(context)
                      .colorScheme
                      .inversePrimary // Match the background color for consistency
                  ),
            ),
          ],
        ),
      ),
    );
  }
}
