import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:promz/features/account/providers/settings_provider.dart';
import 'package:promz_common/promz_common.dart';

/// A widget that displays appearance settings including theme mode and font size options
class AppearanceSettingsCard extends ConsumerWidget {
  const AppearanceSettingsCard({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final settingsNotifier = ref.read(settingsProvider.notifier);
    final settings = ref.watch(settingsProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Theme Settings
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  Strings.themeSettingsTitle,
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 16),
                // System theme option
                RadioListTile<ThemeMode>(
                  title: const Text(Strings.systemThemeLabel),
                  secondary: const Icon(Icons.brightness_auto),
                  value: ThemeMode.system,
                  groupValue: settings.themeMode,
                  onChanged: (ThemeMode? value) {
                    if (value != null) {
                      settingsNotifier.setThemeMode(value);
                    }
                  },
                ),
                // Light theme option
                RadioListTile<ThemeMode>(
                  title: const Text(Strings.lightThemeLabel),
                  secondary: const Icon(Icons.light_mode),
                  value: ThemeMode.light,
                  groupValue: settings.themeMode,
                  onChanged: (ThemeMode? value) {
                    if (value != null) {
                      settingsNotifier.setThemeMode(value);
                    }
                  },
                ),
                // Dark theme option
                RadioListTile<ThemeMode>(
                  title: const Text(Strings.darkThemeLabel),
                  secondary: const Icon(Icons.dark_mode),
                  value: ThemeMode.dark,
                  groupValue: settings.themeMode,
                  onChanged: (ThemeMode? value) {
                    if (value != null) {
                      settingsNotifier.setThemeMode(value);
                    }
                  },
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 16),
        // Font Size Settings
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  Strings.fontSizeSettingsTitle,
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 16),
                ListTile(
                  title: const Text(Strings.titleFontSizeLabel),
                  subtitle: Slider(
                    value: settings.titleFontSize,
                    min: 16.0,
                    max: 32.0,
                    divisions: 8,
                    label: settings.titleFontSize.toStringAsFixed(1),
                    onChanged: (value) {
                      settingsNotifier.setTitleFontSize(value);
                    },
                  ),
                ),
                ListTile(
                  title: const Text(Strings.bodyFontSizeLabel),
                  subtitle: Slider(
                    value: settings.bodyFontSize,
                    min: 12.0,
                    max: 24.0,
                    divisions: 6,
                    label: settings.bodyFontSize.toStringAsFixed(1),
                    onChanged: (value) {
                      settingsNotifier.setBodyFontSize(value);
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
