import 'package:flutter/material.dart';
import 'package:flutter_signin_button/flutter_signin_button.dart';
import 'package:promz_common/promz_common.dart';

/// A widget that displays authentication methods available to the user
/// Includes Google sign-in, Microsoft sign-in, and Apple sign-in (platform-dependent)
class AuthMethodsCard extends StatelessWidget {
  static const _logName = 'AuthMethodsCard';

  /// Whether authentication is currently in progress
  final bool isProcessing;

  /// Whether license verification is in progress after authentication
  final bool isProcessingLicense;

  /// Callback when Google sign-in is selected
  final VoidCallback? onGoogleSignIn;

  /// Callback when Microsoft sign-in is selected
  final VoidCallback? onMicrosoftSignIn;

  /// Callback when Apple sign-in is selected
  final VoidCallback? onAppleSignIn;

  const AuthMethodsCard({
    Key? key,
    required this.isProcessing,
    required this.isProcessingLicense,
    this.onGoogleSignIn,
    this.onMicrosoftSignIn,
    this.onAppleSignIn,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    appLog.debug('Building AuthMethodsCard', name: _logName);

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              Strings.signInWithAccount,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            Text(
              Strings.chooseSignInMethod,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 24),

            // Apple Sign In Button (if available)
            if (onAppleSignIn != null)
              _buildSignInButton(
                context,
                Buttons.Apple,
                Strings.continueWithApple,
                onAppleSignIn,
              ),

            // Google Sign In Button (if available)
            if (onGoogleSignIn != null)
              _buildSignInButton(
                context,
                Buttons.Google,
                Strings.continueWithGoogle,
                onGoogleSignIn,
              ),

            // Microsoft Sign In Button (if available)
            if (onMicrosoftSignIn != null)
              _buildSignInButton(
                context,
                Buttons.Microsoft,
                Strings.continueWithMicrosoft,
                onMicrosoftSignIn,
              ),

            // Note about account linking
            const SizedBox(height: 16),
            Text(
              Strings.accountLinkingNote,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey,
                  ),
            ),

            // Show processing message if needed
            if (isProcessingLicense) ...[
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    Strings.settingUpAccount,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey,
                          fontStyle: FontStyle.italic,
                        ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Build a sign-in button with the flutter_signin_button package
  Widget _buildSignInButton(
    BuildContext context,
    Buttons buttonType,
    String label,
    VoidCallback? onPressed,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: SizedBox(
        width: double.infinity,
        height: 50,
        child: Stack(
          alignment: Alignment.center,
          children: [
            // Sign-in button
            SignInButton(
              buttonType,
              text: isProcessing ? '' : label,
              // Fix: Always pass a non-nullable function to onPressed
              onPressed: isProcessing || onPressed == null
                  ? () {} // No-op function when disabled
                  : () => onPressed(),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 12),
            ),

            // Loading indicator (shown when processing)
            if (isProcessing)
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(_getTextColorForButton(buttonType)),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Signing in...',
                    style: TextStyle(
                      color: _getTextColorForButton(buttonType),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }

  /// Get the appropriate text color for the specific button type
  Color _getTextColorForButton(Buttons buttonType) {
    switch (buttonType) {
      case Buttons.Google:
        return Colors.black87;
      case Buttons.Microsoft:
      case Buttons.Apple:
        return Colors.white;
      default:
        return Colors.white;
    }
  }
}
