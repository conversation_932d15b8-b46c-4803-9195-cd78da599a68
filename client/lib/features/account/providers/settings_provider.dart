import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SettingsState {
  final ThemeMode themeMode;
  final double titleFontSize;
  final double bodyFontSize;
  final bool confirmPromptExecution;

  const SettingsState({
    this.themeMode = ThemeMode.system,
    this.titleFontSize = 24.0,
    this.bodyFontSize = 16.0,
    this.confirmPromptExecution = true,
  });

  SettingsState copyWith({
    ThemeMode? themeMode,
    double? titleFontSize,
    double? bodyFontSize,
    bool? confirmPromptExecution,
  }) {
    return SettingsState(
      themeMode: themeMode ?? this.themeMode,
      titleFontSize: titleFontSize ?? this.titleFontSize,
      bodyFontSize: bodyFontSize ?? this.bodyFontSize,
      confirmPromptExecution: confirmPromptExecution ?? this.confirmPromptExecution,
    );
  }
}

class SettingsNotifier extends StateNotifier<SettingsState> {
  final SharedPreferences _prefs;

  SettingsNotifier(this._prefs) : super(const SettingsState()) {
    _loadSettings();
  }

  static const String _themeModeKey = 'theme_mode';
  static const String _titleFontSizeKey = 'title_font_size';
  static const String _bodyFontSizeKey = 'body_font_size';
  static const String _confirmPromptExecutionKey = 'confirm_prompt_execution';

  void _loadSettings() {
    final themeModeIndex = _prefs.getInt(_themeModeKey) ?? ThemeMode.system.index;
    final titleFontSize = _prefs.getDouble(_titleFontSizeKey) ?? 24.0;
    final bodyFontSize = _prefs.getDouble(_bodyFontSizeKey) ?? 16.0;
    final confirmPromptExecution = _prefs.getBool(_confirmPromptExecutionKey) ?? true;

    state = SettingsState(
      themeMode: ThemeMode.values[themeModeIndex],
      titleFontSize: titleFontSize,
      bodyFontSize: bodyFontSize,
      confirmPromptExecution: confirmPromptExecution,
    );
  }

  Future<void> setThemeMode(ThemeMode mode) async {
    await _prefs.setInt(_themeModeKey, mode.index);
    state = state.copyWith(themeMode: mode);
  }

  Future<void> setTitleFontSize(double size) async {
    await _prefs.setDouble(_titleFontSizeKey, size);
    state = state.copyWith(titleFontSize: size);
  }

  Future<void> setBodyFontSize(double size) async {
    await _prefs.setDouble(_bodyFontSizeKey, size);
    state = state.copyWith(bodyFontSize: size);
  }

  Future<void> setConfirmPromptExecution(bool confirm) async {
    await _prefs.setBool(_confirmPromptExecutionKey, confirm);
    state = state.copyWith(confirmPromptExecution: confirm);
  }
}

final settingsProvider = StateNotifierProvider<SettingsNotifier, SettingsState>((ref) {
  throw UnimplementedError('Initialize with SharedPreferences instance');
});
