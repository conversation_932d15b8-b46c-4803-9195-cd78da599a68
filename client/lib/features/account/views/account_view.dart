import 'dart:io' show Platform;
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:promz/core/models/license_model.dart';
import 'package:promz/core/providers/service_providers.dart';
import 'package:promz/core/utils/global_functions.dart';
import 'package:promz/features/account/providers/settings_provider.dart';
import 'package:promz/features/account/viewmodels/user_profile_view_model.dart';
import 'package:promz/features/account/widgets/app_information_card.dart';
import 'package:promz/features/account/widgets/appearance_settings_card.dart';
import 'package:promz/features/account/widgets/auth_methods_card.dart';
import 'package:promz/features/account/widgets/server_connection_card.dart';
import 'package:promz/core/widgets/profile_avatar_with_badge.dart';
import 'package:promz/features/home/<USER>/home_viewmodel.dart';
import 'package:promz/features/home/<USER>/widgets/bottom_nav_bar.dart';
import 'package:promz/features/onboarding/services/onboarding_service.dart';
import 'package:promz/features/onboarding/views/onboarding_view.dart';
import 'package:promz_common/promz_common.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// A consolidated view that combines user profile and account settings
class AccountView extends ConsumerStatefulWidget {
  /// The initial section to expand
  final String? initialSection;

  const AccountView({
    Key? key,
    this.initialSection,
  }) : super(key: key);

  @override
  AccountViewState createState() => AccountViewState();
}

class AccountViewState extends ConsumerState<AccountView> with WidgetsBindingObserver {
  static const _logName = 'AccountView';
  final bool _isFirstBuild = true;

  // Section expansion states
  bool _isProfileSectionExpanded = true;
  bool _isAppearanceSectionExpanded = false;
  bool _isServerConnectionSectionExpanded = false;
  bool _isAppInfoSectionExpanded = false;
  bool _isPromptExecutionSectionExpanded = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    // Set initial expansion state based on initialSection
    if (widget.initialSection != null) {
      _isProfileSectionExpanded = widget.initialSection == 'profile';
      _isAppearanceSectionExpanded = widget.initialSection == 'appearance';
      _isPromptExecutionSectionExpanded = widget.initialSection == 'prompt_execution';
    }

    // Reset any loading state when the view is initialized
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        final viewModel = ref.read(userProfileViewModelProvider.notifier);
        viewModel.resetLoadingState();

        // Refresh profile with forceFresh=true for AccountView since we want the latest data here
        // final clientContextService = ref.read(clientContextServiceProvider);
        // clientContextService.when(
        //   data: (clientContext) {
        //     final userProfileService = clientContext.userProfile;
        //     userProfileService.refreshProfile(forceFresh: true);
        //   },
        //   loading: () {},
        //   error: (err, stack) {},
        // );
      }
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      // Reset loading state when app is resumed
      final viewModel = ref.read(userProfileViewModelProvider.notifier);
      viewModel.resetLoadingState();
    }
  }

  @override
  Widget build(BuildContext context) {
    appLog.debug('Building AccountView', name: _logName);

    final settingsNotifier = ref.read(settingsProvider.notifier);
    final homeViewModel = ref.read(homeViewModelProvider.notifier);
    final viewModel = ref.watch(userProfileViewModelProvider.notifier);
    final settings = ref.watch(settingsProvider);

    // Get the UserProfileService from ClientContextService
    final clientContextAsync = ref.watch(clientContextServiceProvider);

    // Force reset loading state on first build
    if (_isFirstBuild) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        viewModel.resetLoadingState();
      });
    }

    return PopScope(
      canPop: Navigator.of(context).canPop(), // Check if we can pop
      onPopInvokedWithResult: (didPop, dynamic result) {
        // If we didn't pop (because we're at the root), navigate to home
        if (!didPop && mounted) {
          homeViewModel.navigateToPage(context, 0);
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Account & Settings'),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () {
              // Try to pop first, if that doesn't work (we're at root), navigate to home
              if (!Navigator.of(context).canPop()) {
                homeViewModel.navigateToPage(context, 0);
              } else {
                Navigator.of(context).pop();
              }
            },
          ),
          automaticallyImplyLeading: false, // Disable automatic back button
        ),
        body: clientContextAsync.when(
          data: (clientContext) {
            final userProfileService = clientContext.userProfile;

            return ListenableBuilder(
              listenable: userProfileService, // Use ! to assert non-null based on assumption
              builder: (context, _) {
                final profile = userProfileService.profile;
                final isAuthenticated = profile.isAuthenticated;
                final license = profile.hasLicense
                    ? UserLicense(
                        hasLicense: profile.hasLicense,
                        licenseType: profile.licenseType,
                        expiresAt: profile.licenseExpiresAt,
                      )
                    : null;

                return SingleChildScrollView(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Profile Section
                      _buildExpandableSection(
                        title: 'Profile',
                        icon: Icons.person,
                        isExpanded: _isProfileSectionExpanded,
                        onExpansionChanged: (value) {
                          setState(() {
                            _isProfileSectionExpanded = value;
                          });
                        },
                        children: [
                          if (isAuthenticated) ...[
                            Card(
                              child: Padding(
                                padding: const EdgeInsets.all(16.0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        ProfileAvatarWithBadge(
                                          email: profile.email ?? '',
                                          fullName: profile.fullName,
                                          photoUrl: profile.avatarUrl,
                                          hasLicense: profile.hasLicense,
                                          licenseType: profile.licenseType,
                                          size: 60.0,
                                        ),
                                        const SizedBox(width: 16),
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                profile.fullName ?? profile.email ?? 'User',
                                                style: Theme.of(context).textTheme.titleLarge,
                                              ),
                                              if (profile.email != null)
                                                Text(
                                                  profile.email!,
                                                  style: Theme.of(context).textTheme.bodyMedium,
                                                ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 16),
                                    Row(
                                      children: [
                                        Text(
                                          'License',
                                          style: Theme.of(context).textTheme.titleMedium,
                                        ),
                                        Container(
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 8, vertical: 4),
                                          decoration: BoxDecoration(
                                            color: profile.hasLicense
                                                ? _getLicenseColor(profile.licenseType)
                                                : Colors.grey,
                                            borderRadius: BorderRadius.circular(12),
                                          ),
                                          child: Text(
                                            UserLicense.formatLicenseType(profile.licenseType,
                                                hasLicense: profile.hasLicense),
                                            style: Theme.of(context)
                                                .textTheme
                                                .titleMedium
                                                ?.copyWith(color: Colors.white),
                                          ),
                                        ),
                                      ],
                                    ),
                                    if (profile.licenseExpiresAt != null)
                                      Text(
                                        'Expires: ${profile.licenseExpiresAt!.toLocal().toString().split(' ')[0]}',
                                        style: Theme.of(context).textTheme.bodyMedium,
                                      ),
                                    const SizedBox(height: 16),
                                    ElevatedButton(
                                      onPressed: () => _handleSignOut(viewModel),
                                      child: const Text('Sign Out'),
                                    ),
                                    if (license?.licenseType == LicenseType.free)
                                      TextButton(
                                        onPressed: () => _handleGenerateProTrial(viewModel),
                                        child: const Text('Start Pro Trial'),
                                      ),
                                    if (license?.licenseType == LicenseType.none)
                                      TextButton(
                                        onPressed: () => _handleCreateFreeLicense(viewModel),
                                        child: const Text('Create Free License'),
                                      ),
                                  ],
                                ),
                              ),
                            ),
                          ] else ...[
                            // Pass context to the method call
                            _buildSocialAuthCard(context, viewModel),
                          ],
                          const SizedBox(height: 16),

                          // App Tour Button
                          _buildAppTourButton(),
                        ],
                      ),

                      const SizedBox(height: 16),

                      // Appearance Section
                      _buildExpandableSection(
                        title: 'Appearance',
                        icon: Icons.palette,
                        isExpanded: _isAppearanceSectionExpanded,
                        onExpansionChanged: (value) {
                          setState(() {
                            _isAppearanceSectionExpanded = value;
                          });
                        },
                        children: const [
                          AppearanceSettingsCard(),
                        ],
                      ),

                      const SizedBox(height: 16),

                      // Server Connection Section (only visible on emulators)
                      if (DeviceUtils.isEmulatorOrDefault()) ...[
                        // Only render this section on emulators
                        _buildExpandableSection(
                          title: 'Server Connection',
                          icon: Icons.dns_rounded,
                          isExpanded: _isServerConnectionSectionExpanded,
                          onExpansionChanged: (value) {
                            setState(() {
                              _isServerConnectionSectionExpanded = value;
                            });
                          },
                          children: const [
                            ServerConnectionCard(),
                          ],
                        ),
                        const SizedBox(height: 16),
                      ],

                      // Application Information Section
                      _buildExpandableSection(
                        title: 'Application Information',
                        icon: Icons.info_outline,
                        isExpanded: _isAppInfoSectionExpanded,
                        onExpansionChanged: (value) {
                          setState(() {
                            _isAppInfoSectionExpanded = value;
                          });
                        },
                        children: const [
                          AppInformationCard(),
                        ],
                      ),

                      const SizedBox(height: 16),

                      // Prompt Execution Section
                      _buildExpandableSection(
                        title: 'Prompt Execution',
                        icon: Icons.settings,
                        isExpanded: _isPromptExecutionSectionExpanded,
                        onExpansionChanged: (value) {
                          setState(() {
                            _isPromptExecutionSectionExpanded = value;
                          });
                        },
                        children: [
                          Card(
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    Strings.promptExecutionSettingsTitle,
                                    style: Theme.of(context).textTheme.titleLarge,
                                  ),
                                  const SizedBox(height: 16),
                                  SwitchListTile(
                                    title: const Text(Strings.confirmPromptExecutionLabel),
                                    subtitle: const Text(Strings.confirmPromptExecutionDescription),
                                    value: settings.confirmPromptExecution,
                                    onChanged: (value) {
                                      settingsNotifier.setConfirmPromptExecution(value);
                                    },
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                );
              },
            );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (err, stack) => const Center(child: Text('Error loading profile')),
        ),
        bottomNavigationBar: BottomNavBar(
          currentIndex: homeViewModel.currentNavIndex,
          onTap: (index) => homeViewModel.navigateToPage(context, index),
        ),
      ),
    );
  }

  /// Build an expandable section with a title, icon, and children
  Widget _buildExpandableSection({
    required String title,
    required IconData icon,
    required bool isExpanded,
    required Function(bool) onExpansionChanged,
    required List<Widget> children,
  }) {
    return Card(
      margin: EdgeInsets.zero,
      child: ExpansionTile(
        initiallyExpanded: isExpanded,
        onExpansionChanged: onExpansionChanged,
        leading: Icon(icon),
        title: Text(
          title,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(16.0, 0.0, 16.0, 16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: children,
            ),
          ),
        ],
      ),
    );
  }

  /// Builds the social authentication card.
  Widget _buildSocialAuthCard(BuildContext context, UserProfileViewModel viewModel) {
    final userProfileState = ref.watch(userProfileViewModelProvider);
    final isProcessing = userProfileState.isLoading || userProfileState.isProcessingLicense;

    // Determine which auth methods to show based on platform
    VoidCallback? googleSignInCallback;
    VoidCallback? microsoftSignInCallback;
    VoidCallback? appleSignInCallback;

    // Platform-specific auth methods
    if (Platform.isIOS || Platform.isMacOS) {
      // Apple ecosystem: Show only Apple Sign-In
      appleSignInCallback = () => _handleAppleSignIn(viewModel);
    } else if (Platform.isAndroid || Platform.isWindows) {
      // Android and Windows: Show Google and Microsoft
      googleSignInCallback = () => _handleGoogleSignIn(viewModel);
      microsoftSignInCallback = () => _handleMicrosoftSignIn(viewModel);
    } else {
      // Other platforms (web, Linux, etc.): Show all options
      googleSignInCallback = () => _handleGoogleSignIn(viewModel);
      microsoftSignInCallback = () => _handleMicrosoftSignIn(viewModel);
      appleSignInCallback = () => _handleAppleSignIn(viewModel);
    }

    return Column(
      children: [
        // Authentication Methods Card with platform-specific auth methods
        AuthMethodsCard(
          isProcessing: isProcessing,
          isProcessingLicense: userProfileState.isProcessingLicense,
          onGoogleSignIn: googleSignInCallback,
          onMicrosoftSignIn: microsoftSignInCallback,
          onAppleSignIn: appleSignInCallback,
        ),
      ],
    );
  }

  /// Build a button to start the app tour
  Widget _buildAppTourButton() {
    final theme = Theme.of(context);

    return Card(
      elevation: 1,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              Strings.newToPromz,
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.onSurface, // Ensure proper contrast in dark mode
              ),
            ),
            const SizedBox(height: 8),
            Text(
              Strings.takeTour,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant, // Make sure body text is visible
              ),
            ),
            const SizedBox(height: 16),
            Center(
              child: PromzButton(
                label: Strings.beginTour,
                onPressed: _startAppTour,
                icon: Icons.play_circle_outline,
                height: 44.0, // Slightly taller for better touch target
                width: 200.0, // Fixed width for consistent appearance
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Start the app tour by resetting onboarding status and showing the onboarding view
  void _startAppTour() async {
    // Reset onboarding status
    await OnboardingService.resetOnboardingStatus();

    // Navigate to onboarding view
    if (mounted) {
      // First pop back to the home page if possible
      if (Navigator.of(context).canPop()) {
        Navigator.of(context).pop();
      }

      // Then navigate to the onboarding view with a clean navigation stack
      Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(
          builder: (context) => const OnboardingView(),
          settings: const RouteSettings(name: '/onboarding'),
        ),
        (route) => false, // Remove all previous routes
      );
    }
  }

  // Handler methods that connect the UI to the ViewModel

  Future<void> _handleGoogleSignIn(UserProfileViewModel viewModel) async {
    appLog.debug('Starting Google sign-in process', name: _logName);
    final user = await viewModel.signInWithGoogle();

    // At this point, both authentication and license verification are complete
    appLog.debug('Google sign-in and license verification complete', name: _logName);

    if (user != null && mounted) {
      // Show a success message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(Strings.successfulSignIn),
          duration: Duration(seconds: 2),
        ),
      );

      // Refresh the UI
      setState(() {});
    }
  }

  Future<void> _handleAppleSignIn(UserProfileViewModel viewModel) async {
    appLog.debug('Starting Apple sign-in process', name: _logName);
    User? user; // Declare user variable
    try {
      user = await viewModel.signInWithApple();
      appLog.debug('viewModel.signInWithApple() completed. User object: ${user?.toString()}',
          name: _logName);
    } catch (e, s) {
      appLog.error('Error during Apple sign-in: $e', name: _logName, error: e, stackTrace: s);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Apple Sign-In failed: ${e.toString()}'),
            duration: const Duration(seconds: 3),
          ),
        );
      }
      return; // Exit if error
    }

    if (user != null) {
      appLog.debug('Apple sign-in successful (user is not null)', name: _logName);
    } else {
      appLog.warning(
          'Apple sign-in completed but user object is null. This is likely the cause of the silent failure.',
          name: _logName);
      if (mounted) {
        // Optionally, inform the user that sign-in didn't complete as expected
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
                'Apple Sign-In did not complete successfully. Please try again or check your setup.'),
            duration: Duration(seconds: 3),
          ),
        );
      }
    }

    if (user != null && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(Strings.successfulSignIn),
          duration: Duration(seconds: 2),
        ),
      );
      setState(() {});
    }
  }

  Future<void> _handleMicrosoftSignIn(UserProfileViewModel viewModel) async {
    appLog.debug('Starting Microsoft sign-in process', name: _logName);

    // Use the newly implemented Microsoft sign-in functionality
    final user = await viewModel.signInWithMicrosoft();

    // At this point, both authentication and license verification are complete
    appLog.debug('Microsoft sign-in and license verification complete', name: _logName);

    if (user != null && mounted) {
      // Show a success message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(Strings.successfulSignIn),
          duration: Duration(seconds: 2),
        ),
      );

      // Refresh the UI
      setState(() {});
    }
  }

  Future<void> _handleSignOut(UserProfileViewModel viewModel) async {
    // First clean up any UI elements that might cause context errors
    // This includes dismissing tooltips to prevent animation controller conflicts
    AppGlobalFunctions.cleanupUIBeforeStateChange();

    // Ask for confirmation
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(Strings.signOutConfirmationTitle),
        content: const Text(Strings.signOutConfirmationMessage),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text(Strings.cancel),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text(Strings.signOut),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    // Clean up UI again before the actual sign out to ensure we have no lingering UI elements
    AppGlobalFunctions.cleanupUIBeforeStateChange();

    // Proceed with sign out
    await viewModel.signOut();
  }

  Future<void> _handleGenerateProTrial(UserProfileViewModel viewModel) async {
    final success = await viewModel.generateProTrial();
    if (success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Pro trial activated!'),
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  Future<void> _handleCreateFreeLicense(UserProfileViewModel viewModel) async {
    final success = await viewModel.createFreeLicense();
    if (success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Free license activated!'),
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  /// Get the color for a license type
  Color _getLicenseColor(LicenseType type) {
    switch (type) {
      case LicenseType.free:
        return Colors.blue;
      case LicenseType.trial:
        return Colors.orange;
      case LicenseType.pro:
        return Colors.purple;
      case LicenseType.enterprise:
        return Colors.teal;
      default:
        return Colors.grey;
    }
  }
}
