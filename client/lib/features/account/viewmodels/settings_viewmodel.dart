import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:promz/core/providers/license_provider.dart';
import 'package:promz/core/providers/service_providers.dart';
import 'package:promz/core/services/license_service.dart';
import 'package:promz/core/services/secure_storage_service.dart';
import 'package:promz/core/services/supabase_service.dart';
import 'package:promz_common/promz_common.dart';

final settingsViewModelProvider = ChangeNotifierProvider<SettingsViewModel>((ref) {
  final licenseService = ref.watch(licenseServiceProvider);
  final supabaseService = ref.watch(supabaseServiceProvider);
  return SettingsViewModel(licenseService, supabaseService);
});

class SettingsViewModel extends ChangeNotifier {
  static const _logName = 'SettingsViewModel';

  // Keep reference to licenseService for future use
  // ignore: unused_field
  final LicenseService _licenseService;
  final SupabaseService _supabaseService;

  bool _isLoading = false;
  bool _hasError = false;
  String? _errorMessage;
  String? _apiKey;

  SettingsViewModel(this._licenseService, this._supabaseService) {
    _loadApiKey();
  }

  bool get isLoading => _isLoading;
  bool get hasError => _hasError;
  String? get errorMessage => _errorMessage;
  String? get apiKey => _apiKey;
  bool get hasApiKey => _apiKey != null && _apiKey!.isNotEmpty;

  Future<void> _loadApiKey() async {
    _isLoading = true;
    _hasError = false;
    _errorMessage = null;
    notifyListeners();

    try {
      _apiKey = await _supabaseService.getStoredApiKey();
      _hasError = false;
    } catch (e, stack) {
      _hasError = true;
      _errorMessage = 'Failed to load API key: $e';
      appLog.error('Error loading API key', name: _logName, error: e, stackTrace: stack);
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<bool> saveApiKey(String apiKey) async {
    if (apiKey.isEmpty) {
      _hasError = true;
      _errorMessage = 'API key cannot be empty';
      notifyListeners();
      return false;
    }

    _isLoading = true;
    _hasError = false;
    _errorMessage = null;
    notifyListeners();

    try {
      // Store the API key
      await _supabaseService.storeApiKey(apiKey);

      // Update the local state
      _apiKey = apiKey;
      _hasError = false;

      appLog.debug('API key saved successfully', name: _logName);
      return true;
    } catch (e, stack) {
      _hasError = true;
      _errorMessage = 'Failed to save API key: $e';
      appLog.error('Error saving API key', name: _logName, error: e, stackTrace: stack);
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<bool> clearApiKey() async {
    _isLoading = true;
    _hasError = false;
    _errorMessage = null;
    notifyListeners();

    try {
      // Clear the API key using SecureStorageService
      await SecureStorageService.deleteApiKey();

      // Update the local state
      _apiKey = null;
      _hasError = false;

      appLog.debug('API key cleared successfully', name: _logName);
      return true;
    } catch (e, stack) {
      _hasError = true;
      _errorMessage = 'Failed to clear API key: $e';
      appLog.error('Error clearing API key', name: _logName, error: e, stackTrace: stack);
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  void clearError() {
    _hasError = false;
    _errorMessage = null;
    notifyListeners();
  }
}
