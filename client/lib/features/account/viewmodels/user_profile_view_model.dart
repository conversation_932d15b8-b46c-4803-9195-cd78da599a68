import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:promz/core/providers/service_providers.dart';
import 'package:promz/core/services/user_profile_service.dart';
import 'package:promz/core/providers/license_manager_provider.dart';
import 'package:promz/core/services/supabase_service.dart';
import 'package:promz/core/utils/global_functions.dart';
import 'package:promz_common/promz_common.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Provides the UserProfileService instance derived from ClientContextService.
final userProfileProvider = Provider<UserProfileService?>((ref) {
  // Select the UserProfileService from the ClientContextService AsyncValue
  return ref.watch(clientContextServiceProvider.select((asyncValue) {
    // Return the userProfile service if data is available, otherwise null
    return asyncValue.asData?.value.userProfile;
  }));
});

/// State for the UserProfileViewModel
class UserProfileState {
  final bool isLoading;
  final String? errorMessage;
  final bool isProcessingLicense;

  const UserProfileState({
    this.isLoading = false,
    this.errorMessage,
    this.isProcessingLicense = false,
  });

  /// Create a copy of this state with the given fields replaced
  UserProfileState copyWith({
    bool? isLoading,
    String? errorMessage,
    bool? isProcessingLicense,
  }) {
    return UserProfileState(
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage ?? this.errorMessage,
      isProcessingLicense: isProcessingLicense ?? this.isProcessingLicense,
    );
  }

  /// Create a copy with error cleared
  UserProfileState clearError() {
    return copyWith(errorMessage: null);
  }

  /// Create a copy with loading state
  UserProfileState setLoading(bool loading) {
    return copyWith(isLoading: loading);
  }
}

/// ViewModel for the UserProfileSetupView
class UserProfileViewModel extends StateNotifier<UserProfileState> {
  final SupabaseService _supabaseService;
  final Ref _ref;
  static const _logName = 'UserProfileViewModel';

  UserProfileViewModel(this._ref, this._supabaseService) : super(const UserProfileState());

  /// Sign in with Google
  Future<User?> signInWithGoogle() async {
    state = state.clearError().setLoading(true);

    try {
      // Attempt to sign in with Google
      final user = await _supabaseService.signInWithGoogle();

      // If user is null, the sign-in was canceled or failed
      if (user == null) {
        state = state.copyWith(
          isLoading: false,
          errorMessage: 'Google Sign-In was canceled or failed.',
        );
        return null;
      }

      // Wait for license verification to complete
      appLog.debug('Waiting for license verification to complete', name: _logName);
      final licenseSuccess = await ensureUserHasLicense();
      appLog.debug('License verification completed: $licenseSuccess', name: _logName);

      state = state.setLoading(false);
      return user;
    } catch (e) {
      appLog.error('Error signing in with Google', name: _logName, error: e);
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'Error signing in with Google: ${e.toString()}',
      );
      return null;
    }
  }

  /// Sign in with Apple
  Future<User?> signInWithApple() async {
    state = state.clearError().setLoading(true);

    try {
      final user = await _supabaseService.signInWithApple();

      // If user is null, the sign-in was canceled by the user
      if (user == null) {
        state = state.copyWith(
          isLoading: false,
          errorMessage: 'Apple Sign-In was canceled.',
        );
        return null;
      }

      // Wait for license verification to complete
      appLog.debug('Waiting for license verification to complete', name: _logName);
      final licenseSuccess = await ensureUserHasLicense();
      appLog.debug('License verification completed: $licenseSuccess', name: _logName);

      state = state.setLoading(false);
      return user;
    } catch (e) {
      appLog.error('Error signing in with Apple', name: _logName, error: e);
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'Error signing in with Apple: ${e.toString()}',
      );
      return null;
    }
  }

  /// Sign in with Microsoft
  Future<User?> signInWithMicrosoft() async {
    state = state.clearError().setLoading(true);

    try {
      // Attempt to sign in with Microsoft
      final user = await _supabaseService.signInWithMicrosoft();

      // If user is null, the sign-in was canceled or failed
      if (user == null) {
        state = state.copyWith(
          isLoading: false,
          errorMessage: 'Microsoft Sign-In was canceled or failed.',
        );
        return null;
      }

      // Wait for license verification to complete
      appLog.debug('Waiting for license verification to complete', name: _logName);
      final licenseSuccess = await ensureUserHasLicense();
      appLog.debug('License verification completed: $licenseSuccess', name: _logName);

      state = state.setLoading(false);
      return user;
    } catch (e) {
      appLog.error('Error signing in with Microsoft', name: _logName, error: e);
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'Error signing in with Microsoft: ${e.toString()}',
      );
      return null;
    }
  }

  /// Sign out the current user
  Future<void> signOut() async {
    try {
      // Clean up UI elements before sign-out to prevent context errors
      AppGlobalFunctions.cleanupUIBeforeStateChange();

      // Continue with sign-out process
      state = state.copyWith(isLoading: true);
      await _supabaseService.signOut();
      state = state.copyWith(isLoading: false);
    } catch (e) {
      appLog.error('Error signing out', name: _logName, error: e);
      state = state.copyWith(isLoading: false);
    }
  }

  /// Generate a pro trial license
  Future<bool> generateProTrial() async {
    state = state.clearError().setLoading(true);

    try {
      final licenseService = _ref.read(licenseManagerProvider);

      // Generate a trial license
      final success = await licenseService.generateProTrialLicense();

      if (success) {
        appLog.debug('Pro trial license generated successfully', name: _logName);

        // Refresh license state
        final _ = _ref.refresh(licenseStateProvider);

        state = state.setLoading(false);
        return true;
      } else {
        appLog.debug('Failed to generate pro trial license', name: _logName);
        state = state.copyWith(
          isLoading: false,
          errorMessage: 'Failed to generate trial license. Please try again later.',
        );
        return false;
      }
    } catch (e) {
      appLog.error('Error generating trial license', name: _logName, error: e);
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'Error: ${e.toString()}',
      );
      return false;
    }
  }

  /// Create a free license
  Future<bool> createFreeLicense() async {
    state = state.clearError().setLoading(true);

    try {
      final licenseService = _ref.read(licenseManagerProvider);

      // Create a free license
      final success = await licenseService.ensureUserHasFreeLicense();

      if (success) {
        appLog.debug('Free license created successfully', name: _logName);

        // Refresh license state
        final _ = _ref.refresh(licenseStateProvider);

        state = state.setLoading(false);
        return true;
      } else {
        appLog.debug('Failed to create free license', name: _logName);
        state = state.copyWith(
          isLoading: false,
          errorMessage: 'Failed to create free license. Please try again later.',
        );
        return false;
      }
    } catch (e) {
      appLog.error('Error creating free license', name: _logName, error: e);
      state = state.copyWith(
        isLoading: false,
        errorMessage: 'Error: ${e.toString()}',
      );
      return false;
    }
  }

  /// Helper method to ensure the user has a license after sign-in
  /// This runs in the background to avoid blocking the UI
  Future<bool> ensureUserHasLicense() {
    // Don't block the UI - run license verification in the background
    state = state.copyWith(isProcessingLicense: true);

    // Create a completer to control when this Future completes
    final completer = Completer<bool>();

    final licenseManager = _ref.read(licenseManagerProvider);
    licenseManager.ensureUserHasFreeLicense().then((success) {
      appLog.debug('Background license verification completed: $success', name: _logName);
      state = state.copyWith(isProcessingLicense: false);

      // Refresh license state if successful
      if (success) {
        final _ = _ref.refresh(licenseStateProvider);
      }

      completer.complete(success);
    }).catchError((error) {
      appLog.error('Background license verification failed', name: _logName, error: error);
      state = state.copyWith(isProcessingLicense: false);
      completer.complete(false);
    });

    return completer.future;
  }

  /// Format a date as YYYY-MM-DD
  String formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  /// Get the number of days remaining until the expiry date
  int getRemainingDays(DateTime expiryDate) {
    final now = DateTime.now();
    final difference = expiryDate.difference(now);
    return difference.inDays;
  }

  /// Reset loading state and refresh profile from central service
  Future<void> resetLoadingState() async {
    appLog.debug('Resetting loading state', name: _logName);
    if (state.isLoading || state.isProcessingLicense) {
      state = state.copyWith(isLoading: false, isProcessingLicense: false);
      appLog.debug('Loading state reset', name: _logName);
    }

    // Also refresh the centralized profile service
    try {
      // Get the centralized UserProfileService instance
      final userProfileService = _ref.watch(userProfileProvider);
      // Refresh profile data to ensure consistency
      await userProfileService?.refreshProfile();
    } catch (e) {
      appLog.error('Error refreshing centralized profile', name: _logName, error: e);
      // Even if this fails, we still want to reset our local state
    }
  }
}

/// Provider for the UserProfileViewModel
final userProfileViewModelProvider =
    StateNotifierProvider<UserProfileViewModel, UserProfileState>((ref) {
  final supabaseService = ref.watch(supabaseServiceProvider);
  return UserProfileViewModel(ref, supabaseService);
});
