import 'package:flutter/material.dart';
import 'package:promz/features/input_selection/models/input_source.dart';
import 'package:promz/generated/common.pb.dart'; // Import for UploadStatus
import 'package:promz_common/promz_common.dart';
import 'package:sprintf/sprintf.dart';

/// A specialized card for displaying chat/conversation sources in the input sources list
class ChatSourceCard extends StatelessWidget {
  static const _logName = 'ChatSourceCard';

  final InputSource source;
  final VoidCallback? onTap;
  final VoidCallback? onDelete;

  const ChatSourceCard({
    Key? key,
    required this.source,
    this.onTap,
    this.onDelete,
  }) : super(key: key);

  /// Determines the display name for the chat source
  String _getDisplayName() {
    // For WhatsApp chats, use metadata when available
    if (source.processingResult != null && source.processingResult!.hasWhatsappMetadata()) {
      final metadata = source.processingResult!.whatsappMetadata;

      appLog.debug('Using chatName from WhatsApp metadata: ${metadata.chatName}', name: _logName);

      // First try to use group name if available (for group chats)
      if (metadata.groupName.isNotEmpty) {
        return metadata.groupName;
      }

      // Then try chat name from metadata
      if (metadata.chatName.isNotEmpty) {
        // Extract just the chat name without "WhatsApp Chat with" prefix if present
        final chatName = metadata.chatName;
        final prefixPattern = RegExp(r'^WhatsApp Chat with\s+');
        if (prefixPattern.hasMatch(chatName)) {
          return prefixPattern.stringMatch(chatName)!.isEmpty
              ? chatName
              : chatName.substring(prefixPattern.firstMatch(chatName)!.end);
        }
        return chatName;
      }
    }

    // For non-WhatsApp or without metadata, use the original source name
    return source.fileName ?? '';
  }

  @override
  Widget build(BuildContext context) {
    appLog.debug('Detected WhatsApp platform from metadata', name: _logName);

    final isProcessing = source.processingResult != null &&
        (source.processingResult!.status == UploadStatus.UPLOAD_STATUS_PROCESSING ||
            source.processingResult!.status == UploadStatus.UPLOAD_STATUS_QUEUED);

    // Get proper display name for the chat
    final displayName = _getDisplayName();

    // Handle participant count for display
    int participantCount = -1;
    if (source.processingResult != null && source.processingResult!.hasWhatsappMetadata()) {
      participantCount = source.processingResult!.whatsappMetadata.participantCount;
    }

    // Handle message count for display
    int messageCount = -1;
    if (source.processingResult != null && source.processingResult!.hasWhatsappMetadata()) {
      messageCount = source.processingResult!.whatsappMetadata.messageCount;
    }

    // Determine if it's a WhatsApp group chat
    bool isGroupChat = source.processingResult != null &&
        source.processingResult!.hasWhatsappMetadata() &&
        source.processingResult!.whatsappMetadata.isGroupChat;

    // Generate subtitle showing participant count and message count if available
    String subtitle = '';
    if (isProcessing) {
      appLog.debug('File is processing, showing placeholder participant text', name: _logName);
      subtitle = Strings.processingFile;
    } else {
      List<String> subtitleParts = [];

      // Add participant count if available
      if (participantCount > 0) {
        subtitleParts.add(isGroupChat
            ? sprintf(Strings.groupChatParticipants, [participantCount])
            : Strings.directMessage);
      }

      // Add message count if available
      if (messageCount > 0) {
        subtitleParts.add(sprintf(Strings.messageCount, [messageCount]));
      }

      subtitle = subtitleParts.join(' • ');
    }

    appLog.debug(
        'Creating ChatSourceCard for source: ${source.fileName}, Platform: WhatsApp, '
        'Group: $isGroupChat, Metadata: messages=$messageCount, participants=$participantCount, '
        'isGroup=$isGroupChat, status=${source.processingResult?.status}',
        name: _logName);

    // Use generic subtitle if none generated above
    subtitle = subtitle.isEmpty ? Strings.chatConversation : subtitle;

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4.0),
      clipBehavior: Clip.antiAlias,
      elevation: 1.0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.0),
        side: BorderSide(color: Colors.grey.shade300, width: 1.0),
      ),
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Platform icon
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  // ignore: deprecated_member_use
                  color: _getPlatformColor().withOpacity(0.1),
                  borderRadius: BorderRadius.circular(4.0),
                ),
                child: Center(
                  child: Icon(
                    _getPlatformIcon(isGroupChat: isGroupChat),
                    size: 24,
                    color: _getPlatformColor(),
                  ),
                ),
              ),

              const SizedBox(width: 12.0),

              // Text content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Title with platform badge
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            displayName,
                            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                                  color: Theme.of(context).colorScheme.tertiary,
                                  fontWeight: FontWeight.bold,
                                ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        if (_detectPlatform().isNotEmpty)
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              // ignore: deprecated_member_use
                              color: _getPlatformColor().withOpacity(0.1),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              _detectPlatform(),
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: _getPlatformColor(),
                                    fontWeight: FontWeight.bold,
                                  ),
                            ),
                          ),
                      ],
                    ),

                    // Subtitle
                    if (subtitle.isNotEmpty) ...[
                      const SizedBox(height: 2),
                      Text(
                        subtitle,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.grey.shade600,
                            ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ],
                ),
              ),

              // Delete button
              if (onDelete != null)
                IconButton(
                  icon: const Icon(Icons.close, size: 18),
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  onPressed: onDelete,
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// Gets the platform icon based on detected platform
  IconData _getPlatformIcon({bool isGroupChat = false}) {
    final platform = _detectPlatform();

    switch (platform) {
      case 'WhatsApp':
        return isGroupChat ? Icons.groups_outlined : Icons.chat_outlined;
      case 'Telegram':
        return isGroupChat ? Icons.group : Icons.send_outlined;
      case 'Discord':
        return isGroupChat ? Icons.group : Icons.headset_mic_outlined;
      case 'Slack':
        return isGroupChat ? Icons.groups_outlined : Icons.workspaces_outline;
      case 'Teams':
        return isGroupChat ? Icons.groups_outlined : Icons.people_alt_outlined;
      case 'Messenger':
        return isGroupChat ? Icons.groups_outlined : Icons.chat_outlined;
      default:
        return isGroupChat ? Icons.groups_outlined : Icons.chat_bubble_outline;
    }
  }

  /// Gets the platform color based on detected platform
  Color _getPlatformColor() {
    final platform = _detectPlatform();

    switch (platform) {
      case 'WhatsApp':
        return const Color(0xFF25D366);
      case 'Telegram':
        return const Color(0xFF0088CC);
      case 'Discord':
        return const Color(0xFF5865F2);
      case 'Slack':
        return const Color(0xFF4A154B);
      case 'Teams':
        return const Color(0xFF6264A7);
      case 'Messenger':
        return const Color(0xFF00B2FF);
      default:
        return Colors.blueGrey;
    }
  }

  /// Detects the chat platform from metadata and filename
  String _detectPlatform() {
    final processingResult = source.processingResult;

    // Prioritize WhatsApp detection
    if (processingResult != null && processingResult.hasWhatsappMetadata()) {
      appLog.debug('Detected WhatsApp platform from metadata', name: _logName);
      return 'WhatsApp';
    }

    // If no WhatsApp metadata, check other indicators
    String platform = '';

    // Check for platform indicators in processingResult
    if (processingResult != null) {
      // Check for platform field directly
      if (processingResult.source.isNotEmpty) {
        platform = processingResult.source;
      }

      // Check for source app
      if (platform.isEmpty && processingResult.appName.isNotEmpty) {
        final sourceApp = processingResult.appName;
        if (sourceApp.toLowerCase().contains('whatsapp')) {
          platform = 'WhatsApp';
        } else if (sourceApp.isNotEmpty) {
          platform = sourceApp;
        }
      }

      // Check source type
      if (platform.isEmpty && processingResult.sourceType.isNotEmpty) {
        final sourceType = processingResult.sourceType.toLowerCase();
        if (sourceType == 'whatsapp') {
          platform = 'WhatsApp';
        } else if (sourceType.contains('chat') || sourceType == 'conversation') {
          platform = 'Chat';
        }
      }
    }

    // Check filename patterns
    if (platform.isEmpty) {
      final fileName = source.fileName?.toLowerCase() ?? '';
      if (fileName.contains('whatsapp')) {
        platform = 'WhatsApp';
      } else if (fileName.contains('chat') || fileName.contains('conversation')) {
        platform = 'Chat';
      }
    }

    return platform;
  }
}
