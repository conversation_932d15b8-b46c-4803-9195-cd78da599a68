//
//  Generated code. Do not modify.
//  source: file_metadata.proto
//
// @dart = 3.3

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:async' as $async;
import 'dart:core' as $core;

import 'package:grpc/service_api.dart' as $grpc;
import 'package:protobuf/protobuf.dart' as $pb;

import 'file_metadata.pb.dart' as $2;

export 'file_metadata.pb.dart';

@$pb.GrpcServiceName('promz.api.v1.FileProcessingService')
class FileProcessingServiceClient extends $grpc.Client {
  static final _$validateFileSize =
      $grpc.ClientMethod<$2.FileSizeValidationRequest, $2.FileSizeValidationResponse>(
          '/promz.api.v1.FileProcessingService/ValidateFileSize',
          ($2.FileSizeValidationRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) => $2.FileSizeValidationResponse.fromBuffer(value));
  static final _$uploadFile = $grpc.ClientMethod<$2.FileUploadRequest, $2.FileUploadResponse>(
      '/promz.api.v1.FileProcessingService/UploadFile',
      ($2.FileUploadRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) => $2.FileUploadResponse.fromBuffer(value));
  static final _$getStatus = $grpc.ClientMethod<$2.StatusRequest, $2.StatusResponse>(
      '/promz.api.v1.FileProcessingService/GetStatus',
      ($2.StatusRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) => $2.StatusResponse.fromBuffer(value));
  static final _$getResults = $grpc.ClientMethod<$2.ResultsRequest, $2.ResultsResponse>(
      '/promz.api.v1.FileProcessingService/GetResults',
      ($2.ResultsRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) => $2.ResultsResponse.fromBuffer(value));
  static final _$cancelProcessing = $grpc.ClientMethod<$2.CancelRequest, $2.CancelResponse>(
      '/promz.api.v1.FileProcessingService/CancelProcessing',
      ($2.CancelRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) => $2.CancelResponse.fromBuffer(value));

  FileProcessingServiceClient($grpc.ClientChannel channel,
      {$grpc.CallOptions? options, $core.Iterable<$grpc.ClientInterceptor>? interceptors})
      : super(channel, options: options, interceptors: interceptors);

  $grpc.ResponseFuture<$2.FileSizeValidationResponse> validateFileSize(
      $2.FileSizeValidationRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$validateFileSize, request, options: options);
  }

  $grpc.ResponseFuture<$2.FileUploadResponse> uploadFile(
      $async.Stream<$2.FileUploadRequest> request,
      {$grpc.CallOptions? options}) {
    return $createStreamingCall(_$uploadFile, request, options: options).single;
  }

  $grpc.ResponseFuture<$2.StatusResponse> getStatus($2.StatusRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getStatus, request, options: options);
  }

  $grpc.ResponseStream<$2.ResultsResponse> getResults($2.ResultsRequest request,
      {$grpc.CallOptions? options}) {
    return $createStreamingCall(_$getResults, $async.Stream.fromIterable([request]),
        options: options);
  }

  $grpc.ResponseFuture<$2.CancelResponse> cancelProcessing($2.CancelRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$cancelProcessing, request, options: options);
  }
}

@$pb.GrpcServiceName('promz.api.v1.FileProcessingService')
abstract class FileProcessingServiceBase extends $grpc.Service {
  $core.String get $name => 'promz.api.v1.FileProcessingService';

  FileProcessingServiceBase() {
    $addMethod($grpc.ServiceMethod<$2.FileSizeValidationRequest, $2.FileSizeValidationResponse>(
        'ValidateFileSize',
        validateFileSize_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $2.FileSizeValidationRequest.fromBuffer(value),
        ($2.FileSizeValidationResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$2.FileUploadRequest, $2.FileUploadResponse>(
        'UploadFile',
        uploadFile,
        true,
        false,
        ($core.List<$core.int> value) => $2.FileUploadRequest.fromBuffer(value),
        ($2.FileUploadResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$2.StatusRequest, $2.StatusResponse>(
        'GetStatus',
        getStatus_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $2.StatusRequest.fromBuffer(value),
        ($2.StatusResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$2.ResultsRequest, $2.ResultsResponse>(
        'GetResults',
        getResults_Pre,
        false,
        true,
        ($core.List<$core.int> value) => $2.ResultsRequest.fromBuffer(value),
        ($2.ResultsResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$2.CancelRequest, $2.CancelResponse>(
        'CancelProcessing',
        cancelProcessing_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $2.CancelRequest.fromBuffer(value),
        ($2.CancelResponse value) => value.writeToBuffer()));
  }

  $async.Future<$2.FileSizeValidationResponse> validateFileSize_Pre(
      $grpc.ServiceCall $call, $async.Future<$2.FileSizeValidationRequest> $request) async {
    return validateFileSize($call, await $request);
  }

  $async.Future<$2.StatusResponse> getStatus_Pre(
      $grpc.ServiceCall $call, $async.Future<$2.StatusRequest> $request) async {
    return getStatus($call, await $request);
  }

  $async.Stream<$2.ResultsResponse> getResults_Pre(
      $grpc.ServiceCall $call, $async.Future<$2.ResultsRequest> $request) async* {
    yield* getResults($call, await $request);
  }

  $async.Future<$2.CancelResponse> cancelProcessing_Pre(
      $grpc.ServiceCall $call, $async.Future<$2.CancelRequest> $request) async {
    return cancelProcessing($call, await $request);
  }

  $async.Future<$2.FileSizeValidationResponse> validateFileSize(
      $grpc.ServiceCall call, $2.FileSizeValidationRequest request);
  $async.Future<$2.FileUploadResponse> uploadFile(
      $grpc.ServiceCall call, $async.Stream<$2.FileUploadRequest> request);
  $async.Future<$2.StatusResponse> getStatus($grpc.ServiceCall call, $2.StatusRequest request);
  $async.Stream<$2.ResultsResponse> getResults($grpc.ServiceCall call, $2.ResultsRequest request);
  $async.Future<$2.CancelResponse> cancelProcessing(
      $grpc.ServiceCall call, $2.CancelRequest request);
}
