//
//  Generated code. Do not modify.
//  source: websocket.proto
//
// @dart = 3.3

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:convert' as $convert;
import 'dart:core' as $core;
import 'dart:typed_data' as $typed_data;

@$core.Deprecated('Use webSocketMessageDescriptor instead')
const WebSocketMessage$json = {
  '1': 'WebSocketMessage',
  '2': [
    {'1': 'type', '3': 1, '4': 1, '5': 9, '10': 'type'},
    {'1': 'action', '3': 2, '4': 1, '5': 9, '10': 'action'},
    {'1': 'payload', '3': 3, '4': 1, '5': 12, '10': 'payload'},
    {'1': 'topic', '3': 4, '4': 1, '5': 9, '10': 'topic'},
    {'1': 'message_id', '3': 5, '4': 1, '5': 9, '10': 'messageId'},
    {'1': 'request_id', '3': 6, '4': 1, '5': 9, '10': 'requestId'},
    {'1': 'error', '3': 7, '4': 1, '5': 11, '6': '.promz.api.v1.ErrorInfo', '10': 'error'},
  ],
};

/// Descriptor for `WebSocketMessage`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List webSocketMessageDescriptor = $convert
    .base64Decode('ChBXZWJTb2NrZXRNZXNzYWdlEhIKBHR5cGUYASABKAlSBHR5cGUSFgoGYWN0aW9uGAIgASgJUg'
        'ZhY3Rpb24SGAoHcGF5bG9hZBgDIAEoDFIHcGF5bG9hZBIUCgV0b3BpYxgEIAEoCVIFdG9waWMS'
        'HQoKbWVzc2FnZV9pZBgFIAEoCVIJbWVzc2FnZUlkEh0KCnJlcXVlc3RfaWQYBiABKAlSCXJlcX'
        'Vlc3RJZBItCgVlcnJvchgHIAEoCzIXLnByb216LmFwaS52MS5FcnJvckluZm9SBWVycm9y');

@$core.Deprecated('Use errorInfoDescriptor instead')
const ErrorInfo$json = {
  '1': 'ErrorInfo',
  '2': [
    {'1': 'code', '3': 1, '4': 1, '5': 9, '10': 'code'},
    {'1': 'message', '3': 2, '4': 1, '5': 9, '10': 'message'},
    {
      '1': 'details',
      '3': 3,
      '4': 3,
      '5': 11,
      '6': '.promz.api.v1.ErrorInfo.DetailsEntry',
      '10': 'details'
    },
  ],
  '3': [ErrorInfo_DetailsEntry$json],
};

@$core.Deprecated('Use errorInfoDescriptor instead')
const ErrorInfo_DetailsEntry$json = {
  '1': 'DetailsEntry',
  '2': [
    {'1': 'key', '3': 1, '4': 1, '5': 9, '10': 'key'},
    {'1': 'value', '3': 2, '4': 1, '5': 9, '10': 'value'},
  ],
  '7': {'7': true},
};

/// Descriptor for `ErrorInfo`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List errorInfoDescriptor = $convert
    .base64Decode('CglFcnJvckluZm8SEgoEY29kZRgBIAEoCVIEY29kZRIYCgdtZXNzYWdlGAIgASgJUgdtZXNzYW'
        'dlEj4KB2RldGFpbHMYAyADKAsyJC5wcm9tei5hcGkudjEuRXJyb3JJbmZvLkRldGFpbHNFbnRy'
        'eVIHZGV0YWlscxo6CgxEZXRhaWxzRW50cnkSEAoDa2V5GAEgASgJUgNrZXkSFAoFdmFsdWUYAi'
        'ABKAlSBXZhbHVlOgI4AQ==');

@$core.Deprecated('Use fileUploadUpdateDescriptor instead')
const FileUploadUpdate$json = {
  '1': 'FileUploadUpdate',
  '2': [
    {'1': 'id', '3': 1, '4': 1, '5': 9, '10': 'id'},
    {'1': 'status', '3': 2, '4': 1, '5': 9, '10': 'status'},
    {'1': 'progress', '3': 3, '4': 1, '5': 1, '10': 'progress'},
    {'1': 'error', '3': 4, '4': 1, '5': 9, '10': 'error'},
    {'1': 'message', '3': 5, '4': 1, '5': 9, '10': 'message'},
    {'1': 'tokens_processed', '3': 6, '4': 1, '5': 5, '10': 'tokensProcessed'},
    {'1': 'tokens_limit', '3': 7, '4': 1, '5': 5, '10': 'tokensLimit'},
    {'1': 'tokens_exceeded', '3': 8, '4': 1, '5': 8, '10': 'tokensExceeded'},
    {'1': 'timestamp', '3': 9, '4': 1, '5': 3, '10': 'timestamp'},
  ],
};

/// Descriptor for `FileUploadUpdate`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List fileUploadUpdateDescriptor = $convert
    .base64Decode('ChBGaWxlVXBsb2FkVXBkYXRlEg4KAmlkGAEgASgJUgJpZBIWCgZzdGF0dXMYAiABKAlSBnN0YX'
        'R1cxIaCghwcm9ncmVzcxgDIAEoAVIIcHJvZ3Jlc3MSFAoFZXJyb3IYBCABKAlSBWVycm9yEhgK'
        'B21lc3NhZ2UYBSABKAlSB21lc3NhZ2USKQoQdG9rZW5zX3Byb2Nlc3NlZBgGIAEoBVIPdG9rZW'
        '5zUHJvY2Vzc2VkEiEKDHRva2Vuc19saW1pdBgHIAEoBVILdG9rZW5zTGltaXQSJwoPdG9rZW5z'
        'X2V4Y2VlZGVkGAggASgIUg50b2tlbnNFeGNlZWRlZBIcCgl0aW1lc3RhbXAYCSABKANSCXRpbW'
        'VzdGFtcA==');

@$core.Deprecated('Use subscriptionRequestDescriptor instead')
const SubscriptionRequest$json = {
  '1': 'SubscriptionRequest',
  '2': [
    {'1': 'topic', '3': 1, '4': 1, '5': 9, '10': 'topic'},
    {
      '1': 'filter',
      '3': 2,
      '4': 3,
      '5': 11,
      '6': '.promz.api.v1.SubscriptionRequest.FilterEntry',
      '10': 'filter'
    },
  ],
  '3': [SubscriptionRequest_FilterEntry$json],
};

@$core.Deprecated('Use subscriptionRequestDescriptor instead')
const SubscriptionRequest_FilterEntry$json = {
  '1': 'FilterEntry',
  '2': [
    {'1': 'key', '3': 1, '4': 1, '5': 9, '10': 'key'},
    {'1': 'value', '3': 2, '4': 1, '5': 9, '10': 'value'},
  ],
  '7': {'7': true},
};

/// Descriptor for `SubscriptionRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List subscriptionRequestDescriptor = $convert
    .base64Decode('ChNTdWJzY3JpcHRpb25SZXF1ZXN0EhQKBXRvcGljGAEgASgJUgV0b3BpYxJFCgZmaWx0ZXIYAi'
        'ADKAsyLS5wcm9tei5hcGkudjEuU3Vic2NyaXB0aW9uUmVxdWVzdC5GaWx0ZXJFbnRyeVIGZmls'
        'dGVyGjkKC0ZpbHRlckVudHJ5EhAKA2tleRgBIAEoCVIDa2V5EhQKBXZhbHVlGAIgASgJUgV2YW'
        'x1ZToCOAE=');

@$core.Deprecated('Use subscriptionResponseDescriptor instead')
const SubscriptionResponse$json = {
  '1': 'SubscriptionResponse',
  '2': [
    {'1': 'success', '3': 1, '4': 1, '5': 8, '10': 'success'},
    {'1': 'topic', '3': 2, '4': 1, '5': 9, '10': 'topic'},
    {'1': 'subscription_id', '3': 3, '4': 1, '5': 9, '10': 'subscriptionId'},
    {'1': 'error', '3': 4, '4': 1, '5': 11, '6': '.promz.api.v1.ErrorInfo', '10': 'error'},
  ],
};

/// Descriptor for `SubscriptionResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List subscriptionResponseDescriptor = $convert
    .base64Decode('ChRTdWJzY3JpcHRpb25SZXNwb25zZRIYCgdzdWNjZXNzGAEgASgIUgdzdWNjZXNzEhQKBXRvcG'
        'ljGAIgASgJUgV0b3BpYxInCg9zdWJzY3JpcHRpb25faWQYAyABKAlSDnN1YnNjcmlwdGlvbklk'
        'Ei0KBWVycm9yGAQgASgLMhcucHJvbXouYXBpLnYxLkVycm9ySW5mb1IFZXJyb3I=');
