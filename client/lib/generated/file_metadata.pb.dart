//
//  Generated code. Do not modify.
//  source: file_metadata.proto
//
// @dart = 3.3

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:core' as $core;

import 'package:fixnum/fixnum.dart' as $fixnum;
import 'package:protobuf/protobuf.dart' as $pb;

import 'file_metadata.pbenum.dart';

export 'package:protobuf/protobuf.dart' show GeneratedMessageGenericExtensions;

export 'file_metadata.pbenum.dart';

/// Request to validate file size before upload
class FileSizeValidationRequest extends $pb.GeneratedMessage {
  factory FileSizeValidationRequest({
    $core.String? fileName,
    $fixnum.Int64? fileSizeBytes,
  }) {
    final $result = create();
    if (fileName != null) {
      $result.fileName = fileName;
    }
    if (fileSizeBytes != null) {
      $result.fileSizeBytes = fileSizeBytes;
    }
    return $result;
  }
  FileSizeValidationRequest._() : super();
  factory FileSizeValidationRequest.fromBuffer($core.List<$core.int> i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromBuffer(i, r);
  factory FileSizeValidationRequest.fromJson($core.String i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(
      _omitMessageNames ? '' : 'FileSizeValidationRequest',
      package: const $pb.PackageName(_omitMessageNames ? '' : 'promz.api.v1'),
      createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'fileName')
    ..aInt64(2, _omitFieldNames ? '' : 'fileSizeBytes')
    ..hasRequiredFields = false;

  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
      'Will be removed in next major version')
  FileSizeValidationRequest clone() => FileSizeValidationRequest()..mergeFromMessage(this);
  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
      'Will be removed in next major version')
  FileSizeValidationRequest copyWith(void Function(FileSizeValidationRequest) updates) =>
      super.copyWith((message) => updates(message as FileSizeValidationRequest))
          as FileSizeValidationRequest;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static FileSizeValidationRequest create() => FileSizeValidationRequest._();
  FileSizeValidationRequest createEmptyInstance() => create();
  static $pb.PbList<FileSizeValidationRequest> createRepeated() =>
      $pb.PbList<FileSizeValidationRequest>();
  @$core.pragma('dart2js:noInline')
  static FileSizeValidationRequest getDefault() =>
      _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<FileSizeValidationRequest>(create);
  static FileSizeValidationRequest? _defaultInstance;

  /// Name of the file to validate
  @$pb.TagNumber(1)
  $core.String get fileName => $_getSZ(0);
  @$pb.TagNumber(1)
  set fileName($core.String v) {
    $_setString(0, v);
  }

  @$pb.TagNumber(1)
  $core.bool hasFileName() => $_has(0);
  @$pb.TagNumber(1)
  void clearFileName() => $_clearField(1);

  /// Size of the file in bytes
  @$pb.TagNumber(2)
  $fixnum.Int64 get fileSizeBytes => $_getI64(1);
  @$pb.TagNumber(2)
  set fileSizeBytes($fixnum.Int64 v) {
    $_setInt64(1, v);
  }

  @$pb.TagNumber(2)
  $core.bool hasFileSizeBytes() => $_has(1);
  @$pb.TagNumber(2)
  void clearFileSizeBytes() => $_clearField(2);
}

/// Response for file size validation
class FileSizeValidationResponse extends $pb.GeneratedMessage {
  factory FileSizeValidationResponse({
    $core.bool? isValid,
    FileProcessingErrorCode? errorCode,
    $core.String? errorMessage,
    $pb.PbMap<$core.String, $core.String>? errorDetails,
    $core.String? nextTier,
    $fixnum.Int64? maxSizeBytes,
  }) {
    final $result = create();
    if (isValid != null) {
      $result.isValid = isValid;
    }
    if (errorCode != null) {
      $result.errorCode = errorCode;
    }
    if (errorMessage != null) {
      $result.errorMessage = errorMessage;
    }
    if (errorDetails != null) {
      $result.errorDetails.addAll(errorDetails);
    }
    if (nextTier != null) {
      $result.nextTier = nextTier;
    }
    if (maxSizeBytes != null) {
      $result.maxSizeBytes = maxSizeBytes;
    }
    return $result;
  }
  FileSizeValidationResponse._() : super();
  factory FileSizeValidationResponse.fromBuffer($core.List<$core.int> i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromBuffer(i, r);
  factory FileSizeValidationResponse.fromJson($core.String i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(
      _omitMessageNames ? '' : 'FileSizeValidationResponse',
      package: const $pb.PackageName(_omitMessageNames ? '' : 'promz.api.v1'),
      createEmptyInstance: create)
    ..aOB(1, _omitFieldNames ? '' : 'isValid')
    ..e<FileProcessingErrorCode>(2, _omitFieldNames ? '' : 'errorCode', $pb.PbFieldType.OE,
        defaultOrMaker: FileProcessingErrorCode.FILE_PROCESSING_ERROR_UNSPECIFIED,
        valueOf: FileProcessingErrorCode.valueOf,
        enumValues: FileProcessingErrorCode.values)
    ..aOS(3, _omitFieldNames ? '' : 'errorMessage')
    ..m<$core.String, $core.String>(4, _omitFieldNames ? '' : 'errorDetails',
        entryClassName: 'FileSizeValidationResponse.ErrorDetailsEntry',
        keyFieldType: $pb.PbFieldType.OS,
        valueFieldType: $pb.PbFieldType.OS,
        packageName: const $pb.PackageName('promz.api.v1'))
    ..aOS(5, _omitFieldNames ? '' : 'nextTier')
    ..aInt64(6, _omitFieldNames ? '' : 'maxSizeBytes')
    ..hasRequiredFields = false;

  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
      'Will be removed in next major version')
  FileSizeValidationResponse clone() => FileSizeValidationResponse()..mergeFromMessage(this);
  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
      'Will be removed in next major version')
  FileSizeValidationResponse copyWith(void Function(FileSizeValidationResponse) updates) =>
      super.copyWith((message) => updates(message as FileSizeValidationResponse))
          as FileSizeValidationResponse;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static FileSizeValidationResponse create() => FileSizeValidationResponse._();
  FileSizeValidationResponse createEmptyInstance() => create();
  static $pb.PbList<FileSizeValidationResponse> createRepeated() =>
      $pb.PbList<FileSizeValidationResponse>();
  @$core.pragma('dart2js:noInline')
  static FileSizeValidationResponse getDefault() =>
      _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<FileSizeValidationResponse>(create);
  static FileSizeValidationResponse? _defaultInstance;

  /// Whether the file size is valid for the user's license tier
  @$pb.TagNumber(1)
  $core.bool get isValid => $_getBF(0);
  @$pb.TagNumber(1)
  set isValid($core.bool v) {
    $_setBool(0, v);
  }

  @$pb.TagNumber(1)
  $core.bool hasIsValid() => $_has(0);
  @$pb.TagNumber(1)
  void clearIsValid() => $_clearField(1);

  /// Error code if validation fails
  @$pb.TagNumber(2)
  FileProcessingErrorCode get errorCode => $_getN(1);
  @$pb.TagNumber(2)
  set errorCode(FileProcessingErrorCode v) {
    $_setField(2, v);
  }

  @$pb.TagNumber(2)
  $core.bool hasErrorCode() => $_has(1);
  @$pb.TagNumber(2)
  void clearErrorCode() => $_clearField(2);

  /// Error message if validation fails
  @$pb.TagNumber(3)
  $core.String get errorMessage => $_getSZ(2);
  @$pb.TagNumber(3)
  set errorMessage($core.String v) {
    $_setString(2, v);
  }

  @$pb.TagNumber(3)
  $core.bool hasErrorMessage() => $_has(2);
  @$pb.TagNumber(3)
  void clearErrorMessage() => $_clearField(3);

  /// Additional error details
  @$pb.TagNumber(4)
  $pb.PbMap<$core.String, $core.String> get errorDetails => $_getMap(3);

  /// Next tier that would support this file size (if applicable)
  @$pb.TagNumber(5)
  $core.String get nextTier => $_getSZ(4);
  @$pb.TagNumber(5)
  set nextTier($core.String v) {
    $_setString(4, v);
  }

  @$pb.TagNumber(5)
  $core.bool hasNextTier() => $_has(4);
  @$pb.TagNumber(5)
  void clearNextTier() => $_clearField(5);

  /// Maximum file size allowed for the current tier in bytes
  @$pb.TagNumber(6)
  $fixnum.Int64 get maxSizeBytes => $_getI64(5);
  @$pb.TagNumber(6)
  set maxSizeBytes($fixnum.Int64 v) {
    $_setInt64(5, v);
  }

  @$pb.TagNumber(6)
  $core.bool hasMaxSizeBytes() => $_has(5);
  @$pb.TagNumber(6)
  void clearMaxSizeBytes() => $_clearField(6);
}

/// First message contains metadata
class FileUploadRequest_Metadata extends $pb.GeneratedMessage {
  factory FileUploadRequest_Metadata({
    $core.String? fileName,
    $core.String? mimeType,
    $fixnum.Int64? fileSize,
    $core.String? licenseTier,
    $pb.PbMap<$core.String, $core.String>? customMetadata,
  }) {
    final $result = create();
    if (fileName != null) {
      $result.fileName = fileName;
    }
    if (mimeType != null) {
      $result.mimeType = mimeType;
    }
    if (fileSize != null) {
      $result.fileSize = fileSize;
    }
    if (licenseTier != null) {
      $result.licenseTier = licenseTier;
    }
    if (customMetadata != null) {
      $result.customMetadata.addAll(customMetadata);
    }
    return $result;
  }
  FileUploadRequest_Metadata._() : super();
  factory FileUploadRequest_Metadata.fromBuffer($core.List<$core.int> i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromBuffer(i, r);
  factory FileUploadRequest_Metadata.fromJson($core.String i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(
      _omitMessageNames ? '' : 'FileUploadRequest.Metadata',
      package: const $pb.PackageName(_omitMessageNames ? '' : 'promz.api.v1'),
      createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'fileName')
    ..aOS(2, _omitFieldNames ? '' : 'mimeType')
    ..aInt64(3, _omitFieldNames ? '' : 'fileSize')
    ..aOS(4, _omitFieldNames ? '' : 'licenseTier')
    ..m<$core.String, $core.String>(5, _omitFieldNames ? '' : 'customMetadata',
        entryClassName: 'FileUploadRequest.Metadata.CustomMetadataEntry',
        keyFieldType: $pb.PbFieldType.OS,
        valueFieldType: $pb.PbFieldType.OS,
        packageName: const $pb.PackageName('promz.api.v1'))
    ..hasRequiredFields = false;

  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
      'Will be removed in next major version')
  FileUploadRequest_Metadata clone() => FileUploadRequest_Metadata()..mergeFromMessage(this);
  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
      'Will be removed in next major version')
  FileUploadRequest_Metadata copyWith(void Function(FileUploadRequest_Metadata) updates) =>
      super.copyWith((message) => updates(message as FileUploadRequest_Metadata))
          as FileUploadRequest_Metadata;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static FileUploadRequest_Metadata create() => FileUploadRequest_Metadata._();
  FileUploadRequest_Metadata createEmptyInstance() => create();
  static $pb.PbList<FileUploadRequest_Metadata> createRepeated() =>
      $pb.PbList<FileUploadRequest_Metadata>();
  @$core.pragma('dart2js:noInline')
  static FileUploadRequest_Metadata getDefault() =>
      _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<FileUploadRequest_Metadata>(create);
  static FileUploadRequest_Metadata? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get fileName => $_getSZ(0);
  @$pb.TagNumber(1)
  set fileName($core.String v) {
    $_setString(0, v);
  }

  @$pb.TagNumber(1)
  $core.bool hasFileName() => $_has(0);
  @$pb.TagNumber(1)
  void clearFileName() => $_clearField(1);

  @$pb.TagNumber(2)
  $core.String get mimeType => $_getSZ(1);
  @$pb.TagNumber(2)
  set mimeType($core.String v) {
    $_setString(1, v);
  }

  @$pb.TagNumber(2)
  $core.bool hasMimeType() => $_has(1);
  @$pb.TagNumber(2)
  void clearMimeType() => $_clearField(2);

  @$pb.TagNumber(3)
  $fixnum.Int64 get fileSize => $_getI64(2);
  @$pb.TagNumber(3)
  set fileSize($fixnum.Int64 v) {
    $_setInt64(2, v);
  }

  @$pb.TagNumber(3)
  $core.bool hasFileSize() => $_has(2);
  @$pb.TagNumber(3)
  void clearFileSize() => $_clearField(3);

  @$pb.TagNumber(4)
  $core.String get licenseTier => $_getSZ(3);
  @$pb.TagNumber(4)
  set licenseTier($core.String v) {
    $_setString(3, v);
  }

  @$pb.TagNumber(4)
  $core.bool hasLicenseTier() => $_has(3);
  @$pb.TagNumber(4)
  void clearLicenseTier() => $_clearField(4);

  @$pb.TagNumber(5)
  $pb.PbMap<$core.String, $core.String> get customMetadata => $_getMap(4);
}

/// Subsequent messages contain file chunks
class FileUploadRequest_Chunk extends $pb.GeneratedMessage {
  factory FileUploadRequest_Chunk({
    $core.List<$core.int>? data,
    $core.int? chunkIndex,
  }) {
    final $result = create();
    if (data != null) {
      $result.data = data;
    }
    if (chunkIndex != null) {
      $result.chunkIndex = chunkIndex;
    }
    return $result;
  }
  FileUploadRequest_Chunk._() : super();
  factory FileUploadRequest_Chunk.fromBuffer($core.List<$core.int> i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromBuffer(i, r);
  factory FileUploadRequest_Chunk.fromJson($core.String i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(
      _omitMessageNames ? '' : 'FileUploadRequest.Chunk',
      package: const $pb.PackageName(_omitMessageNames ? '' : 'promz.api.v1'),
      createEmptyInstance: create)
    ..a<$core.List<$core.int>>(1, _omitFieldNames ? '' : 'data', $pb.PbFieldType.OY)
    ..a<$core.int>(2, _omitFieldNames ? '' : 'chunkIndex', $pb.PbFieldType.O3)
    ..hasRequiredFields = false;

  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
      'Will be removed in next major version')
  FileUploadRequest_Chunk clone() => FileUploadRequest_Chunk()..mergeFromMessage(this);
  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
      'Will be removed in next major version')
  FileUploadRequest_Chunk copyWith(void Function(FileUploadRequest_Chunk) updates) =>
      super.copyWith((message) => updates(message as FileUploadRequest_Chunk))
          as FileUploadRequest_Chunk;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static FileUploadRequest_Chunk create() => FileUploadRequest_Chunk._();
  FileUploadRequest_Chunk createEmptyInstance() => create();
  static $pb.PbList<FileUploadRequest_Chunk> createRepeated() =>
      $pb.PbList<FileUploadRequest_Chunk>();
  @$core.pragma('dart2js:noInline')
  static FileUploadRequest_Chunk getDefault() =>
      _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<FileUploadRequest_Chunk>(create);
  static FileUploadRequest_Chunk? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$core.int> get data => $_getN(0);
  @$pb.TagNumber(1)
  set data($core.List<$core.int> v) {
    $_setBytes(0, v);
  }

  @$pb.TagNumber(1)
  $core.bool hasData() => $_has(0);
  @$pb.TagNumber(1)
  void clearData() => $_clearField(1);

  @$pb.TagNumber(2)
  $core.int get chunkIndex => $_getIZ(1);
  @$pb.TagNumber(2)
  set chunkIndex($core.int v) {
    $_setSignedInt32(1, v);
  }

  @$pb.TagNumber(2)
  $core.bool hasChunkIndex() => $_has(1);
  @$pb.TagNumber(2)
  void clearChunkIndex() => $_clearField(2);
}

enum FileUploadRequest_Request { metadata, chunk, notSet }

/// FileUploadRequest is used for streaming file uploads
class FileUploadRequest extends $pb.GeneratedMessage {
  factory FileUploadRequest({
    FileUploadRequest_Metadata? metadata,
    FileUploadRequest_Chunk? chunk,
  }) {
    final $result = create();
    if (metadata != null) {
      $result.metadata = metadata;
    }
    if (chunk != null) {
      $result.chunk = chunk;
    }
    return $result;
  }
  FileUploadRequest._() : super();
  factory FileUploadRequest.fromBuffer($core.List<$core.int> i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromBuffer(i, r);
  factory FileUploadRequest.fromJson($core.String i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromJson(i, r);

  static const $core.Map<$core.int, FileUploadRequest_Request> _FileUploadRequest_RequestByTag = {
    1: FileUploadRequest_Request.metadata,
    2: FileUploadRequest_Request.chunk,
    0: FileUploadRequest_Request.notSet
  };
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'FileUploadRequest',
      package: const $pb.PackageName(_omitMessageNames ? '' : 'promz.api.v1'),
      createEmptyInstance: create)
    ..oo(0, [1, 2])
    ..aOM<FileUploadRequest_Metadata>(1, _omitFieldNames ? '' : 'metadata',
        subBuilder: FileUploadRequest_Metadata.create)
    ..aOM<FileUploadRequest_Chunk>(2, _omitFieldNames ? '' : 'chunk',
        subBuilder: FileUploadRequest_Chunk.create)
    ..hasRequiredFields = false;

  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
      'Will be removed in next major version')
  FileUploadRequest clone() => FileUploadRequest()..mergeFromMessage(this);
  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
      'Will be removed in next major version')
  FileUploadRequest copyWith(void Function(FileUploadRequest) updates) =>
      super.copyWith((message) => updates(message as FileUploadRequest)) as FileUploadRequest;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static FileUploadRequest create() => FileUploadRequest._();
  FileUploadRequest createEmptyInstance() => create();
  static $pb.PbList<FileUploadRequest> createRepeated() => $pb.PbList<FileUploadRequest>();
  @$core.pragma('dart2js:noInline')
  static FileUploadRequest getDefault() =>
      _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<FileUploadRequest>(create);
  static FileUploadRequest? _defaultInstance;

  FileUploadRequest_Request whichRequest() => _FileUploadRequest_RequestByTag[$_whichOneof(0)]!;
  void clearRequest() => $_clearField($_whichOneof(0));

  @$pb.TagNumber(1)
  FileUploadRequest_Metadata get metadata => $_getN(0);
  @$pb.TagNumber(1)
  set metadata(FileUploadRequest_Metadata v) {
    $_setField(1, v);
  }

  @$pb.TagNumber(1)
  $core.bool hasMetadata() => $_has(0);
  @$pb.TagNumber(1)
  void clearMetadata() => $_clearField(1);
  @$pb.TagNumber(1)
  FileUploadRequest_Metadata ensureMetadata() => $_ensure(0);

  @$pb.TagNumber(2)
  FileUploadRequest_Chunk get chunk => $_getN(1);
  @$pb.TagNumber(2)
  set chunk(FileUploadRequest_Chunk v) {
    $_setField(2, v);
  }

  @$pb.TagNumber(2)
  $core.bool hasChunk() => $_has(1);
  @$pb.TagNumber(2)
  void clearChunk() => $_clearField(2);
  @$pb.TagNumber(2)
  FileUploadRequest_Chunk ensureChunk() => $_ensure(1);
}

/// FileUploadResponse is returned after a successful upload
class FileUploadResponse extends $pb.GeneratedMessage {
  factory FileUploadResponse({
    $core.String? id,
    $core.String? status,
    $core.int? estimatedTimeSeconds,
    $core.int? maxTokens,
    $fixnum.Int64? fileSize,
    $core.String? licenseTier,
  }) {
    final $result = create();
    if (id != null) {
      $result.id = id;
    }
    if (status != null) {
      $result.status = status;
    }
    if (estimatedTimeSeconds != null) {
      $result.estimatedTimeSeconds = estimatedTimeSeconds;
    }
    if (maxTokens != null) {
      $result.maxTokens = maxTokens;
    }
    if (fileSize != null) {
      $result.fileSize = fileSize;
    }
    if (licenseTier != null) {
      $result.licenseTier = licenseTier;
    }
    return $result;
  }
  FileUploadResponse._() : super();
  factory FileUploadResponse.fromBuffer($core.List<$core.int> i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromBuffer(i, r);
  factory FileUploadResponse.fromJson($core.String i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'FileUploadResponse',
      package: const $pb.PackageName(_omitMessageNames ? '' : 'promz.api.v1'),
      createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'id')
    ..aOS(2, _omitFieldNames ? '' : 'status')
    ..a<$core.int>(3, _omitFieldNames ? '' : 'estimatedTimeSeconds', $pb.PbFieldType.O3)
    ..a<$core.int>(4, _omitFieldNames ? '' : 'maxTokens', $pb.PbFieldType.O3)
    ..aInt64(5, _omitFieldNames ? '' : 'fileSize')
    ..aOS(6, _omitFieldNames ? '' : 'licenseTier')
    ..hasRequiredFields = false;

  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
      'Will be removed in next major version')
  FileUploadResponse clone() => FileUploadResponse()..mergeFromMessage(this);
  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
      'Will be removed in next major version')
  FileUploadResponse copyWith(void Function(FileUploadResponse) updates) =>
      super.copyWith((message) => updates(message as FileUploadResponse)) as FileUploadResponse;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static FileUploadResponse create() => FileUploadResponse._();
  FileUploadResponse createEmptyInstance() => create();
  static $pb.PbList<FileUploadResponse> createRepeated() => $pb.PbList<FileUploadResponse>();
  @$core.pragma('dart2js:noInline')
  static FileUploadResponse getDefault() =>
      _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<FileUploadResponse>(create);
  static FileUploadResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get id => $_getSZ(0);
  @$pb.TagNumber(1)
  set id($core.String v) {
    $_setString(0, v);
  }

  @$pb.TagNumber(1)
  $core.bool hasId() => $_has(0);
  @$pb.TagNumber(1)
  void clearId() => $_clearField(1);

  @$pb.TagNumber(2)
  $core.String get status => $_getSZ(1);
  @$pb.TagNumber(2)
  set status($core.String v) {
    $_setString(1, v);
  }

  @$pb.TagNumber(2)
  $core.bool hasStatus() => $_has(1);
  @$pb.TagNumber(2)
  void clearStatus() => $_clearField(2);

  @$pb.TagNumber(3)
  $core.int get estimatedTimeSeconds => $_getIZ(2);
  @$pb.TagNumber(3)
  set estimatedTimeSeconds($core.int v) {
    $_setSignedInt32(2, v);
  }

  @$pb.TagNumber(3)
  $core.bool hasEstimatedTimeSeconds() => $_has(2);
  @$pb.TagNumber(3)
  void clearEstimatedTimeSeconds() => $_clearField(3);

  @$pb.TagNumber(4)
  $core.int get maxTokens => $_getIZ(3);
  @$pb.TagNumber(4)
  set maxTokens($core.int v) {
    $_setSignedInt32(3, v);
  }

  @$pb.TagNumber(4)
  $core.bool hasMaxTokens() => $_has(3);
  @$pb.TagNumber(4)
  void clearMaxTokens() => $_clearField(4);

  @$pb.TagNumber(5)
  $fixnum.Int64 get fileSize => $_getI64(4);
  @$pb.TagNumber(5)
  set fileSize($fixnum.Int64 v) {
    $_setInt64(4, v);
  }

  @$pb.TagNumber(5)
  $core.bool hasFileSize() => $_has(4);
  @$pb.TagNumber(5)
  void clearFileSize() => $_clearField(5);

  @$pb.TagNumber(6)
  $core.String get licenseTier => $_getSZ(5);
  @$pb.TagNumber(6)
  set licenseTier($core.String v) {
    $_setString(5, v);
  }

  @$pb.TagNumber(6)
  $core.bool hasLicenseTier() => $_has(5);
  @$pb.TagNumber(6)
  void clearLicenseTier() => $_clearField(6);
}

/// StatusRequest is used to request the status of a processing job
class StatusRequest extends $pb.GeneratedMessage {
  factory StatusRequest({
    $core.String? id,
  }) {
    final $result = create();
    if (id != null) {
      $result.id = id;
    }
    return $result;
  }
  StatusRequest._() : super();
  factory StatusRequest.fromBuffer($core.List<$core.int> i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromBuffer(i, r);
  factory StatusRequest.fromJson($core.String i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'StatusRequest',
      package: const $pb.PackageName(_omitMessageNames ? '' : 'promz.api.v1'),
      createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'id')
    ..hasRequiredFields = false;

  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
      'Will be removed in next major version')
  StatusRequest clone() => StatusRequest()..mergeFromMessage(this);
  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
      'Will be removed in next major version')
  StatusRequest copyWith(void Function(StatusRequest) updates) =>
      super.copyWith((message) => updates(message as StatusRequest)) as StatusRequest;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static StatusRequest create() => StatusRequest._();
  StatusRequest createEmptyInstance() => create();
  static $pb.PbList<StatusRequest> createRepeated() => $pb.PbList<StatusRequest>();
  @$core.pragma('dart2js:noInline')
  static StatusRequest getDefault() =>
      _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<StatusRequest>(create);
  static StatusRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get id => $_getSZ(0);
  @$pb.TagNumber(1)
  set id($core.String v) {
    $_setString(0, v);
  }

  @$pb.TagNumber(1)
  $core.bool hasId() => $_has(0);
  @$pb.TagNumber(1)
  void clearId() => $_clearField(1);
}

/// StatusResponse contains the current status of a processing job
class StatusResponse extends $pb.GeneratedMessage {
  factory StatusResponse({
    $core.String? id,
    $core.String? status,
    $core.double? progress,
    $core.String? error,
    $core.String? message,
    $core.int? tokensProcessed,
    $core.int? tokensLimit,
    $core.bool? tokensExceeded,
  }) {
    final $result = create();
    if (id != null) {
      $result.id = id;
    }
    if (status != null) {
      $result.status = status;
    }
    if (progress != null) {
      $result.progress = progress;
    }
    if (error != null) {
      $result.error = error;
    }
    if (message != null) {
      $result.message = message;
    }
    if (tokensProcessed != null) {
      $result.tokensProcessed = tokensProcessed;
    }
    if (tokensLimit != null) {
      $result.tokensLimit = tokensLimit;
    }
    if (tokensExceeded != null) {
      $result.tokensExceeded = tokensExceeded;
    }
    return $result;
  }
  StatusResponse._() : super();
  factory StatusResponse.fromBuffer($core.List<$core.int> i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromBuffer(i, r);
  factory StatusResponse.fromJson($core.String i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'StatusResponse',
      package: const $pb.PackageName(_omitMessageNames ? '' : 'promz.api.v1'),
      createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'id')
    ..aOS(2, _omitFieldNames ? '' : 'status')
    ..a<$core.double>(3, _omitFieldNames ? '' : 'progress', $pb.PbFieldType.OD)
    ..aOS(4, _omitFieldNames ? '' : 'error')
    ..aOS(5, _omitFieldNames ? '' : 'message')
    ..a<$core.int>(6, _omitFieldNames ? '' : 'tokensProcessed', $pb.PbFieldType.O3)
    ..a<$core.int>(7, _omitFieldNames ? '' : 'tokensLimit', $pb.PbFieldType.O3)
    ..aOB(8, _omitFieldNames ? '' : 'tokensExceeded')
    ..hasRequiredFields = false;

  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
      'Will be removed in next major version')
  StatusResponse clone() => StatusResponse()..mergeFromMessage(this);
  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
      'Will be removed in next major version')
  StatusResponse copyWith(void Function(StatusResponse) updates) =>
      super.copyWith((message) => updates(message as StatusResponse)) as StatusResponse;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static StatusResponse create() => StatusResponse._();
  StatusResponse createEmptyInstance() => create();
  static $pb.PbList<StatusResponse> createRepeated() => $pb.PbList<StatusResponse>();
  @$core.pragma('dart2js:noInline')
  static StatusResponse getDefault() =>
      _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<StatusResponse>(create);
  static StatusResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get id => $_getSZ(0);
  @$pb.TagNumber(1)
  set id($core.String v) {
    $_setString(0, v);
  }

  @$pb.TagNumber(1)
  $core.bool hasId() => $_has(0);
  @$pb.TagNumber(1)
  void clearId() => $_clearField(1);

  @$pb.TagNumber(2)
  $core.String get status => $_getSZ(1);
  @$pb.TagNumber(2)
  set status($core.String v) {
    $_setString(1, v);
  }

  @$pb.TagNumber(2)
  $core.bool hasStatus() => $_has(1);
  @$pb.TagNumber(2)
  void clearStatus() => $_clearField(2);

  @$pb.TagNumber(3)
  $core.double get progress => $_getN(2);
  @$pb.TagNumber(3)
  set progress($core.double v) {
    $_setDouble(2, v);
  }

  @$pb.TagNumber(3)
  $core.bool hasProgress() => $_has(2);
  @$pb.TagNumber(3)
  void clearProgress() => $_clearField(3);

  @$pb.TagNumber(4)
  $core.String get error => $_getSZ(3);
  @$pb.TagNumber(4)
  set error($core.String v) {
    $_setString(3, v);
  }

  @$pb.TagNumber(4)
  $core.bool hasError() => $_has(3);
  @$pb.TagNumber(4)
  void clearError() => $_clearField(4);

  @$pb.TagNumber(5)
  $core.String get message => $_getSZ(4);
  @$pb.TagNumber(5)
  set message($core.String v) {
    $_setString(4, v);
  }

  @$pb.TagNumber(5)
  $core.bool hasMessage() => $_has(4);
  @$pb.TagNumber(5)
  void clearMessage() => $_clearField(5);

  @$pb.TagNumber(6)
  $core.int get tokensProcessed => $_getIZ(5);
  @$pb.TagNumber(6)
  set tokensProcessed($core.int v) {
    $_setSignedInt32(5, v);
  }

  @$pb.TagNumber(6)
  $core.bool hasTokensProcessed() => $_has(5);
  @$pb.TagNumber(6)
  void clearTokensProcessed() => $_clearField(6);

  @$pb.TagNumber(7)
  $core.int get tokensLimit => $_getIZ(6);
  @$pb.TagNumber(7)
  set tokensLimit($core.int v) {
    $_setSignedInt32(6, v);
  }

  @$pb.TagNumber(7)
  $core.bool hasTokensLimit() => $_has(6);
  @$pb.TagNumber(7)
  void clearTokensLimit() => $_clearField(7);

  @$pb.TagNumber(8)
  $core.bool get tokensExceeded => $_getBF(7);
  @$pb.TagNumber(8)
  set tokensExceeded($core.bool v) {
    $_setBool(7, v);
  }

  @$pb.TagNumber(8)
  $core.bool hasTokensExceeded() => $_has(7);
  @$pb.TagNumber(8)
  void clearTokensExceeded() => $_clearField(8);
}

/// ResultsRequest is used to request the results of a processing job
class ResultsRequest extends $pb.GeneratedMessage {
  factory ResultsRequest({
    $core.String? id,
  }) {
    final $result = create();
    if (id != null) {
      $result.id = id;
    }
    return $result;
  }
  ResultsRequest._() : super();
  factory ResultsRequest.fromBuffer($core.List<$core.int> i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromBuffer(i, r);
  factory ResultsRequest.fromJson($core.String i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'ResultsRequest',
      package: const $pb.PackageName(_omitMessageNames ? '' : 'promz.api.v1'),
      createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'id')
    ..hasRequiredFields = false;

  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
      'Will be removed in next major version')
  ResultsRequest clone() => ResultsRequest()..mergeFromMessage(this);
  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
      'Will be removed in next major version')
  ResultsRequest copyWith(void Function(ResultsRequest) updates) =>
      super.copyWith((message) => updates(message as ResultsRequest)) as ResultsRequest;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static ResultsRequest create() => ResultsRequest._();
  ResultsRequest createEmptyInstance() => create();
  static $pb.PbList<ResultsRequest> createRepeated() => $pb.PbList<ResultsRequest>();
  @$core.pragma('dart2js:noInline')
  static ResultsRequest getDefault() =>
      _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ResultsRequest>(create);
  static ResultsRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get id => $_getSZ(0);
  @$pb.TagNumber(1)
  set id($core.String v) {
    $_setString(0, v);
  }

  @$pb.TagNumber(1)
  $core.bool hasId() => $_has(0);
  @$pb.TagNumber(1)
  void clearId() => $_clearField(1);
}

/// First message contains metadata
class ResultsResponse_Metadata extends $pb.GeneratedMessage {
  factory ResultsResponse_Metadata({
    $core.String? id,
    $core.String? contentType,
    $pb.PbMap<$core.String, $core.String>? metadata,
    $core.bool? hasFullContent,
    $core.String? contentUrl,
    $fixnum.Int64? expiresAt,
    $core.int? tokensProcessed,
    $core.int? tokensLimit,
    $core.bool? tokensExceeded,
  }) {
    final $result = create();
    if (id != null) {
      $result.id = id;
    }
    if (contentType != null) {
      $result.contentType = contentType;
    }
    if (metadata != null) {
      $result.metadata.addAll(metadata);
    }
    if (hasFullContent != null) {
      $result.hasFullContent = hasFullContent;
    }
    if (contentUrl != null) {
      $result.contentUrl = contentUrl;
    }
    if (expiresAt != null) {
      $result.expiresAt = expiresAt;
    }
    if (tokensProcessed != null) {
      $result.tokensProcessed = tokensProcessed;
    }
    if (tokensLimit != null) {
      $result.tokensLimit = tokensLimit;
    }
    if (tokensExceeded != null) {
      $result.tokensExceeded = tokensExceeded;
    }
    return $result;
  }
  ResultsResponse_Metadata._() : super();
  factory ResultsResponse_Metadata.fromBuffer($core.List<$core.int> i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromBuffer(i, r);
  factory ResultsResponse_Metadata.fromJson($core.String i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(
      _omitMessageNames ? '' : 'ResultsResponse.Metadata',
      package: const $pb.PackageName(_omitMessageNames ? '' : 'promz.api.v1'),
      createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'id')
    ..aOS(2, _omitFieldNames ? '' : 'contentType')
    ..m<$core.String, $core.String>(3, _omitFieldNames ? '' : 'metadata',
        entryClassName: 'ResultsResponse.Metadata.MetadataEntry',
        keyFieldType: $pb.PbFieldType.OS,
        valueFieldType: $pb.PbFieldType.OS,
        packageName: const $pb.PackageName('promz.api.v1'))
    ..aOB(4, _omitFieldNames ? '' : 'hasFullContent')
    ..aOS(5, _omitFieldNames ? '' : 'contentUrl')
    ..aInt64(6, _omitFieldNames ? '' : 'expiresAt')
    ..a<$core.int>(7, _omitFieldNames ? '' : 'tokensProcessed', $pb.PbFieldType.O3)
    ..a<$core.int>(8, _omitFieldNames ? '' : 'tokensLimit', $pb.PbFieldType.O3)
    ..aOB(9, _omitFieldNames ? '' : 'tokensExceeded')
    ..hasRequiredFields = false;

  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
      'Will be removed in next major version')
  ResultsResponse_Metadata clone() => ResultsResponse_Metadata()..mergeFromMessage(this);
  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
      'Will be removed in next major version')
  ResultsResponse_Metadata copyWith(void Function(ResultsResponse_Metadata) updates) =>
      super.copyWith((message) => updates(message as ResultsResponse_Metadata))
          as ResultsResponse_Metadata;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static ResultsResponse_Metadata create() => ResultsResponse_Metadata._();
  ResultsResponse_Metadata createEmptyInstance() => create();
  static $pb.PbList<ResultsResponse_Metadata> createRepeated() =>
      $pb.PbList<ResultsResponse_Metadata>();
  @$core.pragma('dart2js:noInline')
  static ResultsResponse_Metadata getDefault() =>
      _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ResultsResponse_Metadata>(create);
  static ResultsResponse_Metadata? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get id => $_getSZ(0);
  @$pb.TagNumber(1)
  set id($core.String v) {
    $_setString(0, v);
  }

  @$pb.TagNumber(1)
  $core.bool hasId() => $_has(0);
  @$pb.TagNumber(1)
  void clearId() => $_clearField(1);

  @$pb.TagNumber(2)
  $core.String get contentType => $_getSZ(1);
  @$pb.TagNumber(2)
  set contentType($core.String v) {
    $_setString(1, v);
  }

  @$pb.TagNumber(2)
  $core.bool hasContentType() => $_has(1);
  @$pb.TagNumber(2)
  void clearContentType() => $_clearField(2);

  @$pb.TagNumber(3)
  $pb.PbMap<$core.String, $core.String> get metadata => $_getMap(2);

  @$pb.TagNumber(4)
  $core.bool get hasFullContent => $_getBF(3);
  @$pb.TagNumber(4)
  set hasFullContent($core.bool v) {
    $_setBool(3, v);
  }

  @$pb.TagNumber(4)
  $core.bool hasHasFullContent() => $_has(3);
  @$pb.TagNumber(4)
  void clearHasFullContent() => $_clearField(4);

  @$pb.TagNumber(5)
  $core.String get contentUrl => $_getSZ(4);
  @$pb.TagNumber(5)
  set contentUrl($core.String v) {
    $_setString(4, v);
  }

  @$pb.TagNumber(5)
  $core.bool hasContentUrl() => $_has(4);
  @$pb.TagNumber(5)
  void clearContentUrl() => $_clearField(5);

  @$pb.TagNumber(6)
  $fixnum.Int64 get expiresAt => $_getI64(5);
  @$pb.TagNumber(6)
  set expiresAt($fixnum.Int64 v) {
    $_setInt64(5, v);
  }

  @$pb.TagNumber(6)
  $core.bool hasExpiresAt() => $_has(5);
  @$pb.TagNumber(6)
  void clearExpiresAt() => $_clearField(6);

  @$pb.TagNumber(7)
  $core.int get tokensProcessed => $_getIZ(6);
  @$pb.TagNumber(7)
  set tokensProcessed($core.int v) {
    $_setSignedInt32(6, v);
  }

  @$pb.TagNumber(7)
  $core.bool hasTokensProcessed() => $_has(6);
  @$pb.TagNumber(7)
  void clearTokensProcessed() => $_clearField(7);

  @$pb.TagNumber(8)
  $core.int get tokensLimit => $_getIZ(7);
  @$pb.TagNumber(8)
  set tokensLimit($core.int v) {
    $_setSignedInt32(7, v);
  }

  @$pb.TagNumber(8)
  $core.bool hasTokensLimit() => $_has(7);
  @$pb.TagNumber(8)
  void clearTokensLimit() => $_clearField(8);

  @$pb.TagNumber(9)
  $core.bool get tokensExceeded => $_getBF(8);
  @$pb.TagNumber(9)
  set tokensExceeded($core.bool v) {
    $_setBool(8, v);
  }

  @$pb.TagNumber(9)
  $core.bool hasTokensExceeded() => $_has(8);
  @$pb.TagNumber(9)
  void clearTokensExceeded() => $_clearField(9);
}

/// Subsequent messages contain content chunks
class ResultsResponse_ContentChunk extends $pb.GeneratedMessage {
  factory ResultsResponse_ContentChunk({
    $core.List<$core.int>? data,
    $core.int? chunkIndex,
    $core.bool? isLast,
  }) {
    final $result = create();
    if (data != null) {
      $result.data = data;
    }
    if (chunkIndex != null) {
      $result.chunkIndex = chunkIndex;
    }
    if (isLast != null) {
      $result.isLast = isLast;
    }
    return $result;
  }
  ResultsResponse_ContentChunk._() : super();
  factory ResultsResponse_ContentChunk.fromBuffer($core.List<$core.int> i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromBuffer(i, r);
  factory ResultsResponse_ContentChunk.fromJson($core.String i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(
      _omitMessageNames ? '' : 'ResultsResponse.ContentChunk',
      package: const $pb.PackageName(_omitMessageNames ? '' : 'promz.api.v1'),
      createEmptyInstance: create)
    ..a<$core.List<$core.int>>(1, _omitFieldNames ? '' : 'data', $pb.PbFieldType.OY)
    ..a<$core.int>(2, _omitFieldNames ? '' : 'chunkIndex', $pb.PbFieldType.O3)
    ..aOB(3, _omitFieldNames ? '' : 'isLast')
    ..hasRequiredFields = false;

  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
      'Will be removed in next major version')
  ResultsResponse_ContentChunk clone() => ResultsResponse_ContentChunk()..mergeFromMessage(this);
  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
      'Will be removed in next major version')
  ResultsResponse_ContentChunk copyWith(void Function(ResultsResponse_ContentChunk) updates) =>
      super.copyWith((message) => updates(message as ResultsResponse_ContentChunk))
          as ResultsResponse_ContentChunk;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static ResultsResponse_ContentChunk create() => ResultsResponse_ContentChunk._();
  ResultsResponse_ContentChunk createEmptyInstance() => create();
  static $pb.PbList<ResultsResponse_ContentChunk> createRepeated() =>
      $pb.PbList<ResultsResponse_ContentChunk>();
  @$core.pragma('dart2js:noInline')
  static ResultsResponse_ContentChunk getDefault() =>
      _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ResultsResponse_ContentChunk>(create);
  static ResultsResponse_ContentChunk? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$core.int> get data => $_getN(0);
  @$pb.TagNumber(1)
  set data($core.List<$core.int> v) {
    $_setBytes(0, v);
  }

  @$pb.TagNumber(1)
  $core.bool hasData() => $_has(0);
  @$pb.TagNumber(1)
  void clearData() => $_clearField(1);

  @$pb.TagNumber(2)
  $core.int get chunkIndex => $_getIZ(1);
  @$pb.TagNumber(2)
  set chunkIndex($core.int v) {
    $_setSignedInt32(1, v);
  }

  @$pb.TagNumber(2)
  $core.bool hasChunkIndex() => $_has(1);
  @$pb.TagNumber(2)
  void clearChunkIndex() => $_clearField(2);

  @$pb.TagNumber(3)
  $core.bool get isLast => $_getBF(2);
  @$pb.TagNumber(3)
  set isLast($core.bool v) {
    $_setBool(2, v);
  }

  @$pb.TagNumber(3)
  $core.bool hasIsLast() => $_has(2);
  @$pb.TagNumber(3)
  void clearIsLast() => $_clearField(3);
}

enum ResultsResponse_Response { metadata, content, notSet }

/// ResultsResponse is used to stream processing results
class ResultsResponse extends $pb.GeneratedMessage {
  factory ResultsResponse({
    ResultsResponse_Metadata? metadata,
    ResultsResponse_ContentChunk? content,
  }) {
    final $result = create();
    if (metadata != null) {
      $result.metadata = metadata;
    }
    if (content != null) {
      $result.content = content;
    }
    return $result;
  }
  ResultsResponse._() : super();
  factory ResultsResponse.fromBuffer($core.List<$core.int> i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromBuffer(i, r);
  factory ResultsResponse.fromJson($core.String i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromJson(i, r);

  static const $core.Map<$core.int, ResultsResponse_Response> _ResultsResponse_ResponseByTag = {
    1: ResultsResponse_Response.metadata,
    2: ResultsResponse_Response.content,
    0: ResultsResponse_Response.notSet
  };
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'ResultsResponse',
      package: const $pb.PackageName(_omitMessageNames ? '' : 'promz.api.v1'),
      createEmptyInstance: create)
    ..oo(0, [1, 2])
    ..aOM<ResultsResponse_Metadata>(1, _omitFieldNames ? '' : 'metadata',
        subBuilder: ResultsResponse_Metadata.create)
    ..aOM<ResultsResponse_ContentChunk>(2, _omitFieldNames ? '' : 'content',
        subBuilder: ResultsResponse_ContentChunk.create)
    ..hasRequiredFields = false;

  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
      'Will be removed in next major version')
  ResultsResponse clone() => ResultsResponse()..mergeFromMessage(this);
  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
      'Will be removed in next major version')
  ResultsResponse copyWith(void Function(ResultsResponse) updates) =>
      super.copyWith((message) => updates(message as ResultsResponse)) as ResultsResponse;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static ResultsResponse create() => ResultsResponse._();
  ResultsResponse createEmptyInstance() => create();
  static $pb.PbList<ResultsResponse> createRepeated() => $pb.PbList<ResultsResponse>();
  @$core.pragma('dart2js:noInline')
  static ResultsResponse getDefault() =>
      _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ResultsResponse>(create);
  static ResultsResponse? _defaultInstance;

  ResultsResponse_Response whichResponse() => _ResultsResponse_ResponseByTag[$_whichOneof(0)]!;
  void clearResponse() => $_clearField($_whichOneof(0));

  @$pb.TagNumber(1)
  ResultsResponse_Metadata get metadata => $_getN(0);
  @$pb.TagNumber(1)
  set metadata(ResultsResponse_Metadata v) {
    $_setField(1, v);
  }

  @$pb.TagNumber(1)
  $core.bool hasMetadata() => $_has(0);
  @$pb.TagNumber(1)
  void clearMetadata() => $_clearField(1);
  @$pb.TagNumber(1)
  ResultsResponse_Metadata ensureMetadata() => $_ensure(0);

  @$pb.TagNumber(2)
  ResultsResponse_ContentChunk get content => $_getN(1);
  @$pb.TagNumber(2)
  set content(ResultsResponse_ContentChunk v) {
    $_setField(2, v);
  }

  @$pb.TagNumber(2)
  $core.bool hasContent() => $_has(1);
  @$pb.TagNumber(2)
  void clearContent() => $_clearField(2);
  @$pb.TagNumber(2)
  ResultsResponse_ContentChunk ensureContent() => $_ensure(1);
}

/// CancelRequest is used to cancel a processing job
class CancelRequest extends $pb.GeneratedMessage {
  factory CancelRequest({
    $core.String? id,
  }) {
    final $result = create();
    if (id != null) {
      $result.id = id;
    }
    return $result;
  }
  CancelRequest._() : super();
  factory CancelRequest.fromBuffer($core.List<$core.int> i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromBuffer(i, r);
  factory CancelRequest.fromJson($core.String i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'CancelRequest',
      package: const $pb.PackageName(_omitMessageNames ? '' : 'promz.api.v1'),
      createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'id')
    ..hasRequiredFields = false;

  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
      'Will be removed in next major version')
  CancelRequest clone() => CancelRequest()..mergeFromMessage(this);
  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
      'Will be removed in next major version')
  CancelRequest copyWith(void Function(CancelRequest) updates) =>
      super.copyWith((message) => updates(message as CancelRequest)) as CancelRequest;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static CancelRequest create() => CancelRequest._();
  CancelRequest createEmptyInstance() => create();
  static $pb.PbList<CancelRequest> createRepeated() => $pb.PbList<CancelRequest>();
  @$core.pragma('dart2js:noInline')
  static CancelRequest getDefault() =>
      _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<CancelRequest>(create);
  static CancelRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get id => $_getSZ(0);
  @$pb.TagNumber(1)
  set id($core.String v) {
    $_setString(0, v);
  }

  @$pb.TagNumber(1)
  $core.bool hasId() => $_has(0);
  @$pb.TagNumber(1)
  void clearId() => $_clearField(1);
}

/// CancelResponse is returned after a cancellation request
class CancelResponse extends $pb.GeneratedMessage {
  factory CancelResponse({
    $core.bool? success,
    $core.String? message,
  }) {
    final $result = create();
    if (success != null) {
      $result.success = success;
    }
    if (message != null) {
      $result.message = message;
    }
    return $result;
  }
  CancelResponse._() : super();
  factory CancelResponse.fromBuffer($core.List<$core.int> i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromBuffer(i, r);
  factory CancelResponse.fromJson($core.String i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'CancelResponse',
      package: const $pb.PackageName(_omitMessageNames ? '' : 'promz.api.v1'),
      createEmptyInstance: create)
    ..aOB(1, _omitFieldNames ? '' : 'success')
    ..aOS(2, _omitFieldNames ? '' : 'message')
    ..hasRequiredFields = false;

  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
      'Will be removed in next major version')
  CancelResponse clone() => CancelResponse()..mergeFromMessage(this);
  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
      'Will be removed in next major version')
  CancelResponse copyWith(void Function(CancelResponse) updates) =>
      super.copyWith((message) => updates(message as CancelResponse)) as CancelResponse;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static CancelResponse create() => CancelResponse._();
  CancelResponse createEmptyInstance() => create();
  static $pb.PbList<CancelResponse> createRepeated() => $pb.PbList<CancelResponse>();
  @$core.pragma('dart2js:noInline')
  static CancelResponse getDefault() =>
      _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<CancelResponse>(create);
  static CancelResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.bool get success => $_getBF(0);
  @$pb.TagNumber(1)
  set success($core.bool v) {
    $_setBool(0, v);
  }

  @$pb.TagNumber(1)
  $core.bool hasSuccess() => $_has(0);
  @$pb.TagNumber(1)
  void clearSuccess() => $_clearField(1);

  @$pb.TagNumber(2)
  $core.String get message => $_getSZ(1);
  @$pb.TagNumber(2)
  set message($core.String v) {
    $_setString(1, v);
  }

  @$pb.TagNumber(2)
  $core.bool hasMessage() => $_has(1);
  @$pb.TagNumber(2)
  void clearMessage() => $_clearField(2);
}

const _omitFieldNames = $core.bool.fromEnvironment('protobuf.omit_field_names');
const _omitMessageNames = $core.bool.fromEnvironment('protobuf.omit_message_names');
