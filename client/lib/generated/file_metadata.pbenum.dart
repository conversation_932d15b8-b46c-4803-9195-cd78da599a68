//
//  Generated code. Do not modify.
//  source: file_metadata.proto
//
// @dart = 3.3

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:core' as $core;

import 'package:protobuf/protobuf.dart' as $pb;

/// Error codes specific to file processing operations
class FileProcessingErrorCode extends $pb.ProtobufEnum {
  /// Unknown or unspecified error
  static const FileProcessingErrorCode FILE_PROCESSING_ERROR_UNSPECIFIED =
      FileProcessingErrorCode._(0, _omitEnumNames ? '' : 'FILE_PROCESSING_ERROR_UNSPECIFIED');

  /// File size exceeds the limit for the user's license tier
  static const FileProcessingErrorCode FILE_SIZE_LIMIT_EXCEEDED =
      FileProcessingErrorCode._(1, _omitEnumNames ? '' : 'FILE_SIZE_LIMIT_EXCEEDED');

  /// File format is not supported
  static const FileProcessingErrorCode FILE_FORMAT_UNSUPPORTED =
      FileProcessingErrorCode._(2, _omitEnumNames ? '' : 'FILE_FORMAT_UNSUPPORTED');

  /// File is corrupted or cannot be read
  static const FileProcessingErrorCode FILE_CORRUPTED =
      FileProcessingErrorCode._(3, _omitEnumNames ? '' : 'FILE_CORRUPTED');

  /// User has reached their quota limit
  static const FileProcessingErrorCode QUOTA_EXCEEDED =
      FileProcessingErrorCode._(4, _omitEnumNames ? '' : 'QUOTA_EXCEEDED');

  /// Authentication or authorization error
  static const FileProcessingErrorCode AUTHENTICATION_ERROR =
      FileProcessingErrorCode._(5, _omitEnumNames ? '' : 'AUTHENTICATION_ERROR');

  static const $core.List<FileProcessingErrorCode> values = <FileProcessingErrorCode>[
    FILE_PROCESSING_ERROR_UNSPECIFIED,
    FILE_SIZE_LIMIT_EXCEEDED,
    FILE_FORMAT_UNSUPPORTED,
    FILE_CORRUPTED,
    QUOTA_EXCEEDED,
    AUTHENTICATION_ERROR,
  ];

  static final $core.Map<$core.int, FileProcessingErrorCode> _byValue =
      $pb.ProtobufEnum.initByValue(values);
  static FileProcessingErrorCode? valueOf($core.int value) => _byValue[value];

  const FileProcessingErrorCode._(super.v, super.n);
}

const _omitEnumNames = $core.bool.fromEnvironment('protobuf.omit_enum_names');
