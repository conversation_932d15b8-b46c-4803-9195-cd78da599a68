//
//  Generated code. Do not modify.
//  source: file_metadata.proto
//
// @dart = 3.3

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:convert' as $convert;
import 'dart:core' as $core;
import 'dart:typed_data' as $typed_data;

@$core.Deprecated('Use fileProcessingErrorCodeDescriptor instead')
const FileProcessingErrorCode$json = {
  '1': 'FileProcessingErrorCode',
  '2': [
    {'1': 'FILE_PROCESSING_ERROR_UNSPECIFIED', '2': 0},
    {'1': 'FILE_SIZE_LIMIT_EXCEEDED', '2': 1},
    {'1': 'FILE_FORMAT_UNSUPPORTED', '2': 2},
    {'1': 'FILE_CORRUPTED', '2': 3},
    {'1': 'QUOTA_EXCEEDED', '2': 4},
    {'1': 'AUTHENTICATION_ERROR', '2': 5},
  ],
};

/// Descriptor for `FileProcessingErrorCode`. Decode as a `google.protobuf.EnumDescriptorProto`.
final $typed_data.Uint8List fileProcessingErrorCodeDescriptor = $convert
    .base64Decode('ChdGaWxlUHJvY2Vzc2luZ0Vycm9yQ29kZRIlCiFGSUxFX1BST0NFU1NJTkdfRVJST1JfVU5TUE'
        'VDSUZJRUQQABIcChhGSUxFX1NJWkVfTElNSVRfRVhDRUVERUQQARIbChdGSUxFX0ZPUk1BVF9V'
        'TlNVUFBPUlRFRBACEhIKDkZJTEVfQ09SUlVQVEVEEAMSEgoOUVVPVEFfRVhDRUVERUQQBBIYCh'
        'RBVVRIRU5USUNBVElPTl9FUlJPUhAF');

@$core.Deprecated('Use fileSizeValidationRequestDescriptor instead')
const FileSizeValidationRequest$json = {
  '1': 'FileSizeValidationRequest',
  '2': [
    {'1': 'file_name', '3': 1, '4': 1, '5': 9, '10': 'fileName'},
    {'1': 'file_size_bytes', '3': 2, '4': 1, '5': 3, '10': 'fileSizeBytes'},
  ],
};

/// Descriptor for `FileSizeValidationRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List fileSizeValidationRequestDescriptor = $convert
    .base64Decode('ChlGaWxlU2l6ZVZhbGlkYXRpb25SZXF1ZXN0EhsKCWZpbGVfbmFtZRgBIAEoCVIIZmlsZU5hbW'
        'USJgoPZmlsZV9zaXplX2J5dGVzGAIgASgDUg1maWxlU2l6ZUJ5dGVz');

@$core.Deprecated('Use fileSizeValidationResponseDescriptor instead')
const FileSizeValidationResponse$json = {
  '1': 'FileSizeValidationResponse',
  '2': [
    {'1': 'is_valid', '3': 1, '4': 1, '5': 8, '10': 'isValid'},
    {
      '1': 'error_code',
      '3': 2,
      '4': 1,
      '5': 14,
      '6': '.promz.api.v1.FileProcessingErrorCode',
      '10': 'errorCode'
    },
    {'1': 'error_message', '3': 3, '4': 1, '5': 9, '10': 'errorMessage'},
    {
      '1': 'error_details',
      '3': 4,
      '4': 3,
      '5': 11,
      '6': '.promz.api.v1.FileSizeValidationResponse.ErrorDetailsEntry',
      '10': 'errorDetails'
    },
    {'1': 'next_tier', '3': 5, '4': 1, '5': 9, '10': 'nextTier'},
    {'1': 'max_size_bytes', '3': 6, '4': 1, '5': 3, '10': 'maxSizeBytes'},
  ],
  '3': [FileSizeValidationResponse_ErrorDetailsEntry$json],
};

@$core.Deprecated('Use fileSizeValidationResponseDescriptor instead')
const FileSizeValidationResponse_ErrorDetailsEntry$json = {
  '1': 'ErrorDetailsEntry',
  '2': [
    {'1': 'key', '3': 1, '4': 1, '5': 9, '10': 'key'},
    {'1': 'value', '3': 2, '4': 1, '5': 9, '10': 'value'},
  ],
  '7': {'7': true},
};

/// Descriptor for `FileSizeValidationResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List fileSizeValidationResponseDescriptor = $convert
    .base64Decode('ChpGaWxlU2l6ZVZhbGlkYXRpb25SZXNwb25zZRIZCghpc192YWxpZBgBIAEoCFIHaXNWYWxpZB'
        'JECgplcnJvcl9jb2RlGAIgASgOMiUucHJvbXouYXBpLnYxLkZpbGVQcm9jZXNzaW5nRXJyb3JD'
        'b2RlUgllcnJvckNvZGUSIwoNZXJyb3JfbWVzc2FnZRgDIAEoCVIMZXJyb3JNZXNzYWdlEl8KDW'
        'Vycm9yX2RldGFpbHMYBCADKAsyOi5wcm9tei5hcGkudjEuRmlsZVNpemVWYWxpZGF0aW9uUmVz'
        'cG9uc2UuRXJyb3JEZXRhaWxzRW50cnlSDGVycm9yRGV0YWlscxIbCgluZXh0X3RpZXIYBSABKA'
        'lSCG5leHRUaWVyEiQKDm1heF9zaXplX2J5dGVzGAYgASgDUgxtYXhTaXplQnl0ZXMaPwoRRXJy'
        'b3JEZXRhaWxzRW50cnkSEAoDa2V5GAEgASgJUgNrZXkSFAoFdmFsdWUYAiABKAlSBXZhbHVlOg'
        'I4AQ==');

@$core.Deprecated('Use fileUploadRequestDescriptor instead')
const FileUploadRequest$json = {
  '1': 'FileUploadRequest',
  '2': [
    {
      '1': 'metadata',
      '3': 1,
      '4': 1,
      '5': 11,
      '6': '.promz.api.v1.FileUploadRequest.Metadata',
      '9': 0,
      '10': 'metadata'
    },
    {
      '1': 'chunk',
      '3': 2,
      '4': 1,
      '5': 11,
      '6': '.promz.api.v1.FileUploadRequest.Chunk',
      '9': 0,
      '10': 'chunk'
    },
  ],
  '3': [FileUploadRequest_Metadata$json, FileUploadRequest_Chunk$json],
  '8': [
    {'1': 'request'},
  ],
};

@$core.Deprecated('Use fileUploadRequestDescriptor instead')
const FileUploadRequest_Metadata$json = {
  '1': 'Metadata',
  '2': [
    {'1': 'file_name', '3': 1, '4': 1, '5': 9, '10': 'fileName'},
    {'1': 'mime_type', '3': 2, '4': 1, '5': 9, '10': 'mimeType'},
    {'1': 'file_size', '3': 3, '4': 1, '5': 3, '10': 'fileSize'},
    {'1': 'license_tier', '3': 4, '4': 1, '5': 9, '10': 'licenseTier'},
    {
      '1': 'custom_metadata',
      '3': 5,
      '4': 3,
      '5': 11,
      '6': '.promz.api.v1.FileUploadRequest.Metadata.CustomMetadataEntry',
      '10': 'customMetadata'
    },
  ],
  '3': [FileUploadRequest_Metadata_CustomMetadataEntry$json],
};

@$core.Deprecated('Use fileUploadRequestDescriptor instead')
const FileUploadRequest_Metadata_CustomMetadataEntry$json = {
  '1': 'CustomMetadataEntry',
  '2': [
    {'1': 'key', '3': 1, '4': 1, '5': 9, '10': 'key'},
    {'1': 'value', '3': 2, '4': 1, '5': 9, '10': 'value'},
  ],
  '7': {'7': true},
};

@$core.Deprecated('Use fileUploadRequestDescriptor instead')
const FileUploadRequest_Chunk$json = {
  '1': 'Chunk',
  '2': [
    {'1': 'data', '3': 1, '4': 1, '5': 12, '10': 'data'},
    {'1': 'chunk_index', '3': 2, '4': 1, '5': 5, '10': 'chunkIndex'},
  ],
};

/// Descriptor for `FileUploadRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List fileUploadRequestDescriptor = $convert
    .base64Decode('ChFGaWxlVXBsb2FkUmVxdWVzdBJGCghtZXRhZGF0YRgBIAEoCzIoLnByb216LmFwaS52MS5GaW'
        'xlVXBsb2FkUmVxdWVzdC5NZXRhZGF0YUgAUghtZXRhZGF0YRI9CgVjaHVuaxgCIAEoCzIlLnBy'
        'b216LmFwaS52MS5GaWxlVXBsb2FkUmVxdWVzdC5DaHVua0gAUgVjaHVuaxquAgoITWV0YWRhdG'
        'ESGwoJZmlsZV9uYW1lGAEgASgJUghmaWxlTmFtZRIbCgltaW1lX3R5cGUYAiABKAlSCG1pbWVU'
        'eXBlEhsKCWZpbGVfc2l6ZRgDIAEoA1IIZmlsZVNpemUSIQoMbGljZW5zZV90aWVyGAQgASgJUg'
        'tsaWNlbnNlVGllchJlCg9jdXN0b21fbWV0YWRhdGEYBSADKAsyPC5wcm9tei5hcGkudjEuRmls'
        'ZVVwbG9hZFJlcXVlc3QuTWV0YWRhdGEuQ3VzdG9tTWV0YWRhdGFFbnRyeVIOY3VzdG9tTWV0YW'
        'RhdGEaQQoTQ3VzdG9tTWV0YWRhdGFFbnRyeRIQCgNrZXkYASABKAlSA2tleRIUCgV2YWx1ZRgC'
        'IAEoCVIFdmFsdWU6AjgBGjwKBUNodW5rEhIKBGRhdGEYASABKAxSBGRhdGESHwoLY2h1bmtfaW'
        '5kZXgYAiABKAVSCmNodW5rSW5kZXhCCQoHcmVxdWVzdA==');

@$core.Deprecated('Use fileUploadResponseDescriptor instead')
const FileUploadResponse$json = {
  '1': 'FileUploadResponse',
  '2': [
    {'1': 'id', '3': 1, '4': 1, '5': 9, '10': 'id'},
    {'1': 'status', '3': 2, '4': 1, '5': 9, '10': 'status'},
    {'1': 'estimated_time_seconds', '3': 3, '4': 1, '5': 5, '10': 'estimatedTimeSeconds'},
    {'1': 'max_tokens', '3': 4, '4': 1, '5': 5, '10': 'maxTokens'},
    {'1': 'file_size', '3': 5, '4': 1, '5': 3, '10': 'fileSize'},
    {'1': 'license_tier', '3': 6, '4': 1, '5': 9, '10': 'licenseTier'},
  ],
};

/// Descriptor for `FileUploadResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List fileUploadResponseDescriptor = $convert
    .base64Decode('ChJGaWxlVXBsb2FkUmVzcG9uc2USDgoCaWQYASABKAlSAmlkEhYKBnN0YXR1cxgCIAEoCVIGc3'
        'RhdHVzEjQKFmVzdGltYXRlZF90aW1lX3NlY29uZHMYAyABKAVSFGVzdGltYXRlZFRpbWVTZWNv'
        'bmRzEh0KCm1heF90b2tlbnMYBCABKAVSCW1heFRva2VucxIbCglmaWxlX3NpemUYBSABKANSCG'
        'ZpbGVTaXplEiEKDGxpY2Vuc2VfdGllchgGIAEoCVILbGljZW5zZVRpZXI=');

@$core.Deprecated('Use statusRequestDescriptor instead')
const StatusRequest$json = {
  '1': 'StatusRequest',
  '2': [
    {'1': 'id', '3': 1, '4': 1, '5': 9, '10': 'id'},
  ],
};

/// Descriptor for `StatusRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List statusRequestDescriptor =
    $convert.base64Decode('Cg1TdGF0dXNSZXF1ZXN0Eg4KAmlkGAEgASgJUgJpZA==');

@$core.Deprecated('Use statusResponseDescriptor instead')
const StatusResponse$json = {
  '1': 'StatusResponse',
  '2': [
    {'1': 'id', '3': 1, '4': 1, '5': 9, '10': 'id'},
    {'1': 'status', '3': 2, '4': 1, '5': 9, '10': 'status'},
    {'1': 'progress', '3': 3, '4': 1, '5': 1, '10': 'progress'},
    {'1': 'error', '3': 4, '4': 1, '5': 9, '10': 'error'},
    {'1': 'message', '3': 5, '4': 1, '5': 9, '10': 'message'},
    {'1': 'tokens_processed', '3': 6, '4': 1, '5': 5, '10': 'tokensProcessed'},
    {'1': 'tokens_limit', '3': 7, '4': 1, '5': 5, '10': 'tokensLimit'},
    {'1': 'tokens_exceeded', '3': 8, '4': 1, '5': 8, '10': 'tokensExceeded'},
  ],
};

/// Descriptor for `StatusResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List statusResponseDescriptor = $convert
    .base64Decode('Cg5TdGF0dXNSZXNwb25zZRIOCgJpZBgBIAEoCVICaWQSFgoGc3RhdHVzGAIgASgJUgZzdGF0dX'
        'MSGgoIcHJvZ3Jlc3MYAyABKAFSCHByb2dyZXNzEhQKBWVycm9yGAQgASgJUgVlcnJvchIYCgdt'
        'ZXNzYWdlGAUgASgJUgdtZXNzYWdlEikKEHRva2Vuc19wcm9jZXNzZWQYBiABKAVSD3Rva2Vuc1'
        'Byb2Nlc3NlZBIhCgx0b2tlbnNfbGltaXQYByABKAVSC3Rva2Vuc0xpbWl0EicKD3Rva2Vuc19l'
        'eGNlZWRlZBgIIAEoCFIOdG9rZW5zRXhjZWVkZWQ=');

@$core.Deprecated('Use resultsRequestDescriptor instead')
const ResultsRequest$json = {
  '1': 'ResultsRequest',
  '2': [
    {'1': 'id', '3': 1, '4': 1, '5': 9, '10': 'id'},
  ],
};

/// Descriptor for `ResultsRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List resultsRequestDescriptor =
    $convert.base64Decode('Cg5SZXN1bHRzUmVxdWVzdBIOCgJpZBgBIAEoCVICaWQ=');

@$core.Deprecated('Use resultsResponseDescriptor instead')
const ResultsResponse$json = {
  '1': 'ResultsResponse',
  '2': [
    {
      '1': 'metadata',
      '3': 1,
      '4': 1,
      '5': 11,
      '6': '.promz.api.v1.ResultsResponse.Metadata',
      '9': 0,
      '10': 'metadata'
    },
    {
      '1': 'content',
      '3': 2,
      '4': 1,
      '5': 11,
      '6': '.promz.api.v1.ResultsResponse.ContentChunk',
      '9': 0,
      '10': 'content'
    },
  ],
  '3': [ResultsResponse_Metadata$json, ResultsResponse_ContentChunk$json],
  '8': [
    {'1': 'response'},
  ],
};

@$core.Deprecated('Use resultsResponseDescriptor instead')
const ResultsResponse_Metadata$json = {
  '1': 'Metadata',
  '2': [
    {'1': 'id', '3': 1, '4': 1, '5': 9, '10': 'id'},
    {'1': 'content_type', '3': 2, '4': 1, '5': 9, '10': 'contentType'},
    {
      '1': 'metadata',
      '3': 3,
      '4': 3,
      '5': 11,
      '6': '.promz.api.v1.ResultsResponse.Metadata.MetadataEntry',
      '10': 'metadata'
    },
    {'1': 'has_full_content', '3': 4, '4': 1, '5': 8, '10': 'hasFullContent'},
    {'1': 'content_url', '3': 5, '4': 1, '5': 9, '10': 'contentUrl'},
    {'1': 'expires_at', '3': 6, '4': 1, '5': 3, '10': 'expiresAt'},
    {'1': 'tokens_processed', '3': 7, '4': 1, '5': 5, '10': 'tokensProcessed'},
    {'1': 'tokens_limit', '3': 8, '4': 1, '5': 5, '10': 'tokensLimit'},
    {'1': 'tokens_exceeded', '3': 9, '4': 1, '5': 8, '10': 'tokensExceeded'},
  ],
  '3': [ResultsResponse_Metadata_MetadataEntry$json],
};

@$core.Deprecated('Use resultsResponseDescriptor instead')
const ResultsResponse_Metadata_MetadataEntry$json = {
  '1': 'MetadataEntry',
  '2': [
    {'1': 'key', '3': 1, '4': 1, '5': 9, '10': 'key'},
    {'1': 'value', '3': 2, '4': 1, '5': 9, '10': 'value'},
  ],
  '7': {'7': true},
};

@$core.Deprecated('Use resultsResponseDescriptor instead')
const ResultsResponse_ContentChunk$json = {
  '1': 'ContentChunk',
  '2': [
    {'1': 'data', '3': 1, '4': 1, '5': 12, '10': 'data'},
    {'1': 'chunk_index', '3': 2, '4': 1, '5': 5, '10': 'chunkIndex'},
    {'1': 'is_last', '3': 3, '4': 1, '5': 8, '10': 'isLast'},
  ],
};

/// Descriptor for `ResultsResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List resultsResponseDescriptor = $convert
    .base64Decode('Cg9SZXN1bHRzUmVzcG9uc2USRAoIbWV0YWRhdGEYASABKAsyJi5wcm9tei5hcGkudjEuUmVzdW'
        'x0c1Jlc3BvbnNlLk1ldGFkYXRhSABSCG1ldGFkYXRhEkYKB2NvbnRlbnQYAiABKAsyKi5wcm9t'
        'ei5hcGkudjEuUmVzdWx0c1Jlc3BvbnNlLkNvbnRlbnRDaHVua0gAUgdjb250ZW50Gq0DCghNZX'
        'RhZGF0YRIOCgJpZBgBIAEoCVICaWQSIQoMY29udGVudF90eXBlGAIgASgJUgtjb250ZW50VHlw'
        'ZRJQCghtZXRhZGF0YRgDIAMoCzI0LnByb216LmFwaS52MS5SZXN1bHRzUmVzcG9uc2UuTWV0YW'
        'RhdGEuTWV0YWRhdGFFbnRyeVIIbWV0YWRhdGESKAoQaGFzX2Z1bGxfY29udGVudBgEIAEoCFIO'
        'aGFzRnVsbENvbnRlbnQSHwoLY29udGVudF91cmwYBSABKAlSCmNvbnRlbnRVcmwSHQoKZXhwaX'
        'Jlc19hdBgGIAEoA1IJZXhwaXJlc0F0EikKEHRva2Vuc19wcm9jZXNzZWQYByABKAVSD3Rva2Vu'
        'c1Byb2Nlc3NlZBIhCgx0b2tlbnNfbGltaXQYCCABKAVSC3Rva2Vuc0xpbWl0EicKD3Rva2Vuc1'
        '9leGNlZWRlZBgJIAEoCFIOdG9rZW5zRXhjZWVkZWQaOwoNTWV0YWRhdGFFbnRyeRIQCgNrZXkY'
        'ASABKAlSA2tleRIUCgV2YWx1ZRgCIAEoCVIFdmFsdWU6AjgBGlwKDENvbnRlbnRDaHVuaxISCg'
        'RkYXRhGAEgASgMUgRkYXRhEh8KC2NodW5rX2luZGV4GAIgASgFUgpjaHVua0luZGV4EhcKB2lz'
        'X2xhc3QYAyABKAhSBmlzTGFzdEIKCghyZXNwb25zZQ==');

@$core.Deprecated('Use cancelRequestDescriptor instead')
const CancelRequest$json = {
  '1': 'CancelRequest',
  '2': [
    {'1': 'id', '3': 1, '4': 1, '5': 9, '10': 'id'},
  ],
};

/// Descriptor for `CancelRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List cancelRequestDescriptor =
    $convert.base64Decode('Cg1DYW5jZWxSZXF1ZXN0Eg4KAmlkGAEgASgJUgJpZA==');

@$core.Deprecated('Use cancelResponseDescriptor instead')
const CancelResponse$json = {
  '1': 'CancelResponse',
  '2': [
    {'1': 'success', '3': 1, '4': 1, '5': 8, '10': 'success'},
    {'1': 'message', '3': 2, '4': 1, '5': 9, '10': 'message'},
  ],
};

/// Descriptor for `CancelResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List cancelResponseDescriptor = $convert
    .base64Decode('Cg5DYW5jZWxSZXNwb25zZRIYCgdzdWNjZXNzGAEgASgIUgdzdWNjZXNzEhgKB21lc3NhZ2UYAi'
        'ABKAlSB21lc3NhZ2U=');
