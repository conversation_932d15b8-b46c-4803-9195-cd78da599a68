//
//  Generated code. Do not modify.
//  source: whatsapp_metadata.proto
//
// @dart = 3.3

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:convert' as $convert;
import 'dart:core' as $core;
import 'dart:typed_data' as $typed_data;

@$core.Deprecated('Use detectWhatsAppRequestDescriptor instead')
const DetectWhatsAppRequest$json = {
  '1': 'DetectWhatsAppRequest',
  '2': [
    {'1': 'content', '3': 1, '4': 1, '5': 9, '9': 0, '10': 'content'},
    {'1': 'content_id', '3': 2, '4': 1, '5': 9, '9': 0, '10': 'contentId'},
    {'1': 'file_name', '3': 3, '4': 1, '5': 9, '10': 'fileName'},
  ],
  '8': [
    {'1': 'content_source'},
  ],
};

/// Descriptor for `DetectWhatsAppRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List detectWhatsAppRequestDescriptor = $convert
    .base64Decode('ChVEZXRlY3RXaGF0c0FwcFJlcXVlc3QSGgoHY29udGVudBgBIAEoCUgAUgdjb250ZW50Eh8KCm'
        'NvbnRlbnRfaWQYAiABKAlIAFIJY29udGVudElkEhsKCWZpbGVfbmFtZRgDIAEoCVIIZmlsZU5h'
        'bWVCEAoOY29udGVudF9zb3VyY2U=');

@$core.Deprecated('Use detectWhatsAppResponseDescriptor instead')
const DetectWhatsAppResponse$json = {
  '1': 'DetectWhatsAppResponse',
  '2': [
    {'1': 'is_whatsapp_chat', '3': 1, '4': 1, '5': 8, '10': 'isWhatsappChat'},
    {'1': 'confidence_score', '3': 2, '4': 1, '5': 2, '10': 'confidenceScore'},
    {
      '1': 'metadata',
      '3': 3,
      '4': 1,
      '5': 11,
      '6': '.promz.api.v1.WhatsAppMetadata',
      '10': 'metadata'
    },
  ],
};

/// Descriptor for `DetectWhatsAppResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List detectWhatsAppResponseDescriptor = $convert
    .base64Decode('ChZEZXRlY3RXaGF0c0FwcFJlc3BvbnNlEigKEGlzX3doYXRzYXBwX2NoYXQYASABKAhSDmlzV2'
        'hhdHNhcHBDaGF0EikKEGNvbmZpZGVuY2Vfc2NvcmUYAiABKAJSD2NvbmZpZGVuY2VTY29yZRI6'
        'CghtZXRhZGF0YRgDIAEoCzIeLnByb216LmFwaS52MS5XaGF0c0FwcE1ldGFkYXRhUghtZXRhZG'
        'F0YQ==');

@$core.Deprecated('Use processWhatsAppRequestDescriptor instead')
const ProcessWhatsAppRequest$json = {
  '1': 'ProcessWhatsAppRequest',
  '2': [
    {'1': 'content', '3': 1, '4': 1, '5': 9, '9': 0, '10': 'content'},
    {'1': 'content_id', '3': 2, '4': 1, '5': 9, '9': 0, '10': 'contentId'},
    {'1': 'file_name', '3': 3, '4': 1, '5': 9, '10': 'fileName'},
  ],
  '8': [
    {'1': 'content_source'},
  ],
};

/// Descriptor for `ProcessWhatsAppRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List processWhatsAppRequestDescriptor = $convert
    .base64Decode('ChZQcm9jZXNzV2hhdHNBcHBSZXF1ZXN0EhoKB2NvbnRlbnQYASABKAlIAFIHY29udGVudBIfCg'
        'pjb250ZW50X2lkGAIgASgJSABSCWNvbnRlbnRJZBIbCglmaWxlX25hbWUYAyABKAlSCGZpbGVO'
        'YW1lQhAKDmNvbnRlbnRfc291cmNl');

@$core.Deprecated('Use processWhatsAppResponseDescriptor instead')
const ProcessWhatsAppResponse$json = {
  '1': 'ProcessWhatsAppResponse',
  '2': [
    {'1': 'processed_content', '3': 1, '4': 1, '5': 9, '10': 'processedContent'},
    {
      '1': 'metadata',
      '3': 2,
      '4': 1,
      '5': 11,
      '6': '.promz.api.v1.WhatsAppMetadata',
      '10': 'metadata'
    },
    {
      '1': 'variables',
      '3': 3,
      '4': 3,
      '5': 11,
      '6': '.promz.api.v1.ProcessWhatsAppResponse.VariablesEntry',
      '10': 'variables'
    },
  ],
  '3': [ProcessWhatsAppResponse_VariablesEntry$json],
};

@$core.Deprecated('Use processWhatsAppResponseDescriptor instead')
const ProcessWhatsAppResponse_VariablesEntry$json = {
  '1': 'VariablesEntry',
  '2': [
    {'1': 'key', '3': 1, '4': 1, '5': 9, '10': 'key'},
    {'1': 'value', '3': 2, '4': 1, '5': 9, '10': 'value'},
  ],
  '7': {'7': true},
};

/// Descriptor for `ProcessWhatsAppResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List processWhatsAppResponseDescriptor = $convert
    .base64Decode('ChdQcm9jZXNzV2hhdHNBcHBSZXNwb25zZRIrChFwcm9jZXNzZWRfY29udGVudBgBIAEoCVIQcH'
        'JvY2Vzc2VkQ29udGVudBI6CghtZXRhZGF0YRgCIAEoCzIeLnByb216LmFwaS52MS5XaGF0c0Fw'
        'cE1ldGFkYXRhUghtZXRhZGF0YRJSCgl2YXJpYWJsZXMYAyADKAsyNC5wcm9tei5hcGkudjEuUH'
        'JvY2Vzc1doYXRzQXBwUmVzcG9uc2UuVmFyaWFibGVzRW50cnlSCXZhcmlhYmxlcxo8Cg5WYXJp'
        'YWJsZXNFbnRyeRIQCgNrZXkYASABKAlSA2tleRIUCgV2YWx1ZRgCIAEoCVIFdmFsdWU6AjgB');

@$core.Deprecated('Use whatsAppMetadataDescriptor instead')
const WhatsAppMetadata$json = {
  '1': 'WhatsAppMetadata',
  '2': [
    {'1': 'group_name', '3': 1, '4': 1, '5': 9, '10': 'groupName'},
    {'1': 'chat_name', '3': 2, '4': 1, '5': 9, '10': 'chatName'},
    {'1': 'participants', '3': 3, '4': 3, '5': 9, '10': 'participants'},
    {'1': 'participant_count', '3': 4, '4': 1, '5': 5, '10': 'participantCount'},
    {'1': 'message_count', '3': 5, '4': 1, '5': 5, '10': 'messageCount'},
    {'1': 'first_message_timestamp', '3': 6, '4': 1, '5': 3, '10': 'firstMessageTimestamp'},
    {'1': 'last_message_timestamp', '3': 7, '4': 1, '5': 3, '10': 'lastMessageTimestamp'},
    {
      '1': 'sample_messages',
      '3': 8,
      '4': 3,
      '5': 11,
      '6': '.promz.api.v1.WhatsAppMessage',
      '10': 'sampleMessages'
    },
    {'1': 'is_group_chat', '3': 9, '4': 1, '5': 8, '10': 'isGroupChat'},
  ],
};

/// Descriptor for `WhatsAppMetadata`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List whatsAppMetadataDescriptor = $convert
    .base64Decode('ChBXaGF0c0FwcE1ldGFkYXRhEh0KCmdyb3VwX25hbWUYASABKAlSCWdyb3VwTmFtZRIbCgljaG'
        'F0X25hbWUYAiABKAlSCGNoYXROYW1lEiIKDHBhcnRpY2lwYW50cxgDIAMoCVIMcGFydGljaXBh'
        'bnRzEisKEXBhcnRpY2lwYW50X2NvdW50GAQgASgFUhBwYXJ0aWNpcGFudENvdW50EiMKDW1lc3'
        'NhZ2VfY291bnQYBSABKAVSDG1lc3NhZ2VDb3VudBI2ChdmaXJzdF9tZXNzYWdlX3RpbWVzdGFt'
        'cBgGIAEoA1IVZmlyc3RNZXNzYWdlVGltZXN0YW1wEjQKFmxhc3RfbWVzc2FnZV90aW1lc3RhbX'
        'AYByABKANSFGxhc3RNZXNzYWdlVGltZXN0YW1wEkYKD3NhbXBsZV9tZXNzYWdlcxgIIAMoCzId'
        'LnByb216LmFwaS52MS5XaGF0c0FwcE1lc3NhZ2VSDnNhbXBsZU1lc3NhZ2VzEiIKDWlzX2dyb3'
        'VwX2NoYXQYCSABKAhSC2lzR3JvdXBDaGF0');

@$core.Deprecated('Use whatsAppMessageDescriptor instead')
const WhatsAppMessage$json = {
  '1': 'WhatsAppMessage',
  '2': [
    {'1': 'sender', '3': 1, '4': 1, '5': 9, '10': 'sender'},
    {'1': 'timestamp', '3': 2, '4': 1, '5': 3, '10': 'timestamp'},
    {'1': 'content', '3': 3, '4': 1, '5': 9, '10': 'content'},
    {'1': 'has_media', '3': 4, '4': 1, '5': 8, '10': 'hasMedia'},
    {'1': 'media_type', '3': 5, '4': 1, '5': 9, '10': 'mediaType'},
  ],
};

/// Descriptor for `WhatsAppMessage`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List whatsAppMessageDescriptor = $convert
    .base64Decode('Cg9XaGF0c0FwcE1lc3NhZ2USFgoGc2VuZGVyGAEgASgJUgZzZW5kZXISHAoJdGltZXN0YW1wGA'
        'IgASgDUgl0aW1lc3RhbXASGAoHY29udGVudBgDIAEoCVIHY29udGVudBIbCgloYXNfbWVkaWEY'
        'BCABKAhSCGhhc01lZGlhEh0KCm1lZGlhX3R5cGUYBSABKAlSCW1lZGlhVHlwZQ==');

@$core.Deprecated('Use whatsAppResultsResponseDescriptor instead')
const WhatsAppResultsResponse$json = {
  '1': 'WhatsAppResultsResponse',
  '2': [
    {'1': 'id', '3': 1, '4': 1, '5': 9, '10': 'id'},
    {'1': 'content_type', '3': 2, '4': 1, '5': 9, '10': 'contentType'},
    {
      '1': 'metadata',
      '3': 3,
      '4': 3,
      '5': 11,
      '6': '.promz.api.v1.WhatsAppResultsResponse.MetadataEntry',
      '10': 'metadata'
    },
    {'1': 'has_full_content', '3': 4, '4': 1, '5': 8, '10': 'hasFullContent'},
    {'1': 'content_url', '3': 5, '4': 1, '5': 9, '10': 'contentUrl'},
    {'1': 'expires_at', '3': 6, '4': 1, '5': 3, '10': 'expiresAt'},
    {
      '1': 'whatsapp_metadata',
      '3': 10,
      '4': 1,
      '5': 11,
      '6': '.promz.api.v1.WhatsAppMetadata',
      '10': 'whatsappMetadata'
    },
    {'1': 'is_whatsapp_chat', '3': 11, '4': 1, '5': 8, '10': 'isWhatsappChat'},
  ],
  '3': [WhatsAppResultsResponse_MetadataEntry$json],
};

@$core.Deprecated('Use whatsAppResultsResponseDescriptor instead')
const WhatsAppResultsResponse_MetadataEntry$json = {
  '1': 'MetadataEntry',
  '2': [
    {'1': 'key', '3': 1, '4': 1, '5': 9, '10': 'key'},
    {'1': 'value', '3': 2, '4': 1, '5': 9, '10': 'value'},
  ],
  '7': {'7': true},
};

/// Descriptor for `WhatsAppResultsResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List whatsAppResultsResponseDescriptor = $convert
    .base64Decode('ChdXaGF0c0FwcFJlc3VsdHNSZXNwb25zZRIOCgJpZBgBIAEoCVICaWQSIQoMY29udGVudF90eX'
        'BlGAIgASgJUgtjb250ZW50VHlwZRJPCghtZXRhZGF0YRgDIAMoCzIzLnByb216LmFwaS52MS5X'
        'aGF0c0FwcFJlc3VsdHNSZXNwb25zZS5NZXRhZGF0YUVudHJ5UghtZXRhZGF0YRIoChBoYXNfZn'
        'VsbF9jb250ZW50GAQgASgIUg5oYXNGdWxsQ29udGVudBIfCgtjb250ZW50X3VybBgFIAEoCVIK'
        'Y29udGVudFVybBIdCgpleHBpcmVzX2F0GAYgASgDUglleHBpcmVzQXQSSwoRd2hhdHNhcHBfbW'
        'V0YWRhdGEYCiABKAsyHi5wcm9tei5hcGkudjEuV2hhdHNBcHBNZXRhZGF0YVIQd2hhdHNhcHBN'
        'ZXRhZGF0YRIoChBpc193aGF0c2FwcF9jaGF0GAsgASgIUg5pc1doYXRzYXBwQ2hhdBo7Cg1NZX'
        'RhZGF0YUVudHJ5EhAKA2tleRgBIAEoCVIDa2V5EhQKBXZhbHVlGAIgASgJUgV2YWx1ZToCOAE=');
