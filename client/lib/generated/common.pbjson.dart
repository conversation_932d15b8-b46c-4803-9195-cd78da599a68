//
//  Generated code. Do not modify.
//  source: common.proto
//
// @dart = 3.3

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:convert' as $convert;
import 'dart:core' as $core;
import 'dart:typed_data' as $typed_data;

@$core.Deprecated('Use licenseTierDescriptor instead')
const LicenseTier$json = {
  '1': 'LicenseTier',
  '2': [
    {'1': 'LICENSE_TIER_UNSPECIFIED', '2': 0},
    {'1': 'LICENSE_TIER_FREE', '2': 1},
    {'1': 'LICENSE_TIER_PRO', '2': 2},
    {'1': 'LICENSE_TIER_ENTERPRISE', '2': 3},
  ],
};

/// Descriptor for `LicenseTier`. Decode as a `google.protobuf.EnumDescriptorProto`.
final $typed_data.Uint8List licenseTierDescriptor = $convert
    .base64Decode('CgtMaWNlbnNlVGllchIcChhMSUNFTlNFX1RJRVJfVU5TUEVDSUZJRUQQABIVChFMSUNFTlNFX1'
        'RJRVJfRlJFRRABEhQKEExJQ0VOU0VfVElFUl9QUk8QAhIbChdMSUNFTlNFX1RJRVJfRU5URVJQ'
        'UklTRRAD');

@$core.Deprecated('Use uploadStatusDescriptor instead')
const UploadStatus$json = {
  '1': 'UploadStatus',
  '2': [
    {'1': 'UPLOAD_STATUS_UNSPECIFIED', '2': 0},
    {'1': 'UPLOAD_STATUS_QUEUED', '2': 1},
    {'1': 'UPLOAD_STATUS_PROCESSING', '2': 2},
    {'1': 'UPLOAD_STATUS_COMPLETED', '2': 3},
    {'1': 'UPLOAD_STATUS_FAILED', '2': 4},
    {'1': 'UPLOAD_STATUS_CANCELLED', '2': 5},
  ],
};

/// Descriptor for `UploadStatus`. Decode as a `google.protobuf.EnumDescriptorProto`.
final $typed_data.Uint8List uploadStatusDescriptor = $convert
    .base64Decode('CgxVcGxvYWRTdGF0dXMSHQoZVVBMT0FEX1NUQVRVU19VTlNQRUNJRklFRBAAEhgKFFVQTE9BRF'
        '9TVEFUVVNfUVVFVUVEEAESHAoYVVBMT0FEX1NUQVRVU19QUk9DRVNTSU5HEAISGwoXVVBMT0FE'
        'X1NUQVRVU19DT01QTEVURUQQAxIYChRVUExPQURfU1RBVFVTX0ZBSUxFRBAEEhsKF1VQTE9BRF'
        '9TVEFUVVNfQ0FOQ0VMTEVEEAU=');

@$core.Deprecated('Use errorDescriptor instead')
const Error$json = {
  '1': 'Error',
  '2': [
    {'1': 'code', '3': 1, '4': 1, '5': 9, '10': 'code'},
    {'1': 'message', '3': 2, '4': 1, '5': 9, '10': 'message'},
    {
      '1': 'details',
      '3': 3,
      '4': 3,
      '5': 11,
      '6': '.promz.api.v1.Error.DetailsEntry',
      '10': 'details'
    },
  ],
  '3': [Error_DetailsEntry$json],
};

@$core.Deprecated('Use errorDescriptor instead')
const Error_DetailsEntry$json = {
  '1': 'DetailsEntry',
  '2': [
    {'1': 'key', '3': 1, '4': 1, '5': 9, '10': 'key'},
    {'1': 'value', '3': 2, '4': 1, '5': 9, '10': 'value'},
  ],
  '7': {'7': true},
};

/// Descriptor for `Error`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List errorDescriptor = $convert
    .base64Decode('CgVFcnJvchISCgRjb2RlGAEgASgJUgRjb2RlEhgKB21lc3NhZ2UYAiABKAlSB21lc3NhZ2USOg'
        'oHZGV0YWlscxgDIAMoCzIgLnByb216LmFwaS52MS5FcnJvci5EZXRhaWxzRW50cnlSB2RldGFp'
        'bHMaOgoMRGV0YWlsc0VudHJ5EhAKA2tleRgBIAEoCVIDa2V5EhQKBXZhbHVlGAIgASgJUgV2YW'
        'x1ZToCOAE=');

@$core.Deprecated('Use tierLimitsDescriptor instead')
const TierLimits$json = {
  '1': 'TierLimits',
  '2': [
    {'1': 'max_file_size_bytes', '3': 1, '4': 1, '5': 3, '10': 'maxFileSizeBytes'},
    {'1': 'max_tokens', '3': 2, '4': 1, '5': 5, '10': 'maxTokens'},
    {'1': 'max_storage_days', '3': 3, '4': 1, '5': 5, '10': 'maxStorageDays'},
    {'1': 'max_concurrent_jobs', '3': 4, '4': 1, '5': 5, '10': 'maxConcurrentJobs'},
  ],
};

/// Descriptor for `TierLimits`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List tierLimitsDescriptor = $convert
    .base64Decode('CgpUaWVyTGltaXRzEi0KE21heF9maWxlX3NpemVfYnl0ZXMYASABKANSEG1heEZpbGVTaXplQn'
        'l0ZXMSHQoKbWF4X3Rva2VucxgCIAEoBVIJbWF4VG9rZW5zEigKEG1heF9zdG9yYWdlX2RheXMY'
        'AyABKAVSDm1heFN0b3JhZ2VEYXlzEi4KE21heF9jb25jdXJyZW50X2pvYnMYBCABKAVSEW1heE'
        'NvbmN1cnJlbnRKb2Jz');

@$core.Deprecated('Use timestampDescriptor instead')
const Timestamp$json = {
  '1': 'Timestamp',
  '2': [
    {'1': 'seconds', '3': 1, '4': 1, '5': 3, '10': 'seconds'},
    {'1': 'nanos', '3': 2, '4': 1, '5': 5, '10': 'nanos'},
  ],
};

/// Descriptor for `Timestamp`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List timestampDescriptor = $convert
    .base64Decode('CglUaW1lc3RhbXASGAoHc2Vjb25kcxgBIAEoA1IHc2Vjb25kcxIUCgVuYW5vcxgCIAEoBVIFbm'
        'Fub3M=');
