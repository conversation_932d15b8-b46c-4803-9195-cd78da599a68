//
//  Generated code. Do not modify.
//  source: content_upload.proto
//
// @dart = 3.3

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:convert' as $convert;
import 'dart:core' as $core;
import 'dart:typed_data' as $typed_data;

@$core.Deprecated('Use uploadFileRequestDescriptor instead')
const UploadFileRequest$json = {
  '1': 'UploadFileRequest',
  '2': [
    {'1': 'file_content', '3': 1, '4': 1, '5': 12, '10': 'fileContent'},
    {'1': 'file_name', '3': 2, '4': 1, '5': 9, '10': 'fileName'},
    {'1': 'mime_type', '3': 3, '4': 1, '5': 9, '10': 'mimeType'},
    {'1': 'license_tier', '3': 4, '4': 1, '5': 9, '10': 'licenseTier'},
    {'1': 'file_path', '3': 5, '4': 1, '5': 9, '10': 'filePath'},
    {'1': 'processing_type', '3': 6, '4': 1, '5': 9, '10': 'processingType'},
  ],
};

/// Descriptor for `UploadFileRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List uploadFileRequestDescriptor = $convert
    .base64Decode('ChFVcGxvYWRGaWxlUmVxdWVzdBIhCgxmaWxlX2NvbnRlbnQYASABKAxSC2ZpbGVDb250ZW50Eh'
        'sKCWZpbGVfbmFtZRgCIAEoCVIIZmlsZU5hbWUSGwoJbWltZV90eXBlGAMgASgJUghtaW1lVHlw'
        'ZRIhCgxsaWNlbnNlX3RpZXIYBCABKAlSC2xpY2Vuc2VUaWVyEhsKCWZpbGVfcGF0aBgFIAEoCV'
        'IIZmlsZVBhdGgSJwoPcHJvY2Vzc2luZ190eXBlGAYgASgJUg5wcm9jZXNzaW5nVHlwZQ==');

@$core.Deprecated('Use uploadStatusRequestDescriptor instead')
const UploadStatusRequest$json = {
  '1': 'UploadStatusRequest',
  '2': [
    {'1': 'job_id', '3': 1, '4': 1, '5': 9, '10': 'jobId'},
  ],
};

/// Descriptor for `UploadStatusRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List uploadStatusRequestDescriptor =
    $convert.base64Decode('ChNVcGxvYWRTdGF0dXNSZXF1ZXN0EhUKBmpvYl9pZBgBIAEoCVIFam9iSWQ=');

@$core.Deprecated('Use processingResultDescriptor instead')
const ProcessingResult$json = {
  '1': 'ProcessingResult',
  '2': [
    {'1': 'job_id', '3': 1, '4': 1, '5': 9, '10': 'jobId'},
    {'1': 'content_type', '3': 2, '4': 1, '5': 9, '10': 'contentType'},
    {'1': 'content', '3': 3, '4': 1, '5': 9, '10': 'content'},
    {'1': 'expires_at', '3': 4, '4': 1, '5': 9, '10': 'expiresAt'},
    {'1': 'status', '3': 5, '4': 1, '5': 14, '6': '.promz.api.v1.UploadStatus', '10': 'status'},
    {'1': 'error_message', '3': 6, '4': 1, '5': 9, '10': 'errorMessage'},
    {'1': 'file_name', '3': 7, '4': 1, '5': 9, '10': 'fileName'},
    {'1': 'mime_type', '3': 8, '4': 1, '5': 9, '10': 'mimeType'},
    {'1': 'display_name', '3': 9, '4': 1, '5': 9, '10': 'displayName'},
    {'1': 'file_path', '3': 10, '4': 1, '5': 9, '10': 'filePath'},
    {'1': 'timestamp', '3': 11, '4': 1, '5': 3, '10': 'timestamp'},
    {'1': 'is_server_processed', '3': 12, '4': 1, '5': 8, '10': 'isServerProcessed'},
    {'1': 'processing_type', '3': 13, '4': 1, '5': 9, '10': 'processingType'},
    {'1': 'source_url', '3': 14, '4': 1, '5': 9, '10': 'sourceUrl'},
    {'1': 'author', '3': 15, '4': 1, '5': 9, '10': 'author'},
    {'1': 'source', '3': 26, '4': 1, '5': 9, '10': 'source'},
    {'1': 'app_name', '3': 27, '4': 1, '5': 9, '10': 'appName'},
    {'1': 'is_zip_content', '3': 28, '4': 1, '5': 8, '10': 'isZipContent'},
    {'1': 'source_type', '3': 29, '4': 1, '5': 9, '10': 'sourceType'},
    {'1': 'title', '3': 30, '4': 1, '5': 9, '10': 'title'},
    {'1': 'processing_progress', '3': 31, '4': 1, '5': 2, '10': 'processingProgress'},
    {'1': 'processing_message', '3': 32, '4': 1, '5': 9, '10': 'processingMessage'},
    {
      '1': 'zip_metadata',
      '3': 33,
      '4': 1,
      '5': 11,
      '6': '.promz.api.v1.ZipMetadata',
      '9': 0,
      '10': 'zipMetadata'
    },
    {
      '1': 'whatsapp_metadata',
      '3': 34,
      '4': 1,
      '5': 11,
      '6': '.promz.api.v1.WhatsAppMetadata',
      '9': 0,
      '10': 'whatsappMetadata'
    },
    {
      '1': 'file_metadata',
      '3': 35,
      '4': 1,
      '5': 11,
      '6': '.promz.api.v1.FileMetadata',
      '9': 0,
      '10': 'fileMetadata'
    },
    {
      '1': 'article_metadata',
      '3': 36,
      '4': 1,
      '5': 11,
      '6': '.promz.api.v1.ArticleMetadata',
      '9': 0,
      '10': 'articleMetadata'
    },
    {
      '1': 'youtube_metadata',
      '3': 37,
      '4': 1,
      '5': 11,
      '6': '.promz.api.v1.YouTubeMetadata',
      '9': 0,
      '10': 'youtubeMetadata'
    },
  ],
  '8': [
    {'1': 'content_metadata'},
  ],
};

/// Descriptor for `ProcessingResult`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List processingResultDescriptor = $convert
    .base64Decode('ChBQcm9jZXNzaW5nUmVzdWx0EhUKBmpvYl9pZBgBIAEoCVIFam9iSWQSIQoMY29udGVudF90eX'
        'BlGAIgASgJUgtjb250ZW50VHlwZRIYCgdjb250ZW50GAMgASgJUgdjb250ZW50Eh0KCmV4cGly'
        'ZXNfYXQYBCABKAlSCWV4cGlyZXNBdBIyCgZzdGF0dXMYBSABKA4yGi5wcm9tei5hcGkudjEuVX'
        'Bsb2FkU3RhdHVzUgZzdGF0dXMSIwoNZXJyb3JfbWVzc2FnZRgGIAEoCVIMZXJyb3JNZXNzYWdl'
        'EhsKCWZpbGVfbmFtZRgHIAEoCVIIZmlsZU5hbWUSGwoJbWltZV90eXBlGAggASgJUghtaW1lVH'
        'lwZRIhCgxkaXNwbGF5X25hbWUYCSABKAlSC2Rpc3BsYXlOYW1lEhsKCWZpbGVfcGF0aBgKIAEo'
        'CVIIZmlsZVBhdGgSHAoJdGltZXN0YW1wGAsgASgDUgl0aW1lc3RhbXASLgoTaXNfc2VydmVyX3'
        'Byb2Nlc3NlZBgMIAEoCFIRaXNTZXJ2ZXJQcm9jZXNzZWQSJwoPcHJvY2Vzc2luZ190eXBlGA0g'
        'ASgJUg5wcm9jZXNzaW5nVHlwZRIdCgpzb3VyY2VfdXJsGA4gASgJUglzb3VyY2VVcmwSFgoGYX'
        'V0aG9yGA8gASgJUgZhdXRob3ISFgoGc291cmNlGBogASgJUgZzb3VyY2USGQoIYXBwX25hbWUY'
        'GyABKAlSB2FwcE5hbWUSJAoOaXNfemlwX2NvbnRlbnQYHCABKAhSDGlzWmlwQ29udGVudBIfCg'
        'tzb3VyY2VfdHlwZRgdIAEoCVIKc291cmNlVHlwZRIUCgV0aXRsZRgeIAEoCVIFdGl0bGUSLwoT'
        'cHJvY2Vzc2luZ19wcm9ncmVzcxgfIAEoAlIScHJvY2Vzc2luZ1Byb2dyZXNzEi0KEnByb2Nlc3'
        'NpbmdfbWVzc2FnZRggIAEoCVIRcHJvY2Vzc2luZ01lc3NhZ2USPgoMemlwX21ldGFkYXRhGCEg'
        'ASgLMhkucHJvbXouYXBpLnYxLlppcE1ldGFkYXRhSABSC3ppcE1ldGFkYXRhEk0KEXdoYXRzYX'
        'BwX21ldGFkYXRhGCIgASgLMh4ucHJvbXouYXBpLnYxLldoYXRzQXBwTWV0YWRhdGFIAFIQd2hh'
        'dHNhcHBNZXRhZGF0YRJBCg1maWxlX21ldGFkYXRhGCMgASgLMhoucHJvbXouYXBpLnYxLkZpbG'
        'VNZXRhZGF0YUgAUgxmaWxlTWV0YWRhdGESSgoQYXJ0aWNsZV9tZXRhZGF0YRgkIAEoCzIdLnBy'
        'b216LmFwaS52MS5BcnRpY2xlTWV0YWRhdGFIAFIPYXJ0aWNsZU1ldGFkYXRhEkoKEHlvdXR1Ym'
        'VfbWV0YWRhdGEYJSABKAsyHS5wcm9tei5hcGkudjEuWW91VHViZU1ldGFkYXRhSABSD3lvdXR1'
        'YmVNZXRhZGF0YUISChBjb250ZW50X21ldGFkYXRh');

@$core.Deprecated('Use uploadUpdateDescriptor instead')
const UploadUpdate$json = {
  '1': 'UploadUpdate',
  '2': [
    {'1': 'job_id', '3': 1, '4': 1, '5': 9, '10': 'jobId'},
    {'1': 'status', '3': 2, '4': 1, '5': 14, '6': '.promz.api.v1.UploadStatus', '10': 'status'},
    {'1': 'progress_percentage', '3': 3, '4': 1, '5': 2, '10': 'progressPercentage'},
    {'1': 'current_stage', '3': 4, '4': 1, '5': 9, '10': 'currentStage'},
    {'1': 'message', '3': 5, '4': 1, '5': 9, '10': 'message'},
    {
      '1': 'whatsapp_metadata',
      '3': 6,
      '4': 1,
      '5': 11,
      '6': '.promz.api.v1.WhatsAppMetadata',
      '9': 0,
      '10': 'whatsappMetadata'
    },
    {
      '1': 'zip_metadata',
      '3': 7,
      '4': 1,
      '5': 11,
      '6': '.promz.api.v1.ZipMetadata',
      '9': 0,
      '10': 'zipMetadata'
    },
    {
      '1': 'file_metadata',
      '3': 8,
      '4': 1,
      '5': 11,
      '6': '.promz.api.v1.FileMetadata',
      '9': 0,
      '10': 'fileMetadata'
    },
    {
      '1': 'article_metadata',
      '3': 9,
      '4': 1,
      '5': 11,
      '6': '.promz.api.v1.ArticleMetadata',
      '9': 0,
      '10': 'articleMetadata'
    },
    {
      '1': 'youtube_metadata',
      '3': 10,
      '4': 1,
      '5': 11,
      '6': '.promz.api.v1.YouTubeMetadata',
      '9': 0,
      '10': 'youtubeMetadata'
    },
  ],
  '8': [
    {'1': 'partial_metadata'},
  ],
};

/// Descriptor for `UploadUpdate`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List uploadUpdateDescriptor = $convert
    .base64Decode('CgxVcGxvYWRVcGRhdGUSFQoGam9iX2lkGAEgASgJUgVqb2JJZBIyCgZzdGF0dXMYAiABKA4yGi'
        '5wcm9tei5hcGkudjEuVXBsb2FkU3RhdHVzUgZzdGF0dXMSLwoTcHJvZ3Jlc3NfcGVyY2VudGFn'
        'ZRgDIAEoAlIScHJvZ3Jlc3NQZXJjZW50YWdlEiMKDWN1cnJlbnRfc3RhZ2UYBCABKAlSDGN1cn'
        'JlbnRTdGFnZRIYCgdtZXNzYWdlGAUgASgJUgdtZXNzYWdlEk0KEXdoYXRzYXBwX21ldGFkYXRh'
        'GAYgASgLMh4ucHJvbXouYXBpLnYxLldoYXRzQXBwTWV0YWRhdGFIAFIQd2hhdHNhcHBNZXRhZG'
        'F0YRI+Cgx6aXBfbWV0YWRhdGEYByABKAsyGS5wcm9tei5hcGkudjEuWmlwTWV0YWRhdGFIAFIL'
        'emlwTWV0YWRhdGESQQoNZmlsZV9tZXRhZGF0YRgIIAEoCzIaLnByb216LmFwaS52MS5GaWxlTW'
        'V0YWRhdGFIAFIMZmlsZU1ldGFkYXRhEkoKEGFydGljbGVfbWV0YWRhdGEYCSABKAsyHS5wcm9t'
        'ei5hcGkudjEuQXJ0aWNsZU1ldGFkYXRhSABSD2FydGljbGVNZXRhZGF0YRJKChB5b3V0dWJlX2'
        '1ldGFkYXRhGAogASgLMh0ucHJvbXouYXBpLnYxLllvdVR1YmVNZXRhZGF0YUgAUg95b3V0dWJl'
        'TWV0YWRhdGFCEgoQcGFydGlhbF9tZXRhZGF0YQ==');

@$core.Deprecated('Use zipMetadataDescriptor instead')
const ZipMetadata$json = {
  '1': 'ZipMetadata',
  '2': [
    {'1': 'file_count', '3': 1, '4': 1, '5': 5, '10': 'fileCount'},
    {'1': 'files', '3': 2, '4': 3, '5': 11, '6': '.promz.api.v1.FileInfo', '10': 'files'},
    {'1': 'total_size_bytes', '3': 3, '4': 1, '5': 3, '10': 'totalSizeBytes'},
    {'1': 'extracted_at', '3': 4, '4': 1, '5': 9, '10': 'extractedAt'},
    {'1': 'is_whatsapp_chat', '3': 5, '4': 1, '5': 8, '10': 'isWhatsappChat'},
  ],
};

/// Descriptor for `ZipMetadata`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List zipMetadataDescriptor = $convert
    .base64Decode('CgtaaXBNZXRhZGF0YRIdCgpmaWxlX2NvdW50GAEgASgFUglmaWxlQ291bnQSLAoFZmlsZXMYAi'
        'ADKAsyFi5wcm9tei5hcGkudjEuRmlsZUluZm9SBWZpbGVzEigKEHRvdGFsX3NpemVfYnl0ZXMY'
        'AyABKANSDnRvdGFsU2l6ZUJ5dGVzEiEKDGV4dHJhY3RlZF9hdBgEIAEoCVILZXh0cmFjdGVkQX'
        'QSKAoQaXNfd2hhdHNhcHBfY2hhdBgFIAEoCFIOaXNXaGF0c2FwcENoYXQ=');

@$core.Deprecated('Use fileMetadataDescriptor instead')
const FileMetadata$json = {
  '1': 'FileMetadata',
  '2': [
    {'1': 'size_bytes', '3': 1, '4': 1, '5': 3, '10': 'sizeBytes'},
    {'1': 'last_modified', '3': 2, '4': 1, '5': 9, '10': 'lastModified'},
    {'1': 'content_hash', '3': 3, '4': 1, '5': 9, '10': 'contentHash'},
    {'1': 'detected_language', '3': 4, '4': 1, '5': 9, '10': 'detectedLanguage'},
    {'1': 'line_count', '3': 5, '4': 1, '5': 5, '10': 'lineCount'},
    {'1': 'word_count', '3': 6, '4': 1, '5': 5, '10': 'wordCount'},
    {'1': 'char_count', '3': 7, '4': 1, '5': 5, '10': 'charCount'},
    {'1': 'author', '3': 8, '4': 1, '5': 9, '10': 'author'},
    {'1': 'title', '3': 9, '4': 1, '5': 9, '10': 'title'},
    {'1': 'creation_date', '3': 10, '4': 1, '5': 9, '10': 'creationDate'},
  ],
};

/// Descriptor for `FileMetadata`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List fileMetadataDescriptor = $convert
    .base64Decode('CgxGaWxlTWV0YWRhdGESHQoKc2l6ZV9ieXRlcxgBIAEoA1IJc2l6ZUJ5dGVzEiMKDWxhc3RfbW'
        '9kaWZpZWQYAiABKAlSDGxhc3RNb2RpZmllZBIhCgxjb250ZW50X2hhc2gYAyABKAlSC2NvbnRl'
        'bnRIYXNoEisKEWRldGVjdGVkX2xhbmd1YWdlGAQgASgJUhBkZXRlY3RlZExhbmd1YWdlEh0KCm'
        'xpbmVfY291bnQYBSABKAVSCWxpbmVDb3VudBIdCgp3b3JkX2NvdW50GAYgASgFUgl3b3JkQ291'
        'bnQSHQoKY2hhcl9jb3VudBgHIAEoBVIJY2hhckNvdW50EhYKBmF1dGhvchgIIAEoCVIGYXV0aG'
        '9yEhQKBXRpdGxlGAkgASgJUgV0aXRsZRIjCg1jcmVhdGlvbl9kYXRlGAogASgJUgxjcmVhdGlv'
        'bkRhdGU=');

@$core.Deprecated('Use fileInfoDescriptor instead')
const FileInfo$json = {
  '1': 'FileInfo',
  '2': [
    {'1': 'name', '3': 1, '4': 1, '5': 9, '10': 'name'},
    {'1': 'path', '3': 2, '4': 1, '5': 9, '10': 'path'},
    {'1': 'size_bytes', '3': 3, '4': 1, '5': 3, '10': 'sizeBytes'},
    {'1': 'mime_type', '3': 4, '4': 1, '5': 9, '10': 'mimeType'},
    {'1': 'is_text', '3': 5, '4': 1, '5': 8, '10': 'isText'},
    {'1': 'last_modified', '3': 6, '4': 1, '5': 9, '10': 'lastModified'},
  ],
};

/// Descriptor for `FileInfo`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List fileInfoDescriptor = $convert
    .base64Decode('CghGaWxlSW5mbxISCgRuYW1lGAEgASgJUgRuYW1lEhIKBHBhdGgYAiABKAlSBHBhdGgSHQoKc2'
        'l6ZV9ieXRlcxgDIAEoA1IJc2l6ZUJ5dGVzEhsKCW1pbWVfdHlwZRgEIAEoCVIIbWltZVR5cGUS'
        'FwoHaXNfdGV4dBgFIAEoCFIGaXNUZXh0EiMKDWxhc3RfbW9kaWZpZWQYBiABKAlSDGxhc3RNb2'
        'RpZmllZA==');

@$core.Deprecated('Use articleMetadataDescriptor instead')
const ArticleMetadata$json = {
  '1': 'ArticleMetadata',
  '2': [
    {'1': 'url', '3': 1, '4': 1, '5': 9, '10': 'url'},
    {'1': 'final_url', '3': 2, '4': 1, '5': 9, '10': 'finalUrl'},
    {'1': 'title', '3': 3, '4': 1, '5': 9, '10': 'title'},
    {'1': 'excerpt', '3': 4, '4': 1, '5': 9, '10': 'excerpt'},
    {'1': 'site_name', '3': 5, '4': 1, '5': 9, '10': 'siteName'},
    {'1': 'author', '3': 6, '4': 1, '5': 9, '10': 'author'},
    {'1': 'publish_date', '3': 7, '4': 1, '5': 9, '10': 'publishDate'},
    {'1': 'language', '3': 8, '4': 1, '5': 9, '10': 'language'},
    {'1': 'image_url', '3': 9, '4': 1, '5': 9, '10': 'imageUrl'},
    {'1': 'html_content', '3': 10, '4': 1, '5': 9, '10': 'htmlContent'},
  ],
};

/// Descriptor for `ArticleMetadata`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List articleMetadataDescriptor = $convert
    .base64Decode('Cg9BcnRpY2xlTWV0YWRhdGESEAoDdXJsGAEgASgJUgN1cmwSGwoJZmluYWxfdXJsGAIgASgJUg'
        'hmaW5hbFVybBIUCgV0aXRsZRgDIAEoCVIFdGl0bGUSGAoHZXhjZXJwdBgEIAEoCVIHZXhjZXJw'
        'dBIbCglzaXRlX25hbWUYBSABKAlSCHNpdGVOYW1lEhYKBmF1dGhvchgGIAEoCVIGYXV0aG9yEi'
        'EKDHB1Ymxpc2hfZGF0ZRgHIAEoCVILcHVibGlzaERhdGUSGgoIbGFuZ3VhZ2UYCCABKAlSCGxh'
        'bmd1YWdlEhsKCWltYWdlX3VybBgJIAEoCVIIaW1hZ2VVcmwSIQoMaHRtbF9jb250ZW50GAogAS'
        'gJUgtodG1sQ29udGVudA==');

@$core.Deprecated('Use youTubeMetadataDescriptor instead')
const YouTubeMetadata$json = {
  '1': 'YouTubeMetadata',
  '2': [
    {'1': 'url', '3': 1, '4': 1, '5': 9, '10': 'url'},
    {'1': 'video_id', '3': 2, '4': 1, '5': 9, '10': 'videoId'},
    {'1': 'title', '3': 3, '4': 1, '5': 9, '10': 'title'},
    {'1': 'description', '3': 4, '4': 1, '5': 9, '10': 'description'},
    {'1': 'thumbnail_url', '3': 5, '4': 1, '5': 9, '10': 'thumbnailUrl'},
    {'1': 'publish_date', '3': 6, '4': 1, '5': 9, '10': 'publishDate'},
    {'1': 'language', '3': 7, '4': 1, '5': 9, '10': 'language'},
    {'1': 'duration', '3': 8, '4': 1, '5': 9, '10': 'duration'},
    {'1': 'channel_id', '3': 9, '4': 1, '5': 9, '10': 'channelId'},
    {'1': 'channel_name', '3': 10, '4': 1, '5': 9, '10': 'channelName'},
    {'1': 'category', '3': 11, '4': 1, '5': 9, '10': 'category'},
    {'1': 'tags', '3': 12, '4': 1, '5': 9, '10': 'tags'},
    {'1': 'view_count', '3': 13, '4': 1, '5': 9, '10': 'viewCount'},
    {'1': 'like_count', '3': 14, '4': 1, '5': 9, '10': 'likeCount'},
    {'1': 'dislike_count', '3': 15, '4': 1, '5': 9, '10': 'dislikeCount'},
    {'1': 'comment_count', '3': 16, '4': 1, '5': 9, '10': 'commentCount'},
    {'1': 'image_url', '3': 17, '4': 1, '5': 9, '10': 'imageUrl'},
  ],
};

/// Descriptor for `YouTubeMetadata`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List youTubeMetadataDescriptor = $convert
    .base64Decode('Cg9Zb3VUdWJlTWV0YWRhdGESEAoDdXJsGAEgASgJUgN1cmwSGQoIdmlkZW9faWQYAiABKAlSB3'
        'ZpZGVvSWQSFAoFdGl0bGUYAyABKAlSBXRpdGxlEiAKC2Rlc2NyaXB0aW9uGAQgASgJUgtkZXNj'
        'cmlwdGlvbhIjCg10aHVtYm5haWxfdXJsGAUgASgJUgx0aHVtYm5haWxVcmwSIQoMcHVibGlzaF'
        '9kYXRlGAYgASgJUgtwdWJsaXNoRGF0ZRIaCghsYW5ndWFnZRgHIAEoCVIIbGFuZ3VhZ2USGgoI'
        'ZHVyYXRpb24YCCABKAlSCGR1cmF0aW9uEh0KCmNoYW5uZWxfaWQYCSABKAlSCWNoYW5uZWxJZB'
        'IhCgxjaGFubmVsX25hbWUYCiABKAlSC2NoYW5uZWxOYW1lEhoKCGNhdGVnb3J5GAsgASgJUghj'
        'YXRlZ29yeRISCgR0YWdzGAwgASgJUgR0YWdzEh0KCnZpZXdfY291bnQYDSABKAlSCXZpZXdDb3'
        'VudBIdCgpsaWtlX2NvdW50GA4gASgJUglsaWtlQ291bnQSIwoNZGlzbGlrZV9jb3VudBgPIAEo'
        'CVIMZGlzbGlrZUNvdW50EiMKDWNvbW1lbnRfY291bnQYECABKAlSDGNvbW1lbnRDb3VudBIbCg'
        'lpbWFnZV91cmwYESABKAlSCGltYWdlVXJs');
