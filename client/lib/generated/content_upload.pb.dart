//
//  Generated code. Do not modify.
//  source: content_upload.proto
//
// @dart = 3.3

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:core' as $core;

import 'package:fixnum/fixnum.dart' as $fixnum;
import 'package:protobuf/protobuf.dart' as $pb;

import 'common.pbenum.dart' as $3;
import 'whatsapp_metadata.pb.dart' as $0;

export 'package:protobuf/protobuf.dart' show GeneratedMessageGenericExtensions;

/// Request to upload a file
class UploadFileRequest extends $pb.GeneratedMessage {
  factory UploadFileRequest({
    $core.List<$core.int>? fileContent,
    $core.String? fileName,
    $core.String? mimeType,
    $core.String? licenseTier,
    $core.String? filePath,
    $core.String? processingType,
  }) {
    final $result = create();
    if (fileContent != null) {
      $result.fileContent = fileContent;
    }
    if (fileName != null) {
      $result.fileName = fileName;
    }
    if (mimeType != null) {
      $result.mimeType = mimeType;
    }
    if (licenseTier != null) {
      $result.licenseTier = licenseTier;
    }
    if (filePath != null) {
      $result.filePath = filePath;
    }
    if (processingType != null) {
      $result.processingType = processingType;
    }
    return $result;
  }
  UploadFileRequest._() : super();
  factory UploadFileRequest.fromBuffer($core.List<$core.int> i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromBuffer(i, r);
  factory UploadFileRequest.fromJson($core.String i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'UploadFileRequest',
      package: const $pb.PackageName(_omitMessageNames ? '' : 'promz.api.v1'),
      createEmptyInstance: create)
    ..a<$core.List<$core.int>>(1, _omitFieldNames ? '' : 'fileContent', $pb.PbFieldType.OY)
    ..aOS(2, _omitFieldNames ? '' : 'fileName')
    ..aOS(3, _omitFieldNames ? '' : 'mimeType')
    ..aOS(4, _omitFieldNames ? '' : 'licenseTier')
    ..aOS(5, _omitFieldNames ? '' : 'filePath')
    ..aOS(6, _omitFieldNames ? '' : 'processingType')
    ..hasRequiredFields = false;

  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
      'Will be removed in next major version')
  UploadFileRequest clone() => UploadFileRequest()..mergeFromMessage(this);
  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
      'Will be removed in next major version')
  UploadFileRequest copyWith(void Function(UploadFileRequest) updates) =>
      super.copyWith((message) => updates(message as UploadFileRequest)) as UploadFileRequest;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static UploadFileRequest create() => UploadFileRequest._();
  UploadFileRequest createEmptyInstance() => create();
  static $pb.PbList<UploadFileRequest> createRepeated() => $pb.PbList<UploadFileRequest>();
  @$core.pragma('dart2js:noInline')
  static UploadFileRequest getDefault() =>
      _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<UploadFileRequest>(create);
  static UploadFileRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$core.int> get fileContent => $_getN(0);
  @$pb.TagNumber(1)
  set fileContent($core.List<$core.int> v) {
    $_setBytes(0, v);
  }

  @$pb.TagNumber(1)
  $core.bool hasFileContent() => $_has(0);
  @$pb.TagNumber(1)
  void clearFileContent() => $_clearField(1);

  @$pb.TagNumber(2)
  $core.String get fileName => $_getSZ(1);
  @$pb.TagNumber(2)
  set fileName($core.String v) {
    $_setString(1, v);
  }

  @$pb.TagNumber(2)
  $core.bool hasFileName() => $_has(1);
  @$pb.TagNumber(2)
  void clearFileName() => $_clearField(2);

  @$pb.TagNumber(3)
  $core.String get mimeType => $_getSZ(2);
  @$pb.TagNumber(3)
  set mimeType($core.String v) {
    $_setString(2, v);
  }

  @$pb.TagNumber(3)
  $core.bool hasMimeType() => $_has(2);
  @$pb.TagNumber(3)
  void clearMimeType() => $_clearField(3);

  @$pb.TagNumber(4)
  $core.String get licenseTier => $_getSZ(3);
  @$pb.TagNumber(4)
  set licenseTier($core.String v) {
    $_setString(3, v);
  }

  @$pb.TagNumber(4)
  $core.bool hasLicenseTier() => $_has(3);
  @$pb.TagNumber(4)
  void clearLicenseTier() => $_clearField(4);

  @$pb.TagNumber(5)
  $core.String get filePath => $_getSZ(4);
  @$pb.TagNumber(5)
  set filePath($core.String v) {
    $_setString(4, v);
  }

  @$pb.TagNumber(5)
  $core.bool hasFilePath() => $_has(4);
  @$pb.TagNumber(5)
  void clearFilePath() => $_clearField(5);

  @$pb.TagNumber(6)
  $core.String get processingType => $_getSZ(5);
  @$pb.TagNumber(6)
  set processingType($core.String v) {
    $_setString(5, v);
  }

  @$pb.TagNumber(6)
  $core.bool hasProcessingType() => $_has(5);
  @$pb.TagNumber(6)
  void clearProcessingType() => $_clearField(6);
}

/// Request to get upload status
class UploadStatusRequest extends $pb.GeneratedMessage {
  factory UploadStatusRequest({
    $core.String? jobId,
  }) {
    final $result = create();
    if (jobId != null) {
      $result.jobId = jobId;
    }
    return $result;
  }
  UploadStatusRequest._() : super();
  factory UploadStatusRequest.fromBuffer($core.List<$core.int> i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromBuffer(i, r);
  factory UploadStatusRequest.fromJson($core.String i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'UploadStatusRequest',
      package: const $pb.PackageName(_omitMessageNames ? '' : 'promz.api.v1'),
      createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'jobId')
    ..hasRequiredFields = false;

  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
      'Will be removed in next major version')
  UploadStatusRequest clone() => UploadStatusRequest()..mergeFromMessage(this);
  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
      'Will be removed in next major version')
  UploadStatusRequest copyWith(void Function(UploadStatusRequest) updates) =>
      super.copyWith((message) => updates(message as UploadStatusRequest)) as UploadStatusRequest;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static UploadStatusRequest create() => UploadStatusRequest._();
  UploadStatusRequest createEmptyInstance() => create();
  static $pb.PbList<UploadStatusRequest> createRepeated() => $pb.PbList<UploadStatusRequest>();
  @$core.pragma('dart2js:noInline')
  static UploadStatusRequest getDefault() =>
      _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<UploadStatusRequest>(create);
  static UploadStatusRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get jobId => $_getSZ(0);
  @$pb.TagNumber(1)
  set jobId($core.String v) {
    $_setString(0, v);
  }

  @$pb.TagNumber(1)
  $core.bool hasJobId() => $_has(0);
  @$pb.TagNumber(1)
  void clearJobId() => $_clearField(1);
}

enum ProcessingResult_ContentMetadata {
  zipMetadata,
  whatsappMetadata,
  fileMetadata,
  articleMetadata,
  youtubeMetadata,
  notSet
}

/// Result of content upload
class ProcessingResult extends $pb.GeneratedMessage {
  factory ProcessingResult({
    $core.String? jobId,
    $core.String? contentType,
    $core.String? content,
    $core.String? expiresAt,
    $3.UploadStatus? status,
    $core.String? errorMessage,
    $core.String? fileName,
    $core.String? mimeType,
    $core.String? displayName,
    $core.String? filePath,
    $fixnum.Int64? timestamp,
    $core.bool? isServerProcessed,
    $core.String? processingType,
    $core.String? sourceUrl,
    $core.String? author,
    $core.String? source,
    $core.String? appName,
    $core.bool? isZipContent,
    $core.String? sourceType,
    $core.String? title,
    $core.double? processingProgress,
    $core.String? processingMessage,
    ZipMetadata? zipMetadata,
    $0.WhatsAppMetadata? whatsappMetadata,
    FileMetadata? fileMetadata,
    ArticleMetadata? articleMetadata,
    YouTubeMetadata? youtubeMetadata,
  }) {
    final $result = create();
    if (jobId != null) {
      $result.jobId = jobId;
    }
    if (contentType != null) {
      $result.contentType = contentType;
    }
    if (content != null) {
      $result.content = content;
    }
    if (expiresAt != null) {
      $result.expiresAt = expiresAt;
    }
    if (status != null) {
      $result.status = status;
    }
    if (errorMessage != null) {
      $result.errorMessage = errorMessage;
    }
    if (fileName != null) {
      $result.fileName = fileName;
    }
    if (mimeType != null) {
      $result.mimeType = mimeType;
    }
    if (displayName != null) {
      $result.displayName = displayName;
    }
    if (filePath != null) {
      $result.filePath = filePath;
    }
    if (timestamp != null) {
      $result.timestamp = timestamp;
    }
    if (isServerProcessed != null) {
      $result.isServerProcessed = isServerProcessed;
    }
    if (processingType != null) {
      $result.processingType = processingType;
    }
    if (sourceUrl != null) {
      $result.sourceUrl = sourceUrl;
    }
    if (author != null) {
      $result.author = author;
    }
    if (source != null) {
      $result.source = source;
    }
    if (appName != null) {
      $result.appName = appName;
    }
    if (isZipContent != null) {
      $result.isZipContent = isZipContent;
    }
    if (sourceType != null) {
      $result.sourceType = sourceType;
    }
    if (title != null) {
      $result.title = title;
    }
    if (processingProgress != null) {
      $result.processingProgress = processingProgress;
    }
    if (processingMessage != null) {
      $result.processingMessage = processingMessage;
    }
    if (zipMetadata != null) {
      $result.zipMetadata = zipMetadata;
    }
    if (whatsappMetadata != null) {
      $result.whatsappMetadata = whatsappMetadata;
    }
    if (fileMetadata != null) {
      $result.fileMetadata = fileMetadata;
    }
    if (articleMetadata != null) {
      $result.articleMetadata = articleMetadata;
    }
    if (youtubeMetadata != null) {
      $result.youtubeMetadata = youtubeMetadata;
    }
    return $result;
  }
  ProcessingResult._() : super();
  factory ProcessingResult.fromBuffer($core.List<$core.int> i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromBuffer(i, r);
  factory ProcessingResult.fromJson($core.String i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromJson(i, r);

  static const $core.Map<$core.int, ProcessingResult_ContentMetadata>
      _ProcessingResult_ContentMetadataByTag = {
    33: ProcessingResult_ContentMetadata.zipMetadata,
    34: ProcessingResult_ContentMetadata.whatsappMetadata,
    35: ProcessingResult_ContentMetadata.fileMetadata,
    36: ProcessingResult_ContentMetadata.articleMetadata,
    37: ProcessingResult_ContentMetadata.youtubeMetadata,
    0: ProcessingResult_ContentMetadata.notSet
  };
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'ProcessingResult',
      package: const $pb.PackageName(_omitMessageNames ? '' : 'promz.api.v1'),
      createEmptyInstance: create)
    ..oo(0, [33, 34, 35, 36, 37])
    ..aOS(1, _omitFieldNames ? '' : 'jobId')
    ..aOS(2, _omitFieldNames ? '' : 'contentType')
    ..aOS(3, _omitFieldNames ? '' : 'content')
    ..aOS(4, _omitFieldNames ? '' : 'expiresAt')
    ..e<$3.UploadStatus>(5, _omitFieldNames ? '' : 'status', $pb.PbFieldType.OE,
        defaultOrMaker: $3.UploadStatus.UPLOAD_STATUS_UNSPECIFIED,
        valueOf: $3.UploadStatus.valueOf,
        enumValues: $3.UploadStatus.values)
    ..aOS(6, _omitFieldNames ? '' : 'errorMessage')
    ..aOS(7, _omitFieldNames ? '' : 'fileName')
    ..aOS(8, _omitFieldNames ? '' : 'mimeType')
    ..aOS(9, _omitFieldNames ? '' : 'displayName')
    ..aOS(10, _omitFieldNames ? '' : 'filePath')
    ..aInt64(11, _omitFieldNames ? '' : 'timestamp')
    ..aOB(12, _omitFieldNames ? '' : 'isServerProcessed')
    ..aOS(13, _omitFieldNames ? '' : 'processingType')
    ..aOS(14, _omitFieldNames ? '' : 'sourceUrl')
    ..aOS(15, _omitFieldNames ? '' : 'author')
    ..aOS(26, _omitFieldNames ? '' : 'source')
    ..aOS(27, _omitFieldNames ? '' : 'appName')
    ..aOB(28, _omitFieldNames ? '' : 'isZipContent')
    ..aOS(29, _omitFieldNames ? '' : 'sourceType')
    ..aOS(30, _omitFieldNames ? '' : 'title')
    ..a<$core.double>(31, _omitFieldNames ? '' : 'processingProgress', $pb.PbFieldType.OF)
    ..aOS(32, _omitFieldNames ? '' : 'processingMessage')
    ..aOM<ZipMetadata>(33, _omitFieldNames ? '' : 'zipMetadata', subBuilder: ZipMetadata.create)
    ..aOM<$0.WhatsAppMetadata>(34, _omitFieldNames ? '' : 'whatsappMetadata',
        subBuilder: $0.WhatsAppMetadata.create)
    ..aOM<FileMetadata>(35, _omitFieldNames ? '' : 'fileMetadata', subBuilder: FileMetadata.create)
    ..aOM<ArticleMetadata>(36, _omitFieldNames ? '' : 'articleMetadata',
        subBuilder: ArticleMetadata.create)
    ..aOM<YouTubeMetadata>(37, _omitFieldNames ? '' : 'youtubeMetadata',
        subBuilder: YouTubeMetadata.create)
    ..hasRequiredFields = false;

  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
      'Will be removed in next major version')
  ProcessingResult clone() => ProcessingResult()..mergeFromMessage(this);
  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
      'Will be removed in next major version')
  ProcessingResult copyWith(void Function(ProcessingResult) updates) =>
      super.copyWith((message) => updates(message as ProcessingResult)) as ProcessingResult;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static ProcessingResult create() => ProcessingResult._();
  ProcessingResult createEmptyInstance() => create();
  static $pb.PbList<ProcessingResult> createRepeated() => $pb.PbList<ProcessingResult>();
  @$core.pragma('dart2js:noInline')
  static ProcessingResult getDefault() =>
      _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ProcessingResult>(create);
  static ProcessingResult? _defaultInstance;

  ProcessingResult_ContentMetadata whichContentMetadata() =>
      _ProcessingResult_ContentMetadataByTag[$_whichOneof(0)]!;
  void clearContentMetadata() => $_clearField($_whichOneof(0));

  @$pb.TagNumber(1)
  $core.String get jobId => $_getSZ(0);
  @$pb.TagNumber(1)
  set jobId($core.String v) {
    $_setString(0, v);
  }

  @$pb.TagNumber(1)
  $core.bool hasJobId() => $_has(0);
  @$pb.TagNumber(1)
  void clearJobId() => $_clearField(1);

  @$pb.TagNumber(2)
  $core.String get contentType => $_getSZ(1);
  @$pb.TagNumber(2)
  set contentType($core.String v) {
    $_setString(1, v);
  }

  @$pb.TagNumber(2)
  $core.bool hasContentType() => $_has(1);
  @$pb.TagNumber(2)
  void clearContentType() => $_clearField(2);

  @$pb.TagNumber(3)
  $core.String get content => $_getSZ(2);
  @$pb.TagNumber(3)
  set content($core.String v) {
    $_setString(2, v);
  }

  @$pb.TagNumber(3)
  $core.bool hasContent() => $_has(2);
  @$pb.TagNumber(3)
  void clearContent() => $_clearField(3);

  @$pb.TagNumber(4)
  $core.String get expiresAt => $_getSZ(3);
  @$pb.TagNumber(4)
  set expiresAt($core.String v) {
    $_setString(3, v);
  }

  @$pb.TagNumber(4)
  $core.bool hasExpiresAt() => $_has(3);
  @$pb.TagNumber(4)
  void clearExpiresAt() => $_clearField(4);

  /// Processing status
  @$pb.TagNumber(5)
  $3.UploadStatus get status => $_getN(4);
  @$pb.TagNumber(5)
  set status($3.UploadStatus v) {
    $_setField(5, v);
  }

  @$pb.TagNumber(5)
  $core.bool hasStatus() => $_has(4);
  @$pb.TagNumber(5)
  void clearStatus() => $_clearField(5);

  @$pb.TagNumber(6)
  $core.String get errorMessage => $_getSZ(5);
  @$pb.TagNumber(6)
  set errorMessage($core.String v) {
    $_setString(5, v);
  }

  @$pb.TagNumber(6)
  $core.bool hasErrorMessage() => $_has(5);
  @$pb.TagNumber(6)
  void clearErrorMessage() => $_clearField(6);

  /// Fields extracted from metadata
  @$pb.TagNumber(7)
  $core.String get fileName => $_getSZ(6);
  @$pb.TagNumber(7)
  set fileName($core.String v) {
    $_setString(6, v);
  }

  @$pb.TagNumber(7)
  $core.bool hasFileName() => $_has(6);
  @$pb.TagNumber(7)
  void clearFileName() => $_clearField(7);

  @$pb.TagNumber(8)
  $core.String get mimeType => $_getSZ(7);
  @$pb.TagNumber(8)
  set mimeType($core.String v) {
    $_setString(7, v);
  }

  @$pb.TagNumber(8)
  $core.bool hasMimeType() => $_has(7);
  @$pb.TagNumber(8)
  void clearMimeType() => $_clearField(8);

  @$pb.TagNumber(9)
  $core.String get displayName => $_getSZ(8);
  @$pb.TagNumber(9)
  set displayName($core.String v) {
    $_setString(8, v);
  }

  @$pb.TagNumber(9)
  $core.bool hasDisplayName() => $_has(8);
  @$pb.TagNumber(9)
  void clearDisplayName() => $_clearField(9);

  @$pb.TagNumber(10)
  $core.String get filePath => $_getSZ(9);
  @$pb.TagNumber(10)
  set filePath($core.String v) {
    $_setString(9, v);
  }

  @$pb.TagNumber(10)
  $core.bool hasFilePath() => $_has(9);
  @$pb.TagNumber(10)
  void clearFilePath() => $_clearField(10);

  @$pb.TagNumber(11)
  $fixnum.Int64 get timestamp => $_getI64(10);
  @$pb.TagNumber(11)
  set timestamp($fixnum.Int64 v) {
    $_setInt64(10, v);
  }

  @$pb.TagNumber(11)
  $core.bool hasTimestamp() => $_has(10);
  @$pb.TagNumber(11)
  void clearTimestamp() => $_clearField(11);

  @$pb.TagNumber(12)
  $core.bool get isServerProcessed => $_getBF(11);
  @$pb.TagNumber(12)
  set isServerProcessed($core.bool v) {
    $_setBool(11, v);
  }

  @$pb.TagNumber(12)
  $core.bool hasIsServerProcessed() => $_has(11);
  @$pb.TagNumber(12)
  void clearIsServerProcessed() => $_clearField(12);

  @$pb.TagNumber(13)
  $core.String get processingType => $_getSZ(12);
  @$pb.TagNumber(13)
  set processingType($core.String v) {
    $_setString(12, v);
  }

  @$pb.TagNumber(13)
  $core.bool hasProcessingType() => $_has(12);
  @$pb.TagNumber(13)
  void clearProcessingType() => $_clearField(13);

  @$pb.TagNumber(14)
  $core.String get sourceUrl => $_getSZ(13);
  @$pb.TagNumber(14)
  set sourceUrl($core.String v) {
    $_setString(13, v);
  }

  @$pb.TagNumber(14)
  $core.bool hasSourceUrl() => $_has(13);
  @$pb.TagNumber(14)
  void clearSourceUrl() => $_clearField(14);

  @$pb.TagNumber(15)
  $core.String get author => $_getSZ(14);
  @$pb.TagNumber(15)
  set author($core.String v) {
    $_setString(14, v);
  }

  @$pb.TagNumber(15)
  $core.bool hasAuthor() => $_has(14);
  @$pb.TagNumber(15)
  void clearAuthor() => $_clearField(15);

  /// Additional fields needed by various services
  @$pb.TagNumber(26)
  $core.String get source => $_getSZ(15);
  @$pb.TagNumber(26)
  set source($core.String v) {
    $_setString(15, v);
  }

  @$pb.TagNumber(26)
  $core.bool hasSource() => $_has(15);
  @$pb.TagNumber(26)
  void clearSource() => $_clearField(26);

  @$pb.TagNumber(27)
  $core.String get appName => $_getSZ(16);
  @$pb.TagNumber(27)
  set appName($core.String v) {
    $_setString(16, v);
  }

  @$pb.TagNumber(27)
  $core.bool hasAppName() => $_has(16);
  @$pb.TagNumber(27)
  void clearAppName() => $_clearField(27);

  @$pb.TagNumber(28)
  $core.bool get isZipContent => $_getBF(17);
  @$pb.TagNumber(28)
  set isZipContent($core.bool v) {
    $_setBool(17, v);
  }

  @$pb.TagNumber(28)
  $core.bool hasIsZipContent() => $_has(17);
  @$pb.TagNumber(28)
  void clearIsZipContent() => $_clearField(28);

  @$pb.TagNumber(29)
  $core.String get sourceType => $_getSZ(18);
  @$pb.TagNumber(29)
  set sourceType($core.String v) {
    $_setString(18, v);
  }

  @$pb.TagNumber(29)
  $core.bool hasSourceType() => $_has(18);
  @$pb.TagNumber(29)
  void clearSourceType() => $_clearField(29);

  @$pb.TagNumber(30)
  $core.String get title => $_getSZ(19);
  @$pb.TagNumber(30)
  set title($core.String v) {
    $_setString(19, v);
  }

  @$pb.TagNumber(30)
  $core.bool hasTitle() => $_has(19);
  @$pb.TagNumber(30)
  void clearTitle() => $_clearField(30);

  /// Processing progress information
  @$pb.TagNumber(31)
  $core.double get processingProgress => $_getN(20);
  @$pb.TagNumber(31)
  set processingProgress($core.double v) {
    $_setFloat(20, v);
  }

  @$pb.TagNumber(31)
  $core.bool hasProcessingProgress() => $_has(20);
  @$pb.TagNumber(31)
  void clearProcessingProgress() => $_clearField(31);

  @$pb.TagNumber(32)
  $core.String get processingMessage => $_getSZ(21);
  @$pb.TagNumber(32)
  set processingMessage($core.String v) {
    $_setString(21, v);
  }

  @$pb.TagNumber(32)
  $core.bool hasProcessingMessage() => $_has(21);
  @$pb.TagNumber(32)
  void clearProcessingMessage() => $_clearField(32);

  @$pb.TagNumber(33)
  ZipMetadata get zipMetadata => $_getN(22);
  @$pb.TagNumber(33)
  set zipMetadata(ZipMetadata v) {
    $_setField(33, v);
  }

  @$pb.TagNumber(33)
  $core.bool hasZipMetadata() => $_has(22);
  @$pb.TagNumber(33)
  void clearZipMetadata() => $_clearField(33);
  @$pb.TagNumber(33)
  ZipMetadata ensureZipMetadata() => $_ensure(22);

  @$pb.TagNumber(34)
  $0.WhatsAppMetadata get whatsappMetadata => $_getN(23);
  @$pb.TagNumber(34)
  set whatsappMetadata($0.WhatsAppMetadata v) {
    $_setField(34, v);
  }

  @$pb.TagNumber(34)
  $core.bool hasWhatsappMetadata() => $_has(23);
  @$pb.TagNumber(34)
  void clearWhatsappMetadata() => $_clearField(34);
  @$pb.TagNumber(34)
  $0.WhatsAppMetadata ensureWhatsappMetadata() => $_ensure(23);

  @$pb.TagNumber(35)
  FileMetadata get fileMetadata => $_getN(24);
  @$pb.TagNumber(35)
  set fileMetadata(FileMetadata v) {
    $_setField(35, v);
  }

  @$pb.TagNumber(35)
  $core.bool hasFileMetadata() => $_has(24);
  @$pb.TagNumber(35)
  void clearFileMetadata() => $_clearField(35);
  @$pb.TagNumber(35)
  FileMetadata ensureFileMetadata() => $_ensure(24);

  @$pb.TagNumber(36)
  ArticleMetadata get articleMetadata => $_getN(25);
  @$pb.TagNumber(36)
  set articleMetadata(ArticleMetadata v) {
    $_setField(36, v);
  }

  @$pb.TagNumber(36)
  $core.bool hasArticleMetadata() => $_has(25);
  @$pb.TagNumber(36)
  void clearArticleMetadata() => $_clearField(36);
  @$pb.TagNumber(36)
  ArticleMetadata ensureArticleMetadata() => $_ensure(25);

  @$pb.TagNumber(37)
  YouTubeMetadata get youtubeMetadata => $_getN(26);
  @$pb.TagNumber(37)
  set youtubeMetadata(YouTubeMetadata v) {
    $_setField(37, v);
  }

  @$pb.TagNumber(37)
  $core.bool hasYoutubeMetadata() => $_has(26);
  @$pb.TagNumber(37)
  void clearYoutubeMetadata() => $_clearField(37);
  @$pb.TagNumber(37)
  YouTubeMetadata ensureYoutubeMetadata() => $_ensure(26);
}

enum UploadUpdate_PartialMetadata {
  whatsappMetadata,
  zipMetadata,
  fileMetadata,
  articleMetadata,
  youtubeMetadata,
  notSet
}

/// Real-time upload update
class UploadUpdate extends $pb.GeneratedMessage {
  factory UploadUpdate({
    $core.String? jobId,
    $3.UploadStatus? status,
    $core.double? progressPercentage,
    $core.String? currentStage,
    $core.String? message,
    $0.WhatsAppMetadata? whatsappMetadata,
    ZipMetadata? zipMetadata,
    FileMetadata? fileMetadata,
    ArticleMetadata? articleMetadata,
    YouTubeMetadata? youtubeMetadata,
  }) {
    final $result = create();
    if (jobId != null) {
      $result.jobId = jobId;
    }
    if (status != null) {
      $result.status = status;
    }
    if (progressPercentage != null) {
      $result.progressPercentage = progressPercentage;
    }
    if (currentStage != null) {
      $result.currentStage = currentStage;
    }
    if (message != null) {
      $result.message = message;
    }
    if (whatsappMetadata != null) {
      $result.whatsappMetadata = whatsappMetadata;
    }
    if (zipMetadata != null) {
      $result.zipMetadata = zipMetadata;
    }
    if (fileMetadata != null) {
      $result.fileMetadata = fileMetadata;
    }
    if (articleMetadata != null) {
      $result.articleMetadata = articleMetadata;
    }
    if (youtubeMetadata != null) {
      $result.youtubeMetadata = youtubeMetadata;
    }
    return $result;
  }
  UploadUpdate._() : super();
  factory UploadUpdate.fromBuffer($core.List<$core.int> i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromBuffer(i, r);
  factory UploadUpdate.fromJson($core.String i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromJson(i, r);

  static const $core.Map<$core.int, UploadUpdate_PartialMetadata>
      _UploadUpdate_PartialMetadataByTag = {
    6: UploadUpdate_PartialMetadata.whatsappMetadata,
    7: UploadUpdate_PartialMetadata.zipMetadata,
    8: UploadUpdate_PartialMetadata.fileMetadata,
    9: UploadUpdate_PartialMetadata.articleMetadata,
    10: UploadUpdate_PartialMetadata.youtubeMetadata,
    0: UploadUpdate_PartialMetadata.notSet
  };
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'UploadUpdate',
      package: const $pb.PackageName(_omitMessageNames ? '' : 'promz.api.v1'),
      createEmptyInstance: create)
    ..oo(0, [6, 7, 8, 9, 10])
    ..aOS(1, _omitFieldNames ? '' : 'jobId')
    ..e<$3.UploadStatus>(2, _omitFieldNames ? '' : 'status', $pb.PbFieldType.OE,
        defaultOrMaker: $3.UploadStatus.UPLOAD_STATUS_UNSPECIFIED,
        valueOf: $3.UploadStatus.valueOf,
        enumValues: $3.UploadStatus.values)
    ..a<$core.double>(3, _omitFieldNames ? '' : 'progressPercentage', $pb.PbFieldType.OF)
    ..aOS(4, _omitFieldNames ? '' : 'currentStage')
    ..aOS(5, _omitFieldNames ? '' : 'message')
    ..aOM<$0.WhatsAppMetadata>(6, _omitFieldNames ? '' : 'whatsappMetadata',
        subBuilder: $0.WhatsAppMetadata.create)
    ..aOM<ZipMetadata>(7, _omitFieldNames ? '' : 'zipMetadata', subBuilder: ZipMetadata.create)
    ..aOM<FileMetadata>(8, _omitFieldNames ? '' : 'fileMetadata', subBuilder: FileMetadata.create)
    ..aOM<ArticleMetadata>(9, _omitFieldNames ? '' : 'articleMetadata',
        subBuilder: ArticleMetadata.create)
    ..aOM<YouTubeMetadata>(10, _omitFieldNames ? '' : 'youtubeMetadata',
        subBuilder: YouTubeMetadata.create)
    ..hasRequiredFields = false;

  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
      'Will be removed in next major version')
  UploadUpdate clone() => UploadUpdate()..mergeFromMessage(this);
  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
      'Will be removed in next major version')
  UploadUpdate copyWith(void Function(UploadUpdate) updates) =>
      super.copyWith((message) => updates(message as UploadUpdate)) as UploadUpdate;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static UploadUpdate create() => UploadUpdate._();
  UploadUpdate createEmptyInstance() => create();
  static $pb.PbList<UploadUpdate> createRepeated() => $pb.PbList<UploadUpdate>();
  @$core.pragma('dart2js:noInline')
  static UploadUpdate getDefault() =>
      _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<UploadUpdate>(create);
  static UploadUpdate? _defaultInstance;

  UploadUpdate_PartialMetadata whichPartialMetadata() =>
      _UploadUpdate_PartialMetadataByTag[$_whichOneof(0)]!;
  void clearPartialMetadata() => $_clearField($_whichOneof(0));

  @$pb.TagNumber(1)
  $core.String get jobId => $_getSZ(0);
  @$pb.TagNumber(1)
  set jobId($core.String v) {
    $_setString(0, v);
  }

  @$pb.TagNumber(1)
  $core.bool hasJobId() => $_has(0);
  @$pb.TagNumber(1)
  void clearJobId() => $_clearField(1);

  @$pb.TagNumber(2)
  $3.UploadStatus get status => $_getN(1);
  @$pb.TagNumber(2)
  set status($3.UploadStatus v) {
    $_setField(2, v);
  }

  @$pb.TagNumber(2)
  $core.bool hasStatus() => $_has(1);
  @$pb.TagNumber(2)
  void clearStatus() => $_clearField(2);

  @$pb.TagNumber(3)
  $core.double get progressPercentage => $_getN(2);
  @$pb.TagNumber(3)
  set progressPercentage($core.double v) {
    $_setFloat(2, v);
  }

  @$pb.TagNumber(3)
  $core.bool hasProgressPercentage() => $_has(2);
  @$pb.TagNumber(3)
  void clearProgressPercentage() => $_clearField(3);

  @$pb.TagNumber(4)
  $core.String get currentStage => $_getSZ(3);
  @$pb.TagNumber(4)
  set currentStage($core.String v) {
    $_setString(3, v);
  }

  @$pb.TagNumber(4)
  $core.bool hasCurrentStage() => $_has(3);
  @$pb.TagNumber(4)
  void clearCurrentStage() => $_clearField(4);

  @$pb.TagNumber(5)
  $core.String get message => $_getSZ(4);
  @$pb.TagNumber(5)
  set message($core.String v) {
    $_setString(4, v);
  }

  @$pb.TagNumber(5)
  $core.bool hasMessage() => $_has(4);
  @$pb.TagNumber(5)
  void clearMessage() => $_clearField(5);

  @$pb.TagNumber(6)
  $0.WhatsAppMetadata get whatsappMetadata => $_getN(5);
  @$pb.TagNumber(6)
  set whatsappMetadata($0.WhatsAppMetadata v) {
    $_setField(6, v);
  }

  @$pb.TagNumber(6)
  $core.bool hasWhatsappMetadata() => $_has(5);
  @$pb.TagNumber(6)
  void clearWhatsappMetadata() => $_clearField(6);
  @$pb.TagNumber(6)
  $0.WhatsAppMetadata ensureWhatsappMetadata() => $_ensure(5);

  @$pb.TagNumber(7)
  ZipMetadata get zipMetadata => $_getN(6);
  @$pb.TagNumber(7)
  set zipMetadata(ZipMetadata v) {
    $_setField(7, v);
  }

  @$pb.TagNumber(7)
  $core.bool hasZipMetadata() => $_has(6);
  @$pb.TagNumber(7)
  void clearZipMetadata() => $_clearField(7);
  @$pb.TagNumber(7)
  ZipMetadata ensureZipMetadata() => $_ensure(6);

  @$pb.TagNumber(8)
  FileMetadata get fileMetadata => $_getN(7);
  @$pb.TagNumber(8)
  set fileMetadata(FileMetadata v) {
    $_setField(8, v);
  }

  @$pb.TagNumber(8)
  $core.bool hasFileMetadata() => $_has(7);
  @$pb.TagNumber(8)
  void clearFileMetadata() => $_clearField(8);
  @$pb.TagNumber(8)
  FileMetadata ensureFileMetadata() => $_ensure(7);

  @$pb.TagNumber(9)
  ArticleMetadata get articleMetadata => $_getN(8);
  @$pb.TagNumber(9)
  set articleMetadata(ArticleMetadata v) {
    $_setField(9, v);
  }

  @$pb.TagNumber(9)
  $core.bool hasArticleMetadata() => $_has(8);
  @$pb.TagNumber(9)
  void clearArticleMetadata() => $_clearField(9);
  @$pb.TagNumber(9)
  ArticleMetadata ensureArticleMetadata() => $_ensure(8);

  @$pb.TagNumber(10)
  YouTubeMetadata get youtubeMetadata => $_getN(9);
  @$pb.TagNumber(10)
  set youtubeMetadata(YouTubeMetadata v) {
    $_setField(10, v);
  }

  @$pb.TagNumber(10)
  $core.bool hasYoutubeMetadata() => $_has(9);
  @$pb.TagNumber(10)
  void clearYoutubeMetadata() => $_clearField(10);
  @$pb.TagNumber(10)
  YouTubeMetadata ensureYoutubeMetadata() => $_ensure(9);
}

/// Metadata for ZIP files
class ZipMetadata extends $pb.GeneratedMessage {
  factory ZipMetadata({
    $core.int? fileCount,
    $core.Iterable<FileInfo>? files,
    $fixnum.Int64? totalSizeBytes,
    $core.String? extractedAt,
    $core.bool? isWhatsappChat,
  }) {
    final $result = create();
    if (fileCount != null) {
      $result.fileCount = fileCount;
    }
    if (files != null) {
      $result.files.addAll(files);
    }
    if (totalSizeBytes != null) {
      $result.totalSizeBytes = totalSizeBytes;
    }
    if (extractedAt != null) {
      $result.extractedAt = extractedAt;
    }
    if (isWhatsappChat != null) {
      $result.isWhatsappChat = isWhatsappChat;
    }
    return $result;
  }
  ZipMetadata._() : super();
  factory ZipMetadata.fromBuffer($core.List<$core.int> i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromBuffer(i, r);
  factory ZipMetadata.fromJson($core.String i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'ZipMetadata',
      package: const $pb.PackageName(_omitMessageNames ? '' : 'promz.api.v1'),
      createEmptyInstance: create)
    ..a<$core.int>(1, _omitFieldNames ? '' : 'fileCount', $pb.PbFieldType.O3)
    ..pc<FileInfo>(2, _omitFieldNames ? '' : 'files', $pb.PbFieldType.PM,
        subBuilder: FileInfo.create)
    ..aInt64(3, _omitFieldNames ? '' : 'totalSizeBytes')
    ..aOS(4, _omitFieldNames ? '' : 'extractedAt')
    ..aOB(5, _omitFieldNames ? '' : 'isWhatsappChat')
    ..hasRequiredFields = false;

  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
      'Will be removed in next major version')
  ZipMetadata clone() => ZipMetadata()..mergeFromMessage(this);
  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
      'Will be removed in next major version')
  ZipMetadata copyWith(void Function(ZipMetadata) updates) =>
      super.copyWith((message) => updates(message as ZipMetadata)) as ZipMetadata;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static ZipMetadata create() => ZipMetadata._();
  ZipMetadata createEmptyInstance() => create();
  static $pb.PbList<ZipMetadata> createRepeated() => $pb.PbList<ZipMetadata>();
  @$core.pragma('dart2js:noInline')
  static ZipMetadata getDefault() =>
      _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ZipMetadata>(create);
  static ZipMetadata? _defaultInstance;

  @$pb.TagNumber(1)
  $core.int get fileCount => $_getIZ(0);
  @$pb.TagNumber(1)
  set fileCount($core.int v) {
    $_setSignedInt32(0, v);
  }

  @$pb.TagNumber(1)
  $core.bool hasFileCount() => $_has(0);
  @$pb.TagNumber(1)
  void clearFileCount() => $_clearField(1);

  @$pb.TagNumber(2)
  $pb.PbList<FileInfo> get files => $_getList(1);

  @$pb.TagNumber(3)
  $fixnum.Int64 get totalSizeBytes => $_getI64(2);
  @$pb.TagNumber(3)
  set totalSizeBytes($fixnum.Int64 v) {
    $_setInt64(2, v);
  }

  @$pb.TagNumber(3)
  $core.bool hasTotalSizeBytes() => $_has(2);
  @$pb.TagNumber(3)
  void clearTotalSizeBytes() => $_clearField(3);

  @$pb.TagNumber(4)
  $core.String get extractedAt => $_getSZ(3);
  @$pb.TagNumber(4)
  set extractedAt($core.String v) {
    $_setString(3, v);
  }

  @$pb.TagNumber(4)
  $core.bool hasExtractedAt() => $_has(3);
  @$pb.TagNumber(4)
  void clearExtractedAt() => $_clearField(4);

  @$pb.TagNumber(5)
  $core.bool get isWhatsappChat => $_getBF(4);
  @$pb.TagNumber(5)
  set isWhatsappChat($core.bool v) {
    $_setBool(4, v);
  }

  @$pb.TagNumber(5)
  $core.bool hasIsWhatsappChat() => $_has(4);
  @$pb.TagNumber(5)
  void clearIsWhatsappChat() => $_clearField(5);
}

/// Metadata for generic files
class FileMetadata extends $pb.GeneratedMessage {
  factory FileMetadata({
    $fixnum.Int64? sizeBytes,
    $core.String? lastModified,
    $core.String? contentHash,
    $core.String? detectedLanguage,
    $core.int? lineCount,
    $core.int? wordCount,
    $core.int? charCount,
    $core.String? author,
    $core.String? title,
    $core.String? creationDate,
  }) {
    final $result = create();
    if (sizeBytes != null) {
      $result.sizeBytes = sizeBytes;
    }
    if (lastModified != null) {
      $result.lastModified = lastModified;
    }
    if (contentHash != null) {
      $result.contentHash = contentHash;
    }
    if (detectedLanguage != null) {
      $result.detectedLanguage = detectedLanguage;
    }
    if (lineCount != null) {
      $result.lineCount = lineCount;
    }
    if (wordCount != null) {
      $result.wordCount = wordCount;
    }
    if (charCount != null) {
      $result.charCount = charCount;
    }
    if (author != null) {
      $result.author = author;
    }
    if (title != null) {
      $result.title = title;
    }
    if (creationDate != null) {
      $result.creationDate = creationDate;
    }
    return $result;
  }
  FileMetadata._() : super();
  factory FileMetadata.fromBuffer($core.List<$core.int> i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromBuffer(i, r);
  factory FileMetadata.fromJson($core.String i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'FileMetadata',
      package: const $pb.PackageName(_omitMessageNames ? '' : 'promz.api.v1'),
      createEmptyInstance: create)
    ..aInt64(1, _omitFieldNames ? '' : 'sizeBytes')
    ..aOS(2, _omitFieldNames ? '' : 'lastModified')
    ..aOS(3, _omitFieldNames ? '' : 'contentHash')
    ..aOS(4, _omitFieldNames ? '' : 'detectedLanguage')
    ..a<$core.int>(5, _omitFieldNames ? '' : 'lineCount', $pb.PbFieldType.O3)
    ..a<$core.int>(6, _omitFieldNames ? '' : 'wordCount', $pb.PbFieldType.O3)
    ..a<$core.int>(7, _omitFieldNames ? '' : 'charCount', $pb.PbFieldType.O3)
    ..aOS(8, _omitFieldNames ? '' : 'author')
    ..aOS(9, _omitFieldNames ? '' : 'title')
    ..aOS(10, _omitFieldNames ? '' : 'creationDate')
    ..hasRequiredFields = false;

  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
      'Will be removed in next major version')
  FileMetadata clone() => FileMetadata()..mergeFromMessage(this);
  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
      'Will be removed in next major version')
  FileMetadata copyWith(void Function(FileMetadata) updates) =>
      super.copyWith((message) => updates(message as FileMetadata)) as FileMetadata;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static FileMetadata create() => FileMetadata._();
  FileMetadata createEmptyInstance() => create();
  static $pb.PbList<FileMetadata> createRepeated() => $pb.PbList<FileMetadata>();
  @$core.pragma('dart2js:noInline')
  static FileMetadata getDefault() =>
      _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<FileMetadata>(create);
  static FileMetadata? _defaultInstance;

  @$pb.TagNumber(1)
  $fixnum.Int64 get sizeBytes => $_getI64(0);
  @$pb.TagNumber(1)
  set sizeBytes($fixnum.Int64 v) {
    $_setInt64(0, v);
  }

  @$pb.TagNumber(1)
  $core.bool hasSizeBytes() => $_has(0);
  @$pb.TagNumber(1)
  void clearSizeBytes() => $_clearField(1);

  @$pb.TagNumber(2)
  $core.String get lastModified => $_getSZ(1);
  @$pb.TagNumber(2)
  set lastModified($core.String v) {
    $_setString(1, v);
  }

  @$pb.TagNumber(2)
  $core.bool hasLastModified() => $_has(1);
  @$pb.TagNumber(2)
  void clearLastModified() => $_clearField(2);

  @$pb.TagNumber(3)
  $core.String get contentHash => $_getSZ(2);
  @$pb.TagNumber(3)
  set contentHash($core.String v) {
    $_setString(2, v);
  }

  @$pb.TagNumber(3)
  $core.bool hasContentHash() => $_has(2);
  @$pb.TagNumber(3)
  void clearContentHash() => $_clearField(3);

  @$pb.TagNumber(4)
  $core.String get detectedLanguage => $_getSZ(3);
  @$pb.TagNumber(4)
  set detectedLanguage($core.String v) {
    $_setString(3, v);
  }

  @$pb.TagNumber(4)
  $core.bool hasDetectedLanguage() => $_has(3);
  @$pb.TagNumber(4)
  void clearDetectedLanguage() => $_clearField(4);

  @$pb.TagNumber(5)
  $core.int get lineCount => $_getIZ(4);
  @$pb.TagNumber(5)
  set lineCount($core.int v) {
    $_setSignedInt32(4, v);
  }

  @$pb.TagNumber(5)
  $core.bool hasLineCount() => $_has(4);
  @$pb.TagNumber(5)
  void clearLineCount() => $_clearField(5);

  @$pb.TagNumber(6)
  $core.int get wordCount => $_getIZ(5);
  @$pb.TagNumber(6)
  set wordCount($core.int v) {
    $_setSignedInt32(5, v);
  }

  @$pb.TagNumber(6)
  $core.bool hasWordCount() => $_has(5);
  @$pb.TagNumber(6)
  void clearWordCount() => $_clearField(6);

  @$pb.TagNumber(7)
  $core.int get charCount => $_getIZ(6);
  @$pb.TagNumber(7)
  set charCount($core.int v) {
    $_setSignedInt32(6, v);
  }

  @$pb.TagNumber(7)
  $core.bool hasCharCount() => $_has(6);
  @$pb.TagNumber(7)
  void clearCharCount() => $_clearField(7);

  @$pb.TagNumber(8)
  $core.String get author => $_getSZ(7);
  @$pb.TagNumber(8)
  set author($core.String v) {
    $_setString(7, v);
  }

  @$pb.TagNumber(8)
  $core.bool hasAuthor() => $_has(7);
  @$pb.TagNumber(8)
  void clearAuthor() => $_clearField(8);

  @$pb.TagNumber(9)
  $core.String get title => $_getSZ(8);
  @$pb.TagNumber(9)
  set title($core.String v) {
    $_setString(8, v);
  }

  @$pb.TagNumber(9)
  $core.bool hasTitle() => $_has(8);
  @$pb.TagNumber(9)
  void clearTitle() => $_clearField(9);

  @$pb.TagNumber(10)
  $core.String get creationDate => $_getSZ(9);
  @$pb.TagNumber(10)
  set creationDate($core.String v) {
    $_setString(9, v);
  }

  @$pb.TagNumber(10)
  $core.bool hasCreationDate() => $_has(9);
  @$pb.TagNumber(10)
  void clearCreationDate() => $_clearField(10);
}

/// Information about a file within a ZIP archive
class FileInfo extends $pb.GeneratedMessage {
  factory FileInfo({
    $core.String? name,
    $core.String? path,
    $fixnum.Int64? sizeBytes,
    $core.String? mimeType,
    $core.bool? isText,
    $core.String? lastModified,
  }) {
    final $result = create();
    if (name != null) {
      $result.name = name;
    }
    if (path != null) {
      $result.path = path;
    }
    if (sizeBytes != null) {
      $result.sizeBytes = sizeBytes;
    }
    if (mimeType != null) {
      $result.mimeType = mimeType;
    }
    if (isText != null) {
      $result.isText = isText;
    }
    if (lastModified != null) {
      $result.lastModified = lastModified;
    }
    return $result;
  }
  FileInfo._() : super();
  factory FileInfo.fromBuffer($core.List<$core.int> i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromBuffer(i, r);
  factory FileInfo.fromJson($core.String i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'FileInfo',
      package: const $pb.PackageName(_omitMessageNames ? '' : 'promz.api.v1'),
      createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'name')
    ..aOS(2, _omitFieldNames ? '' : 'path')
    ..aInt64(3, _omitFieldNames ? '' : 'sizeBytes')
    ..aOS(4, _omitFieldNames ? '' : 'mimeType')
    ..aOB(5, _omitFieldNames ? '' : 'isText')
    ..aOS(6, _omitFieldNames ? '' : 'lastModified')
    ..hasRequiredFields = false;

  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
      'Will be removed in next major version')
  FileInfo clone() => FileInfo()..mergeFromMessage(this);
  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
      'Will be removed in next major version')
  FileInfo copyWith(void Function(FileInfo) updates) =>
      super.copyWith((message) => updates(message as FileInfo)) as FileInfo;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static FileInfo create() => FileInfo._();
  FileInfo createEmptyInstance() => create();
  static $pb.PbList<FileInfo> createRepeated() => $pb.PbList<FileInfo>();
  @$core.pragma('dart2js:noInline')
  static FileInfo getDefault() =>
      _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<FileInfo>(create);
  static FileInfo? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get name => $_getSZ(0);
  @$pb.TagNumber(1)
  set name($core.String v) {
    $_setString(0, v);
  }

  @$pb.TagNumber(1)
  $core.bool hasName() => $_has(0);
  @$pb.TagNumber(1)
  void clearName() => $_clearField(1);

  @$pb.TagNumber(2)
  $core.String get path => $_getSZ(1);
  @$pb.TagNumber(2)
  set path($core.String v) {
    $_setString(1, v);
  }

  @$pb.TagNumber(2)
  $core.bool hasPath() => $_has(1);
  @$pb.TagNumber(2)
  void clearPath() => $_clearField(2);

  @$pb.TagNumber(3)
  $fixnum.Int64 get sizeBytes => $_getI64(2);
  @$pb.TagNumber(3)
  set sizeBytes($fixnum.Int64 v) {
    $_setInt64(2, v);
  }

  @$pb.TagNumber(3)
  $core.bool hasSizeBytes() => $_has(2);
  @$pb.TagNumber(3)
  void clearSizeBytes() => $_clearField(3);

  @$pb.TagNumber(4)
  $core.String get mimeType => $_getSZ(3);
  @$pb.TagNumber(4)
  set mimeType($core.String v) {
    $_setString(3, v);
  }

  @$pb.TagNumber(4)
  $core.bool hasMimeType() => $_has(3);
  @$pb.TagNumber(4)
  void clearMimeType() => $_clearField(4);

  @$pb.TagNumber(5)
  $core.bool get isText => $_getBF(4);
  @$pb.TagNumber(5)
  set isText($core.bool v) {
    $_setBool(4, v);
  }

  @$pb.TagNumber(5)
  $core.bool hasIsText() => $_has(4);
  @$pb.TagNumber(5)
  void clearIsText() => $_clearField(5);

  @$pb.TagNumber(6)
  $core.String get lastModified => $_getSZ(5);
  @$pb.TagNumber(6)
  set lastModified($core.String v) {
    $_setString(5, v);
  }

  @$pb.TagNumber(6)
  $core.bool hasLastModified() => $_has(5);
  @$pb.TagNumber(6)
  void clearLastModified() => $_clearField(6);
}

/// Metadata for news articles
class ArticleMetadata extends $pb.GeneratedMessage {
  factory ArticleMetadata({
    $core.String? url,
    $core.String? finalUrl,
    $core.String? title,
    $core.String? excerpt,
    $core.String? siteName,
    $core.String? author,
    $core.String? publishDate,
    $core.String? language,
    $core.String? imageUrl,
    $core.String? htmlContent,
  }) {
    final $result = create();
    if (url != null) {
      $result.url = url;
    }
    if (finalUrl != null) {
      $result.finalUrl = finalUrl;
    }
    if (title != null) {
      $result.title = title;
    }
    if (excerpt != null) {
      $result.excerpt = excerpt;
    }
    if (siteName != null) {
      $result.siteName = siteName;
    }
    if (author != null) {
      $result.author = author;
    }
    if (publishDate != null) {
      $result.publishDate = publishDate;
    }
    if (language != null) {
      $result.language = language;
    }
    if (imageUrl != null) {
      $result.imageUrl = imageUrl;
    }
    if (htmlContent != null) {
      $result.htmlContent = htmlContent;
    }
    return $result;
  }
  ArticleMetadata._() : super();
  factory ArticleMetadata.fromBuffer($core.List<$core.int> i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromBuffer(i, r);
  factory ArticleMetadata.fromJson($core.String i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'ArticleMetadata',
      package: const $pb.PackageName(_omitMessageNames ? '' : 'promz.api.v1'),
      createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'url')
    ..aOS(2, _omitFieldNames ? '' : 'finalUrl')
    ..aOS(3, _omitFieldNames ? '' : 'title')
    ..aOS(4, _omitFieldNames ? '' : 'excerpt')
    ..aOS(5, _omitFieldNames ? '' : 'siteName')
    ..aOS(6, _omitFieldNames ? '' : 'author')
    ..aOS(7, _omitFieldNames ? '' : 'publishDate')
    ..aOS(8, _omitFieldNames ? '' : 'language')
    ..aOS(9, _omitFieldNames ? '' : 'imageUrl')
    ..aOS(10, _omitFieldNames ? '' : 'htmlContent')
    ..hasRequiredFields = false;

  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
      'Will be removed in next major version')
  ArticleMetadata clone() => ArticleMetadata()..mergeFromMessage(this);
  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
      'Will be removed in next major version')
  ArticleMetadata copyWith(void Function(ArticleMetadata) updates) =>
      super.copyWith((message) => updates(message as ArticleMetadata)) as ArticleMetadata;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static ArticleMetadata create() => ArticleMetadata._();
  ArticleMetadata createEmptyInstance() => create();
  static $pb.PbList<ArticleMetadata> createRepeated() => $pb.PbList<ArticleMetadata>();
  @$core.pragma('dart2js:noInline')
  static ArticleMetadata getDefault() =>
      _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ArticleMetadata>(create);
  static ArticleMetadata? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get url => $_getSZ(0);
  @$pb.TagNumber(1)
  set url($core.String v) {
    $_setString(0, v);
  }

  @$pb.TagNumber(1)
  $core.bool hasUrl() => $_has(0);
  @$pb.TagNumber(1)
  void clearUrl() => $_clearField(1);

  @$pb.TagNumber(2)
  $core.String get finalUrl => $_getSZ(1);
  @$pb.TagNumber(2)
  set finalUrl($core.String v) {
    $_setString(1, v);
  }

  @$pb.TagNumber(2)
  $core.bool hasFinalUrl() => $_has(1);
  @$pb.TagNumber(2)
  void clearFinalUrl() => $_clearField(2);

  @$pb.TagNumber(3)
  $core.String get title => $_getSZ(2);
  @$pb.TagNumber(3)
  set title($core.String v) {
    $_setString(2, v);
  }

  @$pb.TagNumber(3)
  $core.bool hasTitle() => $_has(2);
  @$pb.TagNumber(3)
  void clearTitle() => $_clearField(3);

  @$pb.TagNumber(4)
  $core.String get excerpt => $_getSZ(3);
  @$pb.TagNumber(4)
  set excerpt($core.String v) {
    $_setString(3, v);
  }

  @$pb.TagNumber(4)
  $core.bool hasExcerpt() => $_has(3);
  @$pb.TagNumber(4)
  void clearExcerpt() => $_clearField(4);

  @$pb.TagNumber(5)
  $core.String get siteName => $_getSZ(4);
  @$pb.TagNumber(5)
  set siteName($core.String v) {
    $_setString(4, v);
  }

  @$pb.TagNumber(5)
  $core.bool hasSiteName() => $_has(4);
  @$pb.TagNumber(5)
  void clearSiteName() => $_clearField(5);

  @$pb.TagNumber(6)
  $core.String get author => $_getSZ(5);
  @$pb.TagNumber(6)
  set author($core.String v) {
    $_setString(5, v);
  }

  @$pb.TagNumber(6)
  $core.bool hasAuthor() => $_has(5);
  @$pb.TagNumber(6)
  void clearAuthor() => $_clearField(6);

  @$pb.TagNumber(7)
  $core.String get publishDate => $_getSZ(6);
  @$pb.TagNumber(7)
  set publishDate($core.String v) {
    $_setString(6, v);
  }

  @$pb.TagNumber(7)
  $core.bool hasPublishDate() => $_has(6);
  @$pb.TagNumber(7)
  void clearPublishDate() => $_clearField(7);

  @$pb.TagNumber(8)
  $core.String get language => $_getSZ(7);
  @$pb.TagNumber(8)
  set language($core.String v) {
    $_setString(7, v);
  }

  @$pb.TagNumber(8)
  $core.bool hasLanguage() => $_has(7);
  @$pb.TagNumber(8)
  void clearLanguage() => $_clearField(8);

  @$pb.TagNumber(9)
  $core.String get imageUrl => $_getSZ(8);
  @$pb.TagNumber(9)
  set imageUrl($core.String v) {
    $_setString(8, v);
  }

  @$pb.TagNumber(9)
  $core.bool hasImageUrl() => $_has(8);
  @$pb.TagNumber(9)
  void clearImageUrl() => $_clearField(9);

  @$pb.TagNumber(10)
  $core.String get htmlContent => $_getSZ(9);
  @$pb.TagNumber(10)
  set htmlContent($core.String v) {
    $_setString(9, v);
  }

  @$pb.TagNumber(10)
  $core.bool hasHtmlContent() => $_has(9);
  @$pb.TagNumber(10)
  void clearHtmlContent() => $_clearField(10);
}

/// Metadata for YouTube videos
class YouTubeMetadata extends $pb.GeneratedMessage {
  factory YouTubeMetadata({
    $core.String? url,
    $core.String? videoId,
    $core.String? title,
    $core.String? description,
    $core.String? thumbnailUrl,
    $core.String? publishDate,
    $core.String? language,
    $core.String? duration,
    $core.String? channelId,
    $core.String? channelName,
    $core.String? category,
    $core.String? tags,
    $core.String? viewCount,
    $core.String? likeCount,
    $core.String? dislikeCount,
    $core.String? commentCount,
    $core.String? imageUrl,
  }) {
    final $result = create();
    if (url != null) {
      $result.url = url;
    }
    if (videoId != null) {
      $result.videoId = videoId;
    }
    if (title != null) {
      $result.title = title;
    }
    if (description != null) {
      $result.description = description;
    }
    if (thumbnailUrl != null) {
      $result.thumbnailUrl = thumbnailUrl;
    }
    if (publishDate != null) {
      $result.publishDate = publishDate;
    }
    if (language != null) {
      $result.language = language;
    }
    if (duration != null) {
      $result.duration = duration;
    }
    if (channelId != null) {
      $result.channelId = channelId;
    }
    if (channelName != null) {
      $result.channelName = channelName;
    }
    if (category != null) {
      $result.category = category;
    }
    if (tags != null) {
      $result.tags = tags;
    }
    if (viewCount != null) {
      $result.viewCount = viewCount;
    }
    if (likeCount != null) {
      $result.likeCount = likeCount;
    }
    if (dislikeCount != null) {
      $result.dislikeCount = dislikeCount;
    }
    if (commentCount != null) {
      $result.commentCount = commentCount;
    }
    if (imageUrl != null) {
      $result.imageUrl = imageUrl;
    }
    return $result;
  }
  YouTubeMetadata._() : super();
  factory YouTubeMetadata.fromBuffer($core.List<$core.int> i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromBuffer(i, r);
  factory YouTubeMetadata.fromJson($core.String i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'YouTubeMetadata',
      package: const $pb.PackageName(_omitMessageNames ? '' : 'promz.api.v1'),
      createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'url')
    ..aOS(2, _omitFieldNames ? '' : 'videoId')
    ..aOS(3, _omitFieldNames ? '' : 'title')
    ..aOS(4, _omitFieldNames ? '' : 'description')
    ..aOS(5, _omitFieldNames ? '' : 'thumbnailUrl')
    ..aOS(6, _omitFieldNames ? '' : 'publishDate')
    ..aOS(7, _omitFieldNames ? '' : 'language')
    ..aOS(8, _omitFieldNames ? '' : 'duration')
    ..aOS(9, _omitFieldNames ? '' : 'channelId')
    ..aOS(10, _omitFieldNames ? '' : 'channelName')
    ..aOS(11, _omitFieldNames ? '' : 'category')
    ..aOS(12, _omitFieldNames ? '' : 'tags')
    ..aOS(13, _omitFieldNames ? '' : 'viewCount')
    ..aOS(14, _omitFieldNames ? '' : 'likeCount')
    ..aOS(15, _omitFieldNames ? '' : 'dislikeCount')
    ..aOS(16, _omitFieldNames ? '' : 'commentCount')
    ..aOS(17, _omitFieldNames ? '' : 'imageUrl')
    ..hasRequiredFields = false;

  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
      'Will be removed in next major version')
  YouTubeMetadata clone() => YouTubeMetadata()..mergeFromMessage(this);
  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
      'Will be removed in next major version')
  YouTubeMetadata copyWith(void Function(YouTubeMetadata) updates) =>
      super.copyWith((message) => updates(message as YouTubeMetadata)) as YouTubeMetadata;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static YouTubeMetadata create() => YouTubeMetadata._();
  YouTubeMetadata createEmptyInstance() => create();
  static $pb.PbList<YouTubeMetadata> createRepeated() => $pb.PbList<YouTubeMetadata>();
  @$core.pragma('dart2js:noInline')
  static YouTubeMetadata getDefault() =>
      _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<YouTubeMetadata>(create);
  static YouTubeMetadata? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get url => $_getSZ(0);
  @$pb.TagNumber(1)
  set url($core.String v) {
    $_setString(0, v);
  }

  @$pb.TagNumber(1)
  $core.bool hasUrl() => $_has(0);
  @$pb.TagNumber(1)
  void clearUrl() => $_clearField(1);

  @$pb.TagNumber(2)
  $core.String get videoId => $_getSZ(1);
  @$pb.TagNumber(2)
  set videoId($core.String v) {
    $_setString(1, v);
  }

  @$pb.TagNumber(2)
  $core.bool hasVideoId() => $_has(1);
  @$pb.TagNumber(2)
  void clearVideoId() => $_clearField(2);

  @$pb.TagNumber(3)
  $core.String get title => $_getSZ(2);
  @$pb.TagNumber(3)
  set title($core.String v) {
    $_setString(2, v);
  }

  @$pb.TagNumber(3)
  $core.bool hasTitle() => $_has(2);
  @$pb.TagNumber(3)
  void clearTitle() => $_clearField(3);

  @$pb.TagNumber(4)
  $core.String get description => $_getSZ(3);
  @$pb.TagNumber(4)
  set description($core.String v) {
    $_setString(3, v);
  }

  @$pb.TagNumber(4)
  $core.bool hasDescription() => $_has(3);
  @$pb.TagNumber(4)
  void clearDescription() => $_clearField(4);

  @$pb.TagNumber(5)
  $core.String get thumbnailUrl => $_getSZ(4);
  @$pb.TagNumber(5)
  set thumbnailUrl($core.String v) {
    $_setString(4, v);
  }

  @$pb.TagNumber(5)
  $core.bool hasThumbnailUrl() => $_has(4);
  @$pb.TagNumber(5)
  void clearThumbnailUrl() => $_clearField(5);

  @$pb.TagNumber(6)
  $core.String get publishDate => $_getSZ(5);
  @$pb.TagNumber(6)
  set publishDate($core.String v) {
    $_setString(5, v);
  }

  @$pb.TagNumber(6)
  $core.bool hasPublishDate() => $_has(5);
  @$pb.TagNumber(6)
  void clearPublishDate() => $_clearField(6);

  @$pb.TagNumber(7)
  $core.String get language => $_getSZ(6);
  @$pb.TagNumber(7)
  set language($core.String v) {
    $_setString(6, v);
  }

  @$pb.TagNumber(7)
  $core.bool hasLanguage() => $_has(6);
  @$pb.TagNumber(7)
  void clearLanguage() => $_clearField(7);

  @$pb.TagNumber(8)
  $core.String get duration => $_getSZ(7);
  @$pb.TagNumber(8)
  set duration($core.String v) {
    $_setString(7, v);
  }

  @$pb.TagNumber(8)
  $core.bool hasDuration() => $_has(7);
  @$pb.TagNumber(8)
  void clearDuration() => $_clearField(8);

  @$pb.TagNumber(9)
  $core.String get channelId => $_getSZ(8);
  @$pb.TagNumber(9)
  set channelId($core.String v) {
    $_setString(8, v);
  }

  @$pb.TagNumber(9)
  $core.bool hasChannelId() => $_has(8);
  @$pb.TagNumber(9)
  void clearChannelId() => $_clearField(9);

  @$pb.TagNumber(10)
  $core.String get channelName => $_getSZ(9);
  @$pb.TagNumber(10)
  set channelName($core.String v) {
    $_setString(9, v);
  }

  @$pb.TagNumber(10)
  $core.bool hasChannelName() => $_has(9);
  @$pb.TagNumber(10)
  void clearChannelName() => $_clearField(10);

  @$pb.TagNumber(11)
  $core.String get category => $_getSZ(10);
  @$pb.TagNumber(11)
  set category($core.String v) {
    $_setString(10, v);
  }

  @$pb.TagNumber(11)
  $core.bool hasCategory() => $_has(10);
  @$pb.TagNumber(11)
  void clearCategory() => $_clearField(11);

  @$pb.TagNumber(12)
  $core.String get tags => $_getSZ(11);
  @$pb.TagNumber(12)
  set tags($core.String v) {
    $_setString(11, v);
  }

  @$pb.TagNumber(12)
  $core.bool hasTags() => $_has(11);
  @$pb.TagNumber(12)
  void clearTags() => $_clearField(12);

  @$pb.TagNumber(13)
  $core.String get viewCount => $_getSZ(12);
  @$pb.TagNumber(13)
  set viewCount($core.String v) {
    $_setString(12, v);
  }

  @$pb.TagNumber(13)
  $core.bool hasViewCount() => $_has(12);
  @$pb.TagNumber(13)
  void clearViewCount() => $_clearField(13);

  @$pb.TagNumber(14)
  $core.String get likeCount => $_getSZ(13);
  @$pb.TagNumber(14)
  set likeCount($core.String v) {
    $_setString(13, v);
  }

  @$pb.TagNumber(14)
  $core.bool hasLikeCount() => $_has(13);
  @$pb.TagNumber(14)
  void clearLikeCount() => $_clearField(14);

  @$pb.TagNumber(15)
  $core.String get dislikeCount => $_getSZ(14);
  @$pb.TagNumber(15)
  set dislikeCount($core.String v) {
    $_setString(14, v);
  }

  @$pb.TagNumber(15)
  $core.bool hasDislikeCount() => $_has(14);
  @$pb.TagNumber(15)
  void clearDislikeCount() => $_clearField(15);

  @$pb.TagNumber(16)
  $core.String get commentCount => $_getSZ(15);
  @$pb.TagNumber(16)
  set commentCount($core.String v) {
    $_setString(15, v);
  }

  @$pb.TagNumber(16)
  $core.bool hasCommentCount() => $_has(15);
  @$pb.TagNumber(16)
  void clearCommentCount() => $_clearField(16);

  @$pb.TagNumber(17)
  $core.String get imageUrl => $_getSZ(16);
  @$pb.TagNumber(17)
  set imageUrl($core.String v) {
    $_setString(16, v);
  }

  @$pb.TagNumber(17)
  $core.bool hasImageUrl() => $_has(16);
  @$pb.TagNumber(17)
  void clearImageUrl() => $_clearField(17);
}

const _omitFieldNames = $core.bool.fromEnvironment('protobuf.omit_field_names');
const _omitMessageNames = $core.bool.fromEnvironment('protobuf.omit_message_names');
