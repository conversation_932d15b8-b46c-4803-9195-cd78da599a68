//
//  Generated code. Do not modify.
//  source: content_upload.proto
//
// @dart = 3.3

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:async' as $async;
import 'dart:core' as $core;

import 'package:grpc/service_api.dart' as $grpc;
import 'package:protobuf/protobuf.dart' as $pb;

import 'content_upload.pb.dart' as $1;

export 'content_upload.pb.dart';

@$pb.GrpcServiceName('promz.api.v1.ContentUploadService')
class ContentUploadServiceClient extends $grpc.Client {
  static final _$uploadFile = $grpc.ClientMethod<$1.UploadFileRequest, $1.ProcessingResult>(
      '/promz.api.v1.ContentUploadService/UploadFile',
      ($1.UploadFileRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) => $1.ProcessingResult.fromBuffer(value));
  static final _$getUploadStatus = $grpc.ClientMethod<$1.UploadStatusRequest, $1.ProcessingResult>(
      '/promz.api.v1.ContentUploadService/GetUploadStatus',
      ($1.UploadStatusRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) => $1.ProcessingResult.fromBuffer(value));
  static final _$streamUploadUpdates = $grpc.ClientMethod<$1.UploadStatusRequest, $1.UploadUpdate>(
      '/promz.api.v1.ContentUploadService/StreamUploadUpdates',
      ($1.UploadStatusRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) => $1.UploadUpdate.fromBuffer(value));

  ContentUploadServiceClient($grpc.ClientChannel channel,
      {$grpc.CallOptions? options, $core.Iterable<$grpc.ClientInterceptor>? interceptors})
      : super(channel, options: options, interceptors: interceptors);

  $grpc.ResponseFuture<$1.ProcessingResult> uploadFile($1.UploadFileRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$uploadFile, request, options: options);
  }

  $grpc.ResponseFuture<$1.ProcessingResult> getUploadStatus($1.UploadStatusRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getUploadStatus, request, options: options);
  }

  $grpc.ResponseStream<$1.UploadUpdate> streamUploadUpdates($1.UploadStatusRequest request,
      {$grpc.CallOptions? options}) {
    return $createStreamingCall(_$streamUploadUpdates, $async.Stream.fromIterable([request]),
        options: options);
  }
}

@$pb.GrpcServiceName('promz.api.v1.ContentUploadService')
abstract class ContentUploadServiceBase extends $grpc.Service {
  $core.String get $name => 'promz.api.v1.ContentUploadService';

  ContentUploadServiceBase() {
    $addMethod($grpc.ServiceMethod<$1.UploadFileRequest, $1.ProcessingResult>(
        'UploadFile',
        uploadFile_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $1.UploadFileRequest.fromBuffer(value),
        ($1.ProcessingResult value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$1.UploadStatusRequest, $1.ProcessingResult>(
        'GetUploadStatus',
        getUploadStatus_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $1.UploadStatusRequest.fromBuffer(value),
        ($1.ProcessingResult value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$1.UploadStatusRequest, $1.UploadUpdate>(
        'StreamUploadUpdates',
        streamUploadUpdates_Pre,
        false,
        true,
        ($core.List<$core.int> value) => $1.UploadStatusRequest.fromBuffer(value),
        ($1.UploadUpdate value) => value.writeToBuffer()));
  }

  $async.Future<$1.ProcessingResult> uploadFile_Pre(
      $grpc.ServiceCall $call, $async.Future<$1.UploadFileRequest> $request) async {
    return uploadFile($call, await $request);
  }

  $async.Future<$1.ProcessingResult> getUploadStatus_Pre(
      $grpc.ServiceCall $call, $async.Future<$1.UploadStatusRequest> $request) async {
    return getUploadStatus($call, await $request);
  }

  $async.Stream<$1.UploadUpdate> streamUploadUpdates_Pre(
      $grpc.ServiceCall $call, $async.Future<$1.UploadStatusRequest> $request) async* {
    yield* streamUploadUpdates($call, await $request);
  }

  $async.Future<$1.ProcessingResult> uploadFile(
      $grpc.ServiceCall call, $1.UploadFileRequest request);
  $async.Future<$1.ProcessingResult> getUploadStatus(
      $grpc.ServiceCall call, $1.UploadStatusRequest request);
  $async.Stream<$1.UploadUpdate> streamUploadUpdates(
      $grpc.ServiceCall call, $1.UploadStatusRequest request);
}
