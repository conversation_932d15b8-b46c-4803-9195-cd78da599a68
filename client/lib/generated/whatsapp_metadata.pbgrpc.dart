//
//  Generated code. Do not modify.
//  source: whatsapp_metadata.proto
//
// @dart = 3.3

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:async' as $async;
import 'dart:core' as $core;

import 'package:grpc/service_api.dart' as $grpc;
import 'package:protobuf/protobuf.dart' as $pb;

import 'whatsapp_metadata.pb.dart' as $0;

export 'whatsapp_metadata.pb.dart';

@$pb.GrpcServiceName('promz.api.v1.WhatsAppProcessingService')
class WhatsAppProcessingServiceClient extends $grpc.Client {
  static final _$detectWhatsAppContent =
      $grpc.ClientMethod<$0.DetectWhatsAppRequest, $0.DetectWhatsAppResponse>(
          '/promz.api.v1.WhatsAppProcessingService/DetectWhatsAppContent',
          ($0.DetectWhatsAppRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) => $0.DetectWhatsAppResponse.fromBuffer(value));
  static final _$processWhatsAppContent =
      $grpc.ClientMethod<$0.ProcessWhatsAppRequest, $0.ProcessWhatsAppResponse>(
          '/promz.api.v1.WhatsAppProcessingService/ProcessWhatsAppContent',
          ($0.ProcessWhatsAppRequest value) => value.writeToBuffer(),
          ($core.List<$core.int> value) => $0.ProcessWhatsAppResponse.fromBuffer(value));

  WhatsAppProcessingServiceClient($grpc.ClientChannel channel,
      {$grpc.CallOptions? options, $core.Iterable<$grpc.ClientInterceptor>? interceptors})
      : super(channel, options: options, interceptors: interceptors);

  $grpc.ResponseFuture<$0.DetectWhatsAppResponse> detectWhatsAppContent(
      $0.DetectWhatsAppRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$detectWhatsAppContent, request, options: options);
  }

  $grpc.ResponseFuture<$0.ProcessWhatsAppResponse> processWhatsAppContent(
      $0.ProcessWhatsAppRequest request,
      {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$processWhatsAppContent, request, options: options);
  }
}

@$pb.GrpcServiceName('promz.api.v1.WhatsAppProcessingService')
abstract class WhatsAppProcessingServiceBase extends $grpc.Service {
  $core.String get $name => 'promz.api.v1.WhatsAppProcessingService';

  WhatsAppProcessingServiceBase() {
    $addMethod($grpc.ServiceMethod<$0.DetectWhatsAppRequest, $0.DetectWhatsAppResponse>(
        'DetectWhatsAppContent',
        detectWhatsAppContent_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $0.DetectWhatsAppRequest.fromBuffer(value),
        ($0.DetectWhatsAppResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$0.ProcessWhatsAppRequest, $0.ProcessWhatsAppResponse>(
        'ProcessWhatsAppContent',
        processWhatsAppContent_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $0.ProcessWhatsAppRequest.fromBuffer(value),
        ($0.ProcessWhatsAppResponse value) => value.writeToBuffer()));
  }

  $async.Future<$0.DetectWhatsAppResponse> detectWhatsAppContent_Pre(
      $grpc.ServiceCall $call, $async.Future<$0.DetectWhatsAppRequest> $request) async {
    return detectWhatsAppContent($call, await $request);
  }

  $async.Future<$0.ProcessWhatsAppResponse> processWhatsAppContent_Pre(
      $grpc.ServiceCall $call, $async.Future<$0.ProcessWhatsAppRequest> $request) async {
    return processWhatsAppContent($call, await $request);
  }

  $async.Future<$0.DetectWhatsAppResponse> detectWhatsAppContent(
      $grpc.ServiceCall call, $0.DetectWhatsAppRequest request);
  $async.Future<$0.ProcessWhatsAppResponse> processWhatsAppContent(
      $grpc.ServiceCall call, $0.ProcessWhatsAppRequest request);
}
