//
//  Generated code. Do not modify.
//  source: whatsapp_metadata.proto
//
// @dart = 3.3

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:core' as $core;

import 'package:fixnum/fixnum.dart' as $fixnum;
import 'package:protobuf/protobuf.dart' as $pb;

export 'package:protobuf/protobuf.dart' show GeneratedMessageGenericExtensions;

enum DetectWhatsAppRequest_ContentSource { content, contentId, notSet }

/// Request to detect WhatsApp content
class DetectWhatsAppRequest extends $pb.GeneratedMessage {
  factory DetectWhatsAppRequest({
    $core.String? content,
    $core.String? contentId,
    $core.String? fileName,
  }) {
    final $result = create();
    if (content != null) {
      $result.content = content;
    }
    if (contentId != null) {
      $result.contentId = contentId;
    }
    if (fileName != null) {
      $result.fileName = fileName;
    }
    return $result;
  }
  DetectWhatsAppRequest._() : super();
  factory DetectWhatsAppRequest.fromBuffer($core.List<$core.int> i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromBuffer(i, r);
  factory DetectWhatsAppRequest.fromJson($core.String i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromJson(i, r);

  static const $core.Map<$core.int, DetectWhatsAppRequest_ContentSource>
      _DetectWhatsAppRequest_ContentSourceByTag = {
    1: DetectWhatsAppRequest_ContentSource.content,
    2: DetectWhatsAppRequest_ContentSource.contentId,
    0: DetectWhatsAppRequest_ContentSource.notSet
  };
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(
      _omitMessageNames ? '' : 'DetectWhatsAppRequest',
      package: const $pb.PackageName(_omitMessageNames ? '' : 'promz.api.v1'),
      createEmptyInstance: create)
    ..oo(0, [1, 2])
    ..aOS(1, _omitFieldNames ? '' : 'content')
    ..aOS(2, _omitFieldNames ? '' : 'contentId')
    ..aOS(3, _omitFieldNames ? '' : 'fileName')
    ..hasRequiredFields = false;

  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
      'Will be removed in next major version')
  DetectWhatsAppRequest clone() => DetectWhatsAppRequest()..mergeFromMessage(this);
  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
      'Will be removed in next major version')
  DetectWhatsAppRequest copyWith(void Function(DetectWhatsAppRequest) updates) =>
      super.copyWith((message) => updates(message as DetectWhatsAppRequest))
          as DetectWhatsAppRequest;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static DetectWhatsAppRequest create() => DetectWhatsAppRequest._();
  DetectWhatsAppRequest createEmptyInstance() => create();
  static $pb.PbList<DetectWhatsAppRequest> createRepeated() => $pb.PbList<DetectWhatsAppRequest>();
  @$core.pragma('dart2js:noInline')
  static DetectWhatsAppRequest getDefault() =>
      _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<DetectWhatsAppRequest>(create);
  static DetectWhatsAppRequest? _defaultInstance;

  DetectWhatsAppRequest_ContentSource whichContentSource() =>
      _DetectWhatsAppRequest_ContentSourceByTag[$_whichOneof(0)]!;
  void clearContentSource() => $_clearField($_whichOneof(0));

  @$pb.TagNumber(1)
  $core.String get content => $_getSZ(0);
  @$pb.TagNumber(1)
  set content($core.String v) {
    $_setString(0, v);
  }

  @$pb.TagNumber(1)
  $core.bool hasContent() => $_has(0);
  @$pb.TagNumber(1)
  void clearContent() => $_clearField(1);

  @$pb.TagNumber(2)
  $core.String get contentId => $_getSZ(1);
  @$pb.TagNumber(2)
  set contentId($core.String v) {
    $_setString(1, v);
  }

  @$pb.TagNumber(2)
  $core.bool hasContentId() => $_has(1);
  @$pb.TagNumber(2)
  void clearContentId() => $_clearField(2);

  @$pb.TagNumber(3)
  $core.String get fileName => $_getSZ(2);
  @$pb.TagNumber(3)
  set fileName($core.String v) {
    $_setString(2, v);
  }

  @$pb.TagNumber(3)
  $core.bool hasFileName() => $_has(2);
  @$pb.TagNumber(3)
  void clearFileName() => $_clearField(3);
}

/// Response with WhatsApp detection results
class DetectWhatsAppResponse extends $pb.GeneratedMessage {
  factory DetectWhatsAppResponse({
    $core.bool? isWhatsappChat,
    $core.double? confidenceScore,
    WhatsAppMetadata? metadata,
  }) {
    final $result = create();
    if (isWhatsappChat != null) {
      $result.isWhatsappChat = isWhatsappChat;
    }
    if (confidenceScore != null) {
      $result.confidenceScore = confidenceScore;
    }
    if (metadata != null) {
      $result.metadata = metadata;
    }
    return $result;
  }
  DetectWhatsAppResponse._() : super();
  factory DetectWhatsAppResponse.fromBuffer($core.List<$core.int> i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromBuffer(i, r);
  factory DetectWhatsAppResponse.fromJson($core.String i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(
      _omitMessageNames ? '' : 'DetectWhatsAppResponse',
      package: const $pb.PackageName(_omitMessageNames ? '' : 'promz.api.v1'),
      createEmptyInstance: create)
    ..aOB(1, _omitFieldNames ? '' : 'isWhatsappChat')
    ..a<$core.double>(2, _omitFieldNames ? '' : 'confidenceScore', $pb.PbFieldType.OF)
    ..aOM<WhatsAppMetadata>(3, _omitFieldNames ? '' : 'metadata',
        subBuilder: WhatsAppMetadata.create)
    ..hasRequiredFields = false;

  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
      'Will be removed in next major version')
  DetectWhatsAppResponse clone() => DetectWhatsAppResponse()..mergeFromMessage(this);
  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
      'Will be removed in next major version')
  DetectWhatsAppResponse copyWith(void Function(DetectWhatsAppResponse) updates) =>
      super.copyWith((message) => updates(message as DetectWhatsAppResponse))
          as DetectWhatsAppResponse;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static DetectWhatsAppResponse create() => DetectWhatsAppResponse._();
  DetectWhatsAppResponse createEmptyInstance() => create();
  static $pb.PbList<DetectWhatsAppResponse> createRepeated() =>
      $pb.PbList<DetectWhatsAppResponse>();
  @$core.pragma('dart2js:noInline')
  static DetectWhatsAppResponse getDefault() =>
      _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<DetectWhatsAppResponse>(create);
  static DetectWhatsAppResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.bool get isWhatsappChat => $_getBF(0);
  @$pb.TagNumber(1)
  set isWhatsappChat($core.bool v) {
    $_setBool(0, v);
  }

  @$pb.TagNumber(1)
  $core.bool hasIsWhatsappChat() => $_has(0);
  @$pb.TagNumber(1)
  void clearIsWhatsappChat() => $_clearField(1);

  @$pb.TagNumber(2)
  $core.double get confidenceScore => $_getN(1);
  @$pb.TagNumber(2)
  set confidenceScore($core.double v) {
    $_setFloat(1, v);
  }

  @$pb.TagNumber(2)
  $core.bool hasConfidenceScore() => $_has(1);
  @$pb.TagNumber(2)
  void clearConfidenceScore() => $_clearField(2);

  @$pb.TagNumber(3)
  WhatsAppMetadata get metadata => $_getN(2);
  @$pb.TagNumber(3)
  set metadata(WhatsAppMetadata v) {
    $_setField(3, v);
  }

  @$pb.TagNumber(3)
  $core.bool hasMetadata() => $_has(2);
  @$pb.TagNumber(3)
  void clearMetadata() => $_clearField(3);
  @$pb.TagNumber(3)
  WhatsAppMetadata ensureMetadata() => $_ensure(2);
}

enum ProcessWhatsAppRequest_ContentSource { content, contentId, notSet }

/// Request to process WhatsApp content
class ProcessWhatsAppRequest extends $pb.GeneratedMessage {
  factory ProcessWhatsAppRequest({
    $core.String? content,
    $core.String? contentId,
    $core.String? fileName,
  }) {
    final $result = create();
    if (content != null) {
      $result.content = content;
    }
    if (contentId != null) {
      $result.contentId = contentId;
    }
    if (fileName != null) {
      $result.fileName = fileName;
    }
    return $result;
  }
  ProcessWhatsAppRequest._() : super();
  factory ProcessWhatsAppRequest.fromBuffer($core.List<$core.int> i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromBuffer(i, r);
  factory ProcessWhatsAppRequest.fromJson($core.String i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromJson(i, r);

  static const $core.Map<$core.int, ProcessWhatsAppRequest_ContentSource>
      _ProcessWhatsAppRequest_ContentSourceByTag = {
    1: ProcessWhatsAppRequest_ContentSource.content,
    2: ProcessWhatsAppRequest_ContentSource.contentId,
    0: ProcessWhatsAppRequest_ContentSource.notSet
  };
  static final $pb.BuilderInfo _i = $pb.BuilderInfo(
      _omitMessageNames ? '' : 'ProcessWhatsAppRequest',
      package: const $pb.PackageName(_omitMessageNames ? '' : 'promz.api.v1'),
      createEmptyInstance: create)
    ..oo(0, [1, 2])
    ..aOS(1, _omitFieldNames ? '' : 'content')
    ..aOS(2, _omitFieldNames ? '' : 'contentId')
    ..aOS(3, _omitFieldNames ? '' : 'fileName')
    ..hasRequiredFields = false;

  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
      'Will be removed in next major version')
  ProcessWhatsAppRequest clone() => ProcessWhatsAppRequest()..mergeFromMessage(this);
  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
      'Will be removed in next major version')
  ProcessWhatsAppRequest copyWith(void Function(ProcessWhatsAppRequest) updates) =>
      super.copyWith((message) => updates(message as ProcessWhatsAppRequest))
          as ProcessWhatsAppRequest;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static ProcessWhatsAppRequest create() => ProcessWhatsAppRequest._();
  ProcessWhatsAppRequest createEmptyInstance() => create();
  static $pb.PbList<ProcessWhatsAppRequest> createRepeated() =>
      $pb.PbList<ProcessWhatsAppRequest>();
  @$core.pragma('dart2js:noInline')
  static ProcessWhatsAppRequest getDefault() =>
      _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ProcessWhatsAppRequest>(create);
  static ProcessWhatsAppRequest? _defaultInstance;

  ProcessWhatsAppRequest_ContentSource whichContentSource() =>
      _ProcessWhatsAppRequest_ContentSourceByTag[$_whichOneof(0)]!;
  void clearContentSource() => $_clearField($_whichOneof(0));

  @$pb.TagNumber(1)
  $core.String get content => $_getSZ(0);
  @$pb.TagNumber(1)
  set content($core.String v) {
    $_setString(0, v);
  }

  @$pb.TagNumber(1)
  $core.bool hasContent() => $_has(0);
  @$pb.TagNumber(1)
  void clearContent() => $_clearField(1);

  @$pb.TagNumber(2)
  $core.String get contentId => $_getSZ(1);
  @$pb.TagNumber(2)
  set contentId($core.String v) {
    $_setString(1, v);
  }

  @$pb.TagNumber(2)
  $core.bool hasContentId() => $_has(1);
  @$pb.TagNumber(2)
  void clearContentId() => $_clearField(2);

  @$pb.TagNumber(3)
  $core.String get fileName => $_getSZ(2);
  @$pb.TagNumber(3)
  set fileName($core.String v) {
    $_setString(2, v);
  }

  @$pb.TagNumber(3)
  $core.bool hasFileName() => $_has(2);
  @$pb.TagNumber(3)
  void clearFileName() => $_clearField(3);
}

/// Response with processed WhatsApp content
class ProcessWhatsAppResponse extends $pb.GeneratedMessage {
  factory ProcessWhatsAppResponse({
    $core.String? processedContent,
    WhatsAppMetadata? metadata,
    $pb.PbMap<$core.String, $core.String>? variables,
  }) {
    final $result = create();
    if (processedContent != null) {
      $result.processedContent = processedContent;
    }
    if (metadata != null) {
      $result.metadata = metadata;
    }
    if (variables != null) {
      $result.variables.addAll(variables);
    }
    return $result;
  }
  ProcessWhatsAppResponse._() : super();
  factory ProcessWhatsAppResponse.fromBuffer($core.List<$core.int> i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromBuffer(i, r);
  factory ProcessWhatsAppResponse.fromJson($core.String i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(
      _omitMessageNames ? '' : 'ProcessWhatsAppResponse',
      package: const $pb.PackageName(_omitMessageNames ? '' : 'promz.api.v1'),
      createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'processedContent')
    ..aOM<WhatsAppMetadata>(2, _omitFieldNames ? '' : 'metadata',
        subBuilder: WhatsAppMetadata.create)
    ..m<$core.String, $core.String>(3, _omitFieldNames ? '' : 'variables',
        entryClassName: 'ProcessWhatsAppResponse.VariablesEntry',
        keyFieldType: $pb.PbFieldType.OS,
        valueFieldType: $pb.PbFieldType.OS,
        packageName: const $pb.PackageName('promz.api.v1'))
    ..hasRequiredFields = false;

  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
      'Will be removed in next major version')
  ProcessWhatsAppResponse clone() => ProcessWhatsAppResponse()..mergeFromMessage(this);
  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
      'Will be removed in next major version')
  ProcessWhatsAppResponse copyWith(void Function(ProcessWhatsAppResponse) updates) =>
      super.copyWith((message) => updates(message as ProcessWhatsAppResponse))
          as ProcessWhatsAppResponse;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static ProcessWhatsAppResponse create() => ProcessWhatsAppResponse._();
  ProcessWhatsAppResponse createEmptyInstance() => create();
  static $pb.PbList<ProcessWhatsAppResponse> createRepeated() =>
      $pb.PbList<ProcessWhatsAppResponse>();
  @$core.pragma('dart2js:noInline')
  static ProcessWhatsAppResponse getDefault() =>
      _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ProcessWhatsAppResponse>(create);
  static ProcessWhatsAppResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get processedContent => $_getSZ(0);
  @$pb.TagNumber(1)
  set processedContent($core.String v) {
    $_setString(0, v);
  }

  @$pb.TagNumber(1)
  $core.bool hasProcessedContent() => $_has(0);
  @$pb.TagNumber(1)
  void clearProcessedContent() => $_clearField(1);

  @$pb.TagNumber(2)
  WhatsAppMetadata get metadata => $_getN(1);
  @$pb.TagNumber(2)
  set metadata(WhatsAppMetadata v) {
    $_setField(2, v);
  }

  @$pb.TagNumber(2)
  $core.bool hasMetadata() => $_has(1);
  @$pb.TagNumber(2)
  void clearMetadata() => $_clearField(2);
  @$pb.TagNumber(2)
  WhatsAppMetadata ensureMetadata() => $_ensure(1);

  @$pb.TagNumber(3)
  $pb.PbMap<$core.String, $core.String> get variables => $_getMap(2);
}

/// Metadata specific to WhatsApp chats
class WhatsAppMetadata extends $pb.GeneratedMessage {
  factory WhatsAppMetadata({
    $core.String? groupName,
    $core.String? chatName,
    $core.Iterable<$core.String>? participants,
    $core.int? participantCount,
    $core.int? messageCount,
    $fixnum.Int64? firstMessageTimestamp,
    $fixnum.Int64? lastMessageTimestamp,
    $core.Iterable<WhatsAppMessage>? sampleMessages,
    $core.bool? isGroupChat,
  }) {
    final $result = create();
    if (groupName != null) {
      $result.groupName = groupName;
    }
    if (chatName != null) {
      $result.chatName = chatName;
    }
    if (participants != null) {
      $result.participants.addAll(participants);
    }
    if (participantCount != null) {
      $result.participantCount = participantCount;
    }
    if (messageCount != null) {
      $result.messageCount = messageCount;
    }
    if (firstMessageTimestamp != null) {
      $result.firstMessageTimestamp = firstMessageTimestamp;
    }
    if (lastMessageTimestamp != null) {
      $result.lastMessageTimestamp = lastMessageTimestamp;
    }
    if (sampleMessages != null) {
      $result.sampleMessages.addAll(sampleMessages);
    }
    if (isGroupChat != null) {
      $result.isGroupChat = isGroupChat;
    }
    return $result;
  }
  WhatsAppMetadata._() : super();
  factory WhatsAppMetadata.fromBuffer($core.List<$core.int> i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromBuffer(i, r);
  factory WhatsAppMetadata.fromJson($core.String i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'WhatsAppMetadata',
      package: const $pb.PackageName(_omitMessageNames ? '' : 'promz.api.v1'),
      createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'groupName')
    ..aOS(2, _omitFieldNames ? '' : 'chatName')
    ..pPS(3, _omitFieldNames ? '' : 'participants')
    ..a<$core.int>(4, _omitFieldNames ? '' : 'participantCount', $pb.PbFieldType.O3)
    ..a<$core.int>(5, _omitFieldNames ? '' : 'messageCount', $pb.PbFieldType.O3)
    ..aInt64(6, _omitFieldNames ? '' : 'firstMessageTimestamp')
    ..aInt64(7, _omitFieldNames ? '' : 'lastMessageTimestamp')
    ..pc<WhatsAppMessage>(8, _omitFieldNames ? '' : 'sampleMessages', $pb.PbFieldType.PM,
        subBuilder: WhatsAppMessage.create)
    ..aOB(9, _omitFieldNames ? '' : 'isGroupChat')
    ..hasRequiredFields = false;

  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
      'Will be removed in next major version')
  WhatsAppMetadata clone() => WhatsAppMetadata()..mergeFromMessage(this);
  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
      'Will be removed in next major version')
  WhatsAppMetadata copyWith(void Function(WhatsAppMetadata) updates) =>
      super.copyWith((message) => updates(message as WhatsAppMetadata)) as WhatsAppMetadata;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static WhatsAppMetadata create() => WhatsAppMetadata._();
  WhatsAppMetadata createEmptyInstance() => create();
  static $pb.PbList<WhatsAppMetadata> createRepeated() => $pb.PbList<WhatsAppMetadata>();
  @$core.pragma('dart2js:noInline')
  static WhatsAppMetadata getDefault() =>
      _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<WhatsAppMetadata>(create);
  static WhatsAppMetadata? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get groupName => $_getSZ(0);
  @$pb.TagNumber(1)
  set groupName($core.String v) {
    $_setString(0, v);
  }

  @$pb.TagNumber(1)
  $core.bool hasGroupName() => $_has(0);
  @$pb.TagNumber(1)
  void clearGroupName() => $_clearField(1);

  @$pb.TagNumber(2)
  $core.String get chatName => $_getSZ(1);
  @$pb.TagNumber(2)
  set chatName($core.String v) {
    $_setString(1, v);
  }

  @$pb.TagNumber(2)
  $core.bool hasChatName() => $_has(1);
  @$pb.TagNumber(2)
  void clearChatName() => $_clearField(2);

  @$pb.TagNumber(3)
  $pb.PbList<$core.String> get participants => $_getList(2);

  @$pb.TagNumber(4)
  $core.int get participantCount => $_getIZ(3);
  @$pb.TagNumber(4)
  set participantCount($core.int v) {
    $_setSignedInt32(3, v);
  }

  @$pb.TagNumber(4)
  $core.bool hasParticipantCount() => $_has(3);
  @$pb.TagNumber(4)
  void clearParticipantCount() => $_clearField(4);

  @$pb.TagNumber(5)
  $core.int get messageCount => $_getIZ(4);
  @$pb.TagNumber(5)
  set messageCount($core.int v) {
    $_setSignedInt32(4, v);
  }

  @$pb.TagNumber(5)
  $core.bool hasMessageCount() => $_has(4);
  @$pb.TagNumber(5)
  void clearMessageCount() => $_clearField(5);

  @$pb.TagNumber(6)
  $fixnum.Int64 get firstMessageTimestamp => $_getI64(5);
  @$pb.TagNumber(6)
  set firstMessageTimestamp($fixnum.Int64 v) {
    $_setInt64(5, v);
  }

  @$pb.TagNumber(6)
  $core.bool hasFirstMessageTimestamp() => $_has(5);
  @$pb.TagNumber(6)
  void clearFirstMessageTimestamp() => $_clearField(6);

  @$pb.TagNumber(7)
  $fixnum.Int64 get lastMessageTimestamp => $_getI64(6);
  @$pb.TagNumber(7)
  set lastMessageTimestamp($fixnum.Int64 v) {
    $_setInt64(6, v);
  }

  @$pb.TagNumber(7)
  $core.bool hasLastMessageTimestamp() => $_has(6);
  @$pb.TagNumber(7)
  void clearLastMessageTimestamp() => $_clearField(7);

  @$pb.TagNumber(8)
  $pb.PbList<WhatsAppMessage> get sampleMessages => $_getList(7);

  @$pb.TagNumber(9)
  $core.bool get isGroupChat => $_getBF(8);
  @$pb.TagNumber(9)
  set isGroupChat($core.bool v) {
    $_setBool(8, v);
  }

  @$pb.TagNumber(9)
  $core.bool hasIsGroupChat() => $_has(8);
  @$pb.TagNumber(9)
  void clearIsGroupChat() => $_clearField(9);
}

/// Represents a WhatsApp message
class WhatsAppMessage extends $pb.GeneratedMessage {
  factory WhatsAppMessage({
    $core.String? sender,
    $fixnum.Int64? timestamp,
    $core.String? content,
    $core.bool? hasMedia,
    $core.String? mediaType,
  }) {
    final $result = create();
    if (sender != null) {
      $result.sender = sender;
    }
    if (timestamp != null) {
      $result.timestamp = timestamp;
    }
    if (content != null) {
      $result.content = content;
    }
    if (hasMedia != null) {
      $result.hasMedia = hasMedia;
    }
    if (mediaType != null) {
      $result.mediaType = mediaType;
    }
    return $result;
  }
  WhatsAppMessage._() : super();
  factory WhatsAppMessage.fromBuffer($core.List<$core.int> i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromBuffer(i, r);
  factory WhatsAppMessage.fromJson($core.String i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'WhatsAppMessage',
      package: const $pb.PackageName(_omitMessageNames ? '' : 'promz.api.v1'),
      createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'sender')
    ..aInt64(2, _omitFieldNames ? '' : 'timestamp')
    ..aOS(3, _omitFieldNames ? '' : 'content')
    ..aOB(4, _omitFieldNames ? '' : 'hasMedia')
    ..aOS(5, _omitFieldNames ? '' : 'mediaType')
    ..hasRequiredFields = false;

  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
      'Will be removed in next major version')
  WhatsAppMessage clone() => WhatsAppMessage()..mergeFromMessage(this);
  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
      'Will be removed in next major version')
  WhatsAppMessage copyWith(void Function(WhatsAppMessage) updates) =>
      super.copyWith((message) => updates(message as WhatsAppMessage)) as WhatsAppMessage;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static WhatsAppMessage create() => WhatsAppMessage._();
  WhatsAppMessage createEmptyInstance() => create();
  static $pb.PbList<WhatsAppMessage> createRepeated() => $pb.PbList<WhatsAppMessage>();
  @$core.pragma('dart2js:noInline')
  static WhatsAppMessage getDefault() =>
      _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<WhatsAppMessage>(create);
  static WhatsAppMessage? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get sender => $_getSZ(0);
  @$pb.TagNumber(1)
  set sender($core.String v) {
    $_setString(0, v);
  }

  @$pb.TagNumber(1)
  $core.bool hasSender() => $_has(0);
  @$pb.TagNumber(1)
  void clearSender() => $_clearField(1);

  @$pb.TagNumber(2)
  $fixnum.Int64 get timestamp => $_getI64(1);
  @$pb.TagNumber(2)
  set timestamp($fixnum.Int64 v) {
    $_setInt64(1, v);
  }

  @$pb.TagNumber(2)
  $core.bool hasTimestamp() => $_has(1);
  @$pb.TagNumber(2)
  void clearTimestamp() => $_clearField(2);

  @$pb.TagNumber(3)
  $core.String get content => $_getSZ(2);
  @$pb.TagNumber(3)
  set content($core.String v) {
    $_setString(2, v);
  }

  @$pb.TagNumber(3)
  $core.bool hasContent() => $_has(2);
  @$pb.TagNumber(3)
  void clearContent() => $_clearField(3);

  @$pb.TagNumber(4)
  $core.bool get hasMedia => $_getBF(3);
  @$pb.TagNumber(4)
  set hasMedia($core.bool v) {
    $_setBool(3, v);
  }

  @$pb.TagNumber(4)
  $core.bool hasHasMedia() => $_has(3);
  @$pb.TagNumber(4)
  void clearHasMedia() => $_clearField(4);

  @$pb.TagNumber(5)
  $core.String get mediaType => $_getSZ(4);
  @$pb.TagNumber(5)
  set mediaType($core.String v) {
    $_setString(4, v);
  }

  @$pb.TagNumber(5)
  $core.bool hasMediaType() => $_has(4);
  @$pb.TagNumber(5)
  void clearMediaType() => $_clearField(5);
}

/// WhatsAppResultsResponse extends the standard ResultsResponse with WhatsApp-specific fields
class WhatsAppResultsResponse extends $pb.GeneratedMessage {
  factory WhatsAppResultsResponse({
    $core.String? id,
    $core.String? contentType,
    $pb.PbMap<$core.String, $core.String>? metadata,
    $core.bool? hasFullContent,
    $core.String? contentUrl,
    $fixnum.Int64? expiresAt,
    WhatsAppMetadata? whatsappMetadata,
    $core.bool? isWhatsappChat,
  }) {
    final $result = create();
    if (id != null) {
      $result.id = id;
    }
    if (contentType != null) {
      $result.contentType = contentType;
    }
    if (metadata != null) {
      $result.metadata.addAll(metadata);
    }
    if (hasFullContent != null) {
      $result.hasFullContent = hasFullContent;
    }
    if (contentUrl != null) {
      $result.contentUrl = contentUrl;
    }
    if (expiresAt != null) {
      $result.expiresAt = expiresAt;
    }
    if (whatsappMetadata != null) {
      $result.whatsappMetadata = whatsappMetadata;
    }
    if (isWhatsappChat != null) {
      $result.isWhatsappChat = isWhatsappChat;
    }
    return $result;
  }
  WhatsAppResultsResponse._() : super();
  factory WhatsAppResultsResponse.fromBuffer($core.List<$core.int> i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromBuffer(i, r);
  factory WhatsAppResultsResponse.fromJson($core.String i,
          [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) =>
      create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(
      _omitMessageNames ? '' : 'WhatsAppResultsResponse',
      package: const $pb.PackageName(_omitMessageNames ? '' : 'promz.api.v1'),
      createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'id')
    ..aOS(2, _omitFieldNames ? '' : 'contentType')
    ..m<$core.String, $core.String>(3, _omitFieldNames ? '' : 'metadata',
        entryClassName: 'WhatsAppResultsResponse.MetadataEntry',
        keyFieldType: $pb.PbFieldType.OS,
        valueFieldType: $pb.PbFieldType.OS,
        packageName: const $pb.PackageName('promz.api.v1'))
    ..aOB(4, _omitFieldNames ? '' : 'hasFullContent')
    ..aOS(5, _omitFieldNames ? '' : 'contentUrl')
    ..aInt64(6, _omitFieldNames ? '' : 'expiresAt')
    ..aOM<WhatsAppMetadata>(10, _omitFieldNames ? '' : 'whatsappMetadata',
        subBuilder: WhatsAppMetadata.create)
    ..aOB(11, _omitFieldNames ? '' : 'isWhatsappChat')
    ..hasRequiredFields = false;

  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
      'Will be removed in next major version')
  WhatsAppResultsResponse clone() => WhatsAppResultsResponse()..mergeFromMessage(this);
  @$core.Deprecated('Using this can add significant overhead to your binary. '
      'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
      'Will be removed in next major version')
  WhatsAppResultsResponse copyWith(void Function(WhatsAppResultsResponse) updates) =>
      super.copyWith((message) => updates(message as WhatsAppResultsResponse))
          as WhatsAppResultsResponse;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static WhatsAppResultsResponse create() => WhatsAppResultsResponse._();
  WhatsAppResultsResponse createEmptyInstance() => create();
  static $pb.PbList<WhatsAppResultsResponse> createRepeated() =>
      $pb.PbList<WhatsAppResultsResponse>();
  @$core.pragma('dart2js:noInline')
  static WhatsAppResultsResponse getDefault() =>
      _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<WhatsAppResultsResponse>(create);
  static WhatsAppResultsResponse? _defaultInstance;

  /// Standard metadata fields from ResultsResponse.Metadata
  @$pb.TagNumber(1)
  $core.String get id => $_getSZ(0);
  @$pb.TagNumber(1)
  set id($core.String v) {
    $_setString(0, v);
  }

  @$pb.TagNumber(1)
  $core.bool hasId() => $_has(0);
  @$pb.TagNumber(1)
  void clearId() => $_clearField(1);

  @$pb.TagNumber(2)
  $core.String get contentType => $_getSZ(1);
  @$pb.TagNumber(2)
  set contentType($core.String v) {
    $_setString(1, v);
  }

  @$pb.TagNumber(2)
  $core.bool hasContentType() => $_has(1);
  @$pb.TagNumber(2)
  void clearContentType() => $_clearField(2);

  @$pb.TagNumber(3)
  $pb.PbMap<$core.String, $core.String> get metadata => $_getMap(2);

  @$pb.TagNumber(4)
  $core.bool get hasFullContent => $_getBF(3);
  @$pb.TagNumber(4)
  set hasFullContent($core.bool v) {
    $_setBool(3, v);
  }

  @$pb.TagNumber(4)
  $core.bool hasHasFullContent() => $_has(3);
  @$pb.TagNumber(4)
  void clearHasFullContent() => $_clearField(4);

  @$pb.TagNumber(5)
  $core.String get contentUrl => $_getSZ(4);
  @$pb.TagNumber(5)
  set contentUrl($core.String v) {
    $_setString(4, v);
  }

  @$pb.TagNumber(5)
  $core.bool hasContentUrl() => $_has(4);
  @$pb.TagNumber(5)
  void clearContentUrl() => $_clearField(5);

  @$pb.TagNumber(6)
  $fixnum.Int64 get expiresAt => $_getI64(5);
  @$pb.TagNumber(6)
  set expiresAt($fixnum.Int64 v) {
    $_setInt64(5, v);
  }

  @$pb.TagNumber(6)
  $core.bool hasExpiresAt() => $_has(5);
  @$pb.TagNumber(6)
  void clearExpiresAt() => $_clearField(6);

  /// WhatsApp-specific fields
  @$pb.TagNumber(10)
  WhatsAppMetadata get whatsappMetadata => $_getN(6);
  @$pb.TagNumber(10)
  set whatsappMetadata(WhatsAppMetadata v) {
    $_setField(10, v);
  }

  @$pb.TagNumber(10)
  $core.bool hasWhatsappMetadata() => $_has(6);
  @$pb.TagNumber(10)
  void clearWhatsappMetadata() => $_clearField(10);
  @$pb.TagNumber(10)
  WhatsAppMetadata ensureWhatsappMetadata() => $_ensure(6);

  @$pb.TagNumber(11)
  $core.bool get isWhatsappChat => $_getBF(7);
  @$pb.TagNumber(11)
  set isWhatsappChat($core.bool v) {
    $_setBool(7, v);
  }

  @$pb.TagNumber(11)
  $core.bool hasIsWhatsappChat() => $_has(7);
  @$pb.TagNumber(11)
  void clearIsWhatsappChat() => $_clearField(11);
}

const _omitFieldNames = $core.bool.fromEnvironment('protobuf.omit_field_names');
const _omitMessageNames = $core.bool.fromEnvironment('protobuf.omit_message_names');
