//
//  Generated code. Do not modify.
//  source: common.proto
//
// @dart = 3.3

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:core' as $core;

import 'package:protobuf/protobuf.dart' as $pb;

/// LicenseTier represents the user's license tier
class LicenseTier extends $pb.ProtobufEnum {
  /// Unknown or unspecified tier
  static const LicenseTier LICENSE_TIER_UNSPECIFIED =
      LicenseTier._(0, _omitEnumNames ? '' : 'LICENSE_TIER_UNSPECIFIED');

  /// Free tier
  static const LicenseTier LICENSE_TIER_FREE =
      LicenseTier._(1, _omitEnumNames ? '' : 'LICENSE_TIER_FREE');

  /// Pro tier
  static const LicenseTier LICENSE_TIER_PRO =
      LicenseTier._(2, _omitEnumNames ? '' : 'LICENSE_TIER_PRO');

  /// Enterprise tier
  static const LicenseTier LICENSE_TIER_ENTERPRISE =
      LicenseTier._(3, _omitEnumNames ? '' : 'LICENSE_TIER_ENTERPRISE');

  static const $core.List<LicenseTier> values = <LicenseTier>[
    LICENSE_TIER_UNSPECIFIED,
    LICENSE_TIER_FREE,
    LICENSE_TIER_PRO,
    LICENSE_TIER_ENTERPRISE,
  ];

  static final $core.Map<$core.int, LicenseTier> _byValue = $pb.ProtobufEnum.initByValue(values);
  static LicenseTier? valueOf($core.int value) => _byValue[value];

  const LicenseTier._(super.v, super.n);
}

/// UploadStatus represents the status of a processing job
class UploadStatus extends $pb.ProtobufEnum {
  /// Unknown or unspecified status
  static const UploadStatus UPLOAD_STATUS_UNSPECIFIED =
      UploadStatus._(0, _omitEnumNames ? '' : 'UPLOAD_STATUS_UNSPECIFIED');

  /// Job is queued for processing
  static const UploadStatus UPLOAD_STATUS_QUEUED =
      UploadStatus._(1, _omitEnumNames ? '' : 'UPLOAD_STATUS_QUEUED');

  /// Job is currently processing
  static const UploadStatus UPLOAD_STATUS_PROCESSING =
      UploadStatus._(2, _omitEnumNames ? '' : 'UPLOAD_STATUS_PROCESSING');

  /// Job has completed successfully
  static const UploadStatus UPLOAD_STATUS_COMPLETED =
      UploadStatus._(3, _omitEnumNames ? '' : 'UPLOAD_STATUS_COMPLETED');

  /// Job has failed
  static const UploadStatus UPLOAD_STATUS_FAILED =
      UploadStatus._(4, _omitEnumNames ? '' : 'UPLOAD_STATUS_FAILED');

  /// Job has been cancelled
  static const UploadStatus UPLOAD_STATUS_CANCELLED =
      UploadStatus._(5, _omitEnumNames ? '' : 'UPLOAD_STATUS_CANCELLED');

  static const $core.List<UploadStatus> values = <UploadStatus>[
    UPLOAD_STATUS_UNSPECIFIED,
    UPLOAD_STATUS_QUEUED,
    UPLOAD_STATUS_PROCESSING,
    UPLOAD_STATUS_COMPLETED,
    UPLOAD_STATUS_FAILED,
    UPLOAD_STATUS_CANCELLED,
  ];

  static final $core.Map<$core.int, UploadStatus> _byValue = $pb.ProtobufEnum.initByValue(values);
  static UploadStatus? valueOf($core.int value) => _byValue[value];

  const UploadStatus._(super.v, super.n);
}

const _omitEnumNames = $core.bool.fromEnvironment('protobuf.omit_enum_names');
