import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:promz/core/error/error_handler.dart';
import 'package:promz/core/providers/app_restart_provider.dart';
import 'package:promz/core/providers/service_providers.dart';
import 'package:promz/core/providers/shared_content_provider.dart';
import 'package:promz/core/services/deep_link_service.dart';
import 'package:promz/core/utils/deep_link_utils.dart';
import 'package:promz/database/database.dart';
import 'package:promz/database/db_utils.dart';
import 'package:promz/features/account/providers/settings_provider.dart';
import 'package:promz/features/home/<USER>/home_viewmodel.dart';
import 'package:promz/features/layout/app_layout.dart';
import 'package:promz/features/onboarding/services/onboarding_service.dart';
import 'package:promz_common/config/api_config.dart';
import 'package:promz_common/promz_common.dart';

// Global navigator key
final GlobalKey<NavigatorState> appNavigatorKey = GlobalKey<NavigatorState>();

const String _logName = 'PromzApp';

class App extends ConsumerStatefulWidget {
  const App({super.key});

  @override
  ConsumerState<App> createState() => _AppState();
}

class _AppState extends ConsumerState<App> with WidgetsBindingObserver {
  // Subscription for handling incoming links while app is running
  StreamSubscription<Uri>? _deepLinkSubscription;
  // DeepLinkService instance for handling deep links
  final DeepLinkService _deepLinkService = DeepLinkService();

  @override
  void initState() {
    super.initState();

    // Check API connectivity and switch to fallback URL if needed
    _checkApiConnectivity();

    // Check authentication state
    _checkAuthState();

    // Check if we need to show onboarding
    _checkOnboardingStatus();

    // Initialize the shared content handler (will be created by the provider)
    // This will set up listeners for shared files
    ref.read(sharedContentHandlerProvider);

    // Initialize deep link handling
    _initializeDeepLinkHandling();
  }

  void _checkApiConnectivity() async {
    try {
      appLog.info('Checking API connectivity...', name: _logName);
      await ApiConfig.checkConnectivity();
      appLog.info('API connectivity check completed. Using base URL: ${ApiConfig.baseUrl}',
          name: _logName);
    } catch (e) {
      appLog.error('API connectivity check failed', name: _logName, error: e);
    }
  }

  /// Check authentication state on app startup
  Future<void> _checkAuthState() async {
    try {
      appLog.debug('Checking authentication state on app startup', name: _logName);

      // Get the Supabase service
      final supabaseService = ref.read(supabaseServiceProvider);

      // Wait for Supabase to be initialized
      if (!supabaseService.isInitialized) {
        appLog.debug('Supabase not initialized yet, waiting...', name: _logName);
        // We'll rely on the auth state provider to handle this case
        return;
      }

      // Check if we have a valid session
      final session = supabaseService.client.auth.currentSession;
      if (session != null) {
        appLog.debug('Found existing session on app startup', name: _logName);
        appLog.debug('Session expires at: ${session.expiresAt}', name: _logName);

        // Check if session needs to be refreshed
        await supabaseService.checkAndRefreshSession();
      } else {
        appLog.debug('No session found on app startup', name: _logName);
      }
    } catch (e) {
      appLog.error('Error checking auth state on startup: $e', name: _logName);
    }
  }

  /// Check if we need to show onboarding screens
  Future<void> _checkOnboardingStatus() async {
    try {
      appLog.debug('Checking onboarding status', name: _logName);
      final hasCompletedOnboarding = await OnboardingService.hasCompletedOnboarding();

      if (!hasCompletedOnboarding) {
        appLog.debug('User has not completed onboarding', name: _logName);
        // We'll handle navigation in the build method
      } else {
        appLog.debug('User has already completed onboarding', name: _logName);
      }
    } catch (e) {
      appLog.error('Error checking onboarding status: $e', name: _logName);
    }
  }

  void _initializeDeepLinkHandling() {
    // Initialize the deep link handling utility
    DeepLinkUtils.initialize();

    // Listen to the stream of deep links
    _deepLinkSubscription = DeepLinkUtils.links.listen((uri) {
      _handleDeepLink(uri);
    }, onError: (err, stack) {
      appLog.error('Error in deep link stream', name: _logName, error: err, stackTrace: stack);
    });

    // For platforms that don't support method channel deep linking (like Windows),
    // we can still handle links received through other mechanisms (like shared content)
    appLog.info('Deep link handler initialized', name: _logName);
  }

  void _handleDeepLink(Uri uri) {
    appLog.info('Handling deep link: $uri', name: _logName);
    // Use the DeepLinkService to process the link
    final url = uri.toString();
    _deepLinkService.processDeepLink(url).then((result) {
      final (isValid, message, promptId) = result;

      if (promptId != null) {
        appLog.debug('Navigating to prompt detail for ID: $promptId', name: _logName);
        _navigateToPromptDetail(promptId);
      } else {
        appLog.warning('Could not extract a valid prompt ID from link: $url', name: _logName);
        // Consider showing an error message to the user
      }
    }).catchError((error) {
      appLog.error('Error processing deep link', name: _logName, error: error);
    });
  }

  // Helper method to navigate to prompt detail screen
  void _navigateToPromptDetail(String promptId) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (appNavigatorKey.currentState != null) {
        appNavigatorKey.currentState!.pushNamed('/prompt_detail', arguments: promptId);
      } else {
        appLog.error('Cannot navigate: Navigator key\'s current state is null', name: _logName);
      }
    });
  }

  @override
  void dispose() {
    // Cancel the link subscription
    _deepLinkSubscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Watch the restart provider to rebuild the entire app when needed
    final restartCounter = ref.watch(appRestartProvider);

    // Access settings to get theme mode
    final settings = ref.watch(settingsProvider);
    final themeMode = settings.themeMode;

    // Using a key that changes when restartCounter changes forces a complete rebuild
    return MaterialApp(
      key: ValueKey('app_restart_$restartCounter'),
      navigatorKey: appNavigatorKey, // Assign the global navigator key
      title: Strings.appTitle,
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: themeMode,
      home: const AppLayout(),
      // Handle routes not defined in the routes table
      onGenerateRoute: (settings) {
        appLog.debug('Generating route for: ${settings.name} with args: ${settings.arguments}',
            name: _logName);

        // Handle prompt detail route
        if (settings.name == '/prompt_detail') {
          final promptId = settings.arguments as String;
          // Return route to prompt detail screen with the prompt ID
          return MaterialPageRoute(
            builder: (context) => _buildPromptDetailRoute(promptId),
          );
        }

        // Return null if the route is not handled here
        return null;
      },
      // Handle routes that aren't handled by home, routes, or onGenerateRoute
      onUnknownRoute: (settings) {
        appLog.warning('Unknown route: ${settings.name}', name: _logName);
        return MaterialPageRoute(
          builder: (context) => const AppLayout(),
        );
      },
      builder: (context, child) {
        // Register the ScaffoldMessenger for global access
        ErrorHandler.registerScaffoldMessenger(ScaffoldMessenger.of(context));
        return child!;
      },
    );
  }

  // Helper method to build the prompt detail route
  Widget _buildPromptDetailRoute(String promptId) {
    appLog.debug('Building prompt detail route for ID: $promptId', name: _logName);

    // Create a minimal scaffold that immediately processes the prompt
    return ProviderScope(
      child: Builder(
        builder: (context) {
          // Use FutureBuilder just to fetch the data, not to show a UI
          return FutureBuilder<Prompt?>(
            future: AppDatabase.getInstance().then((db) => db.getPrompt(promptId)),
            builder: (context, snapshot) {
              // Handle different states without showing it to the user
              if (snapshot.connectionState == ConnectionState.done) {
                // Execute on the next frame after build is complete
                WidgetsBinding.instance.addPostFrameCallback((_) async {
                  if (snapshot.hasData && snapshot.data != null) {
                    // Create DisplayItem from the prompt
                    final prompt = snapshot.data!;
                    final promptModel = DatabaseUtils.createPromptModelFromDb(prompt);
                    final displayItem = DisplayItem(
                      text: prompt.title,
                      displayText: prompt.title,
                      subtitle: prompt.subtitle,
                      type: DisplayItemType.promptTitle,
                      prompt: promptModel,
                    );

                    // Directly call onPromptSelected
                    final homeViewModel =
                        ProviderScope.containerOf(context).read(homeViewModelProvider);

                    if (context.mounted) {
                      await homeViewModel.onPromptSelected(context, displayItem);
                    } else {
                      appLog.warning(
                          'Context in _buildPromptDetailRoute unmounted before onPromptSelected',
                          name: _logName);
                    }
                  }

                  // Navigate back to home after initiating the process
                  if (context.mounted) {
                    Navigator.of(context).pushAndRemoveUntil(
                      MaterialPageRoute(builder: (_) => const AppLayout()),
                      (route) => false,
                    );
                  } else if (appNavigatorKey.currentContext != null &&
                      appNavigatorKey.currentContext!.mounted) {
                    appLog.warning(
                        'Original context unmounted, using appNavigatorKey for navigation back to AppLayout',
                        name: _logName);
                    Navigator.of(appNavigatorKey.currentContext!).pushAndRemoveUntil(
                      MaterialPageRoute(builder: (_) => const AppLayout()),
                      (route) => false,
                    );
                  } else {
                    appLog.error(
                        'All contexts unmounted, cannot navigate back to AppLayout from _buildPromptDetailRoute',
                        name: _logName);
                  }
                });
              }

              // Return an invisible widget while processing
              return const Opacity(
                opacity: 0,
                child: Scaffold(
                  body: SizedBox.shrink(),
                ),
              );
            },
          );
        },
      ),
    );
  }
}
