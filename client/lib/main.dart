import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:promz/app.dart';
import 'package:promz/core/error/error_handler.dart';
import 'package:promz/core/services/client_context_service.dart';
import 'package:promz/core/services/license_manager_service.dart';
import 'package:promz/core/services/supabase_service.dart';
import 'package:promz/core/services/windows_oauth_service.dart';
import 'package:promz/core/utils/global_functions.dart';
import 'package:promz/database/database.dart';
import 'package:promz/features/account/providers/settings_provider.dart';
import 'package:promz_common/config/api_config.dart';
import 'package:promz_common/promz_common.dart';
import 'package:safe_device/safe_device.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Supabase configuration - using the correct project URL
const String supabaseUrl = 'https://djqmdekosqnyxoqujxpm.supabase.co';
const String supabaseAnonKey =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.gXloMCWb_UN5fol9EZQoJTBK9_Ou_I0E6jVbfgN1mms';
const String serverClientId =
    '************-hdmv1s95niepb7jv060qucjq1o0qitjo.apps.googleusercontent.com';
const String _logName = 'PromzMain';

void main() async {
  // Set up global error handling
  FlutterError.onError = (FlutterErrorDetails details) {
    // Continue to report to console
    FlutterError.presentError(details);

    final String exceptionString = details.exception.toString();

    // Check if this is a defunct widget assertion error that we want to suppress
    if (exceptionString.contains('_lifecycleState != _ElementLifecycle.defunct')) {
      appLog.debug(
        'Suppressed defunct widget rebuild error - this is usually harmless. Details: $exceptionString',
        name: _logName,
        // stackTrace: details.stack, // Stack trace can be noisy for debug logs
      );
      // Return early to prevent app from potentially crashing or over-logging
      return;
    }

    // Check for the "deactivated widget's ancestor" error (common during navigation)
    if (exceptionString.contains("Looking up a deactivated widget's ancestor is unsafe")) {
      appLog.debug(
        'Suppressed unsafe ancestor lookup for deactivated widget: ${exceptionString.substring(0, 100)}...',
        name: _logName,
      );
      return;
    }

    // Check for tooltip animation errors (TooltipState SingleTickerProviderStateMixin issue)
    if (exceptionString.contains('TooltipState is a SingleTickerProviderStateMixin') &&
        exceptionString.contains('multiple tickers were created')) {
      appLog.debug(
        'Suppressed tooltip animation controller error: ${exceptionString.substring(0, 100)}...',
        name: _logName,
      );
      return;
    }

    // Log other Flutter framework errors as critical
    appLog.error(
      'Caught Flutter framework error',
      name: _logName,
      error: details.exception,
      stackTrace: details.stack,
    );
  };

  // Handle errors not caught by Flutter's error zone
  PlatformDispatcher.instance.onError = (error, stack) {
    appLog.error(
      'Uncaught platform error',
      name: _logName,
      error: error,
      stackTrace: stack,
    );
    // Return true to indicate we've handled the error
    return true;
  };

  // Run the app in a custom error zone for handling async errors
  runZonedGuarded(() async {
    // Initialize Flutter binding within the same zone where runApp will be called
    WidgetsFlutterBinding.ensureInitialized();

    final prefs = await SharedPreferences.getInstance();
    appLog.debug('Application starting', name: _logName);

    // Set up global functions to safely dismiss any active UI elements
    // that might cause context errors during navigation/sign-out
    setupGlobalUICleanup();

    // Configure API URLs based on environment (needs dotenv loaded)
    await _configureApiUrls();

    // Check network connectivity
    bool hasInternet = await _checkInternetConnectivity();
    if (!hasInternet) {
      appLog.error('No internet connection available. Some features may not work properly.',
          name: _logName);
    }

    // Handle deep links for Windows OAuth flow
    if (Platform.isWindows) {
      final args = <String>[];
      try {
        // Get command line arguments for deep links
        args.addAll(Platform.executableArguments);
      } catch (e) {
        appLog.error('Error getting executable arguments', name: _logName, error: e);
      }

      appLog.debug('Initial launch arguments: $args', name: _logName);

      // Parse deep link URL if present
      if (args.isNotEmpty) {
        final link = args.firstWhere(
          (arg) => arg.startsWith('promz://'),
          orElse: () => '',
        );

        if (link.isNotEmpty) {
          appLog.debug('Found deep link in launch arguments: $link', name: _logName);
          // Store this for later when we have initialized WindowsOAuthService
        }
      }
    }

    // Initialize Supabase
    try {
      appLog.debug('Initializing Supabase', name: _logName);
      final supabaseService = SupabaseService();
      await supabaseService.initialize(
        supabaseUrl: supabaseUrl,
        supabaseAnonKey: supabaseAnonKey,
        serverClientId: serverClientId,
      );

      // Initialize database
      final db = AppDatabase.getInstance();

      // Initialize client context service
      ClientContextService.initialize(db);

      appLog.debug('Supabase is initialized: ${supabaseService.isInitialized}', name: _logName);
      appLog.debug('User session: ${supabaseService.currentUser?.email ?? 'None'}', name: _logName);
      appLog.debug('Initializing license manager service', name: _logName);
      LicenseManagerService.initialize();

      // Handle deep links for Windows OAuth flow
      if (Platform.isWindows) {
        final args = <String>[];
        try {
          // Get command line arguments again, in case they've changed
          args.addAll(Platform.executableArguments);
        } catch (e) {
          appLog.error('Error getting executable arguments', name: _logName, error: e);
        }

        // Parse deep link URL if present
        if (args.isNotEmpty) {
          final link = args.firstWhere(
            (arg) => arg.startsWith('promz://'),
            orElse: () => '',
          );

          if (link.isNotEmpty) {
            appLog.debug('Processing deep link after Supabase init: $link', name: _logName);

            try {
              // Parse the URI and handle the authentication callback
              final uri = Uri.parse(link);
              final windowsOAuthService = WindowsOAuthService();
              await windowsOAuthService.handleAuthCallback(uri);
              appLog.debug('Successfully handled auth callback', name: _logName);
            } catch (e) {
              appLog.error('Error handling OAuth callback', name: _logName, error: e);
            }
          }
        }
      }

      // Initialize license service if user is authenticated
      if (supabaseService.isAuthenticated) {
        try {
          appLog.debug('Initializing license', name: _logName);
          final licenseManager = LicenseManagerService();
          final apiKey = await licenseManager.initializeLicense();

          if (apiKey != null) {
            appLog.debug('License initialized successfully with key: ${apiKey.substring(0, 8)}...',
                name: _logName);
          } else {
            appLog.warning('No API key available', name: _logName);
            appLog.debug('Creating a free license', name: _logName);
            final success = await licenseManager.ensureUserHasFreeLicense();
            if (!success) {
              appLog.warning('Failed to create free license', name: _logName);
            }
          }
        } catch (e) {
          appLog.error('Failed to initialize license: $e', name: _logName);
        }
      }

      // Test gRPC connectivity after services are initialized
      appLog.debug('Testing gRPC connectivity...', name: _logName);
      try {
        final apiClient = ClientContextService().apiClient;
        final isConnected = await apiClient.testGrpcConnection();
        if (isConnected) {
          appLog.info('✅ gRPC server connection test passed', name: _logName);
        } else {
          appLog.warning('⚠️ gRPC server connection test failed', name: _logName);
        }
      } catch (e) {
        appLog.error('Error testing gRPC connection: $e', name: _logName);
      }
    } catch (e) {
      appLog.error('Failed to initialize Supabase: $e', name: _logName);
    }

    runApp(
      ProviderScope(
        overrides: [
          settingsProvider.overrideWith((ref) => SettingsNotifier(prefs)),
        ],
        child: const App(),
      ),
    );
  }, (error, stackTrace) {
    // Handle any errors not caught by other handlers
    appLog.error(
      'Uncaught error in runZonedGuarded',
      name: _logName,
      error: error,
      stackTrace: stackTrace,
    );
  });
}

/// Set up global functions for safely cleaning up UI elements
void setupGlobalUICleanup() {
  // Create a global function to safely dismiss all UI overlays (snackbars, banners, etc.)
  // This can be called during sign-out or other major state changes
  AppGlobalFunctions.safeCleanupUI = () {
    final messenger = ErrorHandler.globalScaffoldMessenger;
    if (messenger != null) {
      // Hide any visible snackbars
      messenger.hideCurrentSnackBar();

      // Hide any visible material banners
      messenger.clearMaterialBanners();

      // Clear any queued snackbars
      messenger.clearSnackBars();
    }
  };
}

/// Configure API URLs based on the current environment
Future<void> _configureApiUrls() async {
  try {
    // Check if running in an emulator
    final bool isEmulator = await _detectEmulator();

    // Store the emulator status in DeviceUtils for use throughout the app
    DeviceUtils.setEmulatorStatus(isEmulator);

    if (isEmulator) {
      appLog.info('Running on emulator - forcing local development URL for API', name: _logName);
      // By default, use local development URL for emulators
      // This can be overridden later via the AccountView settings
      ApiConfig.forceLocalDevelopmentUrl = true;
    } else {
      appLog.info('Running on physical device - using standard API URL selection', name: _logName);
    }
  } catch (e) {
    // If emulator detection fails, log the error and use default API URL
    appLog.error('Error detecting emulator: $e', name: _logName);
    appLog.info('Using default API URL selection', name: _logName);
  }
}

/// Detect if the app is running on an emulator using safe_device package
Future<bool> _detectEmulator() async {
  try {
    // Use safe_device's isRealDevice method - this returns false for emulators
    // Note: This doesn't require location permissions as we're not checking for mock locations
    final isRealDevice = await SafeDevice.isRealDevice;

    // Log the result for debugging
    appLog.debug('Device detection - Is real device: $isRealDevice', name: _logName);

    // Return true if it's NOT a real device (meaning it's an emulator)
    return !isRealDevice;
  } catch (e) {
    appLog.warning('Failed to detect emulator using safe_device: $e', name: _logName);

    // Fallback mechanism - check if we have a manual override via build flag
    const forceLocalDevelopment =
        bool.fromEnvironment('FORCE_LOCAL_DEVELOPMENT', defaultValue: false);
    if (forceLocalDevelopment) {
      appLog.info('Using manual override to force local development mode', name: _logName);
      return true;
    }

    return false;
  }
}

/// Check if the device has internet connectivity
Future<bool> _checkInternetConnectivity() async {
  try {
    final connectivity = Connectivity();
    final connectivityResult = await connectivity.checkConnectivity();

    // Check if we have no connectivity
    if (connectivityResult.contains(ConnectivityResult.none) || connectivityResult.isEmpty) {
      return false;
    }

    // If we have some type of connectivity, verify with an actual connection
    try {
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } on SocketException catch (_) {
      return false;
    }
  } catch (e) {
    appLog.error('Error checking connectivity: $e', name: _logName);
    return false;
  }
}
