plugins {
    id "com.android.application"
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}

android {
    namespace = "ai.promz"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = flutter.ndkVersion

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = "17"
    }

    defaultConfig {
        applicationId = "ai.promz"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = 21 // Explicitly set to 21 for compatibility with sign_in_with_apple
        targetSdk = flutter.targetSdkVersion
        versionCode = 8
        versionName = "1.0.7"
    }

    signingConfigs {
        release {
            storeFile file('G:/Shared drives/116ideas/promz/keystore/release.keystore')
            storePassword 'suP4gD4B]H1Ss3E+>R'
            keyAlias 'androidreleasekey'
            keyPassword 'suP4gD4B]H1Ss3E+>R'
        }

        debug {
            storeFile file('G:/Shared drives/116ideas/promz/keystore/debug.keystore')
            storePassword 'android'
            keyAlias 'androiddebugkey'
            keyPassword 'android'
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release
        }
        debug {
            signingConfig signingConfigs.debug
        }
    }

    dependencies {
        constraints {
            implementation('org.jetbrains.kotlin:kotlin-stdlib-jdk7')
            implementation('org.jetbrains.kotlin:kotlin-stdlib-jdk8')
        }
    }
}

configurations.all {
    resolutionStrategy {
        force 'org.jetbrains.kotlin:kotlin-stdlib:1.8.22'
        force 'org.jetbrains.kotlin:kotlin-stdlib-common:1.8.22'
    }
}

flutter {
    source = "../.."
}
