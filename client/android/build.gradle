allprojects {
    repositories {
        google()
        mavenCentral()
    }

    project.plugins.withId('org.jetbrains.kotlin.android') {
        project.kotlin.jvmToolchain(17)
    }
}

rootProject.buildDir = "../build"
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(":app")
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
