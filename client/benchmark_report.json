{"timestamp": "2025-05-12T09:38:44.722878", "summary": {"total_tests": 3, "average_metrics": {"extraction_time_ms": 9.333333333333334, "precision": 0.5333333333333333, "recall": 1.0, "f1_score": 0.6944444444444443}, "by_type": {"financial": {"extraction_time_ms": 25.0, "precision": 0.6, "recall": 1.0, "f1_score": 0.7499999999999999}, "news": {"extraction_time_ms": 1.0, "precision": 0.5, "recall": 1.0, "f1_score": 0.6666666666666666}, "chat": {"extraction_time_ms": 2.0, "precision": 0.5, "recall": 1.0, "f1_score": 0.6666666666666666}}}, "detailed_results": {"Stock Analysis Report": {"type": "financial", "extraction_time_ms": 25, "precision": 0.6, "recall": 1.0, "f1_score": 0.7499999999999999, "keywords": ["data center", "quarterly results", "nvda", "nvidia", "ai", "revenue", "earnings", "growth", "chip", "analysis"]}, "Tech News Article": {"type": "news", "extraction_time_ms": 1, "precision": 0.5, "recall": 1.0, "f1_score": 0.6666666666666666, "keywords": ["artificial intelligence", "machine learning", "market analysis", "real time", "ai", "trading", "transforms", "financial", "markets", "leading"]}, "Investment Discussion Chat": {"type": "chat", "extraction_time_ms": 2, "precision": 0.5, "recall": 1.0, "f1_score": 0.6666666666666666, "keywords": ["nvda", "amd", "john", "sarah", "ai", "data center", "revenue", "earnings", "investment", "chip"]}}}