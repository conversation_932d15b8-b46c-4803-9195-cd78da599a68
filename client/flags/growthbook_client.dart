import 'dart:convert';
import 'package:http/http.dart' as http;

/// Lightweight GrowthBook feature flag client
class GrowthBookClient {
  final String apiHost;
  final String sdkKey;

  GrowthBookClient({
    required this.apiHost,
    required this.sdkKey,
  });

  Future<Map<String, dynamic>> getFeaturesForUser(String userId) async {
    final url = Uri.parse('$apiHost/api/features');

    final headers = {
      'Authorization': 'Bearer $sdkKey',
      'Content-Type': 'application/json',
    };

    final response = await http.get(url, headers: headers);

    if (response.statusCode == 200) {
      final decoded = json.decode(response.body);
      return decoded['features'] ?? {};
    } else {
      throw Exception('Failed to fetch features: ${response.body}');
    }
  }

  bool isFeatureEnabled(Map<String, dynamic> features, String flagKey) {
    if (!features.containsKey(flagKey)) return false;
    final value = features[flagKey]['defaultValue'] ?? false;
    return value is bool ? value : false;
  }
}
