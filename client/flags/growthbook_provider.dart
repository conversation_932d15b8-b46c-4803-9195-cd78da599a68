import 'package:flutter/foundation.dart';
import 'growthbook_client.dart';

class GrowthBookProvider with ChangeNotifier {
  final GrowthBookClient _client;
  final String _userId;

  Map<String, dynamic> _features = {};
  bool _isLoading = true;

  GrowthBookProvider(this._client, this._userId) {
    loadFeatures();
  }

  bool get isLoading => _isLoading;
  Map<String, dynamic> get features => _features;

  bool isFeatureEnabled(String key) {
    return _client.isFeatureEnabled(_features, key);
  }

  Future<void> loadFeatures() async {
    try {
      _features = await _client.getFeaturesForUser(_userId);
    } catch (e) {
      debugPrint('GrowthBook load error: $e');
      _features = {};
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> refresh() => loadFeatures();
}
