// A command line utility to test URL extraction functionality
// Usage: flutter run -d windows cmd/run_url_extractor.dart -a [input_file | text_with_url]
//
// This tool will:
// 1. If input_file is provided, read all files in the data/ subdirectory
// 2. Otherwise, process the provided text directly
// 3. Extract URLs from the input using UrlExtractor.extractUrl
// 4. Display detailed information about the extracted URLs

// ignore_for_file: avoid_print

import 'dart:io';
import 'package:path/path.dart' as path;
import 'package:promz/core/utils/url_extractor.dart';
import 'package:promz_common/promz_common.dart';

/// Process a single input file and extract URLs
Future<void> processFile(String filePath) async {
  try {
    print('\n---------------------------------------------------');
    print('Processing file: $filePath');

    // Read the file content
    final fileContent = await File(filePath).readAsString();
    print('File length: ${fileContent.length} characters');

    // Extract basic URL information using common utilities
    final firstUrl = extractFirstUrl(fileContent);
    print('\nBasic URL extraction:');
    print('  First URL: ${firstUrl ?? "None found"}');

    // Process the content with the comprehensive extractUrl function
    print('\nComprehensive URL processing:');
    if (firstUrl != null) {
      final processedInfo = await UrlExtractor.extractUrl(fileContent);
      if (processedInfo != null) {
        print('  Original URL: ${processedInfo.originalUrl}');
        print('  Final URL: ${processedInfo.finalUrl}');
        print('  Embedded URL: ${processedInfo.embeddedUrl ?? "None"}');
        print('  Is YouTube: ${processedInfo.isYouTubeUrl}');
        print('  YouTube ID: ${processedInfo.youtubeVideoId ?? "N/A"}');
        print('  Is News: ${processedInfo.isNewsUrl}');
        print('  Best URL to use: ${processedInfo.bestUrl}');
      } else {
        print('  No URL info could be processed');
      }
    } else {
      print('  No URL found to process');
    }

    print('---------------------------------------------------');
  } catch (e) {
    print('Error processing file: $filePath');
    print('Error: $e');
  }
}

/// Process text input directly
Future<void> processText(String text) async {
  try {
    print('\n---------------------------------------------------');
    print('Processing text input: "${text.length > 50 ? '${text.substring(0, 50)}...' : text}"');

    final processedInfo = await UrlExtractor.extractUrl(text);
    if (processedInfo != null) {
      print('  Original URL: ${processedInfo.originalUrl}');
      print('  Final URL: ${processedInfo.finalUrl}');
      print('  Embedded URL: ${processedInfo.embeddedUrl ?? "None"}');
      print('  Is YouTube: ${processedInfo.isYouTubeUrl}');
      print('  YouTube ID: ${processedInfo.youtubeVideoId ?? "N/A"}');
      print('  Is News: ${processedInfo.isNewsUrl}');
      print('  Best URL to use: ${processedInfo.bestUrl}');
    } else {
      print('  No URL info could be processed');
    }

    print('---------------------------------------------------');
  } catch (e) {
    print('Error processing text input');
    print('Error: $e');
  }
}

/// Main entry point
Future<void> main(List<String> args) async {
  print('URL Extractor Command Line Utility');
  print('=================================');

  if (args.isEmpty) {
    print(
        'Usage: flutter run -d windows cmd/run_url_extractor.dart -a [input_file | text_with_url]');
    print('Examples:');
    print('  flutter run -d windows cmd/run_url_extractor.dart -a data');
    print(
        '  flutter run -d windows cmd/run_url_extractor.dart -a "Check out this video https://youtu.be/abc123"');
    return;
  }

  final firstArg = args.first;

  // Check if the first argument is "data" to process all files in the data directory
  if (firstArg == 'data') {
    // Use path.join for platform-independent path handling
    final dataDir = Directory(path.join('cmd', 'data'));

    // Get all files in the data directory
    print('Processing all files in ${dataDir.path}');
    final files = await dataDir.list().toList();

    if (files.isEmpty) {
      print('No files found in ${dataDir.path}');
      return;
    }

    // Process each file
    for (final file in files) {
      if (file is File) {
        await processFile(file.path);
      }
    }
  } else {
    // Treat the argument as a text input with a URL
    final textInput = args.join(' ');
    await processText(textInput);
  }
}
