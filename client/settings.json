{"java.configuration.runtimes": [{"name": "openjdk-1.8", "path": "c:/jdk-1.8.0/bin", "default": true}], "[kotlin]": {"editor.defaultFormatter": "fwcd.kotlin"}, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": true, "source.fixAll": "always"}, "[sql]": {"editor.formatOnSave": false, "editor.formatOnPaste": false, "editor.formatOnType": false}, "files.associations": {"**/*.moor": "sql"}}