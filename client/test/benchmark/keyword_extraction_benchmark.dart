@Tags(['benchmark'])
library keyword_extraction_benchmark;

import 'dart:convert';
import 'dart:developer' as dev;
import 'dart:io';
import 'package:test/test.dart';
import 'package:promz/features/home/<USER>/text_analysis_service.dart';

class BenchmarkCase {
  final String name;
  final String content;
  final String? title;
  final String type; // 'financial', 'news', or 'chat'
  final List<String> expectedKeywords;
  final Map<String, double> expectedScores;

  const BenchmarkCase({
    required this.name,
    required this.content,
    this.title,
    required this.type,
    required this.expectedKeywords,
    required this.expectedScores,
  });
}

void main() {
  final logFile = File('benchmark_log.txt');
  final logSink = logFile.openWrite();

  void logResult(String message) {
    dev.log(message);
    logSink.writeln(message);
  }

  final benchmarkCases = [
    // Financial Cases
    const BenchmarkCase(
      name: 'Stock Analysis Report',
      content: '''
NVDA Q4 2024 Earnings Analysis
Nvidia Corporation reported strong quarterly results, with revenue reaching \$22.1 billion.
Key growth drivers include AI chip demand and data center solutions.
Gaming revenue showed modest recovery while automotive segment remained stable.
''',
      title: 'NVDA Q4 2024 Earnings Analysis',
      type: 'financial',
      expectedKeywords: ['nvda', 'nvidia', 'earnings', 'revenue', 'ai', 'chip'],
      expectedScores: {
        'nvda': 1.0,
        'nvidia': 1.0,
        'earnings': 0.8,
        'revenue': 0.7,
        'ai': 0.9,
        'chip': 0.7,
      },
    ),

    // News Cases
    const BenchmarkCase(
      name: 'Tech News Article',
      content: '''
Breaking: Artificial Intelligence Transforms Financial Markets
Leading tech companies are deploying AI solutions for market analysis.
Machine learning algorithms now process vast amounts of trading data in real-time.
''',
      title: 'AI Transforms Financial Markets',
      type: 'news',
      expectedKeywords: [
        'artificial intelligence',
        'ai',
        'financial',
        'markets',
        'machine learning'
      ],
      expectedScores: {
        'artificial intelligence': 1.0,
        'ai': 1.0,
        'financial': 0.8,
        'markets': 0.8,
        'machine learning': 0.7,
      },
    ),

    // Chat Cases
    const BenchmarkCase(
      name: 'Investment Discussion Chat',
      content: '''
[2024-03-15, 10:30 AM] John: What do you think about NVDA's latest earnings?
[2024-03-15, 10:31 AM] Sarah: The AI chip demand is incredible
[2024-03-15, 10:32 AM] John: Yes, their data center revenue is breaking records
[2024-03-15, 10:33 AM] Sarah: Competition from AMD might be a concern though
''',
      title: 'Investment Group Chat',
      type: 'chat',
      expectedKeywords: ['nvda', 'ai', 'chip', 'data center', 'amd'],
      expectedScores: {
        'nvda': 0.9,
        'ai': 0.8,
        'chip': 0.7,
        'data center': 0.7,
        'amd': 0.6,
      },
    ),
  ];

  group('Keyword Extraction Benchmark', () {
    late TextAnalysisService service;
    final results = <String, Map<String, dynamic>>{};

    setUp(() async {
      service = await TextAnalysisService.initialize();
    });

    for (final testCase in benchmarkCases) {
      test('${testCase.type.toUpperCase()}: ${testCase.name}', () async {
        final stopwatch = Stopwatch()..start();

        // Extract keywords
        final keywords = await service.extractKeywords(
          testCase.content,
          title: testCase.title,
          collectMetrics: true,
        );

        final extractionTime = stopwatch.elapsedMilliseconds;

        // Calculate metrics
        final metrics = _calculateMetrics(
          keywords,
          testCase.expectedKeywords,
          testCase.expectedScores,
        );

        // Store results
        results[testCase.name] = {
          'type': testCase.type,
          'extraction_time_ms': extractionTime,
          'precision': metrics.precision,
          'recall': metrics.recall,
          'f1_score': metrics.f1Score,
          'keywords': keywords,
        };

        // Log results
        logResult('\n=== Test Results: ${testCase.name} ===');
        logResult('Content: ${testCase.content}');
        logResult('Title: ${testCase.title ?? "N/A"}');
        logResult('Type: ${testCase.type}');
        logResult('Extraction Time: ${extractionTime}ms');
        logResult('Precision: ${(metrics.precision * 100).toStringAsFixed(1)}%');
        logResult('Recall: ${(metrics.recall * 100).toStringAsFixed(1)}%');
        logResult('F1 Score: ${(metrics.f1Score * 100).toStringAsFixed(1)}%');
        logResult('Extracted Keywords: ${keywords.join(', ')}');
        logResult('Expected Keywords: ${testCase.expectedKeywords.join(', ')}');
        logResult(
            'Matched Keywords: ${keywords.where((k) => testCase.expectedKeywords.contains(k)).join(', ')}');
        logResult('Expected Scores: ${testCase.expectedScores}');
        logResult('=====================================\n');

        // Assertions
        expect(metrics.precision, greaterThan(0.3), reason: 'Precision should be above 30%');
        expect(metrics.recall, greaterThan(0.3), reason: 'Recall should be above 30%');
        expect(extractionTime, lessThan(1000), reason: 'Extraction should take less than 1 second');
      });
    }

    tearDownAll(() {
      // Generate summary report
      _generateReport(results);
      logSink.close();
    });
  });
}

class ExtractionMetrics {
  final double precision;
  final double recall;
  final double f1Score;

  ExtractionMetrics({
    required this.precision,
    required this.recall,
    required this.f1Score,
  });
}

ExtractionMetrics _calculateMetrics(
  List<String> extracted,
  List<String> expected,
  Map<String, double> expectedScores,
) {
  if (extracted.isEmpty) {
    return ExtractionMetrics(
      precision: 0.0,
      recall: 0.0,
      f1Score: 0.0,
    );
  }

  final truePositives = extracted.where((k) => expected.contains(k)).length;
  final precision = truePositives / extracted.length;
  final recall = truePositives / expected.length;
  final f1Score = precision + recall > 0 ? 2 * (precision * recall) / (precision + recall) : 0.0;

  return ExtractionMetrics(
    precision: precision,
    recall: recall,
    f1Score: f1Score,
  );
}

void _generateReport(Map<String, Map<String, dynamic>> results) {
  final report = {
    'timestamp': DateTime.now().toIso8601String(),
    'summary': {
      'total_tests': results.length,
      'average_metrics': _calculateAverageMetrics(results),
      'by_type': _calculateMetricsByType(results),
    },
    'detailed_results': results,
  };

  // Save report to file
  final reportFile = File('benchmark_report.json');
  reportFile.writeAsStringSync(
    const JsonEncoder.withIndent('  ').convert(report),
  );

  // Log summary
  final summary = report['summary'] as Map<String, dynamic>;
  dev.log('=== Benchmark Summary ===');
  dev.log('Total Tests: ${summary['total_tests']}');
  dev.log('\nAverage Metrics:');
  final averageMetrics = summary['average_metrics'] as Map<String, dynamic>;
  averageMetrics.forEach((k, v) {
    dev.log('$k: ${v is double ? v.toStringAsFixed(2) : v}');
  });

  dev.log('\nMetrics by Type:');
  final byType = summary['by_type'] as Map<String, dynamic>;
  byType.forEach((type, metricsObj) {
    dev.log('\n$type:');
    final metrics = metricsObj as Map<String, dynamic>;
    metrics.forEach((k, v) {
      dev.log('  $k: ${v is double ? v.toStringAsFixed(2) : v}');
    });
  });
}

Map<String, dynamic> _calculateAverageMetrics(
  Map<String, Map<String, dynamic>> results,
) {
  final metrics = {
    'extraction_time_ms': 0.0,
    'precision': 0.0,
    'recall': 0.0,
    'f1_score': 0.0,
  };

  for (final result in results.values) {
    metrics['extraction_time_ms'] =
        (metrics['extraction_time_ms']! + (result['extraction_time_ms'] as num));
    metrics['precision'] = (metrics['precision']! + (result['precision'] as num));
    metrics['recall'] = (metrics['recall']! + (result['recall'] as num));
    metrics['f1_score'] = (metrics['f1_score']! + (result['f1_score'] as num));
  }

  final count = results.length;
  return metrics.map((k, v) => MapEntry(k, v / count));
}

Map<String, Map<String, dynamic>> _calculateMetricsByType(
  Map<String, Map<String, dynamic>> results,
) {
  final metricsByType = <String, List<Map<String, dynamic>>>{};

  // Group results by type
  for (final result in results.entries) {
    final type = result.value['type'] as String;
    metricsByType.putIfAbsent(type, () => []).add(result.value);
  }

  // Calculate averages for each type
  return metricsByType.map((type, typeResults) {
    return MapEntry(
        type,
        _calculateAverageMetrics(
          Map.fromIterables(
            List.generate(typeResults.length, (i) => 'test_$i'),
            typeResults,
          ),
        ));
  });
}
