import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
// InputContentInfo is no longer used after refactoring
import 'package:promz/core/providers/service_providers.dart';
import 'package:promz/core/providers/shared_content_provider.dart';
import 'package:promz/core/services/attachment/attachment_registry_service.dart';
import 'package:promz/core/services/client_context_service.dart';
import 'package:promz/core/services/content_processing_service.dart';
import 'package:promz/core/services/file_processing_service.dart';
import 'package:promz/core/services/license_manager_service.dart';
import 'package:promz/core/services/shared_content_handler.dart';
import 'package:promz/features/home/<USER>/entity_detection_service.dart';
import 'package:promz/features/home/<USER>/prompt_suggestion_service.dart';
import 'package:promz/features/home/<USER>/home_viewmodel.dart';
import 'package:promz/features/input_selection/models/input_source.dart';
import 'package:promz_common/promz_common.dart';
import '../../../database/database_test_base.dart';
import 'home_viewmodel_test.mocks.dart';

// Mock for ProviderSubscription
class MockProviderSubscription<T> extends Mock implements ProviderSubscription<T> {
  @override
  void close() {}
}

@GenerateMocks([
  ClientContextService,
  FileProcessingService,
  PromptSuggestionService,
  EntityDetectionService,
  AttachmentRegistryService,
  ContentProcessingService,
  SharedContentHandler,
  LicenseManagerService,
], customMocks: [
  MockSpec<Ref>(as: #MockProviderRef),
])
class _DatabaseTest extends DatabaseTestBase {
  // Use the parent class teardown method directly
}

void main() {
  AppLogger.setLogLevelForTest(LogLevel.error);
  late _DatabaseTest testDb;
  late MockProviderRef mockRef;
  late MockClientContextService mockClientContextService;
  late MockFileProcessingService mockFileProcessingService;
  late MockPromptSuggestionService mockPromptSuggestionService;
  late MockEntityDetectionService mockEntityDetectionService;
  late MockAttachmentRegistryService mockAttachmentRegistry;
  late MockContentProcessingService mockContentProcessingService;
  late MockLicenseManagerService mockLicenseManagerService;
  late HomeViewModel viewModel;

  // Provide dummy values for mocks
  provideDummy<ClientContextService>(MockClientContextService());
  provideDummy<FileProcessingService>(MockFileProcessingService());
  provideDummy<AsyncValue<ClientContextService>>(AsyncData(MockClientContextService()));

  // Create a stream controller for the sourceProcessed stream
  late StreamController<InputSource> sourceProcessedController;

  setUp(() async {
    testDb = _DatabaseTest();
    await testDb.setup();

    // Create mock services
    mockRef = MockProviderRef();
    mockClientContextService = MockClientContextService();
    mockFileProcessingService = MockFileProcessingService();
    mockPromptSuggestionService = MockPromptSuggestionService();
    mockEntityDetectionService = MockEntityDetectionService();
    mockAttachmentRegistry = MockAttachmentRegistryService();
    mockContentProcessingService = MockContentProcessingService();
    mockLicenseManagerService = MockLicenseManagerService();

    // Create a new stream controller for each test
    sourceProcessedController = StreamController<InputSource>.broadcast();

    // Setup mock behaviors
    when(mockRef.read(clientContextServiceProvider))
        .thenReturn(AsyncData(mockClientContextService));
    when(mockRef.read(fileProcessingServiceProvider)).thenReturn(mockFileProcessingService);

    // Setup for Riverpod listen method
    when(mockRef.listen<AsyncValue<InputSource>>(sharedContentStreamProvider, any))
        .thenReturn(MockProviderSubscription<AsyncValue<InputSource>>());
    when(mockClientContextService.promptSuggestion).thenReturn(mockPromptSuggestionService);
    when(mockClientContextService.entityDetection).thenReturn(mockEntityDetectionService);
    when(mockClientContextService.attachmentRegistry).thenReturn(mockAttachmentRegistry);
    // No need to mock listener methods since we disabled the attachment registry listener
    when(mockAttachmentRegistry.getAllServerAttachments()).thenReturn([]);
    when(mockClientContextService.contentProcessingService)
        .thenReturn(mockContentProcessingService);

    // Mock the sourceProcessed stream
    when(mockContentProcessingService.sourceProcessed)
        .thenAnswer((_) => sourceProcessedController.stream);

    // Instead of mocking notifySourceProcessed, we'll directly add sources to the stream
    // in our tests when needed
    when(mockClientContextService.getAllVariableValues()).thenReturn({});
    when(mockFileProcessingService.getMimeType(any)).thenReturn('application/pdf');

    // Setup LicenseManagerService mock
    when(mockLicenseManagerService.isLicenseValid()).thenReturn(true);
    // No need to mock isInitialized as it's not used directly in our tests

    // Add stub for getPromptSuggestions
    when(mockPromptSuggestionService.getPromptSuggestions(any)).thenAnswer((_) async => []);

    // Add stub for getPopularPrompts
    when(mockPromptSuggestionService.getPopularPrompts()).thenAnswer((_) async => []);

    // Setup basic mocks that will be overridden in specific tests
    when(mockContentProcessingService.checkForDuplicate(any, any, any)).thenReturn(false);

    // Create a default mock response for processInput
    when(mockContentProcessingService.processInput('/path/to/file.pdf'))
        .thenAnswer((invocation) async {
      final input = invocation.positionalArguments[0] as String;
      return InputSource(
        type: InputSourceType.text,
        filePath: input,
        fileName: 'test.txt',
        mimeType: 'text/plain',
        content: 'Test content',
        contentHash: 'test-hash',
      );
    });

    // Add stub for getAttachment to handle attachment metadata
    when(mockAttachmentRegistry.getAttachment(any)).thenReturn({
      'content': 'Test content',
      'type': 'file',
      'fileName': 'test.pdf',
      'displayName': 'Test File',
      'metadata': {
        MetadataKeys.title: 'Test File',
        MetadataKeys.url: 'https://example.com/test.pdf',
      },
      'timestamp': DateTime.now().toIso8601String(),
    });

    // Initialize the view model with attachment registry listener disabled for testing
    viewModel = HomeViewModel(
      ref: mockRef,
      database: Future.value(testDb.database),
      enableAttachmentRegistryListener:
          false, // Disable the attachment registry listener for testing
    );

    // Enable bypass for duplicate checking in tests
    viewModel.bypassDuplicateChecking = true;

    // Allow time for the database to initialize
    await Future.delayed(const Duration(milliseconds: 100));
  });

  // Only teardown the database after all tests have completed
  tearDown(() async {
    // Close the stream controller
    sourceProcessedController.close();
    await testDb.teardown();
  });

  group('HomeViewModel', () {
    test('_listenToSharedContent subscribes to the shared content stream', () async {
      // This test verifies that the view model subscribes to the shared content stream
      // The subscription is set up in the constructor, so we just need to verify it was called
      verify(mockRef.listen<AsyncValue<InputSource>>(sharedContentStreamProvider, any)).called(1);
    });
  });
}
