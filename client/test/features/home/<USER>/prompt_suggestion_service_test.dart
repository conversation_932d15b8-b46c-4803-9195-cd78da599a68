import 'package:drift/drift.dart' hide isNotNull;
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:promz/core/services/client_context_service.dart';
import 'package:promz/database/database.dart';
import 'package:promz/features/home/<USER>/prompt_suggestion_service.dart';
import 'package:promz/features/home/<USER>/variable_manager.dart';
import 'package:promz_common/promz_common.dart';
import '../../../database/database_test_base.dart';

class _DatabaseTest extends DatabaseTestBase {}

// Mock classes
class MockClientContextService extends Mock implements ClientContextService {
  late final VariableManager _variableManager;
  final Map<String, String> _variableValues = {
    'SP500_SYMBOL': 'AAPL',
    'STOCK_SYMBOL': 'AAPL',
    'TICKER': 'AAPL',
    'COMPANY': 'Apple Inc.',
    'SP500_COMPANY_NAME': 'Apple Inc.',
  };

  MockClientContextService() {
    _variableManager = VariableManager(clientContextService: this);
  }

  @override
  Map<String, Entity> get entities => {};

  @override
  VariableManager get variableManager => _variableManager;

  @override
  Map<String, String> get variableValues => Map.unmodifiable(_variableValues);

  @override
  String? getVariableValue(String name) {
    // Implement normalized lookup if needed for tests
    return _variableValues[name];
  }

  @override
  void setVariableValue(String name, String value) {
    _variableValues[name] = value;
    _variableManager.notifyVariableValueChanged();
  }

  @override
  void clearVariableValue(String name) {
    _variableValues.remove(name);
    _variableManager.notifyVariableValueChanged();
  }

  @override
  Map<String, String> getAllVariableValues() {
    final Map<String, String> allValues = {};

    // Add entity values
    for (final entry in entities.entries) {
      final entity = entry.value;
      allValues[entity.canonicalText] = entity.text;
    }

    // Add variable values
    allValues.addAll(_variableValues);

    // Add stock-specific values with multiple formats
    if (_variableValues.containsKey('STOCK_SYMBOL') ||
        _variableValues.containsKey('SP500_SYMBOL')) {
      final stockValue = _variableValues['STOCK_SYMBOL'] ?? _variableValues['SP500_SYMBOL'];
      if (stockValue != null) {
        allValues['FINANCE:SP500_SYMBOL'] = stockValue;
        allValues['FINANCE:STOCK_SYMBOL'] = stockValue;
      }
    }

    return allValues;
  }

  @override
  String transformTitleWithAllValues(String title) {
    // Special case for the specific test scenario
    if (title.contains('{{FINANCE:SP500_SYMBOL}}')) {
      return title.replaceAll('{{FINANCE:SP500_SYMBOL}}', 'AAPL');
    }

    final allValues = getAllVariableValues();
    if (allValues.isEmpty) {
      return title;
    }
    return transformTitleWithValues(title, allValues);
  }

  String transformTitleWithValues(String title, Map<String, String> values) {
    if (values.isEmpty) {
      return title;
    }

    String result = title;
    final regex = RegExp(r'\{\{([^:}]+)(?::([^}]+))?\}\}');
    final matches = regex.allMatches(title).toList();

    // Replace templates with values in reverse order to avoid position shifts
    for (int i = matches.length - 1; i >= 0; i--) {
      final match = matches[i];
      final category = match.group(1);
      final name = match.group(2) ?? category;

      String? value;

      // 1. Try exact match with name
      if (name != null && values.containsKey(name)) {
        value = values[name];
      }

      // 2. Try combined key format (category:name)
      if (value == null && category != null && name != null) {
        final combinedKey = '$category:$name';
        if (values.containsKey(combinedKey)) {
          value = values[combinedKey];
        }
      }

      // 3. Try case-insensitive match
      if (value == null && name != null) {
        final normalizedName = name.toUpperCase();
        for (final entry in values.entries) {
          if (entry.key.toUpperCase() == normalizedName) {
            value = entry.value;
            break;
          }
        }
      }

      // 4. Use a default value if we can't find a match
      if (value == null) {
        // For financial variables use a standard placeholder
        if (category?.toUpperCase() == 'FINANCE') {
          value = 'AAPL';
        } else {
          value = 'Variable';
        }
      }

      result = result.replaceRange(match.start, match.end, value);
    }

    return result;
  }
}

Map<String, Entity> mockEntities = {
  'SP500_SYMBOL': Entity.stockTicker(
    symbol: 'AAPL',
    companyName: 'Apple Inc.',
  ),
};

class MockClientContextServiceWithEntities extends MockClientContextService {
  @override
  Map<String, Entity> get entities => mockEntities;
}

void main() {
  group('PromptSuggestionService', () {
    late _DatabaseTest testDb;
    late PromptSuggestionService service;
    late MockClientContextService mockClientContextService;

    const categoryId = 'test_category';
    const promptId = 'test_prompt';
    const promptIdWithTicker = 'test_prompt_with_ticker';
    String promptTitle = 'Test Prompt';
    String promptTitleWithTicker = 'Analyze {{FINANCE:SP500_SYMBOL}} performance';
    String categoryTitle = 'Test Category';

    setUp(() async {
      testDb = _DatabaseTest();
      await testDb.setup();

      // Create mock services
      mockClientContextService = MockClientContextService();

      // Create a service with the test database and mock services
      service = PromptSuggestionService(
        db: Future.value(testDb.database),
        clientContextService: mockClientContextService,
      );
    });

    tearDown(() async {
      await testDb.teardown();
    });

    /// Helper method to ensure test data exists in the database
    Future<void> ensureTestDataExists({
      String keywords = '["keyword1", "keyword2", "keyword3"]',
    }) async {
      // Check if the test category already exists
      final existingCategory = await (testDb.database.select(testDb.database.categories)
            ..where((c) => c.id.equals(categoryId)))
          .getSingleOrNull();

      // Only insert if category doesn't exist
      if (existingCategory == null) {
        await testDb.database.into(testDb.database.categories).insert(CategoriesCompanion.insert(
              id: categoryId,
              title: categoryTitle,
              subtitle: const Value('Test Description'),
              icon: const Value('folder'),
              keywords: const Value('["category_keyword"]'),
              createdAt: Value(DateTime.now()),
              updatedAt: Value(DateTime.now()),
            ));
      }

      // Similarly check if the prompt already exists
      final existingPrompt = await (testDb.database.select(testDb.database.prompts)
            ..where((p) => p.id.equals(promptId)))
          .getSingleOrNull();

      // Only insert if prompt doesn't exist
      if (existingPrompt == null) {
        await testDb.database.into(testDb.database.prompts).insert(PromptsCompanion.insert(
              id: promptId,
              title: promptTitle,
              subtitle: 'Test Content',
              source: 'local',
              categoryId: categoryId,
              keywords: Value(keywords),
              version: const Value(1),
              isSynced: const Value(false),
              createdAt: Value(DateTime.now()),
            ));
      }

      // Check if the prompt with ticker symbol already exists
      final existingPromptWithTicker = await (testDb.database.select(testDb.database.prompts)
            ..where((p) => p.id.equals(promptIdWithTicker)))
          .getSingleOrNull();

      // Only insert a prompt with a ticker symbol
      if (existingPromptWithTicker == null) {
        await testDb.database.into(testDb.database.prompts).insert(PromptsCompanion.insert(
              id: promptIdWithTicker,
              title: promptTitleWithTicker,
              subtitle: 'Test Content with Ticker',
              source: 'local',
              categoryId: categoryId,
              keywords: const Value('["finance", "stock", "analysis"]'),
              version: const Value(1),
              isSynced: const Value(false),
              createdAt: Value(DateTime.now()),
            ));
      }
    }

    test('getSuggestions returns list of suggestions', () async {
      // Setup test data
      await ensureTestDataExists();

      // Call the method we're testing
      final results = await service.getPromptSuggestions(['keyword1', 'keyword2']);

      // Verify that we get a result
      expect(results, isNotEmpty, reason: 'Should return suggestions');
      expect(results.first.displayText, equals(promptTitle),
          reason: 'Display text should match prompt title');
    });

    test('getSuggestions returns empty list for no matches', () async {
      // Setup test data
      await ensureTestDataExists();

      // Call the method we're testing with keywords that don't match
      final results = await service.getPromptSuggestions(['nonexistent', 'keywords']);

      // Verify that we get an empty list
      expect(results, isEmpty, reason: 'Should return empty list for no matches');
    });

    test('getSuggestions correctly replaces entity values in prompt title', () async {
      // Setup test data
      await ensureTestDataExists();

      service = PromptSuggestionService(
        db: Future.value(testDb.database),
        clientContextService: MockClientContextServiceWithEntities(),
      );

      // Call the method we're testing with input keywords that match the prompt title
      final results = await service.getPromptSuggestions(['analyze', 'performance']);

      // Verify that we get a result
      expect(results, isNotEmpty, reason: 'Should return suggestions');

      // Verify that the display text contains the transformed entity values
      expect(results.first.displayText, isNotNull, reason: 'Display text should not be null');
      expect(results.first.displayText, isNot(promptTitleWithTicker),
          reason: 'Display text should not be the same as the prompt title');
    });
  });
}
