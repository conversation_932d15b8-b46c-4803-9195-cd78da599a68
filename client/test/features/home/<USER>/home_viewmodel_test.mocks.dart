// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in promz/test/features/home/<USER>/home_viewmodel_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i20;
import 'dart:io' as _i28;
import 'dart:ui' as _i23;

import 'package:flutter_riverpod/flutter_riverpod.dart' as _i17;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i19;
import 'package:promz/core/models/file/processed_file.dart' as _i24;
import 'package:promz/core/models/license_model.dart' as _i22;
import 'package:promz/core/services/api/api_client.dart' as _i9;
import 'package:promz/core/services/attachment/attachment_registry_service.dart' as _i11;
import 'package:promz/core/services/client_context_service.dart' as _i18;
import 'package:promz/core/services/content_processing_service.dart' as _i10;
import 'package:promz/core/services/file/containers/zip_service.dart' as _i16;
import 'package:promz/core/services/file/file_processing_client.dart' as _i26;
import 'package:promz/core/services/file_processing_service.dart' as _i15;
import 'package:promz/core/services/license_manager_service.dart' as _i6;
import 'package:promz/core/services/shared_content_handler.dart' as _i29;
import 'package:promz/core/services/user_profile_service.dart' as _i7;
import 'package:promz/features/home/<USER>/variable.dart' as _i21;
import 'package:promz/features/home/<USER>/entity_detection_service.dart' as _i3;
import 'package:promz/features/home/<USER>/prompt_suggestion_service.dart' as _i4;
import 'package:promz/features/home/<USER>/prompt_usage_service.dart' as _i8;
import 'package:promz/features/home/<USER>/ticker_loading_service.dart' as _i5;
import 'package:promz/features/home/<USER>/variable_manager.dart' as _i2;
import 'package:promz/features/input_selection/models/input_source.dart' as _i27;
import 'package:promz/features/news/services/news_article_service.dart' as _i14;
import 'package:promz/features/youtube/services/youtube_service.dart' as _i12;
import 'package:promz/generated/content_upload.pb.dart' as _i25;
import 'package:promz_common/promz_common.dart' as _i13;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeVariableManager_0 extends _i1.SmartFake implements _i2.VariableManager {
  _FakeVariableManager_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeEntityDetectionService_1 extends _i1.SmartFake implements _i3.EntityDetectionService {
  _FakeEntityDetectionService_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakePromptSuggestionService_2 extends _i1.SmartFake implements _i4.PromptSuggestionService {
  _FakePromptSuggestionService_2(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeTickerLoadingService_3 extends _i1.SmartFake implements _i5.TickerLoadingService {
  _FakeTickerLoadingService_3(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeLicenseManagerService_4 extends _i1.SmartFake implements _i6.LicenseManagerService {
  _FakeLicenseManagerService_4(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeUserProfileService_5 extends _i1.SmartFake implements _i7.UserProfileService {
  _FakeUserProfileService_5(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakePromptUsageService_6 extends _i1.SmartFake implements _i8.PromptUsageService {
  _FakePromptUsageService_6(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeApiClient_7 extends _i1.SmartFake implements _i9.ApiClient {
  _FakeApiClient_7(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeContentProcessingService_8 extends _i1.SmartFake
    implements _i10.ContentProcessingService {
  _FakeContentProcessingService_8(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeAttachmentRegistryService_9 extends _i1.SmartFake
    implements _i11.AttachmentRegistryService {
  _FakeAttachmentRegistryService_9(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeYouTubeService_10 extends _i1.SmartFake implements _i12.YouTubeService {
  _FakeYouTubeService_10(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeEntity_11 extends _i1.SmartFake implements _i13.Entity {
  _FakeEntity_11(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeNewsArticleService_12 extends _i1.SmartFake implements _i14.NewsArticleService {
  _FakeNewsArticleService_12(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeFileProcessingService_13 extends _i1.SmartFake implements _i15.FileProcessingService {
  _FakeFileProcessingService_13(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeZipService_14 extends _i1.SmartFake implements _i16.ZipService {
  _FakeZipService_14(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeProviderContainer_15 extends _i1.SmartFake implements _i17.ProviderContainer {
  _FakeProviderContainer_15(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeKeepAliveLink_16 extends _i1.SmartFake implements _i17.KeepAliveLink {
  _FakeKeepAliveLink_16(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeProviderSubscription_17<State1> extends _i1.SmartFake
    implements _i17.ProviderSubscription<State1> {
  _FakeProviderSubscription_17(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [ClientContextService].
///
/// See the documentation for Mockito's code generation for more information.
class MockClientContextService extends _i1.Mock implements _i18.ClientContextService {
  MockClientContextService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.VariableManager get variableManager => (super.noSuchMethod(
        Invocation.getter(#variableManager),
        returnValue: _FakeVariableManager_0(
          this,
          Invocation.getter(#variableManager),
        ),
      ) as _i2.VariableManager);

  @override
  _i3.EntityDetectionService get entityDetection => (super.noSuchMethod(
        Invocation.getter(#entityDetection),
        returnValue: _FakeEntityDetectionService_1(
          this,
          Invocation.getter(#entityDetection),
        ),
      ) as _i3.EntityDetectionService);

  @override
  _i4.PromptSuggestionService get promptSuggestion => (super.noSuchMethod(
        Invocation.getter(#promptSuggestion),
        returnValue: _FakePromptSuggestionService_2(
          this,
          Invocation.getter(#promptSuggestion),
        ),
      ) as _i4.PromptSuggestionService);

  @override
  _i5.TickerLoadingService get tickerLoading => (super.noSuchMethod(
        Invocation.getter(#tickerLoading),
        returnValue: _FakeTickerLoadingService_3(
          this,
          Invocation.getter(#tickerLoading),
        ),
      ) as _i5.TickerLoadingService);

  @override
  _i6.LicenseManagerService get licenseManager => (super.noSuchMethod(
        Invocation.getter(#licenseManager),
        returnValue: _FakeLicenseManagerService_4(
          this,
          Invocation.getter(#licenseManager),
        ),
      ) as _i6.LicenseManagerService);

  @override
  _i7.UserProfileService get userProfile => (super.noSuchMethod(
        Invocation.getter(#userProfile),
        returnValue: _FakeUserProfileService_5(
          this,
          Invocation.getter(#userProfile),
        ),
      ) as _i7.UserProfileService);

  @override
  _i8.PromptUsageService get promptUsage => (super.noSuchMethod(
        Invocation.getter(#promptUsage),
        returnValue: _FakePromptUsageService_6(
          this,
          Invocation.getter(#promptUsage),
        ),
      ) as _i8.PromptUsageService);

  @override
  _i9.ApiClient get apiClient => (super.noSuchMethod(
        Invocation.getter(#apiClient),
        returnValue: _FakeApiClient_7(
          this,
          Invocation.getter(#apiClient),
        ),
      ) as _i9.ApiClient);

  @override
  _i10.ContentProcessingService get contentProcessing => (super.noSuchMethod(
        Invocation.getter(#contentProcessing),
        returnValue: _FakeContentProcessingService_8(
          this,
          Invocation.getter(#contentProcessing),
        ),
      ) as _i10.ContentProcessingService);

  @override
  set contentProcessingService(_i10.ContentProcessingService? service) => super.noSuchMethod(
        Invocation.setter(
          #contentProcessingService,
          service,
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i10.ContentProcessingService get contentProcessingService => (super.noSuchMethod(
        Invocation.getter(#contentProcessingService),
        returnValue: _FakeContentProcessingService_8(
          this,
          Invocation.getter(#contentProcessingService),
        ),
      ) as _i10.ContentProcessingService);

  @override
  _i11.AttachmentRegistryService get attachmentRegistry => (super.noSuchMethod(
        Invocation.getter(#attachmentRegistry),
        returnValue: _FakeAttachmentRegistryService_9(
          this,
          Invocation.getter(#attachmentRegistry),
        ),
      ) as _i11.AttachmentRegistryService);

  @override
  _i12.YouTubeService get youtube => (super.noSuchMethod(
        Invocation.getter(#youtube),
        returnValue: _FakeYouTubeService_10(
          this,
          Invocation.getter(#youtube),
        ),
      ) as _i12.YouTubeService);

  @override
  Map<String, _i13.Entity> get entities => (super.noSuchMethod(
        Invocation.getter(#entities),
        returnValue: <String, _i13.Entity>{},
      ) as Map<String, _i13.Entity>);

  @override
  Map<String, String> get variableValues => (super.noSuchMethod(
        Invocation.getter(#variableValues),
        returnValue: <String, String>{},
      ) as Map<String, String>);

  @override
  bool get hasListeners => (super.noSuchMethod(
        Invocation.getter(#hasListeners),
        returnValue: false,
      ) as bool);

  @override
  void invalidateAttachmentCache() => super.noSuchMethod(
        Invocation.method(
          #invalidateAttachmentCache,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  String transformTitleWithAllValues(String? title) => (super.noSuchMethod(
        Invocation.method(
          #transformTitleWithAllValues,
          [title],
        ),
        returnValue: _i19.dummyValue<String>(
          this,
          Invocation.method(
            #transformTitleWithAllValues,
            [title],
          ),
        ),
      ) as String);

  @override
  _i20.Future<void> detectEntities(String? text) => (super.noSuchMethod(
        Invocation.method(
          #detectEntities,
          [text],
        ),
        returnValue: _i20.Future<void>.value(),
        returnValueForMissingStub: _i20.Future<void>.value(),
      ) as _i20.Future<void>);

  @override
  void updateEntities(List<_i13.Entity>? newEntities) => super.noSuchMethod(
        Invocation.method(
          #updateEntities,
          [newEntities],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void setVariableValue(
    String? name,
    String? value,
  ) =>
      super.noSuchMethod(
        Invocation.method(
          #setVariableValue,
          [
            name,
            value,
          ],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void clearVariableValue(String? name) => super.noSuchMethod(
        Invocation.method(
          #clearVariableValue,
          [name],
        ),
        returnValueForMissingStub: null,
      );

  @override
  String? getVariableValue(String? name) => (super.noSuchMethod(Invocation.method(
        #getVariableValue,
        [name],
      )) as String?);

  @override
  Map<String, String> getAllVariableValues() => (super.noSuchMethod(
        Invocation.method(
          #getAllVariableValues,
          [],
        ),
        returnValue: <String, String>{},
      ) as Map<String, String>);

  @override
  List<_i21.Variable> getVariablesWithValues() => (super.noSuchMethod(
        Invocation.method(
          #getVariablesWithValues,
          [],
        ),
        returnValue: <_i21.Variable>[],
      ) as List<_i21.Variable>);

  @override
  void clearAllVariableValues() => super.noSuchMethod(
        Invocation.method(
          #clearAllVariableValues,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void updateLicenseState(_i22.UserLicense? license) => super.noSuchMethod(
        Invocation.method(
          #updateLicenseState,
          [license],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void addListener(_i23.VoidCallback? listener) => super.noSuchMethod(
        Invocation.method(
          #addListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void removeListener(_i23.VoidCallback? listener) => super.noSuchMethod(
        Invocation.method(
          #removeListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void notifyListeners() => super.noSuchMethod(
        Invocation.method(
          #notifyListeners,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [FileProcessingService].
///
/// See the documentation for Mockito's code generation for more information.
class MockFileProcessingService extends _i1.Mock implements _i15.FileProcessingService {
  MockFileProcessingService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool isZipFile(
    String? filePath, [
    String? mimeType,
  ]) =>
      (super.noSuchMethod(
        Invocation.method(
          #isZipFile,
          [
            filePath,
            mimeType,
          ],
        ),
        returnValue: false,
      ) as bool);

  @override
  bool isTextFile(
    String? filePath, [
    String? mimeType,
  ]) =>
      (super.noSuchMethod(
        Invocation.method(
          #isTextFile,
          [
            filePath,
            mimeType,
          ],
        ),
        returnValue: false,
      ) as bool);

  @override
  bool isPdfFile(
    String? filePath, [
    String? mimeType,
  ]) =>
      (super.noSuchMethod(
        Invocation.method(
          #isPdfFile,
          [
            filePath,
            mimeType,
          ],
        ),
        returnValue: false,
      ) as bool);

  @override
  bool isWhatsAppExport(String? filePath) => (super.noSuchMethod(
        Invocation.method(
          #isWhatsAppExport,
          [filePath],
        ),
        returnValue: false,
      ) as bool);

  @override
  _i20.Future<String?> getFileContent(
    String? filePath, [
    String? mimeType,
  ]) =>
      (super.noSuchMethod(
        Invocation.method(
          #getFileContent,
          [
            filePath,
            mimeType,
          ],
        ),
        returnValue: _i20.Future<String?>.value(),
      ) as _i20.Future<String?>);

  @override
  String? getTextContent(
    String? filePath, [
    String? mimeType,
  ]) =>
      (super.noSuchMethod(Invocation.method(
        #getTextContent,
        [
          filePath,
          mimeType,
        ],
      )) as String?);

  @override
  _i20.Future<List<_i24.ProcessedFile>> processZipFile(String? filePath) => (super.noSuchMethod(
        Invocation.method(
          #processZipFile,
          [filePath],
        ),
        returnValue: _i20.Future<List<_i24.ProcessedFile>>.value(<_i24.ProcessedFile>[]),
      ) as _i20.Future<List<_i24.ProcessedFile>>);

  @override
  String getMimeType(String? fileName) => (super.noSuchMethod(
        Invocation.method(
          #getMimeType,
          [fileName],
        ),
        returnValue: _i19.dummyValue<String>(
          this,
          Invocation.method(
            #getMimeType,
            [fileName],
          ),
        ),
      ) as String);

  @override
  String determineSourceApp(String? filePath) => (super.noSuchMethod(
        Invocation.method(
          #determineSourceApp,
          [filePath],
        ),
        returnValue: _i19.dummyValue<String>(
          this,
          Invocation.method(
            #determineSourceApp,
            [filePath],
          ),
        ),
      ) as String);

  @override
  Map<String, dynamic> extractWhatsAppMetadata(
    String? content,
    String? fileName,
    String? filePath,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #extractWhatsAppMetadata,
          [
            content,
            fileName,
            filePath,
          ],
        ),
        returnValue: <String, dynamic>{},
      ) as Map<String, dynamic>);
}

/// A class which mocks [PromptSuggestionService].
///
/// See the documentation for Mockito's code generation for more information.
class MockPromptSuggestionService extends _i1.Mock implements _i4.PromptSuggestionService {
  MockPromptSuggestionService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i20.Future<List<_i13.DisplayItem>> getPromptSuggestions(
    List<String>? keywords, {
    _i4.PromptSuggestionMode? mode = _i4.PromptSuggestionMode.semantic,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getPromptSuggestions,
          [keywords],
          {#mode: mode},
        ),
        returnValue: _i20.Future<List<_i13.DisplayItem>>.value(<_i13.DisplayItem>[]),
      ) as _i20.Future<List<_i13.DisplayItem>>);

  @override
  _i20.Future<List<_i13.DisplayItem>> getSuggestedPrompts(List<String>? keywords) =>
      (super.noSuchMethod(
        Invocation.method(
          #getSuggestedPrompts,
          [keywords],
        ),
        returnValue: _i20.Future<List<_i13.DisplayItem>>.value(<_i13.DisplayItem>[]),
      ) as _i20.Future<List<_i13.DisplayItem>>);

  @override
  _i20.Future<List<_i13.DisplayItem>> getRecentPrompts() => (super.noSuchMethod(
        Invocation.method(
          #getRecentPrompts,
          [],
        ),
        returnValue: _i20.Future<List<_i13.DisplayItem>>.value(<_i13.DisplayItem>[]),
      ) as _i20.Future<List<_i13.DisplayItem>>);

  @override
  _i20.Future<List<_i13.DisplayItem>> getPopularPrompts() => (super.noSuchMethod(
        Invocation.method(
          #getPopularPrompts,
          [],
        ),
        returnValue: _i20.Future<List<_i13.DisplayItem>>.value(<_i13.DisplayItem>[]),
      ) as _i20.Future<List<_i13.DisplayItem>>);
}

/// A class which mocks [EntityDetectionService].
///
/// See the documentation for Mockito's code generation for more information.
class MockEntityDetectionService extends _i1.Mock implements _i3.EntityDetectionService {
  MockEntityDetectionService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i20.Future<void> loadEntityDictionaries() => (super.noSuchMethod(
        Invocation.method(
          #loadEntityDictionaries,
          [],
        ),
        returnValue: _i20.Future<void>.value(),
        returnValueForMissingStub: _i20.Future<void>.value(),
      ) as _i20.Future<void>);

  @override
  _i13.Entity? getStockBySymbol(String? symbol) => (super.noSuchMethod(Invocation.method(
        #getStockBySymbol,
        [symbol],
      )) as _i13.Entity?);

  @override
  _i13.Entity? getStockByCompanyName(String? companyName) => (super.noSuchMethod(Invocation.method(
        #getStockByCompanyName,
        [companyName],
      )) as _i13.Entity?);

  @override
  _i13.Entity? getNewsEntityByUrl(String? url) => (super.noSuchMethod(Invocation.method(
        #getNewsEntityByUrl,
        [url],
      )) as _i13.Entity?);

  @override
  _i13.Entity? getYouTubeEntityByVideoId(String? videoId) => (super.noSuchMethod(Invocation.method(
        #getYouTubeEntityByVideoId,
        [videoId],
      )) as _i13.Entity?);

  @override
  _i13.Entity registerNewsArticle({
    required String? url,
    required String? contents,
    String? title,
    String? author,
    String? excerpt,
    String? siteName,
    String? finalUrl,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #registerNewsArticle,
          [],
          {
            #url: url,
            #contents: contents,
            #title: title,
            #author: author,
            #excerpt: excerpt,
            #siteName: siteName,
            #finalUrl: finalUrl,
          },
        ),
        returnValue: _FakeEntity_11(
          this,
          Invocation.method(
            #registerNewsArticle,
            [],
            {
              #url: url,
              #contents: contents,
              #title: title,
              #author: author,
              #excerpt: excerpt,
              #siteName: siteName,
              #finalUrl: finalUrl,
            },
          ),
        ),
      ) as _i13.Entity);

  @override
  _i13.Entity registerYouTubeVideo({
    required String? videoId,
    required String? url,
    required String? title,
    required String? channelName,
    String? thumbnailUrl,
    String? description,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #registerYouTubeVideo,
          [],
          {
            #videoId: videoId,
            #url: url,
            #title: title,
            #channelName: channelName,
            #thumbnailUrl: thumbnailUrl,
            #description: description,
          },
        ),
        returnValue: _FakeEntity_11(
          this,
          Invocation.method(
            #registerYouTubeVideo,
            [],
            {
              #videoId: videoId,
              #url: url,
              #title: title,
              #channelName: channelName,
              #thumbnailUrl: thumbnailUrl,
              #description: description,
            },
          ),
        ),
      ) as _i13.Entity);

  @override
  void addEntity({
    required String? id,
    required String? text,
    required String? type,
    required _i25.ProcessingResult? processingResult,
    Map<String, dynamic>? metadata,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #addEntity,
          [],
          {
            #id: id,
            #text: text,
            #type: type,
            #processingResult: processingResult,
            #metadata: metadata,
          },
        ),
        returnValueForMissingStub: null,
      );

  @override
  void registerEntity({
    required String? id,
    required String? text,
    required String? type,
    required _i25.ProcessingResult? processingResult,
    Map<String, dynamic>? metadata,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #registerEntity,
          [],
          {
            #id: id,
            #text: text,
            #type: type,
            #processingResult: processingResult,
            #metadata: metadata,
          },
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i20.Future<List<_i13.Entity>> detectEntities(String? text) => (super.noSuchMethod(
        Invocation.method(
          #detectEntities,
          [text],
        ),
        returnValue: _i20.Future<List<_i13.Entity>>.value(<_i13.Entity>[]),
      ) as _i20.Future<List<_i13.Entity>>);

  @override
  Map<String, dynamic> getValuesBySymbol(String? symbol) => (super.noSuchMethod(
        Invocation.method(
          #getValuesBySymbol,
          [symbol],
        ),
        returnValue: <String, dynamic>{},
      ) as Map<String, dynamic>);

  @override
  Map<String, dynamic> getValuesByVideoId(String? videoId) => (super.noSuchMethod(
        Invocation.method(
          #getValuesByVideoId,
          [videoId],
        ),
        returnValue: <String, dynamic>{},
      ) as Map<String, dynamic>);

  @override
  Map<String, dynamic> getValuesByConversationId(
    String? attachmentId,
    Map<String, dynamic>? attachment,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #getValuesByConversationId,
          [
            attachmentId,
            attachment,
          ],
        ),
        returnValue: <String, dynamic>{},
      ) as Map<String, dynamic>);

  @override
  Map<String, dynamic> getVariablePackage(
    _i13.EntityType? type,
    String? primaryId, {
    Map<String, dynamic>? additionalData,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getVariablePackage,
          [
            type,
            primaryId,
          ],
          {#additionalData: additionalData},
        ),
        returnValue: <String, dynamic>{},
      ) as Map<String, dynamic>);

  @override
  List<_i13.Entity> getAllEntities() => (super.noSuchMethod(
        Invocation.method(
          #getAllEntities,
          [],
        ),
        returnValue: <_i13.Entity>[],
      ) as List<_i13.Entity>);

  @override
  String? getDisplayTextForFinance(String? nameOrTicker) => (super.noSuchMethod(Invocation.method(
        #getDisplayTextForFinance,
        [nameOrTicker],
      )) as String?);
}

/// A class which mocks [AttachmentRegistryService].
///
/// See the documentation for Mockito's code generation for more information.
class MockAttachmentRegistryService extends _i1.Mock implements _i11.AttachmentRegistryService {
  MockAttachmentRegistryService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get hasListeners => (super.noSuchMethod(
        Invocation.getter(#hasListeners),
        returnValue: false,
      ) as bool);

  @override
  void registerAttachment({
    required String? id,
    required _i25.ProcessingResult? result,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #registerAttachment,
          [],
          {
            #id: id,
            #result: result,
          },
        ),
        returnValueForMissingStub: null,
      );

  @override
  void registerServerAttachment({
    required String? id,
    required _i25.ProcessingResult? initialResult,
    required _i26.FileProcessingClient? client,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #registerServerAttachment,
          [],
          {
            #id: id,
            #initialResult: initialResult,
            #client: client,
          },
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i20.Future<_i25.ProcessingResult?> getAttachmentProto(String? id) => (super.noSuchMethod(
        Invocation.method(
          #getAttachmentProto,
          [id],
        ),
        returnValue: _i20.Future<_i25.ProcessingResult?>.value(),
      ) as _i20.Future<_i25.ProcessingResult?>);

  @override
  _i20.Future<String?> getAttachmentContentProto(
    String? id,
    _i26.FileProcessingClient? client,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #getAttachmentContentProto,
          [
            id,
            client,
          ],
        ),
        returnValue: _i20.Future<String?>.value(),
      ) as _i20.Future<String?>);

  @override
  bool isServerReference(String? content) => (super.noSuchMethod(
        Invocation.method(
          #isServerReference,
          [content],
        ),
        returnValue: false,
      ) as bool);

  @override
  bool isUrlReference(String? content) => (super.noSuchMethod(
        Invocation.method(
          #isUrlReference,
          [content],
        ),
        returnValue: false,
      ) as bool);

  @override
  bool isReference(String? content) => (super.noSuchMethod(
        Invocation.method(
          #isReference,
          [content],
        ),
        returnValue: false,
      ) as bool);

  @override
  String? extractServerId(String? content) => (super.noSuchMethod(Invocation.method(
        #extractServerId,
        [content],
      )) as String?);

  @override
  String? extractContentUrl(String? content) => (super.noSuchMethod(Invocation.method(
        #extractContentUrl,
        [content],
      )) as String?);

  @override
  _i20.Future<String?> getAttachmentContent(
    String? id,
    _i26.FileProcessingClient? client,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #getAttachmentContent,
          [
            id,
            client,
          ],
        ),
        returnValue: _i20.Future<String?>.value(),
      ) as _i20.Future<String?>);

  @override
  Map<String, dynamic>? getAttachment(String? id) => (super.noSuchMethod(Invocation.method(
        #getAttachment,
        [id],
      )) as Map<String, dynamic>?);

  @override
  _i25.ProcessingResult? getMostRecentAttachmentByTypeProto(String? type) =>
      (super.noSuchMethod(Invocation.method(
        #getMostRecentAttachmentByTypeProto,
        [type],
      )) as _i25.ProcessingResult?);

  @override
  bool hasAttachmentOfType(String? type) => (super.noSuchMethod(
        Invocation.method(
          #hasAttachmentOfType,
          [type],
        ),
        returnValue: false,
      ) as bool);

  @override
  bool hasAttachmentOfTypeProto(String? type) => (super.noSuchMethod(
        Invocation.method(
          #hasAttachmentOfTypeProto,
          [type],
        ),
        returnValue: false,
      ) as bool);

  @override
  void removeAttachment(String? id) => super.noSuchMethod(
        Invocation.method(
          #removeAttachment,
          [id],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void clearAttachments() => super.noSuchMethod(
        Invocation.method(
          #clearAttachments,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  List<_i25.ProcessingResult> getAllServerAttachments() => (super.noSuchMethod(
        Invocation.method(
          #getAllServerAttachments,
          [],
        ),
        returnValue: <_i25.ProcessingResult>[],
      ) as List<_i25.ProcessingResult>);

  @override
  Map<String, _i25.ProcessingResult?> getAllAttachmentsByType() => (super.noSuchMethod(
        Invocation.method(
          #getAllAttachmentsByType,
          [],
        ),
        returnValue: <String, _i25.ProcessingResult?>{},
      ) as Map<String, _i25.ProcessingResult?>);

  @override
  void addListener(_i23.VoidCallback? listener) => super.noSuchMethod(
        Invocation.method(
          #addListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void removeListener(_i23.VoidCallback? listener) => super.noSuchMethod(
        Invocation.method(
          #removeListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void notifyListeners() => super.noSuchMethod(
        Invocation.method(
          #notifyListeners,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [ContentProcessingService].
///
/// See the documentation for Mockito's code generation for more information.
class MockContentProcessingService extends _i1.Mock implements _i10.ContentProcessingService {
  MockContentProcessingService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i20.Stream<_i10.ContentProcessingStatus> get processingStatus => (super.noSuchMethod(
        Invocation.getter(#processingStatus),
        returnValue: _i20.Stream<_i10.ContentProcessingStatus>.empty(),
      ) as _i20.Stream<_i10.ContentProcessingStatus>);

  @override
  _i20.Stream<void> get authenticationRequired => (super.noSuchMethod(
        Invocation.getter(#authenticationRequired),
        returnValue: _i20.Stream<void>.empty(),
      ) as _i20.Stream<void>);

  @override
  _i20.Stream<Exception> get fileSizeLimitExceeded => (super.noSuchMethod(
        Invocation.getter(#fileSizeLimitExceeded),
        returnValue: _i20.Stream<Exception>.empty(),
      ) as _i20.Stream<Exception>);

  @override
  _i20.Stream<_i27.InputSource> get sourceProcessed => (super.noSuchMethod(
        Invocation.getter(#sourceProcessed),
        returnValue: _i20.Stream<_i27.InputSource>.empty(),
      ) as _i20.Stream<_i27.InputSource>);

  @override
  _i20.Future<void> validateFileSize(
    _i28.File? file, {
    String? licenseTier,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #validateFileSize,
          [file],
          {#licenseTier: licenseTier},
        ),
        returnValue: _i20.Future<void>.value(),
        returnValueForMissingStub: _i20.Future<void>.value(),
      ) as _i20.Future<void>);

  @override
  _i20.Future<_i27.InputSource?> processInput(String? input) => (super.noSuchMethod(
        Invocation.method(
          #processInput,
          [input],
        ),
        returnValue: _i20.Future<_i27.InputSource?>.value(),
      ) as _i20.Future<_i27.InputSource?>);

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  bool checkForDuplicate(
    String? filePath,
    String? contentHash,
    List<_i27.InputSource>? existingSources,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #checkForDuplicate,
          [
            filePath,
            contentHash,
            existingSources,
          ],
        ),
        returnValue: false,
      ) as bool);

  @override
  _i25.ProcessingResult? getAttachmentByType(String? type) => (super.noSuchMethod(Invocation.method(
        #getAttachmentByType,
        [type],
      )) as _i25.ProcessingResult?);

  @override
  _i11.AttachmentRegistryService getAttachmentRegistry() => (super.noSuchMethod(
        Invocation.method(
          #getAttachmentRegistry,
          [],
        ),
        returnValue: _FakeAttachmentRegistryService_9(
          this,
          Invocation.method(
            #getAttachmentRegistry,
            [],
          ),
        ),
      ) as _i11.AttachmentRegistryService);

  @override
  _i14.NewsArticleService getNewsArticleService() => (super.noSuchMethod(
        Invocation.method(
          #getNewsArticleService,
          [],
        ),
        returnValue: _FakeNewsArticleService_12(
          this,
          Invocation.method(
            #getNewsArticleService,
            [],
          ),
        ),
      ) as _i14.NewsArticleService);

  @override
  _i15.FileProcessingService getFileProcessingService() => (super.noSuchMethod(
        Invocation.method(
          #getFileProcessingService,
          [],
        ),
        returnValue: _FakeFileProcessingService_13(
          this,
          Invocation.method(
            #getFileProcessingService,
            [],
          ),
        ),
      ) as _i15.FileProcessingService);

  @override
  _i16.ZipService getZipService() => (super.noSuchMethod(
        Invocation.method(
          #getZipService,
          [],
        ),
        returnValue: _FakeZipService_14(
          this,
          Invocation.method(
            #getZipService,
            [],
          ),
        ),
      ) as _i16.ZipService);

  @override
  void notifySourceProcessed(_i27.InputSource? source) => super.noSuchMethod(
        Invocation.method(
          #notifySourceProcessed,
          [source],
        ),
        returnValueForMissingStub: null,
      );

  @override
  bool checkForDuplicateInternal(
    String? filePath,
    String? contentHash,
    List<_i27.InputSource>? existingSources,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #checkForDuplicateInternal,
          [
            filePath,
            contentHash,
            existingSources,
          ],
        ),
        returnValue: false,
      ) as bool);
}

/// A class which mocks [SharedContentHandler].
///
/// See the documentation for Mockito's code generation for more information.
class MockSharedContentHandler extends _i1.Mock implements _i29.SharedContentHandler {
  MockSharedContentHandler() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i20.Stream<_i27.InputSource> get sharedContent => (super.noSuchMethod(
        Invocation.getter(#sharedContent),
        returnValue: _i20.Stream<_i27.InputSource>.empty(),
      ) as _i20.Stream<_i27.InputSource>);

  @override
  _i20.Stream<void> get authenticationRequired => (super.noSuchMethod(
        Invocation.getter(#authenticationRequired),
        returnValue: _i20.Stream<void>.empty(),
      ) as _i20.Stream<void>);

  @override
  _i20.Stream<_i29.SharedProcessingStatus> get processingStatus => (super.noSuchMethod(
        Invocation.getter(#processingStatus),
        returnValue: _i20.Stream<_i29.SharedProcessingStatus>.empty(),
      ) as _i20.Stream<_i29.SharedProcessingStatus>);

  @override
  _i20.Stream<Exception> get fileSizeLimitExceeded => (super.noSuchMethod(
        Invocation.getter(#fileSizeLimitExceeded),
        returnValue: _i20.Stream<Exception>.empty(),
      ) as _i20.Stream<Exception>);

  @override
  bool get isProcessing => (super.noSuchMethod(
        Invocation.getter(#isProcessing),
        returnValue: false,
      ) as bool);

  @override
  void addProcessedContent(_i27.InputSource? source) => super.noSuchMethod(
        Invocation.method(
          #addProcessedContent,
          [source],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [LicenseManagerService].
///
/// See the documentation for Mockito's code generation for more information.
class MockLicenseManagerService extends _i1.Mock implements _i6.LicenseManagerService {
  MockLicenseManagerService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i20.Stream<_i22.UserLicense?> get licenseStream => (super.noSuchMethod(
        Invocation.getter(#licenseStream),
        returnValue: _i20.Stream<_i22.UserLicense?>.empty(),
      ) as _i20.Stream<_i22.UserLicense?>);

  @override
  bool get hasListeners => (super.noSuchMethod(
        Invocation.getter(#hasListeners),
        returnValue: false,
      ) as bool);

  @override
  bool isLicenseValid() => (super.noSuchMethod(
        Invocation.method(
          #isLicenseValid,
          [],
        ),
        returnValue: false,
      ) as bool);

  @override
  _i20.Future<bool> ensureUserHasFreeLicense() => (super.noSuchMethod(
        Invocation.method(
          #ensureUserHasFreeLicense,
          [],
        ),
        returnValue: _i20.Future<bool>.value(false),
      ) as _i20.Future<bool>);

  @override
  _i20.Future<bool> generateProTrialLicense() => (super.noSuchMethod(
        Invocation.method(
          #generateProTrialLicense,
          [],
        ),
        returnValue: _i20.Future<bool>.value(false),
      ) as _i20.Future<bool>);

  @override
  _i20.Future<bool> hasActiveProTrial() => (super.noSuchMethod(
        Invocation.method(
          #hasActiveProTrial,
          [],
        ),
        returnValue: _i20.Future<bool>.value(false),
      ) as _i20.Future<bool>);

  @override
  _i20.Future<void> trackAccountUsage(
    String? userId,
    String? email,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #trackAccountUsage,
          [
            userId,
            email,
          ],
        ),
        returnValue: _i20.Future<void>.value(),
        returnValueForMissingStub: _i20.Future<void>.value(),
      ) as _i20.Future<void>);

  @override
  _i20.Future<String?> initializeLicense() => (super.noSuchMethod(
        Invocation.method(
          #initializeLicense,
          [],
        ),
        returnValue: _i20.Future<String?>.value(),
      ) as _i20.Future<String?>);

  @override
  _i20.Future<_i22.UserLicense?> getUserLicense({bool? forceFresh = false}) => (super.noSuchMethod(
        Invocation.method(
          #getUserLicense,
          [],
          {#forceFresh: forceFresh},
        ),
        returnValue: _i20.Future<_i22.UserLicense?>.value(),
      ) as _i20.Future<_i22.UserLicense?>);

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void invalidateLicenseCache() => super.noSuchMethod(
        Invocation.method(
          #invalidateLicenseCache,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i20.Future<Map<String, dynamic>> refreshLicenseStatus() => (super.noSuchMethod(
        Invocation.method(
          #refreshLicenseStatus,
          [],
        ),
        returnValue: _i20.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i20.Future<Map<String, dynamic>>);

  @override
  _i20.Future<Map<String, dynamic>> verifyApiKey(String? apiKey) => (super.noSuchMethod(
        Invocation.method(
          #verifyApiKey,
          [apiKey],
        ),
        returnValue: _i20.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i20.Future<Map<String, dynamic>>);

  @override
  _i20.Future<Map<String, dynamic>> checkLicenseStatusDirect({bool? forceFresh = false}) =>
      (super.noSuchMethod(
        Invocation.method(
          #checkLicenseStatusDirect,
          [],
          {#forceFresh: forceFresh},
        ),
        returnValue: _i20.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i20.Future<Map<String, dynamic>>);

  @override
  _i20.Future<Map<String, dynamic>?> getOrCreateLicense(String? licenseType) => (super.noSuchMethod(
        Invocation.method(
          #getOrCreateLicense,
          [licenseType],
        ),
        returnValue: _i20.Future<Map<String, dynamic>?>.value(),
      ) as _i20.Future<Map<String, dynamic>?>);

  @override
  void addListener(_i23.VoidCallback? listener) => super.noSuchMethod(
        Invocation.method(
          #addListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void removeListener(_i23.VoidCallback? listener) => super.noSuchMethod(
        Invocation.method(
          #removeListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void notifyListeners() => super.noSuchMethod(
        Invocation.method(
          #notifyListeners,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [Ref].
///
/// See the documentation for Mockito's code generation for more information.
class MockProviderRef<State extends Object?> extends _i1.Mock implements _i17.Ref<State> {
  MockProviderRef() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i17.ProviderContainer get container => (super.noSuchMethod(
        Invocation.getter(#container),
        returnValue: _FakeProviderContainer_15(
          this,
          Invocation.getter(#container),
        ),
      ) as _i17.ProviderContainer);

  @override
  T refresh<T>(_i17.Refreshable<T>? provider) => (super.noSuchMethod(
        Invocation.method(
          #refresh,
          [provider],
        ),
        returnValue: _i19.dummyValue<T>(
          this,
          Invocation.method(
            #refresh,
            [provider],
          ),
        ),
      ) as T);

  @override
  void invalidate(_i17.ProviderOrFamily? provider) => super.noSuchMethod(
        Invocation.method(
          #invalidate,
          [provider],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void notifyListeners() => super.noSuchMethod(
        Invocation.method(
          #notifyListeners,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void listenSelf(
    void Function(
      State?,
      State,
    )? listener, {
    void Function(
      Object,
      StackTrace,
    )? onError,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #listenSelf,
          [listener],
          {#onError: onError},
        ),
        returnValueForMissingStub: null,
      );

  @override
  void invalidateSelf() => super.noSuchMethod(
        Invocation.method(
          #invalidateSelf,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void onAddListener(void Function()? cb) => super.noSuchMethod(
        Invocation.method(
          #onAddListener,
          [cb],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void onRemoveListener(void Function()? cb) => super.noSuchMethod(
        Invocation.method(
          #onRemoveListener,
          [cb],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void onResume(void Function()? cb) => super.noSuchMethod(
        Invocation.method(
          #onResume,
          [cb],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void onCancel(void Function()? cb) => super.noSuchMethod(
        Invocation.method(
          #onCancel,
          [cb],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void onDispose(void Function()? cb) => super.noSuchMethod(
        Invocation.method(
          #onDispose,
          [cb],
        ),
        returnValueForMissingStub: null,
      );

  @override
  T read<T>(_i17.ProviderListenable<T>? provider) => (super.noSuchMethod(
        Invocation.method(
          #read,
          [provider],
        ),
        returnValue: _i19.dummyValue<T>(
          this,
          Invocation.method(
            #read,
            [provider],
          ),
        ),
      ) as T);

  @override
  bool exists(_i17.ProviderBase<Object?>? provider) => (super.noSuchMethod(
        Invocation.method(
          #exists,
          [provider],
        ),
        returnValue: false,
      ) as bool);

  @override
  T watch<T>(_i17.ProviderListenable<T>? provider) => (super.noSuchMethod(
        Invocation.method(
          #watch,
          [provider],
        ),
        returnValue: _i19.dummyValue<T>(
          this,
          Invocation.method(
            #watch,
            [provider],
          ),
        ),
      ) as T);

  @override
  _i17.KeepAliveLink keepAlive() => (super.noSuchMethod(
        Invocation.method(
          #keepAlive,
          [],
        ),
        returnValue: _FakeKeepAliveLink_16(
          this,
          Invocation.method(
            #keepAlive,
            [],
          ),
        ),
      ) as _i17.KeepAliveLink);

  @override
  _i17.ProviderSubscription<T> listen<T>(
    _i17.ProviderListenable<T>? provider,
    void Function(
      T?,
      T,
    )? listener, {
    void Function(
      Object,
      StackTrace,
    )? onError,
    bool? fireImmediately,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #listen,
          [
            provider,
            listener,
          ],
          {
            #onError: onError,
            #fireImmediately: fireImmediately,
          },
        ),
        returnValue: _FakeProviderSubscription_17<T>(
          this,
          Invocation.method(
            #listen,
            [
              provider,
              listener,
            ],
            {
              #onError: onError,
              #fireImmediately: fireImmediately,
            },
          ),
        ),
      ) as _i17.ProviderSubscription<T>);
}
