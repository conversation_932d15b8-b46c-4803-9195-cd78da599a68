import 'package:flutter_test/flutter_test.dart';
import 'package:promz/features/home/<USER>/text_analysis_service.dart';

void main() {
  late TextAnalysisService service;

  setUp(() {
    service = TextAnalysisService();
  });

  group('TextAnalysisService', () {
    test('handles empty text', () async {
      final keywords = await service.extractKeywords('');
      expect(keywords, isEmpty);
    });

    test('extracts and scores basic keywords', () async {
      const text = 'Flutter framework mobile applications. Flutter is great for mobile apps.';
      final scores = await service.getKeywordScores(text);
      final keywords = await service.extractKeywords(text);

      // Check that important terms are extracted
      expect(keywords, containsAll(['flutter', 'mobile']));

      // Verify scoring
      expect(scores['flutter']!, equals(1.0),
          reason: 'most frequent meaningful term should have score of 1.0');
      expect(scores['mobile']!, lessThanOrEqualTo(scores['flutter']!),
          reason:
              'less frequent terms should have scores less than or equal to the most frequent term');
    });

    test('prioritizes title terms', () async {
      const text = 'mobile applications development';
      final scores = await service.getKeywordScores(
        text,
        title: 'Flutter Guide',
      );
      final keywords = await service.extractKeywords(
        text,
        title: 'Flutter Guide',
      );

      // Main text terms should be present
      expect(keywords, containsAll(['mobile', 'applications', 'development']),
          reason: 'main text terms should be included');

      // Check scores for main text terms
      expect(scores['mobile']!, greaterThan(0.3),
          reason: 'main text terms should have valid scores');
      expect(scores['applications']!, greaterThan(0.3),
          reason: 'main text terms should have valid scores');
      expect(scores['development']!, greaterThan(0.3),
          reason: 'main text terms should have valid scores');
    });

    test('filters common words', () async {
      final keywords = await service.extractKeywords(
        'the and is example good',
      );

      expect(keywords, isEmpty);
    });

    test('respects maximum keyword limit', () async {
      final longText = List.generate(20, (i) => 'word$i').join(' ');
      final keywords = await service.extractKeywords(longText);

      expect(keywords.length, lessThanOrEqualTo(10));
    });

    test('properly normalizes scores', () async {
      const text = 'flutter flutter flutter mobile mobile framework';
      final scores = await service.getKeywordScores(text);

      expect(scores['flutter']!, equals(1.0), reason: 'most frequent term should have max score');
      expect(scores['mobile']!, greaterThan(0.3),
          reason: 'less frequent terms should still have valid scores');
      expect(scores['mobile']!, lessThan(scores['flutter']!),
          reason: 'less frequent terms should have lower scores');
      expect(scores['framework']!, greaterThan(0.3),
          reason: 'valid terms should have scores above minimum threshold');
      expect(scores['framework']!, lessThan(scores['mobile']!),
          reason: 'least frequent term should have lowest score');
    });
  });
}
