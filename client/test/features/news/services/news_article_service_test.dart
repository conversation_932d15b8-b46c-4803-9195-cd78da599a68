import 'package:flutter_test/flutter_test.dart';
import 'package:promz_common/promz_common.dart';

void main() {
  AppLogger.setLogLevelForTest(LogLevel.error);
  group('Article Share Parsing Tests', () {
    test('should parse article share with title, source, and URL', () {
      // Arrange
      const articleText = '''
Waymo Eyeing Expansion Into San Jose, and Also Much of the South Bay

Source: SFist
 https://search.app/1NPBd

Shared via the Google App
''';

      // Act
      final components = parseArticleShare(articleText);

      // Assert
      expect(components['title'],
          'Waymo Eyeing Expansion Into San Jose, and Also Much of the South Bay');
      expect(components['description'], 'Source: SFist');
      expect(components['url'], 'https://search.app/1NPBd');
    });

    test('should parse article share with title and URL', () {
      // Arrange
      const articleText = '''
Waymo Eyeing Expansion Into San Jose
https://sfist.com/2025/04/18/waymo-eyeing-expansion-into-san-jose/
''';

      // Act
      final components = parseArticleShare(articleText);

      // Assert
      expect(components['title'], 'Waymo Eyeing Expansion Into San Jose');
      expect(
          components['url'], 'https://sfist.com/2025/04/18/waymo-eyeing-expansion-into-san-jose/');
      expect(components['description'], null);
    });
  });
}

// Helper function to parse article shares (copied from InputSelectionViewModel)
Map<String, String?> parseArticleShare(String content) {
  final result = <String, String?>{};

  // Try different patterns to extract components
  final titleUrlPattern = RegExp(r'(.+)\s*\n+\s*(https?:\/\/\S+)', caseSensitive: false);
  final titleDescUrlPattern =
      RegExp(r'(.+)\s*\n+\s*(.+)\s*\n+\s*(https?:\/\/\S+)', caseSensitive: false);
  final urlTitlePattern = RegExp(r'(https?:\/\/\S+)\s*\n+\s*(.+)', caseSensitive: false);

  // Try title + description + URL pattern
  final titleDescUrlMatch = titleDescUrlPattern.firstMatch(content);
  if (titleDescUrlMatch != null && titleDescUrlMatch.groupCount >= 3) {
    result['title'] = titleDescUrlMatch.group(1)?.trim();
    result['description'] = titleDescUrlMatch.group(2)?.trim();
    result['url'] = titleDescUrlMatch.group(3)?.trim();
    return result;
  }

  // Try title + URL pattern
  final titleUrlMatch = titleUrlPattern.firstMatch(content);
  if (titleUrlMatch != null && titleUrlMatch.groupCount >= 2) {
    result['title'] = titleUrlMatch.group(1)?.trim();
    result['url'] = titleUrlMatch.group(2)?.trim();
    return result;
  }

  // Try URL + title pattern
  final urlTitleMatch = urlTitlePattern.firstMatch(content);
  if (urlTitleMatch != null && urlTitleMatch.groupCount >= 2) {
    result['url'] = urlTitleMatch.group(1)?.trim();
    result['title'] = urlTitleMatch.group(2)?.trim();
    return result;
  }

  // Extract any URLs from the content as fallback
  final urlPattern = RegExp(r'(https?:\/\/\S+)', caseSensitive: false);
  final urlMatch = urlPattern.firstMatch(content);
  if (urlMatch != null) {
    result['url'] = urlMatch.group(0)?.trim();
  }

  return result;
}
