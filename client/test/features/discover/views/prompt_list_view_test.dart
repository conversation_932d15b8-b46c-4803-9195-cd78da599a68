import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:promz/core/models/user_profile_model.dart';
import 'package:promz/core/providers/service_providers.dart';
import 'package:promz/core/services/client_context_service.dart';
import 'package:promz/core/services/content_processing_service.dart';
import 'package:promz/core/services/user_profile_service.dart';
import 'package:promz/features/discover/viewmodels/prompt_list_viewmodel.dart';
import 'package:promz/features/discover/views/prompt_list_view.dart';
import 'package:promz/features/home/<USER>/entity_detection_service.dart';
import 'package:promz/features/home/<USER>/prompt_suggestion_service.dart';
import 'package:promz/features/home/<USER>/home_viewmodel.dart';
import 'package:promz/features/news/services/news_article_service.dart';
import 'package:promz_common/promz_common.dart';

class MockPromptListViewModel extends Mock implements PromptListViewModel {}

class MockHomeViewModel extends Mock implements HomeViewModel {
  @override
  bool get hasExecutionResult => false; // No execution result by default in tests

  @override
  bool get isExecutingLlm => false; // Not executing LLM by default in tests

  @override
  String? get executionError => null;

  @override
  LlmExecuteResponse? get llmResponse => null;

  // Methods needed for display transformation
  String transformTitleForDisplay(String title) => title;
  String transformTitleWithValues(String title, Map<String, String> values) => title;

  // Navigation related properties
  @override
  int get currentNavIndex => 0; // Default to home page index

  // Mock the navigateToPage method to prevent navigation during tests
  @override
  void navigateToPage(BuildContext context, int index) {}

  // Add clearExecutionResults method
  @override
  void clearExecutionResults() {}
}

class MockContentProcessingService extends Mock implements ContentProcessingService {}

class MockClientContextService extends Mock implements ClientContextService {
  final MockContentProcessingService _contentProcessingService = MockContentProcessingService();
  final MockNewsArticleService _newsArticleService = MockNewsArticleService();

  // Provide access to ContentProcessingService
  @override
  ContentProcessingService get contentProcessing => _contentProcessingService;

  // Provide access to NewsArticleService
  @override
  NewsArticleService get newsArticle => _newsArticleService;

  String transformTitleForDisplay(String title) => title;
  String transformTitleWithValues(String title, Map<String, String> values) => title;

  @override
  String transformTitleWithAllValues(String title) => title;

  // Return empty maps for any method that returns maps
  @override
  Map<String, String> get variableValues => {};

  @override
  Map<String, String> getAllVariableValues() => {};

  @override
  Map<String, Entity> get entities => {};
}

class MockEntityDetectionService extends Mock implements EntityDetectionService {}

class MockPromptSuggestionService extends Mock implements PromptSuggestionService {}

class MockNewsArticleService extends Mock implements NewsArticleService {}

class MockUserProfileService extends Mock implements UserProfileService {
  @override
  UserProfileModel get profile => UserProfileModel.empty();

  @override
  bool get isLoading => false;
}

void main() {
  AppLogger.setLogLevelForTest(LogLevel.error);
  late MockPromptListViewModel mockViewModel;
  late MockClientContextService mockClientContextService;

  setUp(() {
    mockViewModel = MockPromptListViewModel();
    mockClientContextService = MockClientContextService();

    // Setup EntityDetection and PromptSuggestion mocks
    final mockEntityDetection = MockEntityDetectionService();
    final mockPromptSuggestion = MockPromptSuggestionService();
    final mockUserProfileService = MockUserProfileService();
    when(() => mockClientContextService.entityDetection).thenReturn(mockEntityDetection);
    when(() => mockClientContextService.promptSuggestion).thenReturn(mockPromptSuggestion);
    when(() => mockClientContextService.userProfile).thenReturn(mockUserProfileService);

    // Mock the required properties with default values for PromptListViewModel
    when(() => mockViewModel.categoryTitle).thenReturn('Test Category');
    when(() => mockViewModel.promptsForCurrentPage).thenReturn([]);
    when(() => mockViewModel.totalPages).thenReturn(1);
    when(() => mockViewModel.currentPage).thenReturn(0);
    when(() => mockViewModel.isContinueEnabled).thenReturn(false);
    when(() => mockViewModel.selectedPromptIndex).thenReturn(null);
    when(() => mockViewModel.isPromptSelected(any())).thenReturn(false);
    when(() => mockViewModel.itemsPerPage).thenReturn(10);
  });

  testWidgets('PromptListView loads and displays prompts on build', (WidgetTester tester) async {
    // Setup specific to this test
    when(() => mockViewModel.promptsForCurrentPage).thenReturn([
      const PromptModel(
        title: 'Test Prompt 1',
        subtitle: 'Test Content 1',
        id: '123',
      ),
      const PromptModel(
        title: 'Test Prompt 2',
        subtitle: 'Test Content 2',
        id: '456',
      ),
    ]);

    // Build our app and trigger a frame.
    await tester.pumpWidget(
      ProviderScope(
        overrides: [
          // Override the providers that require database access
          promptListViewModelProvider('Test Category')
              .overrideWith((ref) => Future.value(mockViewModel)),
          homeViewModelProvider.overrideWith((ref) => MockHomeViewModel()),
          clientContextServiceProvider
              .overrideWith((ref) => Future.value(mockClientContextService)),
        ],
        child: const MaterialApp(
          home: Scaffold(
            body: PromptListView(categoryTitle: 'Test Category'),
          ),
        ),
      ),
    );

    // Wait for the UI to update.
    await tester.pumpAndSettle();

    // Verify that the prompts are displayed.
    expect(find.text('Test Prompt 1'), findsOneWidget);
    expect(find.text('Test Prompt 2'), findsOneWidget);
  });

  testWidgets('PromptListView handles selection correctly', (WidgetTester tester) async {
    // Setup specific to this test
    when(() => mockViewModel.promptsForCurrentPage).thenReturn([
      const PromptModel(
        title: 'Test Prompt 1',
        subtitle: 'Test Content 1',
        id: '123',
      ),
    ]);
    when(() => mockViewModel.selectedPromptIndex).thenReturn(0);
    when(() => mockViewModel.isPromptSelected(any())).thenReturn(true);

    // Build our app and trigger a frame.
    await tester.pumpWidget(
      ProviderScope(
        overrides: [
          promptListViewModelProvider('Test Category')
              .overrideWith((ref) => Future.value(mockViewModel)),
          homeViewModelProvider.overrideWith((ref) => MockHomeViewModel()),
          clientContextServiceProvider
              .overrideWith((ref) => Future.value(mockClientContextService)),
        ],
        child: const MaterialApp(
          home: Scaffold(
            body: PromptListView(categoryTitle: 'Test Category'),
          ),
        ),
      ),
    );

    // Wait for the UI to update.
    await tester.pumpAndSettle();

    // Verify that the prompt is displayed
    expect(find.text('Test Prompt 1'), findsOneWidget);
  });

  testWidgets('PromptListView handles pagination correctly', (WidgetTester tester) async {
    // Setup specific to this test
    when(() => mockViewModel.promptsForCurrentPage).thenReturn([
      const PromptModel(
        title: 'Test Prompt 1',
        subtitle: 'Test Content 1',
        id: '123',
      ),
    ]);
    when(() => mockViewModel.totalPages).thenReturn(2);

    // Build our app and trigger a frame.
    await tester.pumpWidget(
      ProviderScope(
        overrides: [
          promptListViewModelProvider('Test Category')
              .overrideWith((ref) => Future.value(mockViewModel)),
          homeViewModelProvider.overrideWith((ref) => MockHomeViewModel()),
          clientContextServiceProvider
              .overrideWith((ref) => Future.value(mockClientContextService)),
        ],
        child: const MaterialApp(
          home: Scaffold(
            body: PromptListView(categoryTitle: 'Test Category'),
          ),
        ),
      ),
    );

    // Wait for the UI to update.
    await tester.pumpAndSettle();

    // Verify that the prompt is displayed
    expect(find.text('Test Prompt 1'), findsOneWidget);
  });
}
