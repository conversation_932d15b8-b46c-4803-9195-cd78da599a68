import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:promz_common/promz_common.dart';
import 'package:promz/features/discover/viewmodels/discover_viewmodel.dart';
import '../../../database/database_test_base.dart';

class _DatabaseTest extends DatabaseTestBase {}

void main() {
  AppLogger.setLogLevelForTest(LogLevel.error);
  late ProviderContainer container;
  late _DatabaseTest testObject;

  setUp(() async {
    // Create a fresh in-memory database instance for each test
    testObject = _DatabaseTest();
    await testObject.setup();

    // Initialize the ProviderContainer
    container = ProviderContainer();

    // Give a small delay to ensure database is fully ready
    await Future.delayed(const Duration(milliseconds: 100));
  });

  tearDown(() async {
    container.dispose();
    try {
      await testObject.teardown();
    } catch (e) {
      // Log teardown errors but don't fail the test
      appLog.error('Teardown error: $e', name: 'CategoryListViewModelTest');
    }
  });

  group('CategoryListViewModel Tests', () {
    test('Initial state is correct', () async {
      final viewModel = DiscoverViewModel();
      await Future.delayed(const Duration(milliseconds: 100));

      // Initial state before loading
      final state = viewModel.state;
      expect(state.categories, isEmpty);
      expect(state.selectedCategoryIndices, isEmpty);
      expect(state.currentPage, 0);
      expect(state.totalPages, equals(0)); // No categories loaded yet
    });

    test('Load categories loads seeded data correctly', () async {
      final viewModel = DiscoverViewModel();

      // Use retry logic for database operations
      await testObject.retryDatabaseOperation(() async {
        await viewModel.loadCategories();
        return Future.value();
      }, operationName: 'load categories');

      await Future.delayed(const Duration(milliseconds: 100));

      final state = viewModel.state;
      // We should have categories from our seeded data
      expect(state.categories, isNotEmpty);

      // We seeded some categories
      expect(state.categories.length, greaterThanOrEqualTo(0));

      // Check some specific categories from our seeded data
      expect(
          state.categories.any((c) =>
              c.title == 'Summarize' &&
              c.subtitle == 'Generate concise summaries of text or topics'),
          isTrue);

      // Code Generation is a category we don't plan to support
      expect(
          state.categories.any((c) =>
              c.title == 'Code Generation' &&
              c.subtitle == 'Assist in writing and debugging code snippets'),
          isFalse);
    });

    test('Toggle category selection works correctly', () async {
      final viewModel = DiscoverViewModel();

      // Use retry logic for database operations
      await testObject.retryDatabaseOperation(() async {
        await viewModel.loadCategories();
        return Future.value();
      }, operationName: 'load categories');

      await Future.delayed(const Duration(milliseconds: 100));

      // Select first category
      viewModel.toggleCategorySelection(0);
      final stateAfterSelect = viewModel.state;
      expect(stateAfterSelect.isCategorySelected(0), isTrue);
      expect(stateAfterSelect.selectedCategoryIndices.length, equals(1));
      expect(stateAfterSelect.isContinueEnabled, isTrue);

      // Deselect first category
      viewModel.toggleCategorySelection(0);
      final stateAfterDeselect = viewModel.state;
      expect(stateAfterDeselect.isCategorySelected(0), isFalse);
      expect(stateAfterDeselect.selectedCategoryIndices.length, equals(0));
      expect(stateAfterDeselect.isContinueEnabled, isFalse);
    });

    test('Pagination works correctly', () async {
      final viewModel = DiscoverViewModel();

      // Use retry logic for database operations
      await testObject.retryDatabaseOperation(() async {
        await viewModel.loadCategories();
        return Future.value();
      }, operationName: 'load categories');

      await Future.delayed(const Duration(milliseconds: 150));

      // With seeded categories and default 5 items per page, we should have non-zero pages
      expect(viewModel.state.totalPages, greaterThanOrEqualTo(1),
          reason: 'Expected at least 1 page with seeded categories and 5 items per page');

      // Test navigation (should be limited since we only have 0 pages)
      expect(viewModel.state.currentPage, equals(0));

      // Test with smaller page size
      viewModel.updateItemsPerPage(2);

      // Wait for state to update after changing page size
      await Future.delayed(const Duration(milliseconds: 50));

      expect(viewModel.state.totalPages, greaterThanOrEqualTo(2),
          reason: 'Expected at least 2 pages with seeded categories and 2 items per page');

      // Now we can navigate between pages
      viewModel.nextPage();
      expect(viewModel.state.currentPage, equals(1));

      viewModel.previousPage();
      expect(viewModel.state.currentPage, equals(0));
    });
  });
}
