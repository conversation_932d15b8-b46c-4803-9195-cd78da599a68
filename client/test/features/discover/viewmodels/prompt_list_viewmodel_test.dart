import 'package:flutter_test/flutter_test.dart';
import 'package:promz_common/promz_common.dart';
import 'package:promz/features/discover/viewmodels/prompt_list_viewmodel.dart';
import '../../../database/database_test_base.dart';

class _DatabaseTest extends DatabaseTestBase {}

void main() {
  AppLogger.setLogLevelForTest(LogLevel.error);
  late _DatabaseTest testObject;

  setUp(() async {
    // Create a fresh in-memory database instance for each test
    testObject = _DatabaseTest();
    await testObject.setup();

    // Give a small delay to ensure database is fully ready
    await Future.delayed(const Duration(milliseconds: 100));
  });

  tearDown(() async {
    await testObject.teardown();
  });

  group('PromptListViewModel Tests', () {
    test('Different categories load different prompts', () async {
      // Arrange - use seeded 'Summarize' category and pass database instance
      final viewModel = PromptListViewModel(categoryTitle: 'Summarize');

      // Act
      await viewModel.loadPrompts();
      // Wait for prompts to load
      await Future.delayed(const Duration(milliseconds: 100));

      // Assert
      expect(viewModel.promptsForCurrentPage.isNotEmpty, true); // Access from state
      expect(viewModel.currentPage, 0);
      expect(viewModel.totalPages, greaterThan(0));
    });
  });
}
