import 'package:flutter_test/flutter_test.dart';
import 'package:promz/features/input_selection/models/input_source.dart';

void main() {
  group('InputSource', () {
    test('creates instance with required parameters', () {
      const source = InputSource(type: InputSourceType.clipboard);

      expect(source.type, InputSourceType.clipboard);
      expect(source.content, isNull);
      expect(source.sourceApp, isNull);
      expect(source.fileName, isNull);
      expect(source.mimeType, isNull);
      expect(source.isSelected, isFalse);
    });

    test('creates instance with all parameters', () {
      const source = InputSource(
        type: InputSourceType.shared,
        content: 'Test content',
        sourceApp: 'Test App',
        fileName: 'test.txt',
        mimeType: 'text/plain',
        isSelected: true,
      );

      expect(source.type, InputSourceType.shared);
      expect(source.content, 'Test content');
      expect(source.sourceApp, 'Test App');
      expect(source.fileName, 'test.txt');
      expect(source.mimeType, 'text/plain');
      expect(source.isSelected, isTrue);
    });

    test('creates file source correctly', () {
      const source = InputSource(
        type: InputSourceType.file,
        fileName: 'test.txt',
        content: 'test content',
        filePath: '/path/to/test.txt',
        mimeType: 'text/plain',
      );

      expect(source.type, InputSourceType.file);
      expect(source.fileName, 'test.txt');
      expect(source.content, 'test content');
      expect(source.filePath, '/path/to/test.txt');
      expect(source.mimeType, 'text/plain');
    });

    test('detects zip files correctly', () {
      const zipSource = InputSource(
        type: InputSourceType.file,
        fileName: 'test.zip',
      );

      const textSource = InputSource(
        type: InputSourceType.file,
        fileName: 'test.txt',
      );

      expect(zipSource.isZipFile, true);
      expect(textSource.isZipFile, false);
    });

    group('copyWith', () {
      test('returns new instance with updated values', () {
        const original = InputSource(
          type: InputSourceType.clipboard,
          content: 'Original content',
        );

        final updated = original.copyWith(
          content: 'Updated content',
          isSelected: true,
        );

        expect(updated.type, original.type);
        expect(updated.content, 'Updated content');
        expect(updated.isSelected, isTrue);
        expect(original.content, 'Original content');
        expect(original.isSelected, isFalse);
      });

      test('returns new instance with same values when no parameters provided', () {
        const original = InputSource(
          type: InputSourceType.manual,
          content: 'Test content',
          isSelected: true,
        );

        final copied = original.copyWith();

        expect(copied.type, original.type);
        expect(copied.content, original.content);
        expect(copied.isSelected, original.isSelected);
        expect(identical(original, copied), isFalse);
      });

      test('copyWith works correctly', () {
        const source = InputSource(
          type: InputSourceType.file,
          fileName: 'test.txt',
        );

        final copied = source.copyWith(
          fileName: 'new.txt',
          type: InputSourceType.manual,
        );

        expect(copied.fileName, 'new.txt');
        expect(copied.type, InputSourceType.manual);
        expect(copied.filePath, source.filePath);
      });
    });
  });
}
