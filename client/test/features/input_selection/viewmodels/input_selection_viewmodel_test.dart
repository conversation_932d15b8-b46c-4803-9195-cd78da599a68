import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:promz/core/services/attachment/attachment_registry_service.dart';
import 'package:promz/core/services/client_context_service.dart';
import 'package:promz_common/promz_common.dart';
import 'package:promz/features/input_selection/models/input_source.dart';
import 'package:promz/features/input_selection/viewmodels/input_selection_viewmodel.dart';

// Mock ClientContextService for testing
class MockClientContextService extends Mock implements ClientContextService {
  final AttachmentRegistryService _attachmentRegistry = AttachmentRegistryService();

  @override
  AttachmentRegistryService get attachmentRegistry => _attachmentRegistry;
}

void main() {
  AppLogger.setLogLevelForTest(LogLevel.error);
  TestWidgetsFlutterBinding.ensureInitialized();

  // Create a mock ClientContextService that we'll use for all tests
  final mockClientContextService = MockClientContextService();

  group('InputSelectionViewModel', () {
    test('initializes with empty sources by default', () async {
      final viewModel = InputSelectionViewModel(clientContextService: mockClientContextService);
      await Future.delayed(Duration.zero); // Wait for initialization
      expect(viewModel.selectedSources.isEmpty, true);
    });

    test('direct source addition works', () {
      final viewModel = InputSelectionViewModel(clientContextService: mockClientContextService);

      // Create a source directly
      const source = InputSource(
        type: InputSourceType.shared,
        content: 'Test content',
        fileName: 'Test file',
      );

      // Use mock raw source addition - addSimpleSource is a helper method we'll add later
      viewModel.addSimpleSource(source);

      // Verify it was added
      expect(viewModel.selectedSources.isNotEmpty, true);
      expect(viewModel.selectedSources.length, 1);
      expect(viewModel.selectedSources.first.content, 'Test content');

      // Now try using the actual method
      viewModel.updateSourceContent(InputSourceType.manual, 'Manual content');

      // Verify both sources are now in the list
      expect(viewModel.selectedSources.length, 2);

      // Log details to help debug
      appLog.debug('Sources after update:', name: 'TEST');
      for (var s in viewModel.selectedSources) {
        appLog.debug('- Type: ${s.type}, Content: ${s.content}', name: 'TEST');
      }
    });

    // Original tests with more detailed assertions
    group('updateSourceContent', () {
      test('updates source content correctly', () {
        final viewModel = InputSelectionViewModel(clientContextService: mockClientContextService);

        // Call updateSourceContent and log before/after counts
        appLog.debug('Before update - Source count: ${viewModel.selectedSources.length}',
            name: 'TEST');
        viewModel.updateSourceContent(InputSourceType.shared, 'Test shared content');
        appLog.debug('After update - Source count: ${viewModel.selectedSources.length}',
            name: 'TEST');

        // Check if the source was added
        expect(viewModel.selectedSources.isNotEmpty, true,
            reason: 'Sources should not be empty after adding content');

        // Dump the entire list for debugging
        appLog.debug('All sources:', name: 'TEST');
        for (var s in viewModel.selectedSources) {
          // Fix the substring operation to handle content of any length
          final contentPreview = s.content == null
              ? 'null'
              : s.content!.length > 20
                  ? '${s.content!.substring(0, 20)}...'
                  : s.content;
          appLog.debug('- Type: ${s.type}, Content: $contentPreview', name: 'TEST');
        }

        // Now try to find the shared source
        final sharedSources =
            viewModel.selectedSources.where((s) => s.type == InputSourceType.shared).toList();
        appLog.debug('Shared sources found: ${sharedSources.length}', name: 'TEST');

        // Check if we found the source
        expect(sharedSources.isNotEmpty, true, reason: 'Should find a shared source');
        expect(sharedSources.first.content, 'Test shared content');
      });
    });

    group('getCombinedContent', () {
      test('combines content from selected sources', () {
        final viewModel = InputSelectionViewModel(clientContextService: mockClientContextService);

        // Add sources manually to ensure they're added properly
        viewModel.updateSourceContent(InputSourceType.shared, 'Shared content');

        // Verify the first source was added
        expect(viewModel.selectedSources.where((s) => s.type == InputSourceType.shared).isNotEmpty,
            true);

        // Add second source
        viewModel.updateSourceContent(InputSourceType.manual, 'Manual content');

        // Verify both sources were added
        expect(viewModel.selectedSources.length, 2);

        // Now test combining content
        final combined = viewModel.getCombinedContent();
        expect(combined, 'Shared content\n\nManual content');
      });

      test('skips sources with null content', () {
        final viewModel = InputSelectionViewModel(clientContextService: mockClientContextService);

        // Add one source with content
        viewModel.updateSourceContent(
          InputSourceType.manual,
          'Manual content',
        );

        // Verify source was added
        expect(viewModel.selectedSources.isNotEmpty, true);
        expect(viewModel.selectedSources.first.content, 'Manual content');

        // Check combined content
        final combined = viewModel.getCombinedContent();
        expect(combined, 'Manual content');
      });
    });
  });
}
