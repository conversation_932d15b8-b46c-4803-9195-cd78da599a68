import 'dart:async';
import 'package:drift/drift.dart' as drift;
import 'package:flutter_test/flutter_test.dart';
import 'package:promz_common/promz_common.dart';
import 'package:promz/database/database.dart';
import 'package:uuid/uuid.dart';
import 'database_test_base.dart';

class _DatabaseTest extends DatabaseTestBase {}

void main() {
  AppLogger.setLogLevelForTest(LogLevel.error);
  TestWidgetsFlutterBinding.ensureInitialized();
  const uuid = Uuid();
  group('Database CRUD Operations', () {
    late _DatabaseTest testObject;

    setUp(() async {
      testObject = _DatabaseTest();
      await testObject.setup();
    });

    tearDown(() async {
      await testObject.teardown();
    });

    test('database can store and retrieve prompts', () async {
      // Get initial count due to seeded data
      final initialPrompts = await testObject.database.getAllPrompts();
      final initialCount = initialPrompts.length;

      final prompt = PromptsCompanion.insert(
        id: uuid.v4(),
        title: 'Test Title',
        subtitle: 'Test Subtitle',
        source: 'local',
        categoryId: '550e8400-e29b-41d4-a716-************',
        keywords: const drift.Value('["k1", "k2"]'),
      );

      final id = await testObject.database.insertPrompt(prompt);
      expect(id, isNotNull);

      final retrieved = await testObject.database.getPrompt(id);
      expect(retrieved, isNotNull);
      expect(retrieved!.title, equals('Test Title'));
      expect(retrieved.subtitle, equals('Test Subtitle'));

      // Check that we added one prompt
      final allPrompts = await testObject.database.getAllPrompts();
      expect(allPrompts.length, equals(initialCount + 1));
    });

    test('getAllPrompts returns seeded data initially', () async {
      final prompts = await testObject.database.getAllPrompts();

      // We know we have more than 2, but checking for at least 2
      expect(prompts.length, greaterThanOrEqualTo(2));

      // Verify some seeded data
      expect(
          prompts.any((p) =>
              p.title == 'Summarize this article' &&
              p.subtitle == 'Provide a brief summary of the following article.'),
          isTrue);
    });

    test('database operations respect timeouts', () async {
      // Use a longer operation that won't complete immediately
      final operation = Future(() async {
        await Future.delayed(const Duration(milliseconds: 150));
        return await testObject.database.getAllPrompts();
      });

      // Test timeout behavior with a shorter timeout
      await expectLater(
        operation.timeout(
          const Duration(milliseconds: 50),
          onTimeout: () => throw TimeoutException('Query timed out'),
        ),
        throwsA(isA<TimeoutException>()),
      );
    });

    test('concurrent access is handled correctly', () async {
      final initialPrompts = await testObject.database.getAllPrompts();
      final initialCount = initialPrompts.length;

      // Create multiple concurrent insert operations
      final operations = List.generate(3, (index) async {
        return await testObject.database.insertPrompt(
          PromptsCompanion.insert(
            id: uuid.v4(),
            title: 'Title $index',
            subtitle: 'Content $index',
            source: 'local',
            categoryId: '550e8400-e29b-41d4-a716-************',
            keywords: const drift.Value('["k1", "k2"]'),
          ),
        );
      });

      final results = await Future.wait(operations);
      expect(results.length, equals(3));

      final prompts = await testObject.database.getAllPrompts();
      expect(prompts.length, equals(initialCount + 3));
    });

    test('database handles close and reopen correctly', () async {
      // Get initial count
      final initialPrompts = await testObject.database.getAllPrompts();
      final initialCount = initialPrompts.length;

      // Close and reopen
      await testObject.database.close();
      testObject.database = await AppDatabase.getInstance();
      await Future.delayed(const Duration(milliseconds: 100));

      // Verify seeded data is still there after reopen
      final afterReopenPrompts = await testObject.database.getAllPrompts();
      expect(afterReopenPrompts.length, equals(initialCount));

      // Try inserting new data
      final prompt = PromptsCompanion.insert(
        id: uuid.v4(),
        title: 'Test Prompt',
        subtitle: 'Test Subtitle',
        source: 'local',
        categoryId: '550e8400-e29b-41d4-a716-************',
        keywords: const drift.Value('["k1", "k2"]'),
      );

      final id = await testObject.database.insertPrompt(prompt);
      expect(id, isNotNull);

      // Verify new total count
      final finalPrompts = await testObject.database.getAllPrompts();
      expect(finalPrompts.length, equals(initialCount + 1));
    });
  });
}
