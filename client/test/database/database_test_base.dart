import 'dart:developer' as dev;
import 'package:flutter_test/flutter_test.dart';
import 'package:path_provider_platform_interface/path_provider_platform_interface.dart';
import 'package:promz/database/database.dart';
import 'package:promz_common/promz_common.dart';
import 'mock_path_provider.dart';

abstract class DatabaseTestBase {
  late MockPathProviderPlatform mockPathProvider;
  late AppDatabase database;

  /// Maximum number of retries for database operations
  static const int maxRetries = 3;

  /// Base delay for exponential backoff in milliseconds
  static const int baseDelayMs = 200;

  /// Helper method to retry database operations with exponential backoff
  Future<T> retryDatabaseOperation<T>(Future<T> Function() operation,
      {String operationName = 'database operation'}) async {
    int retryCount = 0;

    while (true) {
      try {
        return await operation();
      } catch (e) {
        retryCount++;

        // Check if it's a database locked error
        final isDbLocked = e.toString().toLowerCase().contains('database is locked');

        if (isDbLocked && retryCount <= maxRetries) {
          // Calculate exponential backoff delay
          final delayMs = baseDelayMs * (1 << (retryCount - 1)); // 200ms, 400ms, 800ms, etc.
          dev.log(
              'Database locked during $operationName. Retrying in ${delayMs}ms (attempt $retryCount of $maxRetries)');
          await Future.delayed(Duration(milliseconds: delayMs));
          continue;
        }

        // If not a database locked error or we've exceeded retries, rethrow
        rethrow;
      }
    }
  }

  Future<void> setup() async {
    AppLogger.setLogLevelForTest(LogLevel.error);
    TestWidgetsFlutterBinding.ensureInitialized();
    mockPathProvider = MockPathProviderPlatform();
    PathProviderPlatform.instance = mockPathProvider;

    // Create a fresh in-memory database instance for each test with retry logic
    await retryDatabaseOperation(() async {
      database = await AppDatabase.getInstanceForTesting();

      // Verify database connection is working
      await database.customSelect('SELECT 1').get();

      // Enable WAL mode for better performance
      await database.customStatement('PRAGMA journal_mode = WAL');
      return Future.value();
    }, operationName: 'database setup');

    // Give a small delay to ensure database is fully ready
    await Future.delayed(const Duration(milliseconds: 100));
  }

  Future<void> teardown() async {
    dev.log('Cleaning up test database...');

    // Close the database with retry logic
    await retryDatabaseOperation(() async {
      await database.close();
      return Future.value();
    }, operationName: 'database close');

    // Reset the singleton instance with retry logic
    await retryDatabaseOperation(() async {
      final db = await AppDatabase.getInstanceForTesting();
      await db.close();
      return Future.value();
    }, operationName: 'database instance reset');
  }
}
