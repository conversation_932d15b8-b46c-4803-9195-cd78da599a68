import 'package:flutter_test/flutter_test.dart';
import 'package:http/http.dart' as http;
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:promz/core/utils/url_resolver.dart';
import 'package:promz_common/promz_common.dart'; // For appLog setup

import 'url_resolver_test.mocks.dart'; // Generated by build_runner

// Annotate to generate mocks for http.Client
@GenerateMocks([http.Client])
void main() {
  // Setup mock client and logger before tests
  late MockClient mockHttpClient;

  setUpAll(() {
    AppLogger.setLogLevelForTest(LogLevel.error);
  });

  setUp(() {
    mockHttpClient = MockClient();
    // Provide a default non-redirect response for HEAD requests
    when(mockHttpClient.head(any, headers: anyNamed('headers'))).thenAnswer(
      (_) async =>
          http.Response('', 200, request: http.Request('HEAD', Uri.parse('http://example.com'))),
    );
    // Provide a default non-redirect response for GET requests
    when(mockHttpClient.get(any, headers: anyNamed('headers'))).thenAnswer(
      (_) async => http.Response('<html></html>', 200,
          request: http.Request('GET', Uri.parse('http://example.com'))),
    );

    // Inject the mock client factory (if UrlResolver used a factory pattern)
    // For simplicity here, we'll assume direct http.Client usage and mock it where needed
    // If UrlResolver is refactored to accept a client, injection is cleaner.
    // As UrlResolver uses http.Client directly, we'll test methods that don't
    // make network calls directly, or mock the static methods if possible (less ideal).
    // For processUrl/resolveUrl, we'll need to structure tests carefully or refactor UrlResolver.
    // Let's focus on testing the logic parts first.
  });

  group('UrlResolver Logic Tests (No Network)', () {
    test('extractEmbeddedUrl correctly extracts from search.app link', () {
      const url =
          'https://search.app/?link=https://www.youtube.com/watch?v%3DdFObux6mfTc&utm_source=dsdf,sh/x/discover/m5/4';
      const expected = 'https://www.youtube.com/watch?v=dFObux6mfTc';
      expect(UrlResolver.extractEmbeddedUrl(url), expected);
    });

    test('extractEmbeddedUrl correctly extracts from url= parameter', () {
      const url = 'https://example.com/redirect?url=https%3A%2F%2Ftarget.com';
      const expected = 'https://target.com';
      expect(UrlResolver.extractEmbeddedUrl(url), expected);
    });

    test('extractEmbeddedUrl returns null when no embedded URL is present', () {
      const url = 'https://www.example.com/page';
      expect(UrlResolver.extractEmbeddedUrl(url), isNull);
    });

    test('extractEmbeddedUrl handles double encoding', () {
      const url = 'https://search.app/?link=https%253A%252F%252Fexample.com%252Fpath';
      const expected = 'https://example.com/path';
      expect(UrlResolver.extractEmbeddedUrl(url), expected);
    });
  });

  group('YouTubeUrlHandling', () {
    test('isYouTubeUrl identifies various YouTube URLs', () {
      expect(
          YouTubeUrlHandling.isYouTubeUrl('https://www.youtube.com/watch?v=dQw4w9WgXcQ'), isTrue);
      expect(YouTubeUrlHandling.isYouTubeUrl('https://youtube.com/watch?v=dQw4w9WgXcQ'), isTrue);
      expect(YouTubeUrlHandling.isYouTubeUrl('https://youtu.be/dQw4w9WgXcQ'), isTrue);
      expect(YouTubeUrlHandling.isYouTubeUrl('https://m.youtube.com/watch?v=dQw4w9WgXcQ'), isTrue);
      expect(YouTubeUrlHandling.isYouTubeUrl('https://www.youtube-nocookie.com/embed/dQw4w9WgXcQ'),
          isTrue);
      expect(YouTubeUrlHandling.isYouTubeUrl('https://example.com'), isFalse);
    });

    test('extractVideoId extracts ID from various YouTube URL formats', () {
      expect(YouTubeUrlHandling.extractVideoId('https://www.youtube.com/watch?v=dFObux6mfTc'),
          'dFObux6mfTc');
      expect(YouTubeUrlHandling.extractVideoId('https://youtu.be/dFObux6mfTc'), 'dFObux6mfTc');
      expect(YouTubeUrlHandling.extractVideoId('https://www.youtube.com/embed/dFObux6mfTc'),
          'dFObux6mfTc');
      expect(YouTubeUrlHandling.extractVideoId('https://www.youtube.com/shorts/dFObux6mfTc'),
          'dFObux6mfTc');
      expect(
          YouTubeUrlHandling.extractVideoId(
              'https://m.youtube.com/watch?v=dFObux6mfTc&feature=share'),
          'dFObux6mfTc');
      expect(YouTubeUrlHandling.extractVideoId('https://example.com'), isNull);
    });

    test('getThumbnailUrl generates correct URL', () {
      expect(YouTubeUrlHandling.getThumbnailUrl('dFObux6mfTc'),
          'https://img.youtube.com/vi/dFObux6mfTc/mqdefault.jpg');
      expect(YouTubeUrlHandling.getThumbnailUrl('dFObux6mfTc', quality: 'hqdefault'),
          'https://img.youtube.com/vi/dFObux6mfTc/hqdefault.jpg');
    });

    test('getWatchUrl generates correct URL', () {
      expect(YouTubeUrlHandling.getWatchUrl('dFObux6mfTc'),
          'https://www.youtube.com/watch?v=dFObux6mfTc');
    });

    test('getEmbedUrl generates correct URL', () {
      expect(YouTubeUrlHandling.getEmbedUrl('dFObux6mfTc'),
          'https://www.youtube.com/embed/dFObux6mfTc');
    });
  });

  group('NewsArticleUrlHandling', () {
    test('isLikelyNewsUrl identifies news URLs', () {
      expect(
          NewsArticleUrlHandling.isLikelyNewsUrl(
              'https://www.nytimes.com/2023/10/26/world/europe/ukraine-russia-war.html'),
          isTrue);
      expect(
          NewsArticleUrlHandling.isLikelyNewsUrl('https://www.bbc.co.uk/news/uk-politics-67229999'),
          isTrue);
      expect(
          NewsArticleUrlHandling.isLikelyNewsUrl('https://example.com/article/some-news'), isTrue);
      expect(NewsArticleUrlHandling.isLikelyNewsUrl('https://www.example.com/blog/post'), isFalse);
      expect(NewsArticleUrlHandling.isLikelyNewsUrl('https://github.com/user/repo'), isFalse);
    });

    test('extractSiteName extracts common news site names', () {
      expect(extractSiteName('https://www.nytimes.com/article'), 'New York Times');
      expect(extractSiteName('https://www.bbc.co.uk/news'), 'BBC');
      expect(extractSiteName('https://cnn.com/story'), 'CNN');
      expect(extractSiteName('https://www.some-random-news.com/story'), 'Some Random News');
      expect(extractSiteName('https://example.com'), 'Example');
    });
  });
}

// Helper functions assumed to exist based on UrlResolver usage
// If these are private or elsewhere, these tests would need adjustment.
String? extractFirstUrl(String text) {
  // Basic implementation for testing purposes
  final urlRegex = RegExp(
      r'https?:\/\/(?:www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&//=]*)');
  final match = urlRegex.firstMatch(text);
  return match?.group(0);
}

bool isValidUrl(String url) {
  // Basic implementation for testing purposes
  try {
    final uri = Uri.parse(url);
    return uri.isAbsolute && (uri.scheme == 'http' || uri.scheme == 'https');
  } catch (e) {
    return false;
  }
}
