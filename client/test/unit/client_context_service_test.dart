import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:promz/core/services/app_update_service.dart';
import 'package:promz/core/services/client_context_service.dart';
import 'package:promz/core/services/content_processing_service.dart';
import 'package:promz/core/services/secure_storage_service_provider.dart';
import 'package:promz/features/home/<USER>/prompt_usage_service.dart';
import 'package:promz_common/promz_common.dart';
import '../database/database_test_base.dart';

class _DatabaseTest extends DatabaseTestBase {}

class MockPromptUsageService extends Mock implements PromptUsageService {
  @override
  Future<void> fetchPopularPrompts([bool forceRefresh = false]) async {
    // Mock implementation that returns immediately
    return Future.value();
  }
}

class MockContentProcessingService extends Mock implements ContentProcessingService {}

class MockAppUpdateService extends Mock implements AppUpdateService {
  @override
  Future<void> initialize() async {
    // Mock implementation that returns a completed Future
    return Future.value();
  }
}

void main() {
  late _DatabaseTest testDb;
  late ClientContextService service;
  late MockPromptUsageService mockPromptUsageService;
  late MockContentProcessingService mockContentProcessingService;
  late MockAppUpdateService mockAppUpdateService;

  setUp(() async {
    testDb = _DatabaseTest();
    await testDb.setup();

    // Explicitly use the mock storage implementation
    SecureStorageServiceProvider.useTestImplementation();

    mockPromptUsageService = MockPromptUsageService();
    mockContentProcessingService = MockContentProcessingService();
    mockAppUpdateService = MockAppUpdateService();

    // Create a test-specific instance using dependency injection
    service = ClientContextService.forTesting(
      db: Future.value(testDb.database),
      promptUsageService: mockPromptUsageService,
      contentProcessingService: mockContentProcessingService,
      appUpdateService: mockAppUpdateService,
    );
  });

  tearDown(() async {
    await testDb.teardown();
  });

  AppLogger.setLogLevelForTest(LogLevel.error);

  group('ClientContextService', () {
    test('initializes with an empty entity map', () {
      expect(service.entities, isEmpty);
    });

    test('updates entities correctly', () {
      final entities = [
        const Entity(
            text: 'AAPL',
            canonicalText: 'AAPL',
            displayText: 'Apple Inc.',
            type: EntityType.finance),
        const Entity(
            text: 'MSFT',
            canonicalText: 'MSFT',
            displayText: 'Microsoft Corp.',
            type: EntityType.finance),
        const Entity(
            text: 'GOOG',
            canonicalText: 'GOOG',
            displayText: 'Google LLC',
            type: EntityType.finance),
      ];

      service.updateEntities(entities);

      expect(service.entities.length, entities.length);
      expect(service.entities['aapl']?.displayText, 'Apple Inc.');
      expect(service.entities['msft']?.displayText, 'Microsoft Corp.');
      expect(service.entities['goog']?.displayText, 'Google LLC');
    });

    test('overwrites existing entities with new ones', () {
      final initialEntities = [
        const Entity(
            text: 'AAPL',
            canonicalText: 'AAPL',
            displayText: 'Apple Inc.',
            type: EntityType.finance),
      ];
      service.updateEntities(initialEntities);

      final newEntities = [
        const Entity(
            text: 'AAPL (Updated)',
            canonicalText: 'AAPL (Updated)',
            displayText: 'Apple Inc.',
            type: EntityType.finance),
      ];
      service.updateEntities(newEntities);

      expect(service.entities.length, 1);
      expect(service.entities['aapl (updated)']?.text, 'AAPL (Updated)');
      expect(service.entities['aapl (updated)']?.canonicalText, 'AAPL (Updated)');
    });

    test('can retrieve an entity by text', () {
      final entities = [
        const Entity(
            text: 'AAPL',
            canonicalText: 'AAPL',
            displayText: 'Apple Inc.',
            type: EntityType.finance),
      ];
      service.updateEntities(entities);

      expect(service.entities['aapl']?.displayText, 'Apple Inc.');
    });

    test('clears existing variables when updating', () {
      final initialEntities = [
        const Entity(
            text: 'AAPL',
            canonicalText: 'AAPL',
            displayText: 'Apple Inc.',
            type: EntityType.finance),
      ];
      service.updateEntities(initialEntities);

      service.updateEntities([]);

      expect(service.entities, isEmpty);
    });
  });
}
