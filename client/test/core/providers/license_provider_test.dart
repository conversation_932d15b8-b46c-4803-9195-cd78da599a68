import 'package:flutter_test/flutter_test.dart';
import 'package:promz/core/models/license_model.dart';

/// This test file focuses only on the null safety handling in the license provider.
///
/// Instead of trying to mock the entire provider chain, we test the specific
/// helper function that was fixed: UserLicense.getLicenseTypeFromString and
/// the null safety handling for expiry_date parsing.
void main() {
  group('License Provider Null Safety', () {
    test('UserLicense.getLicenseTypeFromString should handle null license type', () {
      // Act
      final result = UserLicense.getLicenseTypeFromString(null);

      // Assert
      expect(result, equals(LicenseType.none));
    });

    test('UserLicense.getLicenseTypeFromString should handle empty license type', () {
      // Act
      final result = UserLicense.getLicenseTypeFromString('');

      // Assert
      expect(result, equals(LicenseType.none));
    });

    test('UserLicense.getLicenseTypeFromString should handle free license type', () {
      // Act
      final result = UserLicense.getLicenseTypeFromString('free');

      // Assert
      expect(result, equals(LicenseType.free));
    });

    test('Null expiry_date should be handled correctly', () {
      // This test verifies the fix we made in the license provider
      // by simulating the parsing logic directly

      // Arrange - simulate license data with null expiry_date
      final Map<String, dynamic> licenseData = {
        'license_id': 'test-id',
        'license_type': 'free',
        'is_active': true,
        'expiry_date': null, // This is what we're testing - null expiry date
        'api_key': 'test-key',
      };

      // Act - simulate the fixed code from license_provider.dart
      final expiresAt = licenseData['expiry_date'] != null
          ? DateTime.parse(licenseData['expiry_date'] as String)
          : DateTime.now().add(const Duration(days: 36500));

      // Assert
      expect(expiresAt, isNotNull);
      expect(
        expiresAt.difference(DateTime.now()).inDays,
        greaterThanOrEqualTo(36000),
      );
    });

    test('License provider should handle parsing expiry_date correctly', () {
      // This test verifies the fix we made in the license provider
      // by simulating the parsing logic directly

      // Arrange - simulate license data with valid expiry_date
      final Map<String, dynamic> licenseData = {
        'license_id': 'test-id',
        'license_type': 'pro',
        'is_active': true,
        'expiry_date': '2099-12-31T23:59:59Z',
        'api_key': 'test-key',
      };

      // Act - simulate the fixed code from license_provider.dart
      final expiresAt = licenseData['expiry_date'] != null
          ? DateTime.parse(licenseData['expiry_date'] as String)
          : DateTime.now().add(const Duration(days: 36500));

      // Assert
      expect(expiresAt, isNotNull);
      expect(expiresAt.year, equals(2099));
      expect(expiresAt.month, equals(12));
      expect(expiresAt.day, equals(31));
    });
  });
}
