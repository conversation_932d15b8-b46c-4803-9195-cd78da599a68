import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:fixnum/fixnum.dart';
// InputContentInfo is no longer used after refactoring
import 'package:promz/core/services/attachment/attachment_registry_service.dart';
import 'package:promz/core/services/content_processing_service.dart';
import 'package:promz/core/services/file/containers/zip_service.dart';
import 'package:promz/core/services/file/file_processing_client.dart';
import 'package:promz/core/services/file/file_upload_service.dart';
import 'package:promz/core/services/file_processing_service.dart';
import 'package:promz/core/services/license_manager_service.dart';
import 'package:promz/core/utils/duplicate_detection_registry.dart';
import 'package:promz/core/utils/hash_helper.dart';
import 'package:promz/features/home/<USER>/entity_detection_service.dart';
import 'package:promz/features/input_selection/models/input_source.dart';
import 'package:promz/features/news/services/news_article_service.dart';
import 'package:promz/features/youtube/services/youtube_service.dart';
import 'package:promz/generated/content_upload.pb.dart';
import 'package:promz/generated/common.pbenum.dart';
import 'package:promz_common/promz_common.dart';

// Define mocks manually to avoid conflicts
class MockAttachmentRegistryService extends Mock implements AttachmentRegistryService {
  // Flag to track if the proto method was called
  bool protoMethodCalled = false;

  @override
  void registerAttachment({required String id, required ProcessingResult result}) {
    protoMethodCalled = true;
  }

  @override
  void registerServerAttachment({
    required String id,
    required ProcessingResult initialResult,
    FileProcessingClient? client,
  }) {
    protoMethodCalled = true;
  }

  @override
  ProcessingResult? getMostRecentAttachmentByTypeProto(String type) => null;
}

class MockFileProcessingService extends Mock implements FileProcessingService {
  @override
  String getMimeType(String fileName) {
    if (fileName.endsWith('.pdf')) return 'application/pdf';
    if (fileName.endsWith('.md')) return 'text/markdown';
    return 'text/plain';
  }
}

class MockFileProcessingClient extends Mock implements FileProcessingClient {}

class MockFileUploadService extends Mock implements FileUploadService {
  // Manually track method calls for validation
  bool validateFileSizeCalled = false;

  @override
  Future<void> validateFileSize({
    required String fileName,
    required int fileSize,
    required String licenseTier,
  }) async {
    validateFileSizeCalled = true;
    return Future.value();
  }
}

class MockLicenseManagerService extends Mock implements LicenseManagerService {
  // Keep track of the license state for testing
  bool _isValid = true;

  void setLicenseValid(bool isValid) {
    _isValid = isValid;
  }

  @override
  bool isLicenseValid() => _isValid;

  LicenseTier get currentLicenseTier => LicenseTier.LICENSE_TIER_FREE;

  Future<void> initialize() async {}

  Stream<LicenseTier> get licenseTierStream => Stream.value(LicenseTier.LICENSE_TIER_FREE);
}

class MockZipService extends Mock implements ZipService {}

class MockEntityDetectionService extends Mock implements EntityDetectionService {
  int detectEntitiesCalls = 0;

  @override
  Future<List<Entity>> detectEntities(String text) async {
    detectEntitiesCalls++;
    return <Entity>[];
  }
}

class MockNewsArticleService extends Mock implements NewsArticleService {}

class MockYouTubeService extends Mock implements YouTubeService {}

void main() {
  AppLogger.setLogLevelForTest(LogLevel.error);
  TestWidgetsFlutterBinding.ensureInitialized();

  // Mock flutter_secure_storage channel
  const MethodChannel channel = MethodChannel('plugins.it_nomads.com/flutter_secure_storage');
  TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
      .setMockMethodCallHandler(channel, (MethodCall methodCall) async {
    if (methodCall.method == 'read') {
      return null;
    }
    return null;
  });

  group('ContentProcessingService Tests', () {
    late ContentProcessingService service;
    late MockAttachmentRegistryService mockAttachmentRegistry;
    late MockFileProcessingService mockFileProcessingService;
    late MockNewsArticleService mockNewsArticleService;
    late MockZipService mockZipService;
    late MockYouTubeService mockYouTubeService;
    late MockLicenseManagerService mockLicenseManagerService;
    late MockFileUploadService mockFileUploadService;
    late MockFileProcessingClient mockFileProcessingClient;
    late MockEntityDetectionService mockEntityDetectionService;

    setUp(() {
      mockAttachmentRegistry = MockAttachmentRegistryService();
      mockFileProcessingService = MockFileProcessingService();
      mockNewsArticleService = MockNewsArticleService();
      mockZipService = MockZipService();
      mockYouTubeService = MockYouTubeService();
      mockLicenseManagerService = MockLicenseManagerService();
      mockFileUploadService = MockFileUploadService();
      mockFileProcessingClient = MockFileProcessingClient();
      mockEntityDetectionService = MockEntityDetectionService();

      // Reset duplicate detection registry before each test
      DuplicateDetectionRegistry.instance.reset();

      // Set default valid license for most tests
      mockLicenseManagerService.setLicenseValid(true);

      service = ContentProcessingService(
        attachmentRegistry: mockAttachmentRegistry,
        fileProcessingService: mockFileProcessingService,
        newsArticleService: mockNewsArticleService,
        zipService: mockZipService,
        youtubeService: mockYouTubeService,
        fileUploadService: mockFileUploadService,
        licenseManagerService: mockLicenseManagerService,
        fileProcessingClient: mockFileProcessingClient,
        entityDetectionService: mockEntityDetectionService,
      );
    });

    tearDown(() async {
      // Clean up mock channel handler
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
          const MethodChannel('plugins.it_nomads.com/flutter_secure_storage'), null);

      // Reset duplicate detection registry after each test
      DuplicateDetectionRegistry.instance.reset();
    });

    group('License Checks', () {
      test('processInput throws exception if license is invalid', () async {
        // Arrange
        mockLicenseManagerService.setLicenseValid(false);
        const testInput = 'Some text input';

        // Act & Assert
        expect(
          () => service.processInput(testInput),
          throwsA(isA<Exception>().having(
            (e) => e.toString(),
            'message',
            contains('License required'),
          )),
        );
      });
    });

    group('Plain Text Processing', () {
      test('correctly processes simple text input', () async {
        // Arrange
        const testInput = 'This is simple text content.';
        const expectedFileName = 'This is simple text content.';
        mockAttachmentRegistry.protoMethodCalled = false;

        // Act
        final result = await service.processInput(testInput);

        // Assert
        expect(result, isNotNull);
        expect(result!.fileName, equals(expectedFileName));
        expect(result.content, equals(testInput));
        expect(result.contentHash, isNotEmpty);
        expect(result.type, equals(InputSourceType.text));

        // Verify interaction with AttachmentRegistry
        expect(mockAttachmentRegistry.protoMethodCalled, isTrue,
            reason: 'registerAttachment should be called');

        // Verify duplicate detection registration
        final contentHash = HashHelper.calculateStringHash(testInput);
        expect(DuplicateDetectionRegistry.instance.hashExists(contentHash), isTrue);

        // Verify entity detection was called
        expect(mockEntityDetectionService.detectEntitiesCalls, equals(1),
            reason: 'detectEntities should be called once');
      });

      test('detects duplicate text input', () async {
        // Arrange
        const testInput = 'This is duplicate text content.';
        final contentHash = HashHelper.calculateStringHash(testInput);

        // Verify hash doesn't exist initially
        expect(DuplicateDetectionRegistry.instance.hashExists(contentHash), isFalse);

        // Act: Process first time
        final result1 = await service.processInput(testInput);

        // Assert: First time processing
        expect(result1, isNotNull);
        expect(result1!.contentHash, equals(contentHash));
        expect(DuplicateDetectionRegistry.instance.hashExists(contentHash), isTrue);
        expect(mockEntityDetectionService.detectEntitiesCalls, equals(1),
            reason: 'detectEntities should be called once');

        // Reset counters for the next call verification
        mockEntityDetectionService.detectEntitiesCalls = 0;
        mockAttachmentRegistry.protoMethodCalled = false;

        // Act: Process second time
        final result2 = await service.processInput(testInput);

        // Assert: Second time processing
        expect(result2, isNotNull);
        expect(result2!.contentHash, equals(contentHash));
        // In the refactored version, we don't have a duplicateReason property
        // We just check that we got a valid InputSource back for the duplicate

        // Verify entity detection was NOT called again for duplicate
        expect(mockEntityDetectionService.detectEntitiesCalls, equals(0),
            reason: 'detectEntities should not be called for duplicates');
        // Verify attachment registry was NOT called again for duplicate
        expect(mockAttachmentRegistry.protoMethodCalled, isFalse,
            reason: 'registerAttachment should not be called for duplicates');
      });
    });

    group('Original Text Processing Tests', () {
      test('processInput with text should register with AttachmentRegistry', () async {
        // Arrange
        const textInput = 'Sample text content for testing';
        mockAttachmentRegistry.protoMethodCalled = false;

        // Act
        final result = await service.processInput(textInput);

        // Assert - check if the proto method was called
        expect(mockAttachmentRegistry.protoMethodCalled, isTrue,
            reason: 'registerAttachment should be called');
        expect(result, isNotNull);
        expect(result!.textContent, equals(textInput));
      });
    });

    group('Proto Migration Compatible Tests', () {
      test('text processing uses ProcessingResult structure', () async {
        // Arrange
        const textInput = 'Text content for protocol buffer compatibility testing';
        mockAttachmentRegistry.protoMethodCalled = false;

        // Act
        final result = await service.processInput(textInput);

        // Assert
        expect(mockAttachmentRegistry.protoMethodCalled, isTrue,
            reason: 'registerAttachmentProto should be called');
        expect(result?.textContent, equals(textInput));
      });

      test('server processing registers attachments using ProcessingResult', () async {
        // Arrange
        mockAttachmentRegistry.protoMethodCalled = false;
        final processingResult = ProcessingResult()
          ..contentType = 'text'
          ..content = 'Server processed content'
          ..fileMetadata = (FileMetadata()
            ..sizeBytes = Int64(100)
            ..contentHash = 'test_hash'
            ..lineCount = 5
            ..lastModified = DateTime.now().toIso8601String()
            ..detectedLanguage = 'en');

        // Act
        mockAttachmentRegistry.registerServerAttachment(
          id: 'test_server_id',
          initialResult: processingResult,
        );

        // Assert
        expect(mockAttachmentRegistry.protoMethodCalled, isTrue,
            reason: 'registerServerAttachmentProto should be called');
      });
    });
  });
}

// Helper extension for InputSource to simplify testing
extension InputSourceHelper on InputSource {
  String? get textContent => content;
}
