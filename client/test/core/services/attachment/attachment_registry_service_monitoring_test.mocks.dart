// Mocks generated by <PERSON>cki<PERSON> 5.4.6 from annotations
// in promz/test/core/services/attachment/attachment_registry_service_monitoring_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;
import 'dart:io' as _i6;

import 'package:mockito/mockito.dart' as _i1;
import 'package:promz/core/services/file/file_processing_client.dart' as _i2;
import 'package:promz/core/services/file/file_upload_service.dart' as _i3;
import 'package:promz/generated/content_upload.pbgrpc.dart' as _i4;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeFileMetadataCheckResponse_0 extends _i1.SmartFake
    implements _i2.FileMetadataCheckResponse {
  _FakeFileMetadataCheckResponse_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeFileUploadResponse_1 extends _i1.SmartFake implements _i3.FileUploadResponse {
  _FakeFileUploadResponse_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeProcessingResult_2 extends _i1.SmartFake implements _i4.ProcessingResult {
  _FakeProcessingResult_2(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeFileProcessingResult_3 extends _i1.SmartFake implements _i3.FileProcessingResult {
  _FakeFileProcessingResult_3(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [FileProcessingClient].
///
/// See the documentation for Mockito's code generation for more information.
class MockFileProcessingClient extends _i1.Mock implements _i2.FileProcessingClient {
  MockFileProcessingClient() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Future<_i2.FileMetadataCheckResponse> checkFileMetadata(
    _i6.File? file, {
    String? licenseTier,
    Map<String, dynamic>? metadata,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #checkFileMetadata,
          [file],
          {
            #licenseTier: licenseTier,
            #metadata: metadata,
          },
        ),
        returnValue:
            _i5.Future<_i2.FileMetadataCheckResponse>.value(_FakeFileMetadataCheckResponse_0(
          this,
          Invocation.method(
            #checkFileMetadata,
            [file],
            {
              #licenseTier: licenseTier,
              #metadata: metadata,
            },
          ),
        )),
      ) as _i5.Future<_i2.FileMetadataCheckResponse>);

  @override
  _i5.Future<_i3.FileUploadResponse> uploadFile(
    _i6.File? file, {
    required _i2.FileMetadataCheckResponse? metadataCheck,
    Map<String, dynamic>? metadata,
    void Function(double)? onProgress,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #uploadFile,
          [file],
          {
            #metadataCheck: metadataCheck,
            #metadata: metadata,
            #onProgress: onProgress,
          },
        ),
        returnValue: _i5.Future<_i3.FileUploadResponse>.value(_FakeFileUploadResponse_1(
          this,
          Invocation.method(
            #uploadFile,
            [file],
            {
              #metadataCheck: metadataCheck,
              #metadata: metadata,
              #onProgress: onProgress,
            },
          ),
        )),
      ) as _i5.Future<_i3.FileUploadResponse>);

  @override
  _i5.Future<_i4.ProcessingResult> getProcessingResultProto(String? jobId) => (super.noSuchMethod(
        Invocation.method(
          #getProcessingResultProto,
          [jobId],
        ),
        returnValue: _i5.Future<_i4.ProcessingResult>.value(_FakeProcessingResult_2(
          this,
          Invocation.method(
            #getProcessingResultProto,
            [jobId],
          ),
        )),
      ) as _i5.Future<_i4.ProcessingResult>);

  @override
  _i5.Future<_i3.FileProcessingResult> getResults(String? id) => (super.noSuchMethod(
        Invocation.method(
          #getResults,
          [id],
        ),
        returnValue: _i5.Future<_i3.FileProcessingResult>.value(_FakeFileProcessingResult_3(
          this,
          Invocation.method(
            #getResults,
            [id],
          ),
        )),
      ) as _i5.Future<_i3.FileProcessingResult>);

  @override
  _i5.Stream<_i3.FileProcessingStatus> subscribeToStatusUpdates(String? id) => (super.noSuchMethod(
        Invocation.method(
          #subscribeToStatusUpdates,
          [id],
        ),
        returnValue: _i5.Stream<_i3.FileProcessingStatus>.empty(),
      ) as _i5.Stream<_i3.FileProcessingStatus>);

  @override
  _i5.Stream<_i4.UploadUpdate> streamUploadUpdatesProto(String? jobId) => (super.noSuchMethod(
        Invocation.method(
          #streamUploadUpdatesProto,
          [jobId],
        ),
        returnValue: _i5.Stream<_i4.UploadUpdate>.empty(),
      ) as _i5.Stream<_i4.UploadUpdate>);

  @override
  _i5.Future<bool> cancelProcessing(String? id) => (super.noSuchMethod(
        Invocation.method(
          #cancelProcessing,
          [id],
        ),
        returnValue: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );
}
