import 'dart:async'; // Add async import
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart'; // Import mockito
import 'package:promz/core/services/attachment/attachment_registry_service.dart';
import 'package:promz/core/services/file/file_processing_client.dart';
import 'package:promz/generated/common.pbenum.dart';
import 'package:promz/generated/content_upload.pb.dart'; // Import pb types
import 'package:promz_common/promz_common.dart';
import 'attachment_registry_service_monitoring_test.mocks.dart';

@GenerateMocks([FileProcessingClient])
void main() {
  AppLogger.setLogLevelForTest(LogLevel.error);

  group('AttachmentRegistryService Tests', () {
    late AttachmentRegistryService service;
    late MockFileProcessingClient mockClient;

    setUp(() {
      service = AttachmentRegistryService();
      mockClient = MockFileProcessingClient();

      // *** FIX: Add default stub for streamUploadUpdatesProto ***
      when(mockClient.streamUploadUpdatesProto(any))
          .thenAnswer((_) => const Stream<UploadUpdate>.empty());
      // Add default stub for getProcessingResultProto as well, as it might be called by onDone
      when(mockClient.getProcessingResultProto(any))
          .thenAnswer((_) async => ProcessingResult()); // Return empty result
    });

    // Note: For testing purposes, we still need to use the deprecated registerAttachment method
    // in some tests since we're testing functionality that depends on it.

    test('registerAttachmentProto should store content with metadata', () async {
      // Arrange
      const id = 'test_id';
      const content = 'Sample content';
      const type = 'text';
      const fileName = 'test.txt';

      // Create a ProcessingResult
      final result = ProcessingResult()
        ..contentType = type
        ..content = content
        ..status = UploadStatus.UPLOAD_STATUS_COMPLETED
        ..expiresAt = DateTime.now().add(const Duration(hours: 24)).toIso8601String();

      // Add metadata using explicit fields
      result.fileName = fileName;
      result.mimeType = 'text/plain';

      // Act
      service.registerAttachment(
        id: id,
        result: result,
      );

      // Assert
      final attachment = await service.getAttachmentProto(id);
      expect(attachment, isNotNull);
      expect(attachment!.content, equals(content));
      expect(attachment.contentType, equals(type));
      expect(attachment.fileName, equals(fileName));
      expect(attachment.displayName, equals('test'));
    });

    test('registerAttachmentProto should handle custom metadata', () async {
      // Arrange
      const id = 'test_metadata_id';
      const content = 'Sample content with metadata';
      const type = 'document';
      const fileName = 'document.pdf';

      // Create a ProcessingResult
      final result = ProcessingResult()
        ..contentType = type
        ..content = content
        ..status = UploadStatus.UPLOAD_STATUS_COMPLETED
        ..expiresAt = DateTime.now().add(const Duration(hours: 24)).toIso8601String();

      // Add metadata using explicit fields
      result.fileName = fileName;
      result.mimeType = 'application/pdf';
      result.title = 'Custom Title';
      result.author = 'Test Author';

      // Act
      service.registerAttachment(
        id: id,
        result: result,
      );

      // Assert
      final attachment = await service.getAttachmentProto(id);
      expect(attachment, isNotNull);
      expect(attachment!.content, equals(content));
      expect(attachment.contentType, equals(type));
      expect(attachment.fileName, equals(fileName));
      expect(attachment.title, equals('Custom Title'));
      expect(attachment.author, equals('Test Author'));

      // The display name should be extracted from the fileName by default
      // or from the title metadata if available
      expect(attachment.displayName, equals('document'));
    });

    test('registerServerAttachmentProto should store server reference', () async {
      // Arrange
      const id = 'server_attachment_id';
      const serverId = 'server_123';
      const type = 'pdf';
      const fileName = 'server_doc.pdf';

      // Create a ProcessingResult
      final result = ProcessingResult()
        ..contentType = type
        ..content = 'ID:$serverId'
        ..jobId = serverId
        ..status = UploadStatus.UPLOAD_STATUS_QUEUED
        ..expiresAt = DateTime.now().add(const Duration(hours: 24)).toIso8601String();

      // Add metadata using explicit fields
      result.fileName = fileName;
      result.mimeType = 'application/pdf';
      result.isServerProcessed = true;
      // Manually set the display name since registerServerAttachmentProto doesn't extract it
      result.displayName = 'server_doc';

      // Act
      service.registerServerAttachment(
        client: mockClient,
        id: id,
        initialResult: result,
      );

      // Assert
      final attachment = await service.getAttachmentProto(id);
      expect(attachment, isNotNull);
      expect(attachment!.content, equals('ID:$serverId'));
      expect(attachment.contentType, equals(type));
      expect(attachment.fileName, equals(fileName));
      expect(attachment.displayName, equals('server_doc'));
      expect(attachment.isServerProcessed, isTrue);
      expect(attachment.jobId, equals(serverId));
    });

    test('getMostRecentAttachmentByTypeProto should return the most recent attachment', () async {
      // Arrange
      // Create the first (older) ProcessingResult
      final olderResult = ProcessingResult()
        ..contentType = 'text'
        ..content = 'Older content'
        ..status = UploadStatus.UPLOAD_STATUS_COMPLETED
        ..expiresAt = DateTime.now().add(const Duration(hours: 24)).toIso8601String();

      // Add metadata using explicit fields
      olderResult.fileName = 'older.txt';
      olderResult.mimeType = 'text/plain';
      olderResult.jobId = 'custom_id'; // Using jobId instead of id

      service.registerAttachment(
        id: 'older',
        result: olderResult,
      );

      // Wait a moment to ensure different timestamps
      await Future.delayed(const Duration(milliseconds: 50));

      // Create the second (newer) ProcessingResult
      final newerResult = ProcessingResult()
        ..contentType = 'text'
        ..content = 'Newer content'
        ..status = UploadStatus.UPLOAD_STATUS_COMPLETED
        ..expiresAt = DateTime.now().add(const Duration(hours: 24)).toIso8601String()
        ..fileName = 'newer.txt'
        ..mimeType = 'text/plain';

      service.registerAttachment(
        id: 'newer',
        result: newerResult,
      );

      // Act & Assert
      final mostRecent = service.getMostRecentAttachmentByTypeProto('text');
      expect(mostRecent, isNotNull);
      expect(mostRecent!.content, equals('Newer content'));
    });

    test('getAllServerAttachmentsProto should only return server attachments', () async {
      // Arrange
      // Create a local attachment
      final localResult = ProcessingResult()
        ..contentType = 'text'
        ..content = 'Local content'
        ..status = UploadStatus.UPLOAD_STATUS_COMPLETED
        ..expiresAt = DateTime.now().add(const Duration(hours: 24)).toIso8601String()
        ..fileName = 'local.txt'
        ..mimeType = 'text/plain';

      service.registerAttachment(
        id: 'local_id',
        result: localResult,
      );

      // Create first server attachment
      final serverResult1 = ProcessingResult()
        ..contentType = 'pdf'
        ..content = 'ID:server_123'
        ..jobId = 'server_123'
        ..status = UploadStatus.UPLOAD_STATUS_QUEUED
        ..expiresAt = DateTime.now().add(const Duration(hours: 24)).toIso8601String()
        ..fileName = 'server1.pdf'
        ..mimeType = 'application/pdf';

      service.registerServerAttachment(
        client: mockClient,
        id: 'server_id1',
        initialResult: serverResult1,
      );

      // Create second server attachment
      final serverResult2 = ProcessingResult()
        ..contentType = 'zip'
        ..content = 'ID:server_456'
        ..jobId = 'server_456'
        ..status = UploadStatus.UPLOAD_STATUS_QUEUED
        ..expiresAt = DateTime.now().add(const Duration(hours: 24)).toIso8601String()
        ..fileName = 'server2.zip'
        ..mimeType = 'application/zip';

      service.registerServerAttachment(
        client: mockClient,
        id: 'server_id2',
        initialResult: serverResult2,
      );

      // Act
      final serverAttachments = service.getAllServerAttachments();

      // Assert
      expect(serverAttachments.length, equals(2));
      expect(serverAttachments.any((a) => a.jobId == 'server_123'), isTrue);
      expect(serverAttachments.any((a) => a.jobId == 'server_456'), isTrue);
      expect(serverAttachments.any((a) => a.content == 'Local content'), isFalse);
    });

    test('isServerReference should detect server references', () {
      // Act & Assert
      expect(service.isServerReference('ID:12345'), isTrue);
      expect(service.isServerReference('Regular content'), isFalse);
      expect(service.isServerReference('URL:https://example.com'), isFalse);
    });

    test('isUrlReference should detect URL references', () {
      // Act & Assert
      expect(service.isUrlReference('URL:https://example.com'), isTrue);
      expect(service.isUrlReference('Regular content'), isFalse);
      expect(service.isUrlReference('ID:12345'), isFalse);
    });

    test('extractServerId should return serverId from reference', () {
      // Act & Assert
      expect(service.extractServerId('ID:12345'), equals('12345'));
      expect(service.extractServerId('Regular content'), isNull);
    });

    test('extractContentUrl should return URL from reference', () {
      // Act & Assert
      const url = 'https://example.com/file.pdf';
      expect(service.extractContentUrl('URL:$url'), equals(url));
      expect(service.extractContentUrl('Regular content'), isNull);
    });

    test('clearAttachments should remove all attachments', () async {
      // Arrange
      // Create first attachment
      final result1 = ProcessingResult()
        ..contentType = 'text'
        ..content = 'Content 1'
        ..status = UploadStatus.UPLOAD_STATUS_COMPLETED
        ..expiresAt = DateTime.now().add(const Duration(hours: 24)).toIso8601String();

      result1.fileName = 'file1.txt';
      result1.mimeType = 'text/plain';

      service.registerAttachment(
        id: 'id1',
        result: result1,
      );

      // Create second attachment
      final result2 = ProcessingResult()
        ..contentType = 'text'
        ..content = 'Content 2'
        ..status = UploadStatus.UPLOAD_STATUS_COMPLETED
        ..expiresAt = DateTime.now().add(const Duration(hours: 24)).toIso8601String();

      result2.fileName = 'file2.txt';
      result2.mimeType = 'text/plain';

      service.registerAttachment(
        id: 'id2',
        result: result2,
      );

      // Act
      service.clearAttachments();

      // Assert
      expect(await service.getAttachmentProto('id1'), isNull);
      expect(await service.getAttachmentProto('id2'), isNull);
      expect(service.hasAttachmentOfType('text'), isFalse);
    });

    // These tests will be useful for the proto migration
    group('Protobuf Implementation Tests', () {
      test('display name extraction from various metadata sources', () async {
        // Test with title in metadata
        final titleResult = ProcessingResult()
          ..contentType = 'text'
          ..content = 'Content'
          ..status = UploadStatus.UPLOAD_STATUS_COMPLETED
          ..expiresAt = DateTime.now().add(const Duration(hours: 24)).toIso8601String();

        titleResult.fileName = 'ignored.txt';
        titleResult.mimeType = 'text/plain';
        titleResult.title = 'Title from Metadata';

        service.registerAttachment(
          id: 'title_id',
          result: titleResult,
        );

        final titleAttachment = await service.getAttachmentProto('title_id');
        expect(titleAttachment!.displayName, equals('ignored'));

        // Test with WhatsApp metadata (would have highest priority but needs proper setup)
        // For now, we'll test with general metadata
        final whatsappResult = ProcessingResult()
          ..contentType = 'whatsapp'
          ..content = 'Content'
          ..status = UploadStatus.UPLOAD_STATUS_COMPLETED
          ..expiresAt = DateTime.now().add(const Duration(hours: 24)).toIso8601String();

        whatsappResult.fileName = 'chat.txt';
        whatsappResult.mimeType = 'text/plain';
        whatsappResult.title = 'Chat Title';

        service.registerAttachment(
          id: 'whatsapp_id',
          result: whatsappResult,
        );

        final whatsappAttachment = await service.getAttachmentProto('whatsapp_id');
        expect(whatsappAttachment!.displayName, equals('chat'));

        // Test fallback to fileName without extension
        final filenameResult = ProcessingResult()
          ..contentType = 'text'
          ..content = 'Content'
          ..status = UploadStatus.UPLOAD_STATUS_COMPLETED
          ..expiresAt = DateTime.now().add(const Duration(hours: 24)).toIso8601String();

        filenameResult.fileName = 'simple_name.txt';
        filenameResult.mimeType = 'text/plain';

        service.registerAttachment(
          id: 'filename_id',
          result: filenameResult,
        );

        final filenameAttachment = await service.getAttachmentProto('filename_id');
        expect(filenameAttachment!.displayName, equals('simple_name'));
      });
    });
  });
}
