import 'dart:async';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:promz/core/services/attachment/attachment_registry_service.dart';
import 'package:promz/core/services/file/file_processing_client.dart';
import 'package:promz/generated/common.pbenum.dart';
import 'package:promz/generated/content_upload.pb.dart';
import 'package:promz_common/promz_common.dart';
import 'attachment_registry_service_monitoring_test.mocks.dart';

@GenerateMocks([FileProcessingClient])
void main() {
  AppLogger.setLogLevelForTest(LogLevel.error);

  group('AttachmentRegistryService - Processing Status Monitoring', () {
    late AttachmentRegistryService service;
    late MockFileProcessingClient mockClient;
    late StreamController<UploadUpdate> protoController;
    late Completer<void> monitoringCompleter;

    setUp(() {
      service = AttachmentRegistryService();
      mockClient = MockFileProcessingClient();
      protoController = StreamController<UploadUpdate>();
      monitoringCompleter = Completer<void>();

      when(mockClient.streamUploadUpdatesProto(any)).thenAnswer((invocation) {
        final stream = protoController.stream;
        return stream.transform(StreamTransformer.fromHandlers(handleDone: (sink) {
          if (!monitoringCompleter.isCompleted) {
            monitoringCompleter.complete();
          }
          sink.close();
        }, handleError: (error, stack, sink) {
          if (!monitoringCompleter.isCompleted) {
            monitoringCompleter.completeError(error, stack);
          }
          sink.addError(error, stack);
        }));
      });
    });

    tearDown(() async {
      if (!protoController.isClosed) {
        await protoController.close();
      }
      if (!monitoringCompleter.isCompleted) {
        monitoringCompleter.completeError('Test finished prematurely');
      }
    });

    test('should update metadata when receiving status updates', () async {
      const id = 'attachment_id';
      const serverId = 'server_123';

      monitoringCompleter = Completer<void>();

      // Create a properly formatted URL content that passes the startsWith('URL:') check
      final completedResult = ProcessingResult()
        ..contentType = 'text/plain'
        ..jobId = serverId
        ..status = UploadStatus.UPLOAD_STATUS_COMPLETED
        ..content = 'URL:https://example.com/results/123' // Ensure this format is used exactly
        ..expiresAt = DateTime.now().add(const Duration(hours: 24)).toIso8601String()
        ..fileName = 'test.pdf'
        ..mimeType = 'application/pdf';

      when(mockClient.getProcessingResultProto(serverId)).thenAnswer((_) async {
        await Future.delayed(const Duration(milliseconds: 10));
        return completedResult;
      });

      final initialProcessingResult = ProcessingResult()
        ..contentType = 'pdf'
        ..jobId = serverId
        ..status = UploadStatus.UPLOAD_STATUS_QUEUED
        ..content = 'ID:$serverId'
        ..expiresAt = DateTime.now().add(const Duration(hours: 24)).toIso8601String()
        ..fileName = 'test.pdf'
        ..mimeType = 'application/pdf';

      service.registerServerAttachment(
        id: id,
        initialResult: initialProcessingResult,
        client: mockClient,
      );

      protoController.add(UploadUpdate()
        ..status = UploadStatus.UPLOAD_STATUS_PROCESSING
        ..progressPercentage = 45.0
        ..message = 'Extracting text');

      await Future.delayed(const Duration(milliseconds: 50));

      var attachment = await service.getAttachmentProto(id);
      expect(attachment, isNotNull);
      expect(attachment!.status, equals(UploadStatus.UPLOAD_STATUS_PROCESSING));
      expect(attachment.processingProgress, equals(45.0));

      protoController.add(UploadUpdate()
        ..status = UploadStatus.UPLOAD_STATUS_COMPLETED
        ..progressPercentage = 100.0
        ..message = 'Processing complete');

      // Close the stream to trigger the final result fetch
      await protoController.close();

      // Give more time for the service's onDone handler to complete
      // This ensures the getProcessingResultProto call finishes and content is updated
      await monitoringCompleter.future;

      // Add an extra delay to ensure all async operations complete
      await Future.delayed(const Duration(milliseconds: 150));

      attachment = await service.getAttachmentProto(id);
      expect(attachment, isNotNull);
      expect(attachment!.status, equals(UploadStatus.UPLOAD_STATUS_COMPLETED));
      expect(attachment.processingProgress, equals(100.0));
      expect(attachment.content.startsWith('URL:'), isTrue);
    });

    test('should handle error states in processing', () async {
      const id = 'error_attachment_id';
      const serverId = 'server_error_456';
      monitoringCompleter = Completer<void>();

      final failedResult = ProcessingResult()
        ..contentType = 'zip'
        ..jobId = serverId
        ..status = UploadStatus.UPLOAD_STATUS_FAILED
        ..content = 'ID:$serverId'
        ..errorMessage = 'Could not process corrupted file'
        ..expiresAt = DateTime.now().add(const Duration(hours: 24)).toIso8601String()
        ..fileName = 'corrupted.zip'
        ..mimeType = 'application/zip';

      when(mockClient.getProcessingResultProto(serverId)).thenAnswer((_) async {
        await Future.delayed(const Duration(milliseconds: 10));
        return failedResult;
      });

      final initialProcessingResult = ProcessingResult()
        ..contentType = 'zip'
        ..jobId = serverId
        ..status = UploadStatus.UPLOAD_STATUS_QUEUED
        ..content = 'ID:$serverId'
        ..expiresAt = DateTime.now().add(const Duration(hours: 24)).toIso8601String()
        ..fileName = 'corrupted.zip'
        ..mimeType = 'application/zip';

      service.registerServerAttachment(
        id: id,
        initialResult: initialProcessingResult,
        client: mockClient,
      );

      protoController.add(UploadUpdate()
        ..status = UploadStatus.UPLOAD_STATUS_FAILED
        ..progressPercentage = 0.0
        ..message = 'Could not process corrupted file'
        ..currentStage = 'Processing failed');

      await protoController.close();
      await monitoringCompleter.future;

      final attachment = await service.getAttachmentProto(id);
      expect(attachment, isNotNull);
      expect(attachment!.status, equals(UploadStatus.UPLOAD_STATUS_FAILED));
      expect(attachment.errorMessage, contains('Could not process corrupted file'));
      expect(attachment.content, equals('ID:$serverId'));
    });

    test('should handle stream error during processing', () async {
      const id = 'stream_error_attachment_id';
      const serverId = 'server_stream_error_789';
      monitoringCompleter = Completer<void>();

      final initialProcessingResult = ProcessingResult()
        ..contentType = 'text'
        ..jobId = serverId
        ..status = UploadStatus.UPLOAD_STATUS_QUEUED
        ..content = 'ID:$serverId'
        ..expiresAt = DateTime.now().add(const Duration(hours: 24)).toIso8601String()
        ..fileName = 'stream_error.txt'
        ..mimeType = 'text/plain';

      service.registerServerAttachment(
        id: id,
        initialResult: initialProcessingResult,
        client: mockClient,
      );

      protoController.add(UploadUpdate()
        ..status = UploadStatus.UPLOAD_STATUS_PROCESSING
        ..progressPercentage = 30.0
        ..message = 'Working...');

      AppLogger.suppressErrorsForTest();
      await Future.delayed(const Duration(milliseconds: 50));
      final streamError = Exception('gRPC connection lost');
      protoController.addError(streamError);
      try {
        await monitoringCompleter.future;
      } catch (e) {
        expect(e, equals(streamError));
      }
      AppLogger.setLogLevelForTest(LogLevel.error);

      final attachment = await service.getAttachmentProto(id);
      expect(attachment, isNotNull);
      expect(attachment!.status, equals(UploadStatus.UPLOAD_STATUS_FAILED));
      expect(attachment.errorMessage, contains('Monitoring stream error: $streamError'));
      expect(attachment.processingProgress, equals(30.0));
      expect(attachment.content, equals('ID:$serverId'));
    });

    test('should handle removal of attachment during monitoring', () async {
      // Arrange
      const id = 'temporary_id';
      const serverId = 'server_789';
      monitoringCompleter = Completer<void>();

      // Stub getProcessingResultProto - might be called if onDone triggers before removal check
      final processingResult = ProcessingResult()..jobId = serverId;
      when(mockClient.getProcessingResultProto(serverId)).thenAnswer((_) async => processingResult);

      final initialResult = ProcessingResult()
        ..contentType = 'text'
        ..jobId = serverId
        ..status = UploadStatus.UPLOAD_STATUS_QUEUED
        ..content = 'ID:$serverId'
        ..fileName = 'temp.txt'
        ..mimeType = 'text/plain';

      // Register attachment
      service.registerServerAttachment(
        id: id,
        initialResult: initialResult,
        client: mockClient,
      );

      // Act: Send an update
      protoController.add(UploadUpdate()
        ..status = UploadStatus.UPLOAD_STATUS_PROCESSING
        ..progressPercentage = 50.0);

      // Allow update to be processed
      await Future.delayed(const Duration(milliseconds: 50));

      // *** Act: Remove the attachment ***
      service.removeAttachment(id);

      // Allow removal logic (including subscription cancellation) to execute
      await Future.delayed(const Duration(milliseconds: 50));

      // Try sending another update - it should be ignored by the service
      protoController.add(UploadUpdate()
        ..status = UploadStatus.UPLOAD_STATUS_COMPLETED
        ..progressPercentage = 100.0);

      // Close the stream - this might trigger onDone in the (now cancelled) listener, but it should exit early
      await protoController.close();

      // *** FIX: Removed await for monitoringCompleter ***
      // Rely on delay to allow cancellation/removal to propagate
      await Future.delayed(const Duration(milliseconds: 100));

      // Assert: Attachment should be removed
      final attachment = await service.getAttachmentProto(id);
      expect(attachment, isNull, reason: 'Attachment should be null after removal');

      // Complete the monitoring completer normally instead of with an error
      if (!monitoringCompleter.isCompleted) {
        monitoringCompleter.complete();
      }

      // Cleanup handled by tearDown
    });
  });
}
