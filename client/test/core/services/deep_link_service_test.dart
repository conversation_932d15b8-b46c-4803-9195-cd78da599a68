import 'dart:convert';
import 'dart:typed_data';

import 'package:flutter_test/flutter_test.dart';
import 'package:promz/core/services/deep_link_service.dart';
import 'package:promz_common/promz_common.dart';
import 'package:base_codecs/base_codecs.dart';
import '../../database/database_test_base.dart';

class DeepLinkServiceTest extends DatabaseTestBase {
  late DeepLinkService deepLinkService;
  static const logName = 'DeepLinkServiceTest';
}

void main() {
  AppLogger.setLogLevelForTest(LogLevel.error);
  late DeepLinkServiceTest tester;

  setUp(() async {
    // Create the test environment
    tester = DeepLinkServiceTest();
    await tester.setup();

    // Create an instance of DeepLinkService
    tester.deepLinkService = DeepLinkService();
  });

  tearDown(() async {
    await tester.teardown();
  });

  group('DeepLinkService - URL parsing', () {
    // Use a standard UUID format for testing
    const testUuid = '123e4567-e89b-12d3-a456-************';
    final encoded = base58BitcoinEncode(Uint8List.fromList(utf8.encode(testUuid)));
    final decoded = utf8.decode(base58BitcoinDecode(encoded));

    // Print for debugging
    appLog.debug('Testing with UUID: $testUuid', name: DeepLinkServiceTest.logName);
    appLog.debug('Generated Base58 short ID for testing: $encoded',
        name: DeepLinkServiceTest.logName);
    appLog.debug('Expanded back to: $decoded', name: DeepLinkServiceTest.logName);

    // Create test URLs
    final pathBasedUrl = 'promz://p/$encoded'; // canonical
    final webUrl = 'https://www.promz.ai/p/$encoded'; // canonical

    test('returns false for empty URL', () async {
      final result = await tester.deepLinkService.processPromptLink('');

      expect(result.$1, isFalse, reason: 'Should return false for empty URL');
      expect(result.$2, contains('Empty URL'), reason: 'Should return error message');
      expect(result.$3, isNull, reason: 'Should return null promptId');
    });

    test('returns false for invalid URL format', () async {
      final result = await tester.deepLinkService.processPromptLink('invalid://url');

      expect(result.$1, isFalse, reason: 'Should return false for invalid URL');
      expect(result.$2, contains('Invalid prompt link'), reason: 'Should return error message');
      expect(result.$3, isNull, reason: 'Should return null promptId');
    });

    test('returns false when short ID cannot be expanded', () async {
      // Create a URL with a short ID that cannot be expanded
      const invalidUrl = 'promz://p/invalid';

      final result = await tester.deepLinkService.processPromptLink(invalidUrl);

      expect(result.$1, isFalse, reason: 'Should return false for invalid short ID');
      // The error message should contain 'Invalid prompt ID' for short IDs that can't be expanded
      expect(result.$2.contains('Invalid prompt ID'), isTrue,
          reason: 'Should return appropriate error message');
      // The promptId should be null for invalid short IDs
      expect(result.$3, isNull, reason: 'PromptId should be null for invalid short ID');
    });

    test('correctly extracts and expands short IDs from different URL formats', () async {
      // Test with different URL formats
      for (final url in [pathBasedUrl, webUrl]) {
        // We're not testing database interaction here, just the URL parsing
        final result = await tester.deepLinkService.processPromptLink(url);

        // The prompt won't be found in the test database, but the UUID extraction should work
        expect(result.$3, equals(decoded),
            reason: 'Should extract and expand the correct UUID from: $url');
      }
    });

    // Test case for Base58 encoding and decoding
    test('Encodes and decodes UUID using Base58', () {
      const uuid = '550e8400-e29b-41d4-a716-************';
      final encoded = base58BitcoinEncode(Uint8List.fromList(utf8.encode(uuid)));
      final decoded = utf8.decode(base58BitcoinDecode(encoded));
      expect(decoded, uuid);
    });
  });

  group('DeepLinkService - processDeepLink', () {
    test('routes prompt links to processPromptLink', () async {
      // Create a spy DeepLinkService to verify method calls
      final spyService = SpyDeepLinkService();

      // Test with different URL formats
      final testUrls = [
        'promz://p/AbCdEf',
        'promz://legacy',
        'https://www.promz.ai/p/123456',
      ];

      for (final url in testUrls) {
        await spyService.processDeepLink(url);

        expect(spyService.processPromptLinkCalled, isTrue,
            reason: 'Should call processPromptLink for URL: $url');
        expect(spyService.lastProcessedUrl, equals(url),
            reason: 'Should pass the URL to processPromptLink');

        // Reset for next test
        spyService.reset();
      }
    });

    test('handles unknown link types', () async {
      final result = await tester.deepLinkService.processDeepLink('https://example.com/not-promz');

      expect(result.$1, isFalse, reason: 'Should return false for unknown link type');
      expect(result.$2, contains('Unknown link type'),
          reason: 'Should return unknown type message');
      expect(result.$3, isNull, reason: 'Should return null promptId');
    });
  });
}

// Spy class to verify method calls
class SpyDeepLinkService extends DeepLinkService {
  bool processPromptLinkCalled = false;
  String? lastProcessedUrl;

  @override
  Future<(bool, String, String?)> processPromptLink(String url) async {
    processPromptLinkCalled = true;
    lastProcessedUrl = url;
    return (false, 'Test response', null);
  }

  void reset() {
    processPromptLinkCalled = false;
    lastProcessedUrl = null;
  }
}
