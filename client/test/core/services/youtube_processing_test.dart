import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:http/http.dart' as http;
import 'package:mockito/mockito.dart';
import 'package:promz/core/services/attachment/attachment_registry_service.dart';
import 'package:promz/core/services/content_processing_service.dart';
import 'package:promz/core/services/file/containers/zip_service.dart';
import 'package:promz/core/services/file/file_processing_client.dart';
import 'package:promz/core/services/file/file_upload_service.dart';
import 'package:promz/core/services/file_processing_service.dart';
import 'package:promz/core/services/license_manager_service.dart';
import 'package:promz/features/home/<USER>/entity_detection_service.dart';
import 'package:promz/features/input_selection/models/input_source.dart';
import 'package:promz/features/news/services/news_article_service.dart';
import 'package:promz/features/youtube/services/youtube_service.dart';
import 'package:promz/generated/content_upload.pb.dart';
import 'package:promz_common/promz_common.dart';
import '../../core/utils/url_resolver_patch.dart';

// Define mocks manually to avoid conflicts
class MockAttachmentRegistryService extends Mock implements AttachmentRegistryService {
  // Flag to track if the proto method was called
  bool registerAttachmentCalled = false;

  @override
  void registerAttachment({required String id, required ProcessingResult result}) {
    registerAttachmentCalled = true;
  }

  @override
  void registerServerAttachment({
    required String id,
    required ProcessingResult initialResult,
    FileProcessingClient? client,
  }) {
    // Not used in this test
  }

  @override
  ProcessingResult? getMostRecentAttachmentByTypeProto(String type) => null;
}

class MockFileProcessingService extends Mock implements FileProcessingService {
  @override
  String getMimeType(String fileName) {
    if (fileName.endsWith('.pdf')) return 'application/pdf';
    if (fileName.endsWith('.md')) return 'text/markdown';
    return 'text/plain';
  }
}

class MockFileProcessingClient extends Mock implements FileProcessingClient {}

class MockHttpClient extends Mock implements http.Client {
  @override
  Future<http.Response> head(Uri url, {Map<String, String>? headers}) {
    // Set this client as the global test HTTP client
    globalTestHttpClient = this;

    // Handle different URLs differently
    if (url.toString().contains('search.app/hWgeF')) {
      // For the problematic short URL, return a 200 status to trigger the special handling
      return Future.value(http.Response('', 200));
    } else if (url.toString().contains('youtube.com')) {
      // For YouTube URLs, return a 200 status
      return Future.value(http.Response('', 200));
    } else {
      // For other URLs, return a 200 status
      return Future.value(http.Response('', 200));
    }
  }

  @override
  Future<http.Response> get(Uri url, {Map<String, String>? headers}) {
    // Set this client as the global test HTTP client
    globalTestHttpClient = this;

    // Handle different URLs differently
    if (url.toString().contains('search.app/hWgeF')) {
      // For the short search.app URL, return HTML that contains a YouTube URL
      // This simulates what would happen if the URL was properly resolved
      return Future.value(http.Response(
        '<html><body>Redirecting to <a href="https://www.youtube.com/watch?v=dFObux6mfTc">YouTube</a></body></html>',
        200,
      ));
    } else if (url.toString().contains('youtube.com')) {
      // For YouTube URLs, return a 200 status with YouTube content
      return Future.value(http.Response(
        '<html><body>https://www.youtube.com/watch?v=dFObux6mfTc</body></html>',
        200,
      ));
    } else {
      // For other URLs, return a 200 status with empty content
      return Future.value(http.Response('', 200));
    }
  }
}

class MockFileUploadService extends Mock implements FileUploadService {}

class MockLicenseManagerService extends Mock implements LicenseManagerService {
  @override
  bool isLicenseValid() => true;
}

class MockZipService extends Mock implements ZipService {}

class MockEntityDetectionService extends Mock implements EntityDetectionService {
  // Track if the register entity method was called
  bool registerEntityCalled = false;

  @override
  void registerEntity({
    required String id,
    required String text,
    required String type,
    ProcessingResult? processingResult,
    Map<String, dynamic>? metadata,
  }) {
    registerEntityCalled = true;
  }
}

class MockNewsArticleService extends Mock implements NewsArticleService {}

class CustomMockYouTubeService extends Mock implements YouTubeService {
  final String videoId;
  final String title;
  final String channelName;
  final String description;
  final bool shouldSucceed;

  CustomMockYouTubeService({
    required this.videoId,
    required this.title,
    required this.channelName,
    required this.description,
    this.shouldSucceed = true,
  });

  @override
  Future<ProcessingResult> fetchVideoMetadata(String url) async {
    if (!shouldSucceed) {
      throw Exception('Failed to fetch video metadata');
    }

    // Create YouTube metadata
    final youtubeMetadata = YouTubeMetadata()
      ..videoId = videoId
      ..title = title
      ..channelName = channelName
      ..description = description
      ..thumbnailUrl = 'https://img.youtube.com/vi/$videoId/maxresdefault.jpg'
      ..url = url;

    // Create the ProcessingResult
    final processingResult = ProcessingResult()
      ..contentType = 'youtube_video'
      ..content = 'YouTube Video: $title\nChannel: $channelName\nDescription: $description'
      ..sourceUrl = url
      ..youtubeMetadata = youtubeMetadata;

    return processingResult;
  }
}

// Create a custom URL resolver that uses our mock HTTP client
class TestUrlResolver {
  static MockHttpClient? _mockClient;

  static void initialize() {
    _mockClient = MockHttpClient();
    globalTestHttpClient = _mockClient;
  }

  static void reset() {
    _mockClient = null;
    globalTestHttpClient = null;
  }
}

void main() {
  AppLogger.setLogLevelForTest(LogLevel.error);
  TestWidgetsFlutterBinding.ensureInitialized();

  // Mock flutter_secure_storage channel
  const MethodChannel channel = MethodChannel('plugins.it_nomads.com/flutter_secure_storage');
  TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
      .setMockMethodCallHandler(channel, (MethodCall methodCall) async {
    return null;
  });

  // Initialize our test URL resolver
  TestUrlResolver.initialize();

  // Apply the URL resolver patch
  applyUrlResolverPatch();

  group('YouTube Processing Tests', () {
    late ContentProcessingService service;
    late MockAttachmentRegistryService mockAttachmentRegistry;
    late MockFileProcessingService mockFileProcessingService;
    late MockNewsArticleService mockNewsArticleService;
    late MockZipService mockZipService;
    late CustomMockYouTubeService mockYouTubeService;
    late MockLicenseManagerService mockLicenseManagerService;
    late MockFileUploadService mockFileUploadService;
    late MockFileProcessingClient mockFileProcessingClient;
    late MockEntityDetectionService mockEntityDetectionService;

    setUp(() {
      mockAttachmentRegistry = MockAttachmentRegistryService();
      mockFileProcessingService = MockFileProcessingService();
      mockNewsArticleService = MockNewsArticleService();
      mockZipService = MockZipService();
      mockEntityDetectionService = MockEntityDetectionService();
      mockLicenseManagerService = MockLicenseManagerService();
      mockFileUploadService = MockFileUploadService();
      mockFileProcessingClient = MockFileProcessingClient();

      // Default mock with a known video ID
      mockYouTubeService = CustomMockYouTubeService(
        videoId: 'dFObux6mfTc',
        title: 'Arm you glad to see me, Atlas? | Boston Dynamics',
        channelName: 'Boston Dynamics',
        description: 'Test description for Boston Dynamics video',
      );

      // Initialize license manager first
      LicenseManagerService.initialize();

      // Create the service with all required dependencies
      service = ContentProcessingService(
        attachmentRegistry: mockAttachmentRegistry,
        fileProcessingService: mockFileProcessingService,
        newsArticleService: mockNewsArticleService,
        zipService: mockZipService,
        youtubeService: mockYouTubeService,
        fileUploadService: mockFileUploadService,
        licenseManagerService: mockLicenseManagerService,
        fileProcessingClient: mockFileProcessingClient,
        entityDetectionService: mockEntityDetectionService,
      );
    });

    tearDown(() async {
      // Clean up mock channel handler
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
          const MethodChannel('plugins.it_nomads.com/flutter_secure_storage'), null);

      // Reset our test URL resolver
      TestUrlResolver.reset();
    });

    test('Process YouTube video from search.app link with explicit YouTube mention', () async {
      // Arrange
      const input = '''
Arm you glad to see me, Atlas? | Boston Dynamics

Source: YouTube
https://search.app/?link=https://www.youtube.com/watch?v%3DdFObux6mfTc&utm_source=dsdf,sh/x/discover/m5/4
''';

      // Act
      final result = await service.processInput(input);

      // Assert
      expect(result, isNotNull);
      // Add null assertion operator to handle null safety
      expect(result!.type, equals(InputSourceType.youtubeVideo));
      expect(result.processingResult?.youtubeMetadata.videoId, equals('dFObux6mfTc'));
      expect(result.processingResult?.youtubeMetadata.title,
          equals('Arm you glad to see me, Atlas? | Boston Dynamics'));
      expect(mockAttachmentRegistry.registerAttachmentCalled, isTrue);
      expect(mockEntityDetectionService.registerEntityCalled, isTrue);
    });

    test('Process YouTube video from direct YouTube link', () async {
      // Arrange
      const input = 'https://www.youtube.com/watch?v=dFObux6mfTc';

      // Act
      final result = await service.processInput(input);

      // Assert
      expect(result, isNotNull);
      // Add null assertion operator to handle null safety
      expect(result!.type, equals(InputSourceType.youtubeVideo));
      expect(result.processingResult?.youtubeMetadata.videoId, equals('dFObux6mfTc'));
      expect(result.processingResult?.youtubeMetadata.title,
          equals('Arm you glad to see me, Atlas? | Boston Dynamics'));
      expect(mockAttachmentRegistry.registerAttachmentCalled, isTrue);
      expect(mockEntityDetectionService.registerEntityCalled, isTrue);
    });
  });
}
