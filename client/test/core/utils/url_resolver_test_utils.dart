import 'package:http/http.dart' as http;

/// Test utilities for URL resolver
/// This class provides a way to intercept HTTP client creation in the URL resolver
/// and use a mock client during tests
class UrlResolverTestUtils {
  static http.Client? _mockClient;

  /// Set a mock HTTP client to be used by the URL resolver
  static void setMockClient(http.Client mockClient) {
    _mockClient = mockClient;
  }

  /// Reset the mock HTTP client
  static void resetMockClient() {
    _mockClient = null;
  }

  /// Get the HTTP client to use
  /// Returns the mock client if one is set, otherwise creates a new client
  static http.Client getHttpClient() {
    return _mockClient ?? http.Client();
  }
}
