import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:promz/core/utils/deep_link_utils.dart';
import 'package:promz_common/promz_common.dart';

void main() {
  AppLogger.setLogLevelForTest(LogLevel.error);
  TestWidgetsFlutterBinding.ensureInitialized();

  group('DeepLinkUtils', () {
    const channel = MethodChannel('ai.promz/deeplink');
    final log = <MethodCall>[];

    setUp(() {
      // Clear the log before each test
      log.clear();

      // Set up mock handler for the method channel
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(channel, (MethodCall methodCall) async {
        log.add(methodCall);
        return null;
      });

      // Initialize DeepLinkUtils for each test
      DeepLinkUtils.initialize();
    });

    tearDown(() {
      // Reset the method channel handler after each test
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(channel, null);

      // Dispose DeepLinkUtils
      DeepLinkUtils.dispose();
    });

    test('notifyLinkReceived adds link to stream', () async {
      // Create a list to collect emitted items
      final receivedLinks = <Uri>[];

      // Subscribe to the links stream
      final subscription = DeepLinkUtils.links.listen(receivedLinks.add);

      // Test with various link formats
      const testLinks = [
        'promz://p/AbCdEf',
        'promz://legacy',
        'https://www.promz.ai/p/123456',
      ];

      // Notify each link
      for (final link in testLinks) {
        DeepLinkUtils.notifyLinkReceived(link);
      }

      // Wait for stream events to be processed
      await Future.delayed(const Duration(milliseconds: 100));

      // Verify results
      expect(receivedLinks.length, equals(testLinks.length),
          reason: 'Should receive all notified links');

      for (int i = 0; i < testLinks.length; i++) {
        expect(receivedLinks[i].toString(), equals(Uri.parse(testLinks[i]).toString()),
            reason: 'Uri should match the notified link');
      }

      // Clean up subscription
      await subscription.cancel();
    });
  });
}
