import 'dart:convert';
import 'dart:io';
import 'package:archive/archive.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:promz/core/services/attachment/attachment_registry_service.dart';
import 'package:promz/core/services/file/containers/zip_service.dart';
import 'package:promz/core/services/file/types/text_service.dart';
import 'package:promz_common/promz_common.dart';

void main() {
  AppLogger.setLogLevelForTest(LogLevel.error);
  group('ZipService', () {
    late ZipService service;
    late Directory tempDir;

    setUp(() {
      // Create a mock attachment registry service for testing
      final attachmentRegistry = AttachmentRegistryService();
      service = ZipService([TextFileService()], attachmentRegistry);
      tempDir = Directory.systemTemp.createTempSync();
    });

    tearDown(() {
      tempDir.deleteSync(recursive: true);
    });

    group('canHandle', () {
      test('accepts ZIP files by extension', () {
        expect(service.canHandle('test.zip'), isTrue);
      });

      test('accepts ZIP files by MIME type', () {
        expect(service.canHandle('file', 'application/zip'), isTrue);
        expect(service.canHandle('file', 'application/x-zip-compressed'), isTrue);
        expect(service.canHandle('file', 'application/x-zip'), isTrue);
      });

      test('rejects non-ZIP files', () {
        expect(service.canHandle('test.txt'), isFalse);
        expect(service.canHandle('file', 'text/plain'), isFalse);
      });
    });

    group('extractFiles', () {
      test('extracts files with correct content and metadata', () async {
        // Create a test ZIP with multiple files
        final archive = Archive()
          ..addFile(ArchiveFile('test.txt', 5, utf8.encode('Hello')))
          ..addFile(ArchiveFile(
            'data.json',
            15,
            utf8.encode('{"key":"value"}'),
          ));

        final zipFile = File('${tempDir.path}/test.zip')
          ..writeAsBytesSync(ZipEncoder().encode(archive));

        final files = await service.extractFiles(zipFile.path);
        expect(files.length, equals(2));

        // Check first file (test.txt)
        final txtFile = files.firstWhere((f) => f.fileName == 'test.txt');
        expect(txtFile.textContent, equals('Hello'));
        expect(txtFile.size, equals(5));
        expect(txtFile.mimeType, equals('text/plain'));

        // Check second file (data.json)
        final jsonFile = files.firstWhere((f) => f.fileName == 'data.json');
        expect(jsonFile.textContent, equals('{"key":"value"}'));
        expect(jsonFile.size, equals(15));
        expect(jsonFile.mimeType, equals('application/json'));
      });

      test('handles empty ZIP file', () async {
        final archive = Archive();
        final zipFile = File('${tempDir.path}/empty.zip')
          ..writeAsBytesSync(ZipEncoder().encode(archive));

        final files = await service.extractFiles(zipFile.path);
        expect(files, isEmpty);
      });

      test('handles ZIP with directories', () async {
        // Create a test ZIP with a file in a directory
        final archive = Archive();

        // Add a regular file in a directory path
        final fileInDir = ArchiveFile(
          'dir/test.txt',
          5,
          utf8.encode('Hello'),
        );
        fileInDir.isFile = true;
        archive.addFile(fileInDir);

        final zipFile = File('${tempDir.path}/with_dir.zip')
          ..writeAsBytesSync(ZipEncoder().encode(archive));

        final files = await service.extractFiles(zipFile.path);
        expect(files.length, equals(1)); // Should only include the file, not the directory
        expect(files.first.fileName, equals('dir/test.txt'));
      });

      test('handles corrupted ZIP file', () async {
        final corruptedFile = File('${tempDir.path}/corrupted.zip')
          ..writeAsBytesSync([1, 2, 3, 4]); // Invalid ZIP format

        final files = await service.extractFiles(corruptedFile.path);
        expect(files, isEmpty);
      });

      test('handles different text encodings', () async {
        final archive = Archive()
          ..addFile(ArchiveFile(
            'utf8.txt',
            6,
            utf8.encode('Hello 世界'),
          ))
          ..addFile(ArchiveFile(
            'latin1.txt',
            6,
            latin1.encode('Hello é'),
          ));

        final zipFile = File('${tempDir.path}/encodings.zip')
          ..writeAsBytesSync(ZipEncoder().encode(archive));

        final files = await service.extractFiles(zipFile.path);
        expect(files.length, equals(2));

        final utf8File = files.firstWhere((f) => f.fileName == 'utf8.txt');
        expect(utf8File.textContent, contains('世界'));

        final latin1File = files.firstWhere((f) => f.fileName == 'latin1.txt');
        expect(latin1File.textContent, contains('é'));
      });
    });

    group('extractMetadata', () {
      test('extracts correct metadata', () async {
        const content = 'Hello World';
        final archive = Archive()
          ..addFile(ArchiveFile('test.txt', content.length, utf8.encode(content)));

        final zipFile = File('${tempDir.path}/test.zip')
          ..writeAsBytesSync(ZipEncoder().encode(archive));

        final metadata = await service.extractMetadata(zipFile.path);

        expect(metadata['fileCount'], equals(1));
        expect(metadata['totalSize'], greaterThan(0));
        expect(metadata['compressedSize'], greaterThan(0));
        expect(metadata['compressionRatio'], greaterThan(0));
        expect(metadata['lastModified'], isNotNull);

        final files = metadata['files'] as List;
        expect(files.length, equals(1));
        expect(files.first['name'], equals('test.txt'));
        expect(files.first['size'], equals(content.length));
      });

      test('handles missing file gracefully', () async {
        AppLogger.suppressErrorsForTest();
        final metadata = await service.extractMetadata('${tempDir.path}/nonexistent.zip');
        expect(metadata, isEmpty);
        AppLogger.setLogLevelForTest(LogLevel.error);
      });
    });

    test('getMimeType returns application/zip', () {
      expect(service.getMimeType('test.zip'), equals('application/zip'));
    });

    test('getTypeName returns ZIP Archive', () {
      expect(service.getTypeName(), equals('ZIP Archive'));
    });
  });
}
