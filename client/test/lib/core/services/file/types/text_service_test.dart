import 'dart:convert';
import 'dart:io';
import 'package:flutter_test/flutter_test.dart';
import 'package:promz/core/services/file/types/text_service.dart';

void main() {
  group('TextFileService', () {
    late TextFileService service;
    late Directory tempDir;

    setUp(() {
      service = TextFileService();
      tempDir = Directory.systemTemp.createTempSync();
    });

    tearDown(() {
      tempDir.deleteSync(recursive: true);
    });

    group('canHandle', () {
      test('accepts text files by extension', () {
        expect(service.canHandle('test.txt'), isTrue);
        expect(service.canHandle('data.json'), isTrue);
        expect(service.canHandle('config.xml'), isTrue);
      });

      test('accepts text files by MIME type', () {
        expect(service.canHandle('file', 'text/plain'), isTrue);
        expect(service.canHandle('file', 'text/html'), isTrue);
        expect(service.canHandle('file', 'application/json'), isTrue);
        expect(service.canHandle('file', 'application/xml'), isTrue);
      });

      test('rejects non-text files', () {
        expect(service.canHandle('image.png'), isFalse);
        expect(service.canHandle('doc.pdf'), isFalse);
        expect(service.canHandle('file', 'image/jpeg'), isFalse);
      });
    });

    group('extractContent', () {
      test('reads UTF-8 text correctly', () async {
        final file = File('${tempDir.path}/test.txt')
          ..writeAsStringSync('Hello 世界', encoding: utf8);

        final content = await service.extractContent(file.path);
        expect(content, equals('Hello 世界'));
      });

      test('reads Latin1 text as fallback', () async {
        final file = File('${tempDir.path}/test.txt')..writeAsBytesSync(latin1.encode('Hello é'));

        final content = await service.extractContent(file.path);
        expect(content, equals('Hello é'));
      });

      test('handles missing file gracefully', () async {
        final content = await service.extractContent('${tempDir.path}/nonexistent.txt');
        expect(content, isNull);
      });

      test('handles empty file', () async {
        final file = File('${tempDir.path}/empty.txt')..writeAsStringSync('');

        final content = await service.extractContent(file.path);
        expect(content, isEmpty);
      });
    });

    group('extractMetadata', () {
      test('extracts basic file metadata', () async {
        final now = DateTime.now();
        const content = 'Hello\nWorld\nTest';
        final file = File('${tempDir.path}/test.txt')..writeAsStringSync(content);

        final metadata = await service.extractMetadata(file.path);

        expect(metadata['size'], equals(content.length));
        expect(metadata['lineCount'], equals(3));
        expect(metadata['encoding'], equals('UTF-8'));

        final modified = DateTime.parse(metadata['modified'] as String);
        expect(modified.difference(now).inSeconds.abs(), lessThan(2));
      });

      test('handles missing file gracefully', () async {
        final metadata = await service.extractMetadata('${tempDir.path}/nonexistent.txt');
        expect(metadata, isEmpty);
      });

      test('detects UTF-8 with BOM', () async {
        final file = File('${tempDir.path}/test.txt')
          ..writeAsBytesSync([0xEF, 0xBB, 0xBF, ...utf8.encode('Hello')]);

        final metadata = await service.extractMetadata(file.path);
        expect(metadata['encoding'], equals('UTF-8 with BOM'));
      });
    });

    group('getMimeType', () {
      test('returns correct MIME types', () {
        expect(service.getMimeType('test.txt'), equals('text/plain'));
        expect(service.getMimeType('data.json'), equals('application/json'));
        expect(service.getMimeType('config.xml'), equals('application/xml'));
        expect(service.getMimeType('data.csv'), equals('text/csv'));
      });
    });

    test('getTypeName returns correct type', () {
      expect(service.getTypeName(), equals('Text File'));
    });
  });
}
