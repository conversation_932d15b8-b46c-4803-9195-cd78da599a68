import 'dart:io';
import 'package:flutter_test/flutter_test.dart';
import 'package:promz/core/services/file/types/pdf_service.dart';
import 'package:syncfusion_flutter_pdf/pdf.dart';
import 'package:path/path.dart' as path;

void main() {
  group('PDFService', () {
    late PDFService service;
    late Directory tempDir;

    setUp(() {
      service = PDFService();
      tempDir = Directory.systemTemp.createTempSync();
    });

    tearDown(() {
      tempDir.deleteSync(recursive: true);
    });

    group('canHandle', () {
      test('accepts PDF files by extension', () {
        expect(service.canHandle('test.pdf'), isTrue);
        expect(service.canHandle('document.PDF'), isTrue);
      });

      test('accepts PDF files by MIME type', () {
        expect(service.canHandle('file', 'application/pdf'), isTrue);
      });

      test('rejects non-PDF files', () {
        expect(service.canHandle('test.txt'), isFalse);
        expect(service.canHandle('doc.docx'), isFalse);
        expect(service.canHandle('file', 'text/plain'), isFalse);
      });
    });

    group('extractContent', () {
      test('extracts text from PDF', () async {
        // Create a simple PDF document
        final document = PdfDocument();
        final page = document.pages.add();
        final graphics = page.graphics;
        final font = PdfStandardFont(PdfFontFamily.helvetica, 12);
        graphics.drawString('Hello World', font);

        final file = File('${tempDir.path}/test.pdf');
        await file.writeAsBytes(document.saveSync());
        document.dispose();

        final content = await service.extractContent(file.path);
        expect(content, contains('Hello World'));
      });

      test('extracts text from PDF bytes', () async {
        // Create a simple PDF document
        final document = PdfDocument();
        final page = document.pages.add();
        final graphics = page.graphics;
        final font = PdfStandardFont(PdfFontFamily.helvetica, 12);
        graphics.drawString('Test Content for Sharing', font);

        final bytes = document.saveSync();
        document.dispose();

        final content = await service.extractContentFromBytes(bytes);
        expect(content, contains('Test Content for Sharing'));
      });

      test('handles missing file gracefully', () async {
        final content = await service.extractContent('${tempDir.path}/nonexistent.pdf');
        expect(content, isNull);
      });

      test('handles multi-page PDF', () async {
        final document = PdfDocument();

        // Add first page
        var page = document.pages.add();
        var graphics = page.graphics;
        var font = PdfStandardFont(PdfFontFamily.helvetica, 12);
        graphics.drawString('Page 1', font);

        // Add second page
        page = document.pages.add();
        graphics = page.graphics;
        graphics.drawString('Page 2', font);

        final file = File('${tempDir.path}/multipage.pdf');
        await file.writeAsBytes(document.saveSync());
        document.dispose();

        final content = await service.extractContent(file.path);
        expect(content, contains('Page 1'));
        expect(content, contains('Page 2'));
      });
    });

    group('extractMetadata', () {
      test('handles PDF metadata', () async {
        // Create a PDF document
        final document = PdfDocument();
        document.documentInformation.author = 'Test Author';
        document.documentInformation.title = 'Test Document';
        document.documentInformation.subject = 'Test Subject';
        document.documentInformation.keywords = 'test, pdf, metadata';

        // Add some content
        final page = document.pages.add();
        final graphics = page.graphics;
        final font = PdfStandardFont(PdfFontFamily.helvetica, 12);
        graphics.drawString('Test Content', font);

        // Save the PDF
        final file = File('${tempDir.path}/test.pdf');
        await file.writeAsBytes(document.saveSync());
        document.dispose();

        // Verify metadata
        final metadata = await service.extractMetadata(file.path);
        expect(metadata, isNotNull);
        expect(metadata, isA<Map<String, dynamic>>());
        expect(metadata['pageCount'], equals(1));
        expect(metadata['author'], equals('Test Author'));
        expect(metadata['title'], equals('Test Document'));
        expect(metadata['subject'], equals('Test Subject'));
        expect(metadata['keywords'], equals('test, pdf, metadata'));
        expect(metadata['size'], greaterThan(0));
        expect(metadata['lastModified'], isNotEmpty);
        expect(metadata['lastAccessed'], isNotEmpty);
      });

      test('handles missing file gracefully', () async {
        final metadata = await service.extractMetadata('${tempDir.path}/nonexistent.pdf');
        expect(metadata, isEmpty);
      });

      test('handles encrypted PDF', () async {
        final document = PdfDocument();
        final page = document.pages.add();
        final graphics = page.graphics;
        graphics.drawString('Test Content', PdfStandardFont(PdfFontFamily.helvetica, 12));

        // Set encryption with a user password
        document.security.userPassword = 'test123';
        document.security.algorithm = PdfEncryptionAlgorithm.rc4x128Bit;

        final tempDir = Directory.systemTemp;
        final filePath = path.join(tempDir.path, 'encrypted_test.pdf');
        File(filePath).writeAsBytesSync(await document.save());
        document.dispose();

        final metadata = await service.extractMetadata(filePath);
        expect(metadata['isEncrypted'], true);
        expect(metadata['hasUserPassword'], true);
        expect(metadata['encryptionAlgorithm'], 'unknown');

        // Clean up
        await File(filePath).delete();
      });
    });

    test('getMimeType returns application/pdf', () {
      expect(service.getMimeType('test.pdf'), equals('application/pdf'));
    });

    test('getTypeName returns PDF Document', () {
      expect(service.getTypeName(), equals('PDF Document'));
    });
  });
}
