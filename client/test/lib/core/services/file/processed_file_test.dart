import 'package:flutter_test/flutter_test.dart';
import 'package:promz/core/models/file/processed_file.dart';

void main() {
  group('ProcessedFile', () {
    test('creates instance with required parameters', () {
      final file = ProcessedFile(
        fileName: 'test.txt',
        size: 100,
      );

      expect(file.fileName, equals('test.txt'));
      expect(file.size, equals(100));
      expect(file.textContent, isNull);
      expect(file.binaryContent, isNull);
      expect(file.lastModified, isNull);
      expect(file.mimeType, isNull);
    });

    test('creates instance from text content', () {
      final file = ProcessedFile.fromText(
        fileName: 'test.txt',
        content: 'Hello World',
        mimeType: 'text/plain',
      );

      expect(file.fileName, equals('test.txt'));
      expect(file.textContent, equals('Hello World'));
      expect(file.size, equals(11)); // Length of 'Hello World'
      expect(file.mimeType, equals('text/plain'));
      expect(file.binaryContent, isNull);
    });

    test('creates instance from binary content', () {
      final content = [1, 2, 3, 4, 5];
      final file = ProcessedFile.fromBinary(
        fileName: 'test.bin',
        content: content,
        mimeType: 'application/octet-stream',
      );

      expect(file.fileName, equals('test.bin'));
      expect(file.binaryContent, equals(content));
      expect(file.size, equals(5));
      expect(file.mimeType, equals('application/octet-stream'));
      expect(file.textContent, isNull);
    });

    test('correctly identifies text content presence', () {
      final emptyFile = ProcessedFile(fileName: 'empty.txt', size: 0);
      final textFile = ProcessedFile(
        fileName: 'test.txt',
        size: 5,
        textContent: 'Hello',
      );
      final emptyTextFile = ProcessedFile(
        fileName: 'empty.txt',
        size: 0,
        textContent: '',
      );

      expect(emptyFile.hasTextContent, isFalse);
      expect(textFile.hasTextContent, isTrue);
      expect(emptyTextFile.hasTextContent, isFalse);
    });

    test('correctly identifies binary content presence', () {
      final emptyFile = ProcessedFile(fileName: 'empty.bin', size: 0);
      final binaryFile = ProcessedFile(
        fileName: 'test.bin',
        size: 3,
        binaryContent: [1, 2, 3],
      );
      final emptyBinaryFile = ProcessedFile(
        fileName: 'empty.bin',
        size: 0,
        binaryContent: [],
      );

      expect(emptyFile.hasBinaryContent, isFalse);
      expect(binaryFile.hasBinaryContent, isTrue);
      expect(emptyBinaryFile.hasBinaryContent, isFalse);
    });

    test('correctly extracts file extension', () {
      final txtFile = ProcessedFile(fileName: 'test.txt', size: 0);
      final noExtFile = ProcessedFile(fileName: 'test', size: 0);
      final multiDotFile = ProcessedFile(fileName: 'archive.tar.gz', size: 0);

      expect(txtFile.extension, equals('txt'));
      expect(noExtFile.extension, equals(''));
      expect(multiDotFile.extension, equals('gz'));
    });

    test('copyWith creates new instance with updated values', () {
      final original = ProcessedFile(
        fileName: 'test.txt',
        size: 100,
        textContent: 'Hello',
        mimeType: 'text/plain',
      );

      final copied = original.copyWith(
        fileName: 'new.txt',
        size: 200,
        textContent: 'World',
      );

      expect(copied.fileName, equals('new.txt'));
      expect(copied.size, equals(200));
      expect(copied.textContent, equals('World'));
      expect(copied.mimeType, equals('text/plain')); // Unchanged
      expect(copied.binaryContent, isNull); // Unchanged
    });

    test('copyWith retains original values when not specified', () {
      final original = ProcessedFile(
        fileName: 'test.txt',
        size: 100,
        textContent: 'Hello',
        mimeType: 'text/plain',
      );

      final copied = original.copyWith();

      expect(copied.fileName, equals(original.fileName));
      expect(copied.size, equals(original.size));
      expect(copied.textContent, equals(original.textContent));
      expect(copied.mimeType, equals(original.mimeType));
      expect(copied.binaryContent, equals(original.binaryContent));
      expect(copied.lastModified, equals(original.lastModified));
    });
  });
}
