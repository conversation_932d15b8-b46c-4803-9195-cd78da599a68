<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleAllowMixedLocalizations</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Promz</string>
	<key>CFBundleDocumentTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeName</key>
			<string>ZIP Archive</string>
			<key>LSHandlerRank</key>
			<string>Alternate</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>com.pkware.zip-archive</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeName</key>
			<string>Plain Text</string>
			<key>LSHandlerRank</key>
			<string>Alternate</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>public.text</string>
				<string>public.plain-text</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeName</key>
			<string>PDF Document</string>
			<key>LSHandlerRank</key>
			<string>Alternate</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>com.adobe.pdf</string>
			</array>
		</dict>
	</array>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>ai.promz.app</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>io.supabase.promz</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>io.supabase.promz</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>promz</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>promz</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>google</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.393789821990-hdmv1s95niepb7jv060qucjq1o0qitjo</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>microsoft</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>msauth.io.supabase.promz</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>supabase.callback</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>https</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>https</string>
		<string>http</string>
		<string>microsoft</string>
		<string>ms-outlook</string>
		<string>msauthv2</string>
		<string>msauth</string>
		<string>googlechrome</string>
		<string>googlechromes</string>
		<string>com.google.gpay.app</string>
		<string>google</string>
		<string>googleauth</string>
		<string>googleusercontent</string>
		<string>itms-apps</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>LSSupportsOpeningDocumentsInPlace</key>
	<true/>
	<key>NSExtensionActivationRule</key>
	<dict>
		<key>NSExtensionActivationSupportsFileWithMaxCount</key>
		<integer>1</integer>
		<key>NSExtensionActivationSupportsImageWithMaxCount</key>
		<integer>1</integer>
		<key>NSExtensionActivationSupportsText</key>
		<true/>
		<key>NSExtensionActivationSupportsWebURLWithMaxCount</key>
		<integer>1</integer>
	</dict>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>To save processed content to your photo library</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>To upload photos for processing in PromZ</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIFileSharingEnabled</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>com.apple.developer.applesignin</key>
	<array>
		<string>Default</string>
	</array>
	<key>com.apple.developer.associated-domains</key>
	<array>
		<string>applinks:djqmdekosqnyxoqujxpm.supabase.co</string>
		<string>applinks:www.promz.ai</string>
		<string>applinks:promz.ai</string>
	</array>
</dict>
</plist>
