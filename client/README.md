# Promz Mobile

A Flutter-based mobile application for managing and sharing prompts with trusted friends. Promz helps users create, organize, and execute AI prompts with intelligent variable handling and entity detection.

## Features

- **Smart Text Analysis**: Advanced keyword extraction and content categorization
- **Entity Detection**: Automatic identification of companies, locations, and other entities
- **Variable Management**: Template-based variable system with {{CATEGORY:ENTITY}} format
- **Intelligent Suggestions**: Context-aware prompt suggestions based on content
- **File Management**: Support for multiple file types (PDF, TXT, WhatsApp exports)
- **Content Deduplication**: Intelligent file deduplication based on content analysis
- **Cross-Platform**: Native experience on both iOS and Android

## Architecture

Promz follows the MVVM (Model-View-ViewModel) architecture pattern with Riverpod for state management:

### Core Components

- **ClientContextService (CCS)**: Central service for managing application context and state
- **VariableManager**: Handles template variables with dynamic resolution
- **EntityDetectionService**: Real-time entity detection in text
- **PromptSuggestionService**: AI-powered prompt suggestions

### Key UI Components

- **Variables System**: 
  - VariablesDialog: Modal interface for editing template variables
  - VariablesSection: Compact display of active variables as chips
  - Support for both entity and custom variables

- **Autocomplete System**:
  - AutocompleteField: Advanced text input with real-time suggestions
  - Intelligent entity detection and suggestion prioritization
  - Debounced suggestion requests for performance

## Getting Started

### For Developers

1. **Development Setup**

   - Clone the repository
   - Install Flutter SDK
   - Run `flutter pub get` to install dependencies
   - Ensure Android SDK is installed with emulator component
   - Set `ANDROID_HOME` environment variable

2. **Development Workflow**
   - Create feature branches from `main`
   - Follow testing standards in [.github/copilot-instructions.md](../.github/copilot-instructions.md)
   - Ensure tests pass before submitting PRs

### Windows Setup

SQLite DLLs are required for database tests. The development scripts will handle this automatically, but you can also install manually:

```powershell
Invoke-WebRequest -Uri "https://sqlite.org/2023/sqlite-dll-win64-x64-3430000.zip" -OutFile sqlite-dll.zip
Expand-Archive -Path sqlite-dll.zip -DestinationPath .
```

## Key Components

### Text Analysis Service

The application includes a sophisticated text analysis service that:

- Extracts keywords from various content types
- Identifies technical and financial terms
- Supports multiple content formats
- Provides content categorization

### Entity Detection

The EntityDetectionService provides:

- Real-time entity detection in text
- Support for multiple entity types (finance, location, etc.)
- Dictionary-based entity matching with fuzzy matching
- Template-based entity extraction
- Special handling for financial entities

### Variable Management

The VariableManager handles:

- Template format: `{{CATEGORY:ENTITY}}`
- Dynamic variable resolution
- User-friendly display text mapping
- Variable dependencies
- Centralized variable value storage

### Prompt Suggestions

The PromptSuggestionService provides:

- AI-powered prompt suggestions
- Category-aware matching
- Score-based relevance ranking
- Recent and popular prompts tracking
- Keyword extraction and matching

## Database Architecture

- SQLite-based local database with drift (formerly moor)
- Supabase integration for cloud synchronization
- Efficient query optimization
- Migration support for schema updates
- Entity relationship modeling

## Development Scripts

### push_and_run.ps1

A PowerShell script that automates the development workflow:

```powershell
# Navigate to scripts directory
cd scripts

# Run in debug mode (default)
.\push_and_run.ps1

# Run with clean build
.\push_and_run.ps1 -clean

# Build and deploy release version
.\push_and_run.ps1 -release

# Only manage emulator
.\push_and_run.ps1 -emulator

# Skip running tests
.\push_and_run.ps1 -skip_tests
```

The script handles:

- SQLite DLL installation
- Android emulator management
- Running tests
- Building and deploying the app
- Clean builds when requested

## Debugging

When debugging Promz, pay close attention to the logcat output. This can provide valuable insights into the application's behavior and help identify errors.

Here are some example commands to filter logcat output for specific modules:

- **PromptSuggestions:** `pidcat ai.promz | Select-String "PromptSuggestions"`
- **EntityDetection:** `pidcat ai.promz | Select-String "EntityDetection"`
- **Database:** `pidcat ai.promz | Select-String "Database"`
- **Auth:** `pidcat ai.promz | Select-String "Auth"`
- **Sync:** `pidcat ai.promz | Select-String "Sync"`

## Resources

For Flutter development help:

- [Lab: Write your first Flutter app](https://docs.flutter.dev/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://docs.flutter.dev/cookbook)
- [Online documentation](https://docs.flutter.dev/)
