name: promz
description: "PromZ: AI Made Easy"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.6.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.8

  # Device Info and Security
  safe_device: ^1.2.1

  # Sharing and Intent Handling
  share_plus: ^10.1.4
  flutter_sharing_intent: ^1.1.1

  # Database and Filesystem
  drift: ^2.25.1
  sqlite3_flutter_libs: ^0.5.30
  path_provider: ^2.1.2
  path_provider_android: ^2.0.11
  path: ^1.8.3
  synchronized: ^3.1.0
  archive: ^4.0.3 # Added for ZIP processing
  file_picker: ^9.0.0
  mime: ^1.0.5
  syncfusion_flutter_pdf: ^24.2.8 # Added for PDF text extraction
  crypto: ^3.0.3 # Added for MD5 hashing

  # Networking and Connectivity
  http: ^1.3.0
  connectivity_plus: ^6.0.1

  # State Management and UI
  flutter_riverpod: ^2.4.9
  google_fonts: ^6.1.0
  flutter_adaptive_scaffold: ^0.1.7+1
  go_router: ^13.1.0

  # JSON Handling
  json_annotation: ^4.8.1
  intl: ^0.19.0

  # Date and Time Utilities
  timeago: ^3.1.0
  provider: ^6.1.2

  # Added from the code block
  shared_preferences: ^2.2.2
  sprintf: ^7.0.0
  meta: ^1.15.0
  uuid: ^4.5.1
  base_codecs: ^1.0.1
  equatable: ^2.0.7
  flutter_secure_storage: ^9.2.4
  htmltopdfwidgets: ^1.0.9

  # Shared package
  promz_common:
    path: ../packages/promz_common
  riverpod: ^2.6.1
  flutter_markdown: ^0.6.23
  pdf: ^3.11.3
  sqflite: ^2.4.2
  readability: ^0.2.2
  html: ^0.15.5

  # Authentication and Backend
  supabase_flutter: ^2.8.4
  google_sign_in: ^6.2.1
  sign_in_with_apple: ^7.0.1
  rxdart: ^0.28.0
  url_launcher: ^6.3.1
  flutter_signin_button: ^2.0.0
  
  # WebSockets
  web_socket_channel: ^2.4.0

  # HTML and UI
  flutter_html: ^3.0.0
  shimmer: ^3.0.0
  protobuf: ^4.0.0
  grpc: ^4.0.4
  fixnum: ^1.1.1
  flutter_dotenv: ^5.2.1
  
  # App Updates
  upgrader: ^11.3.2
  package_info_plus: ^8.3.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0
  mockito: ^5.4.5
  drift_dev: ^2.14.1
  build_runner: ^2.4.0
  test: ^1.24.9
  json_serializable: ^6.9.4
  path_provider_platform_interface: ^2.1.2
  plugin_platform_interface: ^2.1.8
  mocktail: ^1.0.4
  flutter_launcher_icons: ^0.13.1

dependency_overrides:
  analyzer: '7.3.0'

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  uses-material-design: true
  generate: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/promz.db
    - assets/images/onboarding/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

# Flutter Launcher Icons configuration
flutter_launcher_icons:
  android: false # We already have Android icons
  ios: true
  image_path: "android/app/src/main/res/mipmap-xxxhdpi/promz_icon.png"
  remove_alpha_ios: true
